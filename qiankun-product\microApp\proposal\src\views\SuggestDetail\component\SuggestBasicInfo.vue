<template>
  <div class="SuggestBasicInfo">
    <global-dynamic-title templateCode="proposal_title" :params="{ businessId: props.id }"></global-dynamic-title>
    <div class="SuggestDetailInfo">
      <div class="SuggestDetailNumberTime">
        <div class="SuggestDetailNumber">提案编号：{{ details.serialNumber }}</div>
        <div class="SuggestDetailTime">提案时间：{{ format(details.createDate) }}</div>
      </div>
      <div class="SuggestDetailType">
        <div class="SuggestDetailBigType" v-if="details.bigThemeName">{{ details.bigThemeName }}</div>
        <div class="SuggestDetailSmallType" v-if="details.smallThemeName">{{ details.smallThemeName }}</div>
      </div>
    </div>
    <div class="SuggestDetailTitle">{{ details.title }}</div>
    <template v-if="details.suggestSubmitWay === 'team'">
      <div class="SuggestDetailInfoItem">提案者：{{ details.suggestUserName }}</div>
    </template>
    <template v-if="details.suggestSubmitWay === 'cppcc_member'">
      <div class="SuggestDetailInfo">
        <div class="SuggestDetailInfoItem">提案者：{{ details.submitUserInfo?.userName }}</div>
        <div class="SuggestDetailInfoItem">委员证号：{{ details.submitUserInfo?.cardNumber }}</div>
      </div>
      <div class="SuggestDetailInfo">
        <div class="SuggestDetailInfoItem">界别：{{ details.sectorType?.label }}</div>
        <div class="SuggestDetailInfoItem">联系电话：{{ details.submitUserInfo?.mobile }}</div>
      </div>
      <div class="SuggestDetailInfo">
        <div class="SuggestDetailInfoItem">办公电话：{{ details.submitUserInfo?.officePhone }}</div>
        <div class="SuggestDetailInfoItem">邮政编码：{{ details.submitUserInfo?.postcode }}</div>
      </div>
      <div class="SuggestDetailInfoItem">单位及职务：{{ details.submitUserInfo?.position }}</div>
      <div class="SuggestDetailInfoItem">通讯地址：{{ details.submitUserInfo?.callAddress }}</div>
    </template>
    <div class="SuggestDetailContent" v-html="details.content"></div>
    <xyl-global-file :fileData="details.attachments"></xyl-global-file>
    <div class="SuggestDetailInfoName">提案相关情况</div>
    <div class="SuggestDetailInfoItem" v-if="suggestOpenTypeName">{{ suggestOpenTypeName }}：<span>{{
      details.suggestOpenType?.label }}</span></div>
    <div class="SuggestDetailInfoItem" v-if="suggestSurveyTypeName">{{ suggestSurveyTypeName }}：<span>{{
      details.suggestSurveyType?.label }}</span>
    </div>
    <div class="SuggestDetailInfoItem" v-if="isMakeMineJobName">{{ isMakeMineJobName }}：<span>{{
      details.isMakeMineJob?.label }}</span></div>
    <div class="SuggestDetailInfoItem" v-if="notHandleTimeTypeName">{{ notHandleTimeTypeName }}：<span>{{
      details.notHandleTimeType?.label }}</span>
    </div>
    <div class="SuggestDetailInfoItem" v-if="isHopeEnhanceTalkName">{{ isHopeEnhanceTalkName }}：<span>{{
      details.isHopeEnhanceTalk?.label }}</span>
    </div>
    <div class="SuggestDetailInfoItem" v-if="isNeedPaperAnswerName">{{ isNeedPaperAnswerName }}：<span>{{
      details.isNeedPaperAnswer?.label }}</span>
    </div>
    <!-- <div class="SuggestDetailInfoItem">希望送交办单位：{{ details.hopeHandleOfficeIds?.map(v => v.officeName).join('、') }}</div> -->
    <div class="SuggestDetailInfoName" v-if="details.joinUsers?.length">提案联名人</div>
    <div class="SuggestDetailTable" v-if="details.joinUsers?.length">
      <div class="SuggestDetailTableHead">
        <div class="SuggestDetailTableItem row1">姓名</div>
        <div class="SuggestDetailTableItem row1">委员证号</div>
        <div class="SuggestDetailTableItem row1">联系电话</div>
        <div class="SuggestDetailTableItem row3">通讯地址</div>
        <div class="SuggestDetailTableItem row1">是否同意</div>
      </div>
      <div class="SuggestDetailTableBody" v-for="item in details.joinUsers" :key="item.userId">
        <div class="SuggestDetailTableItem row1">{{ item.userName }}</div>
        <div class="SuggestDetailTableItem row1">{{ item.cardNumber }}</div>
        <div class="SuggestDetailTableItem row1">{{ item.mobile }}</div>
        <div class="SuggestDetailTableItem row3">{{ item.callAddress }}</div>
        <div class="SuggestDetailTableItem row1" v-if="!item.agreeStatus"></div>
        <div class="SuggestDetailTableItem row1" v-if="item.agreeStatus === 1">已操作同意</div>
        <div class="SuggestDetailTableItem row1" v-if="item.agreeStatus === 2">已操作不同意</div>
      </div>
    </div>
    <div class="SuggestDetailInfoName" v-if="details.contacters?.length">提案联系人</div>
    <div class="SuggestDetailTable" v-if="details.contacters?.length">
      <div class="SuggestDetailTableHead">
        <div class="SuggestDetailTableItem row1">联系人姓名</div>
        <div class="SuggestDetailTableItem row1">联系人电话</div>
        <div class="SuggestDetailTableItem row4">联系人通讯地址</div>
      </div>
      <div class="SuggestDetailTableBody" v-for="item in details.contacters" :key="item.id">
        <div class="SuggestDetailTableItem row1">{{ item.contacterName }}</div>
        <div class="SuggestDetailTableItem row1">{{ item.contacterMobile }}</div>
        <div class="SuggestDetailTableItem row4">{{ item.contacterAddress }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'SuggestBasicInfo' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onActivated } from 'vue'
import { format } from 'common/js/time.js'
const props = defineProps({ id: { type: String, default: '' }, details: { type: Object, default: () => ({}) } })
const details = computed(() => props.details)
const suggestOpenTypeName = ref('')
const suggestSurveyTypeName = ref('')
const notHandleTimeTypeName = ref('')
const isHopeEnhanceTalkName = ref('')
const isMakeMineJobName = ref('')
const isNeedPaperAnswerName = ref('')

onActivated(() => { dictionaryNameData() })

const dictionaryNameData = async () => {
  const { data } = await api.dictionaryNameData({
    dictCodes: ['suggest_open_type', 'suggest_survey_type', 'not_handle_time_type', 'is_hope_enhance_talk', 'is_make_mine_job', 'is_need_paper_answer']
  })
  suggestOpenTypeName.value = data.suggest_open_type
  suggestSurveyTypeName.value = data.suggest_survey_type
  notHandleTimeTypeName.value = data.not_handle_time_type
  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk
  isMakeMineJobName.value = data.is_make_mine_job
  isNeedPaperAnswerName.value = data.is_need_paper_answer
}
</script>
<style lang="scss">
.SuggestBasicInfo {
  width: 100%;
  padding: var(--zy-distance-one) 0;

  .SuggestDetailName {
    font-size: var(--zy-title-font-size);
    font-weight: bold;
    border-bottom: 2px solid var(--zy-el-color-primary);
    text-align: center;
    padding: 20px 0;

    span {
      color: red;
    }
  }

  .SuggestDetailTitle {
    width: 100%;
    padding: 10px 0;
    font-weight: bold;
    font-size: var(--zy-title-font-size);
    line-height: var(--zy-line-height);
  }

  .SuggestDetailInfo {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .SuggestDetailNumberTime {
      display: flex;
      padding-top: 20px;
      padding-bottom: 10px;

      .SuggestDetailNumber {
        font-weight: bold;
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
      }

      .SuggestDetailTime {
        color: var(--zy-el-text-color-regular);
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
        margin-left: 40px;
      }
    }

    .SuggestDetailType {
      display: flex;
      align-items: center;

      .SuggestDetailBigType {
        color: var(--zy-el-color-warning);
        background-color: var(--zy-el-color-warning-light-9);
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
        padding: 0 10px;
      }

      .SuggestDetailSmallType {
        color: var(--zy-el-color-success);
        background-color: var(--zy-el-color-success-light-9);
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
        padding: 0 10px;
        margin-left: 20px;
      }
    }

    .SuggestDetailInfoItem {
      width: 50%;
      padding: 10px 0;
    }
  }

  .SuggestDetailInfoName {
    padding-top: 15px;
    padding-bottom: 5px;
    font-weight: bold;
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
  }

  .SuggestDetailInfoItem {
    width: 100%;
    padding: 5px 0;
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);

    span {
      font-weight: bold;
    }
  }

  .SuggestDetailContent {
    padding: 20px 0;
    overflow: hidden;
    line-height: var(--zy-line-height);

    img,
    video {
      max-width: 100%;
      height: auto !important;
    }

    table {
      max-width: 100%;
      border-collapse: collapse;
      border-spacing: 0;

      tr {
        page-break-inside: avoid;
      }
    }
  }

  .SuggestDetailTable {
    width: 100%;
    margin: 5px 0;
    border-top: 1px solid var(--zy-el-border-color-lighter);
    border-right: 1px solid var(--zy-el-border-color-lighter);

    .SuggestDetailTableHead,
    .SuggestDetailTableBody {
      width: 100%;
      display: flex;
      border-bottom: 1px solid var(--zy-el-border-color-lighter);
    }

    .SuggestDetailTableHead {
      background-color: var(--zy-el-color-info-light-9);
    }

    .SuggestDetailTableBody {
      border-bottom: 1px solid var(--zy-el-border-color-lighter);
    }

    .row1 {
      flex: 1;
    }

    .row2 {
      flex: 2;
    }

    .row3 {
      flex: 3;
    }

    .row4 {
      flex: 4;
    }

    .SuggestDetailTableItem {
      display: flex;
      align-items: center;
      justify-content: center;
      border-left: 1px solid var(--zy-el-border-color-lighter);
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      padding: 10px;
    }
  }
}
</style>
