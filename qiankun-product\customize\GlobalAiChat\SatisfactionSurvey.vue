<template>
  <div class="SatisfactionSurvey">
    <div class="survey-header">
      <span>您对本次回答是否满意？</span>
    </div>
    <!-- 显示对话内容 -->
    <div v-if="data.question || data.answer" class="survey-content">
      <div v-if="data.question" class="survey-question">
        <div class="survey-label">问题：</div>
        <div class="survey-text">{{ data.question }}</div>
      </div>
      <div v-if="data.answer" class="survey-answer">
        <div class="survey-label">回答：</div>
        <div class="survey-text">{{ data.answer }}</div>
      </div>
    </div>
    <div class="survey-faces">
      <div v-for="(item, idx) in faces" :key="item.id" :class="['survey-face', { active: satisfaction === item.id }]"
        @click="selectSatisfaction(item.id)">
        <span v-html="item.icon"></span>
        <div class="survey-face-label">{{ item.label }}</div>
      </div>
    </div>
    <div v-if="showReasons" class="survey-reason-title">请告诉我们具体原因（可多选，非必填）</div>
    <el-checkbox-group v-if="showReasons" v-model="reasons" class="survey-reasons">
      <el-checkbox v-for="item in reasonOptions" :key="item.id" :label="item.id">{{ item.label }}</el-checkbox>
    </el-checkbox-group>
    <el-button type="primary" class="survey-submit" :disabled="!satisfaction" @click="handleSubmit">提交</el-button>
  </div>
</template>
<script>
export default { name: 'SatisfactionSurvey' }
</script>
<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'
const props = defineProps({ data: { type: Object, default: () => ({}) } })
const emit = defineEmits(['callback'])
const satisfaction = ref('')
const reasons = ref([])
const faces = ref([])
const reasonOptions = ref([])
// 计算是否显示不满意原因
const showReasons = computed(() => {
  return satisfaction.value === '3' ||
    satisfaction.value === '4' ||
    satisfaction.value === '5'
})
onMounted(() => {
  dictionaryData()
})
const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['satisfaction', 'reasons_for_dissatisfaction'] })
  var { data } = res

  // 为满意度选项添加图标
  const iconMap = {
    '非常满意': '😊',
    '基本满意': '🙂',
    '一般': '😐',
    '不满意': '🙁',
    '非常不满意': '😞'
  }

  faces.value = data.satisfaction.map(item => ({
    ...item,
    value: item.id,
    label: item.name,
    icon: iconMap[item.name] || '😐'
  }))

  reasonOptions.value = data.reasons_for_dissatisfaction
}
const selectSatisfaction = val => {
  satisfaction.value = val
  // 如果选择满意或非常满意，清空原因选择
  if (val === '1' || val === '2') {
    reasons.value = []
  }
}
const handleSubmit = async () => {
  const { code } = await api.globalJson('/aiSatisfactionSurvey/add', {
    form: {
      title: props.data.question,
      satisfaction: satisfaction.value,
      reasonsForDissatisfaction: reasons.value.join(','),
      content: props.data.answer || ''
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '新增成功' })
    emit('callback')
  }
}
</script>
<style lang="scss">
.SatisfactionSurvey {
  width: 588px;
  padding: 32px 32px 24px 32px;

  .survey-header {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 24px;
    position: relative;
  }

  .survey-content {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .survey-question,
    .survey-answer {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .survey-label {
      font-weight: bold;
      color: #333;
      margin-bottom: 4px;
      font-size: 14px;
    }

    .survey-text {
      color: #666;
      line-height: 1.5;
      font-size: 14px;
      word-wrap: break-word;
      white-space: pre-wrap;
    }
  }

  .survey-faces {
    display: flex;
    justify-content: space-between;
    margin-bottom: 18px;

    .survey-face {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      font-size: 32px;
      color: #bbb;
      transition: color 0.2s;

      .survey-face-label {
        font-size: 14px;
        margin-top: 6px;
        color: #666;
      }

      &.active {
        color: #409eff;

        .survey-face-label {
          color: #409eff;
          font-weight: bold;
        }
      }
    }
  }

  .survey-reason-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .survey-reasons {
    display: flex;
    flex-direction: column;
    margin-bottom: 18px;

    .el-checkbox {
      margin: 6px 0;
    }
  }

  .survey-submit {
    width: 100%;
    font-size: 16px;
    margin-top: 8px;
  }
}
</style>
