{"ast": null, "code": "import { defineAsyncComponent } from 'vue';\nvar IntelligentErrorCorrection = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/IntelligentErrorCorrection/IntelligentErrorCorrection.vue');\n});\nvar OneClickLayout = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/OneClickLayout/OneClickLayout.vue');\n});\nvar ContentExtraction = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/ContentExtraction/ContentExtraction.vue');\n});\nvar IntelligentManuscriptMerging = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/IntelligentManuscriptMerging/IntelligentManuscriptMerging.vue');\n});\nvar TextComparison = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/TextComparison/TextComparison.vue');\n});\nvar TextPolishing = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/TextPolishing/TextPolishing.vue');\n});\nvar TextExpansion = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/TextExpansion/TextExpansion.vue');\n});\nvar TextContinuation = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/TextContinuation/TextContinuation.vue');\n});\nvar TextRewrite = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/TextRewrite/TextRewrite.vue');\n});\nvar TextRecognition = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/TextRecognition/TextRecognition.vue');\n});\nvar ProposalAuxiliaryWriting = defineAsyncComponent(function () {\n  return import('../AiToolBoxFunction/ProposalAuxiliaryWriting/ProposalAuxiliaryWriting.vue');\n});\nexport var AiToolBoxElement = {\n  IntelligentErrorCorrection,\n  OneClickLayout,\n  ContentExtraction,\n  IntelligentManuscriptMerging,\n  TextComparison,\n  TextPolishing,\n  TextExpansion,\n  TextContinuation,\n  TextRewrite,\n  TextRecognition,\n  ProposalAuxiliaryWriting\n};\nexport var setting = {\n  height: '100%',\n  menubar: false,\n  statusbar: false,\n  elementpath: false,\n  toolbar: 'undo redo | formatselect | fontselect | fontsizeselect | alignleft aligncenter alignright alignjustify | removeformat searchreplace wordcount | bold italic underline strikethrough | forecolor backcolor lineheight | indent2em indent outdent | numlist bullist | link unlink | table',\n  toolbar_mode: 'sliding' // floating / sliding / scrolling / wrap\n};\nexport var content_style = `\nhtml {\n  background: #f7f7f7;\n  padding: 16px 0;\n}\nbody {\n  width: 793.733px;\n  min-height: 1122.53px; \n  padding: 90.7333px 94.5px;\n  background: #fff;\n  box-sizing: border-box;\n  box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 10px 0px, rgba(0, 0, 0, 0.04) 0px 0px 0px 1px;\n  margin: 0 0 0 1px;\n}\nbody::before {\n  left: 94.5px !important;\n}`;\nexport var guid = function guid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n};\nexport var trigerUpload = function trigerUpload() {\n  return new Promise(function (resolve) {\n    var input = document.createElement('input');\n    input.setAttribute('type', 'file');\n    input.setAttribute('multiple', 'multiple');\n    input.addEventListener('change', function (e) {\n      resolve(e.target.files[0]);\n    });\n    input.click();\n  });\n};", "map": {"version": 3, "names": ["defineAsyncComponent", "IntelligentErrorCorrection", "OneClickLayout", "ContentExtraction", "IntelligentManuscriptMerging", "TextComparison", "TextPolishing", "TextExpansion", "TextContinuation", "TextRewrite", "TextRecognition", "ProposalAuxiliaryWriting", "AiToolBoxElement", "setting", "height", "menubar", "statusbar", "elementpath", "toolbar", "toolbar_mode", "content_style", "guid", "replace", "c", "r", "Math", "random", "v", "toString", "trigerUpload", "Promise", "resolve", "input", "document", "createElement", "setAttribute", "addEventListener", "e", "target", "files", "click"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiToolBox/AiToolBox.js"], "sourcesContent": ["import { defineAsyncComponent } from 'vue'\r\nconst IntelligentErrorCorrection = defineAsyncComponent(() =>\r\n  import('../AiToolBoxFunction/IntelligentErrorCorrection/IntelligentErrorCorrection.vue')\r\n)\r\nconst OneClickLayout = defineAsyncComponent(() => import('../AiToolBoxFunction/OneClickLayout/OneClickLayout.vue'))\r\nconst ContentExtraction = defineAsyncComponent(() =>\r\n  import('../AiToolBoxFunction/ContentExtraction/ContentExtraction.vue')\r\n)\r\nconst IntelligentManuscriptMerging = defineAsyncComponent(() =>\r\n  import('../AiToolBoxFunction/IntelligentManuscriptMerging/IntelligentManuscriptMerging.vue')\r\n)\r\nconst TextComparison = defineAsyncComponent(() => import('../AiToolBoxFunction/TextComparison/TextComparison.vue'))\r\nconst TextPolishing = defineAsyncComponent(() => import('../AiToolBoxFunction/TextPolishing/TextPolishing.vue'))\r\nconst TextExpansion = defineAsyncComponent(() => import('../AiToolBoxFunction/TextExpansion/TextExpansion.vue'))\r\nconst TextContinuation = defineAsyncComponent(() =>\r\n  import('../AiToolBoxFunction/TextContinuation/TextContinuation.vue')\r\n)\r\nconst TextRewrite = defineAsyncComponent(() => import('../AiToolBoxFunction/TextRewrite/TextRewrite.vue'))\r\nconst TextRecognition = defineAsyncComponent(() => import('../AiToolBoxFunction/TextRecognition/TextRecognition.vue'))\r\nconst ProposalAuxiliaryWriting = defineAsyncComponent(() =>\r\n  import('../AiToolBoxFunction/ProposalAuxiliaryWriting/ProposalAuxiliaryWriting.vue')\r\n)\r\nexport const AiToolBoxElement = {\r\n  IntelligentErrorCorrection,\r\n  OneClickLayout,\r\n  ContentExtraction,\r\n  IntelligentManuscriptMerging,\r\n  TextComparison,\r\n  TextPolishing,\r\n  TextExpansion,\r\n  TextContinuation,\r\n  TextRewrite,\r\n  TextRecognition,\r\n  ProposalAuxiliaryWriting\r\n}\r\n\r\nexport const setting = {\r\n  height: '100%',\r\n  menubar: false,\r\n  statusbar: false,\r\n  elementpath: false,\r\n  toolbar:\r\n    'undo redo | formatselect | fontselect | fontsizeselect | alignleft aligncenter alignright alignjustify | removeformat searchreplace wordcount | bold italic underline strikethrough | forecolor backcolor lineheight | indent2em indent outdent | numlist bullist | link unlink | table',\r\n  toolbar_mode: 'sliding' // floating / sliding / scrolling / wrap\r\n}\r\nexport const content_style = `\r\nhtml {\r\n  background: #f7f7f7;\r\n  padding: 16px 0;\r\n}\r\nbody {\r\n  width: 793.733px;\r\n  min-height: 1122.53px; \r\n  padding: 90.7333px 94.5px;\r\n  background: #fff;\r\n  box-sizing: border-box;\r\n  box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 10px 0px, rgba(0, 0, 0, 0.04) 0px 0px 0px 1px;\r\n  margin: 0 0 0 1px;\r\n}\r\nbody::before {\r\n  left: 94.5px !important;\r\n}`\r\n\r\nexport const guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nexport const trigerUpload = () => {\r\n  return new Promise((resolve) => {\r\n    let input = document.createElement('input')\r\n    input.setAttribute('type', 'file')\r\n    input.setAttribute('multiple', 'multiple')\r\n    input.addEventListener('change', (e) => {\r\n      resolve(e.target.files[0])\r\n    })\r\n    input.click()\r\n  })\r\n}\r\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,KAAK;AAC1C,IAAMC,0BAA0B,GAAGD,oBAAoB,CAAC;EAAA,OACtD,MAAM,CAAC,gFAAgF,CAAC;AAAA,CAC1F,CAAC;AACD,IAAME,cAAc,GAAGF,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,wDAAwD,CAAC;AAAA,EAAC;AACnH,IAAMG,iBAAiB,GAAGH,oBAAoB,CAAC;EAAA,OAC7C,MAAM,CAAC,8DAA8D,CAAC;AAAA,CACxE,CAAC;AACD,IAAMI,4BAA4B,GAAGJ,oBAAoB,CAAC;EAAA,OACxD,MAAM,CAAC,oFAAoF,CAAC;AAAA,CAC9F,CAAC;AACD,IAAMK,cAAc,GAAGL,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,wDAAwD,CAAC;AAAA,EAAC;AACnH,IAAMM,aAAa,GAAGN,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;AAAA,EAAC;AAChH,IAAMO,aAAa,GAAGP,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;AAAA,EAAC;AAChH,IAAMQ,gBAAgB,GAAGR,oBAAoB,CAAC;EAAA,OAC5C,MAAM,CAAC,4DAA4D,CAAC;AAAA,CACtE,CAAC;AACD,IAAMS,WAAW,GAAGT,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;AAAA,EAAC;AAC1G,IAAMU,eAAe,GAAGV,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,0DAA0D,CAAC;AAAA,EAAC;AACtH,IAAMW,wBAAwB,GAAGX,oBAAoB,CAAC;EAAA,OACpD,MAAM,CAAC,4EAA4E,CAAC;AAAA,CACtF,CAAC;AACD,OAAO,IAAMY,gBAAgB,GAAG;EAC9BX,0BAA0B;EAC1BC,cAAc;EACdC,iBAAiB;EACjBC,4BAA4B;EAC5BC,cAAc;EACdC,aAAa;EACbC,aAAa;EACbC,gBAAgB;EAChBC,WAAW;EACXC,eAAe;EACfC;AACF,CAAC;AAED,OAAO,IAAME,OAAO,GAAG;EACrBC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,KAAK;EAClBC,OAAO,EACL,yRAAyR;EAC3RC,YAAY,EAAE,SAAS,CAAC;AAC1B,CAAC;AACD,OAAO,IAAMC,aAAa,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AAEF,OAAO,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;EACxB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAACC,CAAC,EAAK;IACpE,IAAIC,CAAC,GAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;MAC9BC,CAAC,GAAGJ,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;IACpC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EAChC,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAK;IAC9B,IAAIC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC3CF,KAAK,CAACG,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IAClCH,KAAK,CAACG,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IAC1CH,KAAK,CAACI,gBAAgB,CAAC,QAAQ,EAAE,UAACC,CAAC,EAAK;MACtCN,OAAO,CAACM,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC;IACFP,KAAK,CAACQ,KAAK,CAAC,CAAC;EACf,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}