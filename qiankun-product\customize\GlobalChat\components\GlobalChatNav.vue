<template>
  <div class="GlobalChatNav" :class="{ GlobalChatMacNav: isMac }">
    <div class="GlobalChatUser">
      <el-image :src="user.image" fit="cover" draggable="false" />
    </div>
    <el-badge :value="chatTotal" :hidden="!chatTotal" :offset="[-16, 0]">
      <div :class="['GlobalChatNavItem', { 'is-active': navId === '1' }]" @click="handleNavClick({ id: '1' })">
        <div class="GlobalChatNavIcon" v-html="messageIcon"></div>
        <div class="GlobalChatNavText forbidSelect">消息</div>
      </div>
    </el-badge>
    <div v-for="item in navData" :key="item.id" :class="['GlobalChatNavItem', { 'is-active': navId === item.id }]"
      @click="handleNavClick(item)">
      <div class="GlobalChatNavIcon" v-html="item.icon"></div>
      <div class="GlobalChatNavText forbidSelect">{{ item.name }}</div>
    </div>
    <div class="GlobalChatNavItem GlobalChatNavExit" @click="handleExit" v-if="props.exit">
      <div class="GlobalChatNavIcon" v-html="exitIcon"></div>
    </div>
  </div>
</template>
<script>
export default { name: 'GlobalChatNav' }
</script>
<script setup>
import api from '@/api'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { user } from 'common/js/system_var.js'
import { globalReadOpenConfig } from 'common/js/GlobalMethod'
import { messageIcon, addressBookIcon, groupIcon, exitIcon } from '../js/icon.js'
import { ElMessage, ElMessageBox } from 'element-plus'
const router = useRouter()
const store = useStore()
const props = defineProps({
  modelValue: [String, Number],
  chatTotal: { type: Number, default: 0 },
  exit: { type: Boolean, default: false }
})
const emit = defineEmits(['update:modelValue', 'change'])
const isMac = window.electron?.isMac
const navId = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})
const chatTotal = computed(() => props.chatTotal)
const navData = ref([
  // { id: '1', name: '消息', icon: messageIcon },
  { id: '2', name: '通讯录', icon: addressBookIcon },
  { id: '3', name: '群组', icon: groupIcon }
])
const handleNavClick = (item) => {
  if (navId.value === item.id) return
  navId.value = item.id
  emit('change', item.id)
}
const handleExit = () => {
  ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      loginOut('已安全退出！')
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消退出' })
    })
}
const loginOut = async (text) => {
  const { code } = await api.loginOut()
  if (code === 200) {
    sessionStorage.clear()
    const goal_login_router_path = localStorage.getItem('goal_login_router_path')
    if (goal_login_router_path) {
      const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''
      router.push({
        path: goal_login_router_path,
        query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}
      })
    } else {
      router.push({ path: '/LoginView' })
    }
    store.commit('setUser', {})
    store.commit('setMenu', [])
    store.commit('setArea', [])
    store.commit('setRole', [])
    store.commit('setReadConfig', {})
    store.commit('setReadOpenConfig', {})
    store.commit('setRongCloudToken', '')
    globalReadOpenConfig()
    // store.state.socket.disconnect()
    // store.state.socket = null
    ElMessage({ message: text, showClose: true, type: 'success' })
  }
}
</script>
<style lang="scss">
.GlobalChatNav {
  width: 72px;
  height: 100%;
  padding: 20px 0 60px 0;
  background: #f8f8f8;
  position: relative;
  -webkit-app-region: drag;

  &.GlobalChatMacNav {
    padding-top: 52px;
  }

  .GlobalChatUser {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding-bottom: 30px;
    -webkit-app-region: no-drag;

    .zy-el-image {
      width: 46px;
      height: 46px;
      border-radius: 50%;
      overflow: hidden;
    }
  }

  .zy-el-badge {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    -webkit-app-region: no-drag;

    .GlobalChatNavItem {
      margin-bottom: 0;
    }
  }

  .GlobalChatNavItem {
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-bottom: 20px;
    cursor: pointer;
    -webkit-app-region: no-drag;

    .GlobalChatNavIcon {
      width: 30px;
      height: 30px;

      .b {
        fill: var(--zy-el-text-color-primary);
      }
    }

    .GlobalChatNavText {
      font-size: 14px;
    }

    &.is-active {
      .GlobalChatNavIcon {
        .b {
          fill: var(--zy-el-color-primary);
        }
      }

      .GlobalChatNavText {
        color: var(--zy-el-color-primary);
      }
    }
  }

  .GlobalChatNavExit {
    position: absolute;
    left: 0;
    bottom: 0;
  }
}
</style>
