{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SegreeSatisfactionDetail\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_global_info_item = _resolveComponent(\"global-info-item\");\n  var _component_global_info_line = _resolveComponent(\"global-info-line\");\n  var _component_global_info = _resolveComponent(\"global-info\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"SegreeSatisfactionDetailName\"\n  }, \"提案办理情况征求意见表\", -1 /* HOISTED */)), _createVNode(_component_global_info, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_global_info_item, {\n        label: \"提案标题\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.info.title), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"提案编号\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.info.serialNumber), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"领衔委员\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.info.suggestUserName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"单位及职务\"\n      }, {\n        default: _withCtx(function () {\n          var _$setup$info$submitUs;\n          return [_createTextVNode(_toDisplayString((_$setup$info$submitUs = $setup.info.submitUserInfo) === null || _$setup$info$submitUs === void 0 ? void 0 : _$setup$info$submitUs.position), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"办理单位\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.info.handleOffices, function (item) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              key: item.handleOfficeId\n            }, _toDisplayString(item.handleOfficeType === 'main' ? '主办' : item.handleOfficeType === 'assist' ? '协办' : '分办') + \"：\" + _toDisplayString(item.handleOfficeName), 1 /* TEXT */);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"提案办理情况\",\n        class: \"SegreeSatisfactionDetailInfo\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"联系沟通情况\",\n            class: \"SegreeSatisfactionDetailInfo\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_global_info_item, {\n                label: \"电话/电邮沟通\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.details.mobileCommunication), 1 /* TEXT */)];\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_global_info_item, {\n                label: \"信函沟通\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.details.letterCommunication), 1 /* TEXT */)];\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_global_info_item, {\n                label: \"当面沟通\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.details.faceCommunication), 1 /* TEXT */)];\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_global_info_item, {\n                label: \"未联系\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.details.nonCommunication), 1 /* TEXT */)];\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"办理态度\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.handleMannerName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"办理结果\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.handleResultName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"对提案工作的建议\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"pre\", null, _toDisplayString($setup.details.content), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createVNode", "_component_global_info", "default", "_withCtx", "_component_global_info_item", "label", "_createTextVNode", "_toDisplayString", "$setup", "info", "title", "_", "_component_global_info_line", "serialNumber", "suggestUserName", "_$setup$info$submitUs", "submitUserInfo", "position", "_Fragment", "_renderList", "handleOffices", "item", "key", "handleOfficeId", "handleOfficeType", "handleOfficeName", "details", "mobileCommunication", "letterCommunication", "faceCommunication", "nonCommunication", "handleMannerName", "handleResultName", "content"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SegreeSatisfactionDetail\\SegreeSatisfactionDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SegreeSatisfactionDetail\">\r\n    <div class=\"SegreeSatisfactionDetailName\">提案办理情况征求意见表</div>\r\n    <global-info>\r\n      <global-info-item label=\"提案标题\">{{ info.title }}</global-info-item>\r\n      <global-info-line>\r\n        <global-info-item label=\"提案编号\">{{ info.serialNumber }}</global-info-item>\r\n        <global-info-item label=\"领衔委员\">{{ info.suggestUserName }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-item label=\"单位及职务\">{{ info.submitUserInfo?.position }}</global-info-item>\r\n      <global-info-item label=\"办理单位\">\r\n        <div v-for=\"item in info.handleOffices\"\r\n             :key=\"item.handleOfficeId\">\r\n          {{ item.handleOfficeType === 'main' ? '主办' : item.handleOfficeType === 'assist' ? '协办' : '分办' }}：{{\r\n            item.handleOfficeName }}\r\n        </div>\r\n      </global-info-item>\r\n      <global-info-item label=\"提案办理情况\"\r\n                        class=\"SegreeSatisfactionDetailInfo\">\r\n        <global-info-item label=\"联系沟通情况\"\r\n                          class=\"SegreeSatisfactionDetailInfo\">\r\n          <global-info-item label=\"电话/电邮沟通\">{{ details.mobileCommunication }}</global-info-item>\r\n          <global-info-item label=\"信函沟通\">{{ details.letterCommunication }}</global-info-item>\r\n          <global-info-item label=\"当面沟通\">{{ details.faceCommunication }}</global-info-item>\r\n          <global-info-item label=\"未联系\">{{ details.nonCommunication }}</global-info-item>\r\n        </global-info-item>\r\n        <global-info-item label=\"办理态度\">{{ details.handleMannerName }}</global-info-item>\r\n        <global-info-item label=\"办理结果\">{{ details.handleResultName }}</global-info-item>\r\n      </global-info-item>\r\n      <global-info-item label=\"对提案工作的建议\">\r\n        <pre>{{ details.content }}</pre>\r\n      </global-info-item>\r\n    </global-info>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SegreeSatisfactionDetail' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' }, suggestId: { type: String, default: '' }, type: { type: Boolean, default: false } })\r\n\r\nconst info = ref({})\r\nconst details = ref({})\r\n\r\n\r\nonMounted(() => {\r\n  suggestionInfo()\r\n  if (props.id) { suggestionSatisfactionInfo() }\r\n  if (props.type) { suggestionSatisfactiondetails() }\r\n})\r\n\r\nconst suggestionInfo = async () => {\r\n  const { data } = await api.suggestionInfo({ detailId: props.suggestId })\r\n  info.value = data\r\n}\r\nconst suggestionSatisfactionInfo = async () => {\r\n  const { data } = await api.suggestionSatisfactionInfo({ detailId: props.id })\r\n  details.value = data\r\n}\r\nconst suggestionSatisfactiondetails = async () => {\r\n  const { data } = await api.suggestionSatisfactionInfo({ suggestionId: props.suggestId })\r\n  details.value = data\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SegreeSatisfactionDetail {\r\n  width: 990px;\r\n  padding: 0 var(--zy-distance-one);\r\n  padding-top: var(--zy-distance-one);\r\n\r\n  .SegreeSatisfactionDetailName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    color: var(--zy-el-color-primary);\r\n    border-bottom: 1px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .global-info {\r\n    padding-bottom: 12px;\r\n\r\n    .global-info-item {\r\n      .global-info-label {\r\n        width: 160px;\r\n      }\r\n\r\n      .global-info-content {\r\n        width: calc(100% - 160px);\r\n      }\r\n    }\r\n\r\n    .SegreeSatisfactionDetailInfo {\r\n      &>.global-info-item {\r\n        &>.global-info-content {\r\n          padding: 0;\r\n          border: 0;\r\n\r\n          &>span {\r\n            width: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;;;;uBAArCC,mBAAA,CAgCM,OAhCNC,UAgCM,G,0BA/BJC,mBAAA,CAA2D;IAAtDH,KAAK,EAAC;EAA8B,GAAC,aAAW,sBACrDI,YAAA,CA6BcC,sBAAA;IAhClBC,OAAA,EAAAC,QAAA,CAIM;MAAA,OAAkE,CAAlEH,YAAA,CAAkEI,2BAAA;QAAhDC,KAAK,EAAC;MAAM;QAJpCH,OAAA,EAAAC,QAAA,CAIqC;UAAA,OAAgB,CAJrDG,gBAAA,CAAAC,gBAAA,CAIwCC,MAAA,CAAAC,IAAI,CAACC,KAAK,iB;;QAJlDC,CAAA;UAKMX,YAAA,CAGmBY,2BAAA;QARzBV,OAAA,EAAAC,QAAA,CAMQ;UAAA,OAAyE,CAAzEH,YAAA,CAAyEI,2BAAA;YAAvDC,KAAK,EAAC;UAAM;YANtCH,OAAA,EAAAC,QAAA,CAMuC;cAAA,OAAuB,CAN9DG,gBAAA,CAAAC,gBAAA,CAM0CC,MAAA,CAAAC,IAAI,CAACI,YAAY,iB;;YAN3DF,CAAA;cAOQX,YAAA,CAA4EI,2BAAA;YAA1DC,KAAK,EAAC;UAAM;YAPtCH,OAAA,EAAAC,QAAA,CAOuC;cAAA,OAA0B,CAPjEG,gBAAA,CAAAC,gBAAA,CAO0CC,MAAA,CAAAC,IAAI,CAACK,eAAe,iB;;YAP9DH,CAAA;;;QAAAA,CAAA;UASMX,YAAA,CAAsFI,2BAAA;QAApEC,KAAK,EAAC;MAAO;QATrCH,OAAA,EAAAC,QAAA,CASsC;UAAA,IAAAY,qBAAA;UAAA,OAAmC,CATzET,gBAAA,CAAAC,gBAAA,EAAAQ,qBAAA,GASyCP,MAAA,CAAAC,IAAI,CAACO,cAAc,cAAAD,qBAAA,uBAAnBA,qBAAA,CAAqBE,QAAQ,iB;;QATtEN,CAAA;UAUMX,YAAA,CAMmBI,2BAAA;QANDC,KAAK,EAAC;MAAM;QAVpCH,OAAA,EAAAC,QAAA,CAWa;UAAA,OAAkC,E,kBAAvCN,mBAAA,CAIMqB,SAAA,QAfdC,WAAA,CAW4BX,MAAA,CAAAC,IAAI,CAACW,aAAa,EAX9C,UAWoBC,IAAI;iCAAhBxB,mBAAA,CAIM;cAHAyB,GAAG,EAAED,IAAI,CAACE;gCACXF,IAAI,CAACG,gBAAgB,qBAAqBH,IAAI,CAACG,gBAAgB,+BAA8B,GAAC,GAAAjB,gBAAA,CAC/Fc,IAAI,CAACI,gBAAgB;;;QAdjCd,CAAA;UAiBMX,YAAA,CAWmBI,2BAAA;QAXDC,KAAK,EAAC,QAAQ;QACdT,KAAK,EAAC;;QAlB9BM,OAAA,EAAAC,QAAA,CAmBQ;UAAA,OAMmB,CANnBH,YAAA,CAMmBI,2BAAA;YANDC,KAAK,EAAC,QAAQ;YACdT,KAAK,EAAC;;YApBhCM,OAAA,EAAAC,QAAA,CAqBU;cAAA,OAAsF,CAAtFH,YAAA,CAAsFI,2BAAA;gBAApEC,KAAK,EAAC;cAAS;gBArB3CH,OAAA,EAAAC,QAAA,CAqB4C;kBAAA,OAAiC,CArB7EG,gBAAA,CAAAC,gBAAA,CAqB+CC,MAAA,CAAAkB,OAAO,CAACC,mBAAmB,iB;;gBArB1EhB,CAAA;kBAsBUX,YAAA,CAAmFI,2BAAA;gBAAjEC,KAAK,EAAC;cAAM;gBAtBxCH,OAAA,EAAAC,QAAA,CAsByC;kBAAA,OAAiC,CAtB1EG,gBAAA,CAAAC,gBAAA,CAsB4CC,MAAA,CAAAkB,OAAO,CAACE,mBAAmB,iB;;gBAtBvEjB,CAAA;kBAuBUX,YAAA,CAAiFI,2BAAA;gBAA/DC,KAAK,EAAC;cAAM;gBAvBxCH,OAAA,EAAAC,QAAA,CAuByC;kBAAA,OAA+B,CAvBxEG,gBAAA,CAAAC,gBAAA,CAuB4CC,MAAA,CAAAkB,OAAO,CAACG,iBAAiB,iB;;gBAvBrElB,CAAA;kBAwBUX,YAAA,CAA+EI,2BAAA;gBAA7DC,KAAK,EAAC;cAAK;gBAxBvCH,OAAA,EAAAC,QAAA,CAwBwC;kBAAA,OAA8B,CAxBtEG,gBAAA,CAAAC,gBAAA,CAwB2CC,MAAA,CAAAkB,OAAO,CAACI,gBAAgB,iB;;gBAxBnEnB,CAAA;;;YAAAA,CAAA;cA0BQX,YAAA,CAAgFI,2BAAA;YAA9DC,KAAK,EAAC;UAAM;YA1BtCH,OAAA,EAAAC,QAAA,CA0BuC;cAAA,OAA8B,CA1BrEG,gBAAA,CAAAC,gBAAA,CA0B0CC,MAAA,CAAAkB,OAAO,CAACK,gBAAgB,iB;;YA1BlEpB,CAAA;cA2BQX,YAAA,CAAgFI,2BAAA;YAA9DC,KAAK,EAAC;UAAM;YA3BtCH,OAAA,EAAAC,QAAA,CA2BuC;cAAA,OAA8B,CA3BrEG,gBAAA,CAAAC,gBAAA,CA2B0CC,MAAA,CAAAkB,OAAO,CAACM,gBAAgB,iB;;YA3BlErB,CAAA;;;QAAAA,CAAA;UA6BMX,YAAA,CAEmBI,2BAAA;QAFDC,KAAK,EAAC;MAAU;QA7BxCH,OAAA,EAAAC,QAAA,CA8BQ;UAAA,OAAgC,CAAhCJ,mBAAA,CAAgC,aAAAQ,gBAAA,CAAxBC,MAAA,CAAAkB,OAAO,CAACO,OAAO,iB;;QA9B/BtB,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}