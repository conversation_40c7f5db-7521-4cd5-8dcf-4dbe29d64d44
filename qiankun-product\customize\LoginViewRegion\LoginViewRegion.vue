<template>
  <div class="LoginViewRegion">
    <el-carousel class="LoginViewRegionCarousel" v-if="imgList.length" height="100%">
      <el-carousel-item class="LoginViewRegionCarouselBox" v-for="item in imgList" :key="item.id">
        <el-image :src="item.imgPath" loading="lazy" fit="cover" />
      </el-carousel-item>
    </el-carousel>
    <div class="LoginViewRegionBox">
      <div class="LoginViewRegionLogo">
        <el-image :src="systemLogo" fit="cover" />
      </div>
      <div class="LoginViewRegionName" v-html="loginSystemName"></div>
      <el-form ref="LoginForm" :model="form" :rules="rules" class="LoginViewRegionForm">
        <el-form-item prop="account">
          <el-input v-model="form.account" placeholder="账号/手机号" @blur="handleBlur" clearable />
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" v-model="form.password" placeholder="密码" show-password clearable />
        </el-form-item>
        <el-form-item class="smsValidation" v-if="loginVerifyShow && whetherVerifyCode" prop="verifyCode">
          <el-input v-model="form.verifyCode" placeholder="短信验证码" clearable></el-input>
          <el-button type="primary" @click="handleGetVerifyCode" :disabled="countDownText != '获取验证码'">
            {{ countDownText }}
          </el-button>
        </el-form-item>
        <div class="LoginViewRegionSlideVerify" v-if="loginVerifyShow && !whetherVerifyCode">
          <xyl-slide-verify ref="slideVerify" @again="onAgain" @success="onSuccess" :disabled="disabled" />
        </div>
        <div class="LoginViewRegionFormOperation">
          <el-checkbox v-model="checked">记住用户名和密码</el-checkbox>
          <div class="LoginViewRegionFormOperationText" @click="show = !show">忘记密码？</div>
        </div>
        <el-button type="primary" @click="submitForm(LoginForm)" class="LoginViewRegionFormButton" :loading="loading"
          :disabled="loginDisabled">
          {{ loading ? '登录中' : '登录' }}
        </el-button>
      </el-form>
      <div class="LoginViewRegionOperation" v-if="appDownloadUrl">
        <div class="LoginViewRegionOperationBox">
          <el-popover placement="top" width="auto" @show="refresh" @hide="hideQrcode">
            <div class="LoginViewRegionQrCodeBox">
              <div class="LoginViewRegionQrCodeNameBody">
                <div class="LoginViewRegionQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="LoginViewRegionQrCodeName">APP扫码登录</div>
              </div>
              <div class="LoginViewRegionQrCodeRefreshBody">
                <qrcode-vue :value="loginQrcode" :size="120" />
                <div class="LoginViewRegionQrCodeRefresh" v-show="loginQrcodeShow">
                  <el-button type="primary" @click="refresh">刷新</el-button>
                </div>
              </div>
              <div class="LoginViewRegionQrCodeText">请使用{{ systemName }}APP扫码登录</div>
            </div>
            <template #reference>
              <div class="LoginViewRegionQrCode"></div>
            </template>
          </el-popover>
          <div class="LoginViewRegionOperationText">APP扫码登录</div>
        </div>
        <div class="LoginViewRegionOperationBox">
          <el-popover placement="top" width="auto">
            <div class="LoginViewRegionQrCodeBox">
              <div class="LoginViewRegionQrCodeNameBody">
                <div class="LoginViewRegionQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="LoginViewRegionQrCodeName">手机APP下载</div>
              </div>
              <qrcode-vue :value="appDownloadUrl" :size="120" />
              <div class="LoginViewRegionQrCodeText">使用其他软件扫码下载{{ systemName }}APP</div>
            </div>
            <template #reference>
              <div class="LoginViewRegionApp"></div>
            </template>
          </el-popover>
          <div class="LoginViewRegionOperationText">手机APP下载</div>
        </div>
      </div>
      <div class="LoginViewRegionSystemTips" v-if="systemLoginContact">{{ systemLoginContact }}</div>
    </div>
    <xyl-popup-window v-model="show" name="重置密码">
      <ResetPassword @callback="show = !show"></ResetPassword>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'LoginViewRegion' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, computed, defineAsyncComponent } from 'vue'
import { useRoute } from 'vue-router'
import {
  systemLogo,
  systemName,
  loginNameLineFeedPosition,
  appDownloadUrl,
  systemLoginContact
} from 'common/js/system_var.js'
import { LoginView } from '../LoginView/LoginView.js'
const QrcodeVue = defineAsyncComponent(() => import('qrcode.vue'))
const ResetPassword = defineAsyncComponent(() => import('../LoginView/component/ResetPassword.vue'))
const route = useRoute()
const show = ref(false)
const imgList = ref([])
const localAreaName = ref('')
const localSystemName = ref('')
const localLoginNameLineFeedPosition = ref('')
const loginSystemName = computed(() => {
  const name = (localAreaName.value || '') + (localSystemName.value || systemName.value)
  const num = Number(localLoginNameLineFeedPosition.value || loginNameLineFeedPosition.value || '0') || 0
  return num ? name.substring(0, num) + '\n' + name.substring(num) : name
})
const globalData = async () => {
  const { data } = await api.loginImgRegion({}, route.query.areaId)
  data.forEach((item) => {
    item.imgPath = api.fileURL(item.imgPath)
  })
  imgList.value = data
}
const globalReadConfig = async () => {
  const { data } = await api.readOpenConfigRegion(
    { codes: ['systemName', 'localAreaName', 'loginNameLineFeedPosition'] },
    route.query.areaId
  )
  localSystemName.value = data?.systemName || ''
  localAreaName.value = data?.localAreaName || ''
  localLoginNameLineFeedPosition.value = data?.loginNameLineFeedPosition || ''
}
const {
  loginVerifyShow,
  whetherVerifyCode,
  loginDisabled,
  loading,
  checked,
  LoginForm,
  form,
  rules,
  countDownText,
  slideVerify,
  disabled,
  loginQrcode,
  loginQrcodeShow,
  handleBlur,
  handleGetVerifyCode,
  onAgain,
  onSuccess,
  submitForm,
  loginInfo,
  refresh,
  hideQrcode
} = LoginView('/LoginViewRegion', { areaId: route.query.areaId })
onMounted(() => {
  loginInfo()
  globalData()
  globalReadConfig()
})
</script>
<style lang="scss">
.LoginViewRegion {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .LoginViewRegionCarousel {
    width: 100%;
    height: 100%;
    position: absolute;

    .LoginViewRegionCarouselBox {
      width: 100%;
      height: 100%;

      .zy-el-image {
        width: 100%;
        height: 100%;
      }
    }

    .zy-el-carousel__indicators--horizontal {
      .zy-el-carousel__button {
        width: 16px;
        height: 16px;
        border-radius: 50%;
      }
    }
  }

  .LoginViewRegionBox {
    padding: var(--zy-distance-one);
    box-shadow: var(--zy-el-box-shadow);
    padding-bottom: var(--zy-distance-two);
    border-radius: var(--el-border-radius-base);
    margin-right: 80px;
    background: #fff;
    position: relative;
    z-index: 2;

    .LoginViewRegionLogo {
      width: 60px;
      margin: auto;
      margin-bottom: var(--zy-distance-two);

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .LoginViewRegionName {
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: var(--zy-system-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      letter-spacing: 2px;
      padding-bottom: var(--zy-distance-one);
      white-space: pre-wrap;
      margin: auto;
    }

    .LoginViewRegionForm {
      width: 320px;
      margin: auto;
      padding-bottom: var(--zy-distance-one);

      input:-webkit-autofill {
        transition: background-color 5000s ease-in-out 0s;
      }

      .zy-el-form-item {
        margin-bottom: var(--zy-form-distance-bottom);
      }

      .LoginViewRegionFormButton {
        width: 100%;
      }

      .smsValidation {
        .zy-el-form-item__content {
          display: flex;
          justify-content: space-between;
        }

        .zy-el-input {
          width: 56%;
        }
      }

      .LoginViewRegionSlideVerify {
        margin-bottom: var(--zy-distance-five);
      }

      .LoginViewRegionFormOperation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--zy-distance-three);

        .zy-el-checkbox {
          height: var(--zy-height-secondary);
        }

        .LoginViewRegionFormOperationText {
          cursor: pointer;
          color: var(--zy-el-color-primary);
          font-size: var(--zy-text-font-size);
        }
      }
    }

    .LoginViewRegionOperation {
      width: 100%;
      padding-bottom: var(--zy-distance-two);
      display: flex;
      justify-content: space-between;

      .LoginViewRegionOperationBox {
        margin: 0 var(--zy-distance-two);
        cursor: pointer;

        .LoginViewRegionQrCode {
          width: 50px;
          height: 50px;
          background: url('../img/login_qr_code.png');
          background-size: 100% 100%;
          margin: auto;
        }

        .LoginViewRegionApp {
          width: 50px;
          height: 50px;
          background: url('../img/login_app.png') no-repeat;
          background-size: auto 100%;
          background-position: center;
          margin: auto;
        }

        .LoginViewRegionOperationText {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          padding: var(--el-border-radius-small) 0;
          text-align: center;
        }
      }
    }

    .LoginViewRegionForm+.LoginViewRegionSystemTips {
      padding-top: var(--zy-distance-one);
    }

    .LoginViewRegionSystemTips {
      color: var(--zy-el-text-color-secondary);
      font-size: var(--zy-text-font-size);
      text-align: center;
    }
  }
}

.LoginViewRegionQrCodeBox {
  width: 320px;
  background-color: #fff;

  canvas {
    display: block;
    margin: auto;
  }

  .LoginViewRegionQrCodeNameBody {
    padding: var(--zy-distance-three);
    display: flex;
    align-items: center;
    justify-content: center;

    .LoginViewRegionQrCodeLogo {
      width: 26px;
      margin-right: 6px;

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .LoginViewRegionQrCodeName {
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
    }
  }

  .LoginViewRegionQrCodeRefreshBody {
    position: relative;

    .LoginViewRegionQrCodeRefresh {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 120px;
      background-color: rgba(000, 000, 000, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;

      .zy-el-button {
        --zy-el-button-size: var(--zy-height-secondary);
      }
    }
  }

  .LoginViewRegionQrCodeText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-three);
    color: var(--zy-el-color-primary);
  }
}
</style>
