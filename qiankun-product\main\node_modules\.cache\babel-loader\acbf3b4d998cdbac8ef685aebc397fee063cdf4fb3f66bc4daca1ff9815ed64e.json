{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, vShow as _vShow, withDirectives as _withDirectives, withKeys as _withKeys } from \"vue\";\nimport _imports_0 from '../img/AiIcon3.png';\nimport _imports_1 from '../img/AiIcon2.png';\nimport _imports_2 from '../img/AiIcon1.png';\nimport _imports_3 from '../img/tongjiIcon.png';\nvar _hoisted_1 = {\n  class: \"AiUseStatistics-header\"\n};\nvar _hoisted_2 = {\n  class: \"AiUseStatistics-header-box\"\n};\nvar _hoisted_3 = {\n  class: \"AiUseStatistics-header-content\"\n};\nvar _hoisted_4 = {\n  class: \"AiUseStatistics-header-content-item\"\n};\nvar _hoisted_5 = {\n  class: \"AiUseStatistics-header-content-item-left\"\n};\nvar _hoisted_6 = {\n  class: \"AiUseStatistics-header-content-item-left-num\"\n};\nvar _hoisted_7 = {\n  class: \"AiUseStatistics-header-content-item\"\n};\nvar _hoisted_8 = {\n  class: \"AiUseStatistics-header-content-item-left\"\n};\nvar _hoisted_9 = {\n  class: \"AiUseStatistics-header-content-item-left-num\"\n};\nvar _hoisted_10 = {\n  class: \"AiUseStatistics-header-content-item\"\n};\nvar _hoisted_11 = {\n  class: \"AiUseStatistics-header-content-item-left\"\n};\nvar _hoisted_12 = {\n  class: \"AiUseStatistics-header-content-item-left-num\"\n};\nvar _hoisted_13 = {\n  class: \"AiUseStatistics-chart\"\n};\nvar _hoisted_14 = {\n  class: \"AiUseStatistics-chart-box\"\n};\nvar _hoisted_15 = {\n  class: \"AiUseStatistics-chart-timeSelect\"\n};\nvar _hoisted_16 = {\n  class: \"AiUseStatistics-chart-box-right\"\n};\nvar _hoisted_17 = {\n  class: \"AiUseStatistics-chart-content\"\n};\nvar _hoisted_18 = {\n  class: \"AiUseStatistics-chart-content-left\"\n};\nvar _hoisted_19 = {\n  class: \"AiUseStatistics-chart-content-right-title\"\n};\nvar _hoisted_20 = {\n  class: \"AiUseStatistics-chart-content-right-title-right\"\n};\nvar _hoisted_21 = {\n  class: \"hotWordBox\"\n};\nvar _hoisted_22 = {\n  class: \"hotWordContent\"\n};\nvar _hoisted_23 = {\n  class: \"hotWordBox-content\"\n};\nvar _hoisted_24 = {\n  class: \"hotWordBox-content-title\"\n};\nvar _hoisted_25 = {\n  class: \"hotWordBox-content-list\"\n};\nvar _hoisted_26 = {\n  class: \"hotWordBox-content-list-item-title\"\n};\nvar _hoisted_27 = {\n  class: \"hotWordBox-content-list-item-num\"\n};\nvar _hoisted_28 = {\n  class: \"AiUseStatistics-chart-content-right\"\n};\nvar _hoisted_29 = {\n  class: \"AiUseStatistics-chart-content-right-title\"\n};\nvar _hoisted_30 = {\n  class: \"AiUseStatistics-chart-content-right-title-right\"\n};\nvar _hoisted_31 = {\n  class: \"AiUseStatistics-chart-content-right-content\"\n};\nvar _hoisted_32 = {\n  class: \"globalTable\"\n};\nvar _hoisted_33 = {\n  class: \"globalPagination\"\n};\nvar _hoisted_34 = {\n  class: \"AiUseStatistics-detail\"\n};\nvar _hoisted_35 = {\n  class: \"detail-item-title\"\n};\nvar _hoisted_36 = {\n  class: \"detail-item-content\"\n};\nvar _hoisted_37 = {\n  class: \"chatDetail\"\n};\nvar _hoisted_38 = {\n  class: \"questionHead\"\n};\nvar _hoisted_39 = {\n  class: \"questionName\"\n};\nvar _hoisted_40 = {\n  class: \"questionTime\"\n};\nvar _hoisted_41 = {\n  type: \"primary\"\n};\nvar _hoisted_42 = {\n  class: \"questionContent\"\n};\nvar _hoisted_43 = {\n  class: \"questionContent-title\"\n};\nvar _hoisted_44 = [\"innerHTML\"];\nvar _hoisted_45 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_RefreshRight = _resolveComponent(\"RefreshRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_ArrowRight = _resolveComponent(\"ArrowRight\");\n  var _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_tag = _resolveComponent(\"el-tag\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"AiUseStatistics\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-left\"\n      }, [_createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-left-title\"\n      }, \"AI使用统计分析\"), _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-left-desc\"\n      }, \"实时监控AI服务使用情况与趋势分析\")], -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-right\",\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.aigptStatistics(true);\n        })\n      }, [_cache[8] || (_cache[8] = _createTextVNode(\" 刷新数据 \")), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_RefreshRight)];\n        }),\n        _: 1 /* STABLE */\n      })])]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-left-title\"\n      }, [_createTextVNode(\" 服务总人次 \"), _createCommentVNode(\" <img src=\\\"../img/AiHelp.png\\\" alt=\\\"\\\" /> \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.overviewData.totalServiceTimes || 0), 1 /* TEXT */)]), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-right\"\n      }, [_createElementVNode(\"img\", {\n        src: _imports_0,\n        alt: \"\"\n      })], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-left-title\"\n      }, [_createTextVNode(\" 服务总人数 \"), _createCommentVNode(\" <img src=\\\"../img/AiHelp.png\\\" alt=\\\"\\\" /> \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.overviewData.totalUsers || 0), 1 /* TEXT */)]), _cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-right\"\n      }, [_createElementVNode(\"img\", {\n        src: _imports_1,\n        alt: \"\"\n      })], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-left-title\"\n      }, [_createTextVNode(\" 累计问答次数 \"), _createCommentVNode(\" <img src=\\\"../img/AiHelp.png\\\" alt=\\\"\\\" /> \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createTextVNode(_toDisplayString($setup.overviewData.totalAnswerTimes || 0) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", {\n        onClick: $setup.handleDetail\n      }, [_cache[14] || (_cache[14] = _createTextVNode(\" 详情 \")), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowRight)];\n        }),\n        _: 1 /* STABLE */\n      })])])]), _cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-right\"\n      }, [_createElementVNode(\"img\", {\n        src: _imports_2,\n        alt: \"\"\n      })], -1 /* HOISTED */))])])]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_date_picker, {\n        modelValue: $setup.year,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.year = $event;\n        }),\n        clearable: false,\n        type: \"year\",\n        \"value-format\": \"YYYY\",\n        placeholder: \"选择年份\",\n        onChange: $setup.getLineData\n      }, null, 8 /* PROPS */, [\"modelValue\"])]), _createVNode($setup[\"barAndPie\"], {\n        data: $setup.lineData\n      }, null, 8 /* PROPS */, [\"data\"])]), _createElementVNode(\"div\", _hoisted_16, [_createVNode($setup[\"cockpitChart\"], {\n        data: $setup.detailData\n      }, null, 8 /* PROPS */, [\"data\"])])]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-chart-content-right-title-left\"\n      }, \"业务线热词分布\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_20, [_createVNode(_component_el_select, {\n        modelValue: $setup.hottype,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.hottype = $event;\n        }),\n        placeholder: \"请选择业务线\",\n        onChange: $setup.getHotWordData,\n        filterable: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.hottypeList, function (item) {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.id,\n              label: item.name,\n              value: item.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])])]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createVNode($setup[\"wordCloudChart\"], {\n        data: $setup.hotWordData\n      }, null, 8 /* PROPS */, [\"data\"])])]), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString($setup.getHottypeName()), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.hotWordData, function (item, index) {\n        return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n          class: \"hotWordBox-content-list-item\",\n          key: item.id\n        }, [_createElementVNode(\"div\", _hoisted_26, _toDisplayString(item.hotWord), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_27, _toDisplayString(item.appearTimes) + \" 次\", 1 /* TEXT */)])), [[_vShow, index < 3]]);\n      }), 128 /* KEYED_FRAGMENT */))])])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-chart-content-right-title-left\"\n      }, \"近期对话记录\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_30, [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"]), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.handleQuery\n      }, {\n        default: _withCtx(function () {\n          return _cache[18] || (_cache[18] = [_createTextVNode(\"搜索\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"]), _createCommentVNode(\" 查看更多\\r\\n            <el-icon><ArrowRight /></el-icon> \")])]), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createVNode(_component_el_table, {\n        ref: \"tableRef\",\n        \"row-key\": \"id\",\n        data: $setup.tableData\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_table_column, {\n            label: \"用户\",\n            prop: \"createUserName\",\n            width: \"100\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"对话内容\",\n            prop: \"userQuestion\",\n            \"min-width\": \"120\"\n          }, {\n            default: _withCtx(function (_ref) {\n              var row = _ref.row;\n              return [_createCommentVNode(\" <el-tooltip\\r\\n                    popper-class=\\\"AiUseStatistics-tooltip\\\"\\r\\n                    :effect=\\\"'light'\\\"\\r\\n                    :content=\\\"removeHtmlTag(row.promptQuestion)\\\"\\r\\n                    :disabled=\\\"row.promptQuestion.length < 10\\\"\\r\\n                    placement=\\\"top-start\\\">\\r\\n                    <span class=\\\"AiUseStatistics-tooltip-content\\\">{{ removeHtmlTag(row.promptQuestion) }}</span>\\r\\n                  </el-tooltip> \"), _createCommentVNode(\" <span class=\\\"AiUseStatistics-tooltip-content\\\">{{ removeHtmlTag(row.promptQuestion) }}</span> \"), _createVNode(_component_el_button, {\n                type: \"primary\",\n                link: \"\",\n                text: \"\",\n                onClick: function onClick($event) {\n                  return $setup.lookDetail(row);\n                },\n                class: \"AiUseStatistics-tooltip-content\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.removeHtmlTag(row.userQuestion)), 1 /* TEXT */)];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_table_column, {\n            label: \"业务线\",\n            prop: \"chatBusinessName\",\n            width: \"180\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"时间\",\n            prop: \"createDate\",\n            width: \"160\"\n          }, {\n            default: _withCtx(function (_ref2) {\n              var row = _ref2.row;\n              return [_createTextVNode(_toDisplayString(row.createDate ? $setup.format(row.createDate, 'YYYY-MM-DD HH:mm') : ''), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), false ? (_openBlock(), _createBlock(_component_xyl_global_table_button, {\n            key: 0,\n            data: $setup.tableButtonList,\n            onButtonClick: _ctx.handleCommand\n          }, null, 8 /* PROPS */, [\"onButtonClick\"])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"data\"])]), _createElementVNode(\"div\", _hoisted_33, [_createVNode(_component_el_pagination, {\n        currentPage: $setup.pageNo,\n        \"onUpdate:currentPage\": _cache[4] || (_cache[4] = function ($event) {\n          return $setup.pageNo = $event;\n        }),\n        \"page-size\": $setup.pageSize,\n        \"onUpdate:pageSize\": _cache[5] || (_cache[5] = function ($event) {\n          return $setup.pageSize = $event;\n        }),\n        \"page-sizes\": $setup.pageSizes,\n        layout: \"total, sizes, prev, pager, next, jumper\",\n        onSizeChange: $setup.handleQuery,\n        onCurrentChange: $setup.handleQuery,\n        total: $setup.totals,\n        background: \"\"\n      }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])])])])]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.showMore,\n        \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n          return $setup.showMore = $event;\n        }),\n        name: \"详情\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_34, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.detailData, function (item) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"detail-item\",\n              key: item.id\n            }, [_createElementVNode(\"div\", _hoisted_35, [_cache[20] || (_cache[20] = _createElementVNode(\"img\", {\n              src: _imports_3,\n              alt: \"\"\n            }, null, -1 /* HOISTED */)), _createVNode(_component_el_tooltip, {\n              content: item.name,\n              disabled: item.name.length < 10,\n              placement: \"top\"\n            }, {\n              default: _withCtx(function () {\n                return [_createElementVNode(\"span\", null, _toDisplayString(item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name), 1 /* TEXT */)];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"content\", \"disabled\"])]), _createElementVNode(\"div\", _hoisted_36, [_createTextVNode(_toDisplayString(item.count) + \" \", 1 /* TEXT */), _cache[21] || (_cache[21] = _createElementVNode(\"span\", null, \"次\", -1 /* HOISTED */))])]);\n          }), 128 /* KEYED_FRAGMENT */))])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.showDetail,\n        \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n          return $setup.showDetail = $event;\n        }),\n        name: \"近期对话详情\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createVNode(_component_el_image, {\n            src: $setup.imgUrl($setup.chatDetail.headImg),\n            fit: \"cover\"\n          }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", null, _toDisplayString($setup.chatDetail.createUserName), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_40, [_createVNode(_component_el_tag, {\n            type: \"primary\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.chatDetail.chatBusinessName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createElementVNode(\"span\", _hoisted_41, \"提问于： \" + _toDisplayString($setup.format($setup.chatDetail.createDate, 'YYYY-MM-DD HH:mm')), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_createCommentVNode(\" <span>{{ chatDetail.userQuestion }}</span> \"), _createElementVNode(\"div\", {\n            innerHTML: $setup.chatDetail.userQuestion\n          }, null, 8 /* PROPS */, _hoisted_44)]), _createElementVNode(\"div\", {\n            class: \"questionContent-answer\",\n            innerHTML: $setup.chatDetail.answer\n          }, null, 8 /* PROPS */, _hoisted_45)])])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "_imports_3", "class", "type", "_createBlock", "_component_el_scrollbar", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "_hoisted_2", "onClick", "_cache", "$event", "$setup", "aigptStatistics", "_createTextVNode", "_createVNode", "_component_el_icon", "_component_RefreshRight", "_", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_createCommentVNode", "_hoisted_6", "_toDisplayString", "overviewData", "totalServiceTimes", "src", "alt", "_hoisted_7", "_hoisted_8", "_hoisted_9", "totalUsers", "_hoisted_10", "_hoisted_11", "_hoisted_12", "totalAnswerTimes", "handleDetail", "_component_ArrowRight", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_component_el_date_picker", "modelValue", "year", "clearable", "placeholder", "onChange", "getLineData", "data", "lineData", "_hoisted_16", "detailData", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_component_el_select", "hottype", "getHotWordData", "filterable", "_createElementBlock", "_Fragment", "_renderList", "hottypeList", "item", "_component_el_option", "key", "id", "label", "name", "value", "_hoisted_21", "_hoisted_22", "hotWordData", "_hoisted_23", "_hoisted_24", "getHottypeName", "_hoisted_25", "index", "_hoisted_26", "hotWord", "_hoisted_27", "appearTimes", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_component_el_input", "keyword", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handleQuery", "_component_el_button", "_hoisted_31", "_hoisted_32", "_component_el_table", "ref", "tableData", "_component_el_table_column", "prop", "width", "_ref", "row", "link", "text", "lookDetail", "removeHtmlTag", "userQuestion", "_ref2", "createDate", "format", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "_ctx", "handleCommand", "_hoisted_33", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "showMore", "_hoisted_34", "_hoisted_35", "_component_el_tooltip", "content", "disabled", "length", "placement", "slice", "_hoisted_36", "count", "showDetail", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_component_el_image", "imgUrl", "chatDetail", "headImg", "fit", "createUserName", "_hoisted_40", "_component_el_tag", "chatBusinessName", "_hoisted_41", "_hoisted_42", "_hoisted_43", "innerHTML", "_hoisted_44", "answer", "_hoisted_45"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiUseStatistics\\AiUseStatistics.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"AiUseStatistics\">\r\n    <div class=\"AiUseStatistics-header\">\r\n      <div class=\"AiUseStatistics-header-box\">\r\n        <div class=\"AiUseStatistics-header-left\">\r\n          <div class=\"AiUseStatistics-header-left-title\">AI使用统计分析</div>\r\n          <div class=\"AiUseStatistics-header-left-desc\">实时监控AI服务使用情况与趋势分析</div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-right\" @click=\"aigptStatistics(true)\">\r\n          刷新数据\r\n          <el-icon><RefreshRight /></el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiUseStatistics-header-content\">\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              服务总人次\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">{{ overviewData.totalServiceTimes || 0 }}</div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon3.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              服务总人数\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">{{ overviewData.totalUsers || 0 }}</div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon2.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              累计问答次数\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">\r\n              {{ overviewData.totalAnswerTimes || 0 }}\r\n              <span @click=\"handleDetail\">\r\n                详情\r\n                <el-icon><ArrowRight /></el-icon>\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon1.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"AiUseStatistics-chart\">\r\n      <div class=\"AiUseStatistics-chart-box\">\r\n        <div class=\"AiUseStatistics-chart-timeSelect\">\r\n          <el-date-picker\r\n            v-model=\"year\"\r\n            :clearable=\"false\"\r\n            type=\"year\"\r\n            value-format=\"YYYY\"\r\n            placeholder=\"选择年份\"\r\n            @change=\"getLineData\" />\r\n        </div>\r\n        <barAndPie :data=\"lineData\" />\r\n      </div>\r\n      <div class=\"AiUseStatistics-chart-box-right\">\r\n        <cockpitChart :data=\"detailData\" />\r\n      </div>\r\n    </div>\r\n    <div class=\"AiUseStatistics-chart-content\">\r\n      <div class=\"AiUseStatistics-chart-content-left\">\r\n        <div class=\"AiUseStatistics-chart-content-right-title\">\r\n          <div class=\"AiUseStatistics-chart-content-right-title-left\">业务线热词分布</div>\r\n          <div class=\"AiUseStatistics-chart-content-right-title-right\">\r\n            <el-select v-model=\"hottype\" placeholder=\"请选择业务线\" @change=\"getHotWordData\" filterable>\r\n              <el-option v-for=\"item in hottypeList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"hotWordBox\">\r\n          <div class=\"hotWordContent\">\r\n            <wordCloudChart :data=\"hotWordData\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"hotWordBox-content\">\r\n          <div class=\"hotWordBox-content-title\">\r\n            {{ getHottypeName() }}\r\n          </div>\r\n          <div class=\"hotWordBox-content-list\">\r\n            <div\r\n              class=\"hotWordBox-content-list-item\"\r\n              v-show=\"index < 3\"\r\n              v-for=\"(item, index) in hotWordData\"\r\n              :key=\"item.id\">\r\n              <div class=\"hotWordBox-content-list-item-title\">\r\n                {{ item.hotWord }}\r\n              </div>\r\n              <div class=\"hotWordBox-content-list-item-num\">{{ item.appearTimes }} 次</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiUseStatistics-chart-content-right\">\r\n        <div class=\"AiUseStatistics-chart-content-right-title\">\r\n          <div class=\"AiUseStatistics-chart-content-right-title-left\">近期对话记录</div>\r\n          <div class=\"AiUseStatistics-chart-content-right-title-right\">\r\n            <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n            <el-button type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <!-- 查看更多\r\n            <el-icon><ArrowRight /></el-icon> -->\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-chart-content-right-content\">\r\n          <div class=\"globalTable\">\r\n            <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\">\r\n              <el-table-column label=\"用户\" prop=\"createUserName\" width=\"100\"></el-table-column>\r\n              <el-table-column label=\"对话内容\" prop=\"userQuestion\" min-width=\"120\">\r\n                <template #default=\"{ row }\">\r\n                  <!-- <el-tooltip\r\n                    popper-class=\"AiUseStatistics-tooltip\"\r\n                    :effect=\"'light'\"\r\n                    :content=\"removeHtmlTag(row.promptQuestion)\"\r\n                    :disabled=\"row.promptQuestion.length < 10\"\r\n                    placement=\"top-start\">\r\n                    <span class=\"AiUseStatistics-tooltip-content\">{{ removeHtmlTag(row.promptQuestion) }}</span>\r\n                  </el-tooltip> -->\r\n                  <!-- <span class=\"AiUseStatistics-tooltip-content\">{{ removeHtmlTag(row.promptQuestion) }}</span> -->\r\n                  <el-button type=\"primary\" link text @click=\"lookDetail(row)\" class=\"AiUseStatistics-tooltip-content\">\r\n                    {{ removeHtmlTag(row.userQuestion) }}\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"业务线\" prop=\"chatBusinessName\" width=\"180\"></el-table-column>\r\n              <el-table-column label=\"时间\" prop=\"createDate\" width=\"160\">\r\n                <template #default=\"{ row }\">\r\n                  {{ row.createDate ? format(row.createDate, 'YYYY-MM-DD HH:mm') : '' }}\r\n                </template>\r\n              </el-table-column>\r\n              <xyl-global-table-button\r\n                v-if=\"false\"\r\n                :data=\"tableButtonList\"\r\n                @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n            </el-table>\r\n          </div>\r\n          <div class=\"globalPagination\">\r\n            <el-pagination\r\n              v-model:currentPage=\"pageNo\"\r\n              v-model:page-size=\"pageSize\"\r\n              :page-sizes=\"pageSizes\"\r\n              layout=\"total, sizes, prev, pager, next, jumper\"\r\n              @size-change=\"handleQuery\"\r\n              @current-change=\"handleQuery\"\r\n              :total=\"totals\"\r\n              background />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"showMore\" name=\"详情\">\r\n      <div class=\"AiUseStatistics-detail\">\r\n        <div class=\"detail-item\" v-for=\"item in detailData\" :key=\"item.id\">\r\n          <div class=\"detail-item-title\">\r\n            <img src=\"../img/tongjiIcon.png\" alt=\"\" />\r\n            <el-tooltip :content=\"item.name\" :disabled=\"item.name.length < 10\" placement=\"top\">\r\n              <span>{{ item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name }}</span>\r\n            </el-tooltip>\r\n          </div>\r\n          <div class=\"detail-item-content\">\r\n            {{ item.count }}\r\n            <span>次</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"showDetail\" name=\"近期对话详情\">\r\n      <div class=\"chatDetail\">\r\n        <div class=\"questionHead\">\r\n          <div class=\"questionName\">\r\n            <el-image :src=\"imgUrl(chatDetail.headImg)\" fit=\"cover\" />\r\n            <span>{{ chatDetail.createUserName }}</span>\r\n          </div>\r\n          <div class=\"questionTime\">\r\n            <el-tag type=\"primary\">{{ chatDetail.chatBusinessName }}</el-tag>\r\n            <span type=\"primary\">提问于： {{ format(chatDetail.createDate, 'YYYY-MM-DD HH:mm') }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"questionContent\">\r\n          <div class=\"questionContent-title\">\r\n            <!-- <span>{{ chatDetail.userQuestion }}</span> -->\r\n            <div v-html=\"chatDetail.userQuestion\"></div>\r\n          </div>\r\n          <div class=\"questionContent-answer\" v-html=\"chatDetail.answer\"></div>\r\n        </div>\r\n      </div>\r\n    </xyl-popup-window>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'AiUseStatistics'\r\n}\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport api from '@/api'\r\nimport barAndPie from './common/barAndPie.vue'\r\nimport wordCloudChart from './common/wordCloudChart.vue'\r\nimport cockpitChart from './common/cockpitChart.vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nconst tableButtonList = [{ id: 'edit', name: '查看详情', width: 100, has: '' }]\r\n\r\nconst imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))\r\nconst { tableRef, totals, pageNo, pageSize, pageSizes, tableData, keyword, handleQuery } = GlobalTable({\r\n  tableApi: 'aigptChatLogsList'\r\n})\r\n\r\nonMounted(() => {\r\n  aigptStatistics()\r\n  getLineData()\r\n  handleQuery()\r\n  getHottypeList()\r\n  handleDetail()\r\n})\r\nconst overviewData = ref({})\r\nconst aigptStatistics = async (forceRefresh = false) => {\r\n  const res = await api.globalJson('/aigptStatistics/overview', { forceRefresh })\r\n  overviewData.value = res.data\r\n}\r\nconst lineData = ref([])\r\nconst year = ref(new Date().getFullYear() + '')\r\nconst getLineData = async () => {\r\n  const res = await api.globalJson('/aigptStatistics/trend', {\r\n    beginDate: new Date(year.value + '-01-01').getTime(),\r\n    endDate: new Date(year.value + '-12-31').getTime(),\r\n    timeDimension: 'month'\r\n  })\r\n  const arr = [\r\n    {\r\n      name: '对话量',\r\n      data: res.data.map((v) => ({ ...v, value: v.dialogueCount, name: v.bucket })),\r\n      type: 'line',\r\n      color: 'rgba(31, 198, 255, 1)'\r\n    },\r\n    {\r\n      name: '活跃用户',\r\n      data: res.data.map((v) => ({ ...v, value: v.activeUsers, name: v.bucket })),\r\n      type: 'line',\r\n      color: 'rgba(245, 231, 79, 1)'\r\n    }\r\n  ]\r\n  lineData.value = arr\r\n}\r\nconst hottype = ref('')\r\nconst hottypeList = ref([])\r\nconst getHottypeName = () => {\r\n  return hottypeList.value.find((v) => v.id === hottype.value)?.name || '全部业务线'\r\n}\r\nconst getHottypeList = async () => {\r\n  const res = await api.globalJson('/aigptChatScene/selector')\r\n  hottypeList.value = res.data\r\n  hottypeList.value.unshift({ id: '', name: '全部业务线' })\r\n  if (res.data.length > 0) {\r\n    hottype.value = res.data[0].id\r\n    getHotWordData()\r\n  }\r\n}\r\nconst hotWordData = ref([])\r\nconst getHotWordData = async () => {\r\n  const res = await api.globalJson('/aigptStatistics/hotwordTop', {\r\n    chatBusinessScene: hottype.value\r\n  })\r\n  hotWordData.value = res.data\r\n}\r\nconst showMore = ref(false)\r\nconst detailData = ref([])\r\nconst handleDetail = async () => {\r\n  if (detailData.value.length) {\r\n    showMore.value = true\r\n    return\r\n  }\r\n  const res = await api.globalJson('/aigptStatistics/overviewdetail')\r\n  detailData.value = res.data.sort((a, b) => b.count - a.count)\r\n}\r\n\r\nconst removeHtmlTag = (str) => {\r\n  return decodeURIComponent(str.replace(/<[^>]*>?/g, ''))\r\n}\r\nconst decodeURIComponent = (str) => {\r\n  return str\r\n    .replace(/<[^>]*>?/g, '')\r\n    .replace(/&nbsp;/g, ' ')\r\n    .replace(/&amp;/g, '&')\r\n    .replace(/&lt;/g, '<')\r\n    .replace(/&gt;/g, '>')\r\n    .replace(/&quot;/g, '\"')\r\n}\r\n\r\nconst showDetail = ref(false)\r\nconst chatDetail = ref({})\r\nconst lookDetail = async (row) => {\r\n  const res = await api.globalJson('/aigptChatLogs/info', { detailId: row.id })\r\n  console.log('🚀 ~ lookDetail ~ res:', res)\r\n  chatDetail.value = res.data\r\n  chatDetail.value.chatBusinessName = row.chatBusinessName\r\n  chatDetail.value.headImg = row.headImg\r\n  showDetail.value = true\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.AiUseStatistics {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #f0f2f5;\r\n  padding-top: 20px;\r\n  padding-bottom: 20px;\r\n  .AiUseStatistics-header {\r\n    background: #ffffff;\r\n    border-radius: 0px 0px 0px 0px;\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n    .AiUseStatistics-header-box {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: flex-start;\r\n      margin-bottom: 20px;\r\n      .AiUseStatistics-header-left {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        justify-content: center;\r\n      }\r\n      .AiUseStatistics-header-right {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n      .AiUseStatistics-header-left-title {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n        color: #333333;\r\n        margin-bottom: 10px;\r\n      }\r\n      .AiUseStatistics-header-left-desc {\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n      .AiUseStatistics-header-right {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n        font-size: 14px;\r\n        .zy-el-icon {\r\n          font-size: 16px;\r\n          margin-left: 5px;\r\n        }\r\n      }\r\n    }\r\n    .AiUseStatistics-header-content {\r\n      display: flex;\r\n      gap: 20px;\r\n      .AiUseStatistics-header-content-item {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background: #f5f7fa;\r\n        border-radius: 6px 6px 6px 6px;\r\n        padding: 20px;\r\n        .AiUseStatistics-header-content-item-left {\r\n          .AiUseStatistics-header-content-item-left-title {\r\n            color: #474b4f;\r\n            font-size: 16px;\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 10px;\r\n            img {\r\n              width: 16px;\r\n              height: 16px;\r\n              margin-left: 5px;\r\n            }\r\n          }\r\n          .AiUseStatistics-header-content-item-left-num {\r\n            color: var(--zy-el-color-primary);\r\n            font-size: 26px;\r\n            font-weight: 600;\r\n            span {\r\n              margin-left: 20px;\r\n              color: var(--zy-el-color-primary);\r\n              cursor: pointer;\r\n              font-size: 14px;\r\n              .zy-el-icon {\r\n                font-size: 14px;\r\n                margin-left: 5px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .AiUseStatistics-header-content-item-right {\r\n          width: 60px;\r\n          height: 60px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .AiUseStatistics-chart {\r\n    width: 100%;\r\n    height: 334px;\r\n    padding-top: 20px;\r\n    margin-bottom: 20px;\r\n    background: #ffffff;\r\n    position: relative;\r\n    display: flex;\r\n    .AiUseStatistics-chart-box {\r\n      width: 33%;\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      position: relative;\r\n    }\r\n    .AiUseStatistics-chart-timeSelect {\r\n      position: absolute;\r\n      right: 200px;\r\n      top: 0px;\r\n      z-index: 10;\r\n      .zy-el-date-editor {\r\n        width: 120px;\r\n      }\r\n    }\r\n    .AiUseStatistics-chart-box-right {\r\n      width: 66%;\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      position: relative;\r\n    }\r\n  }\r\n  .AiUseStatistics-chart-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    height: 384px;\r\n\r\n    .AiUseStatistics-chart-content-right-title {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 10px;\r\n      .AiUseStatistics-chart-content-right-title-left {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .AiUseStatistics-chart-content-right-title-right {\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n        font-size: 14px;\r\n        display: flex;\r\n        .zy-el-input {\r\n          width: 160px;\r\n          margin-right: 10px;\r\n        }\r\n        .zy-el-select {\r\n          width: 160px;\r\n        }\r\n        .zy-el-icon {\r\n          font-size: 14px;\r\n          margin-left: 5px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .AiUseStatistics-chart-content-left {\r\n      width: 33%;\r\n      flex-shrink: 0;\r\n      background: #ffffff;\r\n      padding: 0 20px;\r\n      .hotWordBox {\r\n        height: 158px;\r\n        background: #f5f7fa;\r\n        .hotWordContent {\r\n          width: 80%;\r\n          height: 158px;\r\n          margin: auto;\r\n          background-image: url('../img/hotwordbg.png');\r\n          background-size: 100% 100%;\r\n          background-repeat: no-repeat;\r\n          background-position: center;\r\n          border-radius: 0px 0px 0px 0px;\r\n          opacity: 0.5;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 20px;\r\n        }\r\n      }\r\n      .hotWordBox-content {\r\n        height: calc(100% - 158px - 62px);\r\n        padding-top: 20px;\r\n        .hotWordBox-content-title {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          margin-bottom: 10px;\r\n        }\r\n        .hotWordBox-content-list {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 10px;\r\n          .hotWordBox-content-list-item {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            .hotWordBox-content-list-item-title {\r\n              padding-left: 16px;\r\n              font-size: 16px;\r\n              font-weight: 600;\r\n              position: relative;\r\n              &:after {\r\n                content: '';\r\n                display: block;\r\n                width: 6px;\r\n                height: 6px;\r\n                background: var(--zy-el-color-primary);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                left: 0;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                z-index: 10;\r\n              }\r\n            }\r\n            .hotWordBox-content-list-item-num {\r\n              font-size: 14px;\r\n              color: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .AiUseStatistics-chart-content-right {\r\n      flex-shrink: 0;\r\n      width: 66%;\r\n      background: #ffffff;\r\n      .AiUseStatistics-chart-content-right-content {\r\n        height: calc(100% - 46px);\r\n        .globalTable {\r\n          height: calc(100% - 42px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.AiUseStatistics-detail {\r\n  width: 990px;\r\n  padding: 24px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 14px;\r\n  .detail-item {\r\n    width: 32%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background: #f5f7fa;\r\n    border-radius: 4px 4px 4px 4px;\r\n    padding: 12px 16px;\r\n    .detail-item-title {\r\n      display: flex;\r\n      align-items: center;\r\n      img {\r\n        width: 20px;\r\n        height: 20px;\r\n        margin-right: 6px;\r\n      }\r\n    }\r\n    .detail-item-content {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: var(--zy-el-color-primary);\r\n      span {\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n}\r\n.AiUseStatistics-tooltip-content {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.AiUseStatistics-tooltip {\r\n  max-width: 600px;\r\n  background: #f0f2f5 !important;\r\n}\r\n\r\n.chatDetail {\r\n  width: 800px;\r\n  padding: 24px;\r\n  .questionHead {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n    .questionName {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      display: flex;\r\n      align-items: center;\r\n      .zy-el-image {\r\n        width: 32px;\r\n        height: 32px;\r\n        border-radius: 50%;\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n    .questionTime {\r\n      font-size: 14px;\r\n      color: #999999;\r\n      display: flex;\r\n      align-items: center;\r\n      .zy-el-tag {\r\n        margin-right: 10px;\r\n      }\r\n    }\r\n  }\r\n  .questionContent {\r\n    .questionContent-title {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 10px;\r\n    }\r\n    .questionContent-answer {\r\n      font-size: 16px;\r\n      color: #666666;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAuBiBA,UAAwB;OAYxBC,UAAwB;OAkBxBC,UAAwB;OAmHxBC,UAA2B;;EAtKnCC,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAUlCA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA0C;;EAK9CA,KAAK,EAAC;AAA8C;;EAMxDA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA0C;;EAK9CA,KAAK,EAAC;AAA8C;;EAMxDA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA0C;;EAK9CA,KAAK,EAAC;AAA8C;;EAc5DA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAkC;;EAW1CA,KAAK,EAAC;AAAiC;;EAIzCA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAoC;;EACxCA,KAAK,EAAC;AAA2C;;EAE/CA,KAAK,EAAC;AAAiD;;EAMzDA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAgB;;EAIxBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAA0B;;EAGhCA,KAAK,EAAC;AAAyB;;EAM3BA,KAAK,EAAC;AAAoC;;EAG1CA,KAAK,EAAC;AAAkC;;EAKhDA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA2C;;EAE/CA,KAAK,EAAC;AAAiD;;EAOzDA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAAa;;EA+BnBA,KAAK,EAAC;AAAkB;;EAe5BA,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAAmB;;EAMzBA,KAAK,EAAC;AAAqB;;EAQ/BA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAEjBC,IAAI,EAAC;AAAS;;EAGnBD,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAuB;kBAjM5C;kBAAA;;;;;;;;;;;;;;;;;;;uBACEE,YAAA,CAwMeC,uBAAA;IAxMDH,KAAK,EAAC;EAAiB;IADvCI,OAAA,EAAAC,QAAA,CAEI;MAAA,OAuDM,CAvDNC,mBAAA,CAuDM,OAvDNC,UAuDM,GAtDJD,mBAAA,CASM,OATNE,UASM,G,0BARJF,mBAAA,CAGM;QAHDN,KAAK,EAAC;MAA6B,IACtCM,mBAAA,CAA6D;QAAxDN,KAAK,EAAC;MAAmC,GAAC,UAAQ,GACvDM,mBAAA,CAAqE;QAAhEN,KAAK,EAAC;MAAkC,GAAC,mBAAiB,E,sBAEjEM,mBAAA,CAGM;QAHDN,KAAK,EAAC,8BAA8B;QAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEC,MAAA,CAAAC,eAAe;QAAA;oCARzEC,gBAAA,CAQiF,QAEvE,IAAAC,YAAA,CAAmCC,kBAAA;QAV7CZ,OAAA,EAAAC,QAAA,CAUmB;UAAA,OAAgB,CAAhBU,YAAA,CAAgBE,uBAAA,E;;QAVnCC,CAAA;cAaMZ,mBAAA,CA2CM,OA3CNa,UA2CM,GA1CJb,mBAAA,CAWM,OAXNc,UAWM,GAVJd,mBAAA,CAMM,OANNe,UAMM,G,4BALJf,mBAAA,CAGM;QAHDN,KAAK,EAAC;MAAgD,IAhBvEc,gBAAA,CAgBwE,SAE1D,GAAAQ,mBAAA,gDAA+C,C,sBAEjDhB,mBAAA,CAAyG,OAAzGiB,UAAyG,EAAAC,gBAAA,CAA5CZ,MAAA,CAAAa,YAAY,CAACC,iBAAiB,sB,+BAE7FpB,mBAAA,CAEM;QAFDN,KAAK,EAAC;MAA2C,IACpDM,mBAAA,CAAuC;QAAlCqB,GAAwB,EAAxB/B,UAAwB;QAACgC,GAAG,EAAC;iCAGtCtB,mBAAA,CAWM,OAXNuB,UAWM,GAVJvB,mBAAA,CAMM,OANNwB,UAMM,G,4BALJxB,mBAAA,CAGM;QAHDN,KAAK,EAAC;MAAgD,IA5BvEc,gBAAA,CA4BwE,SAE1D,GAAAQ,mBAAA,gDAA+C,C,sBAEjDhB,mBAAA,CAAkG,OAAlGyB,UAAkG,EAAAP,gBAAA,CAArCZ,MAAA,CAAAa,YAAY,CAACO,UAAU,sB,+BAEtF1B,mBAAA,CAEM;QAFDN,KAAK,EAAC;MAA2C,IACpDM,mBAAA,CAAuC;QAAlCqB,GAAwB,EAAxB9B,UAAwB;QAAC+B,GAAG,EAAC;iCAGtCtB,mBAAA,CAiBM,OAjBN2B,WAiBM,GAhBJ3B,mBAAA,CAYM,OAZN4B,WAYM,G,4BAXJ5B,mBAAA,CAGM;QAHDN,KAAK,EAAC;MAAgD,IAxCvEc,gBAAA,CAwCwE,UAE1D,GAAAQ,mBAAA,gDAA+C,C,sBAEjDhB,mBAAA,CAMM,OANN6B,WAMM,GAlDlBrB,gBAAA,CAAAU,gBAAA,CA6CiBZ,MAAA,CAAAa,YAAY,CAACW,gBAAgB,SAAQ,GACxC,iBAAA9B,mBAAA,CAGO;QAHAG,OAAK,EAAEG,MAAA,CAAAyB;MAAY,I,4BA9CxCvB,gBAAA,CA8C0C,MAE1B,IAAAC,YAAA,CAAiCC,kBAAA;QAhDjDZ,OAAA,EAAAC,QAAA,CAgDyB;UAAA,OAAc,CAAdU,YAAA,CAAcuB,qBAAA,E;;QAhDvCpB,CAAA;4CAoDUZ,mBAAA,CAEM;QAFDN,KAAK,EAAC;MAA2C,IACpDM,mBAAA,CAAuC;QAAlCqB,GAAwB,EAAxB7B,UAAwB;QAAC8B,GAAG,EAAC;qCAK1CtB,mBAAA,CAgBM,OAhBNiC,WAgBM,GAfJjC,mBAAA,CAWM,OAXNkC,WAWM,GAVJlC,mBAAA,CAQM,OARNmC,WAQM,GAPJ1B,YAAA,CAM0B2B,yBAAA;QAnEpCC,UAAA,EA8DqB/B,MAAA,CAAAgC,IAAI;QA9DzB,uBAAAlC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA8DqBC,MAAA,CAAAgC,IAAI,GAAAjC,MAAA;QAAA;QACZkC,SAAS,EAAE,KAAK;QACjB5C,IAAI,EAAC,MAAM;QACX,cAAY,EAAC,MAAM;QACnB6C,WAAW,EAAC,MAAM;QACjBC,QAAM,EAAEnC,MAAA,CAAAoC;iDAEbjC,YAAA,CAA8BH,MAAA;QAAlBqC,IAAI,EAAErC,MAAA,CAAAsC;MAAQ,kC,GAE5B5C,mBAAA,CAEM,OAFN6C,WAEM,GADJpC,YAAA,CAAmCH,MAAA;QAApBqC,IAAI,EAAErC,MAAA,CAAAwC;MAAU,kC,KAGnC9C,mBAAA,CAwFM,OAxFN+C,WAwFM,GAvFJ/C,mBAAA,CA+BM,OA/BNgD,WA+BM,GA9BJhD,mBAAA,CAOM,OAPNiD,WAOM,G,4BANJjD,mBAAA,CAAyE;QAApEN,KAAK,EAAC;MAAgD,GAAC,SAAO,sBACnEM,mBAAA,CAIM,OAJNkD,WAIM,GAHJzC,YAAA,CAEY0C,oBAAA;QAlFxBd,UAAA,EAgFgC/B,MAAA,CAAA8C,OAAO;QAhFvC,uBAAAhD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAgFgCC,MAAA,CAAA8C,OAAO,GAAA/C,MAAA;QAAA;QAAEmC,WAAW,EAAC,QAAQ;QAAEC,QAAM,EAAEnC,MAAA,CAAA+C,cAAc;QAAEC,UAAU,EAAV;;QAhFvFxD,OAAA,EAAAC,QAAA,CAiFyB;UAAA,OAA2B,E,kBAAtCwD,mBAAA,CAA4FC,SAAA,QAjF1GC,WAAA,CAiFwCnD,MAAA,CAAAoD,WAAW,EAjFnD,UAiFgCC,IAAI;iCAAtB/D,YAAA,CAA4FgE,oBAAA;cAApDC,GAAG,EAAEF,IAAI,CAACG,EAAE;cAAGC,KAAK,EAAEJ,IAAI,CAACK,IAAI;cAAGC,KAAK,EAAEN,IAAI,CAACG;;;;QAjFpGlD,CAAA;6CAqFQZ,mBAAA,CAIM,OAJNkE,WAIM,GAHJlE,mBAAA,CAEM,OAFNmE,WAEM,GADJ1D,YAAA,CAAsCH,MAAA;QAArBqC,IAAI,EAAErC,MAAA,CAAA8D;MAAW,kC,KAGtCpE,mBAAA,CAgBM,OAhBNqE,WAgBM,GAfJrE,mBAAA,CAEM,OAFNsE,WAEM,EAAApD,gBAAA,CADDZ,MAAA,CAAAiE,cAAc,oBAEnBvE,mBAAA,CAWM,OAXNwE,WAWM,I,kBAVJjB,mBAAA,CASMC,SAAA,QAxGlBC,WAAA,CAkGsCnD,MAAA,CAAA8D,WAAW,EAlGjD,UAkGsBT,IAAI,EAAEc,KAAK;8CAHrBlB,mBAAA,CASM;UARJ7D,KAAK,EAAC,8BAA8B;UAGnCmE,GAAG,EAAEF,IAAI,CAACG;YACX9D,mBAAA,CAEM,OAFN0E,WAEM,EAAAxD,gBAAA,CADDyC,IAAI,CAACgB,OAAO,kBAEjB3E,mBAAA,CAA4E,OAA5E4E,WAA4E,EAAA1D,gBAAA,CAA3ByC,IAAI,CAACkB,WAAW,IAAG,IAAE,gB,cAN9DJ,KAAK,M;4CAWrBzE,mBAAA,CAsDM,OAtDN8E,WAsDM,GArDJ9E,mBAAA,CAQM,OARN+E,WAQM,G,4BAPJ/E,mBAAA,CAAwE;QAAnEN,KAAK,EAAC;MAAgD,GAAC,QAAM,sBAClEM,mBAAA,CAKM,OALNgF,WAKM,GAJJvE,YAAA,CAAwFwE,mBAAA;QAhHpG5C,UAAA,EAgH+B/B,MAAA,CAAA4E,OAAO;QAhHtC,uBAAA9E,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAgH+BC,MAAA,CAAA4E,OAAO,GAAA7E,MAAA;QAAA;QAAEmC,WAAW,EAAC,QAAQ;QAAE2C,OAAK,EAhHnEC,SAAA,CAgH2E9E,MAAA,CAAA+E,WAAW;QAAE9C,SAAS,EAAT;0DAC5E9B,YAAA,CAA6D6E,oBAAA;QAAlD3F,IAAI,EAAC,SAAS;QAAEQ,OAAK,EAAEG,MAAA,CAAA+E;;QAjH9CvF,OAAA,EAAAC,QAAA,CAiH2D;UAAA,OAAEK,MAAA,SAAAA,MAAA,QAjH7DI,gBAAA,CAiH2D,IAAE,E;;QAjH7DI,CAAA;sCAkHYI,mBAAA,2DACqC,C,KAGzChB,mBAAA,CA2CM,OA3CNuF,WA2CM,GA1CJvF,mBAAA,CA8BM,OA9BNwF,WA8BM,GA7BJ/E,YAAA,CA4BWgF,mBAAA;QA5BDC,GAAG,EAAC,UAAU;QAAC,SAAO,EAAC,IAAI;QAAE/C,IAAI,EAAErC,MAAA,CAAAqF;;QAxHzD7F,OAAA,EAAAC,QAAA,CAyHc;UAAA,OAAgF,CAAhFU,YAAA,CAAgFmF,0BAAA;YAA/D7B,KAAK,EAAC,IAAI;YAAC8B,IAAI,EAAC,gBAAgB;YAACC,KAAK,EAAC;cACxDrF,YAAA,CAekBmF,0BAAA;YAfD7B,KAAK,EAAC,MAAM;YAAC8B,IAAI,EAAC,cAAc;YAAC,WAAS,EAAC;;YAC/C/F,OAAO,EAAAC,QAAA,CAChB,UAAAgG,IAAA;cAAA,IADoBC,GAAG,GAAAD,IAAA,CAAHC,GAAG;cAAA,QACvBhF,mBAAA,+cAOiB,EACjBA,mBAAA,oGAAqG,EACrGP,YAAA,CAEY6E,oBAAA;gBAFD3F,IAAI,EAAC,SAAS;gBAACsG,IAAI,EAAJ,EAAI;gBAACC,IAAI,EAAJ,EAAI;gBAAE/F,OAAK,WAALA,OAAKA,CAAAE,MAAA;kBAAA,OAAEC,MAAA,CAAA6F,UAAU,CAACH,GAAG;gBAAA;gBAAGtG,KAAK,EAAC;;gBArIrFI,OAAA,EAAAC,QAAA,CAsIoB;kBAAA,OAAqC,CAtIzDS,gBAAA,CAAAU,gBAAA,CAsIuBZ,MAAA,CAAA8F,aAAa,CAACJ,GAAG,CAACK,YAAY,kB;;gBAtIrDzF,CAAA;;;YAAAA,CAAA;cA0IcH,YAAA,CAAmFmF,0BAAA;YAAlE7B,KAAK,EAAC,KAAK;YAAC8B,IAAI,EAAC,kBAAkB;YAACC,KAAK,EAAC;cAC3DrF,YAAA,CAIkBmF,0BAAA;YAJD7B,KAAK,EAAC,IAAI;YAAC8B,IAAI,EAAC,YAAY;YAACC,KAAK,EAAC;;YACvChG,OAAO,EAAAC,QAAA,CAChB,UAAAuG,KAAA;cAAA,IADoBN,GAAG,GAAAM,KAAA,CAAHN,GAAG;cAAA,QA5IzCxF,gBAAA,CAAAU,gBAAA,CA6IqB8E,GAAG,CAACO,UAAU,GAAGjG,MAAA,CAAAkG,MAAM,CAACR,GAAG,CAACO,UAAU,2C;;YA7I3D3F,CAAA;cAiJsB,KAAK,I,cADbhB,YAAA,CAGyD6G,kCAAA;YAnJvE5C,GAAA;YAkJiBlB,IAAI,EAAErC,MAAA,CAAAoG,eAAe;YACrBC,aAAW,EAAEC,IAAA,CAAAC;wDAnJ9B7F,mBAAA,e;;QAAAJ,CAAA;qCAsJUZ,mBAAA,CAUM,OAVN8G,WAUM,GATJrG,YAAA,CAQesG,wBAAA;QAPLC,WAAW,EAAE1G,MAAA,CAAA2G,MAAM;QAxJzC,wBAAA7G,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAwJmCC,MAAA,CAAA2G,MAAM,GAAA5G,MAAA;QAAA;QACnB,WAAS,EAAEC,MAAA,CAAA4G,QAAQ;QAzJzC,qBAAA9G,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAyJiCC,MAAA,CAAA4G,QAAQ,GAAA7G,MAAA;QAAA;QAC1B,YAAU,EAAEC,MAAA,CAAA6G,SAAS;QACtBC,MAAM,EAAC,yCAAyC;QAC/CC,YAAW,EAAE/G,MAAA,CAAA+E,WAAW;QACxBiC,eAAc,EAAEhH,MAAA,CAAA+E,WAAW;QAC3BkC,KAAK,EAAEjH,MAAA,CAAAkH,MAAM;QACdC,UAAU,EAAV;+HAKVhH,YAAA,CAemBiH,2BAAA;QAnLvBrF,UAAA,EAoK+B/B,MAAA,CAAAqH,QAAQ;QApKvC,uBAAAvH,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAoK+BC,MAAA,CAAAqH,QAAQ,GAAAtH,MAAA;QAAA;QAAE2D,IAAI,EAAC;;QApK9ClE,OAAA,EAAAC,QAAA,CAqKM;UAAA,OAaM,CAbNC,mBAAA,CAaM,OAbN4H,WAaM,I,kBAZJrE,mBAAA,CAWMC,SAAA,QAjLdC,WAAA,CAsKgDnD,MAAA,CAAAwC,UAAU,EAtK1D,UAsKwCa,IAAI;iCAApCJ,mBAAA,CAWM;cAXD7D,KAAK,EAAC,aAAa;cAA6BmE,GAAG,EAAEF,IAAI,CAACG;gBAC7D9D,mBAAA,CAKM,OALN6H,WAKM,G,4BAJJ7H,mBAAA,CAA0C;cAArCqB,GAA2B,EAA3B5B,UAA2B;cAAC6B,GAAG,EAAC;yCACrCb,YAAA,CAEaqH,qBAAA;cAFAC,OAAO,EAAEpE,IAAI,CAACK,IAAI;cAAGgE,QAAQ,EAAErE,IAAI,CAACK,IAAI,CAACiE,MAAM;cAAOC,SAAS,EAAC;;cAzKzFpI,OAAA,EAAAC,QAAA,CA0Kc;gBAAA,OAAqF,CAArFC,mBAAA,CAAqF,cAAAkB,gBAAA,CAA5EyC,IAAI,CAACK,IAAI,CAACiE,MAAM,QAAQtE,IAAI,CAACK,IAAI,CAACmE,KAAK,kBAAkBxE,IAAI,CAACK,IAAI,iB;;cA1KzFpD,CAAA;4EA6KUZ,mBAAA,CAGM,OAHNoI,WAGM,GAhLhB5H,gBAAA,CAAAU,gBAAA,CA8KeyC,IAAI,CAAC0E,KAAK,IAAG,GAChB,iB,4BAAArI,mBAAA,CAAc,cAAR,GAAC,qB;;;QA/KnBY,CAAA;yCAoLIH,YAAA,CAoBmBiH,2BAAA;QAxMvBrF,UAAA,EAoL+B/B,MAAA,CAAAgI,UAAU;QApLzC,uBAAAlI,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAoL+BC,MAAA,CAAAgI,UAAU,GAAAjI,MAAA;QAAA;QAAE2D,IAAI,EAAC;;QApLhDlE,OAAA,EAAAC,QAAA,CAqLM;UAAA,OAkBM,CAlBNC,mBAAA,CAkBM,OAlBNuI,WAkBM,GAjBJvI,mBAAA,CASM,OATNwI,WASM,GARJxI,mBAAA,CAGM,OAHNyI,WAGM,GAFJhI,YAAA,CAA0DiI,mBAAA;YAA/CrH,GAAG,EAAEf,MAAA,CAAAqI,MAAM,CAACrI,MAAA,CAAAsI,UAAU,CAACC,OAAO;YAAGC,GAAG,EAAC;4CAChD9I,mBAAA,CAA4C,cAAAkB,gBAAA,CAAnCZ,MAAA,CAAAsI,UAAU,CAACG,cAAc,iB,GAEpC/I,mBAAA,CAGM,OAHNgJ,WAGM,GAFJvI,YAAA,CAAiEwI,iBAAA;YAAzDtJ,IAAI,EAAC;UAAS;YA5LlCG,OAAA,EAAAC,QAAA,CA4LmC;cAAA,OAAiC,CA5LpES,gBAAA,CAAAU,gBAAA,CA4LsCZ,MAAA,CAAAsI,UAAU,CAACM,gBAAgB,iB;;YA5LjEtI,CAAA;cA6LYZ,mBAAA,CAAwF,QAAxFmJ,WAAwF,EAAnE,OAAK,GAAAjI,gBAAA,CAAGZ,MAAA,CAAAkG,MAAM,CAAClG,MAAA,CAAAsI,UAAU,CAACrC,UAAU,sC,KAG7DvG,mBAAA,CAMM,OANNoJ,WAMM,GALJpJ,mBAAA,CAGM,OAHNqJ,WAGM,GAFJrI,mBAAA,gDAAmD,EACnDhB,mBAAA,CAA4C;YAAvCsJ,SAAgC,EAAxBhJ,MAAA,CAAAsI,UAAU,CAACvC;kCAnMpCkD,WAAA,E,GAqMUvJ,mBAAA,CAAqE;YAAhEN,KAAK,EAAC,wBAAwB;YAAC4J,SAA0B,EAAlBhJ,MAAA,CAAAsI,UAAU,CAACY;kCArMjEC,WAAA,E;;QAAA7I,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}