<template>
  <el-scrollbar always class="SuggestClueAdd" @scroll="scrollEvent($event)">
    <div class="globalForm">
      <div class="clueTitle">{{ pageMsg.title }}</div>
      <div class="clueContent">{{ pageMsg.content }}
      </div>
      <div>
        <div class="litleTitle">我的线索</div>
        <el-button :icon="Share" @click="openWin" class="share-btn" type="text">分享</el-button>
      </div>
      <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top">
        <el-form-item label="线索标题" prop="theme" class="globalFormTitle">
          <el-input v-model="form.theme" placeholder="请输入标题" clearable />
        </el-form-item>

        <el-form-item label="线索类别" prop="proposalClueType">
          <el-select v-model="form.proposalClueType" placeholder="请选择线索类别" clearable>
            <el-option v-for="item in proposalClueType" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="线索内容" prop="content" class="globalFormTitle">
          <el-input v-model="form.content" type="textarea" placeholder="请输入线索内容" clearable rows="6" />
        </el-form-item>
      </el-form>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
      </div>
      <div class="publishBox">
        <div class="publishTitle"><img style="margin-right: 5px;" src="../../../assets/img/proposal_register.png">提案线索选登
        </div>
        <div class="publishItemBox" v-for="(item, index) in listData" :key="index" @click="handleDetail(item)">
          <div class="publishItemTile">{{ item.title }}</div>
          <div class="publishItemContent">
            <p class="text_hid">{{
              (item.content).length > 56 ? (item.content).substr(0, 162) + "..." : item.content
            }}</p>
            <span v-show="item.content.length > 162" class="text_details">详情 ></span>
          </div>
          <div class="publishTagBox">
            <p>提供者:{{ item.furnishName }}</p>
            <p>{{ format(item.createDate) }}</p>
            <div class="tags">{{ item.proposalClueType.label }}</div>
          </div>
        </div>
      </div>
      <div class="tipsText">
        {{ tipsLabel }}
      </div>
      <xyl-popup-window v-model="detailsShow" name="提案线索详情">
        <SuggestClueDetails :id="id"></SuggestClueDetails>
      </xyl-popup-window>
      <xyl-popup-window v-model="qrShow" name="分享二维码">
        <div class="qrcode-box">
          <div ref="printQrcodeRef" class="printQrcodeRef">
            <qrcode-vue :value="shareUrl" :size="300" level="H" ref="qrcodeRef" />
          </div>
          <el-button type="primary" @click="downloadQRCode">保存二维码</el-button>
        </div>
      </xyl-popup-window>

    </div>
  </el-scrollbar>
</template>
<script>
export default { name: 'SuggestClueAdd' }
</script>
<script setup>
import { Share } from '@element-plus/icons-vue'
import QrcodeVue from 'qrcode.vue'
import api from '@/api'
import { user } from 'common/js/system_var.js'
import { ElMessage } from 'element-plus'
import { format } from 'common/js/time.js'
import { reactive, ref, onMounted } from 'vue'
import SuggestClueDetails from './components/SuggestClueDetails'
const formRef = ref()
const pageNo = ref(1)
const pageSize = ref(8)
const listData = ref([])
const proposalClueType = ref([])
const tipsLabel = ref('')
const printQrcodeRef = ref()
const shareUrl = ref('')
const detailsShow = ref(false)
const id = ref('')
const loadStatus = ref(true)
const qrShow = ref(false)
const pageMsg = reactive({
  title: '',
  content: ''
})
const form = reactive({
  theme: '', // 标题
  proposalClueType: '', // 类型
  content: '' // 内容
})
const rules = reactive({
  theme: [{ required: true, message: '请输入线索标题', trigger: ['blur', 'change'] }],
  proposalClueType: [{ required: true, message: '请选择线索类别', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '请输入线索内容', trigger: ['blur', 'change'] }]
})

onMounted(() => {
  dictionaryData()
  proposalClueTheme()
  proposalClueList(0)
})

const scrollEvent = (e) => {
  if (!loadStatus.value) { return }
  if (e.scrollTop + (Math.max(document.documentElement.clientHeight, window.innerHeight || 0) - 130) >= (document.getElementsByClassName('globalForm')[0].clientHeight)) {
    pageNo.value++
    proposalClueList(true)
  }
}
const dictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: ['proposal_clue_type'] })
  proposalClueType.value = data.proposal_clue_type
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { addForm() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}

const addForm = async () => {
  const { code } = await api.globalJson('/proposalClue/add', {
    form: {
      title: form.theme,
      content: form.content,
      proposalClueType: form.proposalClueType,
      furnish: user.value.id,
      furnish_name: user.value.userName,
      furnishMobile: user.value.mobile,
      terminalName: 'PC'
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '提交成功' })
    formRef.value.resetFields()
  }
}

const proposalClueTheme = async () => {
  const { code, data } = await api.proposalClueTheme({ pageNo: 1, pageSize: 1 })
  if (code == 200 && data.length) {
    pageMsg.title = data[0].title
    pageMsg.content = data[0].content
  }
}

const proposalClueList = async (val) => {
  if (!loadStatus.value) { return }
  const { code, data, total } = await api.proposalClueList({ pageNo: pageNo.value, pageSize: pageSize.value, query: { ifPublish: '1' } })
  if (code == 200 && data.length) {
    if (val) {
      data.forEach(element => { listData.value.push(element) })
    } else {
      listData.value = data
    }
    if (listData.value.length < total) {
      tipsLabel.value = '滑动加载更多...'
    } else {
      loadStatus.value = false
      tipsLabel.value = '已加载完'
    }
  }
}

const handleDetail = (item) => {
  id.value = item.id
  detailsShow.value = true
}

const openWin = async () => {
  const { data } = await api.globalReadOpenConfig({ codes: ['appShareAddress'] })
  shareUrl.value = `${data.appShareAddress}pages/index/index.html?%7B%22n%22:%22mo_proposal_clue%22,%22u%22:%22../mo_proposal_clue/mo_proposal_clue.stml%22,%22p%22:%7B%22title%22:%22%E6%8F%90%E6%A1%88%E7%BA%BF%E7%B4%A2%E5%BE%81%E9%9B%86%22,%22code%22:%2238%22,%22headTheme%22:%22#FFF%22,%22appTheme%22:%22#3657C0%22,%22areaId%22:%22${user.value.areaId}%22,%22v%22:%225%22%7D%7D`
  qrShow.value = true
}

const downloadQRCode = () => {
  var canvasData = printQrcodeRef.value.querySelector('canvas')
  var a = document.createElement('a')
  a.href = canvasData.toDataURL()
  a.download = '提案线索征集'
  a.click()
}
</script>
<style lang="scss">
.SuggestClueAdd {
  width: 100%;
  height: 100%;

  .share-btn {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .qrcode-box {
    padding: 20px;
    width: 600px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .printQrcodeRef {
    padding: 20px;
    background-color: #fff;
  }



  .text_hid {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 3;
  }

  .tipsText {
    text-align: center;
    font-weight: bold;
  }

  .globalForm {
    width: 990px;
    margin: 20px auto;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
    position: relative;

    .clueTitle {
      font-size: 22px;
      font-weight: bold;
      text-align: center;
    }

    .clueContent {
      font-size: 18px;
      margin-top: 40px;
    }

    .litleTitle {
      font-size: 20px;
      color: var(--zy-el-color-primary);
      text-align: center;
      font-weight: bold;
      margin-top: 40px;
    }

    .publishBox {
      display: flex;
      flex-direction: column;
      margin-top: 50px;

      .publishTitle {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 30px;
        display: flex;
        align-items: center;
      }

      .publishItemBox {
        display: flex;
        flex-direction: column;
        margin-bottom: 50px;

        .publishItemTile {

          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
        }

        .publishItemContent {
          font-size: 16px;
          margin-bottom: 10px;
          position: relative;

          .text_details {
            position: absolute;
            bottom: 0;
            right: 0;
            font-size: 12px;
            line-height: 15px;
            color: var(--zy-el-color-primary);
            padding: 4px 10px;
            background: linear-gradient(to right, #ffffffdb, white);
          }
        }

        .publishTagBox {
          display: flex;
          flex-direction: row;

          p {
            margin-right: 20px;
            color: gray;
            font-size: 14px;
          }

          .tags {
            font-size: 14px;
            color: var(--zy-el-color-primary);
            background-color: var(--zy-el-color-primary-light-9);
            padding: 0 10px;
          }
        }
      }
    }

    .otherFormItem {
      width: 100%;
      display: flex;
      margin-right: 20px;

      .otherFormItemLabel {
        width: 120px;
        text-align: right;
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-height);
      }

      .otherFormItemBody {
        width: calc(100% - 132px);
        margin-left: 12px;

        .zy-el-switch {
          height: var(--zy-height);
          margin-bottom: 22px;
        }

        .otherFormItemSwitch {
          display: flex;
          align-items: center;
          margin-bottom: 22px;

          .zy-el-switch {
            height: var(--zy-height);
            margin-bottom: 0;
          }

          &>span {
            font-size: var(--zy-text-font-size);
            line-height: var(--zy-height);
            margin-left: 22px;
            color: var(--zy-el-color-primary);
          }
        }

        .otherFormItemSwitchIs {
          margin-bottom: 12px;
        }

        .otherFormItemBodyForm {
          display: flex;
          flex-wrap: wrap;

          .globalFormTime {
            width: 420px;
          }
        }
      }
    }
  }
}
</style>
