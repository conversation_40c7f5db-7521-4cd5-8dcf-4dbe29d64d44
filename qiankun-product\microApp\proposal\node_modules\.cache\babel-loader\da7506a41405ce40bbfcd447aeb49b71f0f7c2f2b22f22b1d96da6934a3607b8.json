{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { suggestExportWord } from '@/assets/js/suggestExportWord';\nimport SuggestBatchAssign from './component/SuggestBatchAssign.vue';\nimport SuggestBatchAssignUnit from './component/SuggestBatchAssignUnit.vue';\nimport SuggestBatchSendBack from './component/SuggestBatchSendBack.vue';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'SuggestAssign'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var buttonList = [\n    // { id: 'next', name: '批量交办', type: 'primary', has: 'next' },\n    {\n      id: 'sendBack',\n      name: '退回政协交办',\n      type: 'primary',\n      has: 'send_back'\n    }, {\n      id: 'nextUnit',\n      name: '批量交办',\n      type: 'primary',\n      has: 'next_unit'\n    }, {\n      id: 'exportWord',\n      name: '导出Word',\n      type: 'primary',\n      has: ''\n    }, {\n      id: 'export',\n      name: '导出Excel',\n      type: 'primary',\n      has: ''\n    }];\n    var id = ref([]);\n    var show = ref(false);\n    var unitShow = ref(false);\n    var sendBackShow = ref(false);\n    var _GlobalTable = GlobalTable({\n        tableId: route.query.tableId,\n        tableApi: 'suggestionList'\n      }),\n      keyword = _GlobalTable.keyword,\n      queryRef = _GlobalTable.queryRef,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableHead = _GlobalTable.tableHead,\n      tableData = _GlobalTable.tableData,\n      exportId = _GlobalTable.exportId,\n      exportParams = _GlobalTable.exportParams,\n      exportShow = _GlobalTable.exportShow,\n      handleQuery = _GlobalTable.handleQuery,\n      tableDataArray = _GlobalTable.tableDataArray,\n      handleSortChange = _GlobalTable.handleSortChange,\n      handleHeaderClass = _GlobalTable.handleHeaderClass,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      tableRefReset = _GlobalTable.tableRefReset,\n      handleGetParams = _GlobalTable.handleGetParams,\n      handleEditorCustom = _GlobalTable.handleEditorCustom,\n      handleExportExcel = _GlobalTable.handleExportExcel,\n      tableQuery = _GlobalTable.tableQuery;\n    onActivated(function () {\n      var suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'));\n      if (suggestIds) {\n        tableQuery.value.ids = suggestIds;\n        handleQuery();\n        setTimeout(function () {\n          sessionStorage.removeItem('suggestIds');\n          tableQuery.value.ids = [];\n        }, 1000);\n      } else {\n        handleQuery();\n      }\n    });\n    var handleExcelData = function handleExcelData(_item) {\n      _item.forEach(function (v) {\n        if (!v.mainHandleOffices) {\n          v.mainHandleOffices = v.publishHandleOffices;\n        }\n      });\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'next':\n          if (tableDataArray.value.length) {\n            id.value = tableDataArray.value.map(function (v) {\n              return v.id;\n            });\n            show.value = true;\n          } else {\n            ElMessage({\n              type: 'warning',\n              message: '请至少选择一条数据'\n            });\n          }\n          break;\n        case 'sendBack':\n          if (tableDataArray.value.length) {\n            id.value = tableDataArray.value.map(function (v) {\n              return v.id;\n            });\n            sendBackShow.value = true;\n          } else {\n            ElMessage({\n              type: 'warning',\n              message: '请至少选择一条数据'\n            });\n          }\n          break;\n        case 'nextUnit':\n          if (tableDataArray.value.length) {\n            id.value = tableDataArray.value.map(function (v) {\n              return v.id;\n            });\n            unitShow.value = true;\n          } else {\n            ElMessage({\n              type: 'warning',\n              message: '请至少选择一条数据'\n            });\n          }\n          break;\n        case 'exportWord':\n          suggestExportWord(handleGetParams());\n          break;\n        case 'export':\n          handleExportExcel();\n          break;\n        default:\n          break;\n      }\n    };\n    var handleTableClick = function handleTableClick(key, row) {\n      switch (key) {\n        case 'details':\n          handleDetails(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleDetails = function handleDetails(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '提案详情',\n          path: '/proposal/SuggestDetail',\n          query: {\n            id: item.id,\n            moduleName: route.query.moduleName,\n            type: 'assign'\n          }\n        }\n      });\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      exportShow.value = false;\n      show.value = false;\n      unitShow.value = false;\n      sendBackShow.value = false;\n    };\n    var __returned__ = {\n      route,\n      buttonList,\n      id,\n      show,\n      unitShow,\n      sendBackShow,\n      keyword,\n      queryRef,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableHead,\n      tableData,\n      exportId,\n      exportParams,\n      exportShow,\n      handleQuery,\n      tableDataArray,\n      handleSortChange,\n      handleHeaderClass,\n      handleTableSelect,\n      tableRefReset,\n      handleGetParams,\n      handleEditorCustom,\n      handleExportExcel,\n      tableQuery,\n      handleExcelData,\n      handleReset,\n      handleButton,\n      handleTableClick,\n      handleDetails,\n      callback,\n      ref,\n      onActivated,\n      get useRoute() {\n        return useRoute;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get suggestExportWord() {\n        return suggestExportWord;\n      },\n      SuggestBatchAssign,\n      SuggestBatchAssignUnit,\n      SuggestBatchSendBack,\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "useRoute", "GlobalTable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestExportWord", "SuggestBatchAssign", "SuggestBatchAssignUnit", "SuggestBatchSendBack", "ElMessage", "__default__", "name", "route", "buttonList", "id", "type", "has", "show", "unitShow", "sendBackShow", "_GlobalTable", "tableId", "query", "tableApi", "keyword", "queryRef", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableHead", "tableData", "exportId", "exportParams", "exportShow", "handleQuery", "tableDataArray", "handleSortChange", "handleHeaderClass", "handleTableSelect", "tableRefReset", "handleGetParams", "handleEditorCustom", "handleExportExcel", "tableQuery", "suggestIds", "JSON", "parse", "sessionStorage", "getItem", "value", "ids", "setTimeout", "removeItem", "handleExcelData", "_item", "for<PERSON>ach", "v", "mainHandleOffices", "publishHandleOffices", "handleReset", "handleButton", "isType", "length", "map", "message", "handleTableClick", "key", "row", "handleDetails", "item", "setGlobalState", "openRoute", "path", "moduleName", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestAssign/SuggestAssign.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestAssign\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\" :data=\"tableHead\" ref=\"queryRef\">\r\n      <template #search>\r\n        <el-popover placement=\"bottom\" title=\"您可以查找：\" trigger=\"hover\" :width=\"250\">\r\n          <div class=\"tips-UL\">\r\n            <div>提案名称</div>\r\n            <div>提案编号</div>\r\n            <div>提案人<strong>(名称前加 n 或 N)</strong></div>\r\n            <div>全部办理单位<strong>(名称前加 d 或 D)</strong></div>\r\n            <div>主办单位<strong>(名称前加 m 或 M)</strong></div>\r\n            <div>协办单位<strong>(名称前加 j 或 J)</strong></div>\r\n          </div>\r\n          <template #reference>\r\n            <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n          </template>\r\n        </el-popover>\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\" @sort-change=\"handleSortChange\" :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\"\r\n          :noTooltip=\"['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\">\r\n          <template #mainHandleOffices=\"scope\">\r\n            <template v-if=\"scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0\">\r\n              {{scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}\r\n            </template>\r\n            <template v-else>\r\n              {{scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}\r\n            </template>\r\n          </template>\r\n          <template #assistHandleOffices=\"scope\">\r\n            <template v-if=\"scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0\">\r\n              {{scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}\r\n            </template>\r\n            <template v-else>\r\n              {{scope.row.assistHandleVoList?.map(v => v.flowHandleOfficeName).join('、')}}\r\n            </template>\r\n          </template>\r\n          <!-- <template #publishHandleOffices=\"scope\">\r\n            {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n          </template> -->\r\n        </xyl-global-table>\r\n        <xyl-global-table-button :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\" name=\"导出Excel\">\r\n      <xyl-export-excel :name=\"route.query.moduleName\" :exportId=\"exportId\" :params=\"exportParams\"\r\n        module=\"proposalExportExcel\" :tableId=\"route.query.tableId\" @excelCallback=\"callback\"\r\n        :handleExcelData=\"handleExcelData\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"show\" name=\"批量交办\">\r\n      <SuggestBatchAssign :id=\"id\" @callback=\"callback\"></SuggestBatchAssign>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"unitShow\" name=\"交办承办单位\" :beforeClose=\"callback\">\r\n      <SuggestBatchAssignUnit :id=\"id\" @callback=\"callback\"></SuggestBatchAssignUnit>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"sendBackShow\" name=\"退回政协交办\">\r\n      <SuggestBatchSendBack :id=\"id\" @callback=\"callback\"></SuggestBatchSendBack>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestAssign' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nimport SuggestBatchAssign from './component/SuggestBatchAssign.vue'\r\nimport SuggestBatchAssignUnit from './component/SuggestBatchAssignUnit.vue'\r\nimport SuggestBatchSendBack from './component/SuggestBatchSendBack.vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst route = useRoute()\r\nconst buttonList = [\r\n  // { id: 'next', name: '批量交办', type: 'primary', has: 'next' },\r\n  { id: 'sendBack', name: '退回政协交办', type: 'primary', has: 'send_back' },\r\n  { id: 'nextUnit', name: '批量交办', type: 'primary', has: 'next_unit' },\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n]\r\nconst id = ref([])\r\nconst show = ref(false)\r\nconst unitShow = ref(false)\r\nconst sendBackShow = ref(false)\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  tableDataArray,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel,\r\n  tableQuery\r\n} = GlobalTable({ tableId: route.query.tableId, tableApi: 'suggestionList' })\r\n\r\nonActivated(() => {\r\n  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))\r\n  if (suggestIds) {\r\n    tableQuery.value.ids = suggestIds\r\n    handleQuery()\r\n    setTimeout(() => {\r\n      sessionStorage.removeItem('suggestIds')\r\n      tableQuery.value.ids = []\r\n    }, 1000)\r\n  } else {\r\n    handleQuery()\r\n  }\r\n})\r\nconst handleExcelData = (_item) => {\r\n  _item.forEach(v => {\r\n    if (!v.mainHandleOffices) {\r\n      v.mainHandleOffices = v.publishHandleOffices\r\n    }\r\n  })\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'next':\r\n      if (tableDataArray.value.length) {\r\n        id.value = tableDataArray.value.map((v) => v.id)\r\n        show.value = true\r\n      } else {\r\n        ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n      }\r\n      break\r\n    case 'sendBack':\r\n      if (tableDataArray.value.length) {\r\n        id.value = tableDataArray.value.map((v) => v.id)\r\n        sendBackShow.value = true\r\n      } else {\r\n        ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n      }\r\n      break\r\n    case 'nextUnit':\r\n      if (tableDataArray.value.length) {\r\n        id.value = tableDataArray.value.map((v) => v.id)\r\n        unitShow.value = true\r\n      } else {\r\n        ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n      }\r\n      break\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams())\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: {\r\n      name: '提案详情',\r\n      path: '/proposal/SuggestDetail',\r\n      query: { id: item.id, moduleName: route.query.moduleName, type: 'assign' }\r\n    }\r\n  })\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  exportShow.value = false\r\n  show.value = false\r\n  unitShow.value = false\r\n  sendBackShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestAssign {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA0EA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,sBAAsB,MAAM,wCAAwC;AAC3E,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,SAASC,SAAS,QAAQ,cAAc;AAXxC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAgB,CAAC;;;;;IAYxC,IAAMC,KAAK,GAAGV,QAAQ,CAAC,CAAC;IACxB,IAAMW,UAAU,GAAG;IACjB;IACA;MAAEC,EAAE,EAAE,UAAU;MAAEH,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAY,CAAC,EACrE;MAAEF,EAAE,EAAE,UAAU;MAAEH,IAAI,EAAE,MAAM;MAAEI,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAY,CAAC,EACnE;MAAEF,EAAE,EAAE,YAAY;MAAEH,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,EAC9D;MAAEF,EAAE,EAAE,QAAQ;MAAEH,IAAI,EAAE,SAAS;MAAEI,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,CAC5D;IACD,IAAMF,EAAE,GAAGd,GAAG,CAAC,EAAE,CAAC;IAClB,IAAMiB,IAAI,GAAGjB,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMkB,QAAQ,GAAGlB,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMmB,YAAY,GAAGnB,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAAoB,YAAA,GAuBIjB,WAAW,CAAC;QAAEkB,OAAO,EAAET,KAAK,CAACU,KAAK,CAACD,OAAO;QAAEE,QAAQ,EAAE;MAAiB,CAAC,CAAC;MAtB3EC,OAAO,GAAAJ,YAAA,CAAPI,OAAO;MACPC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MACRC,QAAQ,GAAAN,YAAA,CAARM,QAAQ;MACRC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,MAAM,GAAAR,YAAA,CAANQ,MAAM;MACNC,QAAQ,GAAAT,YAAA,CAARS,QAAQ;MACRC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,SAAS,GAAAX,YAAA,CAATW,SAAS;MACTC,SAAS,GAAAZ,YAAA,CAATY,SAAS;MACTC,QAAQ,GAAAb,YAAA,CAARa,QAAQ;MACRC,YAAY,GAAAd,YAAA,CAAZc,YAAY;MACZC,UAAU,GAAAf,YAAA,CAAVe,UAAU;MACVC,WAAW,GAAAhB,YAAA,CAAXgB,WAAW;MACXC,cAAc,GAAAjB,YAAA,CAAdiB,cAAc;MACdC,gBAAgB,GAAAlB,YAAA,CAAhBkB,gBAAgB;MAChBC,iBAAiB,GAAAnB,YAAA,CAAjBmB,iBAAiB;MACjBC,iBAAiB,GAAApB,YAAA,CAAjBoB,iBAAiB;MACjBC,aAAa,GAAArB,YAAA,CAAbqB,aAAa;MACbC,eAAe,GAAAtB,YAAA,CAAfsB,eAAe;MACfC,kBAAkB,GAAAvB,YAAA,CAAlBuB,kBAAkB;MAClBC,iBAAiB,GAAAxB,YAAA,CAAjBwB,iBAAiB;MACjBC,UAAU,GAAAzB,YAAA,CAAVyB,UAAU;IAGZ5C,WAAW,CAAC,YAAM;MAChB,IAAM6C,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;MACnE,IAAIJ,UAAU,EAAE;QACdD,UAAU,CAACM,KAAK,CAACC,GAAG,GAAGN,UAAU;QACjCV,WAAW,CAAC,CAAC;QACbiB,UAAU,CAAC,YAAM;UACfJ,cAAc,CAACK,UAAU,CAAC,YAAY,CAAC;UACvCT,UAAU,CAACM,KAAK,CAACC,GAAG,GAAG,EAAE;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLhB,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IACF,IAAMmB,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;MACjCA,KAAK,CAACC,OAAO,CAAC,UAAAC,CAAC,EAAI;QACjB,IAAI,CAACA,CAAC,CAACC,iBAAiB,EAAE;UACxBD,CAAC,CAACC,iBAAiB,GAAGD,CAAC,CAACE,oBAAoB;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBrC,OAAO,CAAC2B,KAAK,GAAG,EAAE;MAClBf,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAM0B,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,MAAM;UACT,IAAI1B,cAAc,CAACc,KAAK,CAACa,MAAM,EAAE;YAC/BlD,EAAE,CAACqC,KAAK,GAAGd,cAAc,CAACc,KAAK,CAACc,GAAG,CAAC,UAACP,CAAC;cAAA,OAAKA,CAAC,CAAC5C,EAAE;YAAA,EAAC;YAChDG,IAAI,CAACkC,KAAK,GAAG,IAAI;UACnB,CAAC,MAAM;YACL1C,SAAS,CAAC;cAAEM,IAAI,EAAE,SAAS;cAAEmD,OAAO,EAAE;YAAY,CAAC,CAAC;UACtD;UACA;QACF,KAAK,UAAU;UACb,IAAI7B,cAAc,CAACc,KAAK,CAACa,MAAM,EAAE;YAC/BlD,EAAE,CAACqC,KAAK,GAAGd,cAAc,CAACc,KAAK,CAACc,GAAG,CAAC,UAACP,CAAC;cAAA,OAAKA,CAAC,CAAC5C,EAAE;YAAA,EAAC;YAChDK,YAAY,CAACgC,KAAK,GAAG,IAAI;UAC3B,CAAC,MAAM;YACL1C,SAAS,CAAC;cAAEM,IAAI,EAAE,SAAS;cAAEmD,OAAO,EAAE;YAAY,CAAC,CAAC;UACtD;UACA;QACF,KAAK,UAAU;UACb,IAAI7B,cAAc,CAACc,KAAK,CAACa,MAAM,EAAE;YAC/BlD,EAAE,CAACqC,KAAK,GAAGd,cAAc,CAACc,KAAK,CAACc,GAAG,CAAC,UAACP,CAAC;cAAA,OAAKA,CAAC,CAAC5C,EAAE;YAAA,EAAC;YAChDI,QAAQ,CAACiC,KAAK,GAAG,IAAI;UACvB,CAAC,MAAM;YACL1C,SAAS,CAAC;cAAEM,IAAI,EAAE,SAAS;cAAEmD,OAAO,EAAE;YAAY,CAAC,CAAC;UACtD;UACA;QACF,KAAK,YAAY;UACf7D,iBAAiB,CAACqC,eAAe,CAAC,CAAC,CAAC;UACpC;QACF,KAAK,QAAQ;UACXE,iBAAiB,CAAC,CAAC;UACnB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMuB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,GAAG,EAAEC,GAAG,EAAK;MACrC,QAAQD,GAAG;QACT,KAAK,SAAS;UACZE,aAAa,CAACD,GAAG,CAAC;UAClB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9BnE,YAAY,CAACoE,cAAc,CAAC;QAC1BC,SAAS,EAAE;UACT9D,IAAI,EAAE,MAAM;UACZ+D,IAAI,EAAE,yBAAyB;UAC/BpD,KAAK,EAAE;YAAER,EAAE,EAAEyD,IAAI,CAACzD,EAAE;YAAE6D,UAAU,EAAE/D,KAAK,CAACU,KAAK,CAACqD,UAAU;YAAE5D,IAAI,EAAE;UAAS;QAC3E;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAM6D,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBnC,aAAa,CAAC,CAAC;MACfL,WAAW,CAAC,CAAC;MACbD,UAAU,CAACgB,KAAK,GAAG,KAAK;MACxBlC,IAAI,CAACkC,KAAK,GAAG,KAAK;MAClBjC,QAAQ,CAACiC,KAAK,GAAG,KAAK;MACtBhC,YAAY,CAACgC,KAAK,GAAG,KAAK;IAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}