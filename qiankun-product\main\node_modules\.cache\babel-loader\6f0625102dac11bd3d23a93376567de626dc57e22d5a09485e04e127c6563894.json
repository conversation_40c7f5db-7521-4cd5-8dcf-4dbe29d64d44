{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, withCtx as _withCtx, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, resolveDynamicComponent as _resolveDynamicComponent, createBlock as _createBlock, KeepAlive as _KeepAlive } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalAiToolBoxHead\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalAiToolBoxLogo\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalAiToolBoxName ellipsis\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalAiToolBoxNavTitle\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalAiToolBoxNavList\"\n};\nvar _hoisted_6 = [\"onClick\"];\nvar _hoisted_7 = {\n  class: \"GlobalAiToolBoxNavToolItemIcon\"\n};\nvar _hoisted_8 = {\n  class: \"GlobalAiToolBoxNavToolItemTitle\"\n};\nvar _hoisted_9 = {\n  class: \"GlobalAiToolBoxMainHead\"\n};\nvar _hoisted_10 = {\n  class: \"GlobalAiToolBoxMainHeadLeft\"\n};\nvar _hoisted_11 = {\n  class: \"GlobalAiToolBoxMainHeadItem\"\n};\nvar _hoisted_12 = {\n  class: \"GlobalAiToolBoxMainHeadRight\"\n};\nvar _hoisted_13 = {\n  class: \"GlobalAiToolBoxUser\"\n};\nvar _hoisted_14 = {\n  class: \"forbidSelect\"\n};\nvar _hoisted_15 = {\n  class: \"AiToolBoxBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_Menu = _resolveComponent(\"Menu\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_aside = _resolveComponent(\"el-aside\");\n  var _component_el_main = _resolveComponent(\"el-main\");\n  var _component_el_container = _resolveComponent(\"el-container\");\n  return _openBlock(), _createBlock(_component_el_container, {\n    class: \"GlobalAiToolBox\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_aside, {\n        class: \"GlobalAiToolBoxNav\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n            src: $setup.systemLogo,\n            fit: \"cover\"\n          }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.systemNameAreaPrefix === 'true' ? $setup.regionName : '') + _toDisplayString($setup.systemName), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n            class: \"GlobalAiToolBoxTabBody\"\n          }, [_createElementVNode(\"div\", {\n            class: \"GlobalAiToolBoxTab\"\n          }, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n            class: \"GlobalAiToolBoxTabIcon\"\n          }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", {\n            class: \"GlobalAiToolBoxTabTitle\",\n            onClick: $setup.handleClick\n          }, \"首页\")])]), _createVNode(_component_el_scrollbar, {\n            class: \"GlobalAiToolBoxNav\",\n            style: _normalizeStyle(`background: url('${$setup.navBottom}') no-repeat;background-size: 100% auto;background-position: bottom center;`)\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.navList, function (item) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  class: \"GlobalAiToolBoxNavItem\",\n                  key: item.id\n                }, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString(item.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.tool, function (tool) {\n                  return _openBlock(), _createElementBlock(\"div\", {\n                    class: _normalizeClass([\"GlobalAiToolBoxNavToolItem\", {\n                      'is-active': $setup.toolId == tool.id\n                    }]),\n                    key: tool.id,\n                    onClick: function onClick($event) {\n                      return $setup.handleToolClick(tool);\n                    }\n                  }, [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_icon, null, {\n                    default: _withCtx(function () {\n                      return [_createVNode(_component_Menu)];\n                    }),\n                    _: 1 /* STABLE */\n                  })]), _createElementVNode(\"div\", _hoisted_8, _toDisplayString(tool.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_6);\n                }), 128 /* KEYED_FRAGMENT */))])]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"style\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_main, {\n        class: \"GlobalAiToolBoxMain\",\n        style: _normalizeStyle(`background: url('${$setup.mainTop}') no-repeat #f7f7f7;background-size: 100% auto;background-position: top right;`)\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString($setup.toolName), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_image, {\n            src: $setup.user.image,\n            fit: \"cover\"\n          }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($setup.user.userName), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n            class: \"GlobalAiToolBoxExit\",\n            innerHTML: $setup.exitIcon,\n            onClick: $setup.handleExit\n          })])]), _createElementVNode(\"div\", _hoisted_15, [(_openBlock(), _createBlock(_KeepAlive, null, [(_openBlock(), _createBlock(_resolveDynamicComponent($setup.AiToolBoxElement[$setup.toolId])))], 1024 /* DYNAMIC_SLOTS */))])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"style\"])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_container", "default", "_withCtx", "_createVNode", "_component_el_aside", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_component_el_image", "src", "$setup", "systemLogo", "fit", "_hoisted_3", "_toDisplayString", "systemNameAreaPrefix", "regionName", "systemName", "onClick", "handleClick", "_component_el_scrollbar", "style", "_normalizeStyle", "navBottom", "_createElementBlock", "_Fragment", "_renderList", "navList", "item", "key", "id", "_hoisted_4", "title", "_hoisted_5", "tool", "_normalizeClass", "toolId", "$event", "handleToolClick", "_hoisted_7", "_component_el_icon", "_component_Menu", "_", "_hoisted_8", "name", "_hoisted_6", "_component_el_main", "mainTop", "_hoisted_9", "_hoisted_10", "_hoisted_11", "toolName", "_hoisted_12", "_hoisted_13", "user", "image", "_hoisted_14", "userName", "innerHTML", "exitIcon", "handleExit", "_hoisted_15", "_KeepAlive", "_resolveDynamicComponent", "AiToolBoxElement"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBox\\GlobalAiToolBox.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"GlobalAiToolBox\">\r\n    <el-aside class=\"GlobalAiToolBoxNav\">\r\n      <div class=\"GlobalAiToolBoxHead\">\r\n        <div class=\"GlobalAiToolBoxLogo\">\r\n          <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n        </div>\r\n        <div class=\"GlobalAiToolBoxName ellipsis\">\r\n          {{ systemNameAreaPrefix === 'true' ? regionName : '' }}{{ systemName }}\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalAiToolBoxTabBody\">\r\n        <div class=\"GlobalAiToolBoxTab\">\r\n          <div class=\"GlobalAiToolBoxTabIcon\"></div>\r\n          <div class=\"GlobalAiToolBoxTabTitle\" @click=\"handleClick\">首页</div>\r\n        </div>\r\n      </div>\r\n      <el-scrollbar\r\n        class=\"GlobalAiToolBoxNav\"\r\n        :style=\"`background: url('${navBottom}') no-repeat;background-size: 100% auto;background-position: bottom center;`\">\r\n        <div class=\"GlobalAiToolBoxNavItem\" v-for=\"item in navList\" :key=\"item.id\">\r\n          <div class=\"GlobalAiToolBoxNavTitle\">{{ item.title }}</div>\r\n          <div class=\"GlobalAiToolBoxNavList\">\r\n            <div\r\n              class=\"GlobalAiToolBoxNavToolItem\"\r\n              v-for=\"tool in item.tool\"\r\n              :key=\"tool.id\"\r\n              :class=\"{ 'is-active': toolId == tool.id }\"\r\n              @click=\"handleToolClick(tool)\">\r\n              <div class=\"GlobalAiToolBoxNavToolItemIcon\">\r\n                <el-icon><Menu /></el-icon>\r\n              </div>\r\n              <div class=\"GlobalAiToolBoxNavToolItemTitle\">{{ tool.name }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n    </el-aside>\r\n    <el-main\r\n      class=\"GlobalAiToolBoxMain\"\r\n      :style=\"`background: url('${mainTop}') no-repeat #f7f7f7;background-size: 100% auto;background-position: top right;`\">\r\n      <div class=\"GlobalAiToolBoxMainHead\">\r\n        <div class=\"GlobalAiToolBoxMainHeadLeft\">\r\n          <div class=\"GlobalAiToolBoxMainHeadItem\">{{ toolName }}</div>\r\n        </div>\r\n        <div class=\"GlobalAiToolBoxMainHeadRight\">\r\n          <div class=\"GlobalAiToolBoxUser\">\r\n            <el-image :src=\"user.image\" fit=\"cover\" />\r\n            <span class=\"forbidSelect\">{{ user.userName }}</span>\r\n          </div>\r\n          <div class=\"GlobalAiToolBoxExit\" v-html=\"exitIcon\" @click=\"handleExit\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiToolBoxBody\">\r\n        <keep-alive>\r\n          <component :is=\"AiToolBoxElement[toolId]\" />\r\n        </keep-alive>\r\n      </div>\r\n    </el-main>\r\n  </el-container>\r\n</template>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport config from 'common/config'\r\nimport { ref, computed } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod.js'\r\nimport { user, systemLogo, systemName, systemNameAreaPrefix } from 'common/js/system_var.js'\r\nimport { AiToolBoxElement } from './AiToolBox.js'\r\nimport { ElMessageBox, ElMessage } from 'element-plus'\r\nconst router = useRouter()\r\nconst store = useStore()\r\nconst mainTop = computed(() => `${config.API_URL}/pageImg/open/GlobalAiToolBoxMainTop?areaId=${user.value.areaId}`)\r\nconst navBottom = computed(() => `${config.API_URL}/pageImg/open/GlobalAiToolBoxNavBottom?areaId=${user.value.areaId}`)\r\nconst exitIcon = `<svg t=\"1739268585892\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2352\" width=\"24\" height=\"24\"><path d=\"M0 192v640c0 70.7 57.3 128 128 128h352c17.7 0 32-14.3 32-32s-14.3-32-32-32H128c-35.3 0-64-28.7-64-64V192c0-35.3 28.7-64 64-64h352c17.7 0 32-14.3 32-32s-14.3-32-32-32H128C57.3 64 0 121.3 0 192z\" p-id=\"2353\" fill=\"#666666\"></path><path d=\"M1013.3 488.3L650.9 160.7c-41.2-37.2-106.9-8-106.9 47.5V339c0 4.4-3.6 8-8 8H224c-17.7 0-32 14.3-32 32v266c0 17.7 14.3 32 32 32h312c4.4 0 8 3.6 8 8v130.9c0 55.5 65.8 84.7 106.9 47.5l362.4-327.6c14.1-12.8 14.1-34.8 0-47.5zM256 597V427c0-8.8 7.2-16 16-16h304c17.7 0 32-14.3 32-32V244.9c0-13.9 16.4-21.2 26.7-11.9L938 506.1c3.5 3.2 3.5 8.7 0 11.9L634.7 791c-10.3 9.3-26.7 2-26.7-11.9V645c0-17.7-14.3-32-32-32H272c-8.8 0-16-7.2-16-16z\" p-id=\"2354\" fill=\"#666666\"></path></svg>`\r\nconst regionName = ref('')\r\nconst toolId = ref('IntelligentErrorCorrection')\r\nconst toolName = ref('智能纠错')\r\nconst navList = ref([\r\n  {\r\n    id: '1',\r\n    title: '工作通用工具',\r\n    tool: [\r\n      { id: 'IntelligentErrorCorrection', name: '智能纠错' },\r\n      { id: 'OneClickLayout', name: '一件排版' },\r\n      { id: 'ContentExtraction', name: '内容提炼' },\r\n      { id: 'IntelligentManuscriptMerging', name: '智能合稿' },\r\n      { id: 'TextComparison', name: '文本比对' },\r\n      { id: 'TextPolishing', name: '文本润色' },\r\n      { id: 'TextExpansion', name: '文本扩写' },\r\n      { id: 'TextContinuation', name: '文本续写' },\r\n      { id: 'TextRewrite', name: '文本重写' },\r\n      { id: 'TextRecognition', name: '文本识别' }\r\n    ]\r\n  },\r\n  {\r\n    id: '2',\r\n    title: '业务专用工具',\r\n    tool: [{ id: 'ProposalAuxiliaryWriting', name: '提案辅助撰写' }]\r\n  }\r\n])\r\nconst handleToolClick = (tool) => {\r\n  toolId.value = tool.id\r\n  toolName.value = tool.name\r\n}\r\nconst handleClick = () => {\r\n  router.push('/')\r\n}\r\nconst handleExit = () => {\r\n  ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  })\r\n    .then(() => {\r\n      loginOut('已安全退出！')\r\n    })\r\n    .catch(() => {\r\n      ElMessage({ type: 'info', message: '已取消退出' })\r\n    })\r\n}\r\nconst loginOut = async (text) => {\r\n  const { code } = await api.loginOut()\r\n  if (code === 200) {\r\n    sessionStorage.clear()\r\n    const goal_login_router_path = localStorage.getItem('goal_login_router_path')\r\n    if (goal_login_router_path) {\r\n      const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''\r\n      router.push({\r\n        path: goal_login_router_path,\r\n        query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\r\n      })\r\n    } else {\r\n      router.push({ path: '/LoginView' })\r\n    }\r\n    store.commit('setState')\r\n    globalReadOpenConfig()\r\n    // store.state.socket.disconnect()\r\n    // store.state.socket = null\r\n    ElMessage({ message: text, showClose: true, type: 'success' })\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiToolBox {\r\n  width: 100%;\r\n  height: 100%;\r\n  .GlobalAiToolBoxNav {\r\n    width: calc((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four));\r\n    height: 100%;\r\n    background: var(--zy-el-color-primary);\r\n\r\n    .GlobalAiToolBoxHead {\r\n      height: 78px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 var(--zy-distance-two);\r\n\r\n      .GlobalAiToolBoxLogo {\r\n        width: 50px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .GlobalAiToolBoxName {\r\n        width: calc(100% - 40px);\r\n        color: #fff;\r\n        font-weight: bold;\r\n        font-size: 22px;\r\n        padding-left: 12px;\r\n      }\r\n    }\r\n    .GlobalAiToolBoxTabBody {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding-bottom: var(--zy-distance-one);\r\n      .GlobalAiToolBoxTab {\r\n        width: 182px;\r\n        height: 36px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding-right: var(--zy-font-name-distance-five);\r\n        position: relative;\r\n        &::before {\r\n          content: '';\r\n          width: 100%;\r\n          height: 100%;\r\n          position: absolute;\r\n          top: 0;\r\n          left: 0;\r\n          border-radius: 18px;\r\n          box-sizing: border-box;\r\n          border: 1px solid #ffffff;\r\n          z-index: 1;\r\n        }\r\n        .GlobalAiToolBoxTabIcon {\r\n          width: 110px;\r\n          height: 36px;\r\n          border-radius: 18px;\r\n          background: url('../img/global_ai_tool_box_icon.png') no-repeat #e0e8ff;\r\n          background-size: 72px auto;\r\n          background-position: center center;\r\n          position: relative;\r\n          z-index: 2;\r\n        }\r\n        .GlobalAiToolBoxTabTitle {\r\n          width: calc(100% - 110px);\r\n          cursor: pointer;\r\n          color: #fff;\r\n          text-align: center;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          position: relative;\r\n          z-index: 2;\r\n        }\r\n      }\r\n    }\r\n    .GlobalAiToolBoxNav {\r\n      width: 100%;\r\n      height: calc(100% - (78px + 36px + var(--zy-distance-one)));\r\n      .GlobalAiToolBoxNavItem {\r\n        width: 100%;\r\n        padding: 0 var(--zy-distance-two);\r\n        .GlobalAiToolBoxNavTitle {\r\n          width: 100%;\r\n          color: #fff;\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n        .GlobalAiToolBoxNavList {\r\n          width: 100%;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding: var(--zy-distance-four) 0;\r\n          .GlobalAiToolBoxNavToolItem {\r\n            width: 112px;\r\n            height: 112px;\r\n            display: flex;\r\n            align-items: center;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n            border: 1px solid transparent;\r\n            background: rgba(255, 255, 255, 0.3);\r\n            border-radius: var(--el-border-radius-base);\r\n            border-image: linear-gradient(131deg, rgba(255, 255, 255, 0.2), rgba(224, 232, 255, 0.1)) 1 1;\r\n            margin-bottom: var(--zy-distance-four);\r\n            cursor: pointer;\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.9);\r\n              border: 1px solid rgba(255, 255, 255, 1);\r\n              .GlobalAiToolBoxNavToolItemTitle {\r\n                color: var(--zy-el-text-color-primary);\r\n              }\r\n            }\r\n            &.is-active {\r\n              background: rgba(255, 255, 255, 1);\r\n              border: 1px solid rgba(255, 255, 255, 1);\r\n              .GlobalAiToolBoxNavToolItemIcon {\r\n                background: var(--zy-el-color-primary);\r\n              }\r\n              .GlobalAiToolBoxNavToolItemTitle {\r\n                color: var(--zy-el-text-color-primary);\r\n              }\r\n            }\r\n            .GlobalAiToolBoxNavToolItemIcon {\r\n              width: 52px;\r\n              height: 52px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              background: var(--zy-el-color-primary-light-3);\r\n              border-radius: var(--el-border-radius-base);\r\n              margin: var(--zy-font-text-distance-five) 0;\r\n              .zy-el-icon {\r\n                font-size: 38px;\r\n                color: #fff;\r\n              }\r\n            }\r\n            .GlobalAiToolBoxNavToolItemTitle {\r\n              width: 100%;\r\n              color: #fff;\r\n              text-align: center;\r\n              font-size: var(--zy-text-font-size);\r\n              line-height: var(--zy-line-height);\r\n              padding-top: var(--zy-font-text-distance-five);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .GlobalAiToolBoxMain {\r\n    width: calc(100% - ((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four)));\r\n    height: 100%;\r\n    padding: 0;\r\n    .GlobalAiToolBoxMainHead {\r\n      width: 100%;\r\n      height: 68px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 0 var(--zy-distance-two);\r\n      padding-top: 10px;\r\n\r\n      .GlobalAiToolBoxUser {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-right: var(--zy-distance-two);\r\n        cursor: pointer;\r\n\r\n        .zy-el-image {\r\n          height: 38px;\r\n          width: 38px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        span {\r\n          margin-left: 8px;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n      }\r\n      .GlobalAiToolBoxMainHeadLeft {\r\n        display: flex;\r\n        align-items: center;\r\n        .GlobalAiToolBoxMainHeadItem {\r\n          width: 100%;\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n          padding: var(--zy-font-name-distance-five);\r\n          border-bottom: 3px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n      .GlobalAiToolBoxMainHeadRight {\r\n        display: flex;\r\n        align-items: center;\r\n        .GlobalAiToolBoxExit {\r\n          width: 30px;\r\n          height: 30px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n    .AiToolBoxBody {\r\n      width: 100%;\r\n      height: calc(100% - 68px);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAGWA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAqB;;EAG3BA,KAAK,EAAC;AAA8B;;EAclCA,KAAK,EAAC;AAAyB;;EAC/BA,KAAK,EAAC;AAAwB;iBAtB7C;;EA6BmBA,KAAK,EAAC;AAAgC;;EAGtCA,KAAK,EAAC;AAAiC;;EAS/CA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAA6B;;EAErCA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAAqB;;EAExBA,KAAK,EAAC;AAAc;;EAK3BA,KAAK,EAAC;AAAe;;;;;;;;;uBApD9BC,YAAA,CA0DeC,uBAAA;IA1DDF,KAAK,EAAC;EAAiB;IADvCG,OAAA,EAAAC,QAAA,CAEI;MAAA,OAmCW,CAnCXC,YAAA,CAmCWC,mBAAA;QAnCDN,KAAK,EAAC;MAAoB;QAFxCG,OAAA,EAAAC,QAAA,CAGM;UAAA,OAOM,CAPNG,mBAAA,CAOM,OAPNC,UAOM,GANJD,mBAAA,CAEM,OAFNE,UAEM,GADJJ,YAAA,CAA0CK,mBAAA;YAA/BC,GAAG,EAAEC,MAAA,CAAAC,UAAU;YAAEC,GAAG,EAAC;8CAElCP,mBAAA,CAEM,OAFNQ,UAEM,EAAAC,gBAAA,CADDJ,MAAA,CAAAK,oBAAoB,cAAcL,MAAA,CAAAM,UAAU,SAAAF,gBAAA,CAAWJ,MAAA,CAAAO,UAAU,iB,GAGxEZ,mBAAA,CAKM;YALDP,KAAK,EAAC;UAAwB,IACjCO,mBAAA,CAGM;YAHDP,KAAK,EAAC;UAAoB,I,0BAC7BO,mBAAA,CAA0C;YAArCP,KAAK,EAAC;UAAwB,6BACnCO,mBAAA,CAAkE;YAA7DP,KAAK,EAAC,yBAAyB;YAAEoB,OAAK,EAAER,MAAA,CAAAS;aAAa,IAAE,E,KAGhEhB,YAAA,CAmBeiB,uBAAA;YAlBbtB,KAAK,EAAC,oBAAoB;YACzBuB,KAAK,EAnBdC,eAAA,qBAmBoCZ,MAAA,CAAAa,SAAS;;YAnB7CtB,OAAA,EAAAC,QAAA,CAoB4C;cAAA,OAAuB,E,kBAA3DsB,mBAAA,CAeMC,SAAA,QAnCdC,WAAA,CAoB2DhB,MAAA,CAAAiB,OAAO,EApBlE,UAoBmDC,IAAI;qCAA/CJ,mBAAA,CAeM;kBAfD1B,KAAK,EAAC,wBAAwB;kBAA0B+B,GAAG,EAAED,IAAI,CAACE;oBACrEzB,mBAAA,CAA2D,OAA3D0B,UAA2D,EAAAjB,gBAAA,CAAnBc,IAAI,CAACI,KAAK,kBAClD3B,mBAAA,CAYM,OAZN4B,UAYM,I,kBAXJT,mBAAA,CAUMC,SAAA,QAjClBC,WAAA,CAyB6BE,IAAI,CAACM,IAAI,EAzBtC,UAyBqBA,IAAI;uCAFbV,mBAAA,CAUM;oBATJ1B,KAAK,EAxBnBqC,eAAA,EAwBoB,4BAA4B;sBAAA,aAGXzB,MAAA,CAAA0B,MAAM,IAAIF,IAAI,CAACJ;oBAAE;oBADvCD,GAAG,EAAEK,IAAI,CAACJ,EAAE;oBAEZZ,OAAK,WAALA,OAAKA,CAAAmB,MAAA;sBAAA,OAAE3B,MAAA,CAAA4B,eAAe,CAACJ,IAAI;oBAAA;sBAC5B7B,mBAAA,CAEM,OAFNkC,UAEM,GADJpC,YAAA,CAA2BqC,kBAAA;oBA9B3CvC,OAAA,EAAAC,QAAA,CA8ByB;sBAAA,OAAQ,CAARC,YAAA,CAAQsC,eAAA,E;;oBA9BjCC,CAAA;wBAgCcrC,mBAAA,CAAkE,OAAlEsC,UAAkE,EAAA7B,gBAAA,CAAlBoB,IAAI,CAACU,IAAI,iB,yBAhCvEC,UAAA;;;;YAAAH,CAAA;;;QAAAA,CAAA;UAsCIvC,YAAA,CAoBU2C,kBAAA;QAnBRhD,KAAK,EAAC,qBAAqB;QAC1BuB,KAAK,EAxCZC,eAAA,qBAwCkCZ,MAAA,CAAAqC,OAAO;;QAxCzC9C,OAAA,EAAAC,QAAA,CAyCM;UAAA,OAWM,CAXNG,mBAAA,CAWM,OAXN2C,UAWM,GAVJ3C,mBAAA,CAEM,OAFN4C,WAEM,GADJ5C,mBAAA,CAA6D,OAA7D6C,WAA6D,EAAApC,gBAAA,CAAjBJ,MAAA,CAAAyC,QAAQ,iB,GAEtD9C,mBAAA,CAMM,OANN+C,WAMM,GALJ/C,mBAAA,CAGM,OAHNgD,WAGM,GAFJlD,YAAA,CAA0CK,mBAAA;YAA/BC,GAAG,EAAEC,MAAA,CAAA4C,IAAI,CAACC,KAAK;YAAE3C,GAAG,EAAC;4CAChCP,mBAAA,CAAqD,QAArDmD,WAAqD,EAAA1C,gBAAA,CAAvBJ,MAAA,CAAA4C,IAAI,CAACG,QAAQ,iB,GAE7CpD,mBAAA,CAA6E;YAAxEP,KAAK,EAAC,qBAAqB;YAAC4D,SAAiB,EAAThD,MAAA,CAAAiD,QAAQ;YAAGzC,OAAK,EAAER,MAAA,CAAAkD;kBAG/DvD,mBAAA,CAIM,OAJNwD,WAIM,I,cAHJ9D,YAAA,CAEa+D,UAAA,U,cADX/D,YAAA,CAA4CgE,wBAvDtD,CAuD0BrD,MAAA,CAAAsD,gBAAgB,CAACtD,MAAA,CAAA0B,MAAM,K;;QAvDjDM,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}