{"ast": null, "code": "import { normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, withModifiers as _withModifiers, Transition as _Transition, withCtx as _withCtx } from \"vue\";\nimport _imports_0 from '../img/login_btn_bg.png';\nvar _hoisted_1 = {\n  class: \"WorkBenchCopy\"\n};\nvar _hoisted_2 = {\n  class: \"WorkBenchHeader\"\n};\nvar _hoisted_3 = {\n  class: \"WorkBenchHeaderLogoName\"\n};\nvar _hoisted_4 = [\"innerHTML\"];\nvar _hoisted_5 = {\n  class: \"WorkBenchHeaderRight\"\n};\nvar _hoisted_6 = [\"src\"];\nvar _hoisted_7 = {\n  class: \"WorkBenchHeaderName\"\n};\nvar _hoisted_8 = {\n  class: \"WorkBenchContent\"\n};\nvar _hoisted_9 = {\n  class: \"menu-container\"\n};\nvar _hoisted_10 = [\"onClick\"];\nvar _hoisted_11 = {\n  class: \"menu-title\"\n};\nvar _hoisted_12 = {\n  key: 0,\n  class: \"module-area\"\n};\nvar _hoisted_13 = {\n  class: \"module-list\"\n};\nvar _hoisted_14 = [\"onClick\"];\nvar _hoisted_15 = [\"src\"];\nvar _hoisted_16 = {\n  class: \"module-title\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_xyl_region = _resolveComponent(\"xyl-region\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"background-layer\", {\n      'blur-bg': $setup.activeMenuIndex !== null\n    }]),\n    style: _normalizeStyle({\n      backgroundImage: `url(${$setup.backgroundImage})`\n    })\n  }, null, 6 /* CLASS, STYLE */), _createElementVNode(\"div\", {\n    class: \"content-layer\",\n    onClick: $setup.handleContentClick\n  }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n    class: \"WorkBenchHeaderLogo\",\n    src: $setup.systemLogo,\n    fit: \"contain\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", {\n    class: \"WorkBenchHeaderName\",\n    innerHTML: $setup.systemName\n  }, null, 8 /* PROPS */, _hoisted_4)]), _createElementVNode(\"div\", _hoisted_5, [_createCommentVNode(\" <span class=\\\"WorkBenchHeaderLink\\\">操作指南</span> \"), _createVNode(_component_xyl_region, {\n    modelValue: $setup.regionId,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.regionId = $event;\n    }),\n    data: $setup.area,\n    onSelect: $setup.regionSelect,\n    props: {\n      label: 'name',\n      children: 'children'\n    }\n  }, null, 8 /* PROPS */, [\"modelValue\", \"data\", \"onSelect\"]), _createElementVNode(\"button\", {\n    class: \"old_version\",\n    onClick: $setup.oldVersion\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"旧版本\", -1 /* HOISTED */)])), $setup.isSys ? (_openBlock(), _createElementBlock(\"span\", {\n    key: 0,\n    class: \"WorkBenchHeaderLink\",\n    onClick: $setup.sysManagement\n  }, \"系统管理\")) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    class: \"WorkBenchHeaderUser\",\n    onClick: $setup.sysUser\n  }, [_createElementVNode(\"img\", {\n    class: \"WorkBenchHeaderAvatar\",\n    src: $setup.user.image,\n    alt: \"头像\"\n  }, null, 8 /* PROPS */, _hoisted_6), _createElementVNode(\"span\", _hoisted_7, _toDisplayString($setup.user.userName), 1 /* TEXT */)]), _createElementVNode(\"span\", {\n    class: \"WorkBenchHeaderLogout WorkBenchHeaderLink\",\n    onClick: $setup.handleExit\n  }, \"退出\")])]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"menu-list\", {\n      'has-active': $setup.activeMenuIndex !== null\n    }])\n  }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.menuItems, function (item, index) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: _normalizeClass([\"menu-item\", [{\n        active: $setup.activeMenuIndex === index\n      }, index === 0 ? 'menu-item-first' : '', index === 1 ? 'menu-item-second' : '', index === 2 ? 'menu-item-third' : '', index === 3 ? 'menu-item-fourth' : '', index === 0 || index === 3 ? 'u-top' : 'u-bottom']]),\n      style: _normalizeStyle({\n        backgroundImage: `url(${$setup.activeMenuIndex === index ? item.bgActive : item.bg})`\n      }),\n      onClick: _withModifiers(function ($event) {\n        return $setup.handleMenuClick(index);\n      }, [\"stop\"])\n    }, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString(item.title), 1 /* TEXT */)], 14 /* CLASS, STYLE, PROPS */, _hoisted_10);\n  }), 128 /* KEYED_FRAGMENT */))], 2 /* CLASS */), _createVNode(_Transition, {\n    name: \"fade-slide\"\n  }, {\n    default: _withCtx(function () {\n      return [$setup.activeMenuIndex !== null ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"module-bg\"\n      }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subMenus[$setup.activeMenuIndex] || [], function (mod, idx) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: mod.id,\n          class: \"module-item\",\n          onClick: function onClick($event) {\n            return $setup.handleWorkBench(mod);\n          }\n        }, [_createElementVNode(\"img\", {\n          class: \"module-icon\",\n          src: mod.icon\n        }, null, 8 /* PROPS */, _hoisted_15), _createElementVNode(\"div\", _hoisted_16, _toDisplayString(mod.name), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_14);\n      }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  })])])]), _createCommentVNode(\" 图片弹窗 \"), $setup.showImageDialog ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"image-dialog-overlay\",\n    onClick: $setup.closeImageDialog\n  }, [_createElementVNode(\"div\", {\n    class: \"image-dialog-content\",\n    onClick: _cache[1] || (_cache[1] = _withModifiers(function () {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", {\n    class: \"image-dialog-close\",\n    onClick: $setup.closeImageDialog\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"Close\"])];\n    }),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_el_image, {\n    src: $setup.dialogImageUrl,\n    fit: \"contain\",\n    class: \"dialog-image\",\n    onClick: $setup.handleImageClick\n  }, null, 8 /* PROPS */, [\"src\"])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 直播弹窗 \"), _createVNode($setup[\"LiveBroadcastDetails\"], {\n    modelValue: $setup.detailsShow,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.detailsShow = $event;\n    }),\n    id: $setup.detailsId,\n    onCallback: _ctx.callback\n  }, null, 8 /* PROPS */, [\"modelValue\", \"id\", \"onCallback\"])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_normalizeClass", "$setup", "activeMenuIndex", "style", "_normalizeStyle", "backgroundImage", "onClick", "handleContentClick", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_image", "src", "systemLogo", "fit", "innerHTML", "systemName", "_hoisted_4", "_hoisted_5", "_createCommentVNode", "_component_xyl_region", "modelValue", "regionId", "_cache", "$event", "data", "area", "onSelect", "regionSelect", "props", "label", "children", "oldVersion", "alt", "isSys", "sysManagement", "sysUser", "user", "image", "_hoisted_6", "_hoisted_7", "_toDisplayString", "userName", "handleExit", "_hoisted_8", "_hoisted_9", "_Fragment", "_renderList", "menuItems", "item", "index", "bgActive", "bg", "_withModifiers", "handleMenuClick", "_hoisted_11", "title", "_hoisted_10", "_Transition", "name", "default", "_withCtx", "_hoisted_12", "_hoisted_13", "subMenus", "mod", "idx", "id", "handleWorkBench", "icon", "_hoisted_15", "_hoisted_16", "_hoisted_14", "_", "showImageDialog", "closeImageDialog", "_component_el_icon", "dialogImageUrl", "handleImageClick", "detailsShow", "detailsId", "onCallback", "_ctx", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\WorkBench\\WorkBenchCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"WorkBenchCopy\">\r\n    <div class=\"background-layer\" :class=\"{ 'blur-bg': activeMenuIndex !== null }\"\r\n      :style=\"{ backgroundImage: `url(${backgroundImage})` }\"></div>\r\n    <div class=\"content-layer\" @click=\"handleContentClick\">\r\n      <div class=\"WorkBenchHeader\">\r\n        <div class=\"WorkBenchHeaderLogoName\">\r\n          <el-image class=\"WorkBenchHeaderLogo\" :src=\"systemLogo\" fit=\"contain\" />\r\n          <div class=\"WorkBenchHeaderName\" v-html=\"systemName\"></div>\r\n        </div>\r\n        <div class=\"WorkBenchHeaderRight\">\r\n          <!-- <span class=\"WorkBenchHeaderLink\">操作指南</span> -->\r\n          <xyl-region v-model=\"regionId\" :data=\"area\" @select=\"regionSelect\"\r\n            :props=\"{ label: 'name', children: 'children' }\"></xyl-region>\r\n          <button class=\"old_version\" @click=\"oldVersion\">\r\n            <img src=\"../img/login_btn_bg.png\" alt=\"\" />\r\n            <span>旧版本</span>\r\n          </button>\r\n          <span class=\"WorkBenchHeaderLink\" @click=\"sysManagement\" v-if=\"isSys\">系统管理</span>\r\n          <div class=\"WorkBenchHeaderUser\" @click=\"sysUser\">\r\n            <img class=\"WorkBenchHeaderAvatar\" :src=\"user.image\" alt=\"头像\" />\r\n            <span class=\"WorkBenchHeaderName\">{{ user.userName }}</span>\r\n          </div>\r\n          <span class=\"WorkBenchHeaderLogout WorkBenchHeaderLink\" @click=\"handleExit\">退出</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"WorkBenchContent\">\r\n        <div class=\"menu-container\">\r\n          <div class=\"menu-list\" :class=\"{ 'has-active': activeMenuIndex !== null }\">\r\n            <div v-for=\"(item, index) in menuItems\" :key=\"index\" class=\"menu-item\" :class=\"[\r\n              { active: activeMenuIndex === index },\r\n              index === 0 ? 'menu-item-first' : '',\r\n              index === 1 ? 'menu-item-second' : '',\r\n              index === 2 ? 'menu-item-third' : '',\r\n              index === 3 ? 'menu-item-fourth' : '',\r\n              index === 0 || index === 3 ? 'u-top' : 'u-bottom'\r\n            ]\" :style=\"{\r\n              backgroundImage: `url(${activeMenuIndex === index ? item.bgActive : item.bg})`\r\n            }\" @click.stop=\"handleMenuClick(index)\">\r\n              <div class=\"menu-title\">{{ item.title }}</div>\r\n            </div>\r\n          </div>\r\n          <transition name=\"fade-slide\">\r\n            <div v-if=\"activeMenuIndex !== null\" class=\"module-area\">\r\n              <div class=\"module-bg\"></div>\r\n              <div class=\"module-list\">\r\n                <div v-for=\"(mod, idx) in subMenus[activeMenuIndex] || []\" :key=\"mod.id\" class=\"module-item\"\r\n                  @click=\"handleWorkBench(mod)\">\r\n                  <img class=\"module-icon\" :src=\"mod.icon\" />\r\n                  <div class=\"module-title\">{{ mod.name }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片弹窗 -->\r\n    <div v-if=\"showImageDialog\" class=\"image-dialog-overlay\" @click=\"closeImageDialog\">\r\n      <div class=\"image-dialog-content\" @click.stop>\r\n        <div class=\"image-dialog-close\" @click=\"closeImageDialog\">\r\n          <el-icon>\r\n            <Close />\r\n          </el-icon>\r\n        </div>\r\n        <el-image :src=\"dialogImageUrl\" fit=\"contain\" class=\"dialog-image\" @click=\"handleImageClick\" />\r\n      </div>\r\n    </div>\r\n    <!-- 直播弹窗 -->\r\n    <LiveBroadcastDetails v-model=\"detailsShow\" :id=\"detailsId\" @callback=\"callback\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'WorkBenchCopy' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { useStore } from 'vuex'\r\nimport { useRouter } from 'vue-router'\r\nimport { ref, inject, onMounted, computed, nextTick } from 'vue'\r\nimport { user, systemLogo, systemName } from 'common/js/system_var.js'\r\nimport config from 'common/config'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod'\r\nimport LiveBroadcastDetails from \"../../microApp/interaction/src/views/LiveManagement/LiveBroadcastDetails.vue\"\r\nconst router = useRouter()\r\nconst openPage = inject('openPage')\r\nconst leftMenuData = inject('leftMenuData')\r\nconst menuListData = inject('WorkBenchList')\r\nconst regionId = inject('regionId')\r\nconst regionSelect = inject('regionSelect')\r\nconst area = inject('area')\r\n// const menuListData = computed(() => filterMenu(store.getters.getMenuFn || []))\r\nconst store = useStore()\r\nconst menuIcon = `${config.API_URL}/pageImg/open/menuIcon`\r\nconst backgroundImage = ref(`${config.API_URL}/pageImg/open/homePage?areaId=${user.value.areaId}`)\r\nconst originMenuItems = [\r\n  {\r\n    title: '',\r\n    bg: require('../img/menu_wdgz.png'),\r\n    bgActive: require('../img/menu_wdgz_s.png')\r\n  },\r\n  {\r\n    title: '',\r\n    bg: require('../img/menu_zhyy.png'),\r\n    bgActive: require('../img/menu_zhyy_s.png')\r\n  },\r\n  {\r\n    title: '',\r\n    bg: require('../img/menu_wddb.png'),\r\n    bgActive: require('../img/menu_wddb_s.png')\r\n  },\r\n  {\r\n    title: '',\r\n    bg: require('../img/menu_qtyy.png'),\r\n    bgActive: require('../img/menu_qtyy_s.png')\r\n  }\r\n]\r\nconst activeMenuIndex = ref(null)\r\nconst isSys = ref(false)\r\n\r\n// 弹窗相关变量\r\nconst showImageDialog = ref(false)\r\nconst dialogImageUrl = ref('')\r\nconst dialogData = ref(null)\r\nconst detailsShow = ref(false)\r\nconst detailsId = ref('')\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    setTimeout(() => {\r\n      const roleList = JSON.parse(sessionStorage.getItem('role'))\r\n      console.log('当前角色===>', roleList)\r\n      if (roleList) { isSys.value = roleList?.includes('管理员') }\r\n      getLiveDialog()\r\n    }, 1000);\r\n  })\r\n})\r\n// 检测是否存在直播弹窗\r\nconst getLiveDialog = async () => {\r\n  const res = await api.globalJson('/popUp/list', {\r\n    pageNo: 1,\r\n    pageSize: 10,\r\n    from: 'pc',\r\n    // query: { type: 1 },\r\n    keyword: ''\r\n  })\r\n  if (res.code === 200 && res.data && res.data.length > 0) {\r\n    dialogData.value = res.data[0]\r\n    dialogImageUrl.value = api.fileURL(res.data[0].pcImg)\r\n    showImageDialog.value = true\r\n  }\r\n}\r\n// 关闭图片弹窗\r\nconst closeImageDialog = () => {\r\n  showImageDialog.value = false\r\n  dialogImageUrl.value = ''\r\n  dialogData.value = null\r\n}\r\n// 处理图片点击事件（如果需要跳转）\r\nconst handleImageClick = () => {\r\n  if (dialogData.value && dialogData.value.pcUrl) {\r\n    detailsId.value = dialogData.value.pcUrl\r\n    detailsShow.value = true\r\n    // window.open(dialogData.value.pcUrl, '_blank')\r\n  }\r\n}\r\nconst menuItems = computed(() => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  return filtered.map((item, idx) => ({\r\n    ...originMenuItems[idx],\r\n    title: item.name,\r\n    id: item.id,\r\n    has: item.permissions\r\n  }))\r\n})\r\nconst subMenus = computed(() => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  var arr = filtered.map(item => (item.children || []).map(child => ({\r\n    id: child.id,\r\n    name: child.name,\r\n    icon: child.icon,\r\n    children: child.children,\r\n    routePath: child.routePath,\r\n    menuFunction: child.menuFunction,\r\n    menuRouteType: child.menuRouteType,\r\n    has: child.has || child.permissions\r\n  }))\r\n  )\r\n  const result = arr.map(subArray =>\r\n    subArray.filter(item =>\r\n      item.name !== '系统运维' && item.name !== '我的'\r\n    )\r\n  ).filter(subArray => subArray.length > 0);\r\n  return result\r\n})\r\nconst handleWorkBench = (item) => { leftMenuData(item) }\r\nconst filterMenu = menuList => {\r\n  let newMenuList = []\r\n  for (let i = 0, len = menuList.length; i < len; i++) {\r\n    newMenuList.push({\r\n      id: menuList[i].menuId, name: menuList[i].name,\r\n      routePath: menuList[i].routePath,\r\n      menuFunction: menuList[i].menuFunction,\r\n      menuRouteType: menuList[i].menuRouteType,\r\n      icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,\r\n      has: menuList[i].permissions,\r\n      children: filterMenu(menuList[i].children || [])\r\n    })\r\n  }\r\n  return newMenuList\r\n}\r\n// 切换旧版本\r\nconst oldVersion = () => {\r\n  console.log('切换到旧版本')\r\n  openPage({ key: 'routePath', value: '/homePage' })\r\n}\r\n// 退出\r\nconst handleExit = () => {\r\n  ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { loginOut('已安全退出！') }).catch(() => { ElMessage({ type: 'info', message: '已取消退出' }) })\r\n}\r\nconst loginOut = async (text) => {\r\n  const { code } = await api.loginOut()\r\n  if (code === 200) {\r\n    sessionStorage.clear()\r\n    const goal_login_router_path = localStorage.getItem('goal_login_router_path')\r\n    if (goal_login_router_path) {\r\n      const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''\r\n      router.push({ path: goal_login_router_path, query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {} })\r\n    } else {\r\n      router.push({ path: '/LoginView' })\r\n    }\r\n    store.commit('setState')\r\n    globalReadOpenConfig()\r\n    ElMessage({ message: text, showClose: true, type: 'success' })\r\n  }\r\n}\r\n// 系统管理\r\nconst sysManagement = () => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '系统运维')\r\n  leftMenuData(systemOperation)\r\n}\r\n// 跳转到我的\r\nconst sysUser = () => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  const myOperation = filtered.flatMap(item => item.children).find(child => child.name === '我的')\r\n  leftMenuData(myOperation)\r\n}\r\n// 点击主菜单展开子级\r\nconst handleMenuClick = (index) => {\r\n  // if (index === 3) {\r\n  //   const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  //   const data = filtered.find(item => item.name === '数据中心')\r\n  //   leftMenuData(data)\r\n  //   return\r\n  // }\r\n  if (!subMenus.value[index]?.length) {\r\n    ElMessage.info('该菜单下暂无子菜单');\r\n    return;\r\n  }\r\n  activeMenuIndex.value = activeMenuIndex.value === index ? null : index\r\n}\r\nconst handleContentClick = (event) => {\r\n  // 如果点击的是菜单项或其子元素，不处理\r\n  if (event.target.closest('.menu-item') || event.target.closest('.module-area')) {\r\n    return;\r\n  }\r\n  // 点击空白区域时收起菜单\r\n  activeMenuIndex.value = null;\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.WorkBenchCopy {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  .background-layer {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: no-repeat;\r\n    background-size: 100% 100%;\r\n    transition: filter 0.3s ease;\r\n    z-index: 1;\r\n\r\n    &.blur-bg {\r\n      filter: blur(5px);\r\n      -webkit-filter: blur(5px);\r\n    }\r\n  }\r\n\r\n  .content-layer {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 2;\r\n  }\r\n\r\n  .WorkBenchHeader {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24px 40px 0 40px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    z-index: 2;\r\n    flex-shrink: 0;\r\n\r\n    .WorkBenchHeaderLogoName {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .WorkBenchHeaderLogo {\r\n        height: 74px;\r\n        width: 74px;\r\n      }\r\n\r\n      .WorkBenchHeaderName {\r\n        font-size: 37px;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        margin-left: 15px;\r\n        letter-spacing: 5px;\r\n      }\r\n    }\r\n\r\n    .WorkBenchHeaderRight {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 20px;\r\n\r\n      .old_version {\r\n        background: none;\r\n        border: none;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          height: 39px;\r\n        }\r\n\r\n        span {\r\n          position: absolute;\r\n          left: 0;\r\n          width: 100%;\r\n          text-align: center;\r\n          color: rgb(0, 51, 152);\r\n          font-size: 14px;\r\n          line-height: 39px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n\r\n      .WorkBenchHeaderLink {\r\n        color: #fff;\r\n        font-size: 16px;\r\n        cursor: pointer;\r\n        margin-right: 8px;\r\n\r\n        &:hover {\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n\r\n      .WorkBenchHeaderUser {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .WorkBenchHeaderAvatar {\r\n          width: 32px;\r\n          height: 32px;\r\n          border-radius: 50%;\r\n          margin: 0 8px;\r\n          object-fit: cover;\r\n          border: 2px solid #fff;\r\n          background: #eee;\r\n        }\r\n\r\n        .WorkBenchHeaderName {\r\n          color: #fff;\r\n          font-size: 16px;\r\n          margin-right: 8px;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n\r\n\r\n      .WorkBenchHeaderLogout {\r\n        color: #fff;\r\n        font-size: 16px;\r\n        cursor: pointer;\r\n        margin-left: 8px;\r\n\r\n        &:hover {\r\n          color: #ff4d4f;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .WorkBenchContent {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: flex-start;\r\n    padding-top: 8vh;\r\n    position: relative;\r\n    z-index: 2;\r\n    height: calc(100% - 98px);\r\n    overflow: hidden;\r\n\r\n    .menu-container {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      overflow: hidden;\r\n      max-height: 100%;\r\n    }\r\n\r\n    .menu-list {\r\n      width: calc(100% - 240px);\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: flex-start;\r\n      position: relative;\r\n      margin-top: 30vh;\r\n      transition: margin-top 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n      &.has-active {\r\n        margin-top: 0;\r\n\r\n        .menu-item {\r\n          max-width: 310px;\r\n          position: relative;\r\n\r\n          &::before {\r\n            content: '';\r\n            display: block;\r\n            padding-top: 90.3%;\r\n            /* 282/310 ≈ 0.903 */\r\n          }\r\n        }\r\n\r\n        .u-top {\r\n          margin-top: 0;\r\n        }\r\n\r\n        .u-bottom {\r\n          margin-top: 25px;\r\n        }\r\n\r\n        .menu-item-first {\r\n          margin-right: -40px;\r\n\r\n          .menu-title {\r\n            margin-top: 8%;\r\n            margin-left: 18px;\r\n          }\r\n        }\r\n\r\n        .menu-item-second {\r\n          margin-right: -55px;\r\n          position: relative;\r\n\r\n          &::before {\r\n            content: '';\r\n            display: block;\r\n            padding-top: 84.5%;\r\n            /* 262/310 ≈ 0.845 */\r\n          }\r\n\r\n          .menu-title {\r\n            margin-top: 5%;\r\n          }\r\n        }\r\n\r\n        .menu-item-third {\r\n          margin-right: -40px;\r\n          position: relative;\r\n\r\n          &::before {\r\n            content: '';\r\n            display: block;\r\n            padding-top: 84.5%;\r\n            /* 262/310 ≈ 0.845 */\r\n          }\r\n\r\n          .menu-title {\r\n            margin-top: 5%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .menu-item {\r\n      flex: 1 1 0;\r\n      max-width: 475px;\r\n      min-width: 220px;\r\n      width: 100%;\r\n      position: relative;\r\n      border-radius: 24px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      cursor: pointer;\r\n      transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s, max-width 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n      margin: 0 8px;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .menu-item::before {\r\n      content: '';\r\n      display: block;\r\n      padding-top: 94.7%;\r\n    }\r\n\r\n    .menu-item>.menu-title,\r\n    .menu-item>.menu-icon {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      z-index: 1;\r\n\r\n    }\r\n\r\n    .u-top {\r\n      margin-top: 0;\r\n    }\r\n\r\n    .u-bottom {\r\n      margin-top: 44px;\r\n    }\r\n\r\n    .menu-item-first {\r\n      margin-right: -40px;\r\n\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 20px;\r\n        font-weight: bold;\r\n        margin-top: 8%;\r\n        margin-left: 18px;\r\n      }\r\n    }\r\n\r\n    .menu-item-second {\r\n      margin-right: -55px;\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        display: block;\r\n        padding-top: 84.5%;\r\n        /* 262/310 ≈ 0.845 */\r\n      }\r\n\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 20px;\r\n        font-weight: bold;\r\n        margin-top: 4%;\r\n      }\r\n    }\r\n\r\n    .menu-item-third {\r\n      margin-right: -40px;\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        display: block;\r\n        padding-top: 84.5%;\r\n      }\r\n\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 20px;\r\n        font-weight: bold;\r\n        margin-top: 4%;\r\n      }\r\n    }\r\n\r\n    .menu-item-fourth {\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 20px;\r\n        font-weight: bold;\r\n        margin-top: 8%;\r\n        left: 42%;\r\n      }\r\n    }\r\n\r\n    .module-area {\r\n      width: 90%;\r\n      max-width: 1248px;\r\n      margin: 0 auto 0;\r\n      position: relative;\r\n      top: -70px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      overflow: hidden;\r\n      min-height: 500px;\r\n      opacity: 0;\r\n      animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;\r\n      height: auto;\r\n\r\n      @keyframes fadeIn {\r\n        from {\r\n          opacity: 0;\r\n        }\r\n\r\n        to {\r\n          opacity: 1;\r\n        }\r\n      }\r\n\r\n      .module-bg {\r\n        position: absolute;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: -1;\r\n        left: 0;\r\n        // top: -20px;\r\n        top: -30px;\r\n        background: url('../img/module_bg.png') no-repeat center top;\r\n        background-size: 100% auto;\r\n        border-radius: 24px;\r\n        pointer-events: none;\r\n      }\r\n\r\n      .module-list {\r\n        display: grid;\r\n        grid-template-columns: repeat(5, 1fr);\r\n        gap: 20px;\r\n        width: 100%;\r\n        // padding: 70px 40px 40px 40px;\r\n        padding: 45px 40px 40px 40px;\r\n        position: relative;\r\n        z-index: 1;\r\n\r\n        @media (max-width: 1400px) {\r\n          grid-template-columns: repeat(4, 1fr);\r\n        }\r\n\r\n        @media (max-width: 1100px) {\r\n          grid-template-columns: repeat(3, 1fr);\r\n        }\r\n      }\r\n\r\n      .module-item {\r\n        min-width: 180px;\r\n        background: url('../img/module_item_bg.png') no-repeat center/100% 100%;\r\n        border-radius: 16px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 15px 20px 0;\r\n        height: 82px;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n        cursor: pointer;\r\n\r\n        .module-icon {\r\n          width: 32px;\r\n          height: 32px;\r\n        }\r\n\r\n        .module-title {\r\n          color: #fff;\r\n          font-size: 16px;\r\n          font-weight: bold;\r\n          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);\r\n          margin-left: 15px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .fade-slide-enter-active,\r\n  .fade-slide-leave-active {\r\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  }\r\n\r\n  .fade-slide-enter-from,\r\n  .fade-slide-leave-to {\r\n    opacity: 0;\r\n    transform: translateY(40px);\r\n  }\r\n\r\n  // 图片弹窗样式\r\n  .image-dialog-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-color: rgba(0, 0, 0, 0.4);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: 100;\r\n    backdrop-filter: blur(3px);\r\n    -webkit-backdrop-filter: blur(3px);\r\n  }\r\n\r\n  .image-dialog-content {\r\n    position: relative;\r\n    max-width: 90%;\r\n    max-height: 90%;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .image-dialog-close {\r\n    position: absolute;\r\n    top: -40px;\r\n    right: -40px;\r\n    width: 32px;\r\n    height: 32px;\r\n    background-color: rgba(255, 255, 255, 0.9);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    z-index: 10000;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\r\n\r\n    &:hover {\r\n      background-color: #fff;\r\n      transform: scale(1.1);\r\n    }\r\n\r\n    .el-icon {\r\n      font-size: 18px;\r\n      color: #666;\r\n    }\r\n  }\r\n\r\n  .dialog-image {\r\n    width: 1200px;\r\n    border-radius: 8px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n    cursor: pointer;\r\n    transition: transform 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: scale(1.02);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAeiBA,UAA6B;;EAdvCC,KAAK,EAAC;AAAe;;EAIjBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAyB;iBAN5C;;EAUaA,KAAK,EAAC;AAAsB;iBAVzC;;EAqBkBA,KAAK,EAAC;AAAqB;;EAKlCA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAgB;kBA3BnC;;EAuCmBA,KAAK,EAAC;AAAY;;EAvCrCC,GAAA;EA2CiDD,KAAK,EAAC;;;EAEpCA,KAAK,EAAC;AAAa;kBA7CtC;kBAAA;;EAiDuBA,KAAK,EAAC;AAAc;;;;;uBAhDzCE,mBAAA,CAsEM,OAtENC,UAsEM,GArEJC,mBAAA,CACgE;IAD3DJ,KAAK,EAFdK,eAAA,EAEe,kBAAkB;MAAA,WAAsBC,MAAA,CAAAC,eAAe;IAAA;IAC/DC,KAAK,EAHZC,eAAA;MAAAC,eAAA,SAGwCJ,MAAA,CAAAI,eAAe;IAAA;kCACnDN,mBAAA,CAoDM;IApDDJ,KAAK,EAAC,eAAe;IAAEW,OAAK,EAAEL,MAAA,CAAAM;MACjCR,mBAAA,CAoBM,OApBNS,UAoBM,GAnBJT,mBAAA,CAGM,OAHNU,UAGM,GAFJC,YAAA,CAAwEC,mBAAA;IAA9DhB,KAAK,EAAC,qBAAqB;IAAEiB,GAAG,EAAEX,MAAA,CAAAY,UAAU;IAAEC,GAAG,EAAC;oCAC5Df,mBAAA,CAA2D;IAAtDJ,KAAK,EAAC,qBAAqB;IAACoB,SAAmB,EAAXd,MAAA,CAAAe;0BARnDC,UAAA,E,GAUQlB,mBAAA,CAcM,OAdNmB,UAcM,GAbJC,mBAAA,qDAAsD,EACtDT,YAAA,CACgEU,qBAAA;IAb1EC,UAAA,EAY+BpB,MAAA,CAAAqB,QAAQ;IAZvC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAY+BvB,MAAA,CAAAqB,QAAQ,GAAAE,MAAA;IAAA;IAAGC,IAAI,EAAExB,MAAA,CAAAyB,IAAI;IAAGC,QAAM,EAAE1B,MAAA,CAAA2B,YAAY;IAC9DC,KAAK,EAAE;MAAAC,KAAA;MAAAC,QAAA;IAAA;+DACVhC,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEW,OAAK,EAAEL,MAAA,CAAA+B;gCAClCjC,mBAAA,CAA4C;IAAvCa,GAA6B,EAA7BlB,UAA6B;IAACuC,GAAG,EAAC;8BACvClC,mBAAA,CAAgB,cAAV,KAAG,oB,IAEoDE,MAAA,CAAAiC,KAAK,I,cAApErC,mBAAA,CAAiF;IAlB3FD,GAAA;IAkBgBD,KAAK,EAAC,qBAAqB;IAAEW,OAAK,EAAEL,MAAA,CAAAkC;KAA4B,MAAI,KAlBpFhB,mBAAA,gBAmBUpB,mBAAA,CAGM;IAHDJ,KAAK,EAAC,qBAAqB;IAAEW,OAAK,EAAEL,MAAA,CAAAmC;MACvCrC,mBAAA,CAAgE;IAA3DJ,KAAK,EAAC,uBAAuB;IAAEiB,GAAG,EAAEX,MAAA,CAAAoC,IAAI,CAACC,KAAK;IAAEL,GAAG,EAAC;0BApBrEM,UAAA,GAqBYxC,mBAAA,CAA4D,QAA5DyC,UAA4D,EAAAC,gBAAA,CAAvBxC,MAAA,CAAAoC,IAAI,CAACK,QAAQ,iB,GAEpD3C,mBAAA,CAAqF;IAA/EJ,KAAK,EAAC,2CAA2C;IAAEW,OAAK,EAAEL,MAAA,CAAA0C;KAAY,IAAE,E,KAGlF5C,mBAAA,CA6BM,OA7BN6C,UA6BM,GA5BJ7C,mBAAA,CA2BM,OA3BN8C,UA2BM,GA1BJ9C,mBAAA,CAaM;IAbDJ,KAAK,EA5BpBK,eAAA,EA4BqB,WAAW;MAAA,cAAyBC,MAAA,CAAAC,eAAe;IAAA;yBAC5DL,mBAAA,CAWMiD,SAAA,QAxClBC,WAAA,CA6ByC9C,MAAA,CAAA+C,SAAS,EA7BlD,UA6ByBC,IAAI,EAAEC,KAAK;yBAAxBrD,mBAAA,CAWM;MAXmCD,GAAG,EAAEsD,KAAK;MAAEvD,KAAK,EA7BtEK,eAAA,EA6BuE,WAAW,G;gBAAoCC,MAAA,CAAAC,eAAe,KAAKgD;MAAK,GAAmBA,KAAK,iCAAgDA,KAAK,kCAAiDA,KAAK,iCAAgDA,KAAK,kCAAiDA,KAAK,UAAUA,KAAK,8B;MAO5X/C,KAAK,EApCrBC,eAAA;gCAoCgEH,MAAA,CAAAC,eAAe,KAAKgD,KAAK,GAAGD,IAAI,CAACE,QAAQ,GAAGF,IAAI,CAACG,EAAE;;MAEnG9C,OAAK,EAtCrB+C,cAAA,WAAA7B,MAAA;QAAA,OAsC4BvB,MAAA,CAAAqD,eAAe,CAACJ,KAAK;MAAA;QACnCnD,mBAAA,CAA8C,OAA9CwD,WAA8C,EAAAd,gBAAA,CAAnBQ,IAAI,CAACO,KAAK,iB,gCAvCnDC,WAAA;mDA0CU/C,YAAA,CAWagD,WAAA;IAXDC,IAAI,EAAC;EAAY;IA1CvCC,OAAA,EAAAC,QAAA,CAmEc;MAAA,OAwBA,CAhDS5D,MAAA,CAAAC,eAAe,a,cAA1BL,mBAAA,CASM,OATNiE,WASM,G,0BARJ/D,mBAAA,CAA6B;QAAxBJ,KAAK,EAAC;MAAW,6BACtBI,mBAAA,CAMM,OANNgE,WAMM,I,kBALJlE,mBAAA,CAIMiD,SAAA,QAlDtBC,WAAA,CA8C0C9C,MAAA,CAAA+D,QAAQ,CAAC/D,MAAA,CAAAC,eAAe,SA9ClE,UA8C6B+D,GAAG,EAAEC,GAAG;6BAArBrE,mBAAA,CAIM;UAJsDD,GAAG,EAAEqE,GAAG,CAACE,EAAE;UAAExE,KAAK,EAAC,aAAa;UACzFW,OAAK,WAALA,OAAKA,CAAAkB,MAAA;YAAA,OAAEvB,MAAA,CAAAmE,eAAe,CAACH,GAAG;UAAA;YAC3BlE,mBAAA,CAA2C;UAAtCJ,KAAK,EAAC,aAAa;UAAEiB,GAAG,EAAEqD,GAAG,CAACI;gCAhDrDC,WAAA,GAiDkBvE,mBAAA,CAA8C,OAA9CwE,WAA8C,EAAA9B,gBAAA,CAAjBwB,GAAG,CAACN,IAAI,iB,iBAjDvDa,WAAA;4CAAArD,mBAAA,e;;IAAAsD,CAAA;YA0DItD,mBAAA,UAAa,EACFlB,MAAA,CAAAyE,eAAe,I,cAA1B7E,mBAAA,CASM;IApEVD,GAAA;IA2DgCD,KAAK,EAAC,sBAAsB;IAAEW,OAAK,EAAEL,MAAA,CAAA0E;MAC/D5E,mBAAA,CAOM;IAPDJ,KAAK,EAAC,sBAAsB;IAAEW,OAAK,EAAAiB,MAAA,QAAAA,MAAA,MA5D9C8B,cAAA,CA4DwC,cAAW;MAC3CtD,mBAAA,CAIM;IAJDJ,KAAK,EAAC,oBAAoB;IAAEW,OAAK,EAAEL,MAAA,CAAA0E;MACtCjE,YAAA,CAEUkE,kBAAA;IAhEpBhB,OAAA,EAAAC,QAAA,CA+DY;MAAA,OAAS,CAATnD,YAAA,CAAST,MAAA,W;;IA/DrBwE,CAAA;QAkEQ/D,YAAA,CAA+FC,mBAAA;IAApFC,GAAG,EAAEX,MAAA,CAAA4E,cAAc;IAAE/D,GAAG,EAAC,SAAS;IAACnB,KAAK,EAAC,cAAc;IAAEW,OAAK,EAAEL,MAAA,CAAA6E;0CAlEnF3D,mBAAA,gBAqEIA,mBAAA,UAAa,EACbT,YAAA,CAAmFT,MAAA;IAtEvFoB,UAAA,EAsEmCpB,MAAA,CAAA8E,WAAW;IAtE9C,uBAAAxD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAsEmCvB,MAAA,CAAA8E,WAAW,GAAAvD,MAAA;IAAA;IAAG2C,EAAE,EAAElE,MAAA,CAAA+E,SAAS;IAAGC,UAAQ,EAAEC,IAAA,CAAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}