{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted, nextTick, defineAsyncComponent } from 'vue';\nimport { guid, svg } from '../../AiToolBox/AiToolBox.js';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'TextRecognition'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var GlobalAiChatScroll = defineAsyncComponent(function () {\n      return import('../../GlobalAiChat/GlobalAiChatScroll.vue');\n    });\n    var GlobalAiChatFile = defineAsyncComponent(function () {\n      return import('../../GlobalAiChat/GlobalAiChatFile.vue');\n    });\n    var GlobalAiChatEditor = defineAsyncComponent(function () {\n      return import('../../GlobalAiChat/GlobalAiChatEditor.vue');\n    });\n    var PreviewPdf = defineAsyncComponent(function () {\n      return import('@/components/global-file-preview/components/preview-pdf.vue');\n    });\n    var PreviewPic = defineAsyncComponent(function () {\n      return import('@/components/global-file-preview/components/preview-pic.vue');\n    });\n    var loading = ref(false);\n    var loadingText = ref('');\n    var file = ref({});\n    var fileProgress = ref(0);\n    var isShowProgress = ref(false);\n    var progressText = ref('审查意见的补充报告.docx');\n    var progressType = ref('docx');\n    var chatScrollRef = ref();\n    var editorRef = ref();\n    var chatId = ref('');\n    var fileData = ref([]);\n    var fileList = ref([]);\n    var sendContent = ref('');\n    var disabled = ref(false);\n    var sendMessageIndex = ref(0);\n    var fileIcon = function fileIcon(name) {\n      var type = name.substring(name.lastIndexOf('.') + 1) || '';\n      var IconClass = {\n        jpg: 'globalFilePicture',\n        png: 'globalFilePicture',\n        jpeg: 'globalFilePicture',\n        pdf: 'globalFilePDF'\n      };\n      return IconClass[type] || 'globalFileUnknown';\n    };\n    onMounted(function () {\n      chatId.value = guid();\n    });\n    var handleReset = function handleReset() {\n      var _editorRef$value;\n      chatId.value = guid();\n      file.value = {};\n      (_editorRef$value = editorRef.value) === null || _editorRef$value === void 0 || _editorRef$value.handleSetFile([]);\n      sendMessageIndex.value = 0;\n      nextTick(function () {\n        var _chatScrollRef$value;\n        (_chatScrollRef$value = chatScrollRef.value) === null || _chatScrollRef$value === void 0 || _chatScrollRef$value.handleNewChat();\n      });\n    };\n    var handleFileUpload = function handleFileUpload(data) {\n      fileList.value = data;\n    };\n    var handleFileCallback = function handleFileCallback(data) {\n      fileData.value = data;\n    };\n    var handleClose = function handleClose(item) {\n      var _editorRef$value2;\n      (_editorRef$value2 = editorRef.value) === null || _editorRef$value2 === void 0 || _editorRef$value2.handleSetFile(fileData.value.filter(function (v) {\n        return v.id !== item.id;\n      }));\n    };\n    var handleSendMessage = function handleSendMessage(value) {\n      var _chatScrollRef$value2;\n      if (!fileData.value.length && !sendMessageIndex.value) return ElMessage({\n        type: 'warning',\n        message: `请先上传相关资料!`\n      });\n      var fileId = fileData.value.map(function (v) {\n        return v.id;\n      }).join(',');\n      var params = {\n        question: value,\n        attachmentIds: fileId,\n        tool: '1960237803606405122'\n      };\n      (_chatScrollRef$value2 = chatScrollRef.value) === null || _chatScrollRef$value2 === void 0 || _chatScrollRef$value2.handleSendMessage(value, params);\n    };\n    var handlePromptWord = function handlePromptWord(data) {\n      handleSendMessage(data.promptWord);\n    };\n    var handleGuideWord = function handleGuideWord(data) {\n      var _chatScrollRef$value3;\n      (_chatScrollRef$value3 = chatScrollRef.value) === null || _chatScrollRef$value3 === void 0 || _chatScrollRef$value3.handleSendMessage(data.question, data);\n    };\n    var handleRetryMessage = function handleRetryMessage(data) {\n      fileData.value = data.fileData;\n      handleSendMessage(data.content);\n    };\n    var handleStopMessage = function handleStopMessage() {\n      var _chatScrollRef$value4;\n      (_chatScrollRef$value4 = chatScrollRef.value) === null || _chatScrollRef$value4 === void 0 || _chatScrollRef$value4.handleStopMessage();\n    };\n    var handleStreamingCallback = function handleStreamingCallback(data) {\n      disabled.value = data;\n    };\n    var handleSendMessageCallback = function handleSendMessageCallback() {\n      var _editorRef$value3, _editorRef$value4;\n      sendMessageIndex.value += 1;\n      (_editorRef$value3 = editorRef.value) === null || _editorRef$value3 === void 0 || _editorRef$value3.handleSetFile([]);\n      (_editorRef$value4 = editorRef.value) === null || _editorRef$value4 === void 0 || _editorRef$value4.handleSetContent('');\n    };\n\n    /**\r\n     * 限制上传附件的文件类型\r\n     */\n    var handleFile = function handleFile(file) {\n      var fileType = file.name.substring(file.name.lastIndexOf('.') + 1);\n      var isShow = ['jpg', 'jpeg', 'png', 'pdf'].includes(fileType);\n      if (!isShow) ElMessage({\n        type: 'warning',\n        message: `仅支持${['jpg', 'jpeg', 'png', 'pdf'].join('、')}格式!`\n      });\n      isShowProgress.value = true;\n      fileProgress.value = 0;\n      progressText.value = file.name;\n      progressType.value = fileType;\n      return isShow;\n    };\n    var onUploadProgress = function onUploadProgress(progressEvent) {\n      var _progressEvent$event;\n      if (progressEvent !== null && progressEvent !== void 0 && (_progressEvent$event = progressEvent.event) !== null && _progressEvent$event !== void 0 && _progressEvent$event.lengthComputable) {\n        var progress = (progressEvent.loaded / progressEvent.total * 100).toFixed(0);\n        fileProgress.value = parseInt(progress);\n      }\n    };\n    /**\r\n     * 上传附件请求方法\r\n     */\n    var fileUpload = function fileUpload(file) {\n      var param = new FormData();\n      param.append('file', file.file);\n      handleGlobalUpload(param);\n    };\n    var handleGlobalUpload = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(params) {\n        var _editorRef$value5, _yield$api$globalUplo, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _context.next = 3;\n              return api.globalUpload(params, onUploadProgress, guid());\n            case 3:\n              _yield$api$globalUplo = _context.sent;\n              data = _yield$api$globalUplo.data;\n              file.value = data;\n              (_editorRef$value5 = editorRef.value) === null || _editorRef$value5 === void 0 || _editorRef$value5.handleSetFile([data]);\n              loading.value = false;\n              isShowProgress.value = false;\n              _context.next = 15;\n              break;\n            case 11:\n              _context.prev = 11;\n              _context.t0 = _context[\"catch\"](0);\n              loading.value = false;\n              isShowProgress.value = false;\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 11]]);\n      }));\n      return function handleGlobalUpload(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      GlobalAiChatScroll,\n      GlobalAiChatFile,\n      GlobalAiChatEditor,\n      PreviewPdf,\n      PreviewPic,\n      loading,\n      loadingText,\n      file,\n      fileProgress,\n      isShowProgress,\n      progressText,\n      progressType,\n      chatScrollRef,\n      editorRef,\n      chatId,\n      fileData,\n      fileList,\n      sendContent,\n      disabled,\n      sendMessageIndex,\n      fileIcon,\n      handleReset,\n      handleFileUpload,\n      handleFileCallback,\n      handleClose,\n      handleSendMessage,\n      handlePromptWord,\n      handleGuideWord,\n      handleRetryMessage,\n      handleStopMessage,\n      handleStreamingCallback,\n      handleSendMessageCallback,\n      handleFile,\n      onUploadProgress,\n      fileUpload,\n      handleGlobalUpload,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      nextTick,\n      defineAsyncComponent,\n      get guid() {\n        return guid;\n      },\n      get svg() {\n        return svg;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "nextTick", "defineAsyncComponent", "guid", "svg", "ElMessage", "__default__", "GlobalAiChatScroll", "GlobalAiChatFile", "GlobalAiChatEditor", "PreviewPdf", "PreviewPic", "loading", "loadingText", "file", "fileProgress", "isShowProgress", "progressText", "progressType", "chatScrollRef", "editor<PERSON><PERSON>", "chatId", "fileData", "fileList", "send<PERSON><PERSON><PERSON>", "disabled", "sendMessageIndex", "fileIcon", "substring", "lastIndexOf", "IconClass", "jpg", "png", "jpeg", "pdf", "handleReset", "_editorRef$value", "handleSetFile", "_chatScrollRef$value", "handleNewChat", "handleFileUpload", "data", "handleFileCallback", "handleClose", "item", "_editorRef$value2", "filter", "id", "handleSendMessage", "_chatScrollRef$value2", "message", "fileId", "map", "join", "params", "question", "attachmentIds", "tool", "handlePromptWord", "promptWord", "handleGuideWord", "_chatScrollRef$value3", "handleRetryMessage", "content", "handleStopMessage", "_chatScrollRef$value4", "handleStreamingCallback", "handleSendMessageCallback", "_editorRef$value3", "_editorRef$value4", "handleSetContent", "handleFile", "fileType", "isShow", "includes", "onUploadProgress", "progressEvent", "_progressEvent$event", "event", "lengthComputable", "progress", "loaded", "total", "toFixed", "parseInt", "fileUpload", "param", "FormData", "append", "handleGlobalUpload", "_ref2", "_callee", "_editorRef$value5", "_yield$api$globalUplo", "_callee$", "_context", "globalUpload", "t0", "_x"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiToolBoxFunction/TextRecognition/TextRecognition.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"TextRecognition\"\r\n    v-loading=\"loading\"\r\n    :element-loading-spinner=\"svg\"\r\n    :lement-loading-text=\"loadingText\"\r\n    element-loading-svg-view-box=\"-10, -10, 50, 50\">\r\n    <div class=\"TextRecognitionHead\">\r\n      <div class=\"TextRecognitionButton\">\r\n        <div class=\"TextRecognitionButtonItem\" v-show=\"file.id\">\r\n          <el-button type=\"primary\" @click=\"handleReset\">重新上传</el-button>\r\n        </div>\r\n        <div class=\"TextRecognitionButtonItem\"></div>\r\n      </div>\r\n      <div class=\"TextRecognitionButton\">\r\n        <div class=\"TextRecognitionButtonItem\"></div>\r\n        <div class=\"TextRecognitionButtonItem\">\r\n          <el-button type=\"primary\">导出</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"TextRecognitionBody\">\r\n      <div class=\"TextRecognitionBodyLeft\">\r\n        <div class=\"TextRecognitionUploadBody\" v-show=\"!file.id\">\r\n          <div class=\"TextRecognitionUpload\">\r\n            <el-upload\r\n              drag\r\n              action=\"/\"\r\n              :before-upload=\"handleFile\"\r\n              :http-request=\"fileUpload\"\r\n              :show-file-list=\"false\"\r\n              multiple>\r\n              <el-icon class=\"zy-el-icon--upload\">\r\n                <upload-filled />\r\n              </el-icon>\r\n              <div class=\"zy-el-upload__text\">\r\n                将附件拖拽至此区域，或\r\n                <em>点击上传</em>\r\n              </div>\r\n              <div class=\"zy-el-upload__tip\">仅支持{{ ['jpg', 'jpeg', 'png', 'pdf'].join('、') }}格式</div>\r\n            </el-upload>\r\n            <div class=\"TextRecognitionUploadProgressBody\" @click.stop v-show=\"isShowProgress\">\r\n              <div class=\"TextRecognitionUploadProgressInfo\">\r\n                <div class=\"TextRecognitionUploadProgress\">\r\n                  <div class=\"globalFileIcon\" :class=\"fileIcon(progressType)\"></div>\r\n                  <div class=\"TextRecognitionUploadProgressBox\">\r\n                    <div class=\"TextRecognitionUploadProgressName ellipsis\">{{ progressText }}</div>\r\n                    <div class=\"TextRecognitionUploadProgressText\">正在解析</div>\r\n                  </div>\r\n                </div>\r\n                <el-progress :percentage=\"fileProgress\" :show-text=\"false\" :stroke-width=\"12\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"TextRecognitionWord\" v-if=\"file.id\">\r\n          <template v-if=\"['pdf', 'doc', 'docx', 'wps', 'ofd'].includes(fileType)\">\r\n            <preview-pdf :id=\"file.id\" :type=\"file.extName\" :name=\"file.newFileName\"></preview-pdf>\r\n          </template>\r\n          <template v-if=\"['png', 'jpg', 'jpeg', 'gif', 'tif'].includes(file.extName)\">\r\n            <preview-pic :id=\"file.id\" :type=\"file.extName\" :name=\"file.newFileName\"></preview-pic>\r\n          </template>\r\n        </div>\r\n      </div>\r\n      <div class=\"TextRecognitionBodyRight\">\r\n        <div class=\"TextRecognitionChatBody\">\r\n          <GlobalAiChatScroll\r\n            ref=\"chatScrollRef\"\r\n            AiChatCode=\"img_extract\"\r\n            :chatId=\"chatId\"\r\n            :fileData=\"fileData\"\r\n            @handlePromptWord=\"handlePromptWord\"\r\n            @handleGuideWord=\"handleGuideWord\"\r\n            @handleRetryMessage=\"handleRetryMessage\"\r\n            @handleStreamingCallback=\"handleStreamingCallback\"\r\n            @handleSendMessageCallback=\"handleSendMessageCallback\"></GlobalAiChatScroll>\r\n          <div class=\"TextRecognitionChatBodyEditor\">\r\n            <div class=\"TextRecognitionChatBodyEditorBody\">\r\n              <GlobalAiChatFile\r\n                :fileList=\"fileList\"\r\n                :fileData=\"fileData\"\r\n                @close=\"handleClose\"\r\n                v-show=\"fileList.length || fileData.length\" />\r\n              <GlobalAiChatEditor\r\n                ref=\"editorRef\"\r\n                v-model=\"sendContent\"\r\n                :disabled=\"disabled\"\r\n                @send=\"handleSendMessage\"\r\n                @stop=\"handleStopMessage\"\r\n                @uploadCallback=\"handleFileUpload\"\r\n                @fileCallback=\"handleFileCallback\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'TextRecognition' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick, defineAsyncComponent } from 'vue'\r\nimport { guid, svg } from '../../AiToolBox/AiToolBox.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst GlobalAiChatScroll = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatScroll.vue'))\r\nconst GlobalAiChatFile = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatFile.vue'))\r\nconst GlobalAiChatEditor = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatEditor.vue'))\r\nconst PreviewPdf = defineAsyncComponent(() => import('@/components/global-file-preview/components/preview-pdf.vue'))\r\nconst PreviewPic = defineAsyncComponent(() => import('@/components/global-file-preview/components/preview-pic.vue'))\r\n\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n\r\nconst file = ref({})\r\nconst fileProgress = ref(0)\r\nconst isShowProgress = ref(false)\r\nconst progressText = ref('审查意见的补充报告.docx')\r\nconst progressType = ref('docx')\r\n\r\nconst chatScrollRef = ref()\r\nconst editorRef = ref()\r\nconst chatId = ref('')\r\nconst fileData = ref([])\r\nconst fileList = ref([])\r\nconst sendContent = ref('')\r\nconst disabled = ref(false)\r\nconst sendMessageIndex = ref(0)\r\n\r\nconst fileIcon = (name) => {\r\n  const type = name.substring(name.lastIndexOf('.') + 1) || ''\r\n  const IconClass = {\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    jpeg: 'globalFilePicture',\r\n    pdf: 'globalFilePDF'\r\n  }\r\n  return IconClass[type] || 'globalFileUnknown'\r\n}\r\n\r\nonMounted(() => {\r\n  chatId.value = guid()\r\n})\r\n\r\nconst handleReset = () => {\r\n  chatId.value = guid()\r\n  file.value = {}\r\n  editorRef.value?.handleSetFile([])\r\n  sendMessageIndex.value = 0\r\n  nextTick(() => {\r\n    chatScrollRef.value?.handleNewChat()\r\n  })\r\n}\r\n\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!fileData.value.length && !sendMessageIndex.value)\r\n    return ElMessage({ type: 'warning', message: `请先上传相关资料!` })\r\n  const fileId = fileData.value.map((v) => v.id).join(',')\r\n  const params = { question: value, attachmentIds: fileId, tool: '1960237803606405122' }\r\n  chatScrollRef.value?.handleSendMessage(value, params)\r\n}\r\nconst handlePromptWord = (data) => {\r\n  handleSendMessage(data.promptWord)\r\n}\r\nconst handleGuideWord = (data) => {\r\n  chatScrollRef.value?.handleSendMessage(data.question, data)\r\n}\r\nconst handleRetryMessage = (data) => {\r\n  fileData.value = data.fileData\r\n  handleSendMessage(data.content)\r\n}\r\nconst handleStopMessage = () => {\r\n  chatScrollRef.value?.handleStopMessage()\r\n}\r\nconst handleStreamingCallback = (data) => {\r\n  disabled.value = data\r\n}\r\nconst handleSendMessageCallback = () => {\r\n  sendMessageIndex.value += 1\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n}\r\n\r\n/**\r\n * 限制上传附件的文件类型\r\n */\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  const isShow = ['jpg', 'jpeg', 'png', 'pdf'].includes(fileType)\r\n  if (!isShow) ElMessage({ type: 'warning', message: `仅支持${['jpg', 'jpeg', 'png', 'pdf'].join('、')}格式!` })\r\n  isShowProgress.value = true\r\n  fileProgress.value = 0\r\n  progressText.value = file.name\r\n  progressType.value = fileType\r\n  return isShow\r\n}\r\n\r\nconst onUploadProgress = (progressEvent) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    fileProgress.value = parseInt(progress)\r\n  }\r\n}\r\n/**\r\n * 上传附件请求方法\r\n */\r\nconst fileUpload = (file) => {\r\n  const param = new FormData()\r\n  param.append('file', file.file)\r\n  handleGlobalUpload(param)\r\n}\r\n\r\nconst handleGlobalUpload = async (params) => {\r\n  try {\r\n    const { data } = await api.globalUpload(params, onUploadProgress, guid())\r\n    file.value = data\r\n    editorRef.value?.handleSetFile([data])\r\n    loading.value = false\r\n    isShowProgress.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n    isShowProgress.value = false\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.TextRecognition {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .TextRecognitionHead {\r\n    width: 100%;\r\n    padding: var(--zy-distance-four) 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .TextRecognitionButton {\r\n      width: 796px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      & + .TextRecognitionButton {\r\n        width: calc(100% - 828px);\r\n      }\r\n      .TextRecognitionButtonItem {\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n  .TextRecognitionBody {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2)));\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: var(--zy-distance-four);\r\n    .TextRecognitionBodyLeft {\r\n      width: 812px;\r\n      height: 100%;\r\n      .TextRecognitionUploadBody {\r\n        width: 800px;\r\n        height: 100%;\r\n        background: #fff;\r\n        .globalFileIcon {\r\n          width: 32px;\r\n          height: 32px;\r\n          vertical-align: middle;\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 0;\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        .globalFileUnknown {\r\n          background: url('../../img/file_type/unknown.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePDF {\r\n          background: url('../../img/file_type/PDF.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePicture {\r\n          background: url('../../img/file_type/picture.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .TextRecognitionUpload {\r\n          width: 100%;\r\n          padding: var(--zy-distance-two);\r\n          position: relative;\r\n\r\n          .zy-el-upload {\r\n            --zy-el-upload-dragger-padding-horizontal: 20px;\r\n            --zy-el-upload-dragger-padding-vertical: 10px;\r\n            .zy-el-upload-dragger {\r\n              height: 220px;\r\n              display: flex;\r\n              align-items: center;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n            }\r\n            .zy-el-icon {\r\n              font-size: 99px;\r\n            }\r\n            .zy-el-upload__text {\r\n              line-height: var(--zy-line-height);\r\n            }\r\n\r\n            .zy-el-upload__tip {\r\n              padding: 0 var(--zy-distance-one);\r\n              line-height: var(--zy-line-height);\r\n            }\r\n          }\r\n\r\n          .TextRecognitionUploadProgressBody {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: var(--zy-distance-two) 0;\r\n            position: absolute;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            background: rgba(255, 255, 255, 0.8);\r\n\r\n            .TextRecognitionUploadProgressInfo {\r\n              width: 460px;\r\n              .TextRecognitionUploadProgress {\r\n                width: 100%;\r\n                padding: var(--zy-distance-five) 40px;\r\n                position: relative;\r\n                .TextRecognitionUploadProgressBox {\r\n                  width: 100%;\r\n                  .TextRecognitionUploadProgressName {\r\n                    font-size: var(--zy-name-font-size);\r\n                    line-height: var(--zy-line-height);\r\n                  }\r\n                  .TextRecognitionUploadProgressText {\r\n                    color: var(--zy-el-color-primary);\r\n                    font-size: var(--zy-text-font-size);\r\n                    line-height: var(--zy-line-height);\r\n                  }\r\n                }\r\n                .TextRecognitionUploadProgressClose {\r\n                  width: 40px;\r\n                  height: 40px;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  position: absolute;\r\n                  right: 0;\r\n                  bottom: calc(var(--zy-distance-five) / 2);\r\n                  cursor: pointer;\r\n                  .zy-el-icon {\r\n                    font-size: 26px;\r\n                    &:hover {\r\n                      color: var(--zy-el-color-danger);\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .TextRecognitionWord {\r\n        width: 100%;\r\n        height: 100%;\r\n        .vue-office-pdf-wrapper {\r\n          padding: 0 !important;\r\n          canvas {\r\n            left: 0 !important;\r\n            transform: none !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .TextRecognitionBodyRight {\r\n      width: calc(100% - 820px);\r\n      height: 100%;\r\n      .TextRecognitionChatBody {\r\n        width: 100%;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        background: #fff;\r\n        .GlobalAiChatBody {\r\n          padding-top: 12px;\r\n        }\r\n        .TextRecognitionChatBodyEditor {\r\n          width: 100%;\r\n          padding: 12px;\r\n          .GlobalAiChatFileItemClose {\r\n            display: none !important;\r\n          }\r\n          .TextRecognitionChatBodyEditorBody {\r\n            width: 100%;\r\n            background: #fff;\r\n            border-radius: 8px;\r\n            box-shadow: var(--zy-el-box-shadow);\r\n            border: 1px solid var(--zy-el-border-color-lighter);\r\n            .GlobalAiChatEditorUpload {\r\n              & > div {\r\n                display: none;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAwGA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,KAAK;AACpE,SAASC,IAAI,EAAEC,GAAG,QAAQ,8BAA8B;AACxD,SAASC,SAAS,QAAQ,cAAc;AAPxC,IAAAC,WAAA,GAAe;EAAEnC,IAAI,EAAE;AAAkB,CAAC;;;;;IAQ1C,IAAMoC,kBAAkB,GAAGL,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;IAAA,EAAC;IAC1G,IAAMM,gBAAgB,GAAGN,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,yCAAyC,CAAC;IAAA,EAAC;IACtG,IAAMO,kBAAkB,GAAGP,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;IAAA,EAAC;IAC1G,IAAMQ,UAAU,GAAGR,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,6DAA6D,CAAC;IAAA,EAAC;IACpH,IAAMS,UAAU,GAAGT,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,6DAA6D,CAAC;IAAA,EAAC;IAEpH,IAAMU,OAAO,GAAGb,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMc,WAAW,GAAGd,GAAG,CAAC,EAAE,CAAC;IAE3B,IAAMe,IAAI,GAAGf,GAAG,CAAC,CAAC,CAAC,CAAC;IACpB,IAAMgB,YAAY,GAAGhB,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAMiB,cAAc,GAAGjB,GAAG,CAAC,KAAK,CAAC;IACjC,IAAMkB,YAAY,GAAGlB,GAAG,CAAC,gBAAgB,CAAC;IAC1C,IAAMmB,YAAY,GAAGnB,GAAG,CAAC,MAAM,CAAC;IAEhC,IAAMoB,aAAa,GAAGpB,GAAG,CAAC,CAAC;IAC3B,IAAMqB,SAAS,GAAGrB,GAAG,CAAC,CAAC;IACvB,IAAMsB,MAAM,GAAGtB,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMuB,QAAQ,GAAGvB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMwB,QAAQ,GAAGxB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMyB,WAAW,GAAGzB,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAM0B,QAAQ,GAAG1B,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAM2B,gBAAgB,GAAG3B,GAAG,CAAC,CAAC,CAAC;IAE/B,IAAM4B,QAAQ,GAAG,SAAXA,QAAQA,CAAIxD,IAAI,EAAK;MACzB,IAAMtD,IAAI,GAAGsD,IAAI,CAACyD,SAAS,CAACzD,IAAI,CAAC0D,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;MAC5D,IAAMC,SAAS,GAAG;QAChBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,IAAI,EAAE,mBAAmB;QACzBC,GAAG,EAAE;MACP,CAAC;MACD,OAAOJ,SAAS,CAACjH,IAAI,CAAC,IAAI,mBAAmB;IAC/C,CAAC;IAEDmF,SAAS,CAAC,YAAM;MACdqB,MAAM,CAAC3H,KAAK,GAAGyG,IAAI,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,IAAMgC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MAAA,IAAAC,gBAAA;MACxBf,MAAM,CAAC3H,KAAK,GAAGyG,IAAI,CAAC,CAAC;MACrBW,IAAI,CAACpH,KAAK,GAAG,CAAC,CAAC;MACf,CAAA0I,gBAAA,GAAAhB,SAAS,CAAC1H,KAAK,cAAA0I,gBAAA,eAAfA,gBAAA,CAAiBC,aAAa,CAAC,EAAE,CAAC;MAClCX,gBAAgB,CAAChI,KAAK,GAAG,CAAC;MAC1BuG,QAAQ,CAAC,YAAM;QAAA,IAAAqC,oBAAA;QACb,CAAAA,oBAAA,GAAAnB,aAAa,CAACzH,KAAK,cAAA4I,oBAAA,eAAnBA,oBAAA,CAAqBC,aAAa,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC;IAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;MACjClB,QAAQ,CAAC7H,KAAK,GAAG+I,IAAI;IACvB,CAAC;IACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAID,IAAI,EAAK;MACnCnB,QAAQ,CAAC5H,KAAK,GAAG+I,IAAI;IACvB,CAAC;IACD,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAAA,IAAAC,iBAAA;MAC5B,CAAAA,iBAAA,GAAAzB,SAAS,CAAC1H,KAAK,cAAAmJ,iBAAA,eAAfA,iBAAA,CAAiBR,aAAa,CAACf,QAAQ,CAAC5H,KAAK,CAACoJ,MAAM,CAAC,UAACpH,CAAC;QAAA,OAAKA,CAAC,CAACqH,EAAE,KAAKH,IAAI,CAACG,EAAE;MAAA,EAAC,CAAC;IAChF,CAAC;IACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAItJ,KAAK,EAAK;MAAA,IAAAuJ,qBAAA;MACnC,IAAI,CAAC3B,QAAQ,CAAC5H,KAAK,CAACqE,MAAM,IAAI,CAAC2D,gBAAgB,CAAChI,KAAK,EACnD,OAAO2G,SAAS,CAAC;QAAExF,IAAI,EAAE,SAAS;QAAEqI,OAAO,EAAE;MAAY,CAAC,CAAC;MAC7D,IAAMC,MAAM,GAAG7B,QAAQ,CAAC5H,KAAK,CAAC0J,GAAG,CAAC,UAAC1H,CAAC;QAAA,OAAKA,CAAC,CAACqH,EAAE;MAAA,EAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACxD,IAAMC,MAAM,GAAG;QAAEC,QAAQ,EAAE7J,KAAK;QAAE8J,aAAa,EAAEL,MAAM;QAAEM,IAAI,EAAE;MAAsB,CAAC;MACtF,CAAAR,qBAAA,GAAA9B,aAAa,CAACzH,KAAK,cAAAuJ,qBAAA,eAAnBA,qBAAA,CAAqBD,iBAAiB,CAACtJ,KAAK,EAAE4J,MAAM,CAAC;IACvD,CAAC;IACD,IAAMI,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIjB,IAAI,EAAK;MACjCO,iBAAiB,CAACP,IAAI,CAACkB,UAAU,CAAC;IACpC,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAInB,IAAI,EAAK;MAAA,IAAAoB,qBAAA;MAChC,CAAAA,qBAAA,GAAA1C,aAAa,CAACzH,KAAK,cAAAmK,qBAAA,eAAnBA,qBAAA,CAAqBb,iBAAiB,CAACP,IAAI,CAACc,QAAQ,EAAEd,IAAI,CAAC;IAC7D,CAAC;IACD,IAAMqB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIrB,IAAI,EAAK;MACnCnB,QAAQ,CAAC5H,KAAK,GAAG+I,IAAI,CAACnB,QAAQ;MAC9B0B,iBAAiB,CAACP,IAAI,CAACsB,OAAO,CAAC;IACjC,CAAC;IACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAAA,IAAAC,qBAAA;MAC9B,CAAAA,qBAAA,GAAA9C,aAAa,CAACzH,KAAK,cAAAuK,qBAAA,eAAnBA,qBAAA,CAAqBD,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IACD,IAAME,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIzB,IAAI,EAAK;MACxChB,QAAQ,CAAC/H,KAAK,GAAG+I,IAAI;IACvB,CAAC;IACD,IAAM0B,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAA,EAAS;MAAA,IAAAC,iBAAA,EAAAC,iBAAA;MACtC3C,gBAAgB,CAAChI,KAAK,IAAI,CAAC;MAC3B,CAAA0K,iBAAA,GAAAhD,SAAS,CAAC1H,KAAK,cAAA0K,iBAAA,eAAfA,iBAAA,CAAiB/B,aAAa,CAAC,EAAE,CAAC;MAClC,CAAAgC,iBAAA,GAAAjD,SAAS,CAAC1H,KAAK,cAAA2K,iBAAA,eAAfA,iBAAA,CAAiBC,gBAAgB,CAAC,EAAE,CAAC;IACvC,CAAC;;IAED;AACA;AACA;IACA,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIzD,IAAI,EAAK;MAC3B,IAAM0D,QAAQ,GAAG1D,IAAI,CAAC3C,IAAI,CAACyD,SAAS,CAACd,IAAI,CAAC3C,IAAI,CAAC0D,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACpE,IAAM4C,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACF,QAAQ,CAAC;MAC/D,IAAI,CAACC,MAAM,EAAEpE,SAAS,CAAC;QAAExF,IAAI,EAAE,SAAS;QAAEqI,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;MAAM,CAAC,CAAC;MACxGrC,cAAc,CAACtH,KAAK,GAAG,IAAI;MAC3BqH,YAAY,CAACrH,KAAK,GAAG,CAAC;MACtBuH,YAAY,CAACvH,KAAK,GAAGoH,IAAI,CAAC3C,IAAI;MAC9B+C,YAAY,CAACxH,KAAK,GAAG8K,QAAQ;MAC7B,OAAOC,MAAM;IACf,CAAC;IAED,IAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,aAAa,EAAK;MAAA,IAAAC,oBAAA;MAC1C,IAAID,aAAa,aAAbA,aAAa,gBAAAC,oBAAA,GAAbD,aAAa,CAAEE,KAAK,cAAAD,oBAAA,eAApBA,oBAAA,CAAsBE,gBAAgB,EAAE;QAC1C,IAAMC,QAAQ,GAAG,CAAEJ,aAAa,CAACK,MAAM,GAAGL,aAAa,CAACM,KAAK,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;QAChFpE,YAAY,CAACrH,KAAK,GAAG0L,QAAQ,CAACJ,QAAQ,CAAC;MACzC;IACF,CAAC;IACD;AACA;AACA;IACA,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAIvE,IAAI,EAAK;MAC3B,IAAMwE,KAAK,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC5BD,KAAK,CAACE,MAAM,CAAC,MAAM,EAAE1E,IAAI,CAACA,IAAI,CAAC;MAC/B2E,kBAAkB,CAACH,KAAK,CAAC;IAC3B,CAAC;IAED,IAAMG,kBAAkB;MAAA,IAAAC,KAAA,GAAAjG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAuH,QAAOrC,MAAM;QAAA,IAAAsC,iBAAA,EAAAC,qBAAA,EAAApD,IAAA;QAAA,OAAAzJ,mBAAA,GAAAuB,IAAA,UAAAuL,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAlH,IAAA,GAAAkH,QAAA,CAAA7I,IAAA;YAAA;cAAA6I,QAAA,CAAAlH,IAAA;cAAAkH,QAAA,CAAA7I,IAAA;cAAA,OAEb4C,GAAG,CAACkG,YAAY,CAAC1C,MAAM,EAAEqB,gBAAgB,EAAExE,IAAI,CAAC,CAAC,CAAC;YAAA;cAAA0F,qBAAA,GAAAE,QAAA,CAAApJ,IAAA;cAAjE8F,IAAI,GAAAoD,qBAAA,CAAJpD,IAAI;cACZ3B,IAAI,CAACpH,KAAK,GAAG+I,IAAI;cACjB,CAAAmD,iBAAA,GAAAxE,SAAS,CAAC1H,KAAK,cAAAkM,iBAAA,eAAfA,iBAAA,CAAiBvD,aAAa,CAAC,CAACI,IAAI,CAAC,CAAC;cACtC7B,OAAO,CAAClH,KAAK,GAAG,KAAK;cACrBsH,cAAc,CAACtH,KAAK,GAAG,KAAK;cAAAqM,QAAA,CAAA7I,IAAA;cAAA;YAAA;cAAA6I,QAAA,CAAAlH,IAAA;cAAAkH,QAAA,CAAAE,EAAA,GAAAF,QAAA;cAE5BnF,OAAO,CAAClH,KAAK,GAAG,KAAK;cACrBsH,cAAc,CAACtH,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAqM,QAAA,CAAA/G,IAAA;UAAA;QAAA,GAAA2G,OAAA;MAAA,CAE/B;MAAA,gBAXKF,kBAAkBA,CAAAS,EAAA;QAAA,OAAAR,KAAA,CAAA/F,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}