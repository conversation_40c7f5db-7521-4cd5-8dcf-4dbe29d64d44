<template>
  <div class="SuggestDetail">
    <anchor-location v-model="activeValue">
      <template #function>
        <div class="detailsPrint" title="打印提案" @click="handleSuggestPrint">打印提案</div>
        <div class="detailsExportInfo" title="导出提案信息" @click="handleExportWord">导出提案信息</div>
      </template>
      <div class="SuggestDetailProcessInfo">
        <div class="SuggestLabelName">
          提案流程
          <span class="SuggestDetailBodyActive" @click="isProcessActive = !isProcessActive"
            :class="{ 'is-ctive': isProcessActive }">
            {{ isProcessActive ? '收起' : '展开' }}
            <el-icon>
              <ArrowUp />
            </el-icon>
          </span>
        </div>
        <template v-if="!isPreAssign">
          <transition name="el-zoom-in-top">
            <div class="SuggestDetailProcessNodeBody" v-show="isProcessActive">
              <div class="SuggestDetailProcessNodeMain">
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNode">提交</div>
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('submitSuggestion') }">
                    1
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNode">审查</div>
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('prepareVerify') }">
                    2
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNode">交办</div>
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('prepareSubmitHandle') }">
                    3
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNode">办理</div>
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('suggestionHandling') }">
                    4
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNode">答复</div>
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') }">
                    5
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNode">满意度测评</div>
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') }">
                    6
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNode">办结</div>
                  <div class="SuggestDetailProcessNodeIcon" :class="{
                    'is-active': hasExecuteNodeIds.includes('handleOver'),
                    'is-active-other': !isSatisfaction
                  }">
                    8
                  </div>
                </div>
              </div>
              <div class="SuggestDetailProcessNodeAssist">
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon" :class="{
                    'is-active':
                      hasExecuteNodeIds.includes('exchangeLetter') ||
                      hasExecuteNodeIds.includes('exchangeSocial') ||
                      hasExecuteNodeIds.includes('rejectReceive') ||
                      hasExecuteNodeIds.includes('cancelSuggestion') ||
                      hasExecuteNodeIds.includes('returnSubmit')
                  }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('exchangeLetter') }">
                    3
                    <div class="SuggestDetailProcessNode">转来信</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') && !isSatisfaction }">
                    7
                    <div class="SuggestDetailProcessNode">不满意</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('handleOver') && !isSatisfaction }"></div>
                </div>
              </div>
              <div class="SuggestDetailProcessNodeAssist">
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon" :class="{
                    'is-active':
                      hasExecuteNodeIds.includes('exchangeSocial') ||
                      hasExecuteNodeIds.includes('rejectReceive') ||
                      hasExecuteNodeIds.includes('cancelSuggestion') ||
                      hasExecuteNodeIds.includes('cancelSuggestion') ||
                      hasExecuteNodeIds.includes('returnSubmit')
                  }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('exchangeSocial') }">
                    3
                    <div class="SuggestDetailProcessNode">转社情民意</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
              </div>
              <div class="SuggestDetailProcessNodeAssist">
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon" :class="{
                    'is-active':
                      hasExecuteNodeIds.includes('rejectReceive') ||
                      hasExecuteNodeIds.includes('cancelSuggestion') ||
                      hasExecuteNodeIds.includes('returnSubmit')
                  }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('rejectReceive') }">
                    3
                    <div class="SuggestDetailProcessNode">不予立案</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
              </div>
              <div class="SuggestDetailProcessNodeAssist">
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon" :class="{
                    'is-active':
                      hasExecuteNodeIds.includes('cancelSuggestion') || hasExecuteNodeIds.includes('returnSubmit')
                  }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('cancelSuggestion') }">
                    3
                    <div class="SuggestDetailProcessNode">撤案</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
              </div>
              <div class="SuggestDetailProcessNodeAssist">
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('returnSubmit') }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfo">
                  <div class="SuggestDetailProcessNodeIcon"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('returnSubmit') }">
                    3
                    <div class="SuggestDetailProcessNode">退回</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
                <div class="SuggestDetailProcessNodeInfo"></div>
              </div>
            </div>
          </transition>
        </template>
        <!-- 建议流程图(包含预交办) -->
        <template v-if="isPreAssign">
          <transition name="el-zoom-in-top">
            <div class="SuggestDetailProcessNodeBodyThree" v-show="isProcessActive">
              <div class="SuggestDetailProcessNodeMainThree">
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeThree">提交</div>
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('submitSuggestion') }">
                    1
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeThree">审查</div>
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('prepareVerify') }">
                    2
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeThree">交办</div>
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('prepareSubmitHandle') }">
                    3
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeThree">预交办</div>
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('preAssign') }">
                    4
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeThree">办理</div>
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('suggestionHandling') }">
                    5
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeThree">答复</div>
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') }">
                    6
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeThree">满意度测评</div>
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') }">
                    7
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeThree">办结</div>
                  <div class="SuggestDetailProcessNodeIconThree" :class="{
                    'is-active': hasExecuteNodeIds.includes('handleOver'),
                    'is-active-other': !isSatisfaction
                  }">
                    9
                  </div>
                </div>
              </div>
              <div class="SuggestDetailProcessNodeAssistThree">
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree" :class="{
                    'is-active':
                      hasExecuteNodeIds.includes('exchangeLetter') ||
                      hasExecuteNodeIds.includes('exchangeSocial') ||
                      hasExecuteNodeIds.includes('rejectReceive') ||
                      hasExecuteNodeIds.includes('cancelSuggestion') ||
                      hasExecuteNodeIds.includes('returnSubmit')
                  }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('exchangeLetter') }">
                    3
                    <div class="SuggestDetailProcessNodeThree">转来信</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') && !isSatisfaction }">
                    8
                    <div class="SuggestDetailProcessNodeThree">不满意</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('handleOver') && !isSatisfaction }"></div>
                </div>
              </div>
              <div class="SuggestDetailProcessNodeAssistThree">
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree" :class="{
                    'is-active':
                      hasExecuteNodeIds.includes('exchangeSocial') ||
                      hasExecuteNodeIds.includes('rejectReceive') ||
                      hasExecuteNodeIds.includes('cancelSuggestion') ||
                      hasExecuteNodeIds.includes('cancelSuggestion') ||
                      hasExecuteNodeIds.includes('returnSubmit')
                  }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('exchangeSocial') }">
                    3
                    <div class="SuggestDetailProcessNodeThree">转社情民意</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
              </div>
              <div class="SuggestDetailProcessNodeAssistThree">
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree" :class="{
                    'is-active':
                      hasExecuteNodeIds.includes('rejectReceive') ||
                      hasExecuteNodeIds.includes('cancelSuggestion') ||
                      hasExecuteNodeIds.includes('returnSubmit')
                  }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('rejectReceive') }">
                    3
                    <div class="SuggestDetailProcessNodeThree">不予接收</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
              </div>
              <div class="SuggestDetailProcessNodeAssistThree">
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree" :class="{
                    'is-active':
                      hasExecuteNodeIds.includes('cancelSuggestion') || hasExecuteNodeIds.includes('returnSubmit')
                  }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('cancelSuggestion') }">
                    3
                    <div class="SuggestDetailProcessNodeThree">撤案</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
              </div>
              <div class="SuggestDetailProcessNodeAssistThree">
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('returnSubmit') }"></div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree">
                  <div class="SuggestDetailProcessNodeIconThree"
                    :class="{ 'is-active': hasExecuteNodeIds.includes('returnSubmit') }">
                    3
                    <div class="SuggestDetailProcessNodeThree">退回</div>
                  </div>
                </div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
                <div class="SuggestDetailProcessNodeInfoThree"></div>
              </div>
            </div>
          </transition>
        </template>
      </div>
      <anchor-location-item v-if="reviewList.length" value="aaaa" label="提案审查信息">
        <div class="SuggestLabelName">提案审查信息</div>
        <global-info v-for="item in reviewList" :key="item.id">
          <global-info-item label="审查结果">{{ item.nodeResult }}</global-info-item>
          <!-- <global-info-line>
            <global-info-item label="审查类别">{{ item.nodeName }}</global-info-item>
            <global-info-item label="审查结果">{{ item.nodeResult }}</global-info-item>
          </global-info-line> -->
          <global-info-line>
            <global-info-item label="审查者">{{ item.UserName }}</global-info-item>
            <global-info-item label="审查时间">{{ format(item.handleTime) }}</global-info-item>
          </global-info-line>
          <global-info-item v-if="item.isSpareDict" :label="`${item.nodeResult}理由`">
            {{ item.spareDict?.label }}
          </global-info-item>
          <global-info-item label="审查意见">{{ item.handleContent }}</global-info-item>
        </global-info>
      </anchor-location-item>
      <anchor-location-item v-if="assignList.length" value="bbbb" label="提案交办信息">
        <div class="SuggestLabelName">提案交办信息</div>
        <global-info v-for="(item, index) in assignList" :key="item.id">
          <global-info-item label="交办结果">{{ item.nodeResult }}</global-info-item>
          <!-- <global-info-line>
            <global-info-item label="交办类别">{{ item.nodeName }}</global-info-item>
            <global-info-item label="交办结果">{{ item.nodeResult }}</global-info-item>
          </global-info-line> -->
          <global-info-line>
            <global-info-item label="交办者">{{ item.UserName }}</global-info-item>
            <global-info-item label="交办时间">{{ format(item.handleTime) }}</global-info-item>
          </global-info-line>
          <global-info-item label="交办意见">{{ item.handleContent }}</global-info-item>
          <global-info-item v-if="
            (index === assignList.length - 1 && !handlingMassing?.answerStopDate) ||
            (index === assignList.length - 2 && handlingMassing?.confirmStopDate)
          " label="签收截止时间">
            {{ format(handlingMassing.confirmStopDate) }}
          </global-info-item>
          <global-info-item v-if="index === assignList.length - 1 && handlingMassing?.answerStopDate" label="答复截止时间">
            {{ format(handlingMassing.answerStopDate) }}
          </global-info-item>
        </global-info>
      </anchor-location-item>
      <anchor-location-item v-if="hasSuperviseInfo" value="oooo" label="重点督办">
        <div class="SuggestLabelName">重点督办</div>
        <global-info>
          <global-info-item :label="superviseInfo.superviseLeaderName ? '督办领导' : '督办单位'">
            {{ superviseInfo.superviseGroupName || superviseInfo.superviseLeaderName }}
          </global-info-item>
          <global-info-line>
            <global-info-item label="牵头督办单位">
              {{ superviseInfo.superviseFirstGroupName || superviseInfo.superviseFirstGroup }}
            </global-info-item>
            <global-info-item label="其他协助督办单位">
              {{ superviseInfo.superviseOtherGroupName || superviseInfo.superviseOtherGroup }}
            </global-info-item>
          </global-info-line>
          <global-info-item label="督办意见">{{ superviseInfo.opinion }}</global-info-item>
          <global-info-item label="相关文件">
            <xyl-global-file :fileData="superviseInfo.attachments"></xyl-global-file>
          </global-info-item>
        </global-info>
      </anchor-location-item>
      <anchor-location-item v-if="transactUnit.length" value="cccc" label="提案办理单位">
        <div class="SuggestLabelName">
          提案办理单位
          <div class="SuggestLabelNameButton">
            <el-button @click="isAdjustRecords = !isAdjustRecords"
              v-if="!['unit', 'unitTrackTransact', 'unitConclude', 'unitPreAssign'].includes(route.query.type)"
              type="primary">
              办理单位调整记录
            </el-button>
            <el-button @click="isAdjustResult = !isAdjustResult" type="primary">办理单位调整结果</el-button>
          </div>
        </div>
        <global-info v-for="item in transactUnit" :key="item.id">
          <global-info-line>
            <global-info-item label="办理单位">{{ item.unitName }}</global-info-item>
            <global-info-item label="办理类型">{{ item.unitType }}</global-info-item>
          </global-info-line>
          <global-info-line v-if="isPreAssign">
            <global-info-item label="是否签收">
              <div class="SuggestSign" v-if="item.hasConfirm">已签收</div>
              <div class="SuggestUnSign" v-if="!item.hasConfirm">待签收</div>
            </global-info-item>
            <global-info-item label="签收时间">{{ format(item.confirmTime) }}</global-info-item>
          </global-info-line>
          <global-info-line>
            <global-info-item label="是否阅读">
              <div class="SuggestRead" v-if="item.hasRead">已阅读 {{ format(item.firstReadTime) }}</div>
              <div class="SuggestUnRead" v-if="!item.hasRead">未阅读</div>
            </global-info-item>
            <global-info-item label="办理状态">
              <div class="SuggestReviewUnit">
                <span :style="colorObj(item.status)">{{ item.statusName }}</span>
                <el-link v-if="item.isDelays && route.query.type === 'postpone'" @click="handlePostpone(item)"
                  type="primary">
                  申请延期审查
                </el-link>
              </div>
            </global-info-item>
          </global-info-line>
          <global-info-line>
            <global-info-item label="答复类型">{{ item.answers?.suggestionAnswerType?.label }}</global-info-item>
            <global-info-item label="答复时间">{{ format(item.answers?.answerDate) }}</global-info-item>
          </global-info-line>
          <global-info-item label="答复意见" v-if="item.status === 'has_answer'">
            <el-link @click="handleReply(item.answers)" type="primary">查看{{ item.unitName }}的答复信息</el-link>
          </global-info-item>
        </global-info>
        <!-- 主办+协办的情况 -->
        <global-info>
          <global-info-item label="协办单位">{{maincoOrganizers.map(b =>
            b.flowHandleOfficeName).join('、')}}</global-info-item>
        </global-info>
        <!-- 协办+分办的情况 -->
        <global-info v-if="coOrganizer">
          <global-info-item label="协办单位">{{ coOrganizer }}</global-info-item>
        </global-info>
      </anchor-location-item>
      <anchor-location-item v-if="delaysList.length && route.query.type === 'postpone'" value="gggg" label="申请延期记录">
        <div class="SuggestLabelName">申请延期记录</div>
        <template v-for="item in delaysList" :key="item.id">
          <!-- <div class="SuggestLabelNameButton">
            <el-button v-if="item.delays.length > 1" @click="handleLook(item)" type="primary">
              查看更多延期记录
            </el-button>
          </div> -->
          <global-info v-for="(child, index) in item.delays" :key="child.id" v-show="index === item.delays.length - 1">
            <global-info-line>
              <global-info-item label="申请单位">{{ item?.flowHandleOfficeName }}</global-info-item>
              <global-info-item label="申请时间">
                <div style="width: 100%; display: flex; align-items: center; justify-content: space-between">
                  <div style="margin-right: 20px">{{ format(child.createDate) }}</div>
                  <el-button link text v-if="item.delays.length > 1" @click="handleLook(item)" type="primary">
                    查看更多记录
                  </el-button>
                </div>
              </global-info-item>
            </global-info-line>
            <global-info-line>
              <global-info-item label="答复截止时间">{{ format(child.lastAnswerAdjustDate) }}</global-info-item>
              <global-info-item label="申请答复截止时间">{{ format(child.lastApplyAdjustDate) }}</global-info-item>
            </global-info-line>
            <global-info-item label="申请延期理由">
              <pre>{{ child.delayReason }}</pre>
            </global-info-item>
            <global-info-item label="是否同意延期申请">
              {{ child.verifyStatus ? (child.verifyStatus === 1 ? '同意申请' : '驳回') : '待审查' }}
            </global-info-item>
            <global-info-item v-if="child.verifyStatus === 2" label="驳回理由">
              <pre>{{ child.noPassReason }}</pre>
            </global-info-item>
          </global-info>
        </template>
      </anchor-location-item>
      <anchor-location-item v-if="
        handlingMassing?.answerStopDate && !['unit', 'unitTrackTransact', 'unitConclude'].includes(route.query.type)
      " value="dddd" label="提案办理信息">
        <div class="SuggestLabelName">
          提案办理及答复信息
          <div class="SuggestLabelNameButton">
            <el-button @click="isTrackTransact = !isTrackTransact" type="primary">跟踪办理申请记录</el-button>
          </div>
        </div>
        <global-info>
          <global-info-item label="答复时间">{{ format(handlingMassing.massingAnswerDate) }}</global-info-item>
          <global-info-item label="沟通情况">
            <el-link @click="show = !show" type="primary">查看办理单位与委员沟通情况</el-link>
          </global-info-item>
          <global-info-item label="答复意见">
            <div v-for="item in replyList" :key="item.id">
              <el-link @click="handleReply(item)" type="primary">
                查看{{ item.handleOfficeName }}的答复信息
                {{ format(item.answerDate) }}
                <span v-if="item.submitAnswerType === 'history'">（历史答复）</span>
                <span v-if="item.submitAnswerType === 'trace'">（跟踪办理答复）</span>
                <span v-if="item.submitAnswerType === 'direct'">（最终答复）</span>
                {{ item.suggestionAnswerType?.label }}
              </el-link>
            </div>
          </global-info-item>
          <global-info-item label="满意度测评">
            <div v-if="!satisfactions.length">
              <el-link @click="handleSatisfactions({ id: '' })" type="primary">查看满意度测评</el-link>
            </div>
            <div v-for="item in satisfactions" :key="item.id">
              <el-link @click="handleSatisfactions(item)" type="primary">
                {{ item.handleResultName }}{{ item.isHistoryTest ? '（历史测评）' : '（最终测评）' }}
              </el-link>
            </div>
          </global-info-item>
        </global-info>
      </anchor-location-item>
      <anchor-location-item v-if="isPersonal && replyList.length" value="zzzz" label="提案办理信息">
        <div class="SuggestLabelName">提案办理及答复信息</div>
        <global-info>
          <global-info-item label="答复意见">
            <div v-for="item in replyList" :key="item.id">
              <el-link @click="handleReply(item)" type="primary">
                查看{{ item.handleOfficeName }}的答复信息
                {{ format(item.answerDate) }}
                <span v-if="item.submitAnswerType === 'history'">（历史答复）</span>
                <span v-if="item.submitAnswerType === 'trace'">（跟踪办理答复）</span>
                <span v-if="item.submitAnswerType === 'direct'">（最终答复）</span>
                {{ item.suggestionAnswerType?.label }}
              </el-link>
            </div>
          </global-info-item>
        </global-info>
      </anchor-location-item>
      <anchor-location-item v-if="details.isMergeProposal && !isPersonal" value="eeee" label="提案并案信息">
        <div class="SuggestLabelName">{{ details.isMainMergeProposal ? '并入提案信息' : '主并提案信息' }}</div>
        <div class="SuggestDetailTable" v-if="details.mergeProposals?.length">
          <div class="SuggestDetailTableHead">
            <div class="SuggestDetailTableItem row2">流水号</div>
            <div class="SuggestDetailTableItem row2">案号</div>
            <div class="SuggestDetailTableItem row5">案题</div>
            <div class="SuggestDetailTableItem row2">提案者</div>
            <div class="SuggestDetailTableItem row3">提交时间</div>
          </div>
          <div class="SuggestDetailTableBody" v-for="item in details.mergeProposals" :key="item.proposalId">
            <div class="SuggestDetailTableItem row2">{{ item.streamNumber }}</div>
            <div class="SuggestDetailTableItem row2">{{ item.serialNumber }}</div>
            <div class="SuggestDetailTableItem row5">
              <el-link @click="handleDetails(item)" type="primary">{{ item.title }}</el-link>
            </div>
            <div class="SuggestDetailTableItem row2">{{ item.suggestionUserName }}</div>
            <div class="SuggestDetailTableItem row3">{{ format(item.createDate) }}</div>
          </div>
        </div>
      </anchor-location-item>
      <!-- 交办 -->
      <keep-alive>
        <SuggestAssignDetail :id="route.query.id" :details="details" :transactObj="transactObj"
          :coOrganizerIds="coOrganizerIds" :name="route.query.moduleName" :isPreAssign="isPreAssign"
          v-if="route.query.type === 'assign'" @callback="closeCallback"></SuggestAssignDetail>
      </keep-alive>
      <!-- 申请调整审查 -->
      <keep-alive>
        <SuggestAdjustReview :id="route.query.id" :transactObj="transactObj" v-if="route.query.type === 'adjust'"
          @callback="closeCallback"></SuggestAdjustReview>
      </keep-alive>
      <!-- 跟踪办理审查 -->
      <keep-alive>
        <SuggestTrackTransactDetail :id="route.query.id" :transactUnitObj="transactUnitObj"
          v-if="route.query.type === 'trackTransact'" @refresh="refreshCallback" @callback="closeCallback">
        </SuggestTrackTransactDetail>
      </keep-alive>
      <keep-alive>
        <UnitSuggestDetail :id="route.query.id" :type="route.query.type" :details="details"
          :allhandleOfficeInfos="allhandleOfficeInfos" :transactUnitObj="transactUnitObj" :satisfactions="satisfactions"
          :suggestionOfficeShow="suggestionOfficeShow"
          v-if="['unit', 'unitTrackTransact', 'unitConclude', 'unitPreAssign'].includes(route.query.type)"
          @refresh="refreshCallback" @callback="closeCallback"></UnitSuggestDetail>
      </keep-alive>
      <div v-if="route.query.type === 'reply'" class="SuggestReplyButton">
        <el-button :disabled="!satisfactions.map((v) => !v.isHistoryTest).length" @click="handleConclude"
          type="primary">
          办结
        </el-button>
        <el-button @click="anewTransact" type="primary">重新办理</el-button>
        <el-button @click="closeCallback">取消</el-button>
      </div>
      <!-- v-show="reviewList.length || assignList.length || transactUnit.length || details.isMergeProposal" -->
      <div class="SuggestSegmentation"></div>
      <anchor-location-item value="ffff" label="提案基本信息">
        <!-- 提案基本信息 -->
        <keep-alive>
          <SuggestBasicInfo :id="route.query.id" :details="details"></SuggestBasicInfo>
        </keep-alive>
      </anchor-location-item>
    </anchor-location>
    <suggestPrint v-if="elPrintWhetherShow" :params="printParams" @callback="callback"></suggestPrint>
  </div>
  <xyl-popup-window v-model="postponeShow" name="延期审查">
    <SuggestPostponeReview :id="unitRecordsId" @callback="callback"></SuggestPostponeReview>
  </xyl-popup-window>
  <xyl-popup-window v-model="isAdjustRecords" name="办理单位调整记录">
    <ApplyForAdjustRecords :id="route.query.id"></ApplyForAdjustRecords>
  </xyl-popup-window>
  <xyl-popup-window v-model="isAdjustResult" name="办理单位调整结果">
    <ApplyForAdjustResult :id="route.query.id"></ApplyForAdjustResult>
  </xyl-popup-window>
  <xyl-popup-window v-model="isTrackTransact" name="跟踪办理申请记录">
    <TrackTransactApplyForRecords :id="route.query.id"></TrackTransactApplyForRecords>
  </xyl-popup-window>
  <xyl-popup-window v-model="show" name="办理单位与委员沟通情况">
    <CommunicationSituation :id="route.query.id" :type="['transact', 'reply', 'conclude'].includes(route.query.type)">
    </CommunicationSituation>
  </xyl-popup-window>
  <xyl-popup-window v-model="replyDetailShow" name="答复文件详情">
    <SuggestReplyDetail :id="replyId"></SuggestReplyDetail>
  </xyl-popup-window>
  <xyl-popup-window v-model="ifShow" name="满意度测评">
    <SegreeSatisfactionDetail :id="satisfactionsId" :suggestId="route.query.id"></SegreeSatisfactionDetail>
  </xyl-popup-window>

  <xyl-popup-window v-model="isAnswerRecords" name="申请延期记录">
    <UnitApplyForAnswerRecords :name="flowHandleOfficeName" :data="lookdelays"></UnitApplyForAnswerRecords>
  </xyl-popup-window>
</template>
<script>
export default { name: 'SuggestDetail' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { format } from 'common/js/time.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { exportWordHtmlObj } from 'common/config/MicroGlobal'
import { filterTableData } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
import suggestPrint from '@/components/suggestPrint/suggestPrint'
import SuggestBasicInfo from './component/SuggestBasicInfo.vue' // 提案基本信息
import SuggestAssignDetail from '@/views/SuggestAssign/component/SuggestAssignDetail.vue' // 交办
import SuggestPostponeReview from '@/views/SuggestApplyForPostpone/component/SuggestPostponeReview.vue' // 申请延期审查
import SuggestAdjustReview from '@/views/SuggestApplyForAdjust/component/SuggestAdjustReview.vue' // 申请调整审查
import SuggestTrackTransactDetail from '@/views/SuggestTrackTransact/component/SuggestTrackTransactDetail.vue' // 跟踪办理审查
import UnitSuggestDetail from '@/views/UnitSuggestDetail/UnitSuggestDetail.vue' // 单位办理详情
import ApplyForAdjustRecords from './ApplyForAdjustRecords/ApplyForAdjustRecords.vue' // 单位申请调整记录
import ApplyForAdjustResult from './ApplyForAdjustResult/ApplyForAdjustResult.vue' // 单位申请调整结果
import TrackTransactApplyForRecords from './TrackTransactApplyForRecords/TrackTransactApplyForRecords.vue' // 跟踪办理申请记录
import CommunicationSituation from './CommunicationSituation/CommunicationSituation.vue' // 沟通情况
import SuggestReplyDetail from './SuggestReplyDetail/SuggestReplyDetail.vue' // 答复件详情
import SegreeSatisfactionDetail from './SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue' // 满意度测评
import { user } from 'common/js/system_var.js'
import UnitApplyForAnswerRecords from '@/views/UnitSuggestDetail/component/UnitApplyForAnswerRecords.vue'
const route = useRoute()
const activeValue = ref('aaaa')

const printParams = ref({})
const elPrintWhetherShow = ref(false)

const details = ref({})
const setExtResult = ref([])

const transactObj = ref({}) // 办理方式和办理单位ID

const handlingMassing = ref({})
const isProcessActive = ref(false)
const isSatisfaction = ref(1)
const hasExecuteNodeIds = ref([])
const reviewList = ref([]) // 审查信息数组
const assignList = ref([]) // 交办信息数组
const transactUnit = ref([]) // 办理单位列表
const maincoOrganizers = ref([]) // 协办单位列表
const coOrganizer = ref('') // 分办里边的协办单位
const coOrganizerIds = ref([]) // 分办里边的协办单位id
const transactUnitObj = ref({}) // 当前办理单位数据

const isAdjustRecords = ref(false)
const isAdjustResult = ref(false)
const isTrackTransact = ref(false)

const replyList = ref([]) // 答复件列表
const replyId = ref('') // 答复件ID
const replyDetailShow = ref(false)

const ifShow = ref(false)
const satisfactionsId = ref('') // 满意度测评ID
const satisfactions = ref([]) // 满意度测评

const unitRecordsId = ref('')
const postponeShow = ref(false)
const show = ref(false)
const isPreAssign = ref(false) // 是否开启预交办
const isPersonal = computed(() => route.query.logo === 'Personal')
const suggestionOfficeId = ref('')
const suggestionOfficeShow = ref(true)

onActivated(() => {
  globalReadConfig()
  if (route.query.id) {
    suggestionInfo()
    suggestionDetails()
  }
  // if (route.query.superviseInfoId) {
  //   cppccSuperviseInfoInfo()
  // }
})
// const cppccSuperviseInfoInfo = async () => {
//   const { data } = await api.globalJson('/cppccSuperviseInfo/info', {
//     detailId: route.query.superviseInfoId
//   })
//   superviseInfo.value = data
// }
const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({
    codes: ['proposal_enable_pre_assign']
  })
  isPreAssign.value = data?.proposal_enable_pre_assign === 'true'
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.proposalId } }
  })
}
const colorObj = (state) => {
  var color = { color: '#000' }
  if (state === 'has_answer') {
    color.color = '#4fcc72'
  } else if (state === 'handling') {
    color.color = '#fbd536'
  } else if (state === 'apply_adjust') {
    color.color = '#ca6063'
  }
  return color
}
const superviseInfo = ref({})
const hasSuperviseInfo = ref(false)
const suggestionInfo = async () => {
  const { data, extData } = await api.suggestionInfo({ detailId: route.query.id })
  data.content = data.content.replace(/<p>/g, '<p style="font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;">');
  details.value = data
  setExtResult.value = extData
  suggestUnitUserList()
  if (data.superviseInfo) {
    hasSuperviseInfo.value = true
    superviseInfo.value = data.superviseInfo
  }
}
// 获取当前登陆者是哪个提案办理单位
const suggestUnitUserList = async () => {
  const { data } = await api.suggestUnitUserList({
    isAnd: 1,
    keyword: user.value.userName,
    tableId: 'sys_npc_suuggestion_office_user',
    pageNo: '1',
    pageSize: '199'
  })
  suggestionOfficeId.value = data[0]?.suggestionOfficeId
  for (let i = 0; i < setExtResult.value.length; i++) {
    if (setExtResult.value[i].handleOfficeId === suggestionOfficeId.value) {
      // 找到匹配的 handleOfficeId
      if (setExtResult.value[i].handleOfficeType === 'assist' || setExtResult.value[i].handleOfficeType === 'assistHandle') {
        suggestionOfficeShow.value = false;  // 如果是assist，设置为false
      } else {
        suggestionOfficeShow.value = true;   // 否则设置为true
      }
      break;  // 找到匹配项后可以跳出循环
    }
  }
}
const delaysList = ref([]) // 延期记录
const isAnswerRecords = ref(false)
const flowHandleOfficeName = ref('')
const lookdelays = ref([])
const handleLook = (item) => {
  flowHandleOfficeName.value = item.flowHandleOfficeName
  lookdelays.value = item.delays
  isAnswerRecords.value = true
}
const allhandleOfficeInfos = ref([])
const suggestionDetails = async () => {
  const { data, extData } = await api.suggestionDetails({ suggestionId: route.query.id })
  hasExecuteNodeIds.value = data.hasExecuteNodeIds
  allhandleOfficeInfos.value = data.handleOfficeInfos
  delaysList.value = []
  if (route.query.type === 'postpone') {
    data.handleOfficeInfos.forEach((item) => {
      if (item.delays?.length) {
        delaysList.value.push(
          // ...item.delays.map((v) => ({ ...v, flowHandleOfficeName: item.handlerOffice.flowHandleOfficeName }))
          { ...item, flowHandleOfficeName: item.handlerOffice.flowHandleOfficeName }
        )
      }
    })
  }
  if (isPersonal.value) {
    if (data.handlingMassing?.answerStopDate) handingPortionAnswerList()
    return
  }
  reviewList.value = []
  assignList.value = []
  transactUnit.value = []
  maincoOrganizers.value = []
  coOrganizer.value = ''
  transactObj.value = { transactType: '', mainHandleOfficeId: [], handleOfficeIds: [] }
  handlingMassing.value = data.handlingMassing
  isSatisfaction.value = data.isSatisfaction
  satisfactions.value = data.satisfactions || []
  coOrganizer.value = extData?.map(v => v.flowHandleOfficeName).join('、')
  coOrganizerIds.value = extData?.map(v => v.handleOfficeId)
  console.log(coOrganizerIds.value)
  if (data.handlingMassing?.answerStopDate) { handingPortionAnswerList() }
  for (let index = 0; index < data.streamVariables?.length; index++) {
    const item = data?.streamVariables[index]
    const nodeTtem = data?.streamVariables[index + 1]
    if (nodeTtem) {
      if (item.streamVariable?.spareB === 'verify') {
        reviewList.value.push({
          id: item.id,
          nodeName: item.nodeName,
          nodeResult: nodeTtem.streamVariable?.spareC,
          UserName: nodeTtem.streamVariable?.handleUserName,
          handleTime: nodeTtem.streamVariable?.handleTime,
          handleContent: nodeTtem.streamVariable?.handleContent,
          isSpareDict: nodeTtem.nodeId === 'rejectReceive',
          spareDict: nodeTtem.streamVariable?.spareDict
        })
      }
      if (item.streamVariable?.spareB === 'submitHandling' || item.streamVariable?.spareB === 'preSubmitHandling') {
        assignList.value.push({
          id: item.id,
          nodeName: item.nodeName,
          nodeResult: nodeTtem.streamVariable?.spareC,
          UserName: nodeTtem.streamVariable?.handleUserName,
          handleTime: nodeTtem.streamVariable?.handleTime,
          handleContent: nodeTtem.streamVariable?.handleContent
        })
      }
    }
  }
  for (let index = 0; index < data.handleOfficeInfos.length; index++) {
    const item = data.handleOfficeInfos[index]
    if (item.handlerOffice.handleOfficeType === 'main') {
      transactObj.value.transactType = 'main_assist'
      transactObj.value.mainHandleOfficeId.push(item.handlerOffice.flowHandleOfficeId)
    } else if (item.handlerOffice.handleOfficeType === 'publish') {
      transactObj.value.transactType = 'publish'
      transactObj.value.handleOfficeIds.push(item.handlerOffice.flowHandleOfficeId)
    } else {
      transactObj.value.handleOfficeIds.push(item.handlerOffice.flowHandleOfficeId)
    }
    if (data.handlingMassing?.answerStopDate || data.handlingMassing?.confirmStopDate) {
      if (item.handlerOffice.handleOfficeType !== 'assist') {
        transactUnit.value.push({
          id: item.handlerOffice.id,
          unitId: item.handlerOffice.flowHandleOfficeId,
          unitName: item.handlerOffice.flowHandleOfficeName,
          unitType:
            item.handlerOffice.handleOfficeType === 'main'
              ? '主办'
              : item.handlerOffice.handleOfficeType === 'assist'
                ? '协办'
                : '分办',
          hasRead: item.handlerOffice.hasRead, // 是否已读
          firstReadTime: item.handlerOffice.firstReadTime, // 阅读时间
          status: item.handlerOffice.currentHandleStatus, // 办理状态
          statusName: item.handlerOffice.currentHandleStatusName, // 办理状态
          isDelays: item.delays?.filter((v) => !v.verifyStatus)?.length ? true : false || false, // 是否有待审查的延期申请
          isAdjusts: item.adjusts?.filter((v) => !v.verifyStatus)?.length ? true : false || false, // 是否有待审查的调整申请
          answers: item.answers?.filter((v) => v.submitAnswerType === 'direct')[0] || {}, // 最终答复件
          hasConfirm: item.handlerOffice.hasConfirm,
          confirmTime: item.handlerOffice.confirmTime || ''
        })
      } else {
        maincoOrganizers.value.push(item.handlerOffice)
        console.log('协办单位', maincoOrganizers.value)
      }
      if (
        item.isCurrentLoginOfficeId &&
        ['unit', 'unitTrackTransact', 'unitConclude', 'trackTransact', 'unitPreAssign'].includes(route.query.type)
      ) {
        transactUnitObj.value = {
          ...item,
          isReply: data.handlingMassing.massingAnswerDate ? true : false || false,
          isDelays: item.delays?.filter((r) => !r.verifyStatus)?.length ? true : false || false,
          isAdjusts: item.adjusts?.filter((v) => !v.verifyStatus)?.length ? true : false || false
        }
      }
    }
  }
}
const handleExportWord = () => {
  if (JSON.stringify(details.value) === '{}')
    return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行导出！' })
  suggestionWord({ ids: [route.query.id] })
}
const handleSuggestPrint = async () => {
  if (JSON.stringify(details.value) === '{}')
    return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行打印！' })
  printParams.value = { ids: [route.query.id] }
  elPrintWhetherShow.value = true
}
const suggestionWord = async (params) => {
  const { data } = await api.suggestionWord(params)
  if (data.length) {
    var wordData = {}
    for (let index = 0; index < data.length; index++) {
      wordData = filterTableData(data[index])
    }
    exportWordHtmlObj({ code: 'proposalDetails', name: wordData.docName, key: 'content', data: wordData })
  }
}
const callback = (type) => {
  postponeShow.value = false
  elPrintWhetherShow.value = false
  if (type) {
    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
  }
}
const refreshCallback = () => {
  suggestionInfo()
  suggestionDetails()
}
const closeCallback = () => {
  qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
}

const handingPortionAnswerList = async () => {
  const { data } = await api.handingPortionAnswerList({ query: { suggestionId: route.query.id } })
  replyList.value = isPersonal.value ? data?.filter((v) => v.isOpen) || [] : data
}
const handleReply = (item) => {
  replyId.value = item.id
  replyDetailShow.value = true
}
const handleSatisfactions = (item) => {
  satisfactionsId.value = item.id
  ifShow.value = true
}
const handlePostpone = (item) => {
  unitRecordsId.value = item.id
  postponeShow.value = true
}
const handleConclude = () => {
  ElMessageBox.confirm('此操作将办结该提案, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      suggestionComplete('handleOver')
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消重新办理' })
    })
}
const anewTransact = () => {
  ElMessageBox.confirm('此操作将重新办理该提案, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      suggestionComplete('suggestionHandling')
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消重新办理' })
    })
}
const suggestionComplete = async (nextNodeId) => {
  const { code } = await api.suggestionComplete({ suggestionId: route.query.id, nextNodeId: nextNodeId })
  if (code === 200) {
    ElMessage({ type: 'success', message: '操作成功' })
    closeCallback()
  }
}
</script>
<style lang="scss">
@import './SuggestDetail.scss';

.SuggestDetail {
  width: 100%;
  height: 100%;

  .suggestPrint {
    width: 790px;
    position: fixed;
    top: -100%;
    left: -100%;
  }

  .detailsPrint,
  .detailsExportInfo {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding-left: 30px;
    margin-bottom: 20px;
    position: relative;
    cursor: pointer;
  }

  .detailsPrint {
    background: url('../../assets/img/suggest_details_print.png') no-repeat;
    background-size: 20px 20px;
    background-position: left center;
  }

  .detailsExportInfo {
    background: url('../../assets/img/suggest_details_export_info.png') no-repeat;
    background-size: 20px 20px;
    background-position: left center;
  }

  .anchor-location-item {
    padding-top: 0;
    padding-bottom: 0;
  }

  .SuggestDetailProcessInfo {
    padding: var(--zy-distance-one);
    padding-bottom: 0;
  }

  .SuggestLabelName {
    width: 100%;
    font-weight: bold;
    position: relative;
    padding: 20px 28px;
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 20px;
      height: 20px;
      transform: translateY(-50%);
      background: url('../../assets/img/suggest_details_icon.png') no-repeat;
      background-color: var(--zy-el-color-primary);
      background-size: 20px 20px;
      background-position: center center;
    }

    .SuggestLabelNameButton {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);

      .zy-el-button {
        --zy-el-button-size: var(--zy-height-secondary);
      }
    }

    .SuggestDetailBodyActive {
      font-weight: normal;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-text-font-size);
      line-height: 1;
      cursor: pointer;
      margin-left: 12px;
      display: inline-flex;
      align-items: center;

      .zy-el-icon {
        font-size: var(--zy-name-font-size);
        transform: rotate(180deg);
        transition: transform 0.5s ease;
        margin-left: 2px;
      }
    }

    .SuggestDetailBodyActive.is-ctive {
      .zy-el-icon {
        transform: rotate(0deg);
        transition: transform 0.5s ease;
      }
    }
  }

  .SuggestDetailProcessNodeBody {
    width: 100%;
    padding-bottom: 20px;

    .SuggestDetailProcessNodeMain {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .SuggestDetailProcessNodeInfo+.SuggestDetailProcessNodeInfo {
        .SuggestDetailProcessNodeIcon {
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -99px;
            width: 99px;
            transform: translateY(-50%);
            border-top: 1px dashed var(--zy-el-text-color-regular);
          }
        }
      }

      .SuggestDetailProcessNodeInfo {
        flex: 1;

        .SuggestDetailProcessNode {
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
          text-align: center;
        }

        .SuggestDetailProcessNodeIcon {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 1px solid var(--zy-el-text-color-regular);
          color: var(--zy-el-text-color-regular);
          font-size: var(--zy-name-font-size);
          margin: auto;
          z-index: 9;
        }

        &:nth-child(7) {
          .SuggestDetailProcessNodeIcon {
            &::before {
              content: '';
              position: absolute;
              top: 100%;
              left: 50%;
              height: 45px;
              transform: translateX(-50%);
              border-left: 1px dashed var(--zy-el-text-color-regular);
            }
          }

          .SuggestDetailProcessNodeIcon.is-active.is-active-other {
            &::after {
              border-color: var(--zy-el-text-color-regular);
            }

            &::before {
              border-color: var(--zy-el-color-primary);
            }
          }
        }

        .SuggestDetailProcessNodeIcon.is-active {
          color: #fff;
          background-color: var(--zy-el-color-primary);
          border: 1px solid var(--zy-el-color-primary);

          &::after {
            border-color: var(--zy-el-color-primary);
          }
        }
      }
    }

    .SuggestDetailProcessNodeAssist {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding-top: 30px;

      .SuggestDetailProcessNodeInfo {
        flex: 1;

        .SuggestDetailProcessNode {
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
          text-align: center;
        }

        .SuggestDetailProcessNodeIcon {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 1px solid var(--zy-el-text-color-regular);
          color: var(--zy-el-text-color-regular);
          font-size: var(--zy-name-font-size);
          margin: auto;
          z-index: 2;
        }

        &:nth-child(2) {
          .SuggestDetailProcessNodeIcon {
            border: 0;

            &::after {
              content: '';
              position: absolute;
              bottom: 50%;
              left: 50%;
              height: 60px;
              transform: translateX(-50%);
              border-left: 1px dashed var(--zy-el-text-color-regular);
            }
          }

          .SuggestDetailProcessNodeIcon.is-active {
            background-color: transparent;
          }
        }

        &:nth-child(3) {
          .SuggestDetailProcessNodeIcon {
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: -115px;
              width: 115px;
              transform: translateY(-50%);
              border-top: 1px dashed var(--zy-el-text-color-regular);
            }

            .SuggestDetailProcessNode {
              position: absolute;
              top: 50%;
              left: 100%;
              transform: translateY(-50%);
              white-space: nowrap;
              color: var(--zy-el-text-color-regular);
              padding-left: 10px;
            }
          }
        }

        &:nth-child(4) {
          .SuggestDetailProcessNodeIcon {
            border: 0;

            &::after {
              content: '';
              position: absolute;
              bottom: 50%;
              left: 50%;
              height: 60px;
              transform: translateX(-50%);
              border-left: 1px dashed var(--zy-el-text-color-regular);
            }
          }
        }

        &:nth-child(5) {
          .SuggestDetailProcessNodeIcon {
            border: 0;

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: -115px;
              width: 245px;
              transform: translateY(-50%);
              border-top: 1px dashed var(--zy-el-text-color-regular);
            }
          }
        }

        &:nth-child(6) {
          .SuggestDetailProcessNodeIcon {
            &::after {
              content: '';
              position: absolute;
              bottom: 100%;
              left: 50%;
              height: 30px;
              transform: translateX(-50%);
              border-left: 1px dashed var(--zy-el-text-color-regular);
            }

            .SuggestDetailProcessNode {
              position: absolute;
              top: 100%;
              left: 50%;
              transform: translateX(-50%);
              white-space: nowrap;
              color: var(--zy-el-text-color-regular);
            }
          }
        }

        &:nth-child(7) {
          .SuggestDetailProcessNodeIcon {
            border: 0;

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: -100px;
              width: 115px;
              transform: translateY(-50%);
              border-top: 1px dashed var(--zy-el-text-color-regular);
            }
          }

          .SuggestDetailProcessNodeIcon.is-active {
            background-color: transparent;
            border: 0;
          }
        }

        .SuggestDetailProcessNodeIcon.is-active {
          color: #fff;
          background-color: var(--zy-el-color-primary);
          border: 1px solid var(--zy-el-color-primary);

          &::after {
            border-color: var(--zy-el-color-primary);
          }
        }
      }
    }
  }

  .global-info {
    padding-bottom: 12px;

    .global-info-item {
      .global-info-label {
        width: 160px;
      }

      .global-info-content {
        width: calc(100% - 160px);
      }
    }
  }

  .SuggestRead {
    padding-left: 26px;
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    color: var(--zy-el-text-color-regular);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      background: url("../../assets/img/suggest_details_read.png") no-repeat;
      background-color: var(--zy-el-color-info);
      background-size: 100% 100%;
    }
  }

  .SuggestUnRead {
    padding-left: 26px;
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      background: url("../../assets/img/suggest_details_unread.png") no-repeat;
      background-color: var(--zy-el-color-danger);
      background-size: 100% 100%;
    }
  }

  .SuggestReviewUnit {
    display: flex;
    align-items: center;

    .zy-el-link {
      margin-left: 10px;
    }
  }

  .SuggestDetailTable {
    width: 100%;
    margin-bottom: 20px;
    border-top: 1px solid var(--zy-el-border-color-lighter);
    border-right: 1px solid var(--zy-el-border-color-lighter);

    .SuggestDetailTableHead,
    .SuggestDetailTableBody {
      width: 100%;
      display: flex;
      border-bottom: 1px solid var(--zy-el-border-color-lighter);
    }

    .SuggestDetailTableHead {
      background-color: var(--zy-el-color-info-light-9);
    }

    .SuggestDetailTableBody {
      border-bottom: 1px solid var(--zy-el-border-color-lighter);
    }

    .SuggestDetailTableItem {
      text-align: center;
      border-left: 1px solid var(--zy-el-border-color-lighter);
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      padding: 10px;
      overflow: hidden;
      white-space: nowrap;
    }

    .row2 {
      flex: 2;
    }

    .row3 {
      flex: 3;
    }

    .row5 {
      flex: 5;
    }
  }

  .SuggestReplyButton {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--zy-distance-two) var(--zy-distance-one);
  }

  .SuggestDetailProcessInfo+.SuggestDetailProcessInfo {
    padding-top: 0;
  }

  .SuggestSegmentation {
    width: 100%;
    height: 10px;
    background-color: var(--zy-el-color-info-light-9);
  }
}

@media screen and (max-width: 1580px) {
  .SuggestDetail {

    .detailsPrint,
    .detailsExportInfo {
      color: transparent;
    }
  }
}
</style>
