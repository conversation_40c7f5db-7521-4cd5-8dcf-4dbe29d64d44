{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, watch } from 'vue';\nimport { hasPermission } from 'common/js/permissions';\nimport GlobalDynamicInput from './global-dynamic-input.vue';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'DynamicTitle'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    templateCode: {\n      type: String,\n      default: ''\n    },\n    params: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    titles: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var id = ref('');\n    var title = ref('');\n    var show = ref(false);\n    var formArr = ref([]);\n    var dynamicTextByCode = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$dynamicTex, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.dynamicTextByCode(_objectSpread({\n                templateCode: props.templateCode\n              }, props.params));\n            case 2:\n              _yield$api$dynamicTex = _context.sent;\n              data = _yield$api$dynamicTex.data;\n              if (props.titles) {\n                title.value = props.titles;\n              } else {\n                title.value = data;\n              }\n              emit('callback', data);\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function dynamicTextByCode() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleEdit = function handleEdit() {\n      show.value = true;\n      dynamicTextInfo();\n    };\n    var splitFunc = function splitFunc(array, label) {\n      var outArray = [];\n      for (var i = 0; i < array.length; i++) {\n        var newArr = [];\n        var splitArr = array[i].split(label);\n        for (var j = 0; j < splitArr.length; j++) {\n          newArr.push(splitArr[j]);\n          if (j < splitArr.length - 1) {\n            newArr.push(label);\n          }\n        }\n        outArray.push(newArr);\n      }\n      return outArray;\n    };\n    var dynamicTextInfo = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _yield$api$dynamicTex2, data, array, labels, i, twoArray, oneArray, is, _i;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.dynamicTextInfo(_objectSpread({\n                templateCode: props.templateCode\n              }, props.params));\n            case 2:\n              _yield$api$dynamicTex2 = _context2.sent;\n              data = _yield$api$dynamicTex2.data;\n              formArr.value = [];\n              id.value = data.id;\n              array = [data.templateContent];\n              labels = data.templateContent.match(/\\{[^\\}]+\\}/g); // eslint-disable-line\n              for (i = 0; i < labels.length; i++) {\n                twoArray = splitFunc(array, labels[i]);\n                oneArray = twoArray.reduce(function (a, b) {\n                  return a.concat(b);\n                });\n                array = oneArray.filter(function (item) {\n                  return item !== '';\n                });\n              }\n              is = true;\n              for (_i = 0; _i < array.length; _i++) {\n                if (array[_i].match(/\\{[^\\}]+\\}/g)) {\n                  // eslint-disable-line\n                  if (is) {\n                    formArr.value.push({\n                      id: formArr.value.length + '',\n                      text: '',\n                      disabled: false\n                    });\n                  }\n                  is = true;\n                  formArr.value.push({\n                    id: formArr.value.length + '',\n                    text: array[_i],\n                    disabled: true\n                  });\n                  if (_i === array.length - 1) {\n                    formArr.value.push({\n                      id: formArr.value.length + '',\n                      text: '',\n                      disabled: false\n                    });\n                  }\n                } else {\n                  is = false;\n                  formArr.value.push({\n                    id: formArr.value.length + '',\n                    text: array[_i],\n                    disabled: false\n                  });\n                }\n              }\n            case 11:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function dynamicTextInfo() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var dynamicTextEdit = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var templateContent, index, item, _yield$api$dynamicTex3, code;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              templateContent = '';\n              for (index = 0; index < formArr.value.length; index++) {\n                item = formArr.value[index];\n                templateContent += item.text;\n              }\n              _context3.next = 4;\n              return api.dynamicTextEdit({\n                form: _objectSpread({\n                  id: id.value,\n                  templateCode: props.templateCode,\n                  templateContent: templateContent\n                }, props.params)\n              });\n            case 4:\n              _yield$api$dynamicTex3 = _context3.sent;\n              code = _yield$api$dynamicTex3.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '编辑成功'\n                });\n                dynamicTextByCode();\n                show.value = false;\n              }\n            case 7:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function dynamicTextEdit() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    watch(function () {\n      return props.params;\n    }, function () {\n      dynamicTextByCode();\n      dynamicTextInfo();\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      props,\n      emit,\n      id,\n      title,\n      show,\n      formArr,\n      dynamicTextByCode,\n      handleEdit,\n      splitFunc,\n      dynamicTextInfo,\n      dynamicTextEdit,\n      get api() {\n        return api;\n      },\n      ref,\n      watch,\n      get hasPermission() {\n        return hasPermission;\n      },\n      GlobalDynamicInput,\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread", "arguments", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "asyncGeneratorStep", "_asyncToGenerator", "_next", "_throw", "api", "ref", "watch", "hasPermission", "GlobalDynamicInput", "ElMessage", "__default__", "props", "__props", "emit", "__emit", "id", "title", "show", "formArr", "dynamicTextByCode", "_ref2", "_callee", "_yield$api$dynamicTex", "data", "_callee$", "_context", "templateCode", "params", "titles", "handleEdit", "dynamicTextInfo", "splitFunc", "array", "label", "outArray", "newArr", "splitArr", "split", "j", "_ref3", "_callee2", "_yield$api$dynamicTex2", "labels", "twoArray", "oneArray", "is", "_i", "_callee2$", "_context2", "templateContent", "match", "reduce", "b", "concat", "item", "text", "disabled", "dynamicTextEdit", "_ref4", "_callee3", "index", "_yield$api$dynamicTex3", "code", "_callee3$", "_context3", "form", "message", "immediate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/components/global-dynamic-title/global-dynamic-title.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-dynamic-title\">\r\n    <div class=\"global-dynamic-name\">{{ title }}\r\n      <template v-if=\"hasPermission('dynamic_title') && !props.disabled\">\r\n        <el-tooltip effect=\"dark\"\r\n                    placement=\"top\"\r\n                    v-if=\"!show\"\r\n                    content=\"点击修改表头\">\r\n          <span class=\"global-dynamic-icon\"\r\n                @click=\"handleEdit\"></span>\r\n        </el-tooltip>\r\n        <el-tooltip effect=\"dark\"\r\n                    placement=\"top\"\r\n                    v-if=\"show\"\r\n                    content=\"点击确定修改\">\r\n          <span class=\"global-dynamic-determine\"\r\n                @click=\"dynamicTextEdit\">\r\n            <el-icon>\r\n              <Finished />\r\n            </el-icon>\r\n          </span>\r\n        </el-tooltip>\r\n      </template>\r\n    </div>\r\n    <div class=\"global-dynamic-input-list\"\r\n         v-if=\"show\">\r\n      <GlobalDynamicInput v-for=\"item in formArr\"\r\n                          :key=\"item.id\"\r\n                          v-model=\"item.text\"\r\n                          :disabled=\"item.disabled\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DynamicTitle' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, watch } from 'vue'\r\nimport { hasPermission } from 'common/js/permissions'\r\nimport GlobalDynamicInput from './global-dynamic-input.vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  templateCode: { type: String, default: '' },\r\n  params: { type: Object, default: () => ({}) },\r\n  disabled: { type: Boolean, default: false },\r\n  titles: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst id = ref('')\r\nconst title = ref('')\r\nconst show = ref(false)\r\nconst formArr = ref([])\r\n\r\nconst dynamicTextByCode = async () => {\r\n  const { data } = await api.dynamicTextByCode({ templateCode: props.templateCode, ...props.params })\r\n  if (props.titles) {\r\n    title.value = props.titles\r\n  } else {\r\n    title.value = data\r\n  }\r\n  emit('callback', data)\r\n}\r\nconst handleEdit = () => {\r\n  show.value = true\r\n  dynamicTextInfo()\r\n}\r\nconst splitFunc = (array, label) => {\r\n  const outArray = []\r\n  for (let i = 0; i < array.length; i++) {\r\n    let newArr = []\r\n    const splitArr = array[i].split(label)\r\n    for (let j = 0; j < splitArr.length; j++) {\r\n      newArr.push(splitArr[j])\r\n      if (j < splitArr.length - 1) {\r\n        newArr.push(label)\r\n      }\r\n    }\r\n    outArray.push(newArr)\r\n  }\r\n  return outArray\r\n}\r\nconst dynamicTextInfo = async () => {\r\n  const { data } = await api.dynamicTextInfo({ templateCode: props.templateCode, ...props.params })\r\n  formArr.value = []\r\n  id.value = data.id\r\n  let array = [data.templateContent]\r\n  const labels = data.templateContent.match(/\\{[^\\}]+\\}/g) // eslint-disable-line\r\n  for (let i = 0; i < labels.length; i++) {\r\n    const twoArray = splitFunc(array, labels[i])\r\n    const oneArray = twoArray.reduce(function (a, b) { return a.concat(b) })\r\n    array = oneArray.filter(item => item !== '')\r\n  }\r\n  var is = true\r\n  for (let i = 0; i < array.length; i++) {\r\n    if (array[i].match(/\\{[^\\}]+\\}/g)) { // eslint-disable-line\r\n      if (is) {\r\n        formArr.value.push({ id: formArr.value.length + '', text: '', disabled: false })\r\n      }\r\n      is = true\r\n      formArr.value.push({ id: formArr.value.length + '', text: array[i], disabled: true })\r\n      if (i === array.length - 1) {\r\n        formArr.value.push({ id: formArr.value.length + '', text: '', disabled: false })\r\n      }\r\n    } else {\r\n      is = false\r\n      formArr.value.push({ id: formArr.value.length + '', text: array[i], disabled: false })\r\n    }\r\n  }\r\n}\r\nconst dynamicTextEdit = async () => {\r\n  var templateContent = ''\r\n  for (let index = 0; index < formArr.value.length; index++) {\r\n    const item = formArr.value[index]\r\n    templateContent += item.text\r\n  }\r\n  const { code } = await api.dynamicTextEdit({\r\n    form: {\r\n      id: id.value,\r\n      templateCode: props.templateCode,\r\n      templateContent: templateContent,\r\n      ...props.params\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '编辑成功' })\r\n    dynamicTextByCode()\r\n    show.value = false\r\n  }\r\n}\r\nwatch(() => props.params, () => {\r\n  dynamicTextByCode()\r\n  dynamicTextInfo()\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.global-dynamic-title {\r\n  width: 100%;\r\n  border-bottom: 2px solid var(--zy-el-color-primary);\r\n\r\n  .global-dynamic-name {\r\n    position: relative;\r\n    padding: 20px 40px;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    color: var(--zy-el-color-primary);\r\n    font-size: var(--zy-title-font-size);\r\n    line-height: var(--zy-line-height);\r\n\r\n    .global-dynamic-icon {\r\n      position: absolute;\r\n      top: 50%;\r\n      right: 0;\r\n      transform: translateY(-50%);\r\n      width: 24px;\r\n      height: 24px;\r\n      background: url(\"./img/global_dynamic_title_edit.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .global-dynamic-determine {\r\n      position: absolute;\r\n      top: 50%;\r\n      right: 0;\r\n      transform: translateY(-50%);\r\n      font-size: 26px;\r\n      display: flex;\r\n      align-items: center;\r\n      color: var(--zy-el-text-color-regular);\r\n    }\r\n  }\r\n\r\n  .global-dynamic-input-list {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding-bottom: 20px;\r\n\r\n    .zy-el-input__wrapper {\r\n      border-radius: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAsCA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,QAAAvG,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAqG,qBAAA,QAAAjG,CAAA,GAAAJ,MAAA,CAAAqG,qBAAA,CAAAxG,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAkG,MAAA,WAAAvG,CAAA,WAAAC,MAAA,CAAAuG,wBAAA,CAAA1G,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAkC,KAAA,CAAA1G,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2G,cAAA5G,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAA2G,SAAA,CAAA/B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAA4G,SAAA,CAAA3G,CAAA,IAAA2G,SAAA,CAAA3G,CAAA,QAAAA,CAAA,OAAAqG,OAAA,CAAApG,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA4G,eAAA,CAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA4G,yBAAA,GAAA5G,MAAA,CAAA6G,gBAAA,CAAAhH,CAAA,EAAAG,MAAA,CAAA4G,yBAAA,CAAA9G,CAAA,KAAAsG,OAAA,CAAApG,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAuG,wBAAA,CAAAzG,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA8G,gBAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA+G,cAAA,CAAA/G,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAiH,eAAAhH,CAAA,QAAAS,CAAA,GAAAwG,YAAA,CAAAjH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAwG,aAAAjH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAwG,WAAA,kBAAAnH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAkH,MAAA,GAAAC,MAAA,EAAApH,CAAA;AAAA,SAAAqH,mBAAAjH,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAgH,kBAAAlH,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA6G,SAAA,aAAArB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAAwH,MAAAnH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,UAAApH,CAAA,cAAAoH,OAAApH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,WAAApH,CAAA,KAAAmH,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAChC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,SAASC,SAAS,QAAQ,cAAc;AAPxC,IAAAC,WAAA,GAAe;EAAE9C,IAAI,EAAE;AAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQvC,IAAM+C,KAAK,GAAGC,OAKZ;IACF,IAAMC,IAAI,GAAGC,MAAyB;IAEtC,IAAMC,EAAE,GAAGV,GAAG,CAAC,EAAE,CAAC;IAClB,IAAMW,KAAK,GAAGX,GAAG,CAAC,EAAE,CAAC;IACrB,IAAMY,IAAI,GAAGZ,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMa,OAAO,GAAGb,GAAG,CAAC,EAAE,CAAC;IAEvB,IAAMc,iBAAiB;MAAA,IAAAC,KAAA,GAAAnB,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAwD,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAA9I,mBAAA,GAAAuB,IAAA,UAAAwH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAnD,IAAA,GAAAmD,QAAA,CAAA9E,IAAA;YAAA;cAAA8E,QAAA,CAAA9E,IAAA;cAAA,OACDyD,GAAG,CAACe,iBAAiB,CAAA7B,aAAA;gBAAGoC,YAAY,EAAEf,KAAK,CAACe;cAAY,GAAKf,KAAK,CAACgB,MAAM,CAAE,CAAC;YAAA;cAAAL,qBAAA,GAAAG,QAAA,CAAArF,IAAA;cAA3FmF,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZ,IAAIZ,KAAK,CAACiB,MAAM,EAAE;gBAChBZ,KAAK,CAAC7H,KAAK,GAAGwH,KAAK,CAACiB,MAAM;cAC5B,CAAC,MAAM;gBACLZ,KAAK,CAAC7H,KAAK,GAAGoI,IAAI;cACpB;cACAV,IAAI,CAAC,UAAU,EAAEU,IAAI,CAAC;YAAA;YAAA;cAAA,OAAAE,QAAA,CAAAhD,IAAA;UAAA;QAAA,GAAA4C,OAAA;MAAA,CACvB;MAAA,gBARKF,iBAAiBA,CAAA;QAAA,OAAAC,KAAA,CAAA/B,KAAA,OAAAE,SAAA;MAAA;IAAA,GAQtB;IACD,IAAMsC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvBZ,IAAI,CAAC9H,KAAK,GAAG,IAAI;MACjB2I,eAAe,CAAC,CAAC;IACnB,CAAC;IACD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,KAAK,EAAEC,KAAK,EAAK;MAClC,IAAMC,QAAQ,GAAG,EAAE;MACnB,KAAK,IAAI9I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,KAAK,CAACxE,MAAM,EAAEpE,CAAC,EAAE,EAAE;QACrC,IAAI+I,MAAM,GAAG,EAAE;QACf,IAAMC,QAAQ,GAAGJ,KAAK,CAAC5I,CAAC,CAAC,CAACiJ,KAAK,CAACJ,KAAK,CAAC;QACtC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAAC5E,MAAM,EAAE8E,CAAC,EAAE,EAAE;UACxCH,MAAM,CAAChF,IAAI,CAACiF,QAAQ,CAACE,CAAC,CAAC,CAAC;UACxB,IAAIA,CAAC,GAAGF,QAAQ,CAAC5E,MAAM,GAAG,CAAC,EAAE;YAC3B2E,MAAM,CAAChF,IAAI,CAAC8E,KAAK,CAAC;UACpB;QACF;QACAC,QAAQ,CAAC/E,IAAI,CAACgF,MAAM,CAAC;MACvB;MACA,OAAOD,QAAQ;IACjB,CAAC;IACD,IAAMJ,eAAe;MAAA,IAAAS,KAAA,GAAAtC,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA2E,SAAA;QAAA,IAAAC,sBAAA,EAAAlB,IAAA,EAAAS,KAAA,EAAAU,MAAA,EAAAtJ,CAAA,EAAAuJ,QAAA,EAAAC,QAAA,EAAAC,EAAA,EAAAC,EAAA;QAAA,OAAArK,mBAAA,GAAAuB,IAAA,UAAA+I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1E,IAAA,GAAA0E,SAAA,CAAArG,IAAA;YAAA;cAAAqG,SAAA,CAAArG,IAAA;cAAA,OACCyD,GAAG,CAAC0B,eAAe,CAAAxC,aAAA;gBAAGoC,YAAY,EAAEf,KAAK,CAACe;cAAY,GAAKf,KAAK,CAACgB,MAAM,CAAE,CAAC;YAAA;cAAAc,sBAAA,GAAAO,SAAA,CAAA5G,IAAA;cAAzFmF,IAAI,GAAAkB,sBAAA,CAAJlB,IAAI;cACZL,OAAO,CAAC/H,KAAK,GAAG,EAAE;cAClB4H,EAAE,CAAC5H,KAAK,GAAGoI,IAAI,CAACR,EAAE;cACdiB,KAAK,GAAG,CAACT,IAAI,CAAC0B,eAAe,CAAC;cAC5BP,MAAM,GAAGnB,IAAI,CAAC0B,eAAe,CAACC,KAAK,CAAC,aAAa,CAAC,EAAC;cACzD,KAAS9J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsJ,MAAM,CAAClF,MAAM,EAAEpE,CAAC,EAAE,EAAE;gBAChCuJ,QAAQ,GAAGZ,SAAS,CAACC,KAAK,EAAEU,MAAM,CAACtJ,CAAC,CAAC,CAAC;gBACtCwJ,QAAQ,GAAGD,QAAQ,CAACQ,MAAM,CAAC,UAAU7J,CAAC,EAAE8J,CAAC,EAAE;kBAAE,OAAO9J,CAAC,CAAC+J,MAAM,CAACD,CAAC,CAAC;gBAAC,CAAC,CAAC;gBACxEpB,KAAK,GAAGY,QAAQ,CAACzD,MAAM,CAAC,UAAAmE,IAAI;kBAAA,OAAIA,IAAI,KAAK,EAAE;gBAAA,EAAC;cAC9C;cACIT,EAAE,GAAG,IAAI;cACb,KAASzJ,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAG4I,KAAK,CAACxE,MAAM,EAAEpE,EAAC,EAAE,EAAE;gBACrC,IAAI4I,KAAK,CAAC5I,EAAC,CAAC,CAAC8J,KAAK,CAAC,aAAa,CAAC,EAAE;kBAAE;kBACnC,IAAIL,EAAE,EAAE;oBACN3B,OAAO,CAAC/H,KAAK,CAACgE,IAAI,CAAC;sBAAE4D,EAAE,EAAEG,OAAO,CAAC/H,KAAK,CAACqE,MAAM,GAAG,EAAE;sBAAE+F,IAAI,EAAE,EAAE;sBAAEC,QAAQ,EAAE;oBAAM,CAAC,CAAC;kBAClF;kBACAX,EAAE,GAAG,IAAI;kBACT3B,OAAO,CAAC/H,KAAK,CAACgE,IAAI,CAAC;oBAAE4D,EAAE,EAAEG,OAAO,CAAC/H,KAAK,CAACqE,MAAM,GAAG,EAAE;oBAAE+F,IAAI,EAAEvB,KAAK,CAAC5I,EAAC,CAAC;oBAAEoK,QAAQ,EAAE;kBAAK,CAAC,CAAC;kBACrF,IAAIpK,EAAC,KAAK4I,KAAK,CAACxE,MAAM,GAAG,CAAC,EAAE;oBAC1B0D,OAAO,CAAC/H,KAAK,CAACgE,IAAI,CAAC;sBAAE4D,EAAE,EAAEG,OAAO,CAAC/H,KAAK,CAACqE,MAAM,GAAG,EAAE;sBAAE+F,IAAI,EAAE,EAAE;sBAAEC,QAAQ,EAAE;oBAAM,CAAC,CAAC;kBAClF;gBACF,CAAC,MAAM;kBACLX,EAAE,GAAG,KAAK;kBACV3B,OAAO,CAAC/H,KAAK,CAACgE,IAAI,CAAC;oBAAE4D,EAAE,EAAEG,OAAO,CAAC/H,KAAK,CAACqE,MAAM,GAAG,EAAE;oBAAE+F,IAAI,EAAEvB,KAAK,CAAC5I,EAAC,CAAC;oBAAEoK,QAAQ,EAAE;kBAAM,CAAC,CAAC;gBACxF;cACF;YAAC;YAAA;cAAA,OAAAR,SAAA,CAAAvE,IAAA;UAAA;QAAA,GAAA+D,QAAA;MAAA,CACF;MAAA,gBA3BKV,eAAeA,CAAA;QAAA,OAAAS,KAAA,CAAAlD,KAAA,OAAAE,SAAA;MAAA;IAAA,GA2BpB;IACD,IAAMkE,eAAe;MAAA,IAAAC,KAAA,GAAAzD,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA8F,SAAA;QAAA,IAAAV,eAAA,EAAAW,KAAA,EAAAN,IAAA,EAAAO,sBAAA,EAAAC,IAAA;QAAA,OAAArL,mBAAA,GAAAuB,IAAA,UAAA+J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1F,IAAA,GAAA0F,SAAA,CAAArH,IAAA;YAAA;cAClBsG,eAAe,GAAG,EAAE;cACxB,KAASW,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1C,OAAO,CAAC/H,KAAK,CAACqE,MAAM,EAAEoG,KAAK,EAAE,EAAE;gBACnDN,IAAI,GAAGpC,OAAO,CAAC/H,KAAK,CAACyK,KAAK,CAAC;gBACjCX,eAAe,IAAIK,IAAI,CAACC,IAAI;cAC9B;cAACS,SAAA,CAAArH,IAAA;cAAA,OACsByD,GAAG,CAACqD,eAAe,CAAC;gBACzCQ,IAAI,EAAA3E,aAAA;kBACFyB,EAAE,EAAEA,EAAE,CAAC5H,KAAK;kBACZuI,YAAY,EAAEf,KAAK,CAACe,YAAY;kBAChCuB,eAAe,EAAEA;gBAAe,GAC7BtC,KAAK,CAACgB,MAAM;cAEnB,CAAC,CAAC;YAAA;cAAAkC,sBAAA,GAAAG,SAAA,CAAA5H,IAAA;cAPM0H,IAAI,GAAAD,sBAAA,CAAJC,IAAI;cAQZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBrD,SAAS,CAAC;kBAAEnG,IAAI,EAAE,SAAS;kBAAE4J,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/C/C,iBAAiB,CAAC,CAAC;gBACnBF,IAAI,CAAC9H,KAAK,GAAG,KAAK;cACpB;YAAC;YAAA;cAAA,OAAA6K,SAAA,CAAAvF,IAAA;UAAA;QAAA,GAAAkF,QAAA;MAAA,CACF;MAAA,gBAnBKF,eAAeA,CAAA;QAAA,OAAAC,KAAA,CAAArE,KAAA,OAAAE,SAAA;MAAA;IAAA,GAmBpB;IACDe,KAAK,CAAC;MAAA,OAAMK,KAAK,CAACgB,MAAM;IAAA,GAAE,YAAM;MAC9BR,iBAAiB,CAAC,CAAC;MACnBW,eAAe,CAAC,CAAC;IACnB,CAAC,EAAE;MAAEqC,SAAS,EAAE;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}