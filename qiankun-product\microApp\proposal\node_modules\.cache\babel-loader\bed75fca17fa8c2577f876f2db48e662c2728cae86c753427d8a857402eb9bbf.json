{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { hasPermission } from 'common/js/permissions';\nimport { suggestExportWord } from '@/assets/js/suggestExportWord';\nimport SubmitSegreeSatisfaction from './component/SubmitSegreeSatisfaction.vue';\nimport SegreeSatisfactionDetail from '@/views/SuggestDetail/SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue';\nvar __default__ = {\n  name: 'MyLedSuggest'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [{\n      id: 'exportWord',\n      name: '导出Word',\n      type: 'primary',\n      has: ''\n    }, {\n      id: 'export',\n      name: '导出Excel',\n      type: 'primary',\n      has: ''\n    }];\n    var id = ref('');\n    var type = ref('');\n    var show = ref(false);\n    var isShow = ref(false);\n    var _GlobalTable = GlobalTable({\n        tableId: 'id_prop_proposal_mySubmit',\n        tableApi: 'suggestionList',\n        tableDataObj: {\n          isContainMerge: 1\n        }\n      }),\n      keyword = _GlobalTable.keyword,\n      queryRef = _GlobalTable.queryRef,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableHead = _GlobalTable.tableHead,\n      tableData = _GlobalTable.tableData,\n      exportId = _GlobalTable.exportId,\n      exportParams = _GlobalTable.exportParams,\n      exportShow = _GlobalTable.exportShow,\n      handleQuery = _GlobalTable.handleQuery,\n      handleSortChange = _GlobalTable.handleSortChange,\n      handleHeaderClass = _GlobalTable.handleHeaderClass,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      tableRefReset = _GlobalTable.tableRefReset,\n      handleGetParams = _GlobalTable.handleGetParams,\n      handleEditorCustom = _GlobalTable.handleEditorCustom,\n      handleExportExcel = _GlobalTable.handleExportExcel,\n      tableQuery = _GlobalTable.tableQuery;\n    onActivated(function () {\n      var suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'));\n      if (suggestIds) {\n        tableQuery.value.ids = suggestIds;\n        handleQuery();\n        setTimeout(function () {\n          sessionStorage.removeItem('suggestIds');\n          tableQuery.value.ids = [];\n        }, 1000);\n      } else {\n        handleQuery();\n      }\n    });\n    var handleExcelData = function handleExcelData(_item) {\n      _item.forEach(function (v) {\n        if (!v.mainHandleOffices) {\n          v.mainHandleOffices = v.publishHandleOffices;\n        }\n      });\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'exportWord':\n          suggestExportWord(handleGetParams());\n          break;\n        case 'export':\n          handleExportExcel();\n          break;\n        default:\n          break;\n      }\n    };\n    var handleTableClick = function handleTableClick(key, row) {\n      switch (key) {\n        case 'details':\n          handleDetails(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleDetails = function handleDetails(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '提案详情',\n          path: '/proposal/SuggestDetail',\n          query: {\n            id: item.id\n          }\n        }\n      });\n    };\n    var handleEdit = function handleEdit(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '编辑提案',\n          path: '/proposal/SubmitSuggest',\n          query: {\n            anewId: item.id\n          }\n        }\n      });\n    };\n    var handleSubmit = function handleSubmit(row) {\n      id.value = row.id;\n      type.value = row.satisfactionHandleResult;\n      show.value = true;\n    };\n    var handleSatisfaction = function handleSatisfaction(row) {\n      id.value = row.id;\n      isShow.value = true;\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      exportShow.value = false;\n      show.value = false;\n    };\n    var __returned__ = {\n      buttonList,\n      id,\n      type,\n      show,\n      isShow,\n      keyword,\n      queryRef,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableHead,\n      tableData,\n      exportId,\n      exportParams,\n      exportShow,\n      handleQuery,\n      handleSortChange,\n      handleHeaderClass,\n      handleTableSelect,\n      tableRefReset,\n      handleGetParams,\n      handleEditorCustom,\n      handleExportExcel,\n      tableQuery,\n      handleExcelData,\n      handleReset,\n      handleButton,\n      handleTableClick,\n      handleDetails,\n      handleEdit,\n      handleSubmit,\n      handleSatisfaction,\n      callback,\n      ref,\n      onActivated,\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get hasPermission() {\n        return hasPermission;\n      },\n      get suggestExportWord() {\n        return suggestExportWord;\n      },\n      SubmitSegreeSatisfaction,\n      SegreeSatisfactionDetail\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "GlobalTable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasPermission", "suggestExportWord", "SubmitSegreeSatisfaction", "SegreeSatisfactionDetail", "__default__", "name", "buttonList", "id", "type", "has", "show", "isShow", "_GlobalTable", "tableId", "tableApi", "tableDataObj", "isContainMerge", "keyword", "queryRef", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableHead", "tableData", "exportId", "exportParams", "exportShow", "handleQuery", "handleSortChange", "handleHeaderClass", "handleTableSelect", "tableRefReset", "handleGetParams", "handleEditorCustom", "handleExportExcel", "tableQuery", "suggestIds", "JSON", "parse", "sessionStorage", "getItem", "value", "ids", "setTimeout", "removeItem", "handleExcelData", "_item", "for<PERSON>ach", "v", "mainHandleOffices", "publishHandleOffices", "handleReset", "handleButton", "isType", "handleTableClick", "key", "row", "handleDetails", "item", "setGlobalState", "openRoute", "path", "query", "handleEdit", "anewId", "handleSubmit", "satisfactionHandleResult", "handleSatisfaction", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/BehalfSuggest/MyLedSuggest/MyLedSuggest.vue"], "sourcesContent": ["<template>\r\n  <div class=\"MyLedSuggest\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\" :data=\"tableHead\" ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\" @sort-change=\"handleSortChange\" :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\"\r\n          :noTooltip=\"['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\">\r\n          <template #mainHandleOffices=\"scope\">\r\n            {{scope.row.mainHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}\r\n          </template>\r\n          <template #assistHandleOffices=\"scope\">\r\n            {{scope.row.assistHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}\r\n          </template>\r\n          <template #publishHandleOffices=\"scope\">\r\n            {{scope.row.publishHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}\r\n          </template>\r\n        </xyl-global-table>\r\n        <el-table-column width=\"160\" fixed=\"right\" class-name=\"globalTableCustom\">\r\n          <template #header>\r\n            操作\r\n            <div class=\"TableCustomIcon\" v-if=\"hasPermission('table_custom')\" @click=\"handleEditorCustom\"></div>\r\n          </template>\r\n          <template #default=\"scope\">\r\n            <el-button @click=\"handleEdit(scope.row)\" v-if=\"scope.row.currentNodeId === 'returnSubmit'\" type=\"primary\"\r\n              plain>\r\n              重新提交\r\n            </el-button>\r\n            <el-button @click=\"handleSubmit(scope.row)\" v-if=\"scope.row.currentNodeId === 'hasAnswerSuggestion'\"\r\n              type=\"primary\" plain>\r\n              {{ scope.row.satisfactionHandleResult || '满意度测评' }}\r\n            </el-button>\r\n            <el-button @click=\"handleSatisfaction(scope.row)\" v-if=\"scope.row.currentNodeId === 'handleOver'\"\r\n              type=\"primary\" plain>\r\n              {{ scope.row.satisfactionHandleResult }}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\" name=\"导出Excel\">\r\n      <xyl-export-excel name=\"我领衔的提案\" :exportId=\"exportId\" :params=\"exportParams\" module=\"proposalExportExcel\"\r\n        tableId=\"id_prop_proposal_mySubmit\" @excelCallback=\"callback\"\r\n        :handleExcelData=\"handleExcelData\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"show\" name=\"满意度测评\">\r\n      <SubmitSegreeSatisfaction :id=\"id\" :type=\"type\" @callback=\"callback\"></SubmitSegreeSatisfaction>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"isShow\" name=\"满意度测评\">\r\n      <SegreeSatisfactionDetail :suggestId=\"id\" @callback=\"callback\" type></SegreeSatisfactionDetail>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'MyLedSuggest' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { hasPermission } from 'common/js/permissions'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nimport SubmitSegreeSatisfaction from './component/SubmitSegreeSatisfaction.vue'\r\nimport SegreeSatisfactionDetail from '@/views/SuggestDetail/SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue'\r\nconst buttonList = [\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n]\r\nconst id = ref('')\r\nconst type = ref('')\r\nconst show = ref(false)\r\nconst isShow = ref(false)\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel,\r\n  tableQuery\r\n} = GlobalTable({\r\n  tableId: 'id_prop_proposal_mySubmit',\r\n  tableApi: 'suggestionList',\r\n  tableDataObj: { isContainMerge: 1 }\r\n})\r\n\r\nonActivated(() => {\r\n  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))\r\n  if (suggestIds) {\r\n    tableQuery.value.ids = suggestIds\r\n    handleQuery()\r\n    setTimeout(() => {\r\n      sessionStorage.removeItem('suggestIds')\r\n      tableQuery.value.ids = []\r\n    }, 1000)\r\n  } else {\r\n    handleQuery()\r\n  }\r\n})\r\nconst handleExcelData = (_item) => {\r\n  _item.forEach(v => {\r\n    if (!v.mainHandleOffices) {\r\n      v.mainHandleOffices = v.publishHandleOffices\r\n    }\r\n  })\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams())\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } }\r\n  })\r\n}\r\nconst handleEdit = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '编辑提案', path: '/proposal/SubmitSuggest', query: { anewId: item.id } }\r\n  })\r\n}\r\nconst handleSubmit = (row) => {\r\n  id.value = row.id\r\n  type.value = row.satisfactionHandleResult\r\n  show.value = true\r\n}\r\nconst handleSatisfaction = (row) => {\r\n  id.value = row.id\r\n  isShow.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  exportShow.value = false\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.MyLedSuggest {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAoEA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,wBAAwB,MAAM,0CAA0C;AAC/E,OAAOC,wBAAwB,MAAM,6EAA6E;AATlH,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAe,CAAC;;;;;IAUvC,IAAMC,UAAU,GAAG,CACjB;MAAEC,EAAE,EAAE,YAAY;MAAEF,IAAI,EAAE,QAAQ;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,EAC9D;MAAEF,EAAE,EAAE,QAAQ;MAAEF,IAAI,EAAE,SAAS;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,CAC5D;IACD,IAAMF,EAAE,GAAGX,GAAG,CAAC,EAAE,CAAC;IAClB,IAAMY,IAAI,GAAGZ,GAAG,CAAC,EAAE,CAAC;IACpB,IAAMc,IAAI,GAAGd,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMe,MAAM,GAAGf,GAAG,CAAC,KAAK,CAAC;IACzB,IAAAgB,YAAA,GAsBId,WAAW,CAAC;QACde,OAAO,EAAE,2BAA2B;QACpCC,QAAQ,EAAE,gBAAgB;QAC1BC,YAAY,EAAE;UAAEC,cAAc,EAAE;QAAE;MACpC,CAAC,CAAC;MAzBAC,OAAO,GAAAL,YAAA,CAAPK,OAAO;MACPC,QAAQ,GAAAN,YAAA,CAARM,QAAQ;MACRC,QAAQ,GAAAP,YAAA,CAARO,QAAQ;MACRC,MAAM,GAAAR,YAAA,CAANQ,MAAM;MACNC,MAAM,GAAAT,YAAA,CAANS,MAAM;MACNC,QAAQ,GAAAV,YAAA,CAARU,QAAQ;MACRC,SAAS,GAAAX,YAAA,CAATW,SAAS;MACTC,SAAS,GAAAZ,YAAA,CAATY,SAAS;MACTC,SAAS,GAAAb,YAAA,CAATa,SAAS;MACTC,QAAQ,GAAAd,YAAA,CAARc,QAAQ;MACRC,YAAY,GAAAf,YAAA,CAAZe,YAAY;MACZC,UAAU,GAAAhB,YAAA,CAAVgB,UAAU;MACVC,WAAW,GAAAjB,YAAA,CAAXiB,WAAW;MACXC,gBAAgB,GAAAlB,YAAA,CAAhBkB,gBAAgB;MAChBC,iBAAiB,GAAAnB,YAAA,CAAjBmB,iBAAiB;MACjBC,iBAAiB,GAAApB,YAAA,CAAjBoB,iBAAiB;MACjBC,aAAa,GAAArB,YAAA,CAAbqB,aAAa;MACbC,eAAe,GAAAtB,YAAA,CAAfsB,eAAe;MACfC,kBAAkB,GAAAvB,YAAA,CAAlBuB,kBAAkB;MAClBC,iBAAiB,GAAAxB,YAAA,CAAjBwB,iBAAiB;MACjBC,UAAU,GAAAzB,YAAA,CAAVyB,UAAU;IAOZxC,WAAW,CAAC,YAAM;MAChB,IAAMyC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;MACnE,IAAIJ,UAAU,EAAE;QACdD,UAAU,CAACM,KAAK,CAACC,GAAG,GAAGN,UAAU;QACjCT,WAAW,CAAC,CAAC;QACbgB,UAAU,CAAC,YAAM;UACfJ,cAAc,CAACK,UAAU,CAAC,YAAY,CAAC;UACvCT,UAAU,CAACM,KAAK,CAACC,GAAG,GAAG,EAAE;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLf,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IACF,IAAMkB,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;MACjCA,KAAK,CAACC,OAAO,CAAC,UAAAC,CAAC,EAAI;QACjB,IAAI,CAACA,CAAC,CAACC,iBAAiB,EAAE;UACxBD,CAAC,CAACC,iBAAiB,GAAGD,CAAC,CAACE,oBAAoB;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBpC,OAAO,CAAC0B,KAAK,GAAG,EAAE;MAClBd,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMyB,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,YAAY;UACftD,iBAAiB,CAACiC,eAAe,CAAC,CAAC,CAAC;UACpC;QACF,KAAK,QAAQ;UACXE,iBAAiB,CAAC,CAAC;UACnB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMoB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,GAAG,EAAEC,GAAG,EAAK;MACrC,QAAQD,GAAG;QACT,KAAK,SAAS;UACZE,aAAa,CAACD,GAAG,CAAC;UAClB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9B7D,YAAY,CAAC8D,cAAc,CAAC;QAC1BC,SAAS,EAAE;UAAEzD,IAAI,EAAE,MAAM;UAAE0D,IAAI,EAAE,yBAAyB;UAAEC,KAAK,EAAE;YAAEzD,EAAE,EAAEqD,IAAI,CAACrD;UAAG;QAAE;MACrF,CAAC,CAAC;IACJ,CAAC;IACD,IAAM0D,UAAU,GAAG,SAAbA,UAAUA,CAAIL,IAAI,EAAK;MAC3B7D,YAAY,CAAC8D,cAAc,CAAC;QAC1BC,SAAS,EAAE;UAAEzD,IAAI,EAAE,MAAM;UAAE0D,IAAI,EAAE,yBAAyB;UAAEC,KAAK,EAAE;YAAEE,MAAM,EAAEN,IAAI,CAACrD;UAAG;QAAE;MACzF,CAAC,CAAC;IACJ,CAAC;IACD,IAAM4D,YAAY,GAAG,SAAfA,YAAYA,CAAIT,GAAG,EAAK;MAC5BnD,EAAE,CAACoC,KAAK,GAAGe,GAAG,CAACnD,EAAE;MACjBC,IAAI,CAACmC,KAAK,GAAGe,GAAG,CAACU,wBAAwB;MACzC1D,IAAI,CAACiC,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAM0B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIX,GAAG,EAAK;MAClCnD,EAAE,CAACoC,KAAK,GAAGe,GAAG,CAACnD,EAAE;MACjBI,MAAM,CAACgC,KAAK,GAAG,IAAI;IACrB,CAAC;IACD,IAAM2B,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBrC,aAAa,CAAC,CAAC;MACfJ,WAAW,CAAC,CAAC;MACbD,UAAU,CAACe,KAAK,GAAG,KAAK;MACxBjC,IAAI,CAACiC,KAAK,GAAG,KAAK;IACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}