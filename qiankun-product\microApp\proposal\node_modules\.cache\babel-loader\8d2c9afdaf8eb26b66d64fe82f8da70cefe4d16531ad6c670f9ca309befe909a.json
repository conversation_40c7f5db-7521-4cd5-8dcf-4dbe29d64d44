{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { Back, Right } from '@element-plus/icons-vue';\nimport { reactive, ref, onActivated, watch } from 'vue';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nvar __default__ = {\n  name: 'SuggestedClassification'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var loading = ref(false);\n    var loadingText = ref('');\n    var loadingRight = ref(false);\n    var toCategory = ref(false);\n    var toBack = ref(false);\n    var form = reactive({\n      SuggestBigType: '',\n      // 提案大类\n      SuggestBigTypeName: '',\n      SuggestSmallType: '',\n      // 提案小类\n      SuggestSmallTypeName: '',\n      transactType: '',\n      // 请选择办理方式\n      mainHandleOfficeId: [],\n      handleOfficeIds: []\n    });\n    var BigTypeArr = ref([]);\n    var SmallTypeArr = ref([]);\n    var SuggestBigTypeChange = function SuggestBigTypeChange() {\n      if (form.SuggestBigType) {\n        for (var index = 0; index < BigTypeArr.value.length; index++) {\n          var item = BigTypeArr.value[index];\n          if (item.id === form.SuggestBigType) {\n            form.SuggestBigTypeName = item.name;\n            form.SuggestSmallType = '';\n            SmallTypeArr.value = item.children;\n          }\n        }\n      } else {\n        form.SuggestBigTypeName = '';\n        form.SuggestSmallType = '';\n        SmallTypeArr.value = [];\n      }\n    };\n    var suggestionThemeSelect = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.suggestionThemeSelect({\n                query: {\n                  isUsing: 1\n                }\n              });\n            case 2:\n              res = _context.sent;\n              data = res.data;\n              BigTypeArr.value = data;\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function suggestionThemeSelect() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var chekDetail = function chekDetail(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '提案详情',\n          path: '/proposal/SuggestDetail',\n          query: {\n            id: item.id\n          }\n        }\n      });\n    };\n    onActivated(function () {\n      suggestionThemeSelect();\n      leftInfo();\n    });\n\n    // 未分类提案\n    var SugData = reactive({\n      total: 0,\n      pageNo: 1,\n      pageSize: 20,\n      pageSizes: [10, 20, 50, 80]\n    });\n    var checkAll = ref(false);\n    var isIndeterminate = ref(true);\n    var checkListLeft = ref([]);\n    var leftArr = ref([]);\n    var ChangePageNo = function ChangePageNo(i) {\n      SugData.pageNo = i;\n      leftInfo();\n    };\n    var ChangeSize = function ChangeSize(i) {\n      SugData.pageSize = i;\n      leftInfo();\n    };\n    var handleCheckAllChange = function handleCheckAllChange(val) {\n      checkListLeft.value = val ? leftArr.value.map(function (v) {\n        return v.id;\n      }) : [];\n      isIndeterminate.value = false;\n    };\n    var handleCheckedCitiesChange = function handleCheckedCitiesChange(val) {\n      var checkedCount = val.length;\n      checkAll.value = checkedCount === leftArr.value.length;\n      isIndeterminate.value = checkedCount > 0 && checkedCount < leftArr.value.length;\n    };\n    var leftInfo = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var params, res, data, total, code;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              // loading.value = true\n              params = {\n                keyword: '',\n                pageNo: SugData.pageNo,\n                pageSize: SugData.pageSize\n              };\n              _context2.next = 4;\n              return api.reqProposalEmpty('empty', params);\n            case 4:\n              res = _context2.sent;\n              //查询未分类提案\n              data = res.data, total = res.total, code = res.code;\n              if (code === 200) {\n                // loading.value = false\n              }\n              leftArr.value = data;\n              SugData.total = total;\n              checkListLeft.value = [];\n              _context2.next = 14;\n              break;\n            case 12:\n              _context2.prev = 12;\n              _context2.t0 = _context2[\"catch\"](0);\n            case 14:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 12]]);\n      }));\n      return function leftInfo() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n\n    //已分类提案\n    var SugDataRight = reactive({\n      total: 0,\n      pageNo: 1,\n      pageSize: 20,\n      pageSizes: [10, 20, 50, 80]\n    });\n    var checkListRight = ref([]);\n    var rightArr = ref([]);\n    var checkAllRight = ref(false);\n    var isIndeterminateRight = ref(true);\n    var ChangePageNoRight = function ChangePageNoRight(i) {\n      SugDataRight.pageNo = i;\n      RightInfo();\n    };\n    var ChangeSizeRight = function ChangeSizeRight(i) {\n      SugDataRight.pageSize = i;\n      RightInfo();\n    };\n    var handleCheckAllChangeRight = function handleCheckAllChangeRight(val) {\n      checkListRight.value = val ? rightArr.value.map(function (v) {\n        return v.id;\n      }) : [];\n      isIndeterminateRight.value = false;\n    };\n    var handleCheckedCitiesChangeRight = function handleCheckedCitiesChangeRight(val) {\n      var checkedCount = val.length;\n      checkAllRight.value = checkedCount === rightArr.value.length;\n      isIndeterminateRight.value = checkedCount > 0 && checkedCount < rightArr.value.length;\n    };\n    var RightInfo = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var params, res, data, total, code;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              // loadingRight.value = true\n              params = {\n                keyword: '',\n                pageNo: SugDataRight.pageNo,\n                pageSize: SugDataRight.pageSize,\n                bigThemeId: form.SuggestBigType,\n                smallThemeId: form.SuggestSmallType\n              };\n              _context3.next = 4;\n              return api.reqProposalEmpty('notempty', params);\n            case 4:\n              res = _context3.sent;\n              //查询已分类提案\n              data = res.data, total = res.total, code = res.code;\n              if (code === 200) {\n                // loadingRight.value = false\n              }\n              rightArr.value = data;\n              SugDataRight.total = total;\n              checkListRight.value = [];\n              _context3.next = 14;\n              break;\n            case 12:\n              _context3.prev = 12;\n              _context3.t0 = _context3[\"catch\"](0);\n            case 14:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 12]]);\n      }));\n      return function RightInfo() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var Category = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var idsArr, params, res, code;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              if (!form.SuggestBigType) {\n                _context4.next = 10;\n                break;\n              }\n              //调取分类操作接口\n              idsArr = checkListLeft.value;\n              params = {\n                ids: idsArr,\n                bigThemeId: form.SuggestBigType,\n                bigThemeName: form.SuggestBigTypeName,\n                smallThemeId: form.SuggestSmallType,\n                smallThemeName: form.SuggestSmallTypeName\n              };\n              _context4.next = 5;\n              return api.reqProposalTheme('add', params);\n            case 5:\n              res = _context4.sent;\n              //查询已分类提案\n              code = res.code;\n              if (code == 200) {\n                ElMessage.success('分类成功');\n                leftInfo();\n                RightInfo();\n              }\n              _context4.next = 11;\n              break;\n            case 10:\n              ElMessageBox.alert(`请先确定右侧提案类别再点击“分类”`, '提示', {\n                confirmButtonText: '确定',\n                type: 'warning'\n              }).then(function () {}).catch(function () {});\n            case 11:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function Category() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var sendBack = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var idsArr, res, code, message;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              idsArr = checkListRight.value;\n              _context5.next = 3;\n              return api.reqProposalTheme('clear', {\n                ids: idsArr\n              });\n            case 3:\n              res = _context5.sent;\n              //查询已分类提案\n              code = res.code, message = res.message;\n              if (code == 200) {\n                ElMessage.success(message);\n                leftInfo();\n                RightInfo();\n              }\n            case 6:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function sendBack() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    watch(function () {\n      return checkListLeft.value;\n    }, function (val) {\n      if (val) {\n        toCategory.value = val.length > 0;\n        if (val.length > 0) {\n          toCategory.value = false;\n        } else {\n          toCategory.value = true;\n        }\n      }\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return checkListRight.value;\n    }, function (val) {\n      if (val) {\n        toBack.value = val.length > 0;\n        if (val.length > 0) {\n          toBack.value = false;\n        } else {\n          toBack.value = true;\n        }\n      }\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return form.SuggestBigType;\n    }, function (val) {\n      RightInfo();\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return form.SuggestSmallType;\n    }, function (nowVal, oldVal) {\n      if (nowVal) {\n        BigTypeArr.value.forEach(function (v) {\n          if (form.SuggestBigType === v.id) {\n            v.children.forEach(function (vv) {\n              if (vv.id === nowVal) {\n                form.SuggestSmallTypeName = vv.name;\n              }\n            });\n          }\n        });\n        RightInfo();\n      } else {\n        form.SuggestSmallTypeName = '';\n      }\n      if (oldVal && nowVal === '') {\n        RightInfo();\n      }\n    }, {\n      immediate: false\n    });\n    var __returned__ = {\n      loading,\n      loadingText,\n      loadingRight,\n      toCategory,\n      toBack,\n      form,\n      BigTypeArr,\n      SmallTypeArr,\n      SuggestBigTypeChange,\n      suggestionThemeSelect,\n      chekDetail,\n      SugData,\n      checkAll,\n      isIndeterminate,\n      checkListLeft,\n      leftArr,\n      ChangePageNo,\n      ChangeSize,\n      handleCheckAllChange,\n      handleCheckedCitiesChange,\n      leftInfo,\n      SugDataRight,\n      checkListRight,\n      rightArr,\n      checkAllRight,\n      isIndeterminateRight,\n      ChangePageNoRight,\n      ChangeSizeRight,\n      handleCheckAllChangeRight,\n      handleCheckedCitiesChangeRight,\n      RightInfo,\n      Category,\n      sendBack,\n      get api() {\n        return api;\n      },\n      get Back() {\n        return Back;\n      },\n      get Right() {\n        return Right;\n      },\n      reactive,\n      ref,\n      onActivated,\n      watch,\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "Back", "Right", "reactive", "ref", "onActivated", "watch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ElMessage", "ElMessageBox", "__default__", "loading", "loadingText", "loadingRight", "to<PERSON>ate<PERSON><PERSON>", "toBack", "form", "SuggestBigType", "SuggestBigTypeName", "SuggestSmallType", "SuggestSmallTypeName", "transactType", "mainHandleOfficeId", "handleOfficeIds", "BigTypeArr", "SmallTypeArr", "SuggestBigTypeChange", "index", "item", "id", "children", "suggestionThemeSelect", "_ref2", "_callee", "res", "data", "_callee$", "_context", "query", "isUsing", "chekDetail", "setGlobalState", "openRoute", "path", "leftInfo", "SugData", "total", "pageNo", "pageSize", "pageSizes", "checkAll", "isIndeterminate", "checkListLeft", "leftArr", "ChangePageNo", "ChangeSize", "handleCheckAllChange", "val", "map", "handleCheckedCitiesChange", "checkedCount", "_ref3", "_callee2", "params", "code", "_callee2$", "_context2", "keyword", "reqProposalEmpty", "t0", "SugDataRight", "checkListRight", "rightArr", "checkAllRight", "isIndeterminateRight", "ChangePageNoRight", "RightInfo", "ChangeSizeRight", "handleCheckAllChangeRight", "handleCheckedCitiesChangeRight", "_ref4", "_callee3", "_callee3$", "_context3", "bigThemeId", "smallThemeId", "Category", "_ref5", "_callee4", "idsArr", "_callee4$", "_context4", "ids", "bigThemeName", "smallThemeName", "reqProposalTheme", "success", "alert", "confirmButtonText", "sendBack", "_ref6", "_callee5", "message", "_callee5$", "_context5", "immediate", "nowVal", "oldVal", "vv"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestedClassification/SuggestedClassification.vue"], "sourcesContent": ["<!--\r\n * @Description: 提案分类\r\n -->\r\n<template>\r\n  <el-scrollbar always class=\"SuggestedClassification\">\r\n    <div class=\"SubmitSuggestBody\">\r\n      <div class=\"leftBox\">\r\n        <div class=\"titleClas\">\r\n          <el-icon>\r\n            <DArrowLeft />\r\n          </el-icon>\r\n          <div class=\"titleMidC\">\r\n            <img class=\"iconCla\" src=\"../../assets/img/column.png\" alt=\"\">\r\n            未分类提案\r\n          </div>\r\n          <el-icon>\r\n            <DArrowRight />\r\n          </el-icon>\r\n        </div>\r\n        <div>\r\n          <div class=\"tipsClas\">\r\n            <el-checkbox style=\"display: none;\" v-model=\"checkAll\" :indeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\">checkAll</el-checkbox>\r\n            提示：请先确定右侧提案类别再点击“分类”\r\n          </div>\r\n          <div class=\"contentCla\">\r\n            <el-scrollbar always class=\"scrollbarClas\" v-loading=\"loading\" :lement-loading-text=\"loadingText\">\r\n              <el-checkbox-group v-model=\"checkListLeft\" @change=\"handleCheckedCitiesChange\" class=\"checkBoxClas\">\r\n                <el-checkbox v-for=\"item in leftArr\" :key=\"item.id\" :label=\"item.id\">\r\n                  <!-- <el-tooltip effect=\"dark\"\r\n                              :show-after=\"500\"\r\n                              :content=\"item.title\"\r\n                              placement=\"top-start\"> -->\r\n                  <div @click.prevent=\"chekDetail(item)\" class=\"titleTips ellipsis\" :title=\"item.title\"><span\r\n                      v-if=\"item.streamNumber\">[{{ item.streamNumber }}]</span>\r\n                    {{ item.title }}\r\n                  </div>\r\n                  <!-- </el-tooltip> -->\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-scrollbar>\r\n            <el-pagination class=\"paginationCla\" v-model:currentPage=\"SugData.pageNo\"\r\n              v-model:page-size=\"SugData.pageSize\" :page-sizes=\"SugData.pageSizes\"\r\n              layout=\"sizes, prev, pager, next, total\" @size-change=\"ChangeSize\" @current-change=\"ChangePageNo\"\r\n              :pager-count=\"5\" :total=\"SugData.total\" small />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"middleBox\">\r\n        <div class=\"midTop\">\r\n          <img class=\"iconCla2\" src=\"../../assets/img/swop.png\" alt=\"\">\r\n        </div>\r\n        <div class=\"midButtom\">\r\n          <el-button style=\"margin: 0px 0px 10px 0px;\" :disabled=\"toCategory\" class=\"btn\" type=\"primary\" :icon=\"Right\"\r\n            @click=\"Category()\">分类</el-button>\r\n          <el-button style=\"margin: 20px 0px 0px 0px;\" type=\"primary\" class=\"btn\" :disabled=\"toBack\" @click=\"sendBack()\"\r\n            :icon=\"Back\">退回</el-button>\r\n        </div>\r\n      </div>\r\n      <div class=\"rightBox\">\r\n        <div class=\"titleClasRight\">\r\n          <el-icon>\r\n            <DArrowLeft />\r\n          </el-icon>\r\n          <div class=\"titleMidC\">\r\n            <img class=\"iconCla\" src=\"../../assets/img/column.png\" alt=\"\">\r\n            已分类提案\r\n          </div>\r\n          <el-icon>\r\n            <DArrowRight />\r\n          </el-icon>\r\n        </div>\r\n        <div>\r\n          <div class=\"tipsClasRight\">\r\n            <el-checkbox style=\"display: none;\" v-model=\"checkAllRight\" :indeterminate=\"isIndeterminateRight\"\r\n              @change=\"handleCheckAllChangeRight\">checkAll</el-checkbox>\r\n            <div class=\"sugclass\" style=\"padding-bottom: 5px;\">\r\n              <div class=\"leftCTip requiredStar\">提案大类：</div>\r\n              <el-select v-model=\"form.SuggestBigType\" placeholder=\"请选择提案大类\" @change=\"SuggestBigTypeChange\" clearable>\r\n                <el-option v-for=\"item in BigTypeArr\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n              </el-select>\r\n            </div>\r\n            <div class=\"sugclass\" style=\"padding-top: 5px;\">\r\n              <div class=\"leftCTip\" style=\"margin-left:10px ;\">提案小类：</div>\r\n              <el-select v-model=\"form.SuggestSmallType\" placeholder=\"请选择提案小类\" clearable>\r\n                <el-option v-for=\"item in SmallTypeArr\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n          <div class=\"contentClaRight\">\r\n            <el-scrollbar always class=\"scrollbarClasRight\" v-loading=\"loadingRight\">\r\n              <el-checkbox-group v-model=\"checkListRight\" @change=\"handleCheckedCitiesChangeRight\" class=\"checkBoxClas\">\r\n                <el-checkbox v-for=\"item in rightArr\" :key=\"item.id\" :label=\"item.id\">\r\n                  <!-- <el-tooltip effect=\"dark\"\r\n                              :show-after=\"500\"\r\n                              :content=\"item.title\"\r\n                              placement=\"top-start\"> -->\r\n                  <div @click.prevent=\"chekDetail(item)\" :title=\"item.title\" class=\"titleTips ellipsis\"><span\r\n                      v-if=\"item.streamNumber\">[{{ item.streamNumber }}]</span>\r\n                    {{ item.title }}\r\n                  </div>\r\n                  <!-- </el-tooltip> -->\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-scrollbar>\r\n            <el-pagination class=\"paginationClaRight\" v-model:currentPage=\"SugDataRight.pageNo\"\r\n              v-model:page-size=\"SugDataRight.pageSize\" :page-sizes=\"SugDataRight.pageSizes\"\r\n              layout=\"sizes, prev, pager, next, total\" @size-change=\"ChangeSizeRight\"\r\n              @current-change=\"ChangePageNoRight\" :pager-count=\"5\" :total=\"SugDataRight.total\" small />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestedClassification' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { Back, Right } from '@element-plus/icons-vue'\r\nimport { reactive, ref, onActivated, watch } from 'vue'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst loadingRight = ref(false)\r\n\r\nconst toCategory = ref(false)\r\nconst toBack = ref(false)\r\n\r\nconst form = reactive({\r\n  SuggestBigType: '', // 提案大类\r\n  SuggestBigTypeName: '',\r\n  SuggestSmallType: '', // 提案小类\r\n  SuggestSmallTypeName: '',\r\n  transactType: '', // 请选择办理方式\r\n  mainHandleOfficeId: [],\r\n  handleOfficeIds: []\r\n})\r\nconst BigTypeArr = ref([])\r\nconst SmallTypeArr = ref([])\r\n\r\nconst SuggestBigTypeChange = () => {\r\n  if (form.SuggestBigType) {\r\n    for (let index = 0; index < BigTypeArr.value.length; index++) {\r\n      const item = BigTypeArr.value[index]\r\n      if (item.id === form.SuggestBigType) {\r\n        form.SuggestBigTypeName = item.name\r\n        form.SuggestSmallType = ''\r\n        SmallTypeArr.value = item.children\r\n      }\r\n    }\r\n  } else {\r\n    form.SuggestBigTypeName = ''\r\n    form.SuggestSmallType = ''\r\n    SmallTypeArr.value = []\r\n  }\r\n}\r\nconst suggestionThemeSelect = async () => {\r\n  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })\r\n  var { data } = res\r\n  BigTypeArr.value = data\r\n}\r\n\r\nconst chekDetail = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } } })\r\n}\r\n\r\nonActivated(() => {\r\n  suggestionThemeSelect()\r\n  leftInfo()\r\n})\r\n\r\n// 未分类提案\r\nconst SugData = reactive({\r\n  total: 0,\r\n  pageNo: 1,\r\n  pageSize: 20,\r\n  pageSizes: [10, 20, 50, 80]\r\n})\r\nconst checkAll = ref(false)\r\nconst isIndeterminate = ref(true)\r\nconst checkListLeft = ref([])\r\nconst leftArr = ref([])\r\nconst ChangePageNo = (i) => {\r\n  SugData.pageNo = i\r\n  leftInfo()\r\n}\r\nconst ChangeSize = (i) => {\r\n  SugData.pageSize = i\r\n  leftInfo()\r\n}\r\nconst handleCheckAllChange = (val) => {\r\n  checkListLeft.value = val ? leftArr.value.map(v => v.id) : []\r\n  isIndeterminate.value = false\r\n}\r\nconst handleCheckedCitiesChange = (val) => {\r\n  const checkedCount = val.length\r\n  checkAll.value = checkedCount === leftArr.value.length\r\n  isIndeterminate.value = checkedCount > 0 && checkedCount < leftArr.value.length\r\n}\r\nconst leftInfo = async () => {\r\n  try {\r\n    // loading.value = true\r\n    var params = {\r\n      keyword: '',\r\n      pageNo: SugData.pageNo,\r\n      pageSize: SugData.pageSize,\r\n    }\r\n    const res = await api.reqProposalEmpty('empty', params) //查询未分类提案\r\n    var { data, total, code } = res\r\n    if (code === 200) {\r\n      // loading.value = false\r\n    }\r\n    leftArr.value = data\r\n    SugData.total = total\r\n    checkListLeft.value = []\r\n  } catch (err) {\r\n    // loading.value = false\r\n  }\r\n}\r\n\r\n\r\n//已分类提案\r\nconst SugDataRight = reactive({\r\n  total: 0,\r\n  pageNo: 1,\r\n  pageSize: 20,\r\n  pageSizes: [10, 20, 50, 80]\r\n})\r\nconst checkListRight = ref([])\r\nconst rightArr = ref([])\r\nconst checkAllRight = ref(false)\r\nconst isIndeterminateRight = ref(true)\r\nconst ChangePageNoRight = (i) => {\r\n  SugDataRight.pageNo = i\r\n  RightInfo()\r\n}\r\nconst ChangeSizeRight = (i) => {\r\n  SugDataRight.pageSize = i\r\n  RightInfo()\r\n}\r\nconst handleCheckAllChangeRight = (val) => {\r\n  checkListRight.value = val ? rightArr.value.map(v => v.id) : []\r\n  isIndeterminateRight.value = false\r\n}\r\nconst handleCheckedCitiesChangeRight = (val) => {\r\n  const checkedCount = val.length\r\n  checkAllRight.value = checkedCount === rightArr.value.length\r\n  isIndeterminateRight.value = checkedCount > 0 && checkedCount < rightArr.value.length\r\n}\r\nconst RightInfo = async () => {\r\n  try {\r\n    // loadingRight.value = true\r\n    var params = {\r\n      keyword: '',\r\n      pageNo: SugDataRight.pageNo,\r\n      pageSize: SugDataRight.pageSize,\r\n      bigThemeId: form.SuggestBigType,\r\n      smallThemeId: form.SuggestSmallType\r\n    }\r\n    const res = await api.reqProposalEmpty('notempty', params) //查询已分类提案\r\n    var { data, total, code } = res\r\n    if (code === 200) {\r\n      // loadingRight.value = false\r\n    }\r\n    rightArr.value = data\r\n    SugDataRight.total = total\r\n    checkListRight.value = []\r\n  } catch (err) {\r\n    // loadingRight.value = false\r\n  }\r\n}\r\n\r\nconst Category = async () => {\r\n  if (form.SuggestBigType) {\r\n    //调取分类操作接口\r\n    var idsArr = checkListLeft.value\r\n    var params = {\r\n      ids: idsArr,\r\n      bigThemeId: form.SuggestBigType,\r\n      bigThemeName: form.SuggestBigTypeName,\r\n      smallThemeId: form.SuggestSmallType,\r\n      smallThemeName: form.SuggestSmallTypeName\r\n    }\r\n    const res = await api.reqProposalTheme('add', params) //查询已分类提案\r\n    var { code } = res\r\n    if (code == 200) {\r\n      ElMessage.success('分类成功')\r\n      leftInfo()\r\n      RightInfo()\r\n    }\r\n  } else {\r\n    ElMessageBox.alert(`请先确定右侧提案类别再点击“分类”`, '提示', {\r\n      confirmButtonText: '确定',\r\n      type: 'warning'\r\n    }).then(() => { }).catch(() => { })\r\n  }\r\n}\r\nconst sendBack = async () => {\r\n  var idsArr = checkListRight.value\r\n  const res = await api.reqProposalTheme('clear', { ids: idsArr }) //查询已分类提案\r\n  var { code, message } = res\r\n  if (code == 200) {\r\n    ElMessage.success(message)\r\n    leftInfo()\r\n    RightInfo()\r\n  }\r\n}\r\n\r\nwatch(() => checkListLeft.value, (val) => {\r\n  if (val) {\r\n    toCategory.value = val.length > 0\r\n    if (val.length > 0) {\r\n      toCategory.value = false\r\n    } else {\r\n      toCategory.value = true\r\n    }\r\n  }\r\n}, { immediate: true })\r\nwatch(() => checkListRight.value, (val) => {\r\n  if (val) {\r\n    toBack.value = val.length > 0\r\n    if (val.length > 0) {\r\n      toBack.value = false\r\n    } else {\r\n      toBack.value = true\r\n    }\r\n  }\r\n}, { immediate: true })\r\nwatch(() => form.SuggestBigType, (val) => {\r\n  RightInfo()\r\n}, { immediate: true })\r\nwatch(() => form.SuggestSmallType, (nowVal, oldVal) => {\r\n  if (nowVal) {\r\n    BigTypeArr.value.forEach((v) => {\r\n      if (form.SuggestBigType === v.id) {\r\n        v.children.forEach((vv) => { if (vv.id === nowVal) { form.SuggestSmallTypeName = vv.name } })\r\n      }\r\n    })\r\n    RightInfo()\r\n  } else { form.SuggestSmallTypeName = '' }\r\n  if (oldVal && nowVal === '') { RightInfo() }\r\n}, { immediate: false })\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestedClassification {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SubmitSuggestBody {\r\n    padding: 20px 20px 10px 20px;\r\n    width: 1000px;\r\n    margin: 10px auto;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n\r\n    .leftBox {\r\n      width: 400px;\r\n\r\n      .titleClas {\r\n        background: #999999;\r\n        height: 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        color: #fff;\r\n        font-size: 22px;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: bold;\r\n        margin-bottom: 10px;\r\n        padding: 0 20px;\r\n\r\n        .titleMidC {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          color: #fff;\r\n          font-size: 16px;\r\n        }\r\n\r\n        .iconCla {\r\n          height: 24px;\r\n          padding-right: 12px;\r\n        }\r\n      }\r\n\r\n      .tipsClas {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n      }\r\n\r\n      .contentCla {\r\n        margin-top: 10px;\r\n\r\n        .scrollbarClas {\r\n          height: calc(100vh - 310px);\r\n          border: 1px solid #cccccc;\r\n          padding: 0 10px;\r\n\r\n          .checkBoxClas {\r\n            display: flex;\r\n            flex-direction: column;\r\n\r\n            .titleTips {\r\n              width: 350px;\r\n\r\n              &:hover {\r\n                color: var(--zy-el-color-primary);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .paginationCla {\r\n        padding-top: 6px;\r\n        overflow-x: auto;\r\n      }\r\n    }\r\n\r\n    .middleBox {\r\n      width: 100px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .midTop {\r\n        height: 40px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n\r\n        .iconCla2 {\r\n          height: 40px;\r\n        }\r\n      }\r\n\r\n      .midButtom {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding-bottom: 100px;\r\n      }\r\n    }\r\n\r\n    .rightBox {\r\n      width: 400px;\r\n\r\n      .titleClasRight {\r\n        background: var(--zy-el-color-primary);\r\n        height: 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        color: #fff;\r\n        font-size: 22px;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: bold;\r\n        margin-bottom: 10px;\r\n        padding: 0 20px;\r\n\r\n        .titleMidC {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          color: #fff;\r\n          font-size: 16px;\r\n        }\r\n\r\n        .iconCla {\r\n          height: 24px;\r\n          padding-right: 12px;\r\n        }\r\n      }\r\n\r\n      .tipsClasRight {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n\r\n        .sugclass {\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          .leftCTip {\r\n            font-weight: bold;\r\n            color: #333333;\r\n          }\r\n\r\n          .zy-el-select {\r\n            width: 240px;\r\n          }\r\n\r\n          .requiredStar::before {\r\n            content: \"*\";\r\n            color: var(--zy-el-color-danger);\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .contentClaRight {\r\n        margin-top: 10px;\r\n\r\n        .scrollbarClasRight {\r\n          height: calc(100vh - 372px);\r\n          border: 1px solid #cccccc;\r\n          padding: 0 10px;\r\n\r\n          .checkBoxClas {\r\n            display: flex;\r\n            flex-direction: column;\r\n\r\n            .titleTips {\r\n              width: 350px;\r\n\r\n              &:hover {\r\n                color: var(--zy-el-color-primary);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .paginationClaRight {\r\n        padding-top: 6px;\r\n        overflow-x: auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAyHA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,IAAI,EAAEC,KAAK,QAAQ,yBAAyB;AACrD,SAASC,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAEC,KAAK,QAAQ,KAAK;AACvD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AAPtD,IAAAC,WAAA,GAAe;EAAErC,IAAI,EAAE;AAA0B,CAAC;;;;;IAQlD,IAAMsC,OAAO,GAAGP,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMQ,WAAW,GAAGR,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMS,YAAY,GAAGT,GAAG,CAAC,KAAK,CAAC;IAE/B,IAAMU,UAAU,GAAGV,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMW,MAAM,GAAGX,GAAG,CAAC,KAAK,CAAC;IAEzB,IAAMY,IAAI,GAAGb,QAAQ,CAAC;MACpBc,cAAc,EAAE,EAAE;MAAE;MACpBC,kBAAkB,EAAE,EAAE;MACtBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,oBAAoB,EAAE,EAAE;MACxBC,YAAY,EAAE,EAAE;MAAE;MAClBC,kBAAkB,EAAE,EAAE;MACtBC,eAAe,EAAE;IACnB,CAAC,CAAC;IACF,IAAMC,UAAU,GAAGpB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMqB,YAAY,GAAGrB,GAAG,CAAC,EAAE,CAAC;IAE5B,IAAMsB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjC,IAAIV,IAAI,CAACC,cAAc,EAAE;QACvB,KAAK,IAAIU,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,UAAU,CAAC5H,KAAK,CAACqE,MAAM,EAAE0D,KAAK,EAAE,EAAE;UAC5D,IAAMC,IAAI,GAAGJ,UAAU,CAAC5H,KAAK,CAAC+H,KAAK,CAAC;UACpC,IAAIC,IAAI,CAACC,EAAE,KAAKb,IAAI,CAACC,cAAc,EAAE;YACnCD,IAAI,CAACE,kBAAkB,GAAGU,IAAI,CAACvD,IAAI;YACnC2C,IAAI,CAACG,gBAAgB,GAAG,EAAE;YAC1BM,YAAY,CAAC7H,KAAK,GAAGgI,IAAI,CAACE,QAAQ;UACpC;QACF;MACF,CAAC,MAAM;QACLd,IAAI,CAACE,kBAAkB,GAAG,EAAE;QAC5BF,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAC1BM,YAAY,CAAC7H,KAAK,GAAG,EAAE;MACzB;IACF,CAAC;IACD,IAAMmI,qBAAqB;MAAA,IAAAC,KAAA,GAAArC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2D,QAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAjJ,mBAAA,GAAAuB,IAAA,UAAA2H,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAtD,IAAA,GAAAsD,QAAA,CAAAjF,IAAA;YAAA;cAAAiF,QAAA,CAAAjF,IAAA;cAAA,OACV4C,GAAG,CAAC+B,qBAAqB,CAAC;gBAAEO,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAE;cAAE,CAAC,CAAC;YAAA;cAAhEL,GAAG,GAAAG,QAAA,CAAAxF,IAAA;cACHsF,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVX,UAAU,CAAC5H,KAAK,GAAGuI,IAAI;YAAA;YAAA;cAAA,OAAAE,QAAA,CAAAnD,IAAA;UAAA;QAAA,GAAA+C,OAAA;MAAA,CACxB;MAAA,gBAJKF,qBAAqBA,CAAA;QAAA,OAAAC,KAAA,CAAAnC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAI1B;IAED,IAAM4C,UAAU,GAAG,SAAbA,UAAUA,CAAIZ,IAAI,EAAK;MAC3BrB,YAAY,CAACkC,cAAc,CAAC;QAAEC,SAAS,EAAE;UAAErE,IAAI,EAAE,MAAM;UAAEsE,IAAI,EAAE,yBAAyB;UAAEL,KAAK,EAAE;YAAET,EAAE,EAAED,IAAI,CAACC;UAAG;QAAE;MAAE,CAAC,CAAC;IACvH,CAAC;IAEDxB,WAAW,CAAC,YAAM;MAChB0B,qBAAqB,CAAC,CAAC;MACvBa,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;;IAEF;IACA,IAAMC,OAAO,GAAG1C,QAAQ,CAAC;MACvB2C,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAC5B,CAAC,CAAC;IACF,IAAMC,QAAQ,GAAG9C,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAM+C,eAAe,GAAG/C,GAAG,CAAC,IAAI,CAAC;IACjC,IAAMgD,aAAa,GAAGhD,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMiD,OAAO,GAAGjD,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMkD,YAAY,GAAG,SAAfA,YAAYA,CAAIzJ,CAAC,EAAK;MAC1BgJ,OAAO,CAACE,MAAM,GAAGlJ,CAAC;MAClB+I,QAAQ,CAAC,CAAC;IACZ,CAAC;IACD,IAAMW,UAAU,GAAG,SAAbA,UAAUA,CAAI1J,CAAC,EAAK;MACxBgJ,OAAO,CAACG,QAAQ,GAAGnJ,CAAC;MACpB+I,QAAQ,CAAC,CAAC;IACZ,CAAC;IACD,IAAMY,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,GAAG,EAAK;MACpCL,aAAa,CAACxJ,KAAK,GAAG6J,GAAG,GAAGJ,OAAO,CAACzJ,KAAK,CAAC8J,GAAG,CAAC,UAAA9H,CAAC;QAAA,OAAIA,CAAC,CAACiG,EAAE;MAAA,EAAC,GAAG,EAAE;MAC7DsB,eAAe,CAACvJ,KAAK,GAAG,KAAK;IAC/B,CAAC;IACD,IAAM+J,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAIF,GAAG,EAAK;MACzC,IAAMG,YAAY,GAAGH,GAAG,CAACxF,MAAM;MAC/BiF,QAAQ,CAACtJ,KAAK,GAAGgK,YAAY,KAAKP,OAAO,CAACzJ,KAAK,CAACqE,MAAM;MACtDkF,eAAe,CAACvJ,KAAK,GAAGgK,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAGP,OAAO,CAACzJ,KAAK,CAACqE,MAAM;IACjF,CAAC;IACD,IAAM2E,QAAQ;MAAA,IAAAiB,KAAA,GAAAlE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwF,SAAA;QAAA,IAAAC,MAAA,EAAA7B,GAAA,EAAAC,IAAA,EAAAW,KAAA,EAAAkB,IAAA;QAAA,OAAA9K,mBAAA,GAAAuB,IAAA,UAAAwJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnF,IAAA,GAAAmF,SAAA,CAAA9G,IAAA;YAAA;cAAA8G,SAAA,CAAAnF,IAAA;cAEb;cACIgF,MAAM,GAAG;gBACXI,OAAO,EAAE,EAAE;gBACXpB,MAAM,EAAEF,OAAO,CAACE,MAAM;gBACtBC,QAAQ,EAAEH,OAAO,CAACG;cACpB,CAAC;cAAAkB,SAAA,CAAA9G,IAAA;cAAA,OACiB4C,GAAG,CAACoE,gBAAgB,CAAC,OAAO,EAAEL,MAAM,CAAC;YAAA;cAAjD7B,GAAG,GAAAgC,SAAA,CAAArH,IAAA;cAA+C;cAClDsF,IAAI,GAAkBD,GAAG,CAAzBC,IAAI,EAAEW,KAAK,GAAWZ,GAAG,CAAnBY,KAAK,EAAEkB,IAAI,GAAK9B,GAAG,CAAZ8B,IAAI;cACvB,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChB;cAAA;cAEFX,OAAO,CAACzJ,KAAK,GAAGuI,IAAI;cACpBU,OAAO,CAACC,KAAK,GAAGA,KAAK;cACrBM,aAAa,CAACxJ,KAAK,GAAG,EAAE;cAAAsK,SAAA,CAAA9G,IAAA;cAAA;YAAA;cAAA8G,SAAA,CAAAnF,IAAA;cAAAmF,SAAA,CAAAG,EAAA,GAAAH,SAAA;YAAA;YAAA;cAAA,OAAAA,SAAA,CAAAhF,IAAA;UAAA;QAAA,GAAA4E,QAAA;MAAA,CAI3B;MAAA,gBAnBKlB,QAAQA,CAAA;QAAA,OAAAiB,KAAA,CAAAhE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAmBb;;IAGD;IACA,IAAM0E,YAAY,GAAGnE,QAAQ,CAAC;MAC5B2C,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAC5B,CAAC,CAAC;IACF,IAAMsB,cAAc,GAAGnE,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMoE,QAAQ,GAAGpE,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMqE,aAAa,GAAGrE,GAAG,CAAC,KAAK,CAAC;IAChC,IAAMsE,oBAAoB,GAAGtE,GAAG,CAAC,IAAI,CAAC;IACtC,IAAMuE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAI9K,CAAC,EAAK;MAC/ByK,YAAY,CAACvB,MAAM,GAAGlJ,CAAC;MACvB+K,SAAS,CAAC,CAAC;IACb,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIhL,CAAC,EAAK;MAC7ByK,YAAY,CAACtB,QAAQ,GAAGnJ,CAAC;MACzB+K,SAAS,CAAC,CAAC;IACb,CAAC;IACD,IAAME,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAIrB,GAAG,EAAK;MACzCc,cAAc,CAAC3K,KAAK,GAAG6J,GAAG,GAAGe,QAAQ,CAAC5K,KAAK,CAAC8J,GAAG,CAAC,UAAA9H,CAAC;QAAA,OAAIA,CAAC,CAACiG,EAAE;MAAA,EAAC,GAAG,EAAE;MAC/D6C,oBAAoB,CAAC9K,KAAK,GAAG,KAAK;IACpC,CAAC;IACD,IAAMmL,8BAA8B,GAAG,SAAjCA,8BAA8BA,CAAItB,GAAG,EAAK;MAC9C,IAAMG,YAAY,GAAGH,GAAG,CAACxF,MAAM;MAC/BwG,aAAa,CAAC7K,KAAK,GAAGgK,YAAY,KAAKY,QAAQ,CAAC5K,KAAK,CAACqE,MAAM;MAC5DyG,oBAAoB,CAAC9K,KAAK,GAAGgK,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAGY,QAAQ,CAAC5K,KAAK,CAACqE,MAAM;IACvF,CAAC;IACD,IAAM2G,SAAS;MAAA,IAAAI,KAAA,GAAArF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2G,SAAA;QAAA,IAAAlB,MAAA,EAAA7B,GAAA,EAAAC,IAAA,EAAAW,KAAA,EAAAkB,IAAA;QAAA,OAAA9K,mBAAA,GAAAuB,IAAA,UAAAyK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApG,IAAA,GAAAoG,SAAA,CAAA/H,IAAA;YAAA;cAAA+H,SAAA,CAAApG,IAAA;cAEd;cACIgF,MAAM,GAAG;gBACXI,OAAO,EAAE,EAAE;gBACXpB,MAAM,EAAEuB,YAAY,CAACvB,MAAM;gBAC3BC,QAAQ,EAAEsB,YAAY,CAACtB,QAAQ;gBAC/BoC,UAAU,EAAEpE,IAAI,CAACC,cAAc;gBAC/BoE,YAAY,EAAErE,IAAI,CAACG;cACrB,CAAC;cAAAgE,SAAA,CAAA/H,IAAA;cAAA,OACiB4C,GAAG,CAACoE,gBAAgB,CAAC,UAAU,EAAEL,MAAM,CAAC;YAAA;cAApD7B,GAAG,GAAAiD,SAAA,CAAAtI,IAAA;cAAkD;cACrDsF,IAAI,GAAkBD,GAAG,CAAzBC,IAAI,EAAEW,KAAK,GAAWZ,GAAG,CAAnBY,KAAK,EAAEkB,IAAI,GAAK9B,GAAG,CAAZ8B,IAAI;cACvB,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChB;cAAA;cAEFQ,QAAQ,CAAC5K,KAAK,GAAGuI,IAAI;cACrBmC,YAAY,CAACxB,KAAK,GAAGA,KAAK;cAC1ByB,cAAc,CAAC3K,KAAK,GAAG,EAAE;cAAAuL,SAAA,CAAA/H,IAAA;cAAA;YAAA;cAAA+H,SAAA,CAAApG,IAAA;cAAAoG,SAAA,CAAAd,EAAA,GAAAc,SAAA;YAAA;YAAA;cAAA,OAAAA,SAAA,CAAAjG,IAAA;UAAA;QAAA,GAAA+F,QAAA;MAAA,CAI5B;MAAA,gBArBKL,SAASA,CAAA;QAAA,OAAAI,KAAA,CAAAnF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqBd;IAED,IAAM0F,QAAQ;MAAA,IAAAC,KAAA,GAAA5F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkH,SAAA;QAAA,IAAAC,MAAA,EAAA1B,MAAA,EAAA7B,GAAA,EAAA8B,IAAA;QAAA,OAAA9K,mBAAA,GAAAuB,IAAA,UAAAiL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,IAAA,GAAA4G,SAAA,CAAAvI,IAAA;YAAA;cAAA,KACX4D,IAAI,CAACC,cAAc;gBAAA0E,SAAA,CAAAvI,IAAA;gBAAA;cAAA;cACrB;cACIqI,MAAM,GAAGrC,aAAa,CAACxJ,KAAK;cAC5BmK,MAAM,GAAG;gBACX6B,GAAG,EAAEH,MAAM;gBACXL,UAAU,EAAEpE,IAAI,CAACC,cAAc;gBAC/B4E,YAAY,EAAE7E,IAAI,CAACE,kBAAkB;gBACrCmE,YAAY,EAAErE,IAAI,CAACG,gBAAgB;gBACnC2E,cAAc,EAAE9E,IAAI,CAACI;cACvB,CAAC;cAAAuE,SAAA,CAAAvI,IAAA;cAAA,OACiB4C,GAAG,CAAC+F,gBAAgB,CAAC,KAAK,EAAEhC,MAAM,CAAC;YAAA;cAA/C7B,GAAG,GAAAyD,SAAA,CAAA9I,IAAA;cAA6C;cAChDmH,IAAI,GAAK9B,GAAG,CAAZ8B,IAAI;cACV,IAAIA,IAAI,IAAI,GAAG,EAAE;gBACfxD,SAAS,CAACwF,OAAO,CAAC,MAAM,CAAC;gBACzBpD,QAAQ,CAAC,CAAC;gBACVgC,SAAS,CAAC,CAAC;cACb;cAACe,SAAA,CAAAvI,IAAA;cAAA;YAAA;cAEDqD,YAAY,CAACwF,KAAK,CAAC,mBAAmB,EAAE,IAAI,EAAE;gBAC5CC,iBAAiB,EAAE,IAAI;gBACvBnL,IAAI,EAAE;cACR,CAAC,CAAC,CAACuB,IAAI,CAAC,YAAM,CAAE,CAAC,CAAC,CAACiD,KAAK,CAAC,YAAM,CAAE,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAoG,SAAA,CAAAzG,IAAA;UAAA;QAAA,GAAAsG,QAAA;MAAA,CAEtC;MAAA,gBAxBKF,QAAQA,CAAA;QAAA,OAAAC,KAAA,CAAA1F,KAAA,OAAAD,SAAA;MAAA;IAAA,GAwBb;IACD,IAAMuG,QAAQ;MAAA,IAAAC,KAAA,GAAAzG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+H,SAAA;QAAA,IAAAZ,MAAA,EAAAvD,GAAA,EAAA8B,IAAA,EAAAsC,OAAA;QAAA,OAAApN,mBAAA,GAAAuB,IAAA,UAAA8L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzH,IAAA,GAAAyH,SAAA,CAAApJ,IAAA;YAAA;cACXqI,MAAM,GAAGlB,cAAc,CAAC3K,KAAK;cAAA4M,SAAA,CAAApJ,IAAA;cAAA,OACf4C,GAAG,CAAC+F,gBAAgB,CAAC,OAAO,EAAE;gBAAEH,GAAG,EAAEH;cAAO,CAAC,CAAC;YAAA;cAA1DvD,GAAG,GAAAsE,SAAA,CAAA3J,IAAA;cAAwD;cAC3DmH,IAAI,GAAc9B,GAAG,CAArB8B,IAAI,EAAEsC,OAAO,GAAKpE,GAAG,CAAfoE,OAAO;cACnB,IAAItC,IAAI,IAAI,GAAG,EAAE;gBACfxD,SAAS,CAACwF,OAAO,CAACM,OAAO,CAAC;gBAC1B1D,QAAQ,CAAC,CAAC;gBACVgC,SAAS,CAAC,CAAC;cACb;YAAC;YAAA;cAAA,OAAA4B,SAAA,CAAAtH,IAAA;UAAA;QAAA,GAAAmH,QAAA;MAAA,CACF;MAAA,gBATKF,QAAQA,CAAA;QAAA,OAAAC,KAAA,CAAAvG,KAAA,OAAAD,SAAA;MAAA;IAAA,GASb;IAEDU,KAAK,CAAC;MAAA,OAAM8C,aAAa,CAACxJ,KAAK;IAAA,GAAE,UAAC6J,GAAG,EAAK;MACxC,IAAIA,GAAG,EAAE;QACP3C,UAAU,CAAClH,KAAK,GAAG6J,GAAG,CAACxF,MAAM,GAAG,CAAC;QACjC,IAAIwF,GAAG,CAACxF,MAAM,GAAG,CAAC,EAAE;UAClB6C,UAAU,CAAClH,KAAK,GAAG,KAAK;QAC1B,CAAC,MAAM;UACLkH,UAAU,CAAClH,KAAK,GAAG,IAAI;QACzB;MACF;IACF,CAAC,EAAE;MAAE6M,SAAS,EAAE;IAAK,CAAC,CAAC;IACvBnG,KAAK,CAAC;MAAA,OAAMiE,cAAc,CAAC3K,KAAK;IAAA,GAAE,UAAC6J,GAAG,EAAK;MACzC,IAAIA,GAAG,EAAE;QACP1C,MAAM,CAACnH,KAAK,GAAG6J,GAAG,CAACxF,MAAM,GAAG,CAAC;QAC7B,IAAIwF,GAAG,CAACxF,MAAM,GAAG,CAAC,EAAE;UAClB8C,MAAM,CAACnH,KAAK,GAAG,KAAK;QACtB,CAAC,MAAM;UACLmH,MAAM,CAACnH,KAAK,GAAG,IAAI;QACrB;MACF;IACF,CAAC,EAAE;MAAE6M,SAAS,EAAE;IAAK,CAAC,CAAC;IACvBnG,KAAK,CAAC;MAAA,OAAMU,IAAI,CAACC,cAAc;IAAA,GAAE,UAACwC,GAAG,EAAK;MACxCmB,SAAS,CAAC,CAAC;IACb,CAAC,EAAE;MAAE6B,SAAS,EAAE;IAAK,CAAC,CAAC;IACvBnG,KAAK,CAAC;MAAA,OAAMU,IAAI,CAACG,gBAAgB;IAAA,GAAE,UAACuF,MAAM,EAAEC,MAAM,EAAK;MACrD,IAAID,MAAM,EAAE;QACVlF,UAAU,CAAC5H,KAAK,CAACoC,OAAO,CAAC,UAACJ,CAAC,EAAK;UAC9B,IAAIoF,IAAI,CAACC,cAAc,KAAKrF,CAAC,CAACiG,EAAE,EAAE;YAChCjG,CAAC,CAACkG,QAAQ,CAAC9F,OAAO,CAAC,UAAC4K,EAAE,EAAK;cAAE,IAAIA,EAAE,CAAC/E,EAAE,KAAK6E,MAAM,EAAE;gBAAE1F,IAAI,CAACI,oBAAoB,GAAGwF,EAAE,CAACvI,IAAI;cAAC;YAAE,CAAC,CAAC;UAC/F;QACF,CAAC,CAAC;QACFuG,SAAS,CAAC,CAAC;MACb,CAAC,MAAM;QAAE5D,IAAI,CAACI,oBAAoB,GAAG,EAAE;MAAC;MACxC,IAAIuF,MAAM,IAAID,MAAM,KAAK,EAAE,EAAE;QAAE9B,SAAS,CAAC,CAAC;MAAC;IAC7C,CAAC,EAAE;MAAE6B,SAAS,EAAE;IAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}