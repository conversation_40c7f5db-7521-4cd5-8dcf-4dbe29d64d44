{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SimilarityQuery\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"SimilarityQueryButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_select, {\n        modelValue: $setup.termYearId,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.termYearId = $event;\n        }),\n        placeholder: \"请选择届次\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.termYearData, function (item) {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.termYearId,\n              label: item.name,\n              value: item.termYearId\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"globalTable\", {\n      isGlobalTable: !$setup.props.type\n    }])\n  }, [_createVNode(_component_el_table, {\n    data: $setup.tableData\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        label: \"相似度\",\n        width: \"100\",\n        prop: \"similar\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"提案标题\",\n        \"min-width\": \"320\",\n        \"show-overflow-tooltip\": \"\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_link, {\n            onClick: function onClick($event) {\n              return $setup.handleDetails(scope.row);\n            },\n            type: \"primary\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString(scope.row.title), 1 /* TEXT */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"提案者\",\n        \"min-width\": \"120\",\n        prop: \"suggestionUserName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"界别\",\n        \"min-width\": \"120\"\n      }, {\n        default: _withCtx(function (scope) {\n          var _scope$row$sectorType;\n          return [_createTextVNode(_toDisplayString((_scope$row$sectorType = scope.row.sectorType) === null || _scope$row$sectorType === void 0 ? void 0 : _scope$row$sectorType.label), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"状态\",\n        \"min-width\": \"100\",\n        prop: \"currentStatus\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"届\",\n        width: \"100\",\n        prop: \"circlesName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"次\",\n        width: \"90\",\n        prop: \"boutName\"\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"])], 2 /* CLASS */), $setup.props.type ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    onClick: $setup.resetForm,\n    type: \"primary\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"返回修改提案\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    onClick: $setup.submitForm,\n    type: \"primary\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"直接提交\")]);\n    }),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"相似度详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SimilarityDetails\"], {\n        id: $setup.similarityId,\n        similarity: $setup.similarityNumber,\n        title: $setup.props.title,\n        content: $setup.props.content\n      }, null, 8 /* PROPS */, [\"id\", \"similarity\", \"title\", \"content\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "search", "_withCtx", "_component_el_select", "modelValue", "termYearId", "_cache", "$event", "placeholder", "clearable", "default", "_Fragment", "_renderList", "termYearData", "item", "_createBlock", "_component_el_option", "label", "name", "value", "_", "_createElementVNode", "_normalizeClass", "isGlobalTable", "props", "type", "_component_el_table", "data", "tableData", "_component_el_table_column", "width", "prop", "scope", "_component_el_link", "onClick", "handleDetails", "row", "_createTextVNode", "_toDisplayString", "title", "_scope$row$sectorType", "sectorType", "_hoisted_2", "_component_el_button", "resetForm", "submitForm", "_createCommentVNode", "_component_xyl_popup_window", "show", "id", "similarityId", "similarity", "similarityNumber", "content"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SimilarityQuery.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SimilarityQuery\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\">\r\n      <template #search>\r\n        <el-select v-model=\"termYearId\" placeholder=\"请选择届次\" clearable>\r\n          <el-option v-for=\"item in termYearData\" :key=\"item.termYearId\" :label=\"item.name\" :value=\"item.termYearId\" />\r\n        </el-select>\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\" :class=\"{ isGlobalTable: !props.type }\">\r\n      <el-table :data=\"tableData\">\r\n        <el-table-column label=\"相似度\" width=\"100\" prop=\"similar\" />\r\n        <el-table-column label=\"提案标题\" min-width=\"320\" show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            <el-link @click=\"handleDetails(scope.row)\" type=\"primary\">{{ scope.row.title }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"提案者\" min-width=\"120\" prop=\"suggestionUserName\" />\r\n        <el-table-column label=\"界别\" min-width=\"120\">\r\n          <template #default=\"scope\">{{ scope.row.sectorType?.label }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" min-width=\"100\" prop=\"currentStatus\" />\r\n        <el-table-column label=\"届\" width=\"100\" prop=\"circlesName\" />\r\n        <el-table-column label=\"次\" width=\"90\" prop=\"boutName\" />\r\n      </el-table>\r\n    </div>\r\n    <div class=\"SimilarityQueryButton\" v-if=\"props.type\">\r\n      <el-button @click=\"resetForm\" type=\"primary\">返回修改提案</el-button>\r\n      <el-button @click=\"submitForm\" type=\"primary\">直接提交</el-button>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"相似度详情\">\r\n      <SimilarityDetails :id=\"similarityId\" :similarity=\"similarityNumber\" :title=\"props.title\"\r\n        :content=\"props.content\" />\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SimilarityQuery' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\n// import { globalLocation } from 'common/config/location'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport SimilarityDetails from './SimilarityDetails.vue'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  title: { type: String, default: '' },\r\n  content: { type: String, default: '' },\r\n  type: { type: Boolean, default: true }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst termYearId = ref('')\r\nconst termYearData = ref([])\r\nconst tableData = ref([])\r\n\r\nconst similarityId = ref('')\r\nconst similarityNumber = ref('')\r\nconst show = ref(false)\r\n\r\nonMounted(() => {\r\n  handleQuery()\r\n  termYearTree()\r\n})\r\n\r\nconst termYearTree = async () => {\r\n  const res = await api.termYearTree({ termYearType: 'cppcc_member' })\r\n  var { data } = res\r\n  termYearData.value = []\r\n  data.forEach((item) => {\r\n    termYearData.value = [...termYearData.value, ...item.children]\r\n  })\r\n}\r\nconst handleQuery = () => {\r\n  tableBodyData()\r\n}\r\nconst handleReset = () => {\r\n  termYearId.value = ''\r\n  handleQuery()\r\n}\r\nconst tableBodyData = async () => {\r\n  const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区\r\n  const { data } = await api.similarity({\r\n    dirId: '60',\r\n    index: '1_2_',\r\n    id: props.id,\r\n    areaCode: AreaId,\r\n    content: props.content,\r\n    termYearId: termYearId.value,\r\n    status: '政协交办中,党委交办中,政府交办中,法院交办中,检察院交办中,两院交办中,办理中,已答复,满意度测评,重新办理,已办结'\r\n  })\r\n  tableData.value = data || []\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '相似文件详情', path: '/proposal/SuggestDetail', query: { id: item.id } }\r\n  })\r\n}\r\n// const contrast = (item) => {\r\n//   similarityId.value = item.id\r\n//   similarityNumber.value = item.similar\r\n//   show.value = true\r\n//   // window.open(`${globalLocation()}MicroAppContainer?name=proposal&path=TextQueryTool&id=${props.id}&contrastId=${item.id}`, '_blank')\r\n//   // window.open(`${globalLocation()}MicroAppContainer?name=proposal&path=SimilarityDetails&oneId=${props.id}&twoId=${item.id}`, '_blank')\r\n// }\r\nconst submitForm = () => {\r\n  emit('callback', true)\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SimilarityQuery {\r\n  width: 1100px;\r\n  height: calc(85vh - 52px);\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - ((var(--zy-height) * 2) + (var(--zy-distance-four) * 4)));\r\n  }\r\n\r\n  .globalTable.isGlobalTable {\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 3)));\r\n  }\r\n\r\n  .SimilarityQueryButton {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: center;\r\n    padding: var(--zy-distance-four);\r\n\r\n    .zy-el-button+.zy-el-button {\r\n      margin-left: 120px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAD9BC,GAAA;EA0BSD,KAAK,EAAC;;;;;;;;;;;uBAzBbE,mBAAA,CAiCM,OAjCNC,UAiCM,GAhCJC,YAAA,CAMoBC,4BAAA;IANAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IAAGC,YAAU,EAAEF,MAAA,CAAAG;;IAC7CC,MAAM,EAAAC,QAAA,CACf;MAAA,OAEY,CAFZR,YAAA,CAEYS,oBAAA;QANpBC,UAAA,EAI4BP,MAAA,CAAAQ,UAAU;QAJtC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAI4BV,MAAA,CAAAQ,UAAU,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,OAAO;QAACC,SAAS,EAAT;;QAJ5DC,OAAA,EAAAR,QAAA,CAKqB;UAAA,OAA4B,E,kBAAvCV,mBAAA,CAA6GmB,SAAA,QALvHC,WAAA,CAKoCf,MAAA,CAAAgB,YAAY,EALhD,UAK4BC,IAAI;iCAAtBC,YAAA,CAA6GC,oBAAA;cAApEzB,GAAG,EAAEuB,IAAI,CAACT,UAAU;cAAGY,KAAK,EAAEH,IAAI,CAACI,IAAI;cAAGC,KAAK,EAAEL,IAAI,CAACT;;;;QALzGe,CAAA;;;IAAAA,CAAA;MASIC,mBAAA,CAgBM;IAhBD/B,KAAK,EATdgC,eAAA,EASe,aAAa;MAAAC,aAAA,GAA2B1B,MAAA,CAAA2B,KAAK,CAACC;IAAI;MAC3D/B,YAAA,CAcWgC,mBAAA;IAdAC,IAAI,EAAE9B,MAAA,CAAA+B;EAAS;IAVhClB,OAAA,EAAAR,QAAA,CAWQ;MAAA,OAA0D,CAA1DR,YAAA,CAA0DmC,0BAAA;QAAzCZ,KAAK,EAAC,KAAK;QAACa,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC;UAC9CrC,YAAA,CAIkBmC,0BAAA;QAJDZ,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;;QACjCP,OAAO,EAAAR,QAAA,CAChB,UAAyF8B,KADlE;UAAA,QACvBtC,YAAA,CAAyFuC,kBAAA;YAA/EC,OAAK,WAALA,OAAKA,CAAA3B,MAAA;cAAA,OAAEV,MAAA,CAAAsC,aAAa,CAACH,KAAK,CAACI,GAAG;YAAA;YAAGX,IAAI,EAAC;;YAd5Df,OAAA,EAAAR,QAAA,CAcsE;cAAA,OAAqB,CAd3FmC,gBAAA,CAAAC,gBAAA,CAcyEN,KAAK,CAACI,GAAG,CAACG,KAAK,iB;;YAdxFnB,CAAA;;;QAAAA,CAAA;UAiBQ1B,YAAA,CAAyEmC,0BAAA;QAAxDZ,KAAK,EAAC,KAAK;QAAC,WAAS,EAAC,KAAK;QAACc,IAAI,EAAC;UAClDrC,YAAA,CAEkBmC,0BAAA;QAFDZ,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC;;QACzBP,OAAO,EAAAR,QAAA,CAAS,UAAiC8B,KAAnC;UAAA,IAAAQ,qBAAA;UAAA,QAnBnCH,gBAAA,CAAAC,gBAAA,EAAAE,qBAAA,GAmBwCR,KAAK,CAACI,GAAG,CAACK,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBvB,KAAK,iB;;QAnBnEG,CAAA;UAqBQ1B,YAAA,CAAmEmC,0BAAA;QAAlDZ,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC,KAAK;QAACc,IAAI,EAAC;UACjDrC,YAAA,CAA4DmC,0BAAA;QAA3CZ,KAAK,EAAC,GAAG;QAACa,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC;UAC5CrC,YAAA,CAAwDmC,0BAAA;QAAvCZ,KAAK,EAAC,GAAG;QAACa,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC;;;IAvBnDX,CAAA;gDA0B6CvB,MAAA,CAAA2B,KAAK,CAACC,IAAI,I,cAAnDjC,mBAAA,CAGM,OAHNkD,UAGM,GAFJhD,YAAA,CAA+DiD,oBAAA;IAAnDT,OAAK,EAAErC,MAAA,CAAA+C,SAAS;IAAEnB,IAAI,EAAC;;IA3BzCf,OAAA,EAAAR,QAAA,CA2BmD;MAAA,OAAMI,MAAA,QAAAA,MAAA,OA3BzD+B,gBAAA,CA2BmD,QAAM,E;;IA3BzDjB,CAAA;MA4BM1B,YAAA,CAA8DiD,oBAAA;IAAlDT,OAAK,EAAErC,MAAA,CAAAgD,UAAU;IAAEpB,IAAI,EAAC;;IA5B1Cf,OAAA,EAAAR,QAAA,CA4BoD;MAAA,OAAII,MAAA,QAAAA,MAAA,OA5BxD+B,gBAAA,CA4BoD,MAAI,E;;IA5BxDjB,CAAA;UAAA0B,mBAAA,gBA8BIpD,YAAA,CAGmBqD,2BAAA;IAjCvB3C,UAAA,EA8B+BP,MAAA,CAAAmD,IAAI;IA9BnC,uBAAA1C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA8B+BV,MAAA,CAAAmD,IAAI,GAAAzC,MAAA;IAAA;IAAEW,IAAI,EAAC;;IA9B1CR,OAAA,EAAAR,QAAA,CA+BM;MAAA,OAC6B,CAD7BR,YAAA,CAC6BG,MAAA;QADToD,EAAE,EAAEpD,MAAA,CAAAqD,YAAY;QAAGC,UAAU,EAAEtD,MAAA,CAAAuD,gBAAgB;QAAGb,KAAK,EAAE1C,MAAA,CAAA2B,KAAK,CAACe,KAAK;QACrFc,OAAO,EAAExD,MAAA,CAAA2B,KAAK,CAAC6B;;;IAhCxBjC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}