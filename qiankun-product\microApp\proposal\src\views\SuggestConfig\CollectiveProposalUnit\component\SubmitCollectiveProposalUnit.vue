<template>
  <div class="SubmitCollectiveProposalUnit">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             inline
             label-position="top"
             class="globalForm">
      <el-form-item label="单位名称"
                    prop="name">
        <el-input v-model="form.name"
                  placeholder="请输入单位名称"
                  clearable />
      </el-form-item>
      <el-form-item label="序号">
        <el-input v-model="form.sort"
                  maxlength="10"
                  show-word-limit
                  @input="form.sort = validNum(form.sort)"
                  placeholder="请输入序号"
                  clearable />
      </el-form-item>
      <el-form-item label="联系人">
        <el-input v-model="form.contactUser"
                  placeholder="请输入联系人"
                  clearable />
      </el-form-item>
      <el-form-item label="联系电话">
        <el-input v-model="form.contactPhone"
                  placeholder="请输入联系电话"
                  clearable />
      </el-form-item>
      <el-form-item label="类型"
                    prop="teamOfficeTheme"
                    class="globalFormTitle">
        <el-select v-model="form.teamOfficeTheme"
                   placeholder="请选择类型"
                   clearable>
          <el-option v-for="item in teamOfficeThemeData"
                     :key="item.key"
                     :label="item.name"
                     :value="item.key">
          </el-option>
        </el-select>
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary"
                   @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SubmitCollectiveProposalUnit' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { validNum } from 'common/js/utils.js'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  name: '',
  sort: '',
  contactUser: '',
  contactPhone: '',
  teamOfficeTheme: ''
})
const teamOfficeThemeData = ref([])
const rules = reactive({ name: [{ required: true, message: '请输入单位名称', trigger: ['blur', 'change'] }] })

onMounted(() => {
  dictionaryData()
  if (props.id) { teamOfficeInfo() }
})

/**
 * @description: 获取字典数据，用于填充模板类型下拉选项
 * @return {void}
 */
const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['team_office_theme'] })
  var { data } = res
  teamOfficeThemeData.value = data.team_office_theme
}
const teamOfficeInfo = async () => {
  const res = await api.teamOfficeInfo({ detailId: props.id })
  var { data } = res
  form.name = data.name
  form.sort = data.sort
  form.contactUser = data.contactUser
  form.contactPhone = data.contactPhone
  form.teamOfficeTheme = data.teamOfficeTheme.value
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(props.id ? '/teamOffice/edit' : '/teamOffice/add', {
    form: { id: props.id, name: form.name, sort: form.sort, contactUser: form.contactUser, contactPhone: form.contactPhone, teamOfficeTheme: form.teamOfficeTheme }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}
const resetForm = () => { emit('callback') }
</script>
<style lang="scss">
.SubmitCollectiveProposalUnit {
  width: 680px;
}
</style>
