<template>
  <div class="SubmitExamine" :style="'width:' + props.width">
    <div class="SuggestAssignDetailNameBody" v-if="props.name">
      <div class="SuggestAssignDetailName">
        <div>{{ props.name }}</div>
      </div>
    </div>
    <el-form ref="formRef" :model="form" inline :rules="rules" class="globalForm">
      <el-form-item label="提交人" v-if="!props.name" label-width="100" class="globalFormTitle">
        <span>{{ details.submitUserName }}</span>
        <span>{{ format(details.submitDate, 'YYYY-MM-DD') }}</span>
      </el-form-item>
      <el-form-item label="标题" v-if="!props.name" label-width="100" class="globalFormTitle">
        {{ details.title }}
      </el-form-item>
      <el-form-item label="交办" label-width="100" class="globalFormTitle">
        <el-radio-group v-model="form.state">
          <el-radio :label="1" size="large">交给办理单位</el-radio>
          <!-- <el-radio :label="2" size="large" v-if="underAreaList.length>0">转交下级</el-radio>
          <el-radio v-if="details.allowReport" :label="3" size="large">上报上级</el-radio> -->
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.state === 1" label="主办单位" label-width="100" prop="groupId">
        <el-select v-model="form.groupId" clearable>
          <el-option v-for="item in groupList" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.state === 1" label="协办单位" label-width="100" prop="assistGroupId">
        <el-select v-model="form.assistGroupId" multiple clearable>
          <el-option v-for="item in assistGroupList" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.state === 2" label="下级地区" label-width="100" prop="targetArea">
        <el-select v-model="form.targetArea">
          <el-option v-for="item in underAreaList" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="意见说明" label-width="100" class="globalFormTitle" prop="opinion">
        <el-input v-model="form.opinion" type="textarea" maxlength="800" rows="3" placeholder="请输入意见说明" clearable />
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default { name: "SubmitExamine" }
</script>
<script setup>
import api from '@/api'
import { reactive, onMounted, ref, computed } from 'vue'
import { format } from 'common/js/time.js'
import { ElMessage } from 'element-plus'
import { useStore } from "vuex";
const props = defineProps({
  id: { type: String, default: '' },
  name: { type: String, default: '' },
  width: { type: String, default: '800px' }
})
const emit = defineEmits(['callback'])
const store = useStore()
const formRef = ref()
const user = computed(() => store.getters.getUserFn)
const form = reactive({
  opinion: '', // 意见
  state: 1,
  groupId: '', // 主办单位
  assistGroupId: '', // 协办单位
  targetArea: ''
})
const details = ref({})
const rules = reactive({
  groupId: [{ required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }],
  targetArea: [{ required: true, message: '请选择下级地区', trigger: ['blur', 'change'] }],
})
const groupList = ref([])
const assistGroupList = ref([])
const underAreaList = ref([])
onMounted(() => {
  if (props.id) {
    microAdviceUnder()
    microAdviceInfo()
    microFlowGroup()
  }
})

const microAdviceInfo = async () => {
  const res = await api.microAdviceInfo({ detailId: props.id })
  var { data } = res
  details.value = data
}
const microFlowGroup = async () => {
  const res = await api.microAdviceGroupSelector({
    "form": {
    }
  })
  var { data } = res
  groupList.value = data
}
const microAdviceUnder = async () => {
  const res = await api.microAdviceUnderAreaSelector()
  var { data } = res
  underAreaList.value = data
}

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请输入必填项！' }) }
  })
}
const globalJson = async () => {
  let params = {}
  if (form.state === 1) {
    params = {
      microAdviceId: props.id,
      nextNodeId: 'pushNegotiateGroup',
      record: {
        opinion: form.opinion,
        sourceArea: user.value.areaId,
        groupId: form.groupId
      },
      assistGroupId: form.assistGroupId
    }
  }
  const { code } = await api.complete(params)
  if (code === 200) {
    ElMessage({ type: 'success', message: '交办成功' })
    emit('callback')
  }
}
const resetForm = () => { emit('callback') }
</script>

<style scoped lang="scss">
.SubmitExamine {

  //width: 800px;
  .SuggestAssignDetailNameBody {
    padding: 0 var(--zy-distance-one);
    padding-top: var(--zy-distance-one);

    .SuggestAssignDetailName {
      width: 100%;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      position: relative;
      text-align: center;

      div {
        display: inline-block;
        background-color: #fff;
        position: relative;
        z-index: 2;
        padding: 0 20px;
      }

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 1px;
        background-color: var(--zy-el-color-primary);
      }
    }
  }

  .globalFormTitle {
    span {
      margin-right: 10px;
    }
  }
}
</style>
