{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, reactive, onActivated, computed, watch, onDeactivated, onBeforeUnmount } from 'vue';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { format } from 'common/js/time.js';\nimport { ElMessage } from 'element-plus';\nimport { exportWordHtmlObj } from \"common/config/MicroGlobal\";\nimport CommunicationSituation from '@/views/SuggestDetail/CommunicationSituation/CommunicationSituation.vue';\nimport CommunicationSituationSubmit from '@/views/SuggestDetail/CommunicationSituation/CommunicationSituationSubmit.vue';\nimport ApplyForAnswer from './component/ApplyForAnswer.vue';\nimport UnitApplyForAnswerRecords from './component/UnitApplyForAnswerRecords.vue';\nimport ApplyForAdjust from './component/ApplyForAdjust.vue';\nimport UnitApplyForAdjustRecords from './component/UnitApplyForAdjustRecords.vue';\nimport SubmitSuggestReply from './component/SubmitSuggestReply.vue';\nimport ApplyForTrackTransact from './component/ApplyForTrackTransact.vue';\nimport SubmitSuggestTrackTransactReply from './component/SubmitSuggestTrackTransactReply.vue';\nimport SuggestReplyDetail from '@/views/SuggestDetail/SuggestReplyDetail/SuggestReplyDetail.vue';\nimport SegreeSatisfactionDetail from '@/views/SuggestDetail/SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue';\nvar __default__ = {\n  name: \"UnitSuggestDetail\"\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    type: {\n      type: String,\n      default: ''\n    },\n    details: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    transactUnitObj: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    satisfactions: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    allhandleOfficeInfos: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    setExtResult: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    suggestionOfficeShow: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['refresh', 'callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var formRef = ref();\n    var form = reactive({\n      sign: '1',\n      adjustReason: '',\n      hopeHandleOffice: ''\n    });\n    var rules = reactive({\n      sign: [{\n        required: true,\n        message: '请选择是否签收',\n        trigger: ['blur', 'change']\n      }],\n      adjustReason: [{\n        required: true,\n        message: '请输入调整理由',\n        trigger: ['blur', 'change']\n      }],\n      hopeHandleOffice: [{\n        required: true,\n        message: '请输入希望办理单位',\n        trigger: ['blur', 'change']\n      }]\n    });\n    var transactUnitObj = computed(function () {\n      return props.transactUnitObj;\n    });\n    var allhandleOfficeInfos = computed(function () {\n      return props.allhandleOfficeInfos;\n    });\n    var delaysInfo = computed(function () {\n      return props.transactUnitObj.delays[props.transactUnitObj.delays.length - 1];\n    });\n    var adjustsInfo = computed(function () {\n      return props.transactUnitObj.adjusts[props.transactUnitObj.adjusts.length - 1];\n    });\n    var tracesInfo = computed(function () {\n      return props.transactUnitObj.traces.filter(function (v) {\n        return !v.hasAnswer;\n      })[0] || {};\n    });\n    var satisfactions = computed(function () {\n      return props.satisfactions;\n    });\n    var handlerOffice = computed(function () {\n      return props.transactUnitObj.handlerOffice;\n    });\n    var adjustTime = computed(function () {\n      var _props$transactUnitOb;\n      return (_props$transactUnitOb = props.transactUnitObj) === null || _props$transactUnitOb === void 0 ? void 0 : _props$transactUnitOb.officeAdjustStopDate;\n    });\n    var answerTime = computed(function () {\n      var _props$transactUnitOb2;\n      return (_props$transactUnitOb2 = props.transactUnitObj) === null || _props$transactUnitOb2 === void 0 ? void 0 : _props$transactUnitOb2.officeAnswerStopDate;\n    });\n    var transactId = computed(function () {\n      var _props$transactUnitOb3;\n      return (_props$transactUnitOb3 = props.transactUnitObj) === null || _props$transactUnitOb3 === void 0 || (_props$transactUnitOb3 = _props$transactUnitOb3.handlerOffice) === null || _props$transactUnitOb3 === void 0 ? void 0 : _props$transactUnitOb3.id;\n    });\n    var transactStatus = computed(function () {\n      var _props$transactUnitOb4;\n      return (_props$transactUnitOb4 = props.transactUnitObj) === null || _props$transactUnitOb4 === void 0 || (_props$transactUnitOb4 = _props$transactUnitOb4.handlerOffice) === null || _props$transactUnitOb4 === void 0 ? void 0 : _props$transactUnitOb4.currentHandleStatus;\n    });\n    var handleOfficeType = computed(function () {\n      var _props$transactUnitOb5;\n      return (_props$transactUnitOb5 = props.transactUnitObj) === null || _props$transactUnitOb5 === void 0 || (_props$transactUnitOb5 = _props$transactUnitOb5.handlerOffice) === null || _props$transactUnitOb5 === void 0 ? void 0 : _props$transactUnitOb5.handleOfficeType;\n    });\n    var isReply = computed(function () {\n      var _props$transactUnitOb6;\n      return ((_props$transactUnitOb6 = props.transactUnitObj.answers) === null || _props$transactUnitOb6 === void 0 || (_props$transactUnitOb6 = _props$transactUnitOb6.filter(function (v) {\n        return v.submitAnswerType === 'direct';\n      })) === null || _props$transactUnitOb6 === void 0 ? void 0 : _props$transactUnitOb6.length) || 0;\n    });\n    var isConclude = computed(function () {\n      return props.type === 'unitConclude';\n    });\n    var show = ref(false);\n    var isShow = ref(false);\n    var isAdjust = ref(false);\n    var isAnswer = ref(false);\n    var isAnswerShow = ref(false);\n    var isAnswerRecords = ref(false);\n    var isAdjustShow = ref(false);\n    var isAdjustsRecords = ref(false);\n    var isReplyShow = ref(false);\n    var isTrackTransactReply = ref(false);\n    var isTrackTransactShow = ref(false);\n    var replyId = ref(\"\");\n    var replyDetailShow = ref(false);\n    var ifShow = ref(false);\n    var satisfactionsId = ref(\"\"); // 满意度测评ID\n\n    var handleCondition = ref('');\n    var handleStatusContent = ref('');\n    var suggestionHandleStatus = ref([]);\n    var AreaId = ref('');\n    var isTime = ref(false);\n    onActivated(function () {\n      dictionaryData();\n      globalReadConfig();\n      AreaId.value = sessionStorage.getItem('AreaId') || '';\n    });\n    var isAssignAnswer = ref(false);\n    var globalReadConfig = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$globalRead, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.globalReadConfig({\n                codes: ['proposal_check_assist_answer']\n              });\n            case 2:\n              _yield$api$globalRead = _context.sent;\n              data = _yield$api$globalRead.data;\n              isAssignAnswer.value = (data === null || data === void 0 ? void 0 : data.proposal_check_assist_answer) === 'true';\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function globalReadConfig() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var disabledFunc = function disabledFunc() {\n      if (isAssignAnswer.value) {\n        if (handleOfficeType.value == 'main') {\n          var _transactUnitObj$valu;\n          return !((_transactUnitObj$valu = transactUnitObj.value.communications) !== null && _transactUnitObj$valu !== void 0 && _transactUnitObj$valu.length && allhandleOfficeInfos.value.filter(function (v) {\n            return v.handlerOffice.currentHandleStatus === 'has_answer' && v.handlerOffice.handleOfficeType === 'assist';\n          }).length === allhandleOfficeInfos.value.filter(function (v) {\n            return v.handlerOffice.handleOfficeType === 'assist';\n          }).length);\n        } else if (handleOfficeType.value === 'assist') {\n          return false;\n        } else {\n          var _transactUnitObj$valu2;\n          return !((_transactUnitObj$valu2 = transactUnitObj.value.communications) !== null && _transactUnitObj$valu2 !== void 0 && _transactUnitObj$valu2.length);\n        }\n      } else {\n        var _transactUnitObj$valu3;\n        return !((_transactUnitObj$valu3 = transactUnitObj.value.communications) !== null && _transactUnitObj$valu3 !== void 0 && _transactUnitObj$valu3.length) && handleOfficeType.value !== 'assist';\n      }\n    };\n    var dictionaryData = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.dictionaryData({\n                dictCodes: ['suggestion_handle_status']\n              });\n            case 2:\n              res = _context2.sent;\n              data = res.data;\n              suggestionHandleStatus.value = data.suggestion_handle_status;\n              qiankunMicro.setGlobalState({\n                AiChatCode: 'ai-proposal-handle-chat'\n              });\n              qiankunMicro.setGlobalState({\n                AiChatWindow: true\n              });\n            case 7:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function dictionaryData() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var consultationDownLoad = function consultationDownLoad(row, setExtResult) {\n      var _row$termYear, _row$termYear2;\n      console.log('row===>', row);\n      console.log('setExtResult===>', setExtResult);\n      row.organizer = handlerOffice.value.flowHandleOfficeName || \"\"; // 主办单位\n      row.officePhone = row.submitUserInfo.officePhone || \"\"; // 办公电话\n      row.mobile = row.submitUserInfo.mobile || \"\"; // 手机\n      row.callAddress = row.submitUserInfo.callAddress || \"\"; // 通讯地址\n      var assistData = setExtResult.filter(function (item) {\n        return item.handleOfficeType === \"assist\";\n      });\n      var assistNames = assistData.map(function (item) {\n        return item.handleOfficeName;\n      }).join(\",\");\n      row.CoOrganizer = assistNames; // 协办单位\n      var mainData = setExtResult.filter(function (item) {\n        return item.handleOfficeType === \"main\";\n      });\n      row.telephone = mainData[0] ? mainData[0].telephone : '';\n      row.circlesType = (_row$termYear = row.termYear) === null || _row$termYear === void 0 || (_row$termYear = _row$termYear.circlesType) === null || _row$termYear === void 0 ? void 0 : _row$termYear.name;\n      row.boutType = (_row$termYear2 = row.termYear) === null || _row$termYear2 === void 0 || (_row$termYear2 = _row$termYear2.boutType) === null || _row$termYear2 === void 0 ? void 0 : _row$termYear2.name;\n      exportWordHtmlObj({\n        code: \"unitSuggestDetail\",\n        name: \"征询意见表模板下载\",\n        key: \"content\",\n        data: row\n      });\n    };\n    var beforeClose = function beforeClose(cb) {\n      emit(\"refresh\");\n      cb();\n    };\n    var callback = function callback() {\n      emit('refresh');\n      isShow.value = false;\n    };\n    var adjustCallback = function adjustCallback(type) {\n      isAdjust.value = type;\n    };\n    var answerCallback = function answerCallback(type) {\n      isAnswer.value = type;\n    };\n    var handleCallback = function handleCallback(type) {\n      isAnswerShow.value = false;\n      isAdjustShow.value = false;\n      isReplyShow.value = false;\n      isTrackTransactShow.value = false;\n      if (type) {\n        emit(\"callback\");\n      }\n    };\n    var handleConditionClick = function handleConditionClick() {\n      globalJson();\n    };\n    var globalJson = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _yield$api$globalJson, code;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.globalJson(\"/proposal/handle/status\", {\n                handlingPortionId: transactId.value,\n                suggestionHandleStatus: handleCondition.value,\n                handleStatusContent: handleStatusContent.value\n              });\n            case 2:\n              _yield$api$globalJson = _context3.sent;\n              code = _yield$api$globalJson.code;\n              if (code === 200) {\n                ElMessage({\n                  type: \"success\",\n                  message: \"更新成功\"\n                });\n                emit(\"refresh\");\n              }\n            case 5:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function globalJson() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var handleReply = function handleReply(item) {\n      replyId.value = item.id;\n      replyDetailShow.value = true;\n    };\n    var handleTrackTransact = function handleTrackTransact() {\n      isTrackTransactShow.value = true;\n    };\n    var handleSatisfactions = function handleSatisfactions(item) {\n      satisfactionsId.value = item.id;\n      ifShow.value = true;\n    };\n    onDeactivated(function () {\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'test_chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatContent: ''\n      });\n      qiankunMicro.setGlobalState({\n        AiChatWindow: false\n      });\n    });\n    onBeforeUnmount(function () {\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'test_chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatContent: ''\n      });\n      qiankunMicro.setGlobalState({\n        AiChatWindow: false\n      });\n    });\n    var checkTime = function checkTime(time) {\n      // 获取传入时间、现在时间的时间戳\n      var date = new Date(time).getTime();\n      var nowDate = new Date().getTime();\n      // 判断现在的时间是否大于传入时间\n      return nowDate < date;\n    };\n    watch(function () {\n      return [props.transactUnitObj, props.details];\n    }, function () {\n      var _props$transactUnitOb7, _props$details, _props$transactUnitOb8, _props$transactUnitOb9;\n      isTime.value = checkTime((_props$transactUnitOb7 = props.transactUnitObj) === null || _props$transactUnitOb7 === void 0 ? void 0 : _props$transactUnitOb7.officeConfirmStopDate);\n      qiankunMicro.setGlobalState({\n        AiChatContent: ((_props$details = props.details) === null || _props$details === void 0 ? void 0 : _props$details.content) || ''\n      });\n      handleCondition.value = (_props$transactUnitOb8 = props.transactUnitObj.handlerOffice) === null || _props$transactUnitOb8 === void 0 || (_props$transactUnitOb8 = _props$transactUnitOb8.suggestionHandleStatus) === null || _props$transactUnitOb8 === void 0 ? void 0 : _props$transactUnitOb8.value;\n      handleStatusContent.value = (_props$transactUnitOb9 = props.transactUnitObj.handlerOffice) === null || _props$transactUnitOb9 === void 0 ? void 0 : _props$transactUnitOb9.handleStatusContent;\n    }, {\n      immediate: true\n    });\n    var submitForm = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(formEl) {\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              if (formEl) {\n                _context4.next = 2;\n                break;\n              }\n              return _context4.abrupt(\"return\");\n            case 2:\n              _context4.next = 4;\n              return formEl.validate(function (valid, fields) {\n                if (valid) {\n                  if (form.sign === '1') {\n                    handlingPortionConfirm();\n                  } else {\n                    handingPortionAdjust();\n                  }\n                } else {\n                  ElMessage({\n                    type: 'warning',\n                    message: '请根据提示信息完善字段内容！'\n                  });\n                }\n              });\n            case 4:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function submitForm(_x) {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handlingPortionConfirm = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var _yield$api$handlingPo, code;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.next = 2;\n              return api.handlingPortionConfirm({\n                form: {\n                  id: transactId.value\n                }\n              });\n            case 2:\n              _yield$api$handlingPo = _context5.sent;\n              code = _yield$api$handlingPo.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '签收成功'\n                });\n                emit('callback');\n              }\n            case 5:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function handlingPortionConfirm() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var handingPortionAdjust = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var _yield$api$globalJson2, code;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.next = 2;\n              return api.globalJson('/cppcc/handingPortionAdjust/add', {\n                form: {\n                  handlingPortionId: transactId.value,\n                  suggestionId: props.id,\n                  adjustReason: form.adjustReason,\n                  hopeHandleOffice: form.hopeHandleOffice\n                }\n              });\n            case 2:\n              _yield$api$globalJson2 = _context6.sent;\n              code = _yield$api$globalJson2.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '申请成功'\n                });\n                emit('callback');\n              }\n            case 5:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function handingPortionAdjust() {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var resetForm = function resetForm() {\n      emit('callback');\n    };\n    var __returned__ = {\n      props,\n      emit,\n      formRef,\n      form,\n      rules,\n      transactUnitObj,\n      allhandleOfficeInfos,\n      delaysInfo,\n      adjustsInfo,\n      tracesInfo,\n      satisfactions,\n      handlerOffice,\n      adjustTime,\n      answerTime,\n      transactId,\n      transactStatus,\n      handleOfficeType,\n      isReply,\n      isConclude,\n      show,\n      isShow,\n      isAdjust,\n      isAnswer,\n      isAnswerShow,\n      isAnswerRecords,\n      isAdjustShow,\n      isAdjustsRecords,\n      isReplyShow,\n      isTrackTransactReply,\n      isTrackTransactShow,\n      replyId,\n      replyDetailShow,\n      ifShow,\n      satisfactionsId,\n      handleCondition,\n      handleStatusContent,\n      suggestionHandleStatus,\n      AreaId,\n      isTime,\n      isAssignAnswer,\n      globalReadConfig,\n      disabledFunc,\n      dictionaryData,\n      consultationDownLoad,\n      beforeClose,\n      callback,\n      adjustCallback,\n      answerCallback,\n      handleCallback,\n      handleConditionClick,\n      globalJson,\n      handleReply,\n      handleTrackTransact,\n      handleSatisfactions,\n      checkTime,\n      submitForm,\n      handlingPortionConfirm,\n      handingPortionAdjust,\n      resetForm,\n      get api() {\n        return api;\n      },\n      ref,\n      reactive,\n      onActivated,\n      computed,\n      watch,\n      onDeactivated,\n      onBeforeUnmount,\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get format() {\n        return format;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get exportWordHtmlObj() {\n        return exportWordHtmlObj;\n      },\n      CommunicationSituation,\n      CommunicationSituationSubmit,\n      ApplyForAnswer,\n      UnitApplyForAnswerRecords,\n      ApplyForAdjust,\n      UnitApplyForAdjustRecords,\n      SubmitSuggestReply,\n      ApplyForTrackTransact,\n      SubmitSuggestTrackTransactReply,\n      SuggestReplyDetail,\n      SegreeSatisfactionDetail\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "reactive", "onActivated", "computed", "watch", "onDeactivated", "onBeforeUnmount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "format", "ElMessage", "exportWordHtmlObj", "CommunicationSituation", "CommunicationSituationSubmit", "ApplyForAnswer", "UnitApplyForAnswerRecords", "ApplyForAdjust", "UnitApplyForAdjustRecords", "SubmitSuggestReply", "ApplyForTrackTransact", "SubmitSuggestTrackTransactReply", "SuggestReplyDetail", "SegreeSatisfactionDetail", "__default__", "props", "__props", "emit", "__emit", "formRef", "form", "sign", "adjustReason", "hopeHandleOffice", "rules", "required", "message", "trigger", "transactUnitObj", "allhandleOfficeInfos", "delaysInfo", "delays", "adjustsInfo", "adjusts", "tracesInfo", "traces", "filter", "hasAnswer", "satisfactions", "handlerOffice", "adjustTime", "_props$transactUnitOb", "officeAdjustStopDate", "answerTime", "_props$transactUnitOb2", "officeAnswerStopDate", "transactId", "_props$transactUnitOb3", "id", "transactStatus", "_props$transactUnitOb4", "currentHandleStatus", "handleOfficeType", "_props$transactUnitOb5", "isReply", "_props$transactUnitOb6", "answers", "submitAnswerType", "isConclude", "show", "isShow", "isAdjust", "isAnswer", "isAnswerShow", "isAnswerRecords", "isAdjustShow", "isAdjustsRecords", "isReplyShow", "isTrackTransactReply", "isTrackTransactShow", "replyId", "replyDetailShow", "ifShow", "satisfactionsId", "handleCondition", "handleStatusContent", "suggestionHandleStatus", "AreaId", "isTime", "dictionaryData", "globalReadConfig", "sessionStorage", "getItem", "isAssignAnswer", "_ref2", "_callee", "_yield$api$globalRead", "data", "_callee$", "_context", "codes", "proposal_check_assist_answer", "disabledFunc", "_transactUnitObj$valu", "communications", "_transactUnitObj$valu2", "_transactUnitObj$valu3", "_ref3", "_callee2", "res", "_callee2$", "_context2", "dictCodes", "suggestion_handle_status", "setGlobalState", "AiChatCode", "AiChatWindow", "consultationDownLoad", "row", "setExtResult", "_row$termYear", "_row$termYear2", "console", "log", "organizer", "flowHandleOfficeName", "officePhone", "submitUserInfo", "mobile", "call<PERSON>dd<PERSON>", "assistData", "item", "assistNames", "map", "handleOfficeName", "join", "CoOrganizer", "mainData", "telephone", "circlesType", "termYear", "boutType", "code", "key", "beforeClose", "cb", "callback", "adjustCallback", "answerCallback", "handleCallback", "handleConditionClick", "globalJson", "_ref4", "_callee3", "_yield$api$globalJson", "_callee3$", "_context3", "handlingPortionId", "handleReply", "handleTrackTransact", "handleSatisfactions", "AiChatContent", "checkTime", "time", "date", "Date", "getTime", "nowDate", "details", "_props$transactUnitOb7", "_props$details", "_props$transactUnitOb8", "_props$transactUnitOb9", "officeConfirmStopDate", "content", "immediate", "submitForm", "_ref5", "_callee4", "formEl", "_callee4$", "_context4", "validate", "valid", "fields", "handlingPortionConfirm", "handingPortionAdjust", "_x", "_ref6", "_callee5", "_yield$api$handlingPo", "_callee5$", "_context5", "_ref7", "_callee6", "_yield$api$globalJson2", "_callee6$", "_context6", "suggestionId", "resetForm"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/UnitSuggestDetail/UnitSuggestDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"UnitSuggestDetail\">\r\n    <div class=\"SuggestDetailProcessInfo\" v-if=\"transactUnitObj.delays?.length\">\r\n      <div class=\"SuggestLabelName\">\r\n        申请延期记录\r\n        <div class=\"SuggestLabelNameButton\">\r\n          <el-button @click=\"isAnswerRecords = !isAnswerRecords\" v-if=\"transactUnitObj.delays?.length > 1\"\r\n            type=\"primary\">\r\n            查看更多延期记录\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <global-info>\r\n        <global-info-line>\r\n          <global-info-item label=\"申请单位\">{{\r\n            handlerOffice?.flowHandleOfficeName\r\n          }}</global-info-item>\r\n          <global-info-item label=\"申请时间\">{{\r\n            format(delaysInfo.createDate)\r\n          }}</global-info-item>\r\n        </global-info-line>\r\n        <global-info-line>\r\n          <global-info-item label=\"答复截止时间\">{{\r\n            format(delaysInfo.lastAnswerAdjustDate)\r\n          }}</global-info-item>\r\n          <global-info-item label=\"申请答复截止时间\">{{\r\n            format(delaysInfo.lastApplyAdjustDate)\r\n          }}</global-info-item>\r\n        </global-info-line>\r\n        <global-info-item label=\"申请延期理由\">\r\n          <pre>{{ delaysInfo.delayReason }}</pre>\r\n        </global-info-item>\r\n        <global-info-item label=\"是否同意延期申请\">\r\n          {{ delaysInfo.verifyStatus ? (delaysInfo.verifyStatus === 1 ? '同意申请' : '驳回') : '待审查' }}\r\n        </global-info-item>\r\n        <global-info-item v-if=\"delaysInfo.verifyStatus === 2\" label=\"驳回理由\">\r\n          <pre>{{ delaysInfo.noPassReason }}</pre>\r\n        </global-info-item>\r\n      </global-info>\r\n    </div>\r\n    <div class=\"SuggestDetailProcessInfo\" v-if=\"transactUnitObj.adjusts?.length\">\r\n      <div class=\"SuggestLabelName\">\r\n        申请调整记录\r\n        <div class=\"SuggestLabelNameButton\">\r\n          <el-button @click=\"isAdjustsRecords = !isAdjustsRecords\" v-if=\"transactUnitObj.adjusts?.length > 1\"\r\n            type=\"primary\">\r\n            查看更多调整记录\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <global-info>\r\n        <global-info-line>\r\n          <global-info-item label=\"申请单位\">{{\r\n            handlerOffice?.flowHandleOfficeName\r\n          }}</global-info-item>\r\n          <global-info-item label=\"申请时间\">{{\r\n            format(adjustsInfo.createDate)\r\n          }}</global-info-item>\r\n        </global-info-line>\r\n        <global-info-item label=\"申请调整理由\">\r\n          <pre>{{ adjustsInfo.adjustReason }}</pre>\r\n        </global-info-item>\r\n        <global-info-item label=\"希望办理单位\">\r\n          <pre>{{ adjustsInfo.hopeHandleOffice }}</pre>\r\n        </global-info-item>\r\n        <global-info-item label=\"是否同意调整申请\">\r\n          {{ adjustsInfo.verifyStatus ? (adjustsInfo.verifyStatus === 1 ? '同意申请' : '驳回') : '待审查' }}\r\n        </global-info-item>\r\n        <global-info-item v-if=\"adjustsInfo.verifyStatus\" :label=\"adjustsInfo.verifyStatus === 1 ? '同意调整意见' : '驳回理由'\">\r\n          <pre>{{ adjustsInfo.noPassReason }}</pre>\r\n        </global-info-item>\r\n      </global-info>\r\n    </div>\r\n    <div class=\"SuggestTransactDetailNameBody\">\r\n      <div class=\"SuggestTransactDetailName\">\r\n        <div>{{ props.type === 'unitPreAssign' ? '预交办提案签收' : '提案办理' }}</div>\r\n      </div>\r\n    </div>\r\n    <template v-if=\"props.type === 'unitPreAssign'\">\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n        <el-form-item label=\"是否签收\" prop=\"sign\" class=\"globalFormTitle\">\r\n          <el-radio-group v-model=\"form.sign\" :disabled=\"!isTime\">\r\n            <el-radio key=\"1\" label=\"1\">签收</el-radio>\r\n            <el-radio key=\"0\" label=\"0\">申请调整</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"调整理由\" v-if=\"form.sign === '0'\" prop=\"adjustReason\" class=\"globalFormTitle\">\r\n          <el-input v-model=\"form.adjustReason\" placeholder=\"请输入调整理由\" type=\"textarea\" :rows=\"5\" :disabled=\"!isTime\"\r\n            clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"希望办理单位\" v-if=\"form.sign === '0'\" prop=\"hopeHandleOffice\" class=\"globalFormTitle\">\r\n          <el-input v-model=\"form.hopeHandleOffice\" placeholder=\"请输入希望办理单位\" type=\"textarea\" :rows=\"5\"\r\n            :disabled=\"!isTime\" clearable />\r\n        </el-form-item>\r\n        <div class=\"globalFormButton\">\r\n          <el-button type=\"primary\" @click=\"submitForm(formRef)\" :disabled=\"!isTime\">提交</el-button>\r\n          <el-button @click=\"resetForm\" :disabled=\"!isTime\">取消</el-button>\r\n        </div>\r\n      </el-form>\r\n    </template>\r\n    <template v-else>\r\n      <div class=\"SuggestTransactBody\">\r\n        <template v-if=\"suggestionOfficeShow\">\r\n          <global-info>\r\n            <global-info-item label=\"办理单位\">{{ handlerOffice?.flowHandleOfficeName }}</global-info-item>\r\n            <global-info-item label=\"调整截止时间\" class=\"transactDetail\">\r\n              <div class=\"transactDetailBody\">\r\n                <div class=\"transactDetailInfo\">\r\n                  {{ format(adjustTime) }}\r\n                  （\r\n                  <global-countdown :time=\"adjustTime\" @callback=\"adjustCallback\"></global-countdown>\r\n                  ）\r\n                </div>\r\n                <div class=\"transactDetailButton\" v-if=\"isAdjust && transactStatus !== 'trace'\">\r\n                  <el-button @click=\"isAdjustShow = !isAdjustShow\"\r\n                    :disabled=\"transactUnitObj.isReply || transactUnitObj.isAdjusts || transactStatus !== 'handling'\"\r\n                    type=\"primary\">\r\n                    申请调整办理单位\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </global-info-item>\r\n            <global-info-item label=\"答复截止时间\" class=\"transactDetail\">\r\n              <div class=\"transactDetailBody\">\r\n                <div class=\"transactDetailInfo\">\r\n                  {{ format(answerTime) }}\r\n                  （\r\n                  <global-countdown :time=\"answerTime\" @callback=\"answerCallback\"></global-countdown>\r\n                  ）\r\n                </div>\r\n                <div class=\"transactDetailButton\" v-if=\"isAnswer && transactStatus !== 'trace'\">\r\n                  <el-button @click=\"isAnswerShow = !isAnswerShow\"\r\n                    :disabled=\"transactUnitObj.isReply || transactUnitObj.isDelays || transactStatus !== 'handling'\"\r\n                    type=\"primary\">\r\n                    申请延期答复\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </global-info-item>\r\n            <global-info-item label=\"办理情况（仅供标记）\" class=\"transactDetail\">\r\n              <div class=\"transactDetailBody\">\r\n                <div class=\"transactDetailInfo\">\r\n                  <el-select v-model=\"handleCondition\" :disabled=\"transactStatus === 'trace'\" placeholder=\"请选择内部流程状态\"\r\n                    clearable>\r\n                    <el-option v-for=\"item in suggestionHandleStatus\" :key=\"item.key\" :label=\"item.name\"\r\n                      :value=\"item.key\" />\r\n                  </el-select>\r\n                  <el-input v-model=\"handleStatusContent\" :disabled=\"transactStatus === 'trace'\" placeholder=\"请输入内容\"\r\n                    type=\"textarea\" :rows=\"5\" clearable />\r\n                </div>\r\n                <div class=\"transactDetailButton\" v-if=\"transactStatus !== 'trace'\">\r\n                  <el-button @click=\"handleConditionClick\" type=\"primary\">更新办理情况</el-button>\r\n                </div>\r\n              </div>\r\n            </global-info-item>\r\n            <global-info-item label=\"征询意见表模板下载\" class=\"transactDetail\">\r\n              <div class=\"transactDetailBody\">\r\n                <div class=\"transactDetailInfo\">\r\n                  <el-link @click=\"consultationDownLoad(details, setExtResult)\" type=\"primary\">{{ details.serialNumber\r\n                  }}征询意见表.docx</el-link>\r\n                </div>\r\n              </div>\r\n            </global-info-item>\r\n            <global-info-item label=\"沟通情况\" class=\"transactDetail\">\r\n              <div class=\"transactDetailBody\">\r\n                <div class=\"transactDetailInfo\">\r\n                  <el-link @click=\"show = !show\" type=\"primary\">查看办理单位与委员沟通情况</el-link>\r\n                </div>\r\n                <div class=\"transactDetailButton\" v-if=\"transactStatus !== 'trace'\">\r\n                  <el-button @click=\"isShow = !isShow\" :disabled=\"isConclude\" type=\"primary\">添加沟通情况</el-button>\r\n                </div>\r\n              </div>\r\n            </global-info-item>\r\n            <global-info-item label=\"答复意见\" class=\"transactDetail\">\r\n              <div class=\"transactDetailBody\">\r\n                <div class=\"transactDetailInfo\">\r\n                  <div v-for=\"item in transactUnitObj.answers\" :key=\"item.id\">\r\n                    <el-link @click=\"handleReply(item)\" type=\"primary\">\r\n                      查看{{ handlerOffice?.flowHandleOfficeName }}的答复信息\r\n                      {{ format(item.answerDate) }}\r\n                      <span v-if=\"item.submitAnswerType === 'history'\">（历史答复）</span>\r\n                      <span v-if=\"item.submitAnswerType === 'history_trace'\">（历史跟踪办理答复）</span>\r\n                      <span v-if=\"item.submitAnswerType === 'trace'\">（跟踪办理答复）</span>\r\n                      <span v-if=\"item.submitAnswerType === 'direct'\">（最终答复）</span>\r\n                      {{ item.suggestionAnswerType?.label }}\r\n                    </el-link>\r\n                  </div>\r\n                </div>\r\n                <div class=\"transactDetailButton\">\r\n                  <span v-if=\"transactStatus === 'handling' && !isConclude\"\r\n                    style=\"font-size: 12px; color: red\">请先添加沟通情况后再填写答复文件</span>\r\n                  <el-button @click=\"isReplyShow = !isReplyShow\" v-if=\"transactStatus === 'handling' && !isConclude\"\r\n                    :disabled=\"disabledFunc()\" type=\"primary\">填写答复文件</el-button>\r\n                  <el-button @click=\"isReplyShow = !isReplyShow\" v-if=\"transactStatus === 'has_answer' && !isConclude\"\r\n                    :disabled=\"!transactUnitObj.communications?.length &&\r\n                      handleOfficeType !== 'assist'\r\n                      \" type=\"primary\">编辑答复文件</el-button>\r\n                  <el-button @click=\"isReplyShow = !isReplyShow\"\r\n                    v-if=\"!isReply && transactStatus === 'apply_adjust' && !isConclude\" :disabled=\"!transactUnitObj.communications?.length &&\r\n                      handleOfficeType !== 'assist'\r\n                      \" type=\"primary\">填写答复文件</el-button>\r\n                  <el-button @click=\"isReplyShow = !isReplyShow\"\r\n                    v-if=\"isReply && transactStatus === 'apply_adjust' && !isConclude\" :disabled=\"!transactUnitObj.communications?.length &&\r\n                      handleOfficeType !== 'assist'\r\n                      \" type=\"primary\">编辑答复文件</el-button>\r\n                  <el-button @click=\"handleTrackTransact\" v-if=\"\r\n                    transactStatus === 'has_answer' ||\r\n                    transactStatus === 'apply_trace' ||\r\n                    (transactStatus === 'has_answer' && isConclude)\r\n                  \" :disabled=\"handleOfficeType === 'apply_trace'\" type=\"primary\">申请跟踪办理</el-button>\r\n                  <el-button @click=\"isTrackTransactReply = !isTrackTransactReply\" v-if=\"transactStatus === 'trace'\"\r\n                    type=\"primary\">跟踪办理答复文件</el-button>\r\n                </div>\r\n              </div>\r\n            </global-info-item>\r\n            <global-info-item label=\"满意度测评\">\r\n              <div v-if=\"!satisfactions.length\">\r\n                <el-link @click=\"handleSatisfactions({ id: '' })\" type=\"primary\">查看满意度测评</el-link>\r\n              </div>\r\n              <div v-for=\"item in satisfactions\" :key=\"item.id\">\r\n                <el-link @click=\"handleSatisfactions(item)\" type=\"primary\">{{ item.handleResultName\r\n                }}{{ item.isHistoryTest ? \"（历史测评）\" : \"（最终测评）\" }}</el-link>\r\n              </div>\r\n            </global-info-item>\r\n          </global-info>\r\n        </template>\r\n        <template v-else>\r\n          <div class=\"noText\">协办单位办理情况联合主办单位一同答复,无需单独答复</div>\r\n        </template>\r\n      </div>\r\n    </template>\r\n  </div>\r\n  <xyl-popup-window v-model=\"show\" :beforeClose=\"beforeClose\" name=\"办理单位与委员沟通情况\">\r\n    <CommunicationSituation :id=\"props.id\" :unitId=\"transactId\" type></CommunicationSituation>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isShow\" name=\"添加沟通情况\">\r\n    <CommunicationSituationSubmit :suggestId=\"props.id\" :unitId=\"transactId\" @callback=\"callback\">\r\n    </CommunicationSituationSubmit>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isAnswerShow\" name=\"申请延期\">\r\n    <ApplyForAnswer :suggestId=\"props.id\" :unitId=\"transactId\" :time=\"answerTime\" @callback=\"handleCallback\">\r\n    </ApplyForAnswer>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isAnswerRecords\" name=\"申请延期记录\">\r\n    <UnitApplyForAnswerRecords :name=\"handlerOffice?.flowHandleOfficeName\" :data=\"transactUnitObj.delays\">\r\n    </UnitApplyForAnswerRecords>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isAdjustShow\" name=\"申请调整\">\r\n    <ApplyForAdjust :suggestId=\"props.id\" :unitId=\"transactId\" @callback=\"handleCallback\"></ApplyForAdjust>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isAdjustsRecords\" name=\"申请调整记录\">\r\n    <UnitApplyForAdjustRecords :name=\"handlerOffice?.flowHandleOfficeName\" :data=\"transactUnitObj.adjusts\">\r\n    </UnitApplyForAdjustRecords>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isReplyShow\" name=\"填写答复文件\">\r\n    <SubmitSuggestReply :suggestId=\"props.id\" :unitId=\"transactId\" :id=\"isReply ? transactId : ''\"\r\n      @callback=\"handleCallback\"></SubmitSuggestReply>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isTrackTransactReply\" name=\"填写跟踪办理答复文件\">\r\n    <SubmitSuggestTrackTransactReply :suggestId=\"props.id\" :unitId=\"transactId\" :traceId=\"tracesInfo.id\"\r\n      @callback=\"handleCallback\"></SubmitSuggestTrackTransactReply>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"replyDetailShow\" name=\"答复文件详情\">\r\n    <SuggestReplyDetail :id=\"replyId\"></SuggestReplyDetail>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isTrackTransactShow\" name=\"申请跟踪办理\">\r\n    <ApplyForTrackTransact :unitId=\"transactId\" :suggestId=\"props.id\" @callback=\"handleCallback\">\r\n    </ApplyForTrackTransact>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"ifShow\" name=\"满意度测评\">\r\n    <SegreeSatisfactionDetail :id=\"satisfactionsId\" :suggestId=\"props.id\"></SegreeSatisfactionDetail>\r\n  </xyl-popup-window>\r\n</template>\r\n<script>\r\nexport default { name: \"UnitSuggestDetail\" };\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, reactive, onActivated, computed, watch, onDeactivated, onBeforeUnmount } from 'vue'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport { exportWordHtmlObj } from \"common/config/MicroGlobal\"\r\nimport CommunicationSituation from '@/views/SuggestDetail/CommunicationSituation/CommunicationSituation.vue'\r\nimport CommunicationSituationSubmit from '@/views/SuggestDetail/CommunicationSituation/CommunicationSituationSubmit.vue'\r\nimport ApplyForAnswer from './component/ApplyForAnswer.vue'\r\nimport UnitApplyForAnswerRecords from './component/UnitApplyForAnswerRecords.vue'\r\nimport ApplyForAdjust from './component/ApplyForAdjust.vue'\r\nimport UnitApplyForAdjustRecords from './component/UnitApplyForAdjustRecords.vue'\r\nimport SubmitSuggestReply from './component/SubmitSuggestReply.vue'\r\nimport ApplyForTrackTransact from './component/ApplyForTrackTransact.vue'\r\nimport SubmitSuggestTrackTransactReply from './component/SubmitSuggestTrackTransactReply.vue'\r\nimport SuggestReplyDetail from '@/views/SuggestDetail/SuggestReplyDetail/SuggestReplyDetail.vue'\r\nimport SegreeSatisfactionDetail from '@/views/SuggestDetail/SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  type: { type: String, default: '' },\r\n  details: { type: Object, default: () => ({}) },\r\n  transactUnitObj: { type: Object, default: () => ({}) },\r\n  satisfactions: { type: Array, default: () => [] },\r\n  allhandleOfficeInfos: { type: Array, default: () => [] },\r\n  setExtResult: { type: Object, default: () => ({}) },\r\n  suggestionOfficeShow: { type: Boolean, default: true }\r\n})\r\nconst emit = defineEmits(['refresh', 'callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  sign: '1',\r\n  adjustReason: '',\r\n  hopeHandleOffice: ''\r\n})\r\nconst rules = reactive({\r\n  sign: [{ required: true, message: '请选择是否签收', trigger: ['blur', 'change'] }],\r\n  adjustReason: [{ required: true, message: '请输入调整理由', trigger: ['blur', 'change'] }],\r\n  hopeHandleOffice: [{ required: true, message: '请输入希望办理单位', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nconst transactUnitObj = computed(() => props.transactUnitObj)\r\nconst allhandleOfficeInfos = computed(() => props.allhandleOfficeInfos)\r\nconst delaysInfo = computed(() => props.transactUnitObj.delays[props.transactUnitObj.delays.length - 1])\r\nconst adjustsInfo = computed(() => props.transactUnitObj.adjusts[props.transactUnitObj.adjusts.length - 1])\r\nconst tracesInfo = computed(() => props.transactUnitObj.traces.filter((v) => !v.hasAnswer)[0] || {})\r\nconst satisfactions = computed(() => props.satisfactions)\r\nconst handlerOffice = computed(() => props.transactUnitObj.handlerOffice)\r\nconst adjustTime = computed(() => props.transactUnitObj?.officeAdjustStopDate)\r\nconst answerTime = computed(() => props.transactUnitObj?.officeAnswerStopDate)\r\nconst transactId = computed(() => props.transactUnitObj?.handlerOffice?.id)\r\nconst transactStatus = computed(() => props.transactUnitObj?.handlerOffice?.currentHandleStatus)\r\nconst handleOfficeType = computed(() => props.transactUnitObj?.handlerOffice?.handleOfficeType)\r\nconst isReply = computed(\r\n  () => props.transactUnitObj.answers?.filter((v) => v.submitAnswerType === 'direct')?.length || 0\r\n)\r\nconst isConclude = computed(() => props.type === 'unitConclude')\r\n\r\nconst show = ref(false);\r\nconst isShow = ref(false);\r\nconst isAdjust = ref(false);\r\nconst isAnswer = ref(false);\r\nconst isAnswerShow = ref(false);\r\nconst isAnswerRecords = ref(false);\r\nconst isAdjustShow = ref(false);\r\nconst isAdjustsRecords = ref(false);\r\nconst isReplyShow = ref(false);\r\nconst isTrackTransactReply = ref(false);\r\nconst isTrackTransactShow = ref(false);\r\n\r\nconst replyId = ref(\"\");\r\nconst replyDetailShow = ref(false);\r\n\r\nconst ifShow = ref(false);\r\nconst satisfactionsId = ref(\"\"); // 满意度测评ID\r\n\r\nconst handleCondition = ref('')\r\nconst handleStatusContent = ref('')\r\nconst suggestionHandleStatus = ref([])\r\nconst AreaId = ref('')\r\nconst isTime = ref(false)\r\nonActivated(() => {\r\n  dictionaryData()\r\n  globalReadConfig()\r\n  AreaId.value = sessionStorage.getItem('AreaId') || ''\r\n})\r\nconst isAssignAnswer = ref(false)\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({\r\n    codes: ['proposal_check_assist_answer']\r\n  })\r\n  isAssignAnswer.value = data?.proposal_check_assist_answer === 'true'\r\n}\r\nconst disabledFunc = () => {\r\n  if (isAssignAnswer.value) {\r\n    if (handleOfficeType.value == 'main') {\r\n      return !(\r\n        transactUnitObj.value.communications?.length &&\r\n        allhandleOfficeInfos.value.filter(\r\n          (v) => v.handlerOffice.currentHandleStatus === 'has_answer' && v.handlerOffice.handleOfficeType === 'assist'\r\n        ).length === allhandleOfficeInfos.value.filter((v) => v.handlerOffice.handleOfficeType === 'assist').length\r\n      )\r\n    } else if (handleOfficeType.value === 'assist') {\r\n      return false\r\n    } else {\r\n      return !transactUnitObj.value.communications?.length\r\n    }\r\n  } else {\r\n    return !transactUnitObj.value.communications?.length && handleOfficeType.value !== 'assist'\r\n  }\r\n}\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['suggestion_handle_status'] })\r\n  var { data } = res\r\n  suggestionHandleStatus.value = data.suggestion_handle_status\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'ai-proposal-handle-chat' })\r\n  qiankunMicro.setGlobalState({ AiChatWindow: true })\r\n}\r\nconst consultationDownLoad = (row, setExtResult) => {\r\n  console.log('row===>', row)\r\n  console.log('setExtResult===>', setExtResult)\r\n  row.organizer = handlerOffice.value.flowHandleOfficeName || \"\"; // 主办单位\r\n  row.officePhone = row.submitUserInfo.officePhone || \"\"; // 办公电话\r\n  row.mobile = row.submitUserInfo.mobile || \"\"; // 手机\r\n  row.callAddress = row.submitUserInfo.callAddress || \"\"; // 通讯地址\r\n  const assistData = setExtResult.filter((item) => item.handleOfficeType === \"assist\");\r\n  const assistNames = assistData.map((item) => item.handleOfficeName).join(\",\");\r\n  row.CoOrganizer = assistNames; // 协办单位\r\n  const mainData = setExtResult.filter((item) => item.handleOfficeType === \"main\");\r\n  row.telephone = mainData[0] ? mainData[0].telephone : '';\r\n  row.circlesType = row.termYear?.circlesType?.name\r\n  row.boutType = row.termYear?.boutType?.name\r\n  exportWordHtmlObj({\r\n    code: \"unitSuggestDetail\",\r\n    name: \"征询意见表模板下载\",\r\n    key: \"content\",\r\n    data: row,\r\n  });\r\n}\r\nconst beforeClose = (cb) => {\r\n  emit(\"refresh\")\r\n  cb()\r\n}\r\nconst callback = () => {\r\n  emit('refresh')\r\n  isShow.value = false\r\n}\r\nconst adjustCallback = (type) => {\r\n  isAdjust.value = type\r\n}\r\nconst answerCallback = (type) => {\r\n  isAnswer.value = type\r\n}\r\nconst handleCallback = (type) => {\r\n  isAnswerShow.value = false;\r\n  isAdjustShow.value = false;\r\n  isReplyShow.value = false;\r\n  isTrackTransactShow.value = false;\r\n  if (type) {\r\n    emit(\"callback\");\r\n  }\r\n}\r\nconst handleConditionClick = () => {\r\n  globalJson()\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(\"/proposal/handle/status\", {\r\n    handlingPortionId: transactId.value,\r\n    suggestionHandleStatus: handleCondition.value,\r\n    handleStatusContent: handleStatusContent.value,\r\n  });\r\n  if (code === 200) {\r\n    ElMessage({ type: \"success\", message: \"更新成功\" });\r\n    emit(\"refresh\");\r\n  }\r\n};\r\nconst handleReply = (item) => {\r\n  replyId.value = item.id;\r\n  replyDetailShow.value = true;\r\n};\r\nconst handleTrackTransact = () => {\r\n  isTrackTransactShow.value = true;\r\n};\r\nconst handleSatisfactions = (item) => {\r\n  satisfactionsId.value = item.id\r\n  ifShow.value = true\r\n}\r\nonDeactivated(() => {\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n  qiankunMicro.setGlobalState({ AiChatWindow: false })\r\n})\r\nonBeforeUnmount(() => {\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n  qiankunMicro.setGlobalState({ AiChatWindow: false })\r\n})\r\nconst checkTime = (time) => {\r\n  // 获取传入时间、现在时间的时间戳\r\n  const date = new Date(time).getTime()\r\n  const nowDate = new Date().getTime()\r\n  // 判断现在的时间是否大于传入时间\r\n  return nowDate < date\r\n}\r\nwatch(\r\n  () => [props.transactUnitObj, props.details],\r\n  () => {\r\n    isTime.value = checkTime(props.transactUnitObj?.officeConfirmStopDate)\r\n    qiankunMicro.setGlobalState({ AiChatContent: props.details?.content || '' })\r\n    handleCondition.value = props.transactUnitObj.handlerOffice?.suggestionHandleStatus?.value\r\n    handleStatusContent.value = props.transactUnitObj.handlerOffice?.handleStatusContent\r\n  },\r\n  { immediate: true }\r\n)\r\n\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      if (form.sign === '1') {\r\n        handlingPortionConfirm()\r\n      } else {\r\n        handingPortionAdjust()\r\n      }\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\n\r\nconst handlingPortionConfirm = async () => {\r\n  const { code } = await api.handlingPortionConfirm({\r\n    form: { id: transactId.value }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '签收成功' })\r\n    emit('callback')\r\n  }\r\n}\r\n\r\nconst handingPortionAdjust = async () => {\r\n  const { code } = await api.globalJson('/cppcc/handingPortionAdjust/add', {\r\n    form: {\r\n      handlingPortionId: transactId.value,\r\n      suggestionId: props.id,\r\n      adjustReason: form.adjustReason,\r\n      hopeHandleOffice: form.hopeHandleOffice\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '申请成功' })\r\n    emit('callback')\r\n  }\r\n}\r\n\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.UnitSuggestDetail {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SuggestDetailProcessInfo {\r\n    padding-top: 0 !important;\r\n  }\r\n\r\n  .SuggestTransactDetailNameBody {\r\n    padding: 0 var(--zy-distance-one);\r\n    padding-top: var(--zy-distance-one);\r\n\r\n    .SuggestTransactDetailName {\r\n      width: 100%;\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      position: relative;\r\n      text-align: center;\r\n\r\n      div {\r\n        display: inline-block;\r\n        background-color: #fff;\r\n        position: relative;\r\n        z-index: 2;\r\n        padding: 0 20px;\r\n      }\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 100%;\r\n        height: 1px;\r\n        background-color: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .SuggestTransactBody {\r\n    padding: var(--zy-distance-one);\r\n\r\n    .global-info {\r\n      padding-bottom: 12px;\r\n\r\n      .global-info-item {\r\n        .global-info-label {\r\n          width: 160px;\r\n        }\r\n\r\n        .global-info-content {\r\n          width: calc(100% - 160px);\r\n        }\r\n      }\r\n\r\n      .transactDetail {\r\n        .global-info-content {\r\n          width: calc(100% - 160px);\r\n          padding: 0;\r\n\r\n          &>span {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n\r\n          .transactDetailBody {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: flex;\r\n\r\n            .transactDetailInfo {\r\n              width: calc(100% - 180px);\r\n              padding: var(--zy-distance-five) var(--zy-distance-four);\r\n              display: flex;\r\n              align-items: center;\r\n              flex-wrap: wrap;\r\n\r\n              .zy-el-select {\r\n                margin-bottom: var(--zy-distance-five);\r\n              }\r\n            }\r\n\r\n            .transactDetailButton {\r\n              width: 180px;\r\n              border-left: 1px solid var(--zy-el-border-color-lighter);\r\n              display: flex;\r\n              align-items: center;\r\n              flex-wrap: wrap;\r\n              padding: var(--zy-distance-five) var(--zy-distance-four);\r\n\r\n              .zy-el-button {\r\n                --zy-el-button-size: var(--zy-height-secondary);\r\n                border-radius: var(--el-border-radius-small);\r\n                margin: 0;\r\n              }\r\n\r\n              .zy-el-button+.zy-el-button {\r\n                margin-top: var(--zy-distance-five);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .noText {\r\n      text-align: center;\r\n      font-size: var(--zy-name-font-size);\r\n      font-weight: bold;\r\n      border: 1px solid #3657c0;\r\n      border-radius: 5px;\r\n      padding: 120px 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAsRA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,aAAa,EAAEC,eAAe,QAAQ,KAAK;AACjG,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAOC,sBAAsB,MAAM,yEAAyE;AAC5G,OAAOC,4BAA4B,MAAM,+EAA+E;AACxH,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,yBAAyB,MAAM,2CAA2C;AACjF,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,yBAAyB,MAAM,2CAA2C;AACjF,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,+BAA+B,MAAM,iDAAiD;AAC7F,OAAOC,kBAAkB,MAAM,iEAAiE;AAChG,OAAOC,wBAAwB,MAAM,6EAA6E;AAnBlH,IAAAC,WAAA,GAAe;EAAElD,IAAI,EAAE;AAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoB5C,IAAMmD,KAAK,GAAGC,OASZ;IACF,IAAMC,IAAI,GAAGC,MAAoC;IAEjD,IAAMC,OAAO,GAAG3B,GAAG,CAAC,CAAC;IACrB,IAAM4B,IAAI,GAAG3B,QAAQ,CAAC;MACpB4B,IAAI,EAAE,GAAG;MACTC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE;IACpB,CAAC,CAAC;IACF,IAAMC,KAAK,GAAG/B,QAAQ,CAAC;MACrB4B,IAAI,EAAE,CAAC;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC3EL,YAAY,EAAE,CAAC;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACnFJ,gBAAgB,EAAE,CAAC;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC;IAC1F,CAAC,CAAC;IAEF,IAAMC,eAAe,GAAGjC,QAAQ,CAAC;MAAA,OAAMoB,KAAK,CAACa,eAAe;IAAA,EAAC;IAC7D,IAAMC,oBAAoB,GAAGlC,QAAQ,CAAC;MAAA,OAAMoB,KAAK,CAACc,oBAAoB;IAAA,EAAC;IACvE,IAAMC,UAAU,GAAGnC,QAAQ,CAAC;MAAA,OAAMoB,KAAK,CAACa,eAAe,CAACG,MAAM,CAAChB,KAAK,CAACa,eAAe,CAACG,MAAM,CAACvE,MAAM,GAAG,CAAC,CAAC;IAAA,EAAC;IACxG,IAAMwE,WAAW,GAAGrC,QAAQ,CAAC;MAAA,OAAMoB,KAAK,CAACa,eAAe,CAACK,OAAO,CAAClB,KAAK,CAACa,eAAe,CAACK,OAAO,CAACzE,MAAM,GAAG,CAAC,CAAC;IAAA,EAAC;IAC3G,IAAM0E,UAAU,GAAGvC,QAAQ,CAAC;MAAA,OAAMoB,KAAK,CAACa,eAAe,CAACO,MAAM,CAACC,MAAM,CAAC,UAACjH,CAAC;QAAA,OAAK,CAACA,CAAC,CAACkH,SAAS;MAAA,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAAA,EAAC;IACpG,IAAMC,aAAa,GAAG3C,QAAQ,CAAC;MAAA,OAAMoB,KAAK,CAACuB,aAAa;IAAA,EAAC;IACzD,IAAMC,aAAa,GAAG5C,QAAQ,CAAC;MAAA,OAAMoB,KAAK,CAACa,eAAe,CAACW,aAAa;IAAA,EAAC;IACzE,IAAMC,UAAU,GAAG7C,QAAQ,CAAC;MAAA,IAAA8C,qBAAA;MAAA,QAAAA,qBAAA,GAAM1B,KAAK,CAACa,eAAe,cAAAa,qBAAA,uBAArBA,qBAAA,CAAuBC,oBAAoB;IAAA,EAAC;IAC9E,IAAMC,UAAU,GAAGhD,QAAQ,CAAC;MAAA,IAAAiD,sBAAA;MAAA,QAAAA,sBAAA,GAAM7B,KAAK,CAACa,eAAe,cAAAgB,sBAAA,uBAArBA,sBAAA,CAAuBC,oBAAoB;IAAA,EAAC;IAC9E,IAAMC,UAAU,GAAGnD,QAAQ,CAAC;MAAA,IAAAoD,sBAAA;MAAA,QAAAA,sBAAA,GAAMhC,KAAK,CAACa,eAAe,cAAAmB,sBAAA,gBAAAA,sBAAA,GAArBA,sBAAA,CAAuBR,aAAa,cAAAQ,sBAAA,uBAApCA,sBAAA,CAAsCC,EAAE;IAAA,EAAC;IAC3E,IAAMC,cAAc,GAAGtD,QAAQ,CAAC;MAAA,IAAAuD,sBAAA;MAAA,QAAAA,sBAAA,GAAMnC,KAAK,CAACa,eAAe,cAAAsB,sBAAA,gBAAAA,sBAAA,GAArBA,sBAAA,CAAuBX,aAAa,cAAAW,sBAAA,uBAApCA,sBAAA,CAAsCC,mBAAmB;IAAA,EAAC;IAChG,IAAMC,gBAAgB,GAAGzD,QAAQ,CAAC;MAAA,IAAA0D,sBAAA;MAAA,QAAAA,sBAAA,GAAMtC,KAAK,CAACa,eAAe,cAAAyB,sBAAA,gBAAAA,sBAAA,GAArBA,sBAAA,CAAuBd,aAAa,cAAAc,sBAAA,uBAApCA,sBAAA,CAAsCD,gBAAgB;IAAA,EAAC;IAC/F,IAAME,OAAO,GAAG3D,QAAQ,CACtB;MAAA,IAAA4D,sBAAA;MAAA,OAAM,EAAAA,sBAAA,GAAAxC,KAAK,CAACa,eAAe,CAAC4B,OAAO,cAAAD,sBAAA,gBAAAA,sBAAA,GAA7BA,sBAAA,CAA+BnB,MAAM,CAAC,UAACjH,CAAC;QAAA,OAAKA,CAAC,CAACsI,gBAAgB,KAAK,QAAQ;MAAA,EAAC,cAAAF,sBAAA,uBAA7EA,sBAAA,CAA+E/F,MAAM,KAAI,CAAC;IAAA,CAClG,CAAC;IACD,IAAMkG,UAAU,GAAG/D,QAAQ,CAAC;MAAA,OAAMoB,KAAK,CAACzG,IAAI,KAAK,cAAc;IAAA,EAAC;IAEhE,IAAMqJ,IAAI,GAAGnE,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMoE,MAAM,GAAGpE,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMqE,QAAQ,GAAGrE,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMsE,QAAQ,GAAGtE,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMuE,YAAY,GAAGvE,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMwE,eAAe,GAAGxE,GAAG,CAAC,KAAK,CAAC;IAClC,IAAMyE,YAAY,GAAGzE,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAM0E,gBAAgB,GAAG1E,GAAG,CAAC,KAAK,CAAC;IACnC,IAAM2E,WAAW,GAAG3E,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAM4E,oBAAoB,GAAG5E,GAAG,CAAC,KAAK,CAAC;IACvC,IAAM6E,mBAAmB,GAAG7E,GAAG,CAAC,KAAK,CAAC;IAEtC,IAAM8E,OAAO,GAAG9E,GAAG,CAAC,EAAE,CAAC;IACvB,IAAM+E,eAAe,GAAG/E,GAAG,CAAC,KAAK,CAAC;IAElC,IAAMgF,MAAM,GAAGhF,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMiF,eAAe,GAAGjF,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEjC,IAAMkF,eAAe,GAAGlF,GAAG,CAAC,EAAE,CAAC;IAC/B,IAAMmF,mBAAmB,GAAGnF,GAAG,CAAC,EAAE,CAAC;IACnC,IAAMoF,sBAAsB,GAAGpF,GAAG,CAAC,EAAE,CAAC;IACtC,IAAMqF,MAAM,GAAGrF,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMsF,MAAM,GAAGtF,GAAG,CAAC,KAAK,CAAC;IACzBE,WAAW,CAAC,YAAM;MAChBqF,cAAc,CAAC,CAAC;MAChBC,gBAAgB,CAAC,CAAC;MAClBH,MAAM,CAAC1L,KAAK,GAAG8L,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;IACvD,CAAC,CAAC;IACF,IAAMC,cAAc,GAAG3F,GAAG,CAAC,KAAK,CAAC;IACjC,IAAMwF,gBAAgB;MAAA,IAAAI,KAAA,GAAAlG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwH,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAA9M,mBAAA,GAAAuB,IAAA,UAAAwL,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAnH,IAAA,GAAAmH,QAAA,CAAA9I,IAAA;YAAA;cAAA8I,QAAA,CAAA9I,IAAA;cAAA,OACA4C,GAAG,CAACyF,gBAAgB,CAAC;gBAC1CU,KAAK,EAAE,CAAC,8BAA8B;cACxC,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAG,QAAA,CAAArJ,IAAA;cAFMmJ,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAGZJ,cAAc,CAAChM,KAAK,GAAG,CAAAoM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,4BAA4B,MAAK,MAAM;YAAA;YAAA;cAAA,OAAAF,QAAA,CAAAhH,IAAA;UAAA;QAAA,GAAA4G,OAAA;MAAA,CACrE;MAAA,gBALKL,gBAAgBA,CAAA;QAAA,OAAAI,KAAA,CAAAhG,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKrB;IACD,IAAMyG,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAIT,cAAc,CAAChM,KAAK,EAAE;QACxB,IAAIiK,gBAAgB,CAACjK,KAAK,IAAI,MAAM,EAAE;UAAA,IAAA0M,qBAAA;UACpC,OAAO,EACL,CAAAA,qBAAA,GAAAjE,eAAe,CAACzI,KAAK,CAAC2M,cAAc,cAAAD,qBAAA,eAApCA,qBAAA,CAAsCrI,MAAM,IAC5CqE,oBAAoB,CAAC1I,KAAK,CAACiJ,MAAM,CAC/B,UAACjH,CAAC;YAAA,OAAKA,CAAC,CAACoH,aAAa,CAACY,mBAAmB,KAAK,YAAY,IAAIhI,CAAC,CAACoH,aAAa,CAACa,gBAAgB,KAAK,QAAQ;UAAA,CAC9G,CAAC,CAAC5F,MAAM,KAAKqE,oBAAoB,CAAC1I,KAAK,CAACiJ,MAAM,CAAC,UAACjH,CAAC;YAAA,OAAKA,CAAC,CAACoH,aAAa,CAACa,gBAAgB,KAAK,QAAQ;UAAA,EAAC,CAAC5F,MAAM,CAC5G;QACH,CAAC,MAAM,IAAI4F,gBAAgB,CAACjK,KAAK,KAAK,QAAQ,EAAE;UAC9C,OAAO,KAAK;QACd,CAAC,MAAM;UAAA,IAAA4M,sBAAA;UACL,OAAO,GAAAA,sBAAA,GAACnE,eAAe,CAACzI,KAAK,CAAC2M,cAAc,cAAAC,sBAAA,eAApCA,sBAAA,CAAsCvI,MAAM;QACtD;MACF,CAAC,MAAM;QAAA,IAAAwI,sBAAA;QACL,OAAO,GAAAA,sBAAA,GAACpE,eAAe,CAACzI,KAAK,CAAC2M,cAAc,cAAAE,sBAAA,eAApCA,sBAAA,CAAsCxI,MAAM,KAAI4F,gBAAgB,CAACjK,KAAK,KAAK,QAAQ;MAC7F;IACF,CAAC;IACD,IAAM4L,cAAc;MAAA,IAAAkB,KAAA,GAAA/G,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqI,SAAA;QAAA,IAAAC,GAAA,EAAAZ,IAAA;QAAA,OAAA9M,mBAAA,GAAAuB,IAAA,UAAAoM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/H,IAAA,GAAA+H,SAAA,CAAA1J,IAAA;YAAA;cAAA0J,SAAA,CAAA1J,IAAA;cAAA,OACH4C,GAAG,CAACwF,cAAc,CAAC;gBAAEuB,SAAS,EAAE,CAAC,0BAA0B;cAAE,CAAC,CAAC;YAAA;cAA3EH,GAAG,GAAAE,SAAA,CAAAjK,IAAA;cACHmJ,IAAI,GAAKY,GAAG,CAAZZ,IAAI;cACVX,sBAAsB,CAACzL,KAAK,GAAGoM,IAAI,CAACgB,wBAAwB;cAC5DxG,YAAY,CAACyG,cAAc,CAAC;gBAAEC,UAAU,EAAE;cAA0B,CAAC,CAAC;cACtE1G,YAAY,CAACyG,cAAc,CAAC;gBAAEE,YAAY,EAAE;cAAK,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAL,SAAA,CAAA5H,IAAA;UAAA;QAAA,GAAAyH,QAAA;MAAA,CACpD;MAAA,gBANKnB,cAAcA,CAAA;QAAA,OAAAkB,KAAA,CAAA7G,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMnB;IACD,IAAMwH,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,GAAG,EAAEC,YAAY,EAAK;MAAA,IAAAC,aAAA,EAAAC,cAAA;MAClDC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEL,GAAG,CAAC;MAC3BI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEJ,YAAY,CAAC;MAC7CD,GAAG,CAACM,SAAS,GAAG3E,aAAa,CAACpJ,KAAK,CAACgO,oBAAoB,IAAI,EAAE,CAAC,CAAC;MAChEP,GAAG,CAACQ,WAAW,GAAGR,GAAG,CAACS,cAAc,CAACD,WAAW,IAAI,EAAE,CAAC,CAAC;MACxDR,GAAG,CAACU,MAAM,GAAGV,GAAG,CAACS,cAAc,CAACC,MAAM,IAAI,EAAE,CAAC,CAAC;MAC9CV,GAAG,CAACW,WAAW,GAAGX,GAAG,CAACS,cAAc,CAACE,WAAW,IAAI,EAAE,CAAC,CAAC;MACxD,IAAMC,UAAU,GAAGX,YAAY,CAACzE,MAAM,CAAC,UAACqF,IAAI;QAAA,OAAKA,IAAI,CAACrE,gBAAgB,KAAK,QAAQ;MAAA,EAAC;MACpF,IAAMsE,WAAW,GAAGF,UAAU,CAACG,GAAG,CAAC,UAACF,IAAI;QAAA,OAAKA,IAAI,CAACG,gBAAgB;MAAA,EAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAC7EjB,GAAG,CAACkB,WAAW,GAAGJ,WAAW,CAAC,CAAC;MAC/B,IAAMK,QAAQ,GAAGlB,YAAY,CAACzE,MAAM,CAAC,UAACqF,IAAI;QAAA,OAAKA,IAAI,CAACrE,gBAAgB,KAAK,MAAM;MAAA,EAAC;MAChFwD,GAAG,CAACoB,SAAS,GAAGD,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,CAACC,SAAS,GAAG,EAAE;MACxDpB,GAAG,CAACqB,WAAW,IAAAnB,aAAA,GAAGF,GAAG,CAACsB,QAAQ,cAAApB,aAAA,gBAAAA,aAAA,GAAZA,aAAA,CAAcmB,WAAW,cAAAnB,aAAA,uBAAzBA,aAAA,CAA2BlJ,IAAI;MACjDgJ,GAAG,CAACuB,QAAQ,IAAApB,cAAA,GAAGH,GAAG,CAACsB,QAAQ,cAAAnB,cAAA,gBAAAA,cAAA,GAAZA,cAAA,CAAcoB,QAAQ,cAAApB,cAAA,uBAAtBA,cAAA,CAAwBnJ,IAAI;MAC3CsC,iBAAiB,CAAC;QAChBkI,IAAI,EAAE,mBAAmB;QACzBxK,IAAI,EAAE,WAAW;QACjByK,GAAG,EAAE,SAAS;QACd9C,IAAI,EAAEqB;MACR,CAAC,CAAC;IACJ,CAAC;IACD,IAAM0B,WAAW,GAAG,SAAdA,WAAWA,CAAIC,EAAE,EAAK;MAC1BtH,IAAI,CAAC,SAAS,CAAC;MACfsH,EAAE,CAAC,CAAC;IACN,CAAC;IACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBvH,IAAI,CAAC,SAAS,CAAC;MACf2C,MAAM,CAACzK,KAAK,GAAG,KAAK;IACtB,CAAC;IACD,IAAMsP,cAAc,GAAG,SAAjBA,cAAcA,CAAInO,IAAI,EAAK;MAC/BuJ,QAAQ,CAAC1K,KAAK,GAAGmB,IAAI;IACvB,CAAC;IACD,IAAMoO,cAAc,GAAG,SAAjBA,cAAcA,CAAIpO,IAAI,EAAK;MAC/BwJ,QAAQ,CAAC3K,KAAK,GAAGmB,IAAI;IACvB,CAAC;IACD,IAAMqO,cAAc,GAAG,SAAjBA,cAAcA,CAAIrO,IAAI,EAAK;MAC/ByJ,YAAY,CAAC5K,KAAK,GAAG,KAAK;MAC1B8K,YAAY,CAAC9K,KAAK,GAAG,KAAK;MAC1BgL,WAAW,CAAChL,KAAK,GAAG,KAAK;MACzBkL,mBAAmB,CAAClL,KAAK,GAAG,KAAK;MACjC,IAAImB,IAAI,EAAE;QACR2G,IAAI,CAAC,UAAU,CAAC;MAClB;IACF,CAAC;IACD,IAAM2H,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjCC,UAAU,CAAC,CAAC;IACd,CAAC;IACD,IAAMA,UAAU;MAAA,IAAAC,KAAA,GAAA5J,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkL,SAAA;QAAA,IAAAC,qBAAA,EAAAZ,IAAA;QAAA,OAAA3P,mBAAA,GAAAuB,IAAA,UAAAiP,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5K,IAAA,GAAA4K,SAAA,CAAAvM,IAAA;YAAA;cAAAuM,SAAA,CAAAvM,IAAA;cAAA,OACM4C,GAAG,CAACsJ,UAAU,CAAC,yBAAyB,EAAE;gBAC/DM,iBAAiB,EAAErG,UAAU,CAAC3J,KAAK;gBACnCyL,sBAAsB,EAAEF,eAAe,CAACvL,KAAK;gBAC7CwL,mBAAmB,EAAEA,mBAAmB,CAACxL;cAC3C,CAAC,CAAC;YAAA;cAAA6P,qBAAA,GAAAE,SAAA,CAAA9M,IAAA;cAJMgM,IAAI,GAAAY,qBAAA,CAAJZ,IAAI;cAKZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBnI,SAAS,CAAC;kBAAE3F,IAAI,EAAE,SAAS;kBAAEoH,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/CT,IAAI,CAAC,SAAS,CAAC;cACjB;YAAC;YAAA;cAAA,OAAAiI,SAAA,CAAAzK,IAAA;UAAA;QAAA,GAAAsK,QAAA;MAAA,CACF;MAAA,gBAVKF,UAAUA,CAAA;QAAA,OAAAC,KAAA,CAAA1J,KAAA,OAAAD,SAAA;MAAA;IAAA,GAUf;IACD,IAAMiK,WAAW,GAAG,SAAdA,WAAWA,CAAI3B,IAAI,EAAK;MAC5BnD,OAAO,CAACnL,KAAK,GAAGsO,IAAI,CAACzE,EAAE;MACvBuB,eAAe,CAACpL,KAAK,GAAG,IAAI;IAC9B,CAAC;IACD,IAAMkQ,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;MAChChF,mBAAmB,CAAClL,KAAK,GAAG,IAAI;IAClC,CAAC;IACD,IAAMmQ,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAI7B,IAAI,EAAK;MACpChD,eAAe,CAACtL,KAAK,GAAGsO,IAAI,CAACzE,EAAE;MAC/BwB,MAAM,CAACrL,KAAK,GAAG,IAAI;IACrB,CAAC;IACD0G,aAAa,CAAC,YAAM;MAClBE,YAAY,CAACyG,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC;MACxD1G,YAAY,CAACyG,cAAc,CAAC;QAAE+C,aAAa,EAAE;MAAG,CAAC,CAAC;MAClDxJ,YAAY,CAACyG,cAAc,CAAC;QAAEE,YAAY,EAAE;MAAM,CAAC,CAAC;IACtD,CAAC,CAAC;IACF5G,eAAe,CAAC,YAAM;MACpBC,YAAY,CAACyG,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC;MACxD1G,YAAY,CAACyG,cAAc,CAAC;QAAE+C,aAAa,EAAE;MAAG,CAAC,CAAC;MAClDxJ,YAAY,CAACyG,cAAc,CAAC;QAAEE,YAAY,EAAE;MAAM,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,IAAM8C,SAAS,GAAG,SAAZA,SAASA,CAAIC,IAAI,EAAK;MAC1B;MACA,IAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC,CAACG,OAAO,CAAC,CAAC;MACrC,IAAMC,OAAO,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MACpC;MACA,OAAOC,OAAO,GAAGH,IAAI;IACvB,CAAC;IACD9J,KAAK,CACH;MAAA,OAAM,CAACmB,KAAK,CAACa,eAAe,EAAEb,KAAK,CAAC+I,OAAO,CAAC;IAAA,GAC5C,YAAM;MAAA,IAAAC,sBAAA,EAAAC,cAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACJpF,MAAM,CAAC3L,KAAK,GAAGqQ,SAAS,EAAAO,sBAAA,GAAChJ,KAAK,CAACa,eAAe,cAAAmI,sBAAA,uBAArBA,sBAAA,CAAuBI,qBAAqB,CAAC;MACtEpK,YAAY,CAACyG,cAAc,CAAC;QAAE+C,aAAa,EAAE,EAAAS,cAAA,GAAAjJ,KAAK,CAAC+I,OAAO,cAAAE,cAAA,uBAAbA,cAAA,CAAeI,OAAO,KAAI;MAAG,CAAC,CAAC;MAC5E1F,eAAe,CAACvL,KAAK,IAAA8Q,sBAAA,GAAGlJ,KAAK,CAACa,eAAe,CAACW,aAAa,cAAA0H,sBAAA,gBAAAA,sBAAA,GAAnCA,sBAAA,CAAqCrF,sBAAsB,cAAAqF,sBAAA,uBAA3DA,sBAAA,CAA6D9Q,KAAK;MAC1FwL,mBAAmB,CAACxL,KAAK,IAAA+Q,sBAAA,GAAGnJ,KAAK,CAACa,eAAe,CAACW,aAAa,cAAA2H,sBAAA,uBAAnCA,sBAAA,CAAqCvF,mBAAmB;IACtF,CAAC,EACD;MAAE0F,SAAS,EAAE;IAAK,CACpB,CAAC;IAED,IAAMC,UAAU;MAAA,IAAAC,KAAA,GAAArL,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2M,SAAOC,MAAM;QAAA,OAAAhS,mBAAA,GAAAuB,IAAA,UAAA0Q,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArM,IAAA,GAAAqM,SAAA,CAAAhO,IAAA;YAAA;cAAA,IACzB8N,MAAM;gBAAAE,SAAA,CAAAhO,IAAA;gBAAA;cAAA;cAAA,OAAAgO,SAAA,CAAApO,MAAA;YAAA;cAAAoO,SAAA,CAAAhO,IAAA;cAAA,OACL8N,MAAM,CAACG,QAAQ,CAAC,UAACC,KAAK,EAAEC,MAAM,EAAK;gBACvC,IAAID,KAAK,EAAE;kBACT,IAAIzJ,IAAI,CAACC,IAAI,KAAK,GAAG,EAAE;oBACrB0J,sBAAsB,CAAC,CAAC;kBAC1B,CAAC,MAAM;oBACLC,oBAAoB,CAAC,CAAC;kBACxB;gBACF,CAAC,MAAM;kBACL/K,SAAS,CAAC;oBAAE3F,IAAI,EAAE,SAAS;oBAAEoH,OAAO,EAAE;kBAAiB,CAAC,CAAC;gBAC3D;cACF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAiJ,SAAA,CAAAlM,IAAA;UAAA;QAAA,GAAA+L,QAAA;MAAA,CACH;MAAA,gBAbKF,UAAUA,CAAAW,EAAA;QAAA,OAAAV,KAAA,CAAAnL,KAAA,OAAAD,SAAA;MAAA;IAAA,GAaf;IAED,IAAM4L,sBAAsB;MAAA,IAAAG,KAAA,GAAAhM,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsN,SAAA;QAAA,IAAAC,qBAAA,EAAAhD,IAAA;QAAA,OAAA3P,mBAAA,GAAAuB,IAAA,UAAAqR,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhN,IAAA,GAAAgN,SAAA,CAAA3O,IAAA;YAAA;cAAA2O,SAAA,CAAA3O,IAAA;cAAA,OACN4C,GAAG,CAACwL,sBAAsB,CAAC;gBAChD3J,IAAI,EAAE;kBAAE4B,EAAE,EAAEF,UAAU,CAAC3J;gBAAM;cAC/B,CAAC,CAAC;YAAA;cAAAiS,qBAAA,GAAAE,SAAA,CAAAlP,IAAA;cAFMgM,IAAI,GAAAgD,qBAAA,CAAJhD,IAAI;cAGZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBnI,SAAS,CAAC;kBAAE3F,IAAI,EAAE,SAAS;kBAAEoH,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/CT,IAAI,CAAC,UAAU,CAAC;cAClB;YAAC;YAAA;cAAA,OAAAqK,SAAA,CAAA7M,IAAA;UAAA;QAAA,GAAA0M,QAAA;MAAA,CACF;MAAA,gBARKJ,sBAAsBA,CAAA;QAAA,OAAAG,KAAA,CAAA9L,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQ3B;IAED,IAAM6L,oBAAoB;MAAA,IAAAO,KAAA,GAAArM,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2N,SAAA;QAAA,IAAAC,sBAAA,EAAArD,IAAA;QAAA,OAAA3P,mBAAA,GAAAuB,IAAA,UAAA0R,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArN,IAAA,GAAAqN,SAAA,CAAAhP,IAAA;YAAA;cAAAgP,SAAA,CAAAhP,IAAA;cAAA,OACJ4C,GAAG,CAACsJ,UAAU,CAAC,iCAAiC,EAAE;gBACvEzH,IAAI,EAAE;kBACJ+H,iBAAiB,EAAErG,UAAU,CAAC3J,KAAK;kBACnCyS,YAAY,EAAE7K,KAAK,CAACiC,EAAE;kBACtB1B,YAAY,EAAEF,IAAI,CAACE,YAAY;kBAC/BC,gBAAgB,EAAEH,IAAI,CAACG;gBACzB;cACF,CAAC,CAAC;YAAA;cAAAkK,sBAAA,GAAAE,SAAA,CAAAvP,IAAA;cAPMgM,IAAI,GAAAqD,sBAAA,CAAJrD,IAAI;cAQZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBnI,SAAS,CAAC;kBAAE3F,IAAI,EAAE,SAAS;kBAAEoH,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/CT,IAAI,CAAC,UAAU,CAAC;cAClB;YAAC;YAAA;cAAA,OAAA0K,SAAA,CAAAlN,IAAA;UAAA;QAAA,GAAA+M,QAAA;MAAA,CACF;MAAA,gBAbKR,oBAAoBA,CAAA;QAAA,OAAAO,KAAA,CAAAnM,KAAA,OAAAD,SAAA;MAAA;IAAA,GAazB;IAED,IAAM0M,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB5K,IAAI,CAAC,UAAU,CAAC;IAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}