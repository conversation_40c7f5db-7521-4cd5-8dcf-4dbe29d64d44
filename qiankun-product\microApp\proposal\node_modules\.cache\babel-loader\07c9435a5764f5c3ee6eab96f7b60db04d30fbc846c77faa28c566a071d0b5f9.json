{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestRecommendType\"\n};\nvar _hoisted_2 = {\n  class: \"SuggestRecommendTypeBody\"\n};\nvar _hoisted_3 = [\"onMouseenter\", \"onMouseleave\", \"onClick\"];\nvar _hoisted_4 = {\n  key: 0,\n  class: \"SuggestRecommendTypeName\"\n};\nvar _hoisted_5 = {\n  key: 1,\n  class: \"SuggestRecommendTypeBody\"\n};\nvar _hoisted_6 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"SuggestRecommendTypeTitle\"\n  }, \"根据历年提案分类数据，小助手为您智能推荐了提案的类别，点击他们就会对号入座哦~\", -1 /* HOISTED */)), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"SuggestRecommendTypeName\"\n  }, \"提案大类：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.typeData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"SuggestRecommendTypeItem\", {\n        'is-active': item._id === $setup.typeId\n      }]),\n      onMouseenter: function onMouseenter($event) {\n        return $setup.handleMouseEnter(item);\n      },\n      onMouseleave: function onMouseleave($event) {\n        return $setup.handleMouseLeave(item);\n      },\n      onClick: function onClick($event) {\n        return $setup.handleTypeClick(item);\n      },\n      key: item._id\n    }, _toDisplayString(item.name), 43 /* TEXT, CLASS, PROPS, NEED_HYDRATION */, _hoisted_3);\n  }), 128 /* KEYED_FRAGMENT */))]), $setup.secondType.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \"提案小类：\")) : _createCommentVNode(\"v-if\", true), $setup.secondType.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.secondType, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"SuggestRecommendTypeItem\",\n      onClick: function onClick($event) {\n        return $setup.handleTypeChildClick(item);\n      },\n      key: item._id\n    }, _toDisplayString(item.name), 9 /* TEXT, PROPS */, _hoisted_6);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$setup", "typeData", "item", "_normalizeClass", "_id", "typeId", "onMouseenter", "$event", "handleMouseEnter", "onMouseleave", "handleMouseLeave", "onClick", "handleTypeClick", "name", "_hoisted_3", "secondType", "length", "_hoisted_4", "_createCommentVNode", "_hoisted_5", "handleTypeChildClick", "_hoisted_6"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendType\\SuggestRecommendType.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestRecommendType\">\r\n    <div class=\"SuggestRecommendTypeTitle\">根据历年提案分类数据，小助手为您智能推荐了提案的类别，点击他们就会对号入座哦~</div>\r\n    <div class=\"SuggestRecommendTypeName\">提案大类：</div>\r\n    <div class=\"SuggestRecommendTypeBody\">\r\n      <div class=\"SuggestRecommendTypeItem\"\r\n           :class=\"{ 'is-active': item._id === typeId }\"\r\n           v-for=\"item in typeData\"\r\n           @mouseenter=\"handleMouseEnter(item)\"\r\n           @mouseleave=\"handleMouseLeave(item)\"\r\n           @click=\"handleTypeClick(item)\"\r\n           :key=\"item._id\">{{ item.name }}</div>\r\n    </div>\r\n    <div class=\"SuggestRecommendTypeName\"\r\n         v-if=\"secondType.length\">提案小类：</div>\r\n    <div class=\"SuggestRecommendTypeBody\"\r\n         v-if=\"secondType.length\">\r\n      <div class=\"SuggestRecommendTypeItem\"\r\n           v-for=\"item in secondType\"\r\n           @click=\"handleTypeChildClick(item)\"\r\n           :key=\"item._id\">{{ item.name }}</div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestRecommendType' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, watch } from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' }, content: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback', 'select'])\r\nconst typeId = ref([])\r\nconst typeData = ref([])\r\nconst typeChild = ref([])\r\nconst secondType = ref([])\r\n\r\nconst commonMethod = async () => {\r\n  try {\r\n    const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区\r\n    const { data } = await api.commonType({ id: props.id, areaId: AreaId, dbName: 'thinktank', type: '1', is_refresh: '1', content: props.content })\r\n    typeData.value = data\r\n    if (typeData.value.length) { emit('callback', true, true) } else { emit('callback', false, false) }\r\n  } catch (err) {\r\n    emit('callback', false, false)\r\n  }\r\n}\r\nconst handleMouseEnter = (item) => {\r\n  secondType.value = item.secondType\r\n}\r\nconst handleMouseLeave = () => {\r\n  secondType.value = typeId.value ? typeChild.value : []\r\n}\r\nconst handleTypeClick = (item) => {\r\n  typeId.value = item._id\r\n  typeChild.value = item.secondType\r\n  secondType.value = item.secondType\r\n  emit('select', item)\r\n}\r\nconst handleTypeChildClick = (item) => {\r\n  emit('select', item, typeId.value)\r\n}\r\nwatch(() => props.content, () => {\r\n  if (props.content) { commonMethod() } else { emit('callback', false, false) }\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestRecommendType {\r\n  width: 380px;\r\n  padding: var(--zy-distance-three) var(--zy-distance-two);\r\n  padding-top: 0;\r\n\r\n  .SuggestRecommendTypeTitle {\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding-bottom: var(--zy-font-name-distance-five);\r\n    color: var(--zy-el-color-primary);\r\n  }\r\n\r\n  .SuggestRecommendTypeName {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding-bottom: var(--zy-font-name-distance-five);\r\n  }\r\n\r\n  .SuggestRecommendTypeBody {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n\r\n    .SuggestRecommendTypeItem {\r\n      height: var(--zy-height-routine);\r\n      line-height: var(--zy-height-routine);\r\n      font-size: var(--zy-text-font-size);\r\n      background-color: var(--zy-el-color-info-light-9);\r\n      padding: 0 var(--zy-distance-five);\r\n      margin-right: var(--zy-distance-five);\r\n      margin-bottom: var(--zy-distance-five);\r\n      border-radius: var(--el-border-radius-small);\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background-color: var(--zy-el-color-info-light-8);\r\n      }\r\n    }\r\n\r\n    .is-active {\r\n      background-color: var(--zy-el-color-info-light-8);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EAG1BA,KAAK,EAAC;AAA0B;iBAJzC;;EAAAC,GAAA;EAaSD,KAAK,EAAC;;;EAbfC,GAAA;EAeSD,KAAK,EAAC;;iBAff;;uBACEE,mBAAA,CAqBM,OArBNC,UAqBM,G,0BApBJC,mBAAA,CAAoF;IAA/EJ,KAAK,EAAC;EAA2B,GAAC,yCAAuC,sB,0BAC9EI,mBAAA,CAAiD;IAA5CJ,KAAK,EAAC;EAA0B,GAAC,OAAK,sBAC3CI,mBAAA,CAQM,OARNC,UAQM,I,kBAPJH,mBAAA,CAM0CI,SAAA,QAXhDC,WAAA,CAO0BC,MAAA,CAAAC,QAAQ,EAPlC,UAOkBC,IAAI;yBAFhBR,mBAAA,CAM0C;MANrCF,KAAK,EALhBW,eAAA,EAKiB,0BAA0B;QAAA,aACTD,IAAI,CAACE,GAAG,KAAKJ,MAAA,CAAAK;MAAM;MAEzCC,YAAU,WAAVA,YAAUA,CAAAC,MAAA;QAAA,OAAEP,MAAA,CAAAQ,gBAAgB,CAACN,IAAI;MAAA;MACjCO,YAAU,WAAVA,YAAUA,CAAAF,MAAA;QAAA,OAAEP,MAAA,CAAAU,gBAAgB,CAACR,IAAI;MAAA;MACjCS,OAAK,WAALA,OAAKA,CAAAJ,MAAA;QAAA,OAAEP,MAAA,CAAAY,eAAe,CAACV,IAAI;MAAA;MAC3BT,GAAG,EAAES,IAAI,CAACE;wBAAQF,IAAI,CAACW,IAAI,gDAXvCC,UAAA;oCAced,MAAA,CAAAe,UAAU,CAACC,MAAM,I,cAD5BtB,mBAAA,CACyC,OADzCuB,UACyC,EAAX,OAAK,KAdvCC,mBAAA,gBAgBelB,MAAA,CAAAe,UAAU,CAACC,MAAM,I,cAD5BtB,mBAAA,CAMM,OANNyB,UAMM,I,kBAJJzB,mBAAA,CAG0CI,SAAA,QApBhDC,WAAA,CAkB0BC,MAAA,CAAAe,UAAU,EAlBpC,UAkBkBb,IAAI;yBADhBR,mBAAA,CAG0C;MAHrCF,KAAK,EAAC,0BAA0B;MAE/BmB,OAAK,WAALA,OAAKA,CAAAJ,MAAA;QAAA,OAAEP,MAAA,CAAAoB,oBAAoB,CAAClB,IAAI;MAAA;MAChCT,GAAG,EAAES,IAAI,CAACE;wBAAQF,IAAI,CAACW,IAAI,wBApBvCQ,UAAA;sCAAAH,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}