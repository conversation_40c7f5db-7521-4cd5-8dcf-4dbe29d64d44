<template>
  <div class="SuggestAssignDetail">
    <div class="SuggestAssignDetailNameBody">
      <div class="SuggestAssignDetailName">
        <div>{{ props.name }}</div>
      </div>
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="交办方式" prop="reviewResult" class="globalFormTitle" v-if="AreaId !== '370500'">
        <el-radio-group v-model="form.reviewResult" @change="reviewResultChange">
          <el-radio v-for="item in reviewResult" :key="item.nodeId" :label="item.nodeId">{{ item.nodeName }}</el-radio>
        </el-radio-group>
        <template v-if="whetherUseIntelligentize">
          <intelligent-assistant v-model:elIsShow="elTypeShow" v-model="visibleTypeShow">
            <SuggestRecommendType :id="props.id" :content="props.details.content" @callback="typeCallback"
              @select="typeSelect"></SuggestRecommendType>
          </intelligent-assistant>
        </template>
      </el-form-item>
      <el-form-item label="提案大类" v-show="['success', 'other', 'preAssign'].includes(isReviewResult)"
        prop="SuggestBigType">
        <el-select v-model="form.SuggestBigType" placeholder="请选择提案大类" @change="SuggestBigTypeChange" clearable>
          <el-option v-for="item in SuggestBigType" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="提案小类" v-show="['success', 'other', 'preAssign'].includes(isReviewResult)">
        <el-select v-model="form.SuggestSmallType" placeholder="请选择提案小类" clearable>
          <el-option v-for="item in SuggestSmallType" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item> -->
      <el-form-item :label="`${['success', 'other', 'preAssign'].includes(isReviewResult) ? '交办' : '退回'}意见`"
        class="globalFormTitle">
        <el-input v-model="form.reviewOpinion"
          :placeholder="`请输入${['success', 'other', 'preAssign'].includes(isReviewResult) ? '交办' : '退回'}意见`"
          type="textarea" :rows="5" clearable />
      </el-form-item>
      <template v-if="isReviewResult === 'success'">
        <el-form-item label="办理方式" prop="transactType">
          <el-select v-model="form.transactType" placeholder="请选择办理方式" @change="transactTypeChange" clearable
            :disabled="props.isPreAssign">
            <el-option label="主办/协办" value="main_assist" />
            <el-option label="分办" value="publish" />
          </el-select>
        </el-form-item>
        <template v-if="form.transactType === 'main_assist'">
          <el-form-item label="主办单位" prop="mainHandleOfficeId" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.mainHandleOfficeId" :filterId="form.handleOfficeIds" :max="1"
              :disabled="props.isPreAssign"></suggest-simple-select-unit>
            <template v-if="whetherUseIntelligentize">
              <intelligent-assistant v-model:elIsShow="elUnitShow" v-model="visibleUnitShow">
                <SuggestRecommendUnit :params="unitParams" @callback="unitCallback" @select="unitSelect">
                </SuggestRecommendUnit>
              </intelligent-assistant>
            </template>
          </el-form-item>
        </template>
        <template v-if="form.transactType === 'main_assist'">
          <el-form-item label="协办单位" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.handleOfficeIds" :filterId="form.mainHandleOfficeId"
              :disabled="props.isPreAssign"></suggest-simple-select-unit>
          </el-form-item>
        </template>
        <template v-if="form.transactType === 'publish'">
          <el-form-item label="分办单位" prop="handleOfficeIds" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.handleOfficeIds"
              :disabled="props.isPreAssign"></suggest-simple-select-unit>
            <template v-if="whetherUseIntelligentize">
              <intelligent-assistant v-model:elIsShow="elUnitShow" v-model="visibleUnitShow">
                <SuggestRecommendUnit :params="unitParams" @callback="unitCallback" @select="unitSelect">
                </SuggestRecommendUnit>
              </intelligent-assistant>
            </template>
          </el-form-item>
        </template>
        <!-- 分办项中的协办单位 -->
        <template v-if="form.transactType === 'publish'">
          <el-form-item label="协办单位" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.objectParamHandleOfficeIds"
              :filterId="form.mainHandleOfficeId"></suggest-simple-select-unit>
          </el-form-item>
        </template>
        <div class="zy-el-form-item-br"></div>
        <el-form-item label="答复截止时间" prop="answerStopDate">
          <xyl-date-picker v-model="form.answerStopDate" type="datetime" value-format="x" placeholder="请选择答复截止时间"
            :disabled-date="disabledDate"></xyl-date-picker>
        </el-form-item>
        <el-form-item label="调整截止时间" prop="adjustStopDate">
          <xyl-date-picker v-model="form.adjustStopDate" type="datetime" value-format="x" placeholder="请选择调整截止时间"
            :disabled-date="adjustDisabledDate"></xyl-date-picker>
        </el-form-item>
      </template>
      <template v-if="isReviewResult === 'preAssign'">
        <el-form-item label="办理方式" prop="transactType">
          <el-select v-model="form.transactType" placeholder="请选择办理方式" @change="transactTypeChange" clearable>
            <el-option label="主办/协办" value="main_assist" />
            <el-option label="分办" value="publish" />
          </el-select>
        </el-form-item>
        <template v-if="form.transactType === 'main_assist'">
          <el-form-item label="主办单位" prop="mainHandleOfficeId" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.mainHandleOfficeId" :filterId="form.handleOfficeIds"
              :max="1"></suggest-simple-select-unit>
            <template v-if="whetherUseIntelligentize">
              <intelligent-assistant v-model:elIsShow="elUnitShow" v-model="visibleUnitShow">
                <SuggestRecommendUnit :params="unitParams" @callback="unitCallback" @select="unitSelect">
                </SuggestRecommendUnit>
              </intelligent-assistant>
            </template>
          </el-form-item>
        </template>
        <template v-if="form.transactType === 'main_assist'">
          <el-form-item label="协办单位" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.handleOfficeIds"
              :filterId="form.mainHandleOfficeId"></suggest-simple-select-unit>
          </el-form-item>
        </template>
        <template v-if="form.transactType === 'publish'">
          <el-form-item label="分办单位" prop="handleOfficeIds" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.handleOfficeIds"></suggest-simple-select-unit>
            <template v-if="whetherUseIntelligentize">
              <intelligent-assistant v-model:elIsShow="elUnitShow" v-model="visibleUnitShow">
                <SuggestRecommendUnit :params="unitParams" @callback="unitCallback" @select="unitSelect">
                </SuggestRecommendUnit>
              </intelligent-assistant>
            </template>
          </el-form-item>
        </template>
        <!-- 分办项中的协办单位 -->
        <template v-if="form.transactType === 'publish'">
          <el-form-item label="协办单位" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.objectParamHandleOfficeIds"
              :filterId="form.mainHandleOfficeId"></suggest-simple-select-unit>
          </el-form-item>
        </template>
        <div class="zy-el-form-item-br"></div>
        <el-form-item label="签收截止时间" prop="confirmStopDate">
          <xyl-date-picker v-model="form.confirmStopDate" type="datetime" value-format="x" placeholder="请选择签收截止时间"
            :disabled-date="disabledDate"></xyl-date-picker>
        </el-form-item>
      </template>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SuggestAssignDetail' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onActivated, watch } from 'vue'
import { whetherUseIntelligentize } from 'common/js/system_var.js'
import { ElMessage } from 'element-plus'
import SuggestRecommendType from '@/components/SuggestRecommendType/SuggestRecommendType.vue'
import SuggestRecommendUnit from '@/components/SuggestRecommendUnit/SuggestRecommendUnit.vue'
const props = defineProps({
  id: { type: String, default: '' },
  name: { type: String, default: '' },
  details: { type: Object, default: () => ({}) },
  coOrganizerIds: { type: Array, default: () => ([]) },
  transactObj: { type: Object, default: () => ({}) },
  isPreAssign: { type: Boolean, default: false }
})
const emit = defineEmits(['callback'])
const isReviewResult = ref('')
const formRef = ref()
const form = reactive({
  reviewResult: '', // 交办方式
  SuggestBigType: '', // 提案大类
  SuggestSmallType: '', // 提案小类
  reviewOpinion: '', // 交办意见
  transactType: '', // 请选择办理方式
  mainHandleOfficeId: [],
  handleOfficeIds: [],
  answerStopDate: '',
  adjustStopDate: '',
  objectParamHandleOfficeIds: [], // 分办选项的协办单位
  confirmStopDate: ''
})
const rules = reactive({
  reviewResult: [{ required: true, message: '请选择交办方式', trigger: ['blur', 'change'] }],
  SuggestBigType: [{ required: true, message: '请选择提案大类', trigger: ['blur', 'change'] }],
  transactType: [{ required: false, message: '请选择办理方式', trigger: ['blur', 'change'] }],
  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],
  handleOfficeIds: [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }],
  answerStopDate: [{ required: false, message: '请选择答复截止时间', trigger: ['blur', 'change'] }],
  adjustStopDate: [{ required: false, message: '请选择调整截止时间', trigger: ['blur', 'change'] }],
  confirmStopDate: [{ required: false, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]
})
const disabledDate = (time) => time.getTime() < Date.now() + 3600 * 1000 * 24 * 3
const adjustDisabledDate = (time) => time.getTime() < Date.now() + 3600 * 1000 * 24
const unitParams = ref({})
const reviewResult = ref([])
const SuggestBigType = ref([])
const SuggestSmallType = ref([])
// const transactType = ref([])

const elTypeShow = ref(false)
const visibleTypeShow = ref(false)
const elUnitShow = ref(false)
const visibleUnitShow = ref(false)
const AreaId = ref(sessionStorage.getItem('AreaId') || '')
onActivated(() => {
  globalReadConfig()
  suggestionThemeSelect()
  if (props.id) {
    suggestionNextNodes()
  }
})

const typeCallback = (isElIsShow, isVisibleIsShow) => {
  elTypeShow.value = isElIsShow
  visibleTypeShow.value = isVisibleIsShow
}
const typeSelect = (item, id) => {
  if (id) {
    form.SuggestBigType = id
    SuggestBigTypeChange()
    form.SuggestSmallType = item._id
  } else {
    form.SuggestBigType = item._id
    SuggestBigTypeChange()
  }
}
const unitCallback = (isElIsShow, isVisibleIsShow) => {
  elUnitShow.value = isElIsShow
  visibleUnitShow.value = isVisibleIsShow
}
const unitSelect = (item) => {
  if (form.transactType === 'main_assist') {
    if (!form.mainHandleOfficeId.length) {
      if (!form.handleOfficeIds.includes(item.id)) {
        form.mainHandleOfficeId = [item.id]
        ElMessage({ type: 'success', message: `已为您将【${item.name}】添加到主办单位` })
      }
    } else {
      if (!form.mainHandleOfficeId.includes(item.id) && !form.handleOfficeIds.includes(item.id)) {
        form.handleOfficeIds = [...form.handleOfficeIds, item.id]
        ElMessage({ type: 'success', message: `当前已选择主办单位，已为您将【${item.name}】添加到协办单位` })
      }
    }
  } else if (form.transactType === 'publish') {
    if (!form.handleOfficeIds.includes(item.id)) {
      form.handleOfficeIds = [...form.handleOfficeIds, item.id]
      ElMessage({ type: 'success', message: `已为您将${item.name}添加到分办单位` })
    }
  }
}

const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({
    codes: ['SuggestSignTime', 'SuggestReplyTime', 'SuggestAdjustTime', 'SuggestReplyDate']
  })
  if (data.SuggestReplyTime) {
    form.answerStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestReplyTime)
  }
  if (data.SuggestAdjustTime) {
    form.adjustStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestAdjustTime)
  }
  if (data.SuggestSignTime) {
    form.confirmStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestSignTime)
  }
  if (data.SuggestReplyDate && new Date().getTime() < new Date(data.SuggestReplyDate).getTime()) {
    form.answerStopDate = new Date(data.SuggestReplyDate).getTime()
  }
}
const suggestionNextNodes = async () => {
  const res = await api.suggestionNextNodes({ suggestionId: props.id })
  var { data } = res
  form.reviewResult = data[0].nodeId
  reviewResult.value = data
  reviewResultChange()
}
const reviewResultChange = () => {
  isReviewResult.value = ''
  rules.SuggestBigType = [{ required: true, message: '请选择提案大类', trigger: ['blur', 'change'] }]
  rules.transactType = [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }]
  rules.answerStopDate = [{ required: false, message: '请选择答复截止时间', trigger: ['blur', 'change'] }]
  rules.adjustStopDate = [{ required: false, message: '请选择调整截止时间', trigger: ['blur', 'change'] }]
  for (let index = 0; index < reviewResult.value.length; index++) {
    const item = reviewResult.value[index]
    console.log('item==>', item)
    if (AreaId.value === '370500') {
      isReviewResult.value = 'success'
      form.reviewResult = 'suggestionHandling'
      return
    }
    if (item.nodeId === form.reviewResult) {
      isReviewResult.value = item.formType
      if (item.formType === 'success') {
        rules.transactType = [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }]
        rules.answerStopDate = [{ required: true, message: '请选择答复截止时间', trigger: ['blur', 'change'] }]
        rules.adjustStopDate = [{ required: true, message: '请选择调整截止时间', trigger: ['blur', 'change'] }]
      }
      if (item.formType === 'preAssign') {
        rules.transactType = [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }]
        rules.confirmStopDate = [{ required: true, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]
      }
      if (item.formType === 'sendback') {
        rules.SuggestBigType = [{ required: false, message: '请选择提案大类', trigger: ['blur', 'change'] }]
      }
    }
  }
}
const suggestionThemeSelect = async () => {
  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })
  var { data } = res
  SuggestBigType.value = data
  SuggestBigTypeChange()
}
const SuggestBigTypeChange = () => {
  if (form.SuggestBigType) {
    for (let index = 0; index < SuggestBigType.value.length; index++) {
      const item = SuggestBigType.value[index]
      if (item.id === form.SuggestBigType) {
        if (!item.children.map((v) => v.id).includes(form.SuggestSmallType)) {
          form.SuggestSmallType = ''
        }
        SuggestSmallType.value = item.children
      }
    }
  } else {
    form.SuggestSmallType = ''
    SuggestSmallType.value = []
  }
}
const transactTypeChange = () => {
  if (form.transactType === 'main_assist') {
    rules.mainHandleOfficeId = [{ type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else if (form.transactType === 'publish') {
    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else {
    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  }
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      suggestionComplete()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}

const suggestionComplete = async () => {
  const { code } = await api.suggestionComplete({
    suggestionId: props.id,
    nextNodeId: form.reviewResult, // 交办方式
    bigThemeId: form.SuggestBigType, // 提案大类
    smallThemeId: form.SuggestSmallType, // 提案小类
    variable: {
      handleContent: form.reviewOpinion // 交办意见
    },
    handleOfficeType:
      isReviewResult.value === 'success' || isReviewResult.value === 'preAssign' ? form.transactType : null, // 办理方式
    mainHandleOfficeId:
      isReviewResult.value === 'success' || isReviewResult.value === 'preAssign'
        ? form.mainHandleOfficeId.join('')
        : null, // 主办单位
    handleOfficeIds:
      isReviewResult.value === 'success' || isReviewResult.value === 'preAssign' ? form.handleOfficeIds : null, // 协办或分办单位
    answerStopDate: isReviewResult.value === 'success' ? form.answerStopDate : null,
    adjustStopDate: isReviewResult.value === 'success' ? form.adjustStopDate : null,
    objectParam: {
      handleOfficeIds: isReviewResult.value === 'success' ? form.objectParamHandleOfficeIds.join(',') : null,
    },
    confirmStopDate: isReviewResult.value === 'preAssign' ? form.confirmStopDate : null
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '交办成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
watch(
  () => props.details,
  () => {
    const selectUnit = props.details?.hopeHandleOfficeIds?.map((v) => v.officeId) || []
    unitParams.value = { selectUnit: JSON.stringify(selectUnit), content: props.details.content }
    if (form.SuggestBigType === '') {
      if (props.details.bigThemeId) {
        form.SuggestBigType = props.details.bigThemeId // 提案大类
        form.SuggestSmallType = props.details.smallThemeId // 提案小类
        SuggestBigTypeChange()
      }
    }
  },
  { immediate: true }
)
watch(() => props.transactObj, () => {
  if (form.transactType === '') {
    if (props.transactObj.transactType) {
      form.transactType = props.transactObj.transactType
      form.mainHandleOfficeId = props.transactObj.mainHandleOfficeId
      form.handleOfficeIds = props.transactObj.handleOfficeIds
      transactTypeChange()
    }
  }
}, { immediate: true })

// 监听主办单位变化，清除验证错误
watch(() => form.mainHandleOfficeId, (newVal) => {
  if (newVal && newVal.length > 0 && formRef.value) {
    // 清除主办单位字段的验证错误
    formRef.value.clearValidate('mainHandleOfficeId')
  }
}, { deep: true })

// 监听分办单位变化，清除验证错误
watch(() => form.handleOfficeIds, (newVal) => {
  if (newVal && newVal.length > 0 && formRef.value) {
    // 清除分办单位字段的验证错误
    formRef.value.clearValidate('handleOfficeIds')
  }
}, { deep: true })
watch(() => props.coOrganizerIds, () => {
  form.objectParamHandleOfficeIds = props.coOrganizerIds
}, { deep: true })
</script>
<style lang="scss">
.SuggestAssignDetail {
  width: 100%;
  height: 100%;

  .SuggestAssignDetailNameBody {
    padding: 0 var(--zy-distance-one);
    padding-top: var(--zy-distance-one);

    .SuggestAssignDetailName {
      width: 100%;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      position: relative;
      text-align: center;

      div {
        display: inline-block;
        background-color: #fff;
        position: relative;
        z-index: 2;
        padding: 0 20px;
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 1px;
        background-color: var(--zy-el-color-primary);
      }
    }
  }

  .suggest-simple-select-unit {
    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;
    border-radius: var(--zy-el-input-border-radius,
        var(--zy-el-border-radius-base));
  }
}
</style>
