{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, computed, onMounted, onUnmounted, nextTick, watch, defineAsyncComponent } from 'vue';\nimport { useStore } from 'vuex';\nimport { size2Str } from 'common/js/utils.js';\nimport http_stream from 'common/http/stream.js';\nimport { AiChatClass } from 'common/js/GlobalClass.js';\nimport { globalFileLocation } from 'common/config/location';\nimport config from 'common/config/index';\nimport { user, IntelligentAssistant } from 'common/js/system_var.js';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'GlobalAiChat'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['update:modelValue'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var GlobalMarkdown = defineAsyncComponent(function () {\n      return import('common/components/global-markdown/global-markdown.vue');\n    });\n    var GlobalAiChatFile = defineAsyncComponent(function () {\n      return import('./GlobalAiChatFile');\n    });\n    var GlobalAiChatEditor = defineAsyncComponent(function () {\n      return import('./GlobalAiChatEditor');\n    });\n    var GlobalAiChatHistory = defineAsyncComponent(function () {\n      return import('./GlobalAiChatHistory');\n    });\n    var GlobalAiChatData = defineAsyncComponent(function () {\n      return import('./GlobalAiChatData');\n    });\n    var CustomSatisfactionModal = defineAsyncComponent(function () {\n      return import('./CustomSatisfactionModal');\n    });\n    var GlobalAiChart = defineAsyncComponent(function () {\n      return import('./GlobalAiChart');\n    });\n    var store = useStore();\n    var props = __props;\n    var emit = __emit;\n    var newIcon = '<svg t=\"1741161744028\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"3254\" width=\"26\" height=\"26\"><path d=\"M566.464 150.336c22.144 0 39.744 17.216 40.576 39.296a40.448 40.448 0 0 1-38.08 41.792H183.488v432.128h171.2c21.312 0 38.912 16.384 40.576 37.696v52.416l66.368-76.16a39.872 39.872 0 0 1 27.456-13.952h319.04v-190.08c0-21.248 16.384-38.912 37.76-40.512h2.816c21.312 0 38.912 16.384 40.512 37.696v198.208c0 40.576-31.936 73.728-72.064 75.776H510.784l-125.76 143.808a40.832 40.832 0 0 1-71.296-23.808v-119.552H178.176c-40.576 0-73.728-32-75.776-72.128V226.112c0-40.576 32-73.728 72.064-75.776h392z m35.648 340.8c18.816 0 34.816 14.72 35.648 33.92a35.264 35.264 0 0 1-32.768 36.48h-285.44a35.328 35.328 0 0 1-2.944-70.4h285.504zM443.2 349.824c19.2 0 34.816 15.104 35.648 33.92a35.264 35.264 0 0 1-32.768 36.48H319.488a35.328 35.328 0 0 1-2.88-70.4h126.592z m335.424-229.376c18.432 0 34.048 14.336 35.2 32.768v72.896h70.528a35.264 35.264 0 0 1 2.88 70.4h-72.96v70.464c0 18.88-14.72 34.816-33.92 35.648a35.264 35.264 0 0 1-36.48-32.768V296.96h-70.464a35.264 35.264 0 0 1-2.88-70.4h72.96V155.968a34.816 34.816 0 0 1 35.2-35.584z\" p-id=\"3255\"></path></svg>';\n    var tipsIcon = '<svg t=\"1741241762761\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"7848\" width=\"14\" height=\"14\"><path d=\"M115.152 356.453c-1.492-9.942-2.486-18.724-3.314-25.849-0.829-7.125-0.995-14.251-0.995-21.541 0-22.867 3.314-38.112 9.611-45.237 6.463-7.125 15.41-10.77 27.01-10.77h198.343L410.596 5.001c40.266 2.818 67.109 8.285 80.863 16.239 13.753 7.954 20.546 17.564 20.546 28.998v15.079L460.141 252.89h239.438L766.522 4.836c40.266 2.817 67.108 8.285 80.862 16.238 13.753 7.954 20.547 17.565 20.547 28.998 0 5.8-0.829 10.771-2.154 15.079l-49.71 187.739h170.34c2.817 8.617 4.309 16.902 4.309 24.855v22.701c0 21.541-3.314 36.289-9.776 44.242-6.463 7.954-15.41 11.765-27.01 11.765H788.063L710.35 643.281h200.498c1.326 10.108 2.485 19.056 3.314 27.01a217.169 217.169 0 0 1 1.159 22.701c0 37.448-12.262 56.007-36.619 56.007H682.181l-73.24 269.595c-40.265-2.816-66.942-8.285-79.867-16.072-12.925-7.954-19.387-17.564-19.387-29.164 0-5.634 0.662-10.107 2.154-12.925l56.006-211.269H326.421l-71.086 269.597c-40.265-2.817-67.606-8.286-82.022-16.074-14.416-7.953-21.541-17.564-21.541-29.163 0-2.816 0.331-4.971 0.994-6.462 0.663-1.326 0.994-3.646 0.994-6.463l58.327-211.269H39.592c-2.817-10.107-4.308-19.056-4.308-27.009V699.62c0-21.541 3.314-36.289 9.776-44.242 6.463-7.954 15.41-11.765 27.01-11.765h168.186l75.394-286.829H115.152v-0.331z m239.272 286.828H595.85l77.714-286.828H432.138l-77.714 286.828z\" p-id=\"7849\"></path></svg>';\n    var loadingIcon = '<svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\"><path d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\" fill=\"#2c2c2c\" p-id=\"2363\"></path><path d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\" fill=\"#2c2c2c\" p-id=\"2364\"></path><path d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\" fill=\"#2c2c2c\" p-id=\"2365\"></path><path d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\" fill=\"#2c2c2c\" p-id=\"2366\"></path><path d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\" fill=\"#2c2c2c\" p-id=\"2367\"></path><path d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2368\"></path><path d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2369\"></path><path d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\" fill=\"#2c2c2c\" p-id=\"2370\"></path></svg>';\n    var toolIcon = '<svg t=\"1741338779911\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"9993\" width=\"16\" height=\"16\"><path d=\"M707.2 350.976l-198.496 115.264-195.072-111.712c-37.088-21.12-68.736 34.528-31.648 55.616l198.72 113.792v256.736a32 32 0 0 0 64 0v-258.016c0-1.056-0.512-1.952-0.608-3.008l194.752-113.088c37.088-21.056 5.44-76.704-31.648-55.584z\" p-id=\"9994\"></path><path d=\"M880.288 232.48L560.192 45.12a95.648 95.648 0 0 0-96.64 0L143.68 232.48A96.64 96.64 0 0 0 96 315.904v397.664c0 34.784 18.624 66.88 48.736 84l320 181.92a95.52 95.52 0 0 0 94.496 0l320-181.92A96.576 96.576 0 0 0 928 713.568V315.904a96.64 96.64 0 0 0-47.712-83.424zM864 713.568c0 11.584-6.208 22.304-16.256 28l-320 181.92a31.776 31.776 0 0 1-31.488 0l-320-181.92A32.192 32.192 0 0 1 160 713.568V315.904c0-11.456 6.048-22.016 15.904-27.808l319.872-187.36a31.84 31.84 0 0 1 32.192 0l320.128 187.392c9.856 5.728 15.904 16.32 15.904 27.776v397.664z\" p-id=\"9995\"></path></svg>';\n    var ponderIcon = '<svg t=\"1741658991857\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"11203\" width=\"16\" height=\"16\"><path d=\"M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z\" p-id=\"11204\"></path><path d=\"M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z\" p-id=\"11205\"></path><path d=\"M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z\" p-id=\"11206\"></path></svg>';\n    var elShow = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(value) {\n        emit('update:modelValue', value);\n      }\n    });\n    var elRefs = ref([]);\n    var getElRef = function getElRef(el, i) {\n      var index = (i + 1) / 2 - 1;\n      if (el) elRefs.value[index] = el;\n    };\n    var elRef = computed(function () {\n      return elRefs.value[elRefs.value.length - 1];\n    });\n    var elPonderRefs = ref([]);\n    var getElPonderRef = function getElPonderRef(el, i) {\n      var index = (i + 1) / 2 - 1;\n      if (el) elPonderRefs.value[index] = el;\n    };\n    var elPonderRef = computed(function () {\n      return elPonderRefs.value[elPonderRefs.value.length - 1];\n    });\n    var AiChatPrompt = ref('');\n    var AiChatId = ref('');\n    var AiChatCode = ref('test_chat');\n    var AiChatModule = ref('');\n    var AiChatModuleId = ref('');\n    var elIndex = ref(0);\n    var AiChatParams = ref({});\n    var AiChatContent = ref('');\n    var aiTools = ref([]);\n    var aiWords = ref([]);\n    var chatId = ref('');\n    var chatObj = ref({});\n    var chatContent = ref('');\n    var chatMessage = ref([]);\n    var chatMessageTotal = ref(0);\n    var chatMessageUpdate = ref(0);\n    var wrapScrollHeight = ref(0);\n    var chatHistoryRef = ref();\n    var scrollRef = ref();\n    var editorRef = ref();\n    var fileList = ref([]);\n    var fileData = ref([]);\n    var sendContent = ref('');\n    var isScroll = ref(false);\n    var toolId = ref('');\n    var toolName = ref('');\n    var toolIsParam = ref(0);\n    var toolRequired = ref(0);\n    var currentRequest = null;\n    var loading = ref(false);\n    var disabled = ref(false);\n    var isStreaming = ref(false);\n    var startTime = null;\n    var endTime = null;\n    var dataName = ref('');\n    var dataInfo = ref({});\n    var dataShow = ref(false);\n    var satisfactionSurveyShow = ref(false);\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var fileIcon = function fileIcon(fileType) {\n      var IconClass = {\n        docx: 'globalFileWord',\n        doc: 'globalFileWord',\n        wps: 'globalFileWPS',\n        xlsx: 'globalFileExcel',\n        xls: 'globalFileExcel',\n        pdf: 'globalFilePDF',\n        pptx: 'globalFilePPT',\n        ppt: 'globalFilePPT',\n        txt: 'globalFileTXT',\n        jpg: 'globalFilePicture',\n        png: 'globalFilePicture',\n        gif: 'globalFilePicture',\n        avi: 'globalFileVideo',\n        mp4: 'globalFileVideo',\n        zip: 'globalFileCompress',\n        rar: 'globalFileCompress'\n      };\n      return IconClass[fileType] || 'globalFileUnknown';\n    };\n    var formatDuring = function formatDuring(mss) {\n      var days = parseInt(mss / (1000 * 60 * 60 * 24));\n      var hours = parseInt(mss % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n      var minutes = parseInt(mss % (1000 * 60 * 60) / (1000 * 60));\n      var seconds = mss % (1000 * 60) / 1000;\n      var time = '';\n      if (days > 0) time += `${days} 天 `;\n      if (hours > 0) time += `${hours} 小时 `;\n      if (minutes > 0) time += `${minutes} 分钟 `;\n      if (seconds > 0) time += `${seconds} 秒 `;\n      return time;\n    };\n    var handlePreview = function handlePreview(row) {\n      globalFileLocation({\n        name: process.env.VUE_APP_NAME,\n        fileId: row.id,\n        fileType: row.extName,\n        fileName: row.originalFileName,\n        fileSize: row.fileSize\n      });\n    };\n    var handleClick = function handleClick() {\n      elShow.value = !elShow.value;\n    };\n    var handleNewDialogue = function handleNewDialogue() {\n      elRefs.value = [];\n      elPonderRefs.value = [];\n      chatMessageTotal.value = 0;\n      handleToolClose();\n      handleStopMessage();\n      chatId.value = guid();\n      chatContent.value = '';\n      chatMessage.value = [];\n    };\n    var handleFileUpload = function handleFileUpload(data) {\n      fileList.value = data;\n    };\n    var handleFileCallback = function handleFileCallback(data) {\n      fileData.value = data;\n    };\n    var handleClose = function handleClose(item) {\n      var _editorRef$value;\n      (_editorRef$value = editorRef.value) === null || _editorRef$value === void 0 || _editorRef$value.handleSetFile(fileData.value.filter(function (v) {\n        return v.id !== item.id;\n      }));\n    };\n    var handleSelect = function handleSelect(data, type) {\n      chatMessageTotal.value = 0;\n      chatContent.value = data.userQuestion;\n      if (!type) {\n        handleStopMessage();\n        chatMessage.value = [];\n        handleChatMessage();\n      } else {\n        chatObj.value[AiChatCode.value] = {\n          chatId: data.id,\n          businessId: data.businessId || ''\n        };\n      }\n    };\n    var handleChatMessage = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(value) {\n        var _yield$api$aigptChatL, data, total, updateVal, index, item, _index, _item;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.aigptChatLogsList({\n                pageNo: value || 1,\n                pageSize: value ? 1 : 10,\n                isAsc: 1,\n                query: {\n                  chatId: chatId.value\n                }\n              });\n            case 2:\n              _yield$api$aigptChatL = _context.sent;\n              data = _yield$api$aigptChatL.data;\n              total = _yield$api$aigptChatL.total;\n              chatMessageTotal.value = total;\n              if (value) {\n                updateVal = 0;\n                for (index = 0; index < data.length; index++) {\n                  item = data[index];\n                  if (item.reasoning) updateVal += 1;\n                  if (item.answer) updateVal += 1;\n                  chatMessage.value.unshift({\n                    id: guid(),\n                    type: false,\n                    ponderShow: true,\n                    isControls: true,\n                    content: '',\n                    contentOld: item.answer,\n                    ponderContent: '',\n                    ponderContentOld: item.reasoning,\n                    time: item.reasoning ? '1' : '',\n                    dataList: [],\n                    fileData: [],\n                    chartData: [],\n                    guideWord: []\n                  });\n                  chatMessage.value.unshift({\n                    id: guid(),\n                    type: true,\n                    ponderShow: true,\n                    isControls: true,\n                    content: item.userQuestion,\n                    contentOld: '',\n                    ponderContent: '',\n                    ponderContentOld: '',\n                    time: '',\n                    dataList: [],\n                    fileData: item.attachments,\n                    chartData: [],\n                    guideWord: []\n                  });\n                }\n                chatMessageUpdate.value = updateVal;\n                nextTick(function () {\n                  scrollElHeight();\n                });\n              } else {\n                for (_index = 0; _index < data.length; _index++) {\n                  _item = data[_index];\n                  chatMessage.value.push({\n                    id: guid(),\n                    type: true,\n                    ponderShow: true,\n                    isControls: true,\n                    content: _item.userQuestion,\n                    contentOld: '',\n                    ponderContent: '',\n                    ponderContentOld: '',\n                    time: '',\n                    dataList: [],\n                    fileData: _item.attachments,\n                    chartData: [],\n                    guideWord: []\n                  });\n                  chatMessage.value.push({\n                    id: guid(),\n                    type: false,\n                    ponderShow: true,\n                    isControls: true,\n                    content: '',\n                    contentOld: _item.answer,\n                    ponderContent: '',\n                    ponderContentOld: _item.reasoning,\n                    time: _item.reasoning ? '1' : '',\n                    dataList: [],\n                    fileData: [],\n                    chartData: [],\n                    guideWord: []\n                  });\n                }\n                isScroll.value = false;\n                nextTick(function () {\n                  scrollDown();\n                });\n              }\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function handleChatMessage(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleUpdate = function handleUpdate() {\n      if (!isScroll.value && wrapScrollHeight.value) {\n        nextTick(function () {\n          scrollElHeight();\n        });\n        chatMessageUpdate.value = chatMessageUpdate.value - 1;\n      } else {\n        chatMessageUpdate.value = 0;\n        nextTick(function () {\n          scrollDown();\n        });\n      }\n    };\n    var handleScroll = function handleScroll(_ref3) {\n      var scrollTop = _ref3.scrollTop;\n      if (scrollTop === 0) {\n        wrapScrollHeight.value = scrollRef.value.wrapRef.scrollHeight;\n        if (chatMessageTotal.value && chatMessageTotal.value > chatMessage.value.length / 2) {\n          handleChatMessage(chatMessage.value.length / 2 + 1);\n        }\n      }\n      var _scrollRef$value$wrap = scrollRef.value.wrapRef,\n        scrollHeight = _scrollRef$value$wrap.scrollHeight,\n        clientHeight = _scrollRef$value$wrap.clientHeight;\n      if (scrollHeight - scrollTop <= clientHeight + 52) {\n        isScroll.value = false;\n      } else {\n        isScroll.value = true;\n      }\n    };\n    var scrollDown = function scrollDown() {\n      if (isScroll.value) return;\n      scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight;\n    };\n    var scrollElHeight = function scrollElHeight() {\n      scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - wrapScrollHeight.value;\n    };\n    var handleGuideWord = function handleGuideWord(data) {\n      console.log('[]', data);\n      if (isStreaming.value) return ElMessage({\n        type: 'warning',\n        message: '请先完成上一次对话再进行新对话！'\n      });\n      handleCloseMessage();\n      loading.value = true;\n      disabled.value = true;\n      chatMessage.value.push({\n        id: guid(),\n        type: true,\n        ponderShow: true,\n        isControls: true,\n        content: data.question,\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: fileData.value,\n        chartData: []\n      });\n      chatMessage.value.push({\n        id: guid(),\n        type: false,\n        ponderShow: true,\n        isControls: false,\n        content: '',\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: [],\n        chartData: [],\n        guideWord: []\n      });\n      nextTick(function () {\n        var _editorRef$value2;\n        scrollDown();\n        handleHttpStream(data);\n        (_editorRef$value2 = editorRef.value) === null || _editorRef$value2 === void 0 || _editorRef$value2.handleSetFile([]);\n        handleToolClose();\n      });\n    };\n    var handleSendMessage = function handleSendMessage(value) {\n      if (isStreaming.value) return ElMessage({\n        type: 'warning',\n        message: '请先完成上一次对话再进行新对话！'\n      });\n      if (toolRequired.value && !fileData.value.length) return ElMessage({\n        type: 'warning',\n        message: `请先上传相关资料在进行${toolName.value}!`\n      });\n      handleCloseMessage();\n      loading.value = true;\n      disabled.value = true;\n      chatMessage.value.push({\n        id: guid(),\n        type: true,\n        ponderShow: true,\n        isControls: true,\n        content: value,\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: fileData.value,\n        chartData: []\n      });\n      chatMessage.value.push({\n        id: guid(),\n        type: false,\n        ponderShow: true,\n        isControls: false,\n        content: '',\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: [],\n        chartData: [],\n        guideWord: []\n      });\n      var fileId = fileData.value.map(function (v) {\n        return v.id;\n      }).join(',');\n      var defaultParams = toolId.value ? {\n        question: value,\n        tool: toolId.value,\n        attachmentIds: fileId\n      } : {\n        question: value,\n        attachmentIds: fileId\n      };\n      var params = toolIsParam.value ? _objectSpread(_objectSpread({}, defaultParams), {}, {\n        param: {\n          pageContent: AiChatContent.value\n        }\n      }) : defaultParams;\n      nextTick(function () {\n        var _editorRef$value3;\n        scrollDown();\n        handleHttpStream(params);\n        (_editorRef$value3 = editorRef.value) === null || _editorRef$value3 === void 0 || _editorRef$value3.handleSetFile([]);\n        handleToolClose();\n      });\n    };\n    var handleTips = function handleTips(text) {\n      var parts = text.split(/(\\{[^}]+\\})/);\n      var result = parts.map(function (part) {\n        if (part.startsWith('{') && part.endsWith('}')) {\n          return {\n            value: part.slice(1, -1),\n            type: true\n          };\n        } else if (part.trim() !== '') {\n          return {\n            value: part,\n            type: false\n          };\n        }\n      }).filter(function (item) {\n        return item !== undefined;\n      });\n      return result;\n    };\n    var handleToolClose = function handleToolClose() {\n      var _editorRef$value4, _editorRef$value5;\n      toolId.value = '';\n      toolName.value = '';\n      toolIsParam.value = 0;\n      toolRequired.value = 0;\n      (_editorRef$value4 = editorRef.value) === null || _editorRef$value4 === void 0 || _editorRef$value4.handleSetFile([]);\n      (_editorRef$value5 = editorRef.value) === null || _editorRef$value5 === void 0 || _editorRef$value5.handleSetContent('');\n    };\n    var handleLinkClick = function handleLinkClick(_ref4) {\n      var href = _ref4.href,\n        text = _ref4.text,\n        event = _ref4.event;\n      console.log('链接被点击:', href, text);\n      if (text === '查看原文比对') {\n        // store.commit('setOpenRoute', { name: '查看原文比对', path: '/VersionComparisonAi', query: { chatId: chatId.value } })\n        var token = sessionStorage.getItem('token') || '';\n        window.open(`${config.mainPath}VersionComparisonAi?chatId=${chatId.value}&token=${token}`, '_blank');\n      } else {\n        window.open(href, '_blank');\n      }\n    };\n    var handleToolSendMessage = function handleToolSendMessage(value) {\n      var id = value.id,\n        chatToolName = value.chatToolName,\n        isParam = value.isParam,\n        userPromptTip = value.userPromptTip,\n        needTool = value.needTool;\n      if (userPromptTip) {\n        var _editorRef$value6;\n        toolId.value = id;\n        toolName.value = chatToolName;\n        toolIsParam.value = isParam;\n        toolRequired.value = needTool;\n        (_editorRef$value6 = editorRef.value) === null || _editorRef$value6 === void 0 || _editorRef$value6.handleInsertPlaceholder(handleTips(userPromptTip));\n        return;\n      }\n      if (needTool && !fileData.value.length) return ElMessage({\n        type: 'warning',\n        message: `请先上传相关资料再进行${chatToolName}！`\n      });\n      var finallySendContent = isParam ? chatToolName : AiChatContent.value || sendContent.value;\n      if (!finallySendContent) return ElMessage({\n        type: 'warning',\n        message: `请先输入内容再进行${chatToolName}！`\n      });\n      handleStopMessage();\n      loading.value = true;\n      disabled.value = true;\n      // chatId.value = guid()\n      // chatContent.value = ''\n      // chatMessage.value = []\n      chatMessage.value.push({\n        id: guid(),\n        type: true,\n        ponderShow: true,\n        isControls: true,\n        content: chatToolName,\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: fileData.value,\n        chartData: [],\n        guideWord: []\n      });\n      chatMessage.value.push({\n        id: guid(),\n        type: false,\n        ponderShow: true,\n        isControls: false,\n        content: '',\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: [],\n        chartData: [],\n        guideWord: []\n      });\n      var fileId = fileData.value.map(function (v) {\n        return v.id;\n      }).join(',');\n      var defaultParams = {\n        question: finallySendContent,\n        tool: id,\n        attachmentIds: fileId\n      };\n      var params = isParam ? _objectSpread(_objectSpread({}, defaultParams), {}, {\n        param: {\n          pageContent: AiChatContent.value\n        }\n      }) : defaultParams;\n      nextTick(function () {\n        var _editorRef$value7, _editorRef$value8;\n        scrollDown();\n        handleHttpStream(params, fileData.value);\n        (_editorRef$value7 = editorRef.value) === null || _editorRef$value7 === void 0 || _editorRef$value7.handleSetFile([]);\n        (_editorRef$value8 = editorRef.value) === null || _editorRef$value8 === void 0 || _editorRef$value8.handleSetContent('');\n      });\n    };\n    var stringToJson = function stringToJson(str) {\n      // 替换属性名\n      str = str.replace(/(\\w+):/g, '\"$1\":');\n      // 替换单引号为双引号\n      str = str.replace(/'/g, '\"');\n      var obj = JSON.parse(str);\n      return obj;\n    };\n    var handleHttpStream = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var params,\n          AiChatParam,\n          data,\n          quoteList,\n          newChartData,\n          newGuideWord,\n          index,\n          item,\n          _item$quoteList,\n          i,\n          row,\n          _choice$,\n          _elRef$value,\n          choice,\n          details,\n          _elPonderRef$value,\n          executionTime,\n          _elRef$value2,\n          _chatHistoryRef$value,\n          _elRef$value3,\n          _args2 = arguments;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              params = _args2.length > 0 && _args2[0] !== undefined ? _args2[0] : {};\n              isStreaming.value = true;\n              _context2.prev = 2;\n              AiChatParam = {};\n              if (params.param && AiChatParams.value.param) {\n                AiChatParam = {\n                  param: _objectSpread(_objectSpread({}, params.param), AiChatParams.value.param)\n                };\n              }\n              AiChatParam = _objectSpread(_objectSpread(_objectSpread({}, params), AiChatParams.value), AiChatParam);\n              startTime = new Date();\n              currentRequest = http_stream('/aigpt/chatStream', {\n                body: JSON.stringify(_objectSpread({\n                  chatBusinessScene: AiChatCode.value,\n                  chatId: chatId.value\n                }, AiChatParam)),\n                onMessage(event) {\n                  // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\n                  loading.value = false;\n                  if (event.data !== '[DONE]') {\n                    data = JSON.parse(event.data);\n                    console.log('[]', data);\n                    if (Array.isArray(data)) {\n                      // console.log('[]', data)\n                      quoteList = [];\n                      newChartData = [];\n                      newGuideWord = [];\n                      for (index = 0; index < data.length; index++) {\n                        item = data[index];\n                        if (typeof item === 'string') {\n                          newGuideWord.push(_objectSpread(_objectSpread({}, AiChatParam), {}, {\n                            question: item\n                          }));\n                        } else {\n                          if (item !== null && item !== void 0 && (_item$quoteList = item.quoteList) !== null && _item$quoteList !== void 0 && _item$quoteList.length) {\n                            for (i = 0; i < (item === null || item === void 0 ? void 0 : item.quoteList.length); i++) {\n                              row = item === null || item === void 0 ? void 0 : item.quoteList[i];\n                              quoteList.push(_objectSpread(_objectSpread({}, row), {}, {\n                                markdownContent: row.q\n                              }));\n                            }\n                          }\n                          if (item !== null && item !== void 0 && item.echartItem) {\n                            newChartData.push(stringToJson(item === null || item === void 0 ? void 0 : item.echartItem));\n                          }\n                        }\n                      }\n                      chatMessage.value[chatMessage.value.length - 1].dataList = quoteList;\n                      chatMessage.value[chatMessage.value.length - 1].chartData = newChartData;\n                      chatMessage.value[chatMessage.value.length - 1].guideWord = newGuideWord;\n                    } else {\n                      // console.log('{}', data)\n                      choice = (data === null || data === void 0 ? void 0 : data.choices) || [{}];\n                      details = ((_choice$ = choice[0]) === null || _choice$ === void 0 ? void 0 : _choice$.delta) || {};\n                      if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\n                        (_elPonderRef$value = elPonderRef.value) === null || _elPonderRef$value === void 0 || _elPonderRef$value.enqueueRender(details.reasoning_content || '');\n                        if (chatMessage.value[chatMessage.value.length - 1].time) {\n                          startTime = null;\n                          endTime = null;\n                        } else {\n                          endTime = new Date();\n                          executionTime = endTime - startTime;\n                          chatMessage.value[chatMessage.value.length - 1].time = formatDuring(executionTime);\n                        }\n                      }\n                      if (Object.prototype.hasOwnProperty.call(details, 'content')) (_elRef$value = elRef.value) === null || _elRef$value === void 0 || _elRef$value.enqueueRender(details.content || '');\n                      nextTick(function () {\n                        scrollDown();\n                      });\n                    }\n                  } else {\n                    // console.log(event.data)\n                    (_elRef$value2 = elRef.value) === null || _elRef$value2 === void 0 || _elRef$value2.enqueueRender('');\n                    nextTick(function () {\n                      scrollDown();\n                    });\n                    disabled.value = false;\n                    isStreaming.value = false;\n                    chatMessage.value[chatMessage.value.length - 1].isControls = true;\n                    if (!chatContent.value) (_chatHistoryRef$value = chatHistoryRef.value) === null || _chatHistoryRef$value === void 0 || _chatHistoryRef$value.refresh();\n                  }\n                },\n                onError(err) {\n                  console.log('流式接口错误:', err);\n                },\n                onClose() {\n                  loading.value = false;\n                  disabled.value = false;\n                  isStreaming.value = false;\n                  console.log('流式接口关闭');\n                }\n              });\n              _context2.next = 10;\n              return currentRequest.promise;\n            case 10:\n              _context2.next = 20;\n              break;\n            case 12:\n              _context2.prev = 12;\n              _context2.t0 = _context2[\"catch\"](2);\n              loading.value = false;\n              disabled.value = false;\n              isStreaming.value = false;\n              (_elRef$value3 = elRef.value) === null || _elRef$value3 === void 0 || _elRef$value3.enqueueRender('服务器繁忙，请稍后再试。');\n              nextTick(function () {\n                scrollDown();\n              });\n              console.error('启动流式接口失败:', _context2.t0);\n            case 20:\n              _context2.prev = 20;\n              return _context2.finish(20);\n            case 22:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[2, 12, 20, 22]]);\n      }));\n      return function handleHttpStream() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handleDataList = function handleDataList(row) {\n      dataName.value = row.sourceName;\n      dataInfo.value = row;\n      dataShow.value = true;\n    };\n    var handleCopyMessage = function handleCopyMessage(content, i) {\n      var _elRefs$value$index;\n      var index = (i + 1) / 2 - 1;\n      var copyContent = ((_elRefs$value$index = elRefs.value[index]) === null || _elRefs$value$index === void 0 || (_elRefs$value$index = _elRefs$value$index.elRef) === null || _elRefs$value$index === void 0 ? void 0 : _elRefs$value$index.innerText) || content;\n      var textarea = document.createElement('textarea');\n      textarea.readOnly = 'readonly';\n      textarea.style.position = 'absolute';\n      textarea.style.left = '-9999px';\n      textarea.value = copyContent;\n      document.body.appendChild(textarea);\n      textarea.select();\n      var result = document.execCommand('Copy');\n      if (result) {\n        ElMessage({\n          message: '复制成功',\n          type: 'success'\n        });\n      }\n      document.body.removeChild(textarea);\n    };\n    var handleRetryMessage = function handleRetryMessage(item, index) {\n      var length = chatMessage.value.length - 1;\n      if (index === length && currentRequest) {\n        loading.value = true;\n        disabled.value = true;\n        isStreaming.value = true;\n        chatMessage.value[length] = {\n          id: guid(),\n          type: false,\n          ponderShow: true,\n          isControls: false,\n          content: '',\n          contentOld: '',\n          ponderContent: '',\n          ponderContentOld: '',\n          time: '',\n          dataList: [],\n          fileData: [],\n          chartData: [],\n          guideWord: []\n        };\n        nextTick(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n          return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n            while (1) switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.next = 2;\n                return currentRequest.retry();\n              case 2:\n              case \"end\":\n                return _context3.stop();\n            }\n          }, _callee3);\n        })));\n      } else {\n        isScroll.value = false;\n        fileData.value = chatMessage.value[index - 1].fileData;\n        handleSendMessage(chatMessage.value[index - 1].content);\n      }\n    };\n    var handleSatisfactionSurvey = function handleSatisfactionSurvey(item, index) {\n      var _chatMessage$value$qu, _elRefs$value$answerI;\n      // 获取当前回答对应的用户问题\n      var questionIndex = index - 1;\n      var question = ((_chatMessage$value$qu = chatMessage.value[questionIndex]) === null || _chatMessage$value$qu === void 0 ? void 0 : _chatMessage$value$qu.content) || '';\n      var answerIndex = (index + 1) / 2 - 1;\n      var answer = ((_elRefs$value$answerI = elRefs.value[answerIndex]) === null || _elRefs$value$answerI === void 0 || (_elRefs$value$answerI = _elRefs$value$answerI.elRef) === null || _elRefs$value$answerI === void 0 ? void 0 : _elRefs$value$answerI.innerText) || item.content || item.contentOld || '';\n      // 构建传递给满意度调查的数据\n      dataInfo.value = _objectSpread(_objectSpread({}, item), {}, {\n        question: question,\n        answer: answer,\n        chatId: chatId.value,\n        chatContent: chatContent.value\n      });\n      console.log('dataInfo.value==>', dataInfo.value);\n      satisfactionSurveyShow.value = true;\n    };\n    var handleCloseMessage = function handleCloseMessage() {\n      currentRequest = null;\n      loading.value = false;\n      disabled.value = false;\n      isStreaming.value = false;\n    };\n    var handleStopMessage = function handleStopMessage() {\n      if (currentRequest) {\n        var _chatHistoryRef$value2;\n        currentRequest.abort();\n        loading.value = false;\n        disabled.value = false;\n        isStreaming.value = false;\n        console.log('启动流式接口停止');\n        if (!chatContent.value) (_chatHistoryRef$value2 = chatHistoryRef.value) === null || _chatHistoryRef$value2 === void 0 || _chatHistoryRef$value2.refresh();\n      }\n      handleCloseMessage();\n    };\n    var aigptChatSceneDetail = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _yield$api$aigptChatS, data;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.aigptChatSceneDetail({\n                key: guid(),\n                query: {\n                  chatSceneCode: AiChatCode.value\n                }\n              });\n            case 2:\n              _yield$api$aigptChatS = _context4.sent;\n              data = _yield$api$aigptChatS.data;\n              aiTools.value = (data === null || data === void 0 ? void 0 : data.tools) || [];\n              aiWords.value = (data === null || data === void 0 ? void 0 : data.promptWords) || [];\n              chatObj.value[AiChatCode.value] = {\n                chatId: chatId.value,\n                businessId: AiChatModuleId.value\n              };\n            case 7:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function aigptChatSceneDetail() {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var handleHistoryMessage = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5(code, businessId) {\n        var _chatHistoryRef$value3;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              elIndex.value = 1;\n              (_chatHistoryRef$value3 = chatHistoryRef.value) === null || _chatHistoryRef$value3 === void 0 || _chatHistoryRef$value3.handleCurrentChat(code, businessId);\n            case 2:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function handleHistoryMessage(_x2, _x3) {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    var isEmptyObject = function isEmptyObject(obj) {\n      if (typeof obj !== 'object' || obj === null) return false;\n      return Object.keys(obj).length !== 0;\n    };\n    /**\r\n     * AiChatCode 场景code String\r\n     * --主工程调用 store.commit('setAiChatCode', '')\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatCode: '' })\r\n     *\r\n     * AiChatConfig 批量设置参数 Object\r\n     * AiChatConfig可以设置 AiChatWindow AiChatParams AiChatContent AiChatSetContent AiChatAddContent AiChatSendMessage AiChatToolSendMessage\r\n     * --主工程调用 store.commit('setAiChatConfig', { AiChatWindow, AiChatParams, AiChatContent, AiChatSetContent, AiChatAddContent, AiChatSendMessage, AiChatToolSendMessage })\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatConfig: { AiChatWindow, AiChatParams, AiChatContent, AiChatSetContent, AiChatAddContent, AiChatSendMessage, AiChatToolSendMessage } })\r\n     *\r\n     * AiChatWindow 窗口是否显示 Boolean\r\n     * --主工程调用 store.commit('setAiChatWindow', true / false)\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatWindow: true / false })\r\n     *\r\n     * AiChatParams 其他传参 Object\r\n     * --主工程调用 store.commit('setAiChatParams', {})\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatParams: {} })\r\n     *\r\n     * AiChatContent 内容参数 String\r\n     * --主工程调用 store.commit('setAiChatContent', '')\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n     *\r\n     * AiChatSetContent 替换内容参数 String\r\n     * --主工程调用 store.commit('setAiChatSetContent', '')\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatSetContent: '' })\r\n     *\r\n     * AiChatAddContent 追加内容参数 String\r\n     * --主工程调用 store.commit('setAiChatAddContent', '')\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatAddContent: '' })\r\n     *\r\n     * AiChatSendMessage 发送消息 String\r\n     * --主工程调用 store.commit('setAiChatSendMessage', '')\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatSendMessage: '' })\r\n     *\r\n     * AiChatToolSendMessage 点击工具 Object\r\n     * --主工程调用 store.commit('setAiChatToolSendMessage', {})\r\n     * --子工程调用 qiankunMicro.setGlobalState({ AiChatToolSendMessage: {} })\r\n     */\n    watch(function () {\n      return store.state.AiChatCode;\n    }, function (val) {\n      if (val) {\n        AiChatCode.value = val;\n        handleToolClose();\n        handleNewDialogue();\n        aigptChatSceneDetail();\n      }\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatModuleId;\n    }, function (val) {\n      AiChatModuleId.value = val;\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatConfig;\n    }, function (val) {\n      if (isEmptyObject(val)) {\n        var _editorRef$value9, _editorRef$value10, _editorRef$value11;\n        if (val.hasOwnProperty('AiChatWindow')) elShow.value = !!val.AiChatWindow;\n        if (val.hasOwnProperty('AiChatFile')) (_editorRef$value9 = editorRef.value) === null || _editorRef$value9 === void 0 || _editorRef$value9.handleSetFile(val.AiChatFile);\n        if (val.hasOwnProperty('AiChatParams')) AiChatParams.value = val.AiChatParams;\n        if (val.hasOwnProperty('AiChatContent')) AiChatContent.value = val.AiChatContent;\n        if (val.hasOwnProperty('AiChatSetContent')) (_editorRef$value10 = editorRef.value) === null || _editorRef$value10 === void 0 || _editorRef$value10.handleSetContent(val.AiChatSetContent);\n        if (val.hasOwnProperty('AiChatAddContent')) (_editorRef$value11 = editorRef.value) === null || _editorRef$value11 === void 0 || _editorRef$value11.handleAddContent(val.AiChatAddContent);\n        if (val.hasOwnProperty('AiChatSendMessage')) if (val.AiChatSendMessage) handleSendMessage(val.AiChatSendMessage);\n        if (val.hasOwnProperty('AiChatToolSendMessage')) if (isEmptyObject(val.AiChatToolSendMessage)) handleToolSendMessage(val.AiChatToolSendMessage);\n      }\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatWindow;\n    }, function (val) {\n      elShow.value = !!val;\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatFile;\n    }, function (val) {\n      var _editorRef$value12;\n      (_editorRef$value12 = editorRef.value) === null || _editorRef$value12 === void 0 || _editorRef$value12.handleSetFile(val);\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatParams;\n    }, function (val) {\n      AiChatParams.value = val;\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatContent;\n    }, function (val) {\n      AiChatContent.value = val;\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatSetContent;\n    }, function (val) {\n      var _editorRef$value13;\n      (_editorRef$value13 = editorRef.value) === null || _editorRef$value13 === void 0 || _editorRef$value13.handleSetContent(val);\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatAddContent;\n    }, function (val) {\n      var _editorRef$value14;\n      (_editorRef$value14 = editorRef.value) === null || _editorRef$value14 === void 0 || _editorRef$value14.handleAddContent(val);\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatSendMessage;\n    }, function (val) {\n      if (val) handleSendMessage(val);\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return store.state.AiChatToolSendMessage;\n    }, function (val) {\n      if (isEmptyObject(val)) handleToolSendMessage(val);\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return elShow.value;\n    }, function (val) {\n      if (val && elIndex.value) {\n        elIndex.value = 0;\n        nextTick(function () {\n          scrollDown();\n        });\n      }\n    }, {\n      immediate: true\n    });\n    var handleAiChatConfig = function handleAiChatConfig(data) {\n      var _data$AiChatId = data.AiChatId,\n        newAiChatId = _data$AiChatId === void 0 ? '' : _data$AiChatId,\n        _data$AiChatCode = data.AiChatCode,\n        newAiChatCode = _data$AiChatCode === void 0 ? '' : _data$AiChatCode,\n        _data$AiChatModule = data.AiChatModule,\n        newAiChatModule = _data$AiChatModule === void 0 ? '' : _data$AiChatModule,\n        _data$AiChatModuleId = data.AiChatModuleId,\n        newAiChatModuleId = _data$AiChatModuleId === void 0 ? '' : _data$AiChatModuleId,\n        _data$AiChatWindow = data.AiChatWindow,\n        newAiChatWindow = _data$AiChatWindow === void 0 ? false : _data$AiChatWindow,\n        _data$AiChatFile = data.AiChatFile,\n        newAiChatFile = _data$AiChatFile === void 0 ? [] : _data$AiChatFile,\n        _data$AiChatClearFile = data.AiChatClearFile,\n        newAiChatClearFile = _data$AiChatClearFile === void 0 ? false : _data$AiChatClearFile,\n        _data$AiChatParams = data.AiChatParams,\n        newAiChatParams = _data$AiChatParams === void 0 ? {} : _data$AiChatParams,\n        _data$AiChatContent = data.AiChatContent,\n        newAiChatContent = _data$AiChatContent === void 0 ? '' : _data$AiChatContent;\n      AiChatId.value = newAiChatId;\n      AiChatCode.value = newAiChatCode;\n      AiChatModule.value = newAiChatModule;\n      AiChatModuleId.value = newAiChatModuleId;\n      elShow.value = typeof newAiChatWindow === 'boolean' ? newAiChatWindow : false;\n      if ((typeof newAiChatClearFile === 'boolean' ? newAiChatClearFile : false) || newAiChatFile.length) {\n        var _editorRef$value15;\n        (_editorRef$value15 = editorRef.value) === null || _editorRef$value15 === void 0 || _editorRef$value15.handleSetFile(newAiChatFile);\n      }\n      AiChatParams.value = newAiChatParams;\n      AiChatContent.value = newAiChatContent;\n    };\n    var handleAiChatEditor = function handleAiChatEditor() {\n      var _editorRef$value16, _editorRef$value17;\n      var type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      var content = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      var newType = typeof type === 'boolean' ? type : true;\n      if (newType) (_editorRef$value16 = editorRef.value) === null || _editorRef$value16 === void 0 || _editorRef$value16.handleSetContent(content);\n      if (!newType) (_editorRef$value17 = editorRef.value) === null || _editorRef$value17 === void 0 || _editorRef$value17.handleAddContent(content);\n    };\n    var handleAiChatHistory = function handleAiChatHistory() {\n      var newAiChatCode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n      var newAiChatModuleId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      handleToolClose();\n      handleNewDialogue();\n      aigptChatSceneDetail();\n      if (newAiChatCode) handleHistoryMessage(newAiChatCode, newAiChatModuleId);\n    };\n    var handleAiChatSend = function handleAiChatSend(content) {\n      if (content) {\n        elShow.value = true;\n        handleSendMessage(content);\n      }\n    };\n    var handleAiChatToolSend = function handleAiChatToolSend(data) {\n      if (isEmptyObject(data)) {\n        elShow.value = true;\n        handleToolSendMessage(data);\n      }\n    };\n    AiChatClass.prototype.AiChatConfig = handleAiChatConfig;\n    AiChatClass.prototype.AiChatEditor = handleAiChatEditor;\n    AiChatClass.prototype.AiChatHistory = handleAiChatHistory;\n    AiChatClass.prototype.AiChatSend = handleAiChatSend;\n    AiChatClass.prototype.AiChatToolSend = handleAiChatToolSend;\n    var globalReadConfig = /*#__PURE__*/function () {\n      var _ref9 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var _yield$api$globalRead, data;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.next = 2;\n              return api.globalReadConfig({\n                codes: ['AiChatPrompt']\n              });\n            case 2:\n              _yield$api$globalRead = _context6.sent;\n              data = _yield$api$globalRead.data;\n              AiChatPrompt.value = data.AiChatPrompt;\n            case 5:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function globalReadConfig() {\n        return _ref9.apply(this, arguments);\n      };\n    }();\n    onMounted(function () {\n      globalReadConfig();\n      handleNewDialogue();\n      aigptChatSceneDetail();\n    });\n    onUnmounted(function () {});\n    var __returned__ = {\n      GlobalMarkdown,\n      GlobalAiChatFile,\n      GlobalAiChatEditor,\n      GlobalAiChatHistory,\n      GlobalAiChatData,\n      CustomSatisfactionModal,\n      GlobalAiChart,\n      store,\n      props,\n      emit,\n      newIcon,\n      tipsIcon,\n      loadingIcon,\n      toolIcon,\n      ponderIcon,\n      elShow,\n      elRefs,\n      getElRef,\n      elRef,\n      elPonderRefs,\n      getElPonderRef,\n      elPonderRef,\n      AiChatPrompt,\n      AiChatId,\n      AiChatCode,\n      AiChatModule,\n      AiChatModuleId,\n      elIndex,\n      AiChatParams,\n      AiChatContent,\n      aiTools,\n      aiWords,\n      chatId,\n      chatObj,\n      chatContent,\n      chatMessage,\n      chatMessageTotal,\n      chatMessageUpdate,\n      wrapScrollHeight,\n      chatHistoryRef,\n      scrollRef,\n      editorRef,\n      fileList,\n      fileData,\n      sendContent,\n      isScroll,\n      toolId,\n      toolName,\n      toolIsParam,\n      toolRequired,\n      get currentRequest() {\n        return currentRequest;\n      },\n      set currentRequest(v) {\n        currentRequest = v;\n      },\n      loading,\n      disabled,\n      isStreaming,\n      get startTime() {\n        return startTime;\n      },\n      set startTime(v) {\n        startTime = v;\n      },\n      get endTime() {\n        return endTime;\n      },\n      set endTime(v) {\n        endTime = v;\n      },\n      dataName,\n      dataInfo,\n      dataShow,\n      satisfactionSurveyShow,\n      guid,\n      fileIcon,\n      formatDuring,\n      handlePreview,\n      handleClick,\n      handleNewDialogue,\n      handleFileUpload,\n      handleFileCallback,\n      handleClose,\n      handleSelect,\n      handleChatMessage,\n      handleUpdate,\n      handleScroll,\n      scrollDown,\n      scrollElHeight,\n      handleGuideWord,\n      handleSendMessage,\n      handleTips,\n      handleToolClose,\n      handleLinkClick,\n      handleToolSendMessage,\n      stringToJson,\n      handleHttpStream,\n      handleDataList,\n      handleCopyMessage,\n      handleRetryMessage,\n      handleSatisfactionSurvey,\n      handleCloseMessage,\n      handleStopMessage,\n      aigptChatSceneDetail,\n      handleHistoryMessage,\n      isEmptyObject,\n      handleAiChatConfig,\n      handleAiChatEditor,\n      handleAiChatHistory,\n      handleAiChatSend,\n      handleAiChatToolSend,\n      globalReadConfig,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      onMounted,\n      onUnmounted,\n      nextTick,\n      watch,\n      defineAsyncComponent,\n      get useStore() {\n        return useStore;\n      },\n      get size2Str() {\n        return size2Str;\n      },\n      get http_stream() {\n        return http_stream;\n      },\n      get AiChatClass() {\n        return AiChatClass;\n      },\n      get globalFileLocation() {\n        return globalFileLocation;\n      },\n      get config() {\n        return config;\n      },\n      get user() {\n        return user;\n      },\n      get IntelligentAssistant() {\n        return IntelligentAssistant;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "computed", "onMounted", "onUnmounted", "nextTick", "watch", "defineAsyncComponent", "useStore", "size2Str", "http_stream", "AiChatClass", "globalFileLocation", "config", "user", "IntelligentAssistant", "ElMessage", "__default__", "GlobalMarkdown", "GlobalAiChatFile", "GlobalAiChatEditor", "GlobalAiChatHistory", "GlobalAiChatData", "CustomSatisfactionModal", "GlobalAiChart", "store", "props", "__props", "emit", "__emit", "newIcon", "tipsIcon", "loadingIcon", "toolIcon", "ponderIcon", "elShow", "get", "modelValue", "set", "elRefs", "getElRef", "el", "index", "elRef", "elPonderRefs", "getElPonderRef", "elPonderRef", "AiChatPrompt", "AiChatId", "AiChatCode", "AiChatModule", "AiChatModuleId", "elIndex", "AiChatParams", "AiChatContent", "aiTools", "aiWords", "chatId", "chatObj", "chatContent", "chatMessage", "chatMessageTotal", "chatMessageUpdate", "wrapScrollHeight", "chatHistoryRef", "scrollRef", "editor<PERSON><PERSON>", "fileList", "fileData", "send<PERSON><PERSON><PERSON>", "isScroll", "toolId", "toolName", "toolIsParam", "toolRequired", "currentRequest", "loading", "disabled", "isStreaming", "startTime", "endTime", "dataName", "dataInfo", "dataShow", "satisfactionSurveyShow", "guid", "replace", "Math", "random", "toString", "fileIcon", "fileType", "IconClass", "docx", "doc", "wps", "xlsx", "xls", "pdf", "pptx", "ppt", "txt", "jpg", "png", "gif", "avi", "mp4", "zip", "rar", "formatDuring", "mss", "days", "parseInt", "hours", "minutes", "seconds", "time", "handlePreview", "row", "process", "env", "VUE_APP_NAME", "fileId", "id", "extName", "fileName", "originalFileName", "fileSize", "handleClick", "handleNewDialogue", "handleToolClose", "handleStopMessage", "handleFileUpload", "data", "handleFileCallback", "handleClose", "item", "_editorRef$value", "handleSetFile", "filter", "handleSelect", "userQuestion", "handleChatMessage", "businessId", "_ref2", "_callee", "_yield$api$aigptChatL", "total", "updateVal", "_index", "_item", "_callee$", "_context", "aigptChatLogsList", "pageNo", "pageSize", "isAsc", "query", "reasoning", "answer", "unshift", "ponderShow", "isControls", "content", "contentOld", "ponder<PERSON><PERSON>nt", "ponderContentOld", "dataList", "chartData", "guideWord", "attachments", "scrollElHeight", "scrollDown", "_x", "handleUpdate", "handleScroll", "_ref3", "scrollTop", "wrapRef", "scrollHeight", "_scrollRef$value$wrap", "clientHeight", "handleGuideWord", "console", "log", "message", "handleCloseMessage", "question", "_editorRef$value2", "handleHttpStream", "handleSendMessage", "map", "join", "defaultParams", "tool", "attachmentIds", "params", "_objectSpread", "param", "pageContent", "_editorRef$value3", "handleTips", "text", "parts", "split", "result", "part", "startsWith", "endsWith", "trim", "undefined", "_editorRef$value4", "_editorRef$value5", "handleSetContent", "handleLinkClick", "_ref4", "href", "event", "token", "sessionStorage", "getItem", "window", "open", "mainP<PERSON>", "handleToolSendMessage", "chatToolName", "isParam", "userPromptTip", "needTool", "_editorRef$value6", "handleInsertPlaceholder", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_editorRef$value7", "_editorRef$value8", "string<PERSON><PERSON><PERSON><PERSON>", "str", "obj", "JSON", "parse", "_ref5", "_callee2", "AiChatParam", "quoteList", "newChartData", "newGuideWord", "_item$quoteList", "_choice$", "_elRef$value", "choice", "details", "_elPonderRef$value", "executionTime", "_elRef$value2", "_chatHistoryRef$value", "_elRef$value3", "_args2", "_callee2$", "_context2", "Date", "body", "stringify", "chatBusinessScene", "onMessage", "Array", "isArray", "markdownContent", "q", "echartItem", "choices", "delta", "enqueueRender", "reasoning_content", "refresh", "onError", "err", "onClose", "promise", "t0", "error", "handleDataList", "sourceName", "handleCopyMessage", "_elRefs$value$index", "copyContent", "innerText", "textarea", "document", "createElement", "readOnly", "style", "position", "left", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "handleRetryMessage", "_callee3", "_callee3$", "_context3", "retry", "handleSatisfactionSurvey", "_chatMessage$value$qu", "_elRefs$value$answerI", "questionIndex", "answerIndex", "_chatHistoryRef$value2", "abort", "aigptChatSceneDetail", "_ref7", "_callee4", "_yield$api$aigptChatS", "_callee4$", "_context4", "key", "chatSceneCode", "tools", "promptWords", "handleHistoryMessage", "_ref8", "_callee5", "code", "_chatHistoryRef$value3", "_callee5$", "_context5", "handleCurrentChat", "_x2", "_x3", "isEmptyObject", "state", "val", "immediate", "AiChatConfig", "_editorRef$value9", "_editorRef$value10", "_editorRef$value11", "AiChatWindow", "AiChatFile", "AiChatSetContent", "handleAddContent", "AiChatAddContent", "AiChatSendMessage", "AiChatToolSendMessage", "_editorRef$value12", "_editorRef$value13", "_editorRef$value14", "handleAiChatConfig", "_data$AiChatId", "newAiChatId", "_data$AiChatCode", "newAiChatCode", "_data$AiChatModule", "newAiChatModule", "_data$AiChatModuleId", "newAiChatModuleId", "_data$AiChatWindow", "newAiChatWindow", "_data$AiChatFile", "newAiChatFile", "_data$AiChatClearFile", "AiChatClearFile", "newAiChatClearFile", "_data$AiChatParams", "newAiChatParams", "_data$AiChatContent", "newAiChatContent", "_editorRef$value15", "handleAiChatEditor", "_editorRef$value16", "_editorRef$value17", "newType", "handleAiChatHistory", "handleAiChatSend", "handleAiChatToolSend", "AiChatEditor", "AiChatHistory", "AiChatSend", "AiChatToolSend", "globalReadConfig", "_ref9", "_callee6", "_yield$api$globalRead", "_callee6$", "_context6", "codes"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalAiChat/GlobalAiChat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChat\" data-no-text-select>\r\n    <div class=\"GlobalAiChatClose\" @click=\"handleClick\">\r\n      <el-icon>\r\n        <Close />\r\n      </el-icon>\r\n    </div>\r\n    <div class=\"GlobalAiChatLogo\">\r\n      <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" />\r\n    </div>\r\n    <div class=\"GlobalAiChatHead forbidSelect\">\r\n      <div class=\"GlobalAiChatHeadName\">您好 {{ user.userName }}!</div>\r\n      <div class=\"GlobalAiChatHeadText\">我是西安政协深度融合DeepSeek的综合助手，内容由 AI 生成，请仔细甄别。</div>\r\n    </div>\r\n    <div class=\"GlobalAiChatDialogueBody\">\r\n      <GlobalAiChatHistory ref=\"chatHistoryRef\" v-model=\"chatId\" @select=\"handleSelect\"></GlobalAiChatHistory>\r\n      <div class=\"GlobalAiChatDialogue ellipsis\">{{ chatContent || '新对话' }}</div>\r\n      <div class=\"GlobalAiChatDialogueNew\" @click=\"handleNewDialogue\"><span v-html=\"newIcon\"></span></div>\r\n    </div>\r\n    <el-scrollbar always ref=\"scrollRef\" class=\"GlobalAiChatScroll\" @scroll=\"handleScroll\">\r\n      <div class=\"GlobalAiChatBody\">\r\n        <div class=\"GlobalAiChatBodyTipsBody\" v-if=\"aiWords.length\">\r\n          <div class=\"GlobalAiChatBodyTipsVice\">您可以试着问我：</div>\r\n          <div class=\"GlobalAiChatBodyTipsItem\" v-for=\"item in aiWords\" :key=\"item.id\">\r\n            <div class=\"GlobalAiChatBodyTips\" @click=\"handleSendMessage(item.promptWord)\">\r\n              <span v-html=\"tipsIcon\"></span>\r\n              {{ item.promptWord }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div :class=\"[item.type ? 'GlobalAiChatSelfMessage' : 'GlobalAiChatMessage']\"\r\n          v-for=\"(item, index) in chatMessage\" :key=\"item.id\">\r\n          <template v-if=\"item.type\">\r\n            <div class=\"GlobalAiChatSelfMessageFile\" v-for=\"item in item.fileData\" :key=\"item.id\"\r\n              @click=\"handlePreview(item)\">\r\n              <div class=\"globalFileIcon\" :class=\"fileIcon(item?.extName)\"></div>\r\n              <div class=\"GlobalChatMessagesFileName ellipsis\">{{ item?.originalFileName || '未知文件' }}</div>\r\n              <div class=\"GlobalChatMessagesFileSize\">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>\r\n            </div>\r\n            <div class=\"GlobalAiChatSelfMessageInfo\">{{ item.content }}</div>\r\n          </template>\r\n          <template v-if=\"!item.type\">\r\n            <!-- <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" /> -->\r\n            <div class=\"GlobalAiChatMessageInfo\">\r\n              <div class=\"GlobalAiChatMessagePonder forbidSelect\" @click=\"item.ponderShow = !item.ponderShow\"\r\n                v-if=\"item.time\">\r\n                <div v-html=\"ponderIcon\"></div>\r\n                已深度思考\r\n                <span v-if=\"item.time !== '1'\">（用时 {{ item.time }}）</span>\r\n                <el-icon>\r\n                  <ArrowUpBold v-if=\"item.ponderShow\" />\r\n                  <ArrowDownBold v-if=\"!item.ponderShow\" />\r\n                </el-icon>\r\n              </div>\r\n              <div class=\"GlobalAiChatMessagePonderContent\" v-show=\"item.ponderShow\">\r\n                <GlobalMarkdown :ref=\"(el) => getElPonderRef(el, index)\" v-model=\"item.ponderContent\"\r\n                  :content=\"item.ponderContentOld\" @update=\"handleUpdate\" />\r\n              </div>\r\n              <div class=\"GlobalAiChatMessageContent\">\r\n                <GlobalMarkdown :ref=\"(el) => getElRef(el, index)\" v-model=\"item.content\" :content=\"item.contentOld\"\r\n                  :on-link-click=\"handleLinkClick\" @update=\"handleUpdate\" />\r\n                <div class=\"GlobalAiChatMessageDataList\">\r\n                  <div class=\"GlobalAiChatMessageDataItem\" v-for=\"row in item.dataList\" @click=\"handleDataList(row)\">\r\n                    {{ row.sourceName }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"GlobalAiChatMessageChart\">\r\n                  <GlobalAiChart v-for=\"(row, i) in item.chartData\" :key=\"i + 'chartData'\" :option=\"row\" />\r\n                </div>\r\n                <div class=\"GlobalAiChatMessageLoading\" v-if=\"index === chatMessage.length - 1 && loading\">\r\n                  <div class=\"answerLoading\" v-html=\"loadingIcon\"></div>\r\n                </div>\r\n                <div class=\"GlobalAiChatBodyTipsVice\" v-if=\"item.guideWord.length\">您是不是想问：</div>\r\n                <div class=\"GlobalAiChatBodyTipsItem\" v-for=\"(row, i) in item.guideWord\" :key=\"i + 'guideWord'\">\r\n                  <div class=\"GlobalAiChatBodyTips\" @click=\"handleGuideWord(row)\">\r\n                    <span v-html=\"tipsIcon\"></span>\r\n                    {{ row.question }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"GlobalAiChatMessageControls\" v-if=\"item.isControls\">\r\n                <el-tooltip content=\"复制\" placement=\"top\">\r\n                  <div class=\"GlobalAiChatMessageControlsItem\" @click=\"handleCopyMessage(item.content, index)\">\r\n                    <el-icon>\r\n                      <CopyDocument />\r\n                    </el-icon>\r\n                  </div>\r\n                </el-tooltip>\r\n                <el-tooltip content=\"重新生成\" placement=\"top\">\r\n                  <div class=\"GlobalAiChatMessageControlsItem\" @click=\"handleRetryMessage(item, index)\">\r\n                    <el-icon>\r\n                      <Refresh />\r\n                    </el-icon>\r\n                  </div>\r\n                </el-tooltip>\r\n                <el-tooltip content=\"满意度调查\" placement=\"top\">\r\n                  <div class=\"GlobalAiChatMessageControlsItem\" @click=\"handleSatisfactionSurvey(item, index)\">\r\n                    <el-icon>\r\n                      <Star />\r\n                    </el-icon>\r\n                  </div>\r\n                </el-tooltip>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"GlobalAiChatDialogueEditor\">\r\n      <div class=\"GlobalAiChatDialogueTools\" v-show=\"!toolId\">\r\n        <div class=\"GlobalAiChatDialogueTool\" v-for=\"item in aiTools\" :key=\"item.id\"\r\n          @click=\"handleToolSendMessage(item)\">\r\n          <span v-html=\"toolIcon\"></span>\r\n          {{ item.chatToolName }}\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalAiChatDialogueEditorBody\">\r\n        <div class=\"GlobalAiChatToolsActive\" v-show=\"toolId\">\r\n          <div class=\"GlobalAiChatToolsActiveName\">{{ toolName }}</div>\r\n          <div class=\"GlobalAiChatToolsActiveIcon\" @click=\"handleToolClose\">\r\n            <el-icon>\r\n              <Close />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <GlobalAiChatFile :fileList=\"fileList\" :fileData=\"fileData\" @close=\"handleClose\"\r\n          v-show=\"fileList.length || fileData.length\" />\r\n        <GlobalAiChatEditor ref=\"editorRef\" v-model=\"sendContent\" :disabled=\"disabled\" @send=\"handleSendMessage\"\r\n          @stop=\"handleStopMessage\" @uploadCallback=\"handleFileUpload\" @fileCallback=\"handleFileCallback\" />\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"dataShow\" :name=\"dataName\">\r\n      <GlobalAiChatData :data=\"dataInfo\"></GlobalAiChatData>\r\n    </xyl-popup-window>\r\n    <CustomSatisfactionModal v-model=\"satisfactionSurveyShow\" :data=\"dataInfo\"></CustomSatisfactionModal>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChat' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, onUnmounted, nextTick, watch, defineAsyncComponent } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { size2Str } from 'common/js/utils.js'\r\nimport http_stream from 'common/http/stream.js'\r\nimport { AiChatClass } from 'common/js/GlobalClass.js'\r\nimport { globalFileLocation } from 'common/config/location'\r\nimport config from 'common/config/index'\r\nimport { user, IntelligentAssistant } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst GlobalMarkdown = defineAsyncComponent(() => import('common/components/global-markdown/global-markdown.vue'))\r\nconst GlobalAiChatFile = defineAsyncComponent(() => import('./GlobalAiChatFile'))\r\nconst GlobalAiChatEditor = defineAsyncComponent(() => import('./GlobalAiChatEditor'))\r\nconst GlobalAiChatHistory = defineAsyncComponent(() => import('./GlobalAiChatHistory'))\r\nconst GlobalAiChatData = defineAsyncComponent(() => import('./GlobalAiChatData'))\r\nconst CustomSatisfactionModal = defineAsyncComponent(() => import('./CustomSatisfactionModal'))\r\nconst GlobalAiChart = defineAsyncComponent(() => import('./GlobalAiChart'))\r\nconst store = useStore()\r\nconst props = defineProps({ modelValue: { type: Boolean, default: false } })\r\nconst emit = defineEmits(['update:modelValue'])\r\nconst newIcon =\r\n  '<svg t=\"1741161744028\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"3254\" width=\"26\" height=\"26\"><path d=\"M566.464 150.336c22.144 0 39.744 17.216 40.576 39.296a40.448 40.448 0 0 1-38.08 41.792H183.488v432.128h171.2c21.312 0 38.912 16.384 40.576 37.696v52.416l66.368-76.16a39.872 39.872 0 0 1 27.456-13.952h319.04v-190.08c0-21.248 16.384-38.912 37.76-40.512h2.816c21.312 0 38.912 16.384 40.512 37.696v198.208c0 40.576-31.936 73.728-72.064 75.776H510.784l-125.76 143.808a40.832 40.832 0 0 1-71.296-23.808v-119.552H178.176c-40.576 0-73.728-32-75.776-72.128V226.112c0-40.576 32-73.728 72.064-75.776h392z m35.648 340.8c18.816 0 34.816 14.72 35.648 33.92a35.264 35.264 0 0 1-32.768 36.48h-285.44a35.328 35.328 0 0 1-2.944-70.4h285.504zM443.2 349.824c19.2 0 34.816 15.104 35.648 33.92a35.264 35.264 0 0 1-32.768 36.48H319.488a35.328 35.328 0 0 1-2.88-70.4h126.592z m335.424-229.376c18.432 0 34.048 14.336 35.2 32.768v72.896h70.528a35.264 35.264 0 0 1 2.88 70.4h-72.96v70.464c0 18.88-14.72 34.816-33.92 35.648a35.264 35.264 0 0 1-36.48-32.768V296.96h-70.464a35.264 35.264 0 0 1-2.88-70.4h72.96V155.968a34.816 34.816 0 0 1 35.2-35.584z\" p-id=\"3255\"></path></svg>'\r\nconst tipsIcon =\r\n  '<svg t=\"1741241762761\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"7848\" width=\"14\" height=\"14\"><path d=\"M115.152 356.453c-1.492-9.942-2.486-18.724-3.314-25.849-0.829-7.125-0.995-14.251-0.995-21.541 0-22.867 3.314-38.112 9.611-45.237 6.463-7.125 15.41-10.77 27.01-10.77h198.343L410.596 5.001c40.266 2.818 67.109 8.285 80.863 16.239 13.753 7.954 20.546 17.564 20.546 28.998v15.079L460.141 252.89h239.438L766.522 4.836c40.266 2.817 67.108 8.285 80.862 16.238 13.753 7.954 20.547 17.565 20.547 28.998 0 5.8-0.829 10.771-2.154 15.079l-49.71 187.739h170.34c2.817 8.617 4.309 16.902 4.309 24.855v22.701c0 21.541-3.314 36.289-9.776 44.242-6.463 7.954-15.41 11.765-27.01 11.765H788.063L710.35 643.281h200.498c1.326 10.108 2.485 19.056 3.314 27.01a217.169 217.169 0 0 1 1.159 22.701c0 37.448-12.262 56.007-36.619 56.007H682.181l-73.24 269.595c-40.265-2.816-66.942-8.285-79.867-16.072-12.925-7.954-19.387-17.564-19.387-29.164 0-5.634 0.662-10.107 2.154-12.925l56.006-211.269H326.421l-71.086 269.597c-40.265-2.817-67.606-8.286-82.022-16.074-14.416-7.953-21.541-17.564-21.541-29.163 0-2.816 0.331-4.971 0.994-6.462 0.663-1.326 0.994-3.646 0.994-6.463l58.327-211.269H39.592c-2.817-10.107-4.308-19.056-4.308-27.009V699.62c0-21.541 3.314-36.289 9.776-44.242 6.463-7.954 15.41-11.765 27.01-11.765h168.186l75.394-286.829H115.152v-0.331z m239.272 286.828H595.85l77.714-286.828H432.138l-77.714 286.828z\" p-id=\"7849\"></path></svg>'\r\nconst loadingIcon =\r\n  '<svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\"><path d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\" fill=\"#2c2c2c\" p-id=\"2363\"></path><path d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\" fill=\"#2c2c2c\" p-id=\"2364\"></path><path d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\" fill=\"#2c2c2c\" p-id=\"2365\"></path><path d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\" fill=\"#2c2c2c\" p-id=\"2366\"></path><path d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\" fill=\"#2c2c2c\" p-id=\"2367\"></path><path d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2368\"></path><path d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2369\"></path><path d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\" fill=\"#2c2c2c\" p-id=\"2370\"></path></svg>'\r\nconst toolIcon =\r\n  '<svg t=\"1741338779911\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"9993\" width=\"16\" height=\"16\"><path d=\"M707.2 350.976l-198.496 115.264-195.072-111.712c-37.088-21.12-68.736 34.528-31.648 55.616l198.72 113.792v256.736a32 32 0 0 0 64 0v-258.016c0-1.056-0.512-1.952-0.608-3.008l194.752-113.088c37.088-21.056 5.44-76.704-31.648-55.584z\" p-id=\"9994\"></path><path d=\"M880.288 232.48L560.192 45.12a95.648 95.648 0 0 0-96.64 0L143.68 232.48A96.64 96.64 0 0 0 96 315.904v397.664c0 34.784 18.624 66.88 48.736 84l320 181.92a95.52 95.52 0 0 0 94.496 0l320-181.92A96.576 96.576 0 0 0 928 713.568V315.904a96.64 96.64 0 0 0-47.712-83.424zM864 713.568c0 11.584-6.208 22.304-16.256 28l-320 181.92a31.776 31.776 0 0 1-31.488 0l-320-181.92A32.192 32.192 0 0 1 160 713.568V315.904c0-11.456 6.048-22.016 15.904-27.808l319.872-187.36a31.84 31.84 0 0 1 32.192 0l320.128 187.392c9.856 5.728 15.904 16.32 15.904 27.776v397.664z\" p-id=\"9995\"></path></svg>'\r\nconst ponderIcon =\r\n  '<svg t=\"1741658991857\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"11203\" width=\"16\" height=\"16\"><path d=\"M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z\" p-id=\"11204\"></path><path d=\"M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z\" p-id=\"11205\"></path><path d=\"M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z\" p-id=\"11206\"></path></svg>'\r\nconst elShow = computed({\r\n  get () {\r\n    return props.modelValue\r\n  },\r\n  set (value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst elRefs = ref([])\r\nconst getElRef = (el, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  if (el) elRefs.value[index] = el\r\n}\r\nconst elRef = computed(() => elRefs.value[elRefs.value.length - 1])\r\nconst elPonderRefs = ref([])\r\nconst getElPonderRef = (el, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  if (el) elPonderRefs.value[index] = el\r\n}\r\nconst elPonderRef = computed(() => elPonderRefs.value[elPonderRefs.value.length - 1])\r\nconst AiChatPrompt = ref('')\r\nconst AiChatId = ref('')\r\nconst AiChatCode = ref('test_chat')\r\nconst AiChatModule = ref('')\r\nconst AiChatModuleId = ref('')\r\nconst elIndex = ref(0)\r\nconst AiChatParams = ref({})\r\nconst AiChatContent = ref('')\r\nconst aiTools = ref([])\r\nconst aiWords = ref([])\r\nconst chatId = ref('')\r\nconst chatObj = ref({})\r\nconst chatContent = ref('')\r\nconst chatMessage = ref([])\r\nconst chatMessageTotal = ref(0)\r\nconst chatMessageUpdate = ref(0)\r\nconst wrapScrollHeight = ref(0)\r\nconst chatHistoryRef = ref()\r\nconst scrollRef = ref()\r\nconst editorRef = ref()\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst sendContent = ref('')\r\nconst isScroll = ref(false)\r\nconst toolId = ref('')\r\nconst toolName = ref('')\r\nconst toolIsParam = ref(0)\r\nconst toolRequired = ref(0)\r\n\r\nlet currentRequest = null\r\nconst loading = ref(false)\r\nconst disabled = ref(false)\r\nconst isStreaming = ref(false)\r\nlet startTime = null\r\nlet endTime = null\r\n\r\nconst dataName = ref('')\r\nconst dataInfo = ref({})\r\nconst dataShow = ref(false)\r\nconst satisfactionSurveyShow = ref(false)\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nconst formatDuring = (mss) => {\r\n  const days = parseInt(mss / (1000 * 60 * 60 * 24))\r\n  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))\r\n  const seconds = (mss % (1000 * 60)) / 1000\r\n  var time = ''\r\n  if (days > 0) time += `${days} 天 `\r\n  if (hours > 0) time += `${hours} 小时 `\r\n  if (minutes > 0) time += `${minutes} 分钟 `\r\n  if (seconds > 0) time += `${seconds} 秒 `\r\n  return time\r\n}\r\nconst handlePreview = (row) => {\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nconst handleClick = () => {\r\n  elShow.value = !elShow.value\r\n}\r\nconst handleNewDialogue = () => {\r\n  elRefs.value = []\r\n  elPonderRefs.value = []\r\n  chatMessageTotal.value = 0\r\n  handleToolClose()\r\n  handleStopMessage()\r\n  chatId.value = guid()\r\n  chatContent.value = ''\r\n  chatMessage.value = []\r\n}\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleSelect = (data, type) => {\r\n  chatMessageTotal.value = 0\r\n  chatContent.value = data.userQuestion\r\n  if (!type) {\r\n    handleStopMessage()\r\n    chatMessage.value = []\r\n    handleChatMessage()\r\n  } else {\r\n    chatObj.value[AiChatCode.value] = { chatId: data.id, businessId: data.businessId || '' }\r\n  }\r\n}\r\nconst handleChatMessage = async (value) => {\r\n  const { data, total } = await api.aigptChatLogsList({\r\n    pageNo: value || 1,\r\n    pageSize: value ? 1 : 10,\r\n    isAsc: 1,\r\n    query: { chatId: chatId.value }\r\n  })\r\n  chatMessageTotal.value = total\r\n  if (value) {\r\n    let updateVal = 0\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      if (item.reasoning) updateVal += 1\r\n      if (item.answer) updateVal += 1\r\n      chatMessage.value.unshift({\r\n        id: guid(),\r\n        type: false,\r\n        ponderShow: true,\r\n        isControls: true,\r\n        content: '',\r\n        contentOld: item.answer,\r\n        ponderContent: '',\r\n        ponderContentOld: item.reasoning,\r\n        time: item.reasoning ? '1' : '',\r\n        dataList: [],\r\n        fileData: [],\r\n        chartData: [],\r\n        guideWord: []\r\n      })\r\n      chatMessage.value.unshift({\r\n        id: guid(),\r\n        type: true,\r\n        ponderShow: true,\r\n        isControls: true,\r\n        content: item.userQuestion,\r\n        contentOld: '',\r\n        ponderContent: '',\r\n        ponderContentOld: '',\r\n        time: '',\r\n        dataList: [],\r\n        fileData: item.attachments,\r\n        chartData: [],\r\n        guideWord: []\r\n      })\r\n    }\r\n    chatMessageUpdate.value = updateVal\r\n    nextTick(() => {\r\n      scrollElHeight()\r\n    })\r\n  } else {\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      chatMessage.value.push({\r\n        id: guid(),\r\n        type: true,\r\n        ponderShow: true,\r\n        isControls: true,\r\n        content: item.userQuestion,\r\n        contentOld: '',\r\n        ponderContent: '',\r\n        ponderContentOld: '',\r\n        time: '',\r\n        dataList: [],\r\n        fileData: item.attachments,\r\n        chartData: [],\r\n        guideWord: []\r\n      })\r\n      chatMessage.value.push({\r\n        id: guid(),\r\n        type: false,\r\n        ponderShow: true,\r\n        isControls: true,\r\n        content: '',\r\n        contentOld: item.answer,\r\n        ponderContent: '',\r\n        ponderContentOld: item.reasoning,\r\n        time: item.reasoning ? '1' : '',\r\n        dataList: [],\r\n        fileData: [],\r\n        chartData: [],\r\n        guideWord: []\r\n      })\r\n    }\r\n    isScroll.value = false\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n  }\r\n}\r\nconst handleUpdate = () => {\r\n  if (!isScroll.value && wrapScrollHeight.value) {\r\n    nextTick(() => {\r\n      scrollElHeight()\r\n    })\r\n    chatMessageUpdate.value = chatMessageUpdate.value - 1\r\n  } else {\r\n    chatMessageUpdate.value = 0\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n  }\r\n}\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (scrollTop === 0) {\r\n    wrapScrollHeight.value = scrollRef.value.wrapRef.scrollHeight\r\n    if (chatMessageTotal.value && chatMessageTotal.value > chatMessage.value.length / 2) {\r\n      handleChatMessage(chatMessage.value.length / 2 + 1)\r\n    }\r\n  }\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 52) {\r\n    isScroll.value = false\r\n  } else {\r\n    isScroll.value = true\r\n  }\r\n}\r\nconst scrollDown = () => {\r\n  if (isScroll.value) return\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight\r\n}\r\nconst scrollElHeight = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - wrapScrollHeight.value\r\n}\r\nconst handleGuideWord = (data) => {\r\n  console.log('[]', data)\r\n  if (isStreaming.value) return ElMessage({ type: 'warning', message: '请先完成上一次对话再进行新对话！' })\r\n  handleCloseMessage()\r\n  loading.value = true\r\n  disabled.value = true\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: true,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: data.question,\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: fileData.value,\r\n    chartData: []\r\n  })\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: false,\r\n    ponderShow: true,\r\n    isControls: false,\r\n    content: '',\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: [],\r\n    chartData: [],\r\n    guideWord: []\r\n  })\r\n  nextTick(() => {\r\n    scrollDown()\r\n    handleHttpStream(data)\r\n    editorRef.value?.handleSetFile([])\r\n    handleToolClose()\r\n  })\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (isStreaming.value) return ElMessage({ type: 'warning', message: '请先完成上一次对话再进行新对话！' })\r\n  if (toolRequired.value && !fileData.value.length)\r\n    return ElMessage({ type: 'warning', message: `请先上传相关资料在进行${toolName.value}!` })\r\n  handleCloseMessage()\r\n  loading.value = true\r\n  disabled.value = true\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: true,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: value,\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: fileData.value,\r\n    chartData: []\r\n  })\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: false,\r\n    ponderShow: true,\r\n    isControls: false,\r\n    content: '',\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: [],\r\n    chartData: [],\r\n    guideWord: []\r\n  })\r\n  const fileId = fileData.value.map((v) => v.id).join(',')\r\n  const defaultParams = toolId.value\r\n    ? { question: value, tool: toolId.value, attachmentIds: fileId }\r\n    : { question: value, attachmentIds: fileId }\r\n  const params = toolIsParam.value ? { ...defaultParams, param: { pageContent: AiChatContent.value } } : defaultParams\r\n  nextTick(() => {\r\n    scrollDown()\r\n    handleHttpStream(params)\r\n    editorRef.value?.handleSetFile([])\r\n    handleToolClose()\r\n  })\r\n}\r\nconst handleTips = (text) => {\r\n  const parts = text.split(/(\\{[^}]+\\})/)\r\n  const result = parts\r\n    .map((part) => {\r\n      if (part.startsWith('{') && part.endsWith('}')) {\r\n        return { value: part.slice(1, -1), type: true }\r\n      } else if (part.trim() !== '') {\r\n        return { value: part, type: false }\r\n      }\r\n    })\r\n    .filter((item) => item !== undefined)\r\n  return result\r\n}\r\nconst handleToolClose = () => {\r\n  toolId.value = ''\r\n  toolName.value = ''\r\n  toolIsParam.value = 0\r\n  toolRequired.value = 0\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n}\r\nconst handleLinkClick = ({ href, text, event }) => {\r\n  console.log('链接被点击:', href, text)\r\n  if (text === '查看原文比对') {\r\n    // store.commit('setOpenRoute', { name: '查看原文比对', path: '/VersionComparisonAi', query: { chatId: chatId.value } })\r\n    const token = sessionStorage.getItem('token') || ''\r\n    window.open(`${config.mainPath}VersionComparisonAi?chatId=${chatId.value}&token=${token}`, '_blank')\r\n  } else {\r\n    window.open(href, '_blank')\r\n  }\r\n}\r\nconst handleToolSendMessage = (value) => {\r\n  const { id, chatToolName, isParam, userPromptTip, needTool } = value\r\n  if (userPromptTip) {\r\n    toolId.value = id\r\n    toolName.value = chatToolName\r\n    toolIsParam.value = isParam\r\n    toolRequired.value = needTool\r\n    editorRef.value?.handleInsertPlaceholder(handleTips(userPromptTip))\r\n    return\r\n  }\r\n  if (needTool && !fileData.value.length)\r\n    return ElMessage({ type: 'warning', message: `请先上传相关资料再进行${chatToolName}！` })\r\n  const finallySendContent = isParam ? chatToolName : AiChatContent.value || sendContent.value\r\n  if (!finallySendContent) return ElMessage({ type: 'warning', message: `请先输入内容再进行${chatToolName}！` })\r\n  handleStopMessage()\r\n  loading.value = true\r\n  disabled.value = true\r\n  // chatId.value = guid()\r\n  // chatContent.value = ''\r\n  // chatMessage.value = []\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: true,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: chatToolName,\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: fileData.value,\r\n    chartData: [],\r\n    guideWord: []\r\n  })\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: false,\r\n    ponderShow: true,\r\n    isControls: false,\r\n    content: '',\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: [],\r\n    chartData: [],\r\n    guideWord: []\r\n  })\r\n  const fileId = fileData.value.map((v) => v.id).join(',')\r\n  const defaultParams = { question: finallySendContent, tool: id, attachmentIds: fileId }\r\n  const params = isParam ? { ...defaultParams, param: { pageContent: AiChatContent.value } } : defaultParams\r\n  nextTick(() => {\r\n    scrollDown()\r\n    handleHttpStream(params, fileData.value)\r\n    editorRef.value?.handleSetFile([])\r\n    editorRef.value?.handleSetContent('')\r\n  })\r\n}\r\nconst stringToJson = (str) => {\r\n  // 替换属性名\r\n  str = str.replace(/(\\w+):/g, '\"$1\":')\r\n  // 替换单引号为双引号\r\n  str = str.replace(/'/g, '\"')\r\n  const obj = JSON.parse(str)\r\n  return obj\r\n}\r\nconst handleHttpStream = async (params = {}) => {\r\n  isStreaming.value = true\r\n  try {\r\n    let AiChatParam = {}\r\n    if (params.param && AiChatParams.value.param) {\r\n      AiChatParam = { param: { ...params.param, ...AiChatParams.value.param } }\r\n    }\r\n    AiChatParam = { ...params, ...AiChatParams.value, ...AiChatParam }\r\n    startTime = new Date()\r\n    currentRequest = http_stream('/aigpt/chatStream', {\r\n      body: JSON.stringify({\r\n        chatBusinessScene: AiChatCode.value,\r\n        chatId: chatId.value,\r\n        ...AiChatParam\r\n      }),\r\n      onMessage (event) {\r\n        // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\r\n        loading.value = false\r\n        if (event.data !== '[DONE]') {\r\n          const data = JSON.parse(event.data)\r\n          console.log('[]', data)\r\n          if (Array.isArray(data)) {\r\n            // console.log('[]', data)\r\n            let quoteList = []\r\n            let newChartData = []\r\n            let newGuideWord = []\r\n            for (let index = 0; index < data.length; index++) {\r\n              const item = data[index]\r\n              if (typeof item === 'string') {\r\n                newGuideWord.push({ ...AiChatParam, question: item })\r\n              } else {\r\n                if (item?.quoteList?.length) {\r\n                  for (let i = 0; i < item?.quoteList.length; i++) {\r\n                    const row = item?.quoteList[i]\r\n                    quoteList.push({ ...row, markdownContent: row.q })\r\n                  }\r\n                }\r\n                if (item?.echartItem) {\r\n                  newChartData.push(stringToJson(item?.echartItem))\r\n                }\r\n              }\r\n            }\r\n            chatMessage.value[chatMessage.value.length - 1].dataList = quoteList\r\n            chatMessage.value[chatMessage.value.length - 1].chartData = newChartData\r\n            chatMessage.value[chatMessage.value.length - 1].guideWord = newGuideWord\r\n          } else {\r\n            // console.log('{}', data)\r\n            const choice = data?.choices || [{}]\r\n            const details = choice[0]?.delta || {}\r\n            if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\r\n              elPonderRef.value?.enqueueRender(details.reasoning_content || '')\r\n              if (chatMessage.value[chatMessage.value.length - 1].time) {\r\n                startTime = null\r\n                endTime = null\r\n              } else {\r\n                endTime = new Date()\r\n                const executionTime = endTime - startTime\r\n                chatMessage.value[chatMessage.value.length - 1].time = formatDuring(executionTime)\r\n              }\r\n            }\r\n            if (Object.prototype.hasOwnProperty.call(details, 'content'))\r\n              elRef.value?.enqueueRender(details.content || '')\r\n            nextTick(() => {\r\n              scrollDown()\r\n            })\r\n          }\r\n        } else {\r\n          // console.log(event.data)\r\n          elRef.value?.enqueueRender('')\r\n          nextTick(() => {\r\n            scrollDown()\r\n          })\r\n          disabled.value = false\r\n          isStreaming.value = false\r\n          chatMessage.value[chatMessage.value.length - 1].isControls = true\r\n          if (!chatContent.value) chatHistoryRef.value?.refresh()\r\n        }\r\n      },\r\n      onError (err) {\r\n        console.log('流式接口错误:', err)\r\n      },\r\n      onClose () {\r\n        loading.value = false\r\n        disabled.value = false\r\n        isStreaming.value = false\r\n        console.log('流式接口关闭')\r\n      }\r\n    })\r\n    await currentRequest.promise\r\n  } catch (error) {\r\n    loading.value = false\r\n    disabled.value = false\r\n    isStreaming.value = false\r\n    elRef.value?.enqueueRender('服务器繁忙，请稍后再试。')\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n    console.error('启动流式接口失败:', error)\r\n  } finally {\r\n    // currentRequest = null\r\n  }\r\n}\r\nconst handleDataList = (row) => {\r\n  dataName.value = row.sourceName\r\n  dataInfo.value = row\r\n  dataShow.value = true\r\n}\r\nconst handleCopyMessage = (content, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  const copyContent = elRefs.value[index]?.elRef?.innerText || content\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = copyContent\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) {\r\n    ElMessage({ message: '复制成功', type: 'success' })\r\n  }\r\n  document.body.removeChild(textarea)\r\n}\r\nconst handleRetryMessage = (item, index) => {\r\n  const length = chatMessage.value.length - 1\r\n  if (index === length && currentRequest) {\r\n    loading.value = true\r\n    disabled.value = true\r\n    isStreaming.value = true\r\n    chatMessage.value[length] = {\r\n      id: guid(),\r\n      type: false,\r\n      ponderShow: true,\r\n      isControls: false,\r\n      content: '',\r\n      contentOld: '',\r\n      ponderContent: '',\r\n      ponderContentOld: '',\r\n      time: '',\r\n      dataList: [],\r\n      fileData: [],\r\n      chartData: [],\r\n      guideWord: []\r\n    }\r\n    nextTick(async () => {\r\n      await currentRequest.retry()\r\n    })\r\n  } else {\r\n    isScroll.value = false\r\n    fileData.value = chatMessage.value[index - 1].fileData\r\n    handleSendMessage(chatMessage.value[index - 1].content)\r\n  }\r\n}\r\nconst handleSatisfactionSurvey = (item, index) => {\r\n  // 获取当前回答对应的用户问题\r\n  const questionIndex = index - 1\r\n  const question = chatMessage.value[questionIndex]?.content || ''\r\n  const answerIndex = ((index + 1) / 2) - 1\r\n  const answer = elRefs.value[answerIndex]?.elRef?.innerText || item.content || item.contentOld || ''\r\n  // 构建传递给满意度调查的数据\r\n  dataInfo.value = {\r\n    ...item,\r\n    question: question,\r\n    answer: answer,\r\n    chatId: chatId.value,\r\n    chatContent: chatContent.value\r\n  }\r\n  console.log('dataInfo.value==>', dataInfo.value)\r\n  satisfactionSurveyShow.value = true\r\n}\r\nconst handleCloseMessage = () => {\r\n  currentRequest = null\r\n  loading.value = false\r\n  disabled.value = false\r\n  isStreaming.value = false\r\n}\r\nconst handleStopMessage = () => {\r\n  if (currentRequest) {\r\n    currentRequest.abort()\r\n    loading.value = false\r\n    disabled.value = false\r\n    isStreaming.value = false\r\n    console.log('启动流式接口停止')\r\n    if (!chatContent.value) chatHistoryRef.value?.refresh()\r\n  }\r\n  handleCloseMessage()\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const { data } = await api.aigptChatSceneDetail({ key: guid(), query: { chatSceneCode: AiChatCode.value } })\r\n  aiTools.value = data?.tools || []\r\n  aiWords.value = data?.promptWords || []\r\n  chatObj.value[AiChatCode.value] = { chatId: chatId.value, businessId: AiChatModuleId.value }\r\n}\r\nconst handleHistoryMessage = async (code, businessId) => {\r\n  elIndex.value = 1\r\n  chatHistoryRef.value?.handleCurrentChat(code, businessId)\r\n}\r\nconst isEmptyObject = (obj) => {\r\n  if (typeof obj !== 'object' || obj === null) return false\r\n  return Object.keys(obj).length !== 0\r\n}\r\n/**\r\n * AiChatCode 场景code String\r\n * --主工程调用 store.commit('setAiChatCode', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatCode: '' })\r\n *\r\n * AiChatConfig 批量设置参数 Object\r\n * AiChatConfig可以设置 AiChatWindow AiChatParams AiChatContent AiChatSetContent AiChatAddContent AiChatSendMessage AiChatToolSendMessage\r\n * --主工程调用 store.commit('setAiChatConfig', { AiChatWindow, AiChatParams, AiChatContent, AiChatSetContent, AiChatAddContent, AiChatSendMessage, AiChatToolSendMessage })\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatConfig: { AiChatWindow, AiChatParams, AiChatContent, AiChatSetContent, AiChatAddContent, AiChatSendMessage, AiChatToolSendMessage } })\r\n *\r\n * AiChatWindow 窗口是否显示 Boolean\r\n * --主工程调用 store.commit('setAiChatWindow', true / false)\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatWindow: true / false })\r\n *\r\n * AiChatParams 其他传参 Object\r\n * --主工程调用 store.commit('setAiChatParams', {})\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatParams: {} })\r\n *\r\n * AiChatContent 内容参数 String\r\n * --主工程调用 store.commit('setAiChatContent', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n *\r\n * AiChatSetContent 替换内容参数 String\r\n * --主工程调用 store.commit('setAiChatSetContent', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatSetContent: '' })\r\n *\r\n * AiChatAddContent 追加内容参数 String\r\n * --主工程调用 store.commit('setAiChatAddContent', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatAddContent: '' })\r\n *\r\n * AiChatSendMessage 发送消息 String\r\n * --主工程调用 store.commit('setAiChatSendMessage', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatSendMessage: '' })\r\n *\r\n * AiChatToolSendMessage 点击工具 Object\r\n * --主工程调用 store.commit('setAiChatToolSendMessage', {})\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatToolSendMessage: {} })\r\n */\r\nwatch(\r\n  () => store.state.AiChatCode,\r\n  (val) => {\r\n    if (val) {\r\n      AiChatCode.value = val\r\n      handleToolClose()\r\n      handleNewDialogue()\r\n      aigptChatSceneDetail()\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatModuleId,\r\n  (val) => {\r\n    AiChatModuleId.value = val\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatConfig,\r\n  (val) => {\r\n    if (isEmptyObject(val)) {\r\n      if (val.hasOwnProperty('AiChatWindow')) elShow.value = !!val.AiChatWindow\r\n      if (val.hasOwnProperty('AiChatFile')) editorRef.value?.handleSetFile(val.AiChatFile)\r\n      if (val.hasOwnProperty('AiChatParams')) AiChatParams.value = val.AiChatParams\r\n      if (val.hasOwnProperty('AiChatContent')) AiChatContent.value = val.AiChatContent\r\n      if (val.hasOwnProperty('AiChatSetContent')) editorRef.value?.handleSetContent(val.AiChatSetContent)\r\n      if (val.hasOwnProperty('AiChatAddContent')) editorRef.value?.handleAddContent(val.AiChatAddContent)\r\n      if (val.hasOwnProperty('AiChatSendMessage')) if (val.AiChatSendMessage) handleSendMessage(val.AiChatSendMessage)\r\n      if (val.hasOwnProperty('AiChatToolSendMessage'))\r\n        if (isEmptyObject(val.AiChatToolSendMessage)) handleToolSendMessage(val.AiChatToolSendMessage)\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatWindow,\r\n  (val) => {\r\n    elShow.value = !!val\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatFile,\r\n  (val) => {\r\n    editorRef.value?.handleSetFile(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatParams,\r\n  (val) => {\r\n    AiChatParams.value = val\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatContent,\r\n  (val) => {\r\n    AiChatContent.value = val\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatSetContent,\r\n  (val) => {\r\n    editorRef.value?.handleSetContent(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatAddContent,\r\n  (val) => {\r\n    editorRef.value?.handleAddContent(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatSendMessage,\r\n  (val) => {\r\n    if (val) handleSendMessage(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatToolSendMessage,\r\n  (val) => {\r\n    if (isEmptyObject(val)) handleToolSendMessage(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => elShow.value,\r\n  (val) => {\r\n    if (val && elIndex.value) {\r\n      elIndex.value = 0\r\n      nextTick(() => {\r\n        scrollDown()\r\n      })\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nconst handleAiChatConfig = (data) => {\r\n  const {\r\n    AiChatId: newAiChatId = '',\r\n    AiChatCode: newAiChatCode = '',\r\n    AiChatModule: newAiChatModule = '',\r\n    AiChatModuleId: newAiChatModuleId = '',\r\n    AiChatWindow: newAiChatWindow = false,\r\n    AiChatFile: newAiChatFile = [],\r\n    AiChatClearFile: newAiChatClearFile = false,\r\n    AiChatParams: newAiChatParams = {},\r\n    AiChatContent: newAiChatContent = ''\r\n  } = data\r\n  AiChatId.value = newAiChatId\r\n  AiChatCode.value = newAiChatCode\r\n  AiChatModule.value = newAiChatModule\r\n  AiChatModuleId.value = newAiChatModuleId\r\n  elShow.value = typeof newAiChatWindow === 'boolean' ? newAiChatWindow : false\r\n  if ((typeof newAiChatClearFile === 'boolean' ? newAiChatClearFile : false) || newAiChatFile.length) {\r\n    editorRef.value?.handleSetFile(newAiChatFile)\r\n  }\r\n  AiChatParams.value = newAiChatParams\r\n  AiChatContent.value = newAiChatContent\r\n}\r\nconst handleAiChatEditor = (type = true, content = '') => {\r\n  const newType = typeof type === 'boolean' ? type : true\r\n  if (newType) editorRef.value?.handleSetContent(content)\r\n  if (!newType) editorRef.value?.handleAddContent(content)\r\n}\r\nconst handleAiChatHistory = (newAiChatCode = '', newAiChatModuleId = '') => {\r\n  handleToolClose()\r\n  handleNewDialogue()\r\n  aigptChatSceneDetail()\r\n  if (newAiChatCode) handleHistoryMessage(newAiChatCode, newAiChatModuleId)\r\n}\r\nconst handleAiChatSend = (content) => {\r\n  if (content) {\r\n    elShow.value = true\r\n    handleSendMessage(content)\r\n  }\r\n}\r\nconst handleAiChatToolSend = (data) => {\r\n  if (isEmptyObject(data)) {\r\n    elShow.value = true\r\n    handleToolSendMessage(data)\r\n  }\r\n}\r\nAiChatClass.prototype.AiChatConfig = handleAiChatConfig\r\nAiChatClass.prototype.AiChatEditor = handleAiChatEditor\r\nAiChatClass.prototype.AiChatHistory = handleAiChatHistory\r\nAiChatClass.prototype.AiChatSend = handleAiChatSend\r\nAiChatClass.prototype.AiChatToolSend = handleAiChatToolSend\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: ['AiChatPrompt'] })\r\n  AiChatPrompt.value = data.AiChatPrompt\r\n}\r\nonMounted(() => {\r\n  globalReadConfig()\r\n  handleNewDialogue()\r\n  aigptChatSceneDetail()\r\n})\r\nonUnmounted(() => { })\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalAiChat {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 12px 0;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .GlobalAiChatClose {\r\n    width: 38px;\r\n    height: 32px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 20px;\r\n    border-radius: 2px;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: #fff;\r\n      background-color: rgba($color: red, $alpha: 0.6);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatLogo {\r\n    width: 62px;\r\n    height: 62px;\r\n    position: absolute;\r\n    top: 16px;\r\n    left: 12px;\r\n\r\n    .zy-el-image {\r\n      width: 62px;\r\n      height: 62px;\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatHead {\r\n    width: 100%;\r\n    height: 68px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-end;\r\n    padding: 0 12px 0 82px;\r\n\r\n    .GlobalAiChatHeadName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n\r\n    .GlobalAiChatHeadText {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      min-height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 8px);\r\n      max-height: calc((var(--zy-text-font-size) * var(--zy-line-height)) * 2);\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-text-color-secondary);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatDialogueBody {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    padding: 0 6px;\r\n\r\n    .GlobalAiChatDialogue {\r\n      padding: 0 6px;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n\r\n    .GlobalAiChatDialogueNew,\r\n    .GlobalAiChatDialogueHistory {\r\n      width: 38px;\r\n      height: 38px;\r\n      min-width: 38px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border-radius: 2px;\r\n      cursor: pointer;\r\n\r\n      span {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        path {\r\n          fill: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatDialogueNew {\r\n      &:hover {\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        span {\r\n          path {\r\n            fill: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatDialogueHistory {\r\n      &:hover {\r\n        background: var(--zy-el-color-info-light-9);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatScroll {\r\n    width: 100%;\r\n    flex: 1;\r\n\r\n    .GlobalAiChatBody {\r\n      width: 100%;\r\n      padding: 0 12px;\r\n\r\n      .GlobalAiChatBodyTipsBody {\r\n        width: 100%;\r\n        padding-top: 12px;\r\n      }\r\n\r\n      .GlobalAiChatBodyTipsVice {\r\n        width: 100%;\r\n        padding: 6px 0;\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-text-font-size);\r\n        color: var(--zy-el-text-color-secondary);\r\n      }\r\n\r\n      .GlobalAiChatBodyTipsItem {\r\n        width: 100%;\r\n        padding: 6px 0;\r\n\r\n        .GlobalAiChatBodyTips {\r\n          display: inline-block;\r\n          padding: 6px 12px;\r\n          border-radius: 6px;\r\n          line-height: var(--zy-line-height);\r\n          color: var(--zy-el-text-color-regular);\r\n          font-size: var(--zy-text-font-size);\r\n          background: var(--zy-el-color-info-light-9);\r\n          cursor: pointer;\r\n\r\n          span {\r\n            width: 14px;\r\n            display: inline-block;\r\n            line-height: 1.2;\r\n            margin-right: 6px;\r\n            vertical-align: middle;\r\n\r\n            svg {\r\n              vertical-align: top;\r\n\r\n              path {\r\n                fill: var(--zy-el-color-primary);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .GlobalAiChatMessage,\r\n      .GlobalAiChatSelfMessage {\r\n        padding: 12px 0;\r\n      }\r\n\r\n      .GlobalAiChatMessage {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding-right: 12px;\r\n\r\n        .zy-el-image {\r\n          width: 32px;\r\n          height: 32px;\r\n        }\r\n\r\n        .GlobalAiChatMessageInfo {\r\n          width: 100%;\r\n          // width: calc(100% - 46px);\r\n          display: inline-block;\r\n          background: #fff;\r\n          position: relative;\r\n\r\n          .GlobalAiChatMessageLoading {\r\n            width: 100%;\r\n            height: 32px;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n\r\n            @keyframes circleRoate {\r\n              from {\r\n                transform: translateY(-50%) rotate(0deg);\r\n              }\r\n\r\n              to {\r\n                transform: translateY(-50%) rotate(360deg);\r\n              }\r\n            }\r\n\r\n            .answerLoading {\r\n              width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n              height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n              position: absolute;\r\n              top: 50%;\r\n              left: 0;\r\n              z-index: 3;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              animation: circleRoate 1s infinite linear;\r\n\r\n              path {\r\n                fill: var(--zy-el-color-primary);\r\n              }\r\n            }\r\n\r\n            .answerLoading+.QuestionsAndAnswersChatText {\r\n              color: var(--zy-el-color-primary);\r\n              padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            }\r\n          }\r\n\r\n          .GlobalAiChatMessagePonder {\r\n            height: 32px;\r\n            display: inline-flex;\r\n            align-items: center;\r\n            padding: 0 12px;\r\n            border-radius: 6px;\r\n            line-height: var(--zy-line-height);\r\n            font-size: var(--zy-text-font-size);\r\n            color: var(--zy-el-text-color-primary);\r\n            background: var(--zy-el-color-info-light-9);\r\n            margin-bottom: 12px;\r\n            cursor: pointer;\r\n\r\n            div {\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              margin-right: 6px;\r\n            }\r\n\r\n            span {\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              color: var(--zy-el-text-color-primary);\r\n            }\r\n          }\r\n\r\n          .GlobalAiChatMessagePonderContent {\r\n            width: 100%;\r\n            padding-left: 12px;\r\n            border-left: 2px solid var(--zy-el-border-color);\r\n\r\n            * {\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n          }\r\n\r\n          .GlobalAiChatMessageContent {\r\n            width: 100%;\r\n            padding-top: calc((32px - (var(--zy-line-height) * var(--zy-text-font-size))) / 2);\r\n\r\n            .GlobalAiChatMessageDataList {\r\n              padding-top: 12px;\r\n\r\n              .GlobalAiChatMessageDataItem {\r\n                color: var(--zy-el-color-primary);\r\n                line-height: var(--zy-line-height);\r\n                font-size: var(--zy-text-font-size);\r\n                cursor: pointer;\r\n              }\r\n            }\r\n          }\r\n\r\n          .GlobalAiChatMessageControls {\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-top: 12px;\r\n\r\n            .GlobalAiChatMessageControlsItem {\r\n              width: 32px;\r\n              height: 32px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              border-radius: 6px;\r\n              cursor: pointer;\r\n              margin-right: 2px;\r\n\r\n              &:hover {\r\n                background: var(--zy-el-color-info-light-8);\r\n              }\r\n\r\n              .zy-el-icon {\r\n                font-size: 20px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .GlobalAiChatSelfMessage {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        align-items: flex-end;\r\n        flex-direction: column;\r\n        padding-left: 46px;\r\n        padding-right: 12px;\r\n\r\n        .GlobalAiChatSelfMessageFile {\r\n          width: 280px;\r\n          height: 52px;\r\n          display: inline-flex;\r\n          flex-direction: column;\r\n          justify-content: center;\r\n          background: #fff;\r\n          position: relative;\r\n          padding: 0 40px 0 12px;\r\n          border-radius: var(--el-border-radius-base);\r\n          border: 1px solid var(--zy-el-border-color-light);\r\n          background: var(--zy-el-color-info-light-9);\r\n          word-wrap: break-word;\r\n          white-space: pre-wrap;\r\n          cursor: pointer;\r\n          margin-bottom: 12px;\r\n\r\n          .GlobalChatMessagesFileName {\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n            padding-bottom: 2px;\r\n          }\r\n\r\n          .GlobalChatMessagesFileSize {\r\n            color: var(--zy-el-text-color-secondary);\r\n            font-size: calc(var(--zy-text-font-size) - 2px);\r\n          }\r\n\r\n          .globalFileIcon {\r\n            width: 28px;\r\n            height: 28px;\r\n            vertical-align: middle;\r\n            position: absolute;\r\n            top: 50%;\r\n            right: 6px;\r\n            transform: translateY(-50%);\r\n          }\r\n\r\n          .globalFileUnknown {\r\n            background: url('./img/unknown.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFilePDF {\r\n            background: url('./img/PDF.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileWord {\r\n            background: url('./img/Word.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileExcel {\r\n            background: url('./img/Excel.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFilePicture {\r\n            background: url('./img/picture.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileVideo {\r\n            background: url('./img/video.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileTXT {\r\n            background: url('./img/TXT.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileCompress {\r\n            background: url('./img/compress.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileWPS {\r\n            background: url('./img/WPS.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFilePPT {\r\n            background: url('./img/PPT.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n        }\r\n\r\n        .GlobalAiChatSelfMessageInfo {\r\n          display: inline-block;\r\n          padding: 12px;\r\n          border-radius: 6px;\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          background: var(--zy-el-bg-color-page);\r\n          white-space: pre-wrap;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatDialogueEditor {\r\n    width: 100%;\r\n    padding: 0 12px;\r\n\r\n    .GlobalAiChatDialogueTools {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      align-items: center;\r\n      padding: 6px;\r\n\r\n      .GlobalAiChatDialogueTool {\r\n        height: 28px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 14px;\r\n        padding: 0 16px;\r\n        font-size: var(--zy-text-font-size);\r\n        border: 1px solid var(--zy-el-border-color);\r\n        margin: 3px 6px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          color: var(--zy-el-color-primary);\r\n          border: 1px solid var(--zy-el-color-primary);\r\n          background: var(--zy-el-color-primary-light-9);\r\n\r\n          span {\r\n            path {\r\n              fill: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n\r\n        span {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatDialogueEditorBody {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 8px;\r\n      box-shadow: var(--zy-el-box-shadow);\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n      .GlobalAiChatToolsActive {\r\n        width: 100%;\r\n        height: var(--zy-height);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 0 12px;\r\n        background: var(--zy-el-color-info-light-9);\r\n\r\n        div {\r\n          font-weight: bold;\r\n        }\r\n\r\n        .GlobalAiChatToolsActiveIcon {\r\n          width: 26px;\r\n          height: 26px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 2px;\r\n          cursor: pointer;\r\n\r\n          &:hover {\r\n            background: var(--zy-el-color-info-light-8);\r\n          }\r\n\r\n          .zy-el-icon {\r\n            font-size: 18px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;+CA8IA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,oBAAoB,QAAQ,KAAK;AAClG,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,IAAI,EAAEC,oBAAoB,QAAQ,yBAAyB;AACpE,SAASC,SAAS,QAAQ,cAAc;AAZxC,IAAAC,WAAA,GAAe;EAAE5C,IAAI,EAAE;AAAe,CAAC;;;;;;;;;;;;;IAavC,IAAM6C,cAAc,GAAGX,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,uDAAuD,CAAC;IAAA,EAAC;IAClH,IAAMY,gBAAgB,GAAGZ,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oBAAoB,CAAC;IAAA,EAAC;IACjF,IAAMa,kBAAkB,GAAGb,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,sBAAsB,CAAC;IAAA,EAAC;IACrF,IAAMc,mBAAmB,GAAGd,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,uBAAuB,CAAC;IAAA,EAAC;IACvF,IAAMe,gBAAgB,GAAGf,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oBAAoB,CAAC;IAAA,EAAC;IACjF,IAAMgB,uBAAuB,GAAGhB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,2BAA2B,CAAC;IAAA,EAAC;IAC/F,IAAMiB,aAAa,GAAGjB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,iBAAiB,CAAC;IAAA,EAAC;IAC3E,IAAMkB,KAAK,GAAGjB,QAAQ,CAAC,CAAC;IACxB,IAAMkB,KAAK,GAAGC,OAA8D;IAC5E,IAAMC,IAAI,GAAGC,MAAkC;IAC/C,IAAMC,OAAO,GACX,krCAAkrC;IACprC,IAAMC,QAAQ,GACZ,o7CAAo7C;IACt7C,IAAMC,WAAW,GACf,62CAA62C;IAC/2C,IAAMC,QAAQ,GACZ,o9BAAo9B;IACt9B,IAAMC,UAAU,GACd,qqDAAqqD;IACvqD,IAAMC,MAAM,GAAGjC,QAAQ,CAAC;MACtBkC,GAAGA,CAAA,EAAI;QACL,OAAOV,KAAK,CAACW,UAAU;MACzB,CAAC;MACDC,GAAGA,CAAE1I,KAAK,EAAE;QACVgI,IAAI,CAAC,mBAAmB,EAAEhI,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;IACF,IAAM2I,MAAM,GAAGtC,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMuC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,EAAE,EAAE5I,CAAC,EAAK;MAC1B,IAAM6I,KAAK,GAAG,CAAC7I,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;MAC7B,IAAI4I,EAAE,EAAEF,MAAM,CAAC3I,KAAK,CAAC8I,KAAK,CAAC,GAAGD,EAAE;IAClC,CAAC;IACD,IAAME,KAAK,GAAGzC,QAAQ,CAAC;MAAA,OAAMqC,MAAM,CAAC3I,KAAK,CAAC2I,MAAM,CAAC3I,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC;IAAA,EAAC;IACnE,IAAM2E,YAAY,GAAG3C,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAM4C,cAAc,GAAG,SAAjBA,cAAcA,CAAIJ,EAAE,EAAE5I,CAAC,EAAK;MAChC,IAAM6I,KAAK,GAAG,CAAC7I,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;MAC7B,IAAI4I,EAAE,EAAEG,YAAY,CAAChJ,KAAK,CAAC8I,KAAK,CAAC,GAAGD,EAAE;IACxC,CAAC;IACD,IAAMK,WAAW,GAAG5C,QAAQ,CAAC;MAAA,OAAM0C,YAAY,CAAChJ,KAAK,CAACgJ,YAAY,CAAChJ,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC;IAAA,EAAC;IACrF,IAAM8E,YAAY,GAAG9C,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAM+C,QAAQ,GAAG/C,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMgD,UAAU,GAAGhD,GAAG,CAAC,WAAW,CAAC;IACnC,IAAMiD,YAAY,GAAGjD,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMkD,cAAc,GAAGlD,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMmD,OAAO,GAAGnD,GAAG,CAAC,CAAC,CAAC;IACtB,IAAMoD,YAAY,GAAGpD,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAMqD,aAAa,GAAGrD,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMsD,OAAO,GAAGtD,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMuD,OAAO,GAAGvD,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMwD,MAAM,GAAGxD,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMyD,OAAO,GAAGzD,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAM0D,WAAW,GAAG1D,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAM2D,WAAW,GAAG3D,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAM4D,gBAAgB,GAAG5D,GAAG,CAAC,CAAC,CAAC;IAC/B,IAAM6D,iBAAiB,GAAG7D,GAAG,CAAC,CAAC,CAAC;IAChC,IAAM8D,gBAAgB,GAAG9D,GAAG,CAAC,CAAC,CAAC;IAC/B,IAAM+D,cAAc,GAAG/D,GAAG,CAAC,CAAC;IAC5B,IAAMgE,SAAS,GAAGhE,GAAG,CAAC,CAAC;IACvB,IAAMiE,SAAS,GAAGjE,GAAG,CAAC,CAAC;IACvB,IAAMkE,QAAQ,GAAGlE,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMmE,QAAQ,GAAGnE,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMoE,WAAW,GAAGpE,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMqE,QAAQ,GAAGrE,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMsE,MAAM,GAAGtE,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMuE,QAAQ,GAAGvE,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMwE,WAAW,GAAGxE,GAAG,CAAC,CAAC,CAAC;IAC1B,IAAMyE,YAAY,GAAGzE,GAAG,CAAC,CAAC,CAAC;IAE3B,IAAI0E,cAAc,GAAG,IAAI;IACzB,IAAMC,OAAO,GAAG3E,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAM4E,QAAQ,GAAG5E,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAM6E,WAAW,GAAG7E,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAI8E,SAAS,GAAG,IAAI;IACpB,IAAIC,OAAO,GAAG,IAAI;IAElB,IAAMC,QAAQ,GAAGhF,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMiF,QAAQ,GAAGjF,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAMkF,QAAQ,GAAGlF,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMmF,sBAAsB,GAAGnF,GAAG,CAAC,KAAK,CAAC;IAEzC,IAAMoF,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAACrL,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAIkM,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9B5J,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAAC6J,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,QAAQ,EAAK;MAC7B,IAAMC,SAAS,GAAG;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,GAAG,EAAE,gBAAgB;QACrBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,iBAAiB;QACvBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,eAAe;QACrBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,oBAAoB;QACzBC,GAAG,EAAE;MACP,CAAC;MACD,OAAOhB,SAAS,CAACD,QAAQ,CAAC,IAAI,mBAAmB;IACnD,CAAC;IACD,IAAMkB,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAK;MAC5B,IAAMC,IAAI,GAAGC,QAAQ,CAACF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAClD,IAAMG,KAAK,GAAGD,QAAQ,CAAEF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACxE,IAAMI,OAAO,GAAGF,QAAQ,CAAEF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;MAChE,IAAMK,OAAO,GAAIL,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI;MAC1C,IAAIM,IAAI,GAAG,EAAE;MACb,IAAIL,IAAI,GAAG,CAAC,EAAEK,IAAI,IAAI,GAAGL,IAAI,KAAK;MAClC,IAAIE,KAAK,GAAG,CAAC,EAAEG,IAAI,IAAI,GAAGH,KAAK,MAAM;MACrC,IAAIC,OAAO,GAAG,CAAC,EAAEE,IAAI,IAAI,GAAGF,OAAO,MAAM;MACzC,IAAIC,OAAO,GAAG,CAAC,EAAEC,IAAI,IAAI,GAAGD,OAAO,KAAK;MACxC,OAAOC,IAAI;IACb,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAK;MAC7B1G,kBAAkB,CAAC;QACjBvC,IAAI,EAAEkJ,OAAO,CAACC,GAAG,CAACC,YAAY;QAC9BC,MAAM,EAAEJ,GAAG,CAACK,EAAE;QACdhC,QAAQ,EAAE2B,GAAG,CAACM,OAAO;QACrBC,QAAQ,EAAEP,GAAG,CAACQ,gBAAgB;QAC9BC,QAAQ,EAAET,GAAG,CAACS;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB7F,MAAM,CAACvI,KAAK,GAAG,CAACuI,MAAM,CAACvI,KAAK;IAC9B,CAAC;IACD,IAAMqO,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B1F,MAAM,CAAC3I,KAAK,GAAG,EAAE;MACjBgJ,YAAY,CAAChJ,KAAK,GAAG,EAAE;MACvBiK,gBAAgB,CAACjK,KAAK,GAAG,CAAC;MAC1BsO,eAAe,CAAC,CAAC;MACjBC,iBAAiB,CAAC,CAAC;MACnB1E,MAAM,CAAC7J,KAAK,GAAGyL,IAAI,CAAC,CAAC;MACrB1B,WAAW,CAAC/J,KAAK,GAAG,EAAE;MACtBgK,WAAW,CAAChK,KAAK,GAAG,EAAE;IACxB,CAAC;IACD,IAAMwO,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;MACjClE,QAAQ,CAACvK,KAAK,GAAGyO,IAAI;IACvB,CAAC;IACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAID,IAAI,EAAK;MACnCjE,QAAQ,CAACxK,KAAK,GAAGyO,IAAI;IACvB,CAAC;IACD,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAAA,IAAAC,gBAAA;MAC5B,CAAAA,gBAAA,GAAAvE,SAAS,CAACtK,KAAK,cAAA6O,gBAAA,eAAfA,gBAAA,CAAiBC,aAAa,CAACtE,QAAQ,CAACxK,KAAK,CAAC+O,MAAM,CAAC,UAAC/M,CAAC;QAAA,OAAKA,CAAC,CAAC+L,EAAE,KAAKa,IAAI,CAACb,EAAE;MAAA,EAAC,CAAC;IAChF,CAAC;IACD,IAAMiB,YAAY,GAAG,SAAfA,YAAYA,CAAIP,IAAI,EAAEtN,IAAI,EAAK;MACnC8I,gBAAgB,CAACjK,KAAK,GAAG,CAAC;MAC1B+J,WAAW,CAAC/J,KAAK,GAAGyO,IAAI,CAACQ,YAAY;MACrC,IAAI,CAAC9N,IAAI,EAAE;QACToN,iBAAiB,CAAC,CAAC;QACnBvE,WAAW,CAAChK,KAAK,GAAG,EAAE;QACtBkP,iBAAiB,CAAC,CAAC;MACrB,CAAC,MAAM;QACLpF,OAAO,CAAC9J,KAAK,CAACqJ,UAAU,CAACrJ,KAAK,CAAC,GAAG;UAAE6J,MAAM,EAAE4E,IAAI,CAACV,EAAE;UAAEoB,UAAU,EAAEV,IAAI,CAACU,UAAU,IAAI;QAAG,CAAC;MAC1F;IACF,CAAC;IACD,IAAMD,iBAAiB;MAAA,IAAAE,KAAA,GAAArJ,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2K,QAAOrP,KAAK;QAAA,IAAAsP,qBAAA,EAAAb,IAAA,EAAAc,KAAA,EAAAC,SAAA,EAAA1G,KAAA,EAAA8F,IAAA,EAAAa,MAAA,EAAAC,KAAA;QAAA,OAAApQ,mBAAA,GAAAuB,IAAA,UAAA8O,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAzK,IAAA,GAAAyK,QAAA,CAAApM,IAAA;YAAA;cAAAoM,QAAA,CAAApM,IAAA;cAAA,OACN4C,GAAG,CAACyJ,iBAAiB,CAAC;gBAClDC,MAAM,EAAE9P,KAAK,IAAI,CAAC;gBAClB+P,QAAQ,EAAE/P,KAAK,GAAG,CAAC,GAAG,EAAE;gBACxBgQ,KAAK,EAAE,CAAC;gBACRC,KAAK,EAAE;kBAAEpG,MAAM,EAAEA,MAAM,CAAC7J;gBAAM;cAChC,CAAC,CAAC;YAAA;cAAAsP,qBAAA,GAAAM,QAAA,CAAA3M,IAAA;cALMwL,IAAI,GAAAa,qBAAA,CAAJb,IAAI;cAAEc,KAAK,GAAAD,qBAAA,CAALC,KAAK;cAMnBtF,gBAAgB,CAACjK,KAAK,GAAGuP,KAAK;cAC9B,IAAIvP,KAAK,EAAE;gBACLwP,SAAS,GAAG,CAAC;gBACjB,KAAS1G,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG2F,IAAI,CAACpK,MAAM,EAAEyE,KAAK,EAAE,EAAE;kBAC1C8F,IAAI,GAAGH,IAAI,CAAC3F,KAAK,CAAC;kBACxB,IAAI8F,IAAI,CAACsB,SAAS,EAAEV,SAAS,IAAI,CAAC;kBAClC,IAAIZ,IAAI,CAACuB,MAAM,EAAEX,SAAS,IAAI,CAAC;kBAC/BxF,WAAW,CAAChK,KAAK,CAACoQ,OAAO,CAAC;oBACxBrC,EAAE,EAAEtC,IAAI,CAAC,CAAC;oBACVtK,IAAI,EAAE,KAAK;oBACXkP,UAAU,EAAE,IAAI;oBAChBC,UAAU,EAAE,IAAI;oBAChBC,OAAO,EAAE,EAAE;oBACXC,UAAU,EAAE5B,IAAI,CAACuB,MAAM;oBACvBM,aAAa,EAAE,EAAE;oBACjBC,gBAAgB,EAAE9B,IAAI,CAACsB,SAAS;oBAChC1C,IAAI,EAAEoB,IAAI,CAACsB,SAAS,GAAG,GAAG,GAAG,EAAE;oBAC/BS,QAAQ,EAAE,EAAE;oBACZnG,QAAQ,EAAE,EAAE;oBACZoG,SAAS,EAAE,EAAE;oBACbC,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF7G,WAAW,CAAChK,KAAK,CAACoQ,OAAO,CAAC;oBACxBrC,EAAE,EAAEtC,IAAI,CAAC,CAAC;oBACVtK,IAAI,EAAE,IAAI;oBACVkP,UAAU,EAAE,IAAI;oBAChBC,UAAU,EAAE,IAAI;oBAChBC,OAAO,EAAE3B,IAAI,CAACK,YAAY;oBAC1BuB,UAAU,EAAE,EAAE;oBACdC,aAAa,EAAE,EAAE;oBACjBC,gBAAgB,EAAE,EAAE;oBACpBlD,IAAI,EAAE,EAAE;oBACRmD,QAAQ,EAAE,EAAE;oBACZnG,QAAQ,EAAEoE,IAAI,CAACkC,WAAW;oBAC1BF,SAAS,EAAE,EAAE;oBACbC,SAAS,EAAE;kBACb,CAAC,CAAC;gBACJ;gBACA3G,iBAAiB,CAAClK,KAAK,GAAGwP,SAAS;gBACnC/I,QAAQ,CAAC,YAAM;kBACbsK,cAAc,CAAC,CAAC;gBAClB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,KAASjI,MAAK,GAAG,CAAC,EAAEA,MAAK,GAAG2F,IAAI,CAACpK,MAAM,EAAEyE,MAAK,EAAE,EAAE;kBAC1C8F,KAAI,GAAGH,IAAI,CAAC3F,MAAK,CAAC;kBACxBkB,WAAW,CAAChK,KAAK,CAACgE,IAAI,CAAC;oBACrB+J,EAAE,EAAEtC,IAAI,CAAC,CAAC;oBACVtK,IAAI,EAAE,IAAI;oBACVkP,UAAU,EAAE,IAAI;oBAChBC,UAAU,EAAE,IAAI;oBAChBC,OAAO,EAAE3B,KAAI,CAACK,YAAY;oBAC1BuB,UAAU,EAAE,EAAE;oBACdC,aAAa,EAAE,EAAE;oBACjBC,gBAAgB,EAAE,EAAE;oBACpBlD,IAAI,EAAE,EAAE;oBACRmD,QAAQ,EAAE,EAAE;oBACZnG,QAAQ,EAAEoE,KAAI,CAACkC,WAAW;oBAC1BF,SAAS,EAAE,EAAE;oBACbC,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF7G,WAAW,CAAChK,KAAK,CAACgE,IAAI,CAAC;oBACrB+J,EAAE,EAAEtC,IAAI,CAAC,CAAC;oBACVtK,IAAI,EAAE,KAAK;oBACXkP,UAAU,EAAE,IAAI;oBAChBC,UAAU,EAAE,IAAI;oBAChBC,OAAO,EAAE,EAAE;oBACXC,UAAU,EAAE5B,KAAI,CAACuB,MAAM;oBACvBM,aAAa,EAAE,EAAE;oBACjBC,gBAAgB,EAAE9B,KAAI,CAACsB,SAAS;oBAChC1C,IAAI,EAAEoB,KAAI,CAACsB,SAAS,GAAG,GAAG,GAAG,EAAE;oBAC/BS,QAAQ,EAAE,EAAE;oBACZnG,QAAQ,EAAE,EAAE;oBACZoG,SAAS,EAAE,EAAE;oBACbC,SAAS,EAAE;kBACb,CAAC,CAAC;gBACJ;gBACAnG,QAAQ,CAAC1K,KAAK,GAAG,KAAK;gBACtByG,QAAQ,CAAC,YAAM;kBACbuK,UAAU,CAAC,CAAC;gBACd,CAAC,CAAC;cACJ;YAAC;YAAA;cAAA,OAAApB,QAAA,CAAAtK,IAAA;UAAA;QAAA,GAAA+J,OAAA;MAAA,CACF;MAAA,gBAxFKH,iBAAiBA,CAAA+B,EAAA;QAAA,OAAA7B,KAAA,CAAAnJ,KAAA,OAAAD,SAAA;MAAA;IAAA,GAwFtB;IACD,IAAMkL,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAI,CAACxG,QAAQ,CAAC1K,KAAK,IAAImK,gBAAgB,CAACnK,KAAK,EAAE;QAC7CyG,QAAQ,CAAC,YAAM;UACbsK,cAAc,CAAC,CAAC;QAClB,CAAC,CAAC;QACF7G,iBAAiB,CAAClK,KAAK,GAAGkK,iBAAiB,CAAClK,KAAK,GAAG,CAAC;MACvD,CAAC,MAAM;QACLkK,iBAAiB,CAAClK,KAAK,GAAG,CAAC;QAC3ByG,QAAQ,CAAC,YAAM;UACbuK,UAAU,CAAC,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAAC,KAAA,EAAsB;MAAA,IAAhBC,SAAS,GAAAD,KAAA,CAATC,SAAS;MAC/B,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnBlH,gBAAgB,CAACnK,KAAK,GAAGqK,SAAS,CAACrK,KAAK,CAACsR,OAAO,CAACC,YAAY;QAC7D,IAAItH,gBAAgB,CAACjK,KAAK,IAAIiK,gBAAgB,CAACjK,KAAK,GAAGgK,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC,EAAE;UACnF6K,iBAAiB,CAAClF,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QACrD;MACF;MACA,IAAAmN,qBAAA,GAAuCnH,SAAS,CAACrK,KAAK,CAACsR,OAAO;QAAtDC,YAAY,GAAAC,qBAAA,CAAZD,YAAY;QAAEE,YAAY,GAAAD,qBAAA,CAAZC,YAAY;MAClC,IAAIF,YAAY,GAAGF,SAAS,IAAII,YAAY,GAAG,EAAE,EAAE;QACjD/G,QAAQ,CAAC1K,KAAK,GAAG,KAAK;MACxB,CAAC,MAAM;QACL0K,QAAQ,CAAC1K,KAAK,GAAG,IAAI;MACvB;IACF,CAAC;IACD,IAAMgR,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAItG,QAAQ,CAAC1K,KAAK,EAAE;MACpBqK,SAAS,CAACrK,KAAK,CAACsR,OAAO,CAACD,SAAS,GAAGhH,SAAS,CAACrK,KAAK,CAACsR,OAAO,CAACC,YAAY;IAC1E,CAAC;IACD,IAAMR,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B1G,SAAS,CAACrK,KAAK,CAACsR,OAAO,CAACD,SAAS,GAAGhH,SAAS,CAACrK,KAAK,CAACsR,OAAO,CAACC,YAAY,GAAGpH,gBAAgB,CAACnK,KAAK;IACnG,CAAC;IACD,IAAM0R,eAAe,GAAG,SAAlBA,eAAeA,CAAIjD,IAAI,EAAK;MAChCkD,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEnD,IAAI,CAAC;MACvB,IAAIvD,WAAW,CAAClL,KAAK,EAAE,OAAOoH,SAAS,CAAC;QAAEjG,IAAI,EAAE,SAAS;QAAE0Q,OAAO,EAAE;MAAmB,CAAC,CAAC;MACzFC,kBAAkB,CAAC,CAAC;MACpB9G,OAAO,CAAChL,KAAK,GAAG,IAAI;MACpBiL,QAAQ,CAACjL,KAAK,GAAG,IAAI;MACrBgK,WAAW,CAAChK,KAAK,CAACgE,IAAI,CAAC;QACrB+J,EAAE,EAAEtC,IAAI,CAAC,CAAC;QACVtK,IAAI,EAAE,IAAI;QACVkP,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE9B,IAAI,CAACsD,QAAQ;QACtBvB,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBlD,IAAI,EAAE,EAAE;QACRmD,QAAQ,EAAE,EAAE;QACZnG,QAAQ,EAAEA,QAAQ,CAACxK,KAAK;QACxB4Q,SAAS,EAAE;MACb,CAAC,CAAC;MACF5G,WAAW,CAAChK,KAAK,CAACgE,IAAI,CAAC;QACrB+J,EAAE,EAAEtC,IAAI,CAAC,CAAC;QACVtK,IAAI,EAAE,KAAK;QACXkP,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,EAAE;QACXC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBlD,IAAI,EAAE,EAAE;QACRmD,QAAQ,EAAE,EAAE;QACZnG,QAAQ,EAAE,EAAE;QACZoG,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;MACb,CAAC,CAAC;MACFpK,QAAQ,CAAC,YAAM;QAAA,IAAAuL,iBAAA;QACbhB,UAAU,CAAC,CAAC;QACZiB,gBAAgB,CAACxD,IAAI,CAAC;QACtB,CAAAuD,iBAAA,GAAA1H,SAAS,CAACtK,KAAK,cAAAgS,iBAAA,eAAfA,iBAAA,CAAiBlD,aAAa,CAAC,EAAE,CAAC;QAClCR,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC;IACD,IAAM4D,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIlS,KAAK,EAAK;MACnC,IAAIkL,WAAW,CAAClL,KAAK,EAAE,OAAOoH,SAAS,CAAC;QAAEjG,IAAI,EAAE,SAAS;QAAE0Q,OAAO,EAAE;MAAmB,CAAC,CAAC;MACzF,IAAI/G,YAAY,CAAC9K,KAAK,IAAI,CAACwK,QAAQ,CAACxK,KAAK,CAACqE,MAAM,EAC9C,OAAO+C,SAAS,CAAC;QAAEjG,IAAI,EAAE,SAAS;QAAE0Q,OAAO,EAAE,cAAcjH,QAAQ,CAAC5K,KAAK;MAAI,CAAC,CAAC;MACjF8R,kBAAkB,CAAC,CAAC;MACpB9G,OAAO,CAAChL,KAAK,GAAG,IAAI;MACpBiL,QAAQ,CAACjL,KAAK,GAAG,IAAI;MACrBgK,WAAW,CAAChK,KAAK,CAACgE,IAAI,CAAC;QACrB+J,EAAE,EAAEtC,IAAI,CAAC,CAAC;QACVtK,IAAI,EAAE,IAAI;QACVkP,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAEvQ,KAAK;QACdwQ,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBlD,IAAI,EAAE,EAAE;QACRmD,QAAQ,EAAE,EAAE;QACZnG,QAAQ,EAAEA,QAAQ,CAACxK,KAAK;QACxB4Q,SAAS,EAAE;MACb,CAAC,CAAC;MACF5G,WAAW,CAAChK,KAAK,CAACgE,IAAI,CAAC;QACrB+J,EAAE,EAAEtC,IAAI,CAAC,CAAC;QACVtK,IAAI,EAAE,KAAK;QACXkP,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,EAAE;QACXC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBlD,IAAI,EAAE,EAAE;QACRmD,QAAQ,EAAE,EAAE;QACZnG,QAAQ,EAAE,EAAE;QACZoG,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;MACb,CAAC,CAAC;MACF,IAAM/C,MAAM,GAAGtD,QAAQ,CAACxK,KAAK,CAACmS,GAAG,CAAC,UAACnQ,CAAC;QAAA,OAAKA,CAAC,CAAC+L,EAAE;MAAA,EAAC,CAACqE,IAAI,CAAC,GAAG,CAAC;MACxD,IAAMC,aAAa,GAAG1H,MAAM,CAAC3K,KAAK,GAC9B;QAAE+R,QAAQ,EAAE/R,KAAK;QAAEsS,IAAI,EAAE3H,MAAM,CAAC3K,KAAK;QAAEuS,aAAa,EAAEzE;MAAO,CAAC,GAC9D;QAAEiE,QAAQ,EAAE/R,KAAK;QAAEuS,aAAa,EAAEzE;MAAO,CAAC;MAC9C,IAAM0E,MAAM,GAAG3H,WAAW,CAAC7K,KAAK,GAAAyS,aAAA,CAAAA,aAAA,KAAQJ,aAAa;QAAEK,KAAK,EAAE;UAAEC,WAAW,EAAEjJ,aAAa,CAAC1J;QAAM;MAAC,KAAKqS,aAAa;MACpH5L,QAAQ,CAAC,YAAM;QAAA,IAAAmM,iBAAA;QACb5B,UAAU,CAAC,CAAC;QACZiB,gBAAgB,CAACO,MAAM,CAAC;QACxB,CAAAI,iBAAA,GAAAtI,SAAS,CAACtK,KAAK,cAAA4S,iBAAA,eAAfA,iBAAA,CAAiB9D,aAAa,CAAC,EAAE,CAAC;QAClCR,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMuE,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B,IAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,aAAa,CAAC;MACvC,IAAMC,MAAM,GAAGF,KAAK,CACjBZ,GAAG,CAAC,UAACe,IAAI,EAAK;QACb,IAAIA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,IAAID,IAAI,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC9C,OAAO;YAAEpT,KAAK,EAAEkT,IAAI,CAAC7N,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAAElE,IAAI,EAAE;UAAK,CAAC;QACjD,CAAC,MAAM,IAAI+R,IAAI,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAC7B,OAAO;YAAErT,KAAK,EAAEkT,IAAI;YAAE/R,IAAI,EAAE;UAAM,CAAC;QACrC;MACF,CAAC,CAAC,CACD4N,MAAM,CAAC,UAACH,IAAI;QAAA,OAAKA,IAAI,KAAK0E,SAAS;MAAA,EAAC;MACvC,OAAOL,MAAM;IACf,CAAC;IACD,IAAM3E,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAAA,IAAAiF,iBAAA,EAAAC,iBAAA;MAC5B7I,MAAM,CAAC3K,KAAK,GAAG,EAAE;MACjB4K,QAAQ,CAAC5K,KAAK,GAAG,EAAE;MACnB6K,WAAW,CAAC7K,KAAK,GAAG,CAAC;MACrB8K,YAAY,CAAC9K,KAAK,GAAG,CAAC;MACtB,CAAAuT,iBAAA,GAAAjJ,SAAS,CAACtK,KAAK,cAAAuT,iBAAA,eAAfA,iBAAA,CAAiBzE,aAAa,CAAC,EAAE,CAAC;MAClC,CAAA0E,iBAAA,GAAAlJ,SAAS,CAACtK,KAAK,cAAAwT,iBAAA,eAAfA,iBAAA,CAAiBC,gBAAgB,CAAC,EAAE,CAAC;IACvC,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,KAAA,EAA8B;MAAA,IAAxBC,IAAI,GAAAD,KAAA,CAAJC,IAAI;QAAEd,IAAI,GAAAa,KAAA,CAAJb,IAAI;QAAEe,KAAK,GAAAF,KAAA,CAALE,KAAK;MAC1ClC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEgC,IAAI,EAAEd,IAAI,CAAC;MACjC,IAAIA,IAAI,KAAK,QAAQ,EAAE;QACrB;QACA,IAAMgB,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;QACnDC,MAAM,CAACC,IAAI,CAAC,GAAGjN,MAAM,CAACkN,QAAQ,8BAA8BtK,MAAM,CAAC7J,KAAK,UAAU8T,KAAK,EAAE,EAAE,QAAQ,CAAC;MACtG,CAAC,MAAM;QACLG,MAAM,CAACC,IAAI,CAACN,IAAI,EAAE,QAAQ,CAAC;MAC7B;IACF,CAAC;IACD,IAAMQ,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIpU,KAAK,EAAK;MACvC,IAAQ+N,EAAE,GAAqD/N,KAAK,CAA5D+N,EAAE;QAAEsG,YAAY,GAAuCrU,KAAK,CAAxDqU,YAAY;QAAEC,OAAO,GAA8BtU,KAAK,CAA1CsU,OAAO;QAAEC,aAAa,GAAevU,KAAK,CAAjCuU,aAAa;QAAEC,QAAQ,GAAKxU,KAAK,CAAlBwU,QAAQ;MAC1D,IAAID,aAAa,EAAE;QAAA,IAAAE,iBAAA;QACjB9J,MAAM,CAAC3K,KAAK,GAAG+N,EAAE;QACjBnD,QAAQ,CAAC5K,KAAK,GAAGqU,YAAY;QAC7BxJ,WAAW,CAAC7K,KAAK,GAAGsU,OAAO;QAC3BxJ,YAAY,CAAC9K,KAAK,GAAGwU,QAAQ;QAC7B,CAAAC,iBAAA,GAAAnK,SAAS,CAACtK,KAAK,cAAAyU,iBAAA,eAAfA,iBAAA,CAAiBC,uBAAuB,CAAC7B,UAAU,CAAC0B,aAAa,CAAC,CAAC;QACnE;MACF;MACA,IAAIC,QAAQ,IAAI,CAAChK,QAAQ,CAACxK,KAAK,CAACqE,MAAM,EACpC,OAAO+C,SAAS,CAAC;QAAEjG,IAAI,EAAE,SAAS;QAAE0Q,OAAO,EAAE,cAAcwC,YAAY;MAAI,CAAC,CAAC;MAC/E,IAAMM,kBAAkB,GAAGL,OAAO,GAAGD,YAAY,GAAG3K,aAAa,CAAC1J,KAAK,IAAIyK,WAAW,CAACzK,KAAK;MAC5F,IAAI,CAAC2U,kBAAkB,EAAE,OAAOvN,SAAS,CAAC;QAAEjG,IAAI,EAAE,SAAS;QAAE0Q,OAAO,EAAE,YAAYwC,YAAY;MAAI,CAAC,CAAC;MACpG9F,iBAAiB,CAAC,CAAC;MACnBvD,OAAO,CAAChL,KAAK,GAAG,IAAI;MACpBiL,QAAQ,CAACjL,KAAK,GAAG,IAAI;MACrB;MACA;MACA;MACAgK,WAAW,CAAChK,KAAK,CAACgE,IAAI,CAAC;QACrB+J,EAAE,EAAEtC,IAAI,CAAC,CAAC;QACVtK,IAAI,EAAE,IAAI;QACVkP,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE8D,YAAY;QACrB7D,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBlD,IAAI,EAAE,EAAE;QACRmD,QAAQ,EAAE,EAAE;QACZnG,QAAQ,EAAEA,QAAQ,CAACxK,KAAK;QACxB4Q,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;MACb,CAAC,CAAC;MACF7G,WAAW,CAAChK,KAAK,CAACgE,IAAI,CAAC;QACrB+J,EAAE,EAAEtC,IAAI,CAAC,CAAC;QACVtK,IAAI,EAAE,KAAK;QACXkP,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,EAAE;QACXC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBlD,IAAI,EAAE,EAAE;QACRmD,QAAQ,EAAE,EAAE;QACZnG,QAAQ,EAAE,EAAE;QACZoG,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;MACb,CAAC,CAAC;MACF,IAAM/C,MAAM,GAAGtD,QAAQ,CAACxK,KAAK,CAACmS,GAAG,CAAC,UAACnQ,CAAC;QAAA,OAAKA,CAAC,CAAC+L,EAAE;MAAA,EAAC,CAACqE,IAAI,CAAC,GAAG,CAAC;MACxD,IAAMC,aAAa,GAAG;QAAEN,QAAQ,EAAE4C,kBAAkB;QAAErC,IAAI,EAAEvE,EAAE;QAAEwE,aAAa,EAAEzE;MAAO,CAAC;MACvF,IAAM0E,MAAM,GAAG8B,OAAO,GAAA7B,aAAA,CAAAA,aAAA,KAAQJ,aAAa;QAAEK,KAAK,EAAE;UAAEC,WAAW,EAAEjJ,aAAa,CAAC1J;QAAM;MAAC,KAAKqS,aAAa;MAC1G5L,QAAQ,CAAC,YAAM;QAAA,IAAAmO,iBAAA,EAAAC,iBAAA;QACb7D,UAAU,CAAC,CAAC;QACZiB,gBAAgB,CAACO,MAAM,EAAEhI,QAAQ,CAACxK,KAAK,CAAC;QACxC,CAAA4U,iBAAA,GAAAtK,SAAS,CAACtK,KAAK,cAAA4U,iBAAA,eAAfA,iBAAA,CAAiB9F,aAAa,CAAC,EAAE,CAAC;QAClC,CAAA+F,iBAAA,GAAAvK,SAAS,CAACtK,KAAK,cAAA6U,iBAAA,eAAfA,iBAAA,CAAiBpB,gBAAgB,CAAC,EAAE,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC;IACD,IAAMqB,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAK;MAC5B;MACAA,GAAG,GAAGA,GAAG,CAACrJ,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;MACrC;MACAqJ,GAAG,GAAGA,GAAG,CAACrJ,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAC5B,IAAMsJ,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;MAC3B,OAAOC,GAAG;IACZ,CAAC;IACD,IAAM/C,gBAAgB;MAAA,IAAAkD,KAAA,GAAApP,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0Q,SAAA;QAAA,IAAA5C,MAAA;UAAA6C,WAAA;UAAA5G,IAAA;UAAA6G,SAAA;UAAAC,YAAA;UAAAC,YAAA;UAAA1M,KAAA;UAAA8F,IAAA;UAAA6G,eAAA;UAAAxV,CAAA;UAAAyN,GAAA;UAAAgI,QAAA;UAAAC,YAAA;UAAAC,MAAA;UAAAC,OAAA;UAAAC,kBAAA;UAAAC,aAAA;UAAAC,aAAA;UAAAC,qBAAA;UAAAC,aAAA;UAAAC,MAAA,GAAAnQ,SAAA;QAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAAuV,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlR,IAAA,GAAAkR,SAAA,CAAA7S,IAAA;YAAA;cAAOgP,MAAM,GAAA2D,MAAA,CAAA9R,MAAA,QAAA8R,MAAA,QAAA7C,SAAA,GAAA6C,MAAA,MAAG,CAAC,CAAC;cACzCjL,WAAW,CAAClL,KAAK,GAAG,IAAI;cAAAqW,SAAA,CAAAlR,IAAA;cAElBkQ,WAAW,GAAG,CAAC,CAAC;cACpB,IAAI7C,MAAM,CAACE,KAAK,IAAIjJ,YAAY,CAACzJ,KAAK,CAAC0S,KAAK,EAAE;gBAC5C2C,WAAW,GAAG;kBAAE3C,KAAK,EAAAD,aAAA,CAAAA,aAAA,KAAOD,MAAM,CAACE,KAAK,GAAKjJ,YAAY,CAACzJ,KAAK,CAAC0S,KAAK;gBAAG,CAAC;cAC3E;cACA2C,WAAW,GAAA5C,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAQD,MAAM,GAAK/I,YAAY,CAACzJ,KAAK,GAAKqV,WAAW,CAAE;cAClElK,SAAS,GAAG,IAAImL,IAAI,CAAC,CAAC;cACtBvL,cAAc,GAAGjE,WAAW,CAAC,mBAAmB,EAAE;gBAChDyP,IAAI,EAAEtB,IAAI,CAACuB,SAAS,CAAA/D,aAAA;kBAClBgE,iBAAiB,EAAEpN,UAAU,CAACrJ,KAAK;kBACnC6J,MAAM,EAAEA,MAAM,CAAC7J;gBAAK,GACjBqV,WAAW,CACf,CAAC;gBACFqB,SAASA,CAAE7C,KAAK,EAAE;kBAChB;kBACA7I,OAAO,CAAChL,KAAK,GAAG,KAAK;kBACrB,IAAI6T,KAAK,CAACpF,IAAI,KAAK,QAAQ,EAAE;oBACrBA,IAAI,GAAGwG,IAAI,CAACC,KAAK,CAACrB,KAAK,CAACpF,IAAI,CAAC;oBACnCkD,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEnD,IAAI,CAAC;oBACvB,IAAIkI,KAAK,CAACC,OAAO,CAACnI,IAAI,CAAC,EAAE;sBACvB;sBACI6G,SAAS,GAAG,EAAE;sBACdC,YAAY,GAAG,EAAE;sBACjBC,YAAY,GAAG,EAAE;sBACrB,KAAS1M,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG2F,IAAI,CAACpK,MAAM,EAAEyE,KAAK,EAAE,EAAE;wBAC1C8F,IAAI,GAAGH,IAAI,CAAC3F,KAAK,CAAC;wBACxB,IAAI,OAAO8F,IAAI,KAAK,QAAQ,EAAE;0BAC5B4G,YAAY,CAACxR,IAAI,CAAAyO,aAAA,CAAAA,aAAA,KAAM4C,WAAW;4BAAEtD,QAAQ,EAAEnD;0BAAI,EAAE,CAAC;wBACvD,CAAC,MAAM;0BACL,IAAIA,IAAI,aAAJA,IAAI,gBAAA6G,eAAA,GAAJ7G,IAAI,CAAE0G,SAAS,cAAAG,eAAA,eAAfA,eAAA,CAAiBpR,MAAM,EAAE;4BAC3B,KAASpE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAG2O,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,SAAS,CAACjR,MAAM,GAAEpE,CAAC,EAAE,EAAE;8BACzCyN,GAAG,GAAGkB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,SAAS,CAACrV,CAAC,CAAC;8BAC9BqV,SAAS,CAACtR,IAAI,CAAAyO,aAAA,CAAAA,aAAA,KAAM/E,GAAG;gCAAEmJ,eAAe,EAAEnJ,GAAG,CAACoJ;8BAAC,EAAE,CAAC;4BACpD;0BACF;0BACA,IAAIlI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmI,UAAU,EAAE;4BACpBxB,YAAY,CAACvR,IAAI,CAAC8Q,YAAY,CAAClG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmI,UAAU,CAAC,CAAC;0BACnD;wBACF;sBACF;sBACA/M,WAAW,CAAChK,KAAK,CAACgK,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACsM,QAAQ,GAAG2E,SAAS;sBACpEtL,WAAW,CAAChK,KAAK,CAACgK,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACuM,SAAS,GAAG2E,YAAY;sBACxEvL,WAAW,CAAChK,KAAK,CAACgK,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACwM,SAAS,GAAG2E,YAAY;oBAC1E,CAAC,MAAM;sBACL;sBACMI,MAAM,GAAG,CAAAnH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuI,OAAO,KAAI,CAAC,CAAC,CAAC,CAAC;sBAC9BnB,OAAO,GAAG,EAAAH,QAAA,GAAAE,MAAM,CAAC,CAAC,CAAC,cAAAF,QAAA,uBAATA,QAAA,CAAWuB,KAAK,KAAI,CAAC,CAAC;sBACtC,IAAIvX,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAACwU,OAAO,EAAE,mBAAmB,CAAC,EAAE;wBACtE,CAAAC,kBAAA,GAAA5M,WAAW,CAAClJ,KAAK,cAAA8V,kBAAA,eAAjBA,kBAAA,CAAmBoB,aAAa,CAACrB,OAAO,CAACsB,iBAAiB,IAAI,EAAE,CAAC;wBACjE,IAAInN,WAAW,CAAChK,KAAK,CAACgK,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACmJ,IAAI,EAAE;0BACxDrC,SAAS,GAAG,IAAI;0BAChBC,OAAO,GAAG,IAAI;wBAChB,CAAC,MAAM;0BACLA,OAAO,GAAG,IAAIkL,IAAI,CAAC,CAAC;0BACdP,aAAa,GAAG3K,OAAO,GAAGD,SAAS;0BACzCnB,WAAW,CAAChK,KAAK,CAACgK,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACmJ,IAAI,GAAGP,YAAY,CAAC8I,aAAa,CAAC;wBACpF;sBACF;sBACA,IAAIrW,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAACwU,OAAO,EAAE,SAAS,CAAC,EAC1D,CAAAF,YAAA,GAAA5M,KAAK,CAAC/I,KAAK,cAAA2V,YAAA,eAAXA,YAAA,CAAauB,aAAa,CAACrB,OAAO,CAACtF,OAAO,IAAI,EAAE,CAAC;sBACnD9J,QAAQ,CAAC,YAAM;wBACbuK,UAAU,CAAC,CAAC;sBACd,CAAC,CAAC;oBACJ;kBACF,CAAC,MAAM;oBACL;oBACA,CAAAgF,aAAA,GAAAjN,KAAK,CAAC/I,KAAK,cAAAgW,aAAA,eAAXA,aAAA,CAAakB,aAAa,CAAC,EAAE,CAAC;oBAC9BzQ,QAAQ,CAAC,YAAM;sBACbuK,UAAU,CAAC,CAAC;oBACd,CAAC,CAAC;oBACF/F,QAAQ,CAACjL,KAAK,GAAG,KAAK;oBACtBkL,WAAW,CAAClL,KAAK,GAAG,KAAK;oBACzBgK,WAAW,CAAChK,KAAK,CAACgK,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACiM,UAAU,GAAG,IAAI;oBACjE,IAAI,CAACvG,WAAW,CAAC/J,KAAK,EAAE,CAAAiW,qBAAA,GAAA7L,cAAc,CAACpK,KAAK,cAAAiW,qBAAA,eAApBA,qBAAA,CAAsBmB,OAAO,CAAC,CAAC;kBACzD;gBACF,CAAC;gBACDC,OAAOA,CAAEC,GAAG,EAAE;kBACZ3F,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE0F,GAAG,CAAC;gBAC7B,CAAC;gBACDC,OAAOA,CAAA,EAAI;kBACTvM,OAAO,CAAChL,KAAK,GAAG,KAAK;kBACrBiL,QAAQ,CAACjL,KAAK,GAAG,KAAK;kBACtBkL,WAAW,CAAClL,KAAK,GAAG,KAAK;kBACzB2R,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;gBACvB;cACF,CAAC,CAAC;cAAAyE,SAAA,CAAA7S,IAAA;cAAA,OACIuH,cAAc,CAACyM,OAAO;YAAA;cAAAnB,SAAA,CAAA7S,IAAA;cAAA;YAAA;cAAA6S,SAAA,CAAAlR,IAAA;cAAAkR,SAAA,CAAAoB,EAAA,GAAApB,SAAA;cAE5BrL,OAAO,CAAChL,KAAK,GAAG,KAAK;cACrBiL,QAAQ,CAACjL,KAAK,GAAG,KAAK;cACtBkL,WAAW,CAAClL,KAAK,GAAG,KAAK;cACzB,CAAAkW,aAAA,GAAAnN,KAAK,CAAC/I,KAAK,cAAAkW,aAAA,eAAXA,aAAA,CAAagB,aAAa,CAAC,cAAc,CAAC;cAC1CzQ,QAAQ,CAAC,YAAM;gBACbuK,UAAU,CAAC,CAAC;cACd,CAAC,CAAC;cACFW,OAAO,CAAC+F,KAAK,CAAC,WAAW,EAAArB,SAAA,CAAAoB,EAAO,CAAC;YAAA;cAAApB,SAAA,CAAAlR,IAAA;cAAA,OAAAkR,SAAA,CAAA3Q,MAAA;YAAA;YAAA;cAAA,OAAA2Q,SAAA,CAAA/Q,IAAA;UAAA;QAAA,GAAA8P,QAAA;MAAA,CAIpC;MAAA,gBArGKnD,gBAAgBA,CAAA;QAAA,OAAAkD,KAAA,CAAAlP,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqGrB;IACD,IAAM2R,cAAc,GAAG,SAAjBA,cAAcA,CAAIjK,GAAG,EAAK;MAC9BrC,QAAQ,CAACrL,KAAK,GAAG0N,GAAG,CAACkK,UAAU;MAC/BtM,QAAQ,CAACtL,KAAK,GAAG0N,GAAG;MACpBnC,QAAQ,CAACvL,KAAK,GAAG,IAAI;IACvB,CAAC;IACD,IAAM6X,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAItH,OAAO,EAAEtQ,CAAC,EAAK;MAAA,IAAA6X,mBAAA;MACxC,IAAMhP,KAAK,GAAG,CAAC7I,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;MAC7B,IAAM8X,WAAW,GAAG,EAAAD,mBAAA,GAAAnP,MAAM,CAAC3I,KAAK,CAAC8I,KAAK,CAAC,cAAAgP,mBAAA,gBAAAA,mBAAA,GAAnBA,mBAAA,CAAqB/O,KAAK,cAAA+O,mBAAA,uBAA1BA,mBAAA,CAA4BE,SAAS,KAAIzH,OAAO;MACpE,IAAM0H,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACnDF,QAAQ,CAACG,QAAQ,GAAG,UAAU;MAC9BH,QAAQ,CAACI,KAAK,CAACC,QAAQ,GAAG,UAAU;MACpCL,QAAQ,CAACI,KAAK,CAACE,IAAI,GAAG,SAAS;MAC/BN,QAAQ,CAACjY,KAAK,GAAG+X,WAAW;MAC5BG,QAAQ,CAAC3B,IAAI,CAACiC,WAAW,CAACP,QAAQ,CAAC;MACnCA,QAAQ,CAACQ,MAAM,CAAC,CAAC;MACjB,IAAMxF,MAAM,GAAGiF,QAAQ,CAACQ,WAAW,CAAC,MAAM,CAAC;MAC3C,IAAIzF,MAAM,EAAE;QACV7L,SAAS,CAAC;UAAEyK,OAAO,EAAE,MAAM;UAAE1Q,IAAI,EAAE;QAAU,CAAC,CAAC;MACjD;MACA+W,QAAQ,CAAC3B,IAAI,CAACoC,WAAW,CAACV,QAAQ,CAAC;IACrC,CAAC;IACD,IAAMW,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIhK,IAAI,EAAE9F,KAAK,EAAK;MAC1C,IAAMzE,MAAM,GAAG2F,WAAW,CAAChK,KAAK,CAACqE,MAAM,GAAG,CAAC;MAC3C,IAAIyE,KAAK,KAAKzE,MAAM,IAAI0G,cAAc,EAAE;QACtCC,OAAO,CAAChL,KAAK,GAAG,IAAI;QACpBiL,QAAQ,CAACjL,KAAK,GAAG,IAAI;QACrBkL,WAAW,CAAClL,KAAK,GAAG,IAAI;QACxBgK,WAAW,CAAChK,KAAK,CAACqE,MAAM,CAAC,GAAG;UAC1B0J,EAAE,EAAEtC,IAAI,CAAC,CAAC;UACVtK,IAAI,EAAE,KAAK;UACXkP,UAAU,EAAE,IAAI;UAChBC,UAAU,EAAE,KAAK;UACjBC,OAAO,EAAE,EAAE;UACXC,UAAU,EAAE,EAAE;UACdC,aAAa,EAAE,EAAE;UACjBC,gBAAgB,EAAE,EAAE;UACpBlD,IAAI,EAAE,EAAE;UACRmD,QAAQ,EAAE,EAAE;UACZnG,QAAQ,EAAE,EAAE;UACZoG,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE;QACb,CAAC;QACDpK,QAAQ,cAAAV,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAC,SAAAmU,SAAA;UAAA,OAAAvZ,mBAAA,GAAAuB,IAAA,UAAAiY,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA5T,IAAA,GAAA4T,SAAA,CAAAvV,IAAA;cAAA;gBAAAuV,SAAA,CAAAvV,IAAA;gBAAA,OACDuH,cAAc,CAACiO,KAAK,CAAC,CAAC;cAAA;cAAA;gBAAA,OAAAD,SAAA,CAAAzT,IAAA;YAAA;UAAA,GAAAuT,QAAA;QAAA,CAC7B,GAAC;MACJ,CAAC,MAAM;QACLnO,QAAQ,CAAC1K,KAAK,GAAG,KAAK;QACtBwK,QAAQ,CAACxK,KAAK,GAAGgK,WAAW,CAAChK,KAAK,CAAC8I,KAAK,GAAG,CAAC,CAAC,CAAC0B,QAAQ;QACtD0H,iBAAiB,CAAClI,WAAW,CAAChK,KAAK,CAAC8I,KAAK,GAAG,CAAC,CAAC,CAACyH,OAAO,CAAC;MACzD;IACF,CAAC;IACD,IAAM0I,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAIrK,IAAI,EAAE9F,KAAK,EAAK;MAAA,IAAAoQ,qBAAA,EAAAC,qBAAA;MAChD;MACA,IAAMC,aAAa,GAAGtQ,KAAK,GAAG,CAAC;MAC/B,IAAMiJ,QAAQ,GAAG,EAAAmH,qBAAA,GAAAlP,WAAW,CAAChK,KAAK,CAACoZ,aAAa,CAAC,cAAAF,qBAAA,uBAAhCA,qBAAA,CAAkC3I,OAAO,KAAI,EAAE;MAChE,IAAM8I,WAAW,GAAI,CAACvQ,KAAK,GAAG,CAAC,IAAI,CAAC,GAAI,CAAC;MACzC,IAAMqH,MAAM,GAAG,EAAAgJ,qBAAA,GAAAxQ,MAAM,CAAC3I,KAAK,CAACqZ,WAAW,CAAC,cAAAF,qBAAA,gBAAAA,qBAAA,GAAzBA,qBAAA,CAA2BpQ,KAAK,cAAAoQ,qBAAA,uBAAhCA,qBAAA,CAAkCnB,SAAS,KAAIpJ,IAAI,CAAC2B,OAAO,IAAI3B,IAAI,CAAC4B,UAAU,IAAI,EAAE;MACnG;MACAlF,QAAQ,CAACtL,KAAK,GAAAyS,aAAA,CAAAA,aAAA,KACT7D,IAAI;QACPmD,QAAQ,EAAEA,QAAQ;QAClB5B,MAAM,EAAEA,MAAM;QACdtG,MAAM,EAAEA,MAAM,CAAC7J,KAAK;QACpB+J,WAAW,EAAEA,WAAW,CAAC/J;MAAK,EAC/B;MACD2R,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEtG,QAAQ,CAACtL,KAAK,CAAC;MAChDwL,sBAAsB,CAACxL,KAAK,GAAG,IAAI;IACrC,CAAC;IACD,IAAM8R,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B/G,cAAc,GAAG,IAAI;MACrBC,OAAO,CAAChL,KAAK,GAAG,KAAK;MACrBiL,QAAQ,CAACjL,KAAK,GAAG,KAAK;MACtBkL,WAAW,CAAClL,KAAK,GAAG,KAAK;IAC3B,CAAC;IACD,IAAMuO,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B,IAAIxD,cAAc,EAAE;QAAA,IAAAuO,sBAAA;QAClBvO,cAAc,CAACwO,KAAK,CAAC,CAAC;QACtBvO,OAAO,CAAChL,KAAK,GAAG,KAAK;QACrBiL,QAAQ,CAACjL,KAAK,GAAG,KAAK;QACtBkL,WAAW,CAAClL,KAAK,GAAG,KAAK;QACzB2R,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;QACvB,IAAI,CAAC7H,WAAW,CAAC/J,KAAK,EAAE,CAAAsZ,sBAAA,GAAAlP,cAAc,CAACpK,KAAK,cAAAsZ,sBAAA,eAApBA,sBAAA,CAAsBlC,OAAO,CAAC,CAAC;MACzD;MACAtF,kBAAkB,CAAC,CAAC;IACtB,CAAC;IACD,IAAM0H,oBAAoB;MAAA,IAAAC,KAAA,GAAA1T,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgV,SAAA;QAAA,IAAAC,qBAAA,EAAAlL,IAAA;QAAA,OAAAnP,mBAAA,GAAAuB,IAAA,UAAA+Y,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1U,IAAA,GAAA0U,SAAA,CAAArW,IAAA;YAAA;cAAAqW,SAAA,CAAArW,IAAA;cAAA,OACJ4C,GAAG,CAACoT,oBAAoB,CAAC;gBAAEM,GAAG,EAAErO,IAAI,CAAC,CAAC;gBAAEwE,KAAK,EAAE;kBAAE8J,aAAa,EAAE1Q,UAAU,CAACrJ;gBAAM;cAAE,CAAC,CAAC;YAAA;cAAA2Z,qBAAA,GAAAE,SAAA,CAAA5W,IAAA;cAApGwL,IAAI,GAAAkL,qBAAA,CAAJlL,IAAI;cACZ9E,OAAO,CAAC3J,KAAK,GAAG,CAAAyO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuL,KAAK,KAAI,EAAE;cACjCpQ,OAAO,CAAC5J,KAAK,GAAG,CAAAyO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwL,WAAW,KAAI,EAAE;cACvCnQ,OAAO,CAAC9J,KAAK,CAACqJ,UAAU,CAACrJ,KAAK,CAAC,GAAG;gBAAE6J,MAAM,EAAEA,MAAM,CAAC7J,KAAK;gBAAEmP,UAAU,EAAE5F,cAAc,CAACvJ;cAAM,CAAC;YAAA;YAAA;cAAA,OAAA6Z,SAAA,CAAAvU,IAAA;UAAA;QAAA,GAAAoU,QAAA;MAAA,CAC7F;MAAA,gBALKF,oBAAoBA,CAAA;QAAA,OAAAC,KAAA,CAAAxT,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKzB;IACD,IAAMkU,oBAAoB;MAAA,IAAAC,KAAA,GAAApU,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0V,SAAOC,IAAI,EAAElL,UAAU;QAAA,IAAAmL,sBAAA;QAAA,OAAAhb,mBAAA,GAAAuB,IAAA,UAAA0Z,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArV,IAAA,GAAAqV,SAAA,CAAAhX,IAAA;YAAA;cAClDgG,OAAO,CAACxJ,KAAK,GAAG,CAAC;cACjB,CAAAsa,sBAAA,GAAAlQ,cAAc,CAACpK,KAAK,cAAAsa,sBAAA,eAApBA,sBAAA,CAAsBG,iBAAiB,CAACJ,IAAI,EAAElL,UAAU,CAAC;YAAA;YAAA;cAAA,OAAAqL,SAAA,CAAAlV,IAAA;UAAA;QAAA,GAAA8U,QAAA;MAAA,CAC1D;MAAA,gBAHKF,oBAAoBA,CAAAQ,GAAA,EAAAC,GAAA;QAAA,OAAAR,KAAA,CAAAlU,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGzB;IACD,IAAM4U,aAAa,GAAG,SAAhBA,aAAaA,CAAI5F,GAAG,EAAK;MAC7B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE,OAAO,KAAK;MACzD,OAAOtV,MAAM,CAACsF,IAAI,CAACgQ,GAAG,CAAC,CAAC3Q,MAAM,KAAK,CAAC;IACtC,CAAC;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAqC,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACxR,UAAU;IAAA,GAC5B,UAACyR,GAAG,EAAK;MACP,IAAIA,GAAG,EAAE;QACPzR,UAAU,CAACrJ,KAAK,GAAG8a,GAAG;QACtBxM,eAAe,CAAC,CAAC;QACjBD,iBAAiB,CAAC,CAAC;QACnBmL,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,EACD;MAAEuB,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACtR,cAAc;IAAA,GAChC,UAACuR,GAAG,EAAK;MACPvR,cAAc,CAACvJ,KAAK,GAAG8a,GAAG;IAC5B,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACG,YAAY;IAAA,GAC9B,UAACF,GAAG,EAAK;MACP,IAAIF,aAAa,CAACE,GAAG,CAAC,EAAE;QAAA,IAAAG,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QACtB,IAAIL,GAAG,CAACjb,cAAc,CAAC,cAAc,CAAC,EAAE0I,MAAM,CAACvI,KAAK,GAAG,CAAC,CAAC8a,GAAG,CAACM,YAAY;QACzE,IAAIN,GAAG,CAACjb,cAAc,CAAC,YAAY,CAAC,EAAE,CAAAob,iBAAA,GAAA3Q,SAAS,CAACtK,KAAK,cAAAib,iBAAA,eAAfA,iBAAA,CAAiBnM,aAAa,CAACgM,GAAG,CAACO,UAAU,CAAC;QACpF,IAAIP,GAAG,CAACjb,cAAc,CAAC,cAAc,CAAC,EAAE4J,YAAY,CAACzJ,KAAK,GAAG8a,GAAG,CAACrR,YAAY;QAC7E,IAAIqR,GAAG,CAACjb,cAAc,CAAC,eAAe,CAAC,EAAE6J,aAAa,CAAC1J,KAAK,GAAG8a,GAAG,CAACpR,aAAa;QAChF,IAAIoR,GAAG,CAACjb,cAAc,CAAC,kBAAkB,CAAC,EAAE,CAAAqb,kBAAA,GAAA5Q,SAAS,CAACtK,KAAK,cAAAkb,kBAAA,eAAfA,kBAAA,CAAiBzH,gBAAgB,CAACqH,GAAG,CAACQ,gBAAgB,CAAC;QACnG,IAAIR,GAAG,CAACjb,cAAc,CAAC,kBAAkB,CAAC,EAAE,CAAAsb,kBAAA,GAAA7Q,SAAS,CAACtK,KAAK,cAAAmb,kBAAA,eAAfA,kBAAA,CAAiBI,gBAAgB,CAACT,GAAG,CAACU,gBAAgB,CAAC;QACnG,IAAIV,GAAG,CAACjb,cAAc,CAAC,mBAAmB,CAAC,EAAE,IAAIib,GAAG,CAACW,iBAAiB,EAAEvJ,iBAAiB,CAAC4I,GAAG,CAACW,iBAAiB,CAAC;QAChH,IAAIX,GAAG,CAACjb,cAAc,CAAC,uBAAuB,CAAC,EAC7C,IAAI+a,aAAa,CAACE,GAAG,CAACY,qBAAqB,CAAC,EAAEtH,qBAAqB,CAAC0G,GAAG,CAACY,qBAAqB,CAAC;MAClG;IACF,CAAC,EACD;MAAEX,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACO,YAAY;IAAA,GAC9B,UAACN,GAAG,EAAK;MACPvS,MAAM,CAACvI,KAAK,GAAG,CAAC,CAAC8a,GAAG;IACtB,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACQ,UAAU;IAAA,GAC5B,UAACP,GAAG,EAAK;MAAA,IAAAa,kBAAA;MACP,CAAAA,kBAAA,GAAArR,SAAS,CAACtK,KAAK,cAAA2b,kBAAA,eAAfA,kBAAA,CAAiB7M,aAAa,CAACgM,GAAG,CAAC;IACrC,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACpR,YAAY;IAAA,GAC9B,UAACqR,GAAG,EAAK;MACPrR,YAAY,CAACzJ,KAAK,GAAG8a,GAAG;IAC1B,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACnR,aAAa;IAAA,GAC/B,UAACoR,GAAG,EAAK;MACPpR,aAAa,CAAC1J,KAAK,GAAG8a,GAAG;IAC3B,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACS,gBAAgB;IAAA,GAClC,UAACR,GAAG,EAAK;MAAA,IAAAc,kBAAA;MACP,CAAAA,kBAAA,GAAAtR,SAAS,CAACtK,KAAK,cAAA4b,kBAAA,eAAfA,kBAAA,CAAiBnI,gBAAgB,CAACqH,GAAG,CAAC;IACxC,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACW,gBAAgB;IAAA,GAClC,UAACV,GAAG,EAAK;MAAA,IAAAe,kBAAA;MACP,CAAAA,kBAAA,GAAAvR,SAAS,CAACtK,KAAK,cAAA6b,kBAAA,eAAfA,kBAAA,CAAiBN,gBAAgB,CAACT,GAAG,CAAC;IACxC,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACY,iBAAiB;IAAA,GACnC,UAACX,GAAG,EAAK;MACP,IAAIA,GAAG,EAAE5I,iBAAiB,CAAC4I,GAAG,CAAC;IACjC,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAMmB,KAAK,CAACgT,KAAK,CAACa,qBAAqB;IAAA,GACvC,UAACZ,GAAG,EAAK;MACP,IAAIF,aAAa,CAACE,GAAG,CAAC,EAAE1G,qBAAqB,CAAC0G,GAAG,CAAC;IACpD,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IACDrU,KAAK,CACH;MAAA,OAAM6B,MAAM,CAACvI,KAAK;IAAA,GAClB,UAAC8a,GAAG,EAAK;MACP,IAAIA,GAAG,IAAItR,OAAO,CAACxJ,KAAK,EAAE;QACxBwJ,OAAO,CAACxJ,KAAK,GAAG,CAAC;QACjByG,QAAQ,CAAC,YAAM;UACbuK,UAAU,CAAC,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC,EACD;MAAE+J,SAAS,EAAE;IAAK,CACpB,CAAC;IACD,IAAMe,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIrN,IAAI,EAAK;MACnC,IAAAsN,cAAA,GAUItN,IAAI,CATNrF,QAAQ;QAAE4S,WAAW,GAAAD,cAAA,cAAG,EAAE,GAAAA,cAAA;QAAAE,gBAAA,GASxBxN,IAAI,CARNpF,UAAU;QAAE6S,aAAa,GAAAD,gBAAA,cAAG,EAAE,GAAAA,gBAAA;QAAAE,kBAAA,GAQ5B1N,IAAI,CAPNnF,YAAY;QAAE8S,eAAe,GAAAD,kBAAA,cAAG,EAAE,GAAAA,kBAAA;QAAAE,oBAAA,GAOhC5N,IAAI,CANNlF,cAAc;QAAE+S,iBAAiB,GAAAD,oBAAA,cAAG,EAAE,GAAAA,oBAAA;QAAAE,kBAAA,GAMpC9N,IAAI,CALN2M,YAAY;QAAEoB,eAAe,GAAAD,kBAAA,cAAG,KAAK,GAAAA,kBAAA;QAAAE,gBAAA,GAKnChO,IAAI,CAJN4M,UAAU;QAAEqB,aAAa,GAAAD,gBAAA,cAAG,EAAE,GAAAA,gBAAA;QAAAE,qBAAA,GAI5BlO,IAAI,CAHNmO,eAAe;QAAEC,kBAAkB,GAAAF,qBAAA,cAAG,KAAK,GAAAA,qBAAA;QAAAG,kBAAA,GAGzCrO,IAAI,CAFNhF,YAAY;QAAEsT,eAAe,GAAAD,kBAAA,cAAG,CAAC,CAAC,GAAAA,kBAAA;QAAAE,mBAAA,GAEhCvO,IAAI,CADN/E,aAAa;QAAEuT,gBAAgB,GAAAD,mBAAA,cAAG,EAAE,GAAAA,mBAAA;MAEtC5T,QAAQ,CAACpJ,KAAK,GAAGgc,WAAW;MAC5B3S,UAAU,CAACrJ,KAAK,GAAGkc,aAAa;MAChC5S,YAAY,CAACtJ,KAAK,GAAGoc,eAAe;MACpC7S,cAAc,CAACvJ,KAAK,GAAGsc,iBAAiB;MACxC/T,MAAM,CAACvI,KAAK,GAAG,OAAOwc,eAAe,KAAK,SAAS,GAAGA,eAAe,GAAG,KAAK;MAC7E,IAAI,CAAC,OAAOK,kBAAkB,KAAK,SAAS,GAAGA,kBAAkB,GAAG,KAAK,KAAKH,aAAa,CAACrY,MAAM,EAAE;QAAA,IAAA6Y,kBAAA;QAClG,CAAAA,kBAAA,GAAA5S,SAAS,CAACtK,KAAK,cAAAkd,kBAAA,eAAfA,kBAAA,CAAiBpO,aAAa,CAAC4N,aAAa,CAAC;MAC/C;MACAjT,YAAY,CAACzJ,KAAK,GAAG+c,eAAe;MACpCrT,aAAa,CAAC1J,KAAK,GAAGid,gBAAgB;IACxC,CAAC;IACD,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAkC;MAAA,IAAAC,kBAAA,EAAAC,kBAAA;MAAA,IAA9Blc,IAAI,GAAA6E,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAsN,SAAA,GAAAtN,SAAA,MAAG,IAAI;MAAA,IAAEuK,OAAO,GAAAvK,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAsN,SAAA,GAAAtN,SAAA,MAAG,EAAE;MACnD,IAAMsX,OAAO,GAAG,OAAOnc,IAAI,KAAK,SAAS,GAAGA,IAAI,GAAG,IAAI;MACvD,IAAImc,OAAO,EAAE,CAAAF,kBAAA,GAAA9S,SAAS,CAACtK,KAAK,cAAAod,kBAAA,eAAfA,kBAAA,CAAiB3J,gBAAgB,CAAClD,OAAO,CAAC;MACvD,IAAI,CAAC+M,OAAO,EAAE,CAAAD,kBAAA,GAAA/S,SAAS,CAACtK,KAAK,cAAAqd,kBAAA,eAAfA,kBAAA,CAAiB9B,gBAAgB,CAAChL,OAAO,CAAC;IAC1D,CAAC;IACD,IAAMgN,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAmD;MAAA,IAA/CrB,aAAa,GAAAlW,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAsN,SAAA,GAAAtN,SAAA,MAAG,EAAE;MAAA,IAAEsW,iBAAiB,GAAAtW,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAsN,SAAA,GAAAtN,SAAA,MAAG,EAAE;MACrEsI,eAAe,CAAC,CAAC;MACjBD,iBAAiB,CAAC,CAAC;MACnBmL,oBAAoB,CAAC,CAAC;MACtB,IAAI0C,aAAa,EAAEhC,oBAAoB,CAACgC,aAAa,EAAEI,iBAAiB,CAAC;IAC3E,CAAC;IACD,IAAMkB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIjN,OAAO,EAAK;MACpC,IAAIA,OAAO,EAAE;QACXhI,MAAM,CAACvI,KAAK,GAAG,IAAI;QACnBkS,iBAAiB,CAAC3B,OAAO,CAAC;MAC5B;IACF,CAAC;IACD,IAAMkN,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIhP,IAAI,EAAK;MACrC,IAAImM,aAAa,CAACnM,IAAI,CAAC,EAAE;QACvBlG,MAAM,CAACvI,KAAK,GAAG,IAAI;QACnBoU,qBAAqB,CAAC3F,IAAI,CAAC;MAC7B;IACF,CAAC;IACD1H,WAAW,CAACpH,SAAS,CAACqb,YAAY,GAAGc,kBAAkB;IACvD/U,WAAW,CAACpH,SAAS,CAAC+d,YAAY,GAAGP,kBAAkB;IACvDpW,WAAW,CAACpH,SAAS,CAACge,aAAa,GAAGJ,mBAAmB;IACzDxW,WAAW,CAACpH,SAAS,CAACie,UAAU,GAAGJ,gBAAgB;IACnDzW,WAAW,CAACpH,SAAS,CAACke,cAAc,GAAGJ,oBAAoB;IAC3D,IAAMK,gBAAgB;MAAA,IAAAC,KAAA,GAAAhY,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsZ,SAAA;QAAA,IAAAC,qBAAA,EAAAxP,IAAA;QAAA,OAAAnP,mBAAA,GAAAuB,IAAA,UAAAqd,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhZ,IAAA,GAAAgZ,SAAA,CAAA3a,IAAA;YAAA;cAAA2a,SAAA,CAAA3a,IAAA;cAAA,OACA4C,GAAG,CAAC0X,gBAAgB,CAAC;gBAAEM,KAAK,EAAE,CAAC,cAAc;cAAE,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAAlb,IAAA;cAAhEwL,IAAI,GAAAwP,qBAAA,CAAJxP,IAAI;cACZtF,YAAY,CAACnJ,KAAK,GAAGyO,IAAI,CAACtF,YAAY;YAAA;YAAA;cAAA,OAAAgV,SAAA,CAAA7Y,IAAA;UAAA;QAAA,GAAA0Y,QAAA;MAAA,CACvC;MAAA,gBAHKF,gBAAgBA,CAAA;QAAA,OAAAC,KAAA,CAAA9X,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGrB;IACDO,SAAS,CAAC,YAAM;MACduX,gBAAgB,CAAC,CAAC;MAClBzP,iBAAiB,CAAC,CAAC;MACnBmL,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC;IACFhT,WAAW,CAAC,YAAM,CAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}