<template>
  <el-badge :value="totals">
    <el-popover trigger="hover" popper-class="LayoutBoxMessagePopover" transition="zy-el-zoom-in-top">
      <template #reference>
        <div class="LayoutBoxMessage">
          <slot></slot>
        </div>
      </template>
      <div class="LayoutBoxMessageBody">
        <div class="LayoutBoxMessageHead">
          <div class="LayoutBoxMessageName">消息盒子<span @click="handleMark">全部已读</span></div>
          <div @click="openPage({ key: 'routePath', value: '/interaction/BoxMessage' })" class="LayoutBoxMessageText">更多
            <el-icon>
              <DArrowRight />
            </el-icon>
          </div>
        </div>
        <el-scrollbar ref="scrollRef" class="LayoutBoxMessageScrollbar" @scroll="handleScroll">
          <div class="LayoutBoxMessageScroll">
            <div v-for="item in tableData" :key="item.id" class="LayoutBoxMessageItem" @click="handleDetails(item)">
              <div class="LayoutBoxMessageInfo">
                <div class="LayoutBoxMessageType">{{ item.moduleName }}</div>
                <div class="LayoutBoxMessageTime">{{ format(item.createDate) }}</div>
              </div>
              <div class="LayoutBoxMessageTitle">{{ item.content }}</div>
            </div>
            <div class="LayoutBoxMessageLoadingText" v-if="loading">加载中...</div>
            <div class="LayoutBoxMessageLoadingText" v-if="isShow">没有更多了</div>
          </div>
        </el-scrollbar>
      </div>
    </el-popover>
  </el-badge>
</template>
<script>
export default { name: 'LayoutBoxMessage' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, inject, watch } from 'vue'
import { useStore } from 'vuex'
import { format } from 'common/js/time.js'
import { ElMessage, ElMessageBox } from 'element-plus'
const store = useStore()
const openPage = inject('openPage')
const scrollRef = ref()
const loadingScroll = ref(false)
const pageNo = ref(1)
const pageSize = ref(10)
const totals = ref(0)
const isShow = ref(false)
const loading = ref(true)
const tableData = ref([])

onMounted(() => { boxMessageList() })

const handleScroll = ({ scrollTop }) => {
  if (!scrollRef.value) return
  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef
  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {
    load()
  }
}
const load = () => {
  if (pageNo.value * pageSize.value >= totals.value) return
  loadingScroll.value = true
  pageNo.value += 1
  boxMessageList()
}
const boxMessageList = async () => {
  const { data, total } = await api.boxMessageList({
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    hasRead: 0,
    objectParam: {}
  })
  tableData.value = [...tableData.value, ...data]
  totals.value = total
  loading.value = pageNo.value * pageSize.value < totals.value
  isShow.value = pageNo.value * pageSize.value >= totals.value
  loadingScroll.value = false
}
const handleDetails = (item) => {
  if (!item.redirectUrl && item.businessCode !== 'system') return ElMessage({ type: 'info', message: `当前${item.moduleName || ''}数据没有跳转路径，请维护好跳转路径在进行查看详情！` })
  if (item.isDisabled) return ElMessage({ type: 'info', message: `当前${item.moduleName || ''}数据已被删除！` })
  openPage({ key: 'routePath', value: '/interaction/BoxMessage' })
  sessionStorage.setItem('BoxMessage', JSON.stringify(item || ''))
}
const handleMark = () => {
  ElMessageBox.confirm('此操作将把所有的消息设为已读, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => { boxMessageRead() }).catch(() => { ElMessage({ type: 'info', message: '已取消操作' }) })
}
const boxMessageRead = async () => {
  const { code } = await api.boxMessageRead({ businessCode: 'box_message' })
  if (code === 200) {
    ElMessage({ type: 'success', message: '全部设为已读成功' })
    pageNo.value = 1
    pageSize.value = 10
    totals.value = 0
    isShow.value = false
    loading.value = true
    tableData.value = []
    boxMessageList()
  }
}
watch(() => store.state.socket, (val) => {
  if (val) {
    store.state.socket.on('message', (res) => {
      const data = JSON.parse(res)
      if (data.messageType === 'box_message') {
        pageNo.value = 1
        pageSize.value = 10
        totals.value = 0
        isShow.value = false
        loading.value = true
        tableData.value = []
        boxMessageList()
      }
    })
  }
})
watch(() => store.state.boxMessageRefresh, (val) => {
  if (val) {
    pageNo.value = 1
    pageSize.value = 10
    totals.value = 0
    isShow.value = false
    loading.value = true
    tableData.value = []
    boxMessageList()
    store.commit('setBoxMessageRefresh', false)
  }
})
</script>
<style lang="scss">
.LayoutBoxMessagePopover {
  width: 500px !important;
  padding: 0 !important;

  .LayoutBoxMessageHead {
    padding: var(--zy-distance-five) var(--zy-distance-two);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--zy-el-border-color-lighter);

    .LayoutBoxMessageName {
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      color: var(--zy-el-text-color-primary);

      span {
        font-size: var(--zy-text-font-size);
        margin-left: 20px;
        font-weight: normal;
        color: var(--zy-el-color-primary);
        cursor: pointer;
      }
    }

    .LayoutBoxMessageText {
      display: flex;
      align-items: center;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      color: var(--zy-el-text-color-regular);
      cursor: pointer;
    }
  }

  .LayoutBoxMessageScrollbar {
    width: 100%;
    height: 60vh;

    // .zy-el-scrollbar__wrap {
    //   max-height: 60vh;
    // }
  }

  .LayoutBoxMessageScroll {
    padding: var(--zy-distance-five) 0;

    .LayoutBoxMessageItem {
      cursor: pointer;
      padding: var(--zy-distance-five) var(--zy-distance-two);

      .LayoutBoxMessageInfo {
        display: flex;
        align-items: center;
        position: relative;

        &::after {
          content: "";
          position: absolute;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: var(--zy-el-color-danger);
        }

        .LayoutBoxMessageType {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          border: 1px solid var(--zy-el-color-primary);
          border-radius: var(--el-border-radius-small);
          color: var(--zy-el-color-primary);
          padding: 0 12px;
        }

        .LayoutBoxMessageTime {
          margin-left: 20px;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          color: var(--zy-el-text-color-regular);
        }
      }

      .LayoutBoxMessageTitle {
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);
        color: var(--zy-el-text-color-primary);
        padding-top: var(--zy-font-name-distance-five);
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }

    .LayoutBoxMessageLoadingText {
      text-align: center;
      color: var(--zy-el-text-color-regular);
      padding: var(--zy-distance-three) 0;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
    }
  }
}
</style>
