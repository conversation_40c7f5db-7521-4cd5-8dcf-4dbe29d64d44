{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ManualMergeProposal\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table = _resolveComponent(\"xyl-global-table\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList,\n    data: $setup.tableHead,\n    buttonNumber: 2,\n    ref: \"queryRef\"\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\", \"data\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect,\n    onSortChange: $setup.handleSortChange,\n    \"header-cell-class-name\": $setup.handleHeaderClass\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_xyl_global_table, {\n        tableHead: $setup.tableHead,\n        onTableClick: $setup.handleTableClick,\n        noTooltip: ['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\n      }, {\n        mainHandleOffices: _withCtx(function (scope) {\n          var _scope$row$mainHandle, _scope$row$publishHan;\n          return [scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n            key: 0\n          }, [_createTextVNode(_toDisplayString((_scope$row$mainHandle = scope.row.mainHandleOffices) === null || _scope$row$mainHandle === void 0 ? void 0 : _scope$row$mainHandle.map(function (v) {\n            return v.flowHandleOfficeName;\n          }).join('、')), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n            key: 1\n          }, [_createTextVNode(_toDisplayString((_scope$row$publishHan = scope.row.publishHandleOffices) === null || _scope$row$publishHan === void 0 ? void 0 : _scope$row$publishHan.map(function (v) {\n            return v.flowHandleOfficeName;\n          }).join('、')), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */))];\n        }),\n        assistHandleOffices: _withCtx(function (scope) {\n          var _scope$row$assistHand, _scope$row$assistHand2;\n          return [scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n            key: 0\n          }, [_createTextVNode(_toDisplayString((_scope$row$assistHand = scope.row.assistHandleOffices) === null || _scope$row$assistHand === void 0 ? void 0 : _scope$row$assistHand.map(function (v) {\n            return v.flowHandleOfficeName;\n          }).join('、')), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n            key: 1\n          }, [_createTextVNode(_toDisplayString((_scope$row$assistHand2 = scope.row.assistHandleVoList) === null || _scope$row$assistHand2 === void 0 ? void 0 : _scope$row$assistHand2.map(function (v) {\n            return v.flowHandleOfficeName;\n          }).join('、')), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"tableHead\"]), _createVNode(_component_xyl_global_table_button, {\n        editCustomTableHead: $setup.handleEditorCustom\n      }, null, 8 /* PROPS */, [\"editCustomTableHead\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\", \"onSortChange\", \"header-cell-class-name\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.mergeShow,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.mergeShow = $event;\n    }),\n    name: \"合并提案\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SubmitMergerProposal\"], {\n        isSimilar: false,\n        mergeData: $setup.mergeData,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"mergeData\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "data", "tableHead", "buttonNumber", "ref", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "onSortChange", "handleSortChange", "handleHeaderClass", "default", "_component_el_table_column", "type", "width", "fixed", "_component_xyl_global_table", "onTableClick", "handleTableClick", "noTooltip", "mainHandleOffices", "scope", "_scope$row$mainHandle", "_scope$row$publishHan", "row", "length", "_Fragment", "key", "_createTextVNode", "_toDisplayString", "map", "v", "flowHandleOfficeName", "join", "publishHandleOffices", "assistHandleOffices", "_scope$row$assistHand", "_scope$row$assistHand2", "assistHandleVoList", "_component_xyl_global_table_button", "editCustomTableHead", "handleEditorCustom", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "mergeShow", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mergeData", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\ManualMergeProposal.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ManualMergeProposal\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\" :data=\"tableHead\" :buttonNumber=\"2\" ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\" @sort-change=\"handleSortChange\" :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\"\r\n          :noTooltip=\"['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\">\r\n          <template #mainHandleOffices=\"scope\">\r\n            <template v-if=\"scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0\">\r\n              {{ scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n            </template>\r\n            <template v-else>\r\n              {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n            </template>\r\n          </template>\r\n          <template #assistHandleOffices=\"scope\">\r\n            <template v-if=\"scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0\">\r\n              {{ scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n            </template>\r\n            <template v-else>\r\n              {{ scope.row.assistHandleVoList?.map(v => v.flowHandleOfficeName).join('、') }}\r\n            </template>\r\n          </template>\r\n          <!-- <template #publishHandleOffices=\"scope\">\r\n            {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n          </template> -->\r\n        </xyl-global-table>\r\n        <xyl-global-table-button :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"mergeShow\" name=\"合并提案\">\r\n      <SubmitMergerProposal :isSimilar=\"false\" :mergeData=\"mergeData\" @callback=\"callback\"></SubmitMergerProposal>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ManualMergeProposal' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\nimport SubmitMergerProposal from './SubmitMergerProposal'\r\nconst buttonList = [{ id: 'merge', name: '合并提案', type: 'primary', has: '' }]\r\nconst mergeData = ref([])\r\nconst mergeShow = ref(false)\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  handleQuery,\r\n  tableDataArray,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  handleEditorCustom,\r\n  tableRefReset\r\n} = GlobalTable({ tableId: 'id_prop_proposal_prepare_merge_proposal', tableApi: 'suggestionList' })\r\n\r\nonActivated(() => { handleQuery() })\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType, params) => {\r\n  switch (isType) {\r\n    case 'merge':\r\n      handleMerge()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } } })\r\n}\r\nconst handleMerge = () => {\r\n  if (tableDataArray.value.length <= 1) return ElMessage({ type: 'warning', message: '请至少选择两条提案并案！' })\r\n  mergeData.value = tableDataArray.value\r\n  mergeShow.value = true\r\n}\r\nconst callback = (type) => {\r\n  if (type) { tableRefReset() }\r\n  handleQuery()\r\n  mergeShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ManualMergeProposal {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAOzBA,KAAK,EAAC;AAAa;;EA6BnBA,KAAK,EAAC;AAAkB;;;;;;;;;;uBApC/BC,mBAAA,CA4CM,OA5CNC,UA4CM,GA3CJC,YAAA,CAKoBC,4BAAA;IALAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IAAGC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IAAGC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC/FC,UAAU,EAAEN,MAAA,CAAAM,UAAU;IAAGC,IAAI,EAAEP,MAAA,CAAAQ,SAAS;IAAGC,YAAY,EAAE,CAAC;IAAEC,GAAG,EAAC;;IACtDC,MAAM,EAAAC,QAAA,CACf;MAAA,OAAwF,CAAxFf,YAAA,CAAwFgB,mBAAA;QALhGC,UAAA,EAK2Bd,MAAA,CAAAe,OAAO;QALlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAK2BjB,MAAA,CAAAe,OAAO,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAL/DC,SAAA,CAKuEpB,MAAA,CAAAC,WAAW;QAAEoB,SAAS,EAAT;;;IALpFC,CAAA;+CAQIC,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJ3B,YAAA,CA0BW4B,mBAAA;IA1BDf,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAAEH,IAAI,EAAEP,MAAA,CAAA0B,SAAS;IAAGC,QAAM,EAAE3B,MAAA,CAAA4B,iBAAiB;IAC/EC,WAAU,EAAE7B,MAAA,CAAA4B,iBAAiB;IAAGE,YAAW,EAAE9B,MAAA,CAAA+B,gBAAgB;IAAG,wBAAsB,EAAE/B,MAAA,CAAAgC;;IAVjGC,OAAA,EAAArB,QAAA,CAWQ;MAAA,OAAuE,CAAvEf,YAAA,CAAuEqC,0BAAA;QAAtDC,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/DxC,YAAA,CAqBmByC,2BAAA;QArBA9B,SAAS,EAAER,MAAA,CAAAQ,SAAS;QAAG+B,YAAU,EAAEvC,MAAA,CAAAwC,gBAAgB;QACnEC,SAAS,EAAE;;QACDC,iBAAiB,EAAA9B,QAAA,CAGnB,UAKA+B,KAR0B;UAAA,IAAAC,qBAAA,EAAAC,qBAAA;UAAA,QACjBF,KAAK,CAACG,GAAG,CAACJ,iBAAiB,IAAIC,KAAK,CAACG,GAAG,CAACJ,iBAAiB,CAACK,MAAM,Q,cAAjFpD,mBAAA,CAEWqD,SAAA;YAjBvBC,GAAA;UAAA,IAAAC,gBAAA,CAAAC,gBAAA,EAAAP,qBAAA,GAgBiBD,KAAK,CAACG,GAAG,CAACJ,iBAAiB,cAAAE,qBAAA,uBAA3BA,qBAAA,CAA6BQ,GAAG,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,oBAAoB;UAAA,GAAEC,IAAI,sB,8CAEvE5D,mBAAA,CAEWqD,SAAA;YApBvBC,GAAA;UAAA,IAAAC,gBAAA,CAAAC,gBAAA,EAAAN,qBAAA,GAmBiBF,KAAK,CAACG,GAAG,CAACU,oBAAoB,cAAAX,qBAAA,uBAA9BA,qBAAA,CAAgCO,GAAG,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,oBAAoB;UAAA,GAAEC,IAAI,sB;;QAGjEE,mBAAmB,EAAA7C,QAAA,CAKrC,UAEwG+B,KAP5D;UAAA,IAAAe,qBAAA,EAAAC,sBAAA;UAAA,QACnBhB,KAAK,CAACG,GAAG,CAACW,mBAAmB,IAAId,KAAK,CAACG,GAAG,CAACW,mBAAmB,CAACV,MAAM,Q,cAArFpD,mBAAA,CAEWqD,SAAA;YAzBvBC,GAAA;UAAA,IAAAC,gBAAA,CAAAC,gBAAA,EAAAO,qBAAA,GAwBiBf,KAAK,CAACG,GAAG,CAACW,mBAAmB,cAAAC,qBAAA,uBAA7BA,qBAAA,CAA+BN,GAAG,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,oBAAoB;UAAA,GAAEC,IAAI,sB,8CAEzE5D,mBAAA,CAEWqD,SAAA;YA5BvBC,GAAA;UAAA,IAAAC,gBAAA,CAAAC,gBAAA,EAAAQ,sBAAA,GA2BiBhB,KAAK,CAACG,GAAG,CAACc,kBAAkB,cAAAD,sBAAA,uBAA5BA,sBAAA,CAA8BP,GAAG,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,oBAAoB;UAAA,GAAEC,IAAI,sB;;QA3BpFjC,CAAA;wCAkCQzB,YAAA,CAA6FgE,kCAAA;QAAnEC,mBAAmB,EAAE9D,MAAA,CAAA+D;MAAkB,iD;;IAlCzEzC,CAAA;sGAqCIC,mBAAA,CAIM,OAJNyC,UAIM,GAHJnE,YAAA,CAE+BoE,wBAAA;IAFRC,WAAW,EAAElE,MAAA,CAAAmE,MAAM;IAtChD,wBAAAnD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAsC0CjB,MAAA,CAAAmE,MAAM,GAAAlD,MAAA;IAAA;IAAU,WAAS,EAAEjB,MAAA,CAAAoE,QAAQ;IAtC7E,qBAAApD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAsCqEjB,MAAA,CAAAoE,QAAQ,GAAAnD,MAAA;IAAA;IAAG,YAAU,EAAEjB,MAAA,CAAAqE,SAAS;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAEvE,MAAA,CAAAC,WAAW;IAAGuE,eAAc,EAAExE,MAAA,CAAAC,WAAW;IACvGwE,KAAK,EAAEzE,MAAA,CAAA0E,MAAM;IAAEC,UAAU,EAAV;qHAEpB9E,YAAA,CAEmB+E,2BAAA;IA5CvB9D,UAAA,EA0C+Bd,MAAA,CAAA6E,SAAS;IA1CxC,uBAAA7D,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA0C+BjB,MAAA,CAAA6E,SAAS,GAAA5D,MAAA;IAAA;IAAE6D,IAAI,EAAC;;IA1C/C7C,OAAA,EAAArB,QAAA,CA2CM;MAAA,OAA4G,CAA5Gf,YAAA,CAA4GG,MAAA;QAArF+E,SAAS,EAAE,KAAK;QAAGC,SAAS,EAAEhF,MAAA,CAAAgF,SAAS;QAAGC,UAAQ,EAAEjF,MAAA,CAAAkF;;;IA3CjF5D,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}