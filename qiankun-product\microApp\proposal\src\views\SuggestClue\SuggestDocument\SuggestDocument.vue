<template>
  <div class="SuggestDocument">
    <div class="globalTable">
      <el-table ref="tableRef"
                row-key="id"
                :data="tableData"
                @select="handleTableSelect"
                @select-all="handleTableSelect"
                @sort-change="handleSortChange"
                :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection"
                         reserve-selection
                         width="60"
                         fixed />
        <el-table-column prop="title"
                         label="文案类标题"
                         width="280" />
        <el-table-column prop="content"
                         label="文案简介"
                         width="725" />
        <el-table-column prop="userName"
                         label="修改人"
                         width="180" />
        <el-table-column label="修改时间"
                         width="280">
          <template #default="scope">{{ format(scope.row.createDate) }}</template>
        </el-table-column>
        <el-table-column width="120"
                         fixed="right"
                         class-name="globalTableCustom">
          <template #header>操作</template>
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)"
                       type="primary"
                       plain>编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo"
                     v-model:page-size="pageSize"
                     :page-sizes="pageSizes"
                     layout="total, sizes, prev, pager, next, jumper"
                     @size-change="handleQuery"
                     @current-change="handleQuery"
                     :total="totals"
                     background />
    </div>
    <xyl-popup-window v-model="show"
                      :name="id ? '编辑文案' : '新增文案'">
      <SuggestDocumentSubmit :id="id"
                             @callback="callback"></SuggestDocumentSubmit>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestDocument' }
</script>
<script setup>
import { ref, onActivated } from 'vue'
import { format } from 'common/js/time.js'
import { GlobalTable } from 'common/js/GlobalTable.js'
import SuggestDocumentSubmit from './components/SuggestDocumentSubmit'
import { user } from 'common/js/system_var.js'
const show = ref(false)
const id = ref('')
const {
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  handleQuery,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
} = GlobalTable({ tableApi: 'proposalClueTheme', })

onActivated(() => { handleQuery(); userLog() })
const handleEdit = (item) => {
  id.value = item.id
  show.value = true
}

const userLog = (() => {
  console.log(user.value)
})

const callback = () => {
  handleQuery()
  show.value = false
}


</script>
<style lang="scss">
.SuggestDocument {
  width: 100%;
  height: 100%;
  padding: 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - 20px);
  }
}
</style>
