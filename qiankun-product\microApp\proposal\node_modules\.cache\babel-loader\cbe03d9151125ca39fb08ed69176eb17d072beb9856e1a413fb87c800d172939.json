{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON>s as _withKeys, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestDraftBox\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table = _resolveComponent(\"xyl-global-table\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_export_excel = _resolveComponent(\"xyl-export-excel\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList,\n    data: $setup.tableHead,\n    ref: \"queryRef\"\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\", \"data\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect,\n    onSortChange: $setup.handleSortChange,\n    \"header-cell-class-name\": $setup.handleHeaderClass\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_xyl_global_table, {\n        tableHead: $setup.tableHead,\n        onTableClick: $setup.handleTableClick\n      }, null, 8 /* PROPS */, [\"tableHead\"]), _createVNode(_component_xyl_global_table_button, {\n        data: $setup.tableButtonList,\n        onButtonClick: $setup.handleCommand,\n        editCustomTableHead: $setup.handleEditorCustom\n      }, null, 8 /* PROPS */, [\"editCustomTableHead\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\", \"onSortChange\", \"header-cell-class-name\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.exportShow,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.exportShow = $event;\n    }),\n    name: \"导出Excel\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_xyl_export_excel, {\n        name: \"提案草稿箱\",\n        exportId: $setup.exportId,\n        params: $setup.exportParams,\n        module: \"proposalExportExcel\",\n        tableId: \"id_prop_proposal_saveDraft\",\n        onExcelCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"exportId\", \"params\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "data", "tableHead", "ref", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "onSortChange", "handleSortChange", "handleHeaderClass", "default", "_component_el_table_column", "type", "width", "fixed", "_component_xyl_global_table", "onTableClick", "handleTableClick", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "handleCommand", "editCustomTableHead", "handleEditorCustom", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "exportShow", "name", "_component_xyl_export_excel", "exportId", "params", "exportParams", "module", "tableId", "onExcelCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SuggestDraftBox\\SuggestDraftBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestDraftBox\">\r\n    <xyl-search-button\r\n      @queryClick=\"handleQuery\"\r\n      @resetClick=\"handleReset\"\r\n      @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\"\r\n      :data=\"tableHead\"\r\n      ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table\r\n        ref=\"tableRef\"\r\n        row-key=\"id\"\r\n        :data=\"tableData\"\r\n        @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\"\r\n        @sort-change=\"handleSortChange\"\r\n        :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\"></xyl-global-table>\r\n        <xyl-global-table-button\r\n          :data=\"tableButtonList\"\r\n          @buttonClick=\"handleCommand\"\r\n          :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination\r\n        v-model:currentPage=\"pageNo\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleQuery\"\r\n        @current-change=\"handleQuery\"\r\n        :total=\"totals\"\r\n        background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\" name=\"导出Excel\">\r\n      <xyl-export-excel\r\n        name=\"提案草稿箱\"\r\n        :exportId=\"exportId\"\r\n        :params=\"exportParams\"\r\n        module=\"proposalExportExcel\"\r\n        tableId=\"id_prop_proposal_saveDraft\"\r\n        @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestDraftBox' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nconst buttonList = [\r\n  { id: 'next', name: '批量提交', type: 'primary', has: '' },\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' },\r\n  { id: 'del', name: '删除', type: 'primary', has: '' }\r\n]\r\nconst tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: '' }]\r\nconst route = useRoute()\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  tableDataArray,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel,\r\n  tableQuery\r\n} = GlobalTable({ tableId: 'id_prop_proposal_saveDraft', tableApi: 'suggestionList', delApi: 'suggestionDel' })\r\n\r\nonActivated(() => {\r\n  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))\r\n  if (suggestIds) {\r\n    tableQuery.value.ids = suggestIds\r\n    handleQuery()\r\n    setTimeout(() => {\r\n      sessionStorage.removeItem('suggestIds')\r\n      tableQuery.value.ids = []\r\n    }, 1000)\r\n  } else {\r\n    handleQuery()\r\n  }\r\n})\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'next':\r\n      handleNext()\r\n      break\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams())\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    case 'del':\r\n      handleDel('提案')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } }\r\n  })\r\n}\r\nconst handleEdit = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '编辑提案', path: '/proposal/SubmitSuggest', query: { id: item.id, type: 'draft' } }\r\n  })\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  exportShow.value = false\r\n}\r\nconst handleNext = () => {\r\n  if (tableDataArray.value.length) {\r\n    ElMessageBox.confirm('此操作会将当前选中的草稿提案提交, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        suggestionBatchComplete()\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消提交' })\r\n      })\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n  }\r\n}\r\nconst suggestionBatchComplete = async () => {\r\n  const { code } = await api.suggestionBatchComplete({\r\n    suggestionIds: tableDataArray.value.map((v) => v.id),\r\n    nextNodeId: route.query.nextNode || 'prepareVerify'\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '提交成功' })\r\n    tableRefReset()\r\n    handleQuery()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestDraftBox {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAYrBA,KAAK,EAAC;AAAa;;EAiBnBA,KAAK,EAAC;AAAkB;;;;;;;;;;;uBA7B/BC,mBAAA,CAiDM,OAjDNC,UAiDM,GAhDJC,YAAA,CAUoBC,4BAAA;IATjBC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IACvBC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IACvBC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC1BC,UAAU,EAAEN,MAAA,CAAAM,UAAU;IACtBC,IAAI,EAAEP,MAAA,CAAAQ,SAAS;IAChBC,GAAG,EAAC;;IACOC,MAAM,EAAAC,QAAA,CACf;MAAA,OAAwF,CAAxFd,YAAA,CAAwFe,mBAAA;QAVhGC,UAAA,EAU2Bb,MAAA,CAAAc,OAAO;QAVlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAU2BhB,MAAA,CAAAc,OAAO,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAV/DC,SAAA,CAUuEnB,MAAA,CAAAC,WAAW;QAAEmB,SAAS,EAAT;;;IAVpFC,CAAA;+CAaIC,mBAAA,CAgBM,OAhBNC,UAgBM,GAfJ1B,YAAA,CAcW2B,mBAAA;IAbTf,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,IAAI;IACXF,IAAI,EAAEP,MAAA,CAAAyB,SAAS;IACfC,QAAM,EAAE1B,MAAA,CAAA2B,iBAAiB;IACzBC,WAAU,EAAE5B,MAAA,CAAA2B,iBAAiB;IAC7BE,YAAW,EAAE7B,MAAA,CAAA8B,gBAAgB;IAC7B,wBAAsB,EAAE9B,MAAA,CAAA+B;;IArBjCC,OAAA,EAAArB,QAAA,CAsBQ;MAAA,OAAuE,CAAvEd,YAAA,CAAuEoC,0BAAA;QAAtDC,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/DvC,YAAA,CAA2FwC,2BAAA;QAAxE7B,SAAS,EAAER,MAAA,CAAAQ,SAAS;QAAG8B,YAAU,EAAEtC,MAAA,CAAAuC;8CACtD1C,YAAA,CAGsE2C,kCAAA;QAFnEjC,IAAI,EAAEP,MAAA,CAAAyC,eAAe;QACrBC,aAAW,EAAE1C,MAAA,CAAA2C,aAAa;QAC1BC,mBAAmB,EAAE5C,MAAA,CAAA6C;;;IA3BhCxB,CAAA;sGA8BIC,mBAAA,CAUM,OAVNwB,UAUM,GATJjD,YAAA,CAQekD,wBAAA;IAPLC,WAAW,EAAEhD,MAAA,CAAAiD,MAAM;IAhCnC,wBAAAlC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAgC6BhB,MAAA,CAAAiD,MAAM,GAAAjC,MAAA;IAAA;IACnB,WAAS,EAAEhB,MAAA,CAAAkD,QAAQ;IAjCnC,qBAAAnC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAiC2BhB,MAAA,CAAAkD,QAAQ,GAAAlC,MAAA;IAAA;IAC1B,YAAU,EAAEhB,MAAA,CAAAmD,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAErD,MAAA,CAAAC,WAAW;IACxBqD,eAAc,EAAEtD,MAAA,CAAAC,WAAW;IAC3BsD,KAAK,EAAEvD,MAAA,CAAAwD,MAAM;IACdC,UAAU,EAAV;qHAEJ5D,YAAA,CAQmB6D,2BAAA;IAjDvB7C,UAAA,EAyC+Bb,MAAA,CAAA2D,UAAU;IAzCzC,uBAAA5C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAyC+BhB,MAAA,CAAA2D,UAAU,GAAA3C,MAAA;IAAA;IAAE4C,IAAI,EAAC;;IAzChD5B,OAAA,EAAArB,QAAA,CA0CM;MAAA,OAM+C,CAN/Cd,YAAA,CAM+CgE,2BAAA;QAL7CD,IAAI,EAAC,OAAO;QACXE,QAAQ,EAAE9D,MAAA,CAAA8D,QAAQ;QAClBC,MAAM,EAAE/D,MAAA,CAAAgE,YAAY;QACrBC,MAAM,EAAC,qBAAqB;QAC5BC,OAAO,EAAC,4BAA4B;QACnCC,eAAa,EAAEnE,MAAA,CAAAoE;;;IAhDxB/C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}