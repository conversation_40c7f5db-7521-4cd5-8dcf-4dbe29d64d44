<template>
  <div class="SubmitSuggestReply">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm"
      @submit.enter.prevent>
      <el-form-item label="答复类型" prop="replyType">
        <el-select v-model="form.replyType" placeholder="请选择答复类型" clearable>
          <el-option v-for="item in replyType" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否公开" prop="isOpen">
        <el-radio-group v-model="form.isOpen" @change="isOpenChange">
          <el-radio :label="1">公开</el-radio>
          <el-radio :label="0">不公开</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="不公开理由" prop="noOpenReason" v-show="!form.isOpen" class="globalFormTitle">
        <el-input v-model="form.noOpenReason" placeholder="请输入不公开理由" type="textarea" :rows="5" clearable />
      </el-form-item>
      <el-form-item label="提案评价" prop="evaluate" class="globalFormTitle">
        <el-radio-group v-model="form.evaluate">
          <el-radio :label="1">优秀</el-radio>
          <el-radio :label="2">良好</el-radio>
          <el-radio :label="3">一般</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="征询意见表满意度" prop="satisfaction" class="globalFormTitle">
        <el-radio-group v-model="form.satisfaction">
          <el-radio :label="1">满意</el-radio>
          <el-radio :label="2">基本满意</el-radio>
          <el-radio :label="3">不满意</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上传答复件红头文件（pdf）" prop="attachmentsRed" class="globalFormTitle">
        <xyl-upload-file :fileType="['pdf']" :fileData="attachmentsRed" @fileUpload="fileUploadRed" />
      </el-form-item>
      <el-form-item label="上传答复件word文件" prop="fileData" class="globalFormTitle">
        <xyl-upload-file :fileType="['doc', 'docx']" :fileData="fileData" @fileUpload="fileUpload" />
      </el-form-item>
      <el-form-item label="上传意见征询标扫描件pdf（委员签字、单位盖章）" prop="attachmentsopinion" class="globalFormTitle">
        <xyl-upload-file :fileType="['pdf']" :fileData="attachmentsopinion" @fileUpload="fileUploadOpinion" />
      </el-form-item>

      <!-- <el-form-item label="内容"
                    prop="content"
                    class="globalFormTitle">
        <TinyMceEditor v-model="form.content" />
      </el-form-item> -->
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SubmitSuggestReply' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({
  id: { type: String, default: '' },
  unitId: { type: String, default: '' },
  suggestId: { type: String, default: '' },
  detailsObjectType: { type: String, default: 'handlingPortionId' }
})

const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  id: '',
  replyType: '',
  isOpen: 1,
  noOpenReason: '',
  attachmentsRed: [],
  attachmentsopinion: [],
  fileData: [],
  content: '',
  evaluate: '',
  satisfaction: ''
})
const rules = reactive({
  replyType: [{ required: true, message: '请选择答复类型', trigger: ['blur', 'change'] }],
  isOpen: [{ required: true, message: '请选择是否公开', trigger: ['blur', 'change'] }],
  evaluate: [{ required: true, message: '请选择提案评价', trigger: ['blur', 'change'] }],
  satisfaction: [{ required: true, message: '请选择满意度', trigger: ['blur', 'change'] }],
  attachmentsRed: [{ required: true, message: '请上传答复件红头文件', trigger: ['blur', 'change'] }],
  fileData: [{ required: true, message: '请上传答复件红头文件', trigger: ['blur', 'change'] }],
  attachmentsopinion: [{ required: true, message: '请上传意见征询标扫描件', trigger: ['blur', 'change'] }],
  noOpenReason: [{ required: false, message: '请输入不公开理由', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '请输入内容', trigger: ['blur', 'change'] }]
})
const attachmentsRed = ref([])
const attachmentsopinion = ref([])
const details = ref({})
const fileData = ref([])
const replyType = ref([])

onMounted(() => {
  dictionaryData()
  if (props.id) {
    handingPortionAnswerInfo()
  }
  suggestionInfo()
})

const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['suggestion_answer_type'] })
  var { data } = res
  replyType.value = data.suggestion_answer_type
}
const isOpenChange = () => {
  rules.noOpenReason = [{ required: false, message: '请输入不公开理由', trigger: ['blur', 'change'] }]
  if (!form.isOpen) {
    rules.noOpenReason = [{ required: true, message: '请输入不公开理由', trigger: ['blur', 'change'] }]
  }
}
const fileUpload = (file) => {
  form.fileData = file.map((v) => v.id)
  fileData.value = file
  formRef.value.validateField('fileData')
}
const suggestionInfo = async () => {
  const { data } = await api.suggestionInfo({ detailId: props.suggestId })
  details.value = data
}
const fileUploadRed = (file) => {
  form.attachmentsRed = file.map(v => v.id)
  attachmentsRed.value = file
  formRef.value.validateField('attachmentsRed')
}
const fileUploadOpinion = (file) => {
  form.attachmentsopinion = file.map(v => v.id)
  attachmentsopinion.value = file
  formRef.value.validateField('attachmentsopinion')
}
const handingPortionAnswerInfo = async () => {
  var params = {}
  params[props.detailsObjectType] = props.id
  const res = await api.handingPortionAnswerInfo(params)
  var { data, extData } = res
  form.id = data.id
  form.replyType = data.suggestionAnswerType?.value
  form.isOpen = data.isOpen
  form.noOpenReason = data.noOpenReason
  form.evaluate = Number(data.jordan)
  form.satisfaction = Number(data.james)
  // form.content = data.content
  fileData.value = data.attachments || []
  form.fileData = data.attachments.map((v) => v.id)
  attachmentsRed.value = extData.answersFileExt.attachmentsRed || []
  form.attachmentsRed = extData.answersFileExt.attachmentsRed.map(v => v.id)
  attachmentsopinion.value = extData.answersFileExt.attachmentsOpinion || []
  form.attachmentsopinion = extData.answersFileExt.attachmentsOpinion.map(v => v.id)
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(
    props.id ? '/cppcc/handingPortionAnswer/edit' : '/cppcc/handingPortionAnswer/add',
    {
      form: {
        id: form.id,
        handlingPortionId: props.unitId,
        suggestionId: props.suggestId,
        suggestionAnswerType: form.replyType,
        isOpen: form.isOpen,
        noOpenReason: form.isOpen ? '' : form.noOpenReason,
        jordan: form.evaluate,
        james: form.satisfaction,
        // content: form.content,
        attachmentIds: fileData.value.map(v => v.id),
        kobe: attachmentsRed.value.map(v => v.id).join(','),
        duncan: attachmentsopinion.value.map(v => v.id).join(',')
      }
    }
  )
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback', true)
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.SubmitSuggestReply {
  width: 990px;
}
</style>
