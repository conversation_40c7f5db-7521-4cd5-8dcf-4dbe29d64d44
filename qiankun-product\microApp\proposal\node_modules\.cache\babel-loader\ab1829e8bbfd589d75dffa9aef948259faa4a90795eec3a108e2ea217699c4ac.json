{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, with<PERSON><PERSON><PERSON> as _withKeys, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"UnitSuggestAdvanceAssign\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"SuggestUnitPopperText\"\n};\nvar _hoisted_4 = {\n  class: \"SuggestUnitPopperText\"\n};\nvar _hoisted_5 = {\n  class: \"SuggestUnitPopperText\"\n};\nvar _hoisted_6 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_xyl_label_item = _resolveComponent(\"xyl-label-item\");\n  var _component_xyl_label = _resolveComponent(\"xyl-label\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  var _component_xyl_global_table = _resolveComponent(\"xyl-global-table\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_export_excel = _resolveComponent(\"xyl-export-excel\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _directive_copy = _resolveDirective(\"copy\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_label, {\n    modelValue: $setup.labelId,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.labelId = $event;\n    }),\n    onLabelClick: $setup.handleLabel\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.labelList, function (item) {\n        return _openBlock(), _createBlock(_component_xyl_label_item, {\n          key: item.itemCode,\n          value: item.itemCode\n        }, {\n          default: _withCtx(function () {\n            return [_createTextVNode(_toDisplayString(item.itemName) + \"（\" + _toDisplayString(item.count) + \"） \", 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList,\n    data: $setup.tableHead,\n    ref: \"queryRef\"\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\", \"buttonList\", \"data\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect,\n    onSortChange: $setup.handleSortChange,\n    \"header-cell-class-name\": $setup.handleHeaderClass\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_xyl_global_table, {\n        tableHead: $setup.tableHead,\n        onTableClick: $setup.handleTableClick,\n        noTooltip: ['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\n      }, {\n        mainHandleOffices: _withCtx(function (scope) {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(scope.row.mainHandleOffices, function (item) {\n            return _openBlock(), _createBlock(_component_el_popover, {\n              placement: \"top-start\",\n              key: item.id,\n              disabled: item.users == null,\n              \"popper-class\": \"SuggestUnitPopper\"\n            }, {\n              reference: _withCtx(function () {\n                return [_createElementVNode(\"span\", {\n                  style: _normalizeStyle($setup.colorObj(item.suggestionHandleStatus, item.users == null))\n                }, _toDisplayString(item.flowHandleOfficeName), 5 /* TEXT, STYLE */)];\n              }),\n              default: _withCtx(function () {\n                return [_createElementVNode(\"div\", {\n                  style: _normalizeStyle($setup.colorObj(item.suggestionHandleStatus, item.users == null)),\n                  class: \"SuggestUnitPopperName\"\n                }, _toDisplayString(item.flowHandleOfficeName) + \"【\" + _toDisplayString(item.suggestionHandleStatusName) + \"】 \", 5 /* TEXT, STYLE */), _createElementVNode(\"div\", _hoisted_3, [_cache[5] || (_cache[5] = _createTextVNode(\" 经办人： \")), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.users, function (items, index) {\n                  return _withDirectives((_openBlock(), _createElementBlock(\"span\", {\n                    key: items.id\n                  }, [_createTextVNode(_toDisplayString(index == 0 ? '' : '，') + _toDisplayString(items.userName) + \"（\" + _toDisplayString(items.mobile) + \"） \", 1 /* TEXT */)])), [[_directive_copy, items.mobile]]);\n                }), 128 /* KEYED_FRAGMENT */))])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        assistHandleOffices: _withCtx(function (scope) {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(scope.row.assistHandleOffices, function (item, i) {\n            return _openBlock(), _createBlock(_component_el_popover, {\n              placement: \"top-start\",\n              key: item.id,\n              disabled: item.users == null,\n              \"popper-class\": \"SuggestUnitPopper\"\n            }, {\n              reference: _withCtx(function () {\n                return [_createElementVNode(\"span\", {\n                  style: _normalizeStyle($setup.colorObj(item.suggestionHandleStatus, item.users == null))\n                }, _toDisplayString(i == 0 ? '' : '，') + _toDisplayString(item.flowHandleOfficeName), 5 /* TEXT, STYLE */)];\n              }),\n              default: _withCtx(function () {\n                return [_createElementVNode(\"div\", {\n                  style: _normalizeStyle($setup.colorObj(item.suggestionHandleStatus, item.users == null)),\n                  class: \"SuggestUnitPopperName\"\n                }, _toDisplayString(item.flowHandleOfficeName) + \"【\" + _toDisplayString(item.suggestionHandleStatusName) + \"】 \", 5 /* TEXT, STYLE */), _createElementVNode(\"div\", _hoisted_4, [_cache[6] || (_cache[6] = _createTextVNode(\" 经办人： \")), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.users, function (items, index) {\n                  return _withDirectives((_openBlock(), _createElementBlock(\"span\", {\n                    key: items.id\n                  }, [_createTextVNode(_toDisplayString(index == 0 ? '' : '，') + _toDisplayString(items.userName) + \"（\" + _toDisplayString(items.mobile) + \"） \", 1 /* TEXT */)])), [[_directive_copy, items.mobile]]);\n                }), 128 /* KEYED_FRAGMENT */))])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        publishHandleOffices: _withCtx(function (scope) {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(scope.row.publishHandleOffices, function (item, i) {\n            return _openBlock(), _createBlock(_component_el_popover, {\n              placement: \"top-start\",\n              key: item.id,\n              disabled: item.users == null,\n              \"popper-class\": \"SuggestUnitPopper\"\n            }, {\n              reference: _withCtx(function () {\n                return [_createElementVNode(\"span\", {\n                  style: _normalizeStyle($setup.colorObj(item.suggestionHandleStatus, item.users == null))\n                }, _toDisplayString(i == 0 ? '' : '，') + _toDisplayString(item.flowHandleOfficeName), 5 /* TEXT, STYLE */)];\n              }),\n              default: _withCtx(function () {\n                return [_createElementVNode(\"div\", {\n                  style: _normalizeStyle($setup.colorObj(item.suggestionHandleStatus, item.users == null)),\n                  class: \"SuggestUnitPopperName\"\n                }, _toDisplayString(item.flowHandleOfficeName) + \"【\" + _toDisplayString(item.suggestionHandleStatusName) + \"】 \", 5 /* TEXT, STYLE */), _createElementVNode(\"div\", _hoisted_5, [_cache[7] || (_cache[7] = _createTextVNode(\" 经办人： \")), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.users, function (items, index) {\n                  return _withDirectives((_openBlock(), _createElementBlock(\"span\", {\n                    key: items.id\n                  }, [_createTextVNode(_toDisplayString(index == 0 ? '' : '，') + _toDisplayString(items.userName) + \"（\" + _toDisplayString(items.mobile) + \"） \", 1 /* TEXT */)])), [[_directive_copy, items.mobile]]);\n                }), 128 /* KEYED_FRAGMENT */))])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"tableHead\"]), _createVNode(_component_xyl_global_table_button, {\n        label: \"\",\n        editCustomTableHead: $setup.handleEditorCustom\n      }, null, 8 /* PROPS */, [\"editCustomTableHead\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\", \"onSortChange\", \"header-cell-class-name\"])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.exportShow,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.exportShow = $event;\n    }),\n    name: \"导出Excel\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_xyl_export_excel, {\n        name: \"预交办提案\",\n        exportId: $setup.exportId,\n        params: $setup.exportParams,\n        module: \"proposalExportExcel\",\n        tableId: \"id_prop_proposal_company_pre_assign\",\n        onExcelCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"exportId\", \"params\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_label", "modelValue", "$setup", "labelId", "_cache", "$event", "onLabelClick", "handleLabel", "default", "_withCtx", "_Fragment", "_renderList", "labelList", "item", "_createBlock", "_component_xyl_label_item", "key", "itemCode", "value", "_createTextVNode", "_toDisplayString", "itemName", "count", "_", "_component_xyl_search_button", "onQueryClick", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "data", "tableHead", "ref", "search", "_component_el_input", "keyword", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_createElementVNode", "_hoisted_2", "_component_el_table", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "onSortChange", "handleSortChange", "handleHeaderClass", "_component_el_table_column", "type", "width", "fixed", "_component_xyl_global_table", "onTableClick", "handleTableClick", "noTooltip", "mainHandleOffices", "scope", "row", "_component_el_popover", "placement", "id", "disabled", "users", "reference", "style", "_normalizeStyle", "colorObj", "suggestionHandleStatus", "flowHandleOfficeName", "suggestionHandleStatusName", "_hoisted_3", "items", "index", "userName", "mobile", "assistHandleOffices", "i", "_hoisted_4", "publishHandleOffices", "_hoisted_5", "_component_xyl_global_table_button", "label", "editCustomTableHead", "handleEditorCustom", "_hoisted_6", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "exportShow", "name", "_component_xyl_export_excel", "exportId", "params", "exportParams", "module", "tableId", "onExcelCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestAdvanceAssign\\UnitSuggestAdvanceAssign.vue"], "sourcesContent": ["<template>\r\n  <div class=\"UnitSuggestAdvanceAssign\">\r\n    <xyl-label v-model=\"labelId\" @labelClick=\"handleLabel\">\r\n      <xyl-label-item v-for=\"item in labelList\" :key=\"item.itemCode\" :value=\"item.itemCode\">\r\n        {{ item.itemName }}（{{ item.count }}）\r\n      </xyl-label-item>\r\n    </xyl-label>\r\n    <xyl-search-button\r\n      @queryClick=\"handleQuery\"\r\n      @resetClick=\"handleReset\"\r\n      @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\"\r\n      :data=\"tableHead\"\r\n      ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table\r\n        ref=\"tableRef\"\r\n        row-key=\"id\"\r\n        :data=\"tableData\"\r\n        @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\"\r\n        @sort-change=\"handleSortChange\"\r\n        :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table\r\n          :tableHead=\"tableHead\"\r\n          @tableClick=\"handleTableClick\"\r\n          :noTooltip=\"['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\">\r\n          <template #mainHandleOffices=\"scope\">\r\n            <el-popover\r\n              placement=\"top-start\"\r\n              v-for=\"item in scope.row.mainHandleOffices\"\r\n              :key=\"item.id\"\r\n              :disabled=\"item.users == null\"\r\n              popper-class=\"SuggestUnitPopper\">\r\n              <div :style=\"colorObj(item.suggestionHandleStatus, item.users == null)\" class=\"SuggestUnitPopperName\">\r\n                {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】\r\n              </div>\r\n              <div class=\"SuggestUnitPopperText\">\r\n                经办人：\r\n                <span v-for=\"(items, index) in item.users\" v-copy=\"items.mobile\" :key=\"items.id\">\r\n                  {{ index == 0 ? '' : '，' }}{{ items.userName }}（{{ items.mobile }}）\r\n                </span>\r\n              </div>\r\n              <template #reference>\r\n                <span :style=\"colorObj(item.suggestionHandleStatus, item.users == null)\">\r\n                  {{ item.flowHandleOfficeName }}\r\n                </span>\r\n              </template>\r\n            </el-popover>\r\n          </template>\r\n          <template #assistHandleOffices=\"scope\">\r\n            <el-popover\r\n              placement=\"top-start\"\r\n              v-for=\"(item, i) in scope.row.assistHandleOffices\"\r\n              :key=\"item.id\"\r\n              :disabled=\"item.users == null\"\r\n              popper-class=\"SuggestUnitPopper\">\r\n              <div :style=\"colorObj(item.suggestionHandleStatus, item.users == null)\" class=\"SuggestUnitPopperName\">\r\n                {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】\r\n              </div>\r\n              <div class=\"SuggestUnitPopperText\">\r\n                经办人：\r\n                <span v-for=\"(items, index) in item.users\" v-copy=\"items.mobile\" :key=\"items.id\">\r\n                  {{ index == 0 ? '' : '，' }}{{ items.userName }}（{{ items.mobile }}）\r\n                </span>\r\n              </div>\r\n              <template #reference>\r\n                <span :style=\"colorObj(item.suggestionHandleStatus, item.users == null)\">\r\n                  {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}\r\n                </span>\r\n              </template>\r\n            </el-popover>\r\n          </template>\r\n          <template #publishHandleOffices=\"scope\">\r\n            <el-popover\r\n              placement=\"top-start\"\r\n              v-for=\"(item, i) in scope.row.publishHandleOffices\"\r\n              :key=\"item.id\"\r\n              :disabled=\"item.users == null\"\r\n              popper-class=\"SuggestUnitPopper\">\r\n              <div :style=\"colorObj(item.suggestionHandleStatus, item.users == null)\" class=\"SuggestUnitPopperName\">\r\n                {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】\r\n              </div>\r\n              <div class=\"SuggestUnitPopperText\">\r\n                经办人：\r\n                <span v-for=\"(items, index) in item.users\" v-copy=\"items.mobile\" :key=\"items.id\">\r\n                  {{ index == 0 ? '' : '，' }}{{ items.userName }}（{{ items.mobile }}）\r\n                </span>\r\n              </div>\r\n              <template #reference>\r\n                <span :style=\"colorObj(item.suggestionHandleStatus, item.users == null)\">\r\n                  {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}\r\n                </span>\r\n              </template>\r\n            </el-popover>\r\n          </template>\r\n        </xyl-global-table>\r\n        <xyl-global-table-button label=\"\" :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination\r\n        v-model:currentPage=\"pageNo\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleQuery\"\r\n        @current-change=\"handleQuery\"\r\n        :total=\"totals\"\r\n        background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\" name=\"导出Excel\">\r\n      <xyl-export-excel\r\n        name=\"预交办提案\"\r\n        :exportId=\"exportId\"\r\n        :params=\"exportParams\"\r\n        module=\"proposalExportExcel\"\r\n        tableId=\"id_prop_proposal_company_pre_assign\"\r\n        @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'UnitSuggestAdvanceAssign' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nconst buttonList = ref([\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n])\r\nconst labelId = ref('')\r\nconst labelList = ref([])\r\nconst isOpenAutoRead = ref(false)\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel,\r\n  tableQuery,\r\n  tableDataArray\r\n} = GlobalTable({ tableId: 'id_prop_proposal_company_pre_assign', tableApi: 'suggestionList' })\r\n\r\nonActivated(() => {\r\n  suggestionCountSelector()\r\n  globalReadConfig()\r\n})\r\n\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: ['proposal_office_download_read'] })\r\n  if (data.proposal_office_download_read) {\r\n    isOpenAutoRead.value = data.proposal_office_download_read === 'true' ? true : false\r\n  } else {\r\n    isOpenAutoRead.value = false\r\n  }\r\n}\r\n\r\nconst suggestionCountSelector = async () => {\r\n  const { data } = await api.suggestionCountSelector({ countItemType: 'office_pre_assign' })\r\n  labelId.value = data[0].itemCode\r\n  labelList.value = data\r\n  handleLabel()\r\n}\r\nconst handleLabel = () => {\r\n  tableQuery.value = { countItemCode: labelId.value || null }\r\n  if (labelId.value === 'no_receive') {\r\n    buttonList.value = [\r\n      { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n      { id: 'export', name: '导出Excel', type: 'primary', has: '' },\r\n      { id: 'sign', name: '批量签收', type: 'primary', has: '' }\r\n    ]\r\n  } else {\r\n    buttonList.value = [\r\n      { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n      { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n    ]\r\n  }\r\n  handleQuery()\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams(), isOpenAutoRead.value)\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    case 'sign':\r\n      handleSign()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n\r\nconst handleSign = () => {\r\n  if (tableDataArray.value.length) {\r\n    ElMessageBox.confirm('此操作会将当前选中的建议批量签收, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        handlingPortionBatchConfirm()\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消批量签收' })\r\n      })\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n  }\r\n}\r\n\r\nconst handlingPortionBatchConfirm = async () => {\r\n  const { code } = await api.handlingPortionBatchConfirm({\r\n    handlingPortionIds: tableDataArray.value.map((item) => item.currentLoginHandlingPortion.id)\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '签收成功' })\r\n    tableRefReset()\r\n    handleQuery()\r\n  }\r\n}\r\n\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: {\r\n      name: '建议详情',\r\n      path: '/proposal/SuggestDetail',\r\n      query: { id: item.id, type: labelId.value === 'no_receive' ? 'unitPreAssign' : '' }\r\n    }\r\n  })\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  suggestionCountSelector()\r\n  exportShow.value = false\r\n}\r\nconst colorObj = (state, type) => {\r\n  var color = { color: '#000' }\r\n  if (state === 'has_answer') {\r\n    color.color = '#4fcc72'\r\n  } else if (state === 'handling') {\r\n    color.color = '#fbd536'\r\n  } else if (state === 'apply_adjust') {\r\n    color.color = '#ca6063'\r\n  }\r\n  if (type) {\r\n    color = { color: '#000' }\r\n  }\r\n  return color\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.UnitSuggestAdvanceAssign {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - ((var(--zy-height) * 2) + (var(--zy-distance-four) * 4) + 42px));\r\n  }\r\n\r\n  .SuggestTimeIcon {\r\n    width: 12px;\r\n    height: 12px;\r\n    border-radius: 50%;\r\n    margin: auto;\r\n  }\r\n}\r\n\r\n.SuggestUnitPopper {\r\n  width: 500px !important;\r\n\r\n  .SuggestUnitPopperName {\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding-bottom: var(--zy-font-name-distance-five);\r\n  }\r\n\r\n  .SuggestUnitPopperText {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;EAiB9BA,KAAK,EAAC;AAAa;;EAwBTA,KAAK,EAAC;AAAuB;;EAuB7BA,KAAK,EAAC;AAAuB;;EAuB7BA,KAAK,EAAC;AAAuB;;EAiBvCA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;uBAxG/BC,mBAAA,CA4HM,OA5HNC,UA4HM,GA3HJC,YAAA,CAIYC,oBAAA;IANhBC,UAAA,EAEwBC,MAAA,CAAAC,OAAO;IAF/B,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAEwBH,MAAA,CAAAC,OAAO,GAAAE,MAAA;IAAA;IAAGC,YAAU,EAAEJ,MAAA,CAAAK;;IAF9CC,OAAA,EAAAC,QAAA,CAGsB;MAAA,OAAyB,E,kBAAzCZ,mBAAA,CAEiBa,SAAA,QALvBC,WAAA,CAGqCT,MAAA,CAAAU,SAAS,EAH9C,UAG6BC,IAAI;6BAA3BC,YAAA,CAEiBC,yBAAA;UAF0BC,GAAG,EAAEH,IAAI,CAACI,QAAQ;UAAGC,KAAK,EAAEL,IAAI,CAACI;;UAHlFT,OAAA,EAAAC,QAAA,CAIQ;YAAA,OAAmB,CAJ3BU,gBAAA,CAAAC,gBAAA,CAIWP,IAAI,CAACQ,QAAQ,IAAG,GAAC,GAAAD,gBAAA,CAAGP,IAAI,CAACS,KAAK,IAAG,IACtC,gB;;UALNC,CAAA;;;;IAAAA,CAAA;qCAOIxB,YAAA,CAUoByB,4BAAA;IATjBC,YAAU,EAAEvB,MAAA,CAAAwB,WAAW;IACvBC,YAAU,EAAEzB,MAAA,CAAA0B,WAAW;IACvBC,cAAY,EAAE3B,MAAA,CAAA4B,YAAY;IAC1BC,UAAU,EAAE7B,MAAA,CAAA6B,UAAU;IACtBC,IAAI,EAAE9B,MAAA,CAAA+B,SAAS;IAChBC,GAAG,EAAC;;IACOC,MAAM,EAAA1B,QAAA,CACf;MAAA,OAAwF,CAAxFV,YAAA,CAAwFqC,mBAAA;QAfhGnC,UAAA,EAe2BC,MAAA,CAAAmC,OAAO;QAflC,uBAAAjC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAe2BH,MAAA,CAAAmC,OAAO,GAAAhC,MAAA;QAAA;QAAEiC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAf/DC,SAAA,CAeuEtC,MAAA,CAAAwB,WAAW;QAAEe,SAAS,EAAT;;;IAfpFlB,CAAA;6DAkBImB,mBAAA,CAsFM,OAtFNC,UAsFM,GArFJ5C,YAAA,CAoFW6C,mBAAA;IAnFTV,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,IAAI;IACXF,IAAI,EAAE9B,MAAA,CAAA2C,SAAS;IACfC,QAAM,EAAE5C,MAAA,CAAA6C,iBAAiB;IACzBC,WAAU,EAAE9C,MAAA,CAAA6C,iBAAiB;IAC7BE,YAAW,EAAE/C,MAAA,CAAAgD,gBAAgB;IAC7B,wBAAsB,EAAEhD,MAAA,CAAAiD;;IA1BjC3C,OAAA,EAAAC,QAAA,CA2BQ;MAAA,OAAuE,CAAvEV,YAAA,CAAuEqD,0BAAA;QAAtDC,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/DxD,YAAA,CAyEmByD,2BAAA;QAxEhBvB,SAAS,EAAE/B,MAAA,CAAA+B,SAAS;QACpBwB,YAAU,EAAEvD,MAAA,CAAAwD,gBAAgB;QAC5BC,SAAS,EAAE;;QACDC,iBAAiB,EAAAnD,QAAA,CAGxB,UAA2CoD,KAHZ;UAAA,S,kBACjChE,mBAAA,CAoBaa,SAAA,QArDzBC,WAAA,CAmC6BkD,KAAK,CAACC,GAAG,CAACF,iBAAiB,EAnCxD,UAmCqB/C,IAAI;iCAFbC,YAAA,CAoBaiD,qBAAA;cAnBXC,SAAS,EAAC,WAAW;cAEpBhD,GAAG,EAAEH,IAAI,CAACoD,EAAE;cACZC,QAAQ,EAAErD,IAAI,CAACsD,KAAK;cACrB,cAAY,EAAC;;cAUFC,SAAS,EAAA3D,QAAA,CAClB;gBAAA,OAEO,CAFPiC,mBAAA,CAEO;kBAFA2B,KAAK,EAjD5BC,eAAA,CAiD8BpE,MAAA,CAAAqE,QAAQ,CAAC1D,IAAI,CAAC2D,sBAAsB,EAAE3D,IAAI,CAACsD,KAAK;oCACzDtD,IAAI,CAAC4D,oBAAoB,wB;;cAlD9CjE,OAAA,EAAAC,QAAA,CAuCc;gBAAA,OAEM,CAFNiC,mBAAA,CAEM;kBAFA2B,KAAK,EAvCzBC,eAAA,CAuC2BpE,MAAA,CAAAqE,QAAQ,CAAC1D,IAAI,CAAC2D,sBAAsB,EAAE3D,IAAI,CAACsD,KAAK;kBAAWvE,KAAK,EAAC;oCACzEiB,IAAI,CAAC4D,oBAAoB,IAAG,GAAC,GAAArD,gBAAA,CAAGP,IAAI,CAAC6D,0BAA0B,IAAG,IACvE,wBACAhC,mBAAA,CAKM,OALNiC,UAKM,G,0BA/CpBxD,gBAAA,CA0CiD,QAEjC,K,kBAAAtB,mBAAA,CAEOa,SAAA,QA9CvBC,WAAA,CA4C+CE,IAAI,CAACsD,KAAK,EA5CzD,UA4C8BS,KAAK,EAAEC,KAAK;wDAA1BhF,mBAAA,CAEO;oBAF2DmB,GAAG,EAAE4D,KAAK,CAACX;sBA5C7F9C,gBAAA,CAAAC,gBAAA,CA6CqByD,KAAK,oBAAAzD,gBAAA,CAAsBwD,KAAK,CAACE,QAAQ,IAAG,GAAC,GAAA1D,gBAAA,CAAGwD,KAAK,CAACG,MAAM,IAAG,IACpE,gB,uBAFmDH,KAAK,CAACG,MAAM,E;;;cA5C/ExD,CAAA;;;;QAuDqByD,mBAAmB,EAAAvE,QAAA,CAG1B,UAAkDoD,KAHjB;UAAA,S,kBACnChE,mBAAA,CAoBaa,SAAA,QA5EzBC,WAAA,CA0DkCkD,KAAK,CAACC,GAAG,CAACkB,mBAAmB,EA1D/D,UA0DsBnE,IAAI,EAAEoE,CAAC;iCAFjBnE,YAAA,CAoBaiD,qBAAA;cAnBXC,SAAS,EAAC,WAAW;cAEpBhD,GAAG,EAAEH,IAAI,CAACoD,EAAE;cACZC,QAAQ,EAAErD,IAAI,CAACsD,KAAK;cACrB,cAAY,EAAC;;cAUFC,SAAS,EAAA3D,QAAA,CAClB;gBAAA,OAEO,CAFPiC,mBAAA,CAEO;kBAFA2B,KAAK,EAxE5BC,eAAA,CAwE8BpE,MAAA,CAAAqE,QAAQ,CAAC1D,IAAI,CAAC2D,sBAAsB,EAAE3D,IAAI,CAACsD,KAAK;oCACzDc,CAAC,oBAAA7D,gBAAA,CAAsBP,IAAI,CAAC4D,oBAAoB,wB;;cAzErEjE,OAAA,EAAAC,QAAA,CA8Dc;gBAAA,OAEM,CAFNiC,mBAAA,CAEM;kBAFA2B,KAAK,EA9DzBC,eAAA,CA8D2BpE,MAAA,CAAAqE,QAAQ,CAAC1D,IAAI,CAAC2D,sBAAsB,EAAE3D,IAAI,CAACsD,KAAK;kBAAWvE,KAAK,EAAC;oCACzEiB,IAAI,CAAC4D,oBAAoB,IAAG,GAAC,GAAArD,gBAAA,CAAGP,IAAI,CAAC6D,0BAA0B,IAAG,IACvE,wBACAhC,mBAAA,CAKM,OALNwC,UAKM,G,0BAtEpB/D,gBAAA,CAiEiD,QAEjC,K,kBAAAtB,mBAAA,CAEOa,SAAA,QArEvBC,WAAA,CAmE+CE,IAAI,CAACsD,KAAK,EAnEzD,UAmE8BS,KAAK,EAAEC,KAAK;wDAA1BhF,mBAAA,CAEO;oBAF2DmB,GAAG,EAAE4D,KAAK,CAACX;sBAnE7F9C,gBAAA,CAAAC,gBAAA,CAoEqByD,KAAK,oBAAAzD,gBAAA,CAAsBwD,KAAK,CAACE,QAAQ,IAAG,GAAC,GAAA1D,gBAAA,CAAGwD,KAAK,CAACG,MAAM,IAAG,IACpE,gB,uBAFmDH,KAAK,CAACG,MAAM,E;;;cAnE/ExD,CAAA;;;;QA8EqB4D,oBAAoB,EAAA1E,QAAA,CAG3B,UAAmDoD,KAHjB;UAAA,S,kBACpChE,mBAAA,CAoBaa,SAAA,QAnGzBC,WAAA,CAiFkCkD,KAAK,CAACC,GAAG,CAACqB,oBAAoB,EAjFhE,UAiFsBtE,IAAI,EAAEoE,CAAC;iCAFjBnE,YAAA,CAoBaiD,qBAAA;cAnBXC,SAAS,EAAC,WAAW;cAEpBhD,GAAG,EAAEH,IAAI,CAACoD,EAAE;cACZC,QAAQ,EAAErD,IAAI,CAACsD,KAAK;cACrB,cAAY,EAAC;;cAUFC,SAAS,EAAA3D,QAAA,CAClB;gBAAA,OAEO,CAFPiC,mBAAA,CAEO;kBAFA2B,KAAK,EA/F5BC,eAAA,CA+F8BpE,MAAA,CAAAqE,QAAQ,CAAC1D,IAAI,CAAC2D,sBAAsB,EAAE3D,IAAI,CAACsD,KAAK;oCACzDc,CAAC,oBAAA7D,gBAAA,CAAsBP,IAAI,CAAC4D,oBAAoB,wB;;cAhGrEjE,OAAA,EAAAC,QAAA,CAqFc;gBAAA,OAEM,CAFNiC,mBAAA,CAEM;kBAFA2B,KAAK,EArFzBC,eAAA,CAqF2BpE,MAAA,CAAAqE,QAAQ,CAAC1D,IAAI,CAAC2D,sBAAsB,EAAE3D,IAAI,CAACsD,KAAK;kBAAWvE,KAAK,EAAC;oCACzEiB,IAAI,CAAC4D,oBAAoB,IAAG,GAAC,GAAArD,gBAAA,CAAGP,IAAI,CAAC6D,0BAA0B,IAAG,IACvE,wBACAhC,mBAAA,CAKM,OALN0C,UAKM,G,0BA7FpBjE,gBAAA,CAwFiD,QAEjC,K,kBAAAtB,mBAAA,CAEOa,SAAA,QA5FvBC,WAAA,CA0F+CE,IAAI,CAACsD,KAAK,EA1FzD,UA0F8BS,KAAK,EAAEC,KAAK;wDAA1BhF,mBAAA,CAEO;oBAF2DmB,GAAG,EAAE4D,KAAK,CAACX;sBA1F7F9C,gBAAA,CAAAC,gBAAA,CA2FqByD,KAAK,oBAAAzD,gBAAA,CAAsBwD,KAAK,CAACE,QAAQ,IAAG,GAAC,GAAA1D,gBAAA,CAAGwD,KAAK,CAACG,MAAM,IAAG,IACpE,gB,uBAFmDH,KAAK,CAACG,MAAM,E;;;cA1F/ExD,CAAA;;;;QAAAA,CAAA;wCAsGQxB,YAAA,CAAsGsF,kCAAA;QAA7EC,KAAK,EAAC,EAAE;QAAEC,mBAAmB,EAAErF,MAAA,CAAAsF;;;IAtGhEjE,CAAA;sGAyGImB,mBAAA,CAUM,OAVN+C,UAUM,GATJ1F,YAAA,CAQe2F,wBAAA;IAPLC,WAAW,EAAEzF,MAAA,CAAA0F,MAAM;IA3GnC,wBAAAxF,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2G6BH,MAAA,CAAA0F,MAAM,GAAAvF,MAAA;IAAA;IACnB,WAAS,EAAEH,MAAA,CAAA2F,QAAQ;IA5GnC,qBAAAzF,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA4G2BH,MAAA,CAAA2F,QAAQ,GAAAxF,MAAA;IAAA;IAC1B,YAAU,EAAEH,MAAA,CAAA4F,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAE9F,MAAA,CAAAwB,WAAW;IACxBuE,eAAc,EAAE/F,MAAA,CAAAwB,WAAW;IAC3BwE,KAAK,EAAEhG,MAAA,CAAAiG,MAAM;IACdC,UAAU,EAAV;qHAEJrG,YAAA,CAQmBsG,2BAAA;IA5HvBpG,UAAA,EAoH+BC,MAAA,CAAAoG,UAAU;IApHzC,uBAAAlG,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoH+BH,MAAA,CAAAoG,UAAU,GAAAjG,MAAA;IAAA;IAAEkG,IAAI,EAAC;;IApHhD/F,OAAA,EAAAC,QAAA,CAqHM;MAAA,OAM+C,CAN/CV,YAAA,CAM+CyG,2BAAA;QAL7CD,IAAI,EAAC,OAAO;QACXE,QAAQ,EAAEvG,MAAA,CAAAuG,QAAQ;QAClBC,MAAM,EAAExG,MAAA,CAAAyG,YAAY;QACrBC,MAAM,EAAC,qBAAqB;QAC5BC,OAAO,EAAC,qCAAqC;QAC5CC,eAAa,EAAE5G,MAAA,CAAA6G;;;IA3HxBxF,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}