{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createBlock as _createBlock, createTextVNode as _createTextVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalChatAddressBookList forbidSelect\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalChatAddressBookInput\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalChatAddressBookItem forbidSelect\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalChatAddressBookName ellipsis\"\n};\nvar _hoisted_5 = {\n  key: 0,\n  class: \"GlobalChatAddressBookLabel\"\n};\nvar _hoisted_6 = [\"onClick\"];\nvar _hoisted_7 = {\n  class: \"GlobalChatAddressBookName ellipsis\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  class: \"GlobalChatAddressBookBody\"\n};\nvar _hoisted_9 = {\n  class: \"GlobalChatAddressBookInfo\"\n};\nvar _hoisted_10 = {\n  class: \"GlobalChatAddressBookInfoBody\"\n};\nvar _hoisted_11 = [\"innerHTML\"];\nvar _hoisted_12 = [\"innerHTML\"];\nvar _hoisted_13 = {\n  class: \"GlobalChatAddressBookIcon\"\n};\nvar _hoisted_14 = {\n  class: \"GlobalChatAddressBookText ellipsis\"\n};\nvar _hoisted_15 = [\"innerHTML\"];\nvar _hoisted_16 = {\n  class: \"GlobalChatAddressBookText ellipsis\"\n};\nvar _hoisted_17 = {\n  class: \"GlobalChatAddressBookUserBody\"\n};\nvar _hoisted_18 = {\n  class: \"GlobalChatAddressBookUserText ellipsis\"\n};\nvar _hoisted_19 = {\n  class: \"GlobalChatAddressBookUserText ellipsis\"\n};\nvar _hoisted_20 = {\n  class: \"GlobalChatAddressBookUserText ellipsis\"\n};\nvar _hoisted_21 = {\n  key: 0,\n  class: \"GlobalChatAddressBookUserText ellipsis\"\n};\nvar _hoisted_22 = {\n  key: 1,\n  class: \"GlobalChatAddressBookUserText ellipsis\"\n};\nvar _hoisted_23 = {\n  key: 2,\n  class: \"GlobalChatAddressBookUserText ellipsis\"\n};\nvar _hoisted_24 = {\n  key: 3,\n  style: {\n    \"margin\": \"10px 0\"\n  }\n};\nvar _hoisted_25 = {\n  key: 1,\n  class: \"GlobalChatAddressBookDrag\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$setup$userInfo, _$setup$userInfo2, _$setup$oftenList, _$setup$oftenList2;\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_autocomplete = _resolveComponent(\"el-autocomplete\");\n  var _component_el_tree = _resolveComponent(\"el-tree\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_Star = _resolveComponent(\"Star\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_StarFilled = _resolveComponent(\"StarFilled\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"GlobalChatAddressBook\", {\n      GlobalChatMacAddressBook: $setup.isMac\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_autocomplete, {\n    modelValue: $setup.keyword,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.keyword = $event;\n    }),\n    \"prefix-icon\": $setup.Search,\n    \"fetch-suggestions\": $setup.querySearch,\n    placeholder: \"搜索\",\n    \"popper-class\": \"GlobalChatAddressBookAutocomplete\",\n    clearable: \"\",\n    onSelect: $setup.handleClick\n  }, {\n    default: _withCtx(function (_ref) {\n      var item = _ref.item;\n      return [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n        src: $setup.imgUrl(item.user.photo || item.user.headImg),\n        fit: \"cover\",\n        draggable: \"false\"\n      }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_4, _toDisplayString(item.user.userName), 1 /* TEXT */)])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]), _createVNode(_component_el_scrollbar, {\n    class: \"GlobalChatAddressBookScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_tree, {\n        ref: \"treeRef\",\n        lazy: \"\",\n        load: $setup.loadNode,\n        \"node-key\": \"id\",\n        props: {\n          isLeaf: 'isLeaf'\n        }\n      }, {\n        default: _withCtx(function (_ref2) {\n          var data = _ref2.data;\n          return [data.type !== 'user' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _toDisplayString(data.label), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), data.type === 'user' ? (_openBlock(), _createElementBlock(\"div\", {\n            key: 1,\n            class: _normalizeClass(['GlobalChatAddressBookItem', {\n              'is-active': data.id === $setup.userId\n            }]),\n            onClick: function onClick($event) {\n              return $setup.handleClick(data);\n            }\n          }, [_createVNode(_component_el_image, {\n            src: $setup.imgUrl(data.user.photo || data.user.headImg),\n            fit: \"cover\",\n            draggable: \"false\"\n          }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(data.user.userName), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_6)) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }, 512 /* NEED_PATCH */)];\n    }),\n    _: 1 /* STABLE */\n  })]), $setup.userId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_image, {\n    src: $setup.imgUrl($setup.userInfo.photo || $setup.userInfo.headImg),\n    fit: \"cover\",\n    draggable: \"false\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", {\n    class: \"GlobalChatAddressBookName ellipsis\",\n    onClick: _cache[3] || (_cache[3] = function ($event) {\n      return $setup.openUser($setup.userInfo);\n    })\n  }, [_createTextVNode(_toDisplayString($setup.userInfo.userName) + \" \", 1 /* TEXT */), ((_$setup$userInfo = $setup.userInfo) === null || _$setup$userInfo === void 0 || (_$setup$userInfo = _$setup$userInfo.sex) === null || _$setup$userInfo === void 0 ? void 0 : _$setup$userInfo.value) === '1' ? (_openBlock(), _createElementBlock(\"span\", {\n    key: 0,\n    innerHTML: $setup.maleIcon\n  }, null, 8 /* PROPS */, _hoisted_11)) : _createCommentVNode(\"v-if\", true), ((_$setup$userInfo2 = $setup.userInfo) === null || _$setup$userInfo2 === void 0 || (_$setup$userInfo2 = _$setup$userInfo2.sex) === null || _$setup$userInfo2 === void 0 ? void 0 : _$setup$userInfo2.value) === '2' ? (_openBlock(), _createElementBlock(\"span\", {\n    key: 1,\n    innerHTML: $setup.femaleIcon\n  }, null, 8 /* PROPS */, _hoisted_12)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_13, [!((_$setup$oftenList = $setup.oftenList) !== null && _$setup$oftenList !== void 0 && _$setup$oftenList.includes($setup.userId)) ? (_openBlock(), _createBlock(_component_el_icon, {\n    key: 0,\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.handleOftenList(1);\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Star)];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), (_$setup$oftenList2 = $setup.oftenList) !== null && _$setup$oftenList2 !== void 0 && _$setup$oftenList2.includes($setup.userId) ? (_openBlock(), _createBlock(_component_el_icon, {\n    key: 1,\n    class: \"is-active\",\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      return $setup.handleOftenList(0);\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_StarFilled)];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"span\", {\n    innerHTML: $setup.mobileIcon\n  }, null, 8 /* PROPS */, _hoisted_15), _createTextVNode(\" \" + _toDisplayString($setup.userInfo.mobile), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, _toDisplayString($setup.userInfo.position), 1 /* TEXT */)]), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSendMessage\n  }, {\n    default: _withCtx(function () {\n      return _cache[4] || (_cache[4] = [_createTextVNode(\"发送消息\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_el_scrollbar, {\n    class: \"GlobalChatAddressBookUserScroll\"\n  }, {\n    default: _withCtx(function () {\n      var _$setup$userInfo3, _$setup$userInfo$role, _$setup$userInfo$role2, _$setup$userInfo$role3, _$setup$userInfo$role4, _$setup$userInfo$role5, _$setup$userInfo$role6;\n      return [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, \"民族：\" + _toDisplayString((_$setup$userInfo3 = $setup.userInfo) === null || _$setup$userInfo3 === void 0 || (_$setup$userInfo3 = _$setup$userInfo3.nation) === null || _$setup$userInfo3 === void 0 ? void 0 : _$setup$userInfo3.label), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, \" 出生年月：\" + _toDisplayString($setup.format($setup.userInfo.birthday, 'YYYY-MM-DD')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_20, \"籍贯：\" + _toDisplayString($setup.userInfo.nativePlace), 1 /* TEXT */), (_$setup$userInfo$role = $setup.userInfo.roleIds) !== null && _$setup$userInfo$role !== void 0 && _$setup$userInfo$role.includes('1684119713426243586') || (_$setup$userInfo$role2 = $setup.userInfo.roleIds) !== null && _$setup$userInfo$role2 !== void 0 && _$setup$userInfo$role2.includes('1743150705994133506') || (_$setup$userInfo$role3 = $setup.userInfo.roleIds) !== null && _$setup$userInfo$role3 !== void 0 && _$setup$userInfo$role3.includes('1643857093725327362') ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, \" 办公室电话：\" + _toDisplayString($setup.userInfo.officePhone), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), (_$setup$userInfo$role4 = $setup.userInfo.roleIds) !== null && _$setup$userInfo$role4 !== void 0 && _$setup$userInfo$role4.includes('1640259895343255554') ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, \" 地址：\" + _toDisplayString($setup.userInfo.callAddress), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), (_$setup$userInfo$role5 = $setup.userInfo.roleIds) !== null && _$setup$userInfo$role5 !== void 0 && _$setup$userInfo$role5.includes('1640259895343255554') ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, \" 个人二维码：\")) : _createCommentVNode(\"v-if\", true), (_$setup$userInfo$role6 = $setup.userInfo.roleIds) !== null && _$setup$userInfo$role6 !== void 0 && _$setup$userInfo$role6.includes('1640259895343255554') ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_createVNode($setup[\"QrcodeVue\"], {\n        value: $setup.shareUrl,\n        \"render-as\": \"svg\",\n        level: \"L\",\n        size: 90\n      }, null, 8 /* PROPS */, [\"value\"])])) : _createCommentVNode(\"v-if\", true)])];\n    }),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true), !$setup.userId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25)) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "key", "style", "_createElementBlock", "_normalizeClass", "GlobalChatMacAddressBook", "$setup", "isMac", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_autocomplete", "modelValue", "keyword", "_cache", "$event", "Search", "querySearch", "placeholder", "clearable", "onSelect", "handleClick", "default", "_withCtx", "_ref", "item", "_hoisted_3", "_component_el_image", "src", "imgUrl", "user", "photo", "headImg", "fit", "draggable", "_hoisted_4", "_toDisplayString", "userName", "_", "_component_el_scrollbar", "_component_el_tree", "ref", "lazy", "load", "loadNode", "props", "<PERSON><PERSON><PERSON><PERSON>", "_ref2", "data", "type", "_hoisted_5", "label", "_createCommentVNode", "id", "userId", "onClick", "_hoisted_7", "_hoisted_6", "_hoisted_8", "_hoisted_9", "userInfo", "_hoisted_10", "openUser", "_createTextVNode", "_$setup$userInfo", "sex", "value", "innerHTML", "maleIcon", "_hoisted_11", "_$setup$userInfo2", "femaleIcon", "_hoisted_12", "_hoisted_13", "oftenList", "_$setup$oftenList", "includes", "_createBlock", "_component_el_icon", "handleOftenList", "_component_Star", "_$setup$oftenList2", "_component_StarFilled", "_hoisted_14", "mobileIcon", "_hoisted_15", "mobile", "_hoisted_16", "position", "_component_el_button", "handleSendMessage", "_$setup$userInfo3", "_$setup$userInfo$role", "_$setup$userInfo$role2", "_$setup$userInfo$role3", "_$setup$userInfo$role4", "_$setup$userInfo$role5", "_$setup$userInfo$role6", "_hoisted_17", "_hoisted_18", "nation", "_hoisted_19", "format", "birthday", "_hoisted_20", "nativePlace", "roleIds", "_hoisted_21", "officePhone", "_hoisted_22", "call<PERSON>dd<PERSON>", "_hoisted_23", "_hoisted_24", "shareUrl", "level", "size", "_hoisted_25"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalChatAddressBook.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatAddressBook\" :class=\"{ GlobalChatMacAddressBook: isMac }\">\r\n    <div class=\"GlobalChatAddressBookList forbidSelect\">\r\n      <div class=\"GlobalChatAddressBookInput\">\r\n        <el-autocomplete v-model=\"keyword\" :prefix-icon=\"Search\" :fetch-suggestions=\"querySearch\" placeholder=\"搜索\"\r\n          popper-class=\"GlobalChatAddressBookAutocomplete\" clearable @select=\"handleClick\">\r\n          <template #default=\"{ item }\">\r\n            <div class=\"GlobalChatAddressBookItem forbidSelect\">\r\n              <el-image :src=\"imgUrl(item.user.photo || item.user.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n              <div class=\"GlobalChatAddressBookName ellipsis\">{{ item.user.userName }}</div>\r\n            </div>\r\n          </template>\r\n        </el-autocomplete>\r\n      </div>\r\n      <el-scrollbar class=\"GlobalChatAddressBookScrollbar\">\r\n        <el-tree ref=\"treeRef\" lazy :load=\"loadNode\" node-key=\"id\" :props=\"{ isLeaf: 'isLeaf' }\">\r\n          <template #default=\"{ data }\">\r\n            <div class=\"GlobalChatAddressBookLabel\" v-if=\"data.type !== 'user'\">{{ data.label }}</div>\r\n            <div :class=\"['GlobalChatAddressBookItem', { 'is-active': data.id === userId }]\" @click=\"handleClick(data)\"\r\n              v-if=\"data.type === 'user'\">\r\n              <el-image :src=\"imgUrl(data.user.photo || data.user.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n              <div class=\"GlobalChatAddressBookName ellipsis\">{{ data.user.userName }}</div>\r\n            </div>\r\n          </template>\r\n        </el-tree>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"GlobalChatAddressBookBody\" v-if=\"userId\">\r\n      <div class=\"GlobalChatAddressBookInfo\">\r\n        <el-image :src=\"imgUrl(userInfo.photo || userInfo.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n        <div class=\"GlobalChatAddressBookInfoBody\">\r\n          <div class=\"GlobalChatAddressBookName ellipsis\" @click=\"openUser(userInfo)\">{{ userInfo.userName }}\r\n            <span v-html=\"maleIcon\" v-if=\"userInfo?.sex?.value === '1'\"></span>\r\n            <span v-html=\"femaleIcon\" v-if=\"userInfo?.sex?.value === '2'\"></span>\r\n            <div class=\"GlobalChatAddressBookIcon\">\r\n              <el-icon @click=\"handleOftenList(1)\" v-if=\"!oftenList?.includes(userId)\">\r\n                <Star />\r\n              </el-icon>\r\n              <el-icon class=\"is-active\" @click=\"handleOftenList(0)\" v-if=\"oftenList?.includes(userId)\">\r\n                <StarFilled />\r\n              </el-icon>\r\n            </div>\r\n          </div>\r\n          <div class=\"GlobalChatAddressBookText ellipsis\">\r\n            <span v-html=\"mobileIcon\"></span>\r\n            {{ userInfo.mobile }}\r\n          </div>\r\n          <div class=\"GlobalChatAddressBookText ellipsis\">{{ userInfo.position }}</div>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"handleSendMessage\">发送消息</el-button>\r\n      </div>\r\n      <el-scrollbar class=\"GlobalChatAddressBookUserScroll\">\r\n        <div class=\"GlobalChatAddressBookUserBody\">\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\">民族：{{ userInfo?.nation?.label }}</div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\">\r\n            出生年月：{{ format(userInfo.birthday, 'YYYY-MM-DD') }}\r\n          </div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\">籍贯：{{ userInfo.nativePlace }}</div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\"\r\n            v-if=\"userInfo.roleIds?.includes('1684119713426243586') || userInfo.roleIds?.includes('1743150705994133506') || userInfo.roleIds?.includes('1643857093725327362')\">\r\n            办公室电话：{{ userInfo.officePhone }}</div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\" v-if=\"userInfo.roleIds?.includes('1640259895343255554')\">\r\n            地址：{{\r\n              userInfo.callAddress }}</div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\" v-if=\"userInfo.roleIds?.includes('1640259895343255554')\">\r\n            个人二维码：</div>\r\n          <div style=\"margin: 10px 0;\" v-if=\"userInfo.roleIds?.includes('1640259895343255554')\">\r\n            <qrcode-vue :value=\"shareUrl\" render-as=\"svg\" level=\"L\" :size=\"90\" />\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"GlobalChatAddressBookDrag\" v-if=\"!userId\"></div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatAddressBook' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { format } from 'common/js/time.js'\r\nimport { appOnlyHeader } from 'common/js/system_var.js'\r\nimport { mobileIcon, maleIcon, femaleIcon } from '../js/icon.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Search } from '@element-plus/icons-vue'\r\nimport config from 'common/config'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport { useStore } from 'vuex'\r\nconst store = useStore()\r\nconst emit = defineEmits(['send'])\r\nconst isMac = window.electron?.isMac\r\nconst treeRef = ref()\r\nconst keyword = ref('')\r\nconst labelAll = ref([])\r\nconst userId = ref('')\r\nconst userInfo = ref({})\r\nconst oftenList = ref([])\r\nconst imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))\r\nconst shareUrl = ref('')\r\nconst querySearch = async (queryString, cb) => {\r\n  const results = queryString ? await handleQuery(queryString) : []\r\n  cb(results)\r\n}\r\nconst handleQuery = async () => {\r\n  let newUserDataAll = []\r\n  for (let index = 0; index < labelAll.value.length; index++) {\r\n    const item = labelAll.value[index]\r\n    const newUserData = await handleUserData(item.id)\r\n    newUserDataAll = [...newUserDataAll, ...newUserData]\r\n  }\r\n  return [...new Map(newUserDataAll.map((item) => [item.id, item])).values()]\r\n}\r\nconst handleUserData = async (id) => {\r\n  try {\r\n    const { data } = await api.SelectPersonBookUser({\r\n      isOpen: 1,\r\n      keyword: keyword.value,\r\n      labelCode: id,\r\n      nodeId: '',\r\n      relationBookId: '',\r\n      tabCode: 'relationBooksTemp'\r\n    })\r\n    const newUserData = []\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      if (item.userId)\r\n        newUserData.push({\r\n          id: item.userId,\r\n          label: item.userName,\r\n          children: [],\r\n          type: 'user',\r\n          user: item,\r\n          isLeaf: true\r\n        })\r\n    }\r\n    return newUserData\r\n  } catch (error) {\r\n    return []\r\n  }\r\n}\r\nconst handleClick = (item) => {\r\n  userId.value = item.id\r\n  getUserInfo(item)\r\n}\r\nconst getUserInfo = async () => {\r\n  const { data } = await api.userInfo({ detailId: userId.value })\r\n  userInfo.value = data\r\n  globalReadConfig()\r\n}\r\n\r\n// 获取个人二维码\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: ['appShareAddress'] })\r\n  getUserQrCode(data.appShareAddress)\r\n}\r\nconst getUserQrCode = async (appShareAddress) => {\r\n  var params = {\r\n    n: 'mo_npcinfo_details',\r\n    u: '../mo_npcinfo_details/mo_npcinfo_details.stml',\r\n    p: {\r\n      id: userId.value\r\n    }\r\n  }\r\n  longShortLink(appShareAddress + 'pages/index/?' + JSON.stringify(params).replace(/\\{/g, \"%7B\").replace(/\\}/g, \"%7D\").replace(/\\u0022/g, \"%22\"))\r\n}\r\nconst longShortLink = async (url) => {\r\n  const { data } = await api.longShortLink(encodeURIComponent(url))\r\n  shareUrl.value = `${config.API_URL}/viewing/${data}`\r\n}\r\n// 跳转个人信息\r\nconst openUser = (item) => {\r\n  if (item.roleIds) {\r\n    if (item.roleIds.includes('1640259895343255554')) {\r\n      store.commit('setOpenRoute', { name: '委员信息详情', path: '/cppccMember/CppccMemberDetails', query: { id: item.id } })\r\n      return\r\n    }\r\n    if (item.roleIds.includes('1684119713426243586') || item.roleIds.includes('1743150705994133506') || item.roleIds.includes('1643857093725327362')) {\r\n      store.commit('setOpenRoute', { name: '用户信息详情', path: '/system/SubmitUser', query: { id: item.id, utype: 1 } })\r\n    }\r\n  }\r\n}\r\n\r\nconst handleMessages = async (conversationType, targetId) => {\r\n  const res = await RongIMLib.getConversation({ conversationType, targetId })\r\n  return res\r\n}\r\nconst handleSendMessage = async () => {\r\n  const targetId = appOnlyHeader.value + userInfo.value.accountId\r\n  const { code, data } = await handleMessages(1, targetId)\r\n  if (!code) {\r\n    let newSendMessage = {\r\n      isTemporary: true,\r\n      isTop: data.isTop,\r\n      isNotInform: data.notificationStatus,\r\n      id: data.targetId,\r\n      targetId: data.targetId,\r\n      type: data.conversationType,\r\n      chatObjectInfo: {\r\n        uid: data.targetId,\r\n        id: userInfo.value.accountId,\r\n        name: userInfo.value.userName,\r\n        img: userInfo.value.photo || userInfo.value.headImg,\r\n        userInfo: {\r\n          userId: userInfo.value.id,\r\n          userName: userInfo.value.userName,\r\n          photo: userInfo.value.photo,\r\n          headImg: userInfo.value.headImg\r\n        }\r\n      },\r\n      sentTime: data.latestMessage?.sentTime || Date.parse(new Date()),\r\n      messageType: data.latestMessage?.messageType || 'RC:TxtMsg',\r\n      content: data.latestMessage?.content || { content: '' },\r\n      count: data.unreadMessageCount\r\n    }\r\n    emit('send', newSendMessage)\r\n  }\r\n}\r\nconst SelectPersonTab = async (resolve) => {\r\n  const { data } = await api.SelectPersonTab({ tabCodes: ['relationBooksTemp'] })\r\n  const newLabelData = []\r\n  for (let index = 0; index < data[0]?.chooseLabels.length; index++) {\r\n    const item = data[0]?.chooseLabels[index]\r\n    newLabelData.push({ id: item.labelCode, label: item.name, children: [], type: 'label', isLeaf: false })\r\n  }\r\n  labelAll.value = newLabelData\r\n  resolve(newLabelData)\r\n}\r\n\r\nconst handleTreeData = (id, data) => {\r\n  const newLabelData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.code !== id) {\r\n      const children = handleTreeData(id, item.children)\r\n      newLabelData.push({ id: item.code, label: item.name, children: children, type: 'tree', isLeaf: false })\r\n    }\r\n  }\r\n  return newLabelData\r\n}\r\nconst SelectPersonGroup = async (id) => {\r\n  const { data } = await api.SelectPersonGroup({ labelCode: id, tabCode: 'relationBooksTemp' })\r\n  return handleTreeData(id, data)\r\n}\r\nconst SelectPersonBookUser = async (parentId, id) => {\r\n  const { data } = await api.SelectPersonBookUser({\r\n    isOpen: 1,\r\n    keyword: '',\r\n    labelCode: parentId,\r\n    nodeId: id,\r\n    relationBookId: id,\r\n    tabCode: 'relationBooksTemp'\r\n  })\r\n  const newUserData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.userId)\r\n      newUserData.push({ id: item.userId, label: item.userName, children: [], type: 'user', user: item, isLeaf: true })\r\n  }\r\n  return newUserData\r\n}\r\nconst loadNode = async (node, resolve) => {\r\n  if (node.level === 0) {\r\n    SelectPersonTab(resolve)\r\n  } else {\r\n    if (node.data?.children?.length) {\r\n      const newTreeData = node.data?.children\r\n      const newUserData = await SelectPersonBookUser(node.parent.key, node.key)\r\n      const newData = [...newTreeData, ...newUserData]\r\n      resolve(newData)\r\n    } else {\r\n      if (node.parent.level) {\r\n        const newUserData = await SelectPersonBookUser(node.parent.key, node.key)\r\n        resolve(newUserData)\r\n      } else {\r\n        const newTreeData = await SelectPersonGroup(node.key)\r\n        const newUserData = await SelectPersonBookUser(node.key, node.key)\r\n        resolve([...newTreeData, ...newUserData])\r\n      }\r\n    }\r\n  }\r\n}\r\nconst relationBookMemberOftenList = async (id) => {\r\n  const { data } = await api.relationBookMemberOftenList()\r\n  oftenList.value = data\r\n}\r\nconst handleOftenList = (type) => {\r\n  relationBookMemberSetOften(type)\r\n}\r\nconst relationBookMemberSetOften = async (type) => {\r\n  const { code } = await api.relationBookMemberSetOften({ isOften: type, userIds: [userId.value] })\r\n  if (code === 200) {\r\n    relationBookMemberOftenList()\r\n    SelectPersonTab((data) => {\r\n      treeRef.value?.store.setData(data)\r\n    })\r\n    ElMessage({ message: `${type ? '收藏为' : '移除'}常用联系人成功！`, type: 'success' })\r\n  }\r\n}\r\nconst handleRefresh = async () => {\r\n  SelectPersonTab((data) => {\r\n    treeRef.value?.store.setData(data)\r\n  })\r\n}\r\nonMounted(() => {\r\n  relationBookMemberOftenList()\r\n})\r\ndefineExpose({ refresh: handleRefresh })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalChatAddressBook {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n\r\n  &.GlobalChatMacAddressBook {\r\n    .GlobalChatAddressBookList {\r\n      .GlobalChatAddressBookInput {\r\n        height: 56px;\r\n      }\r\n\r\n      .GlobalChatAddressBookScrollbar {\r\n        height: calc(100% - 56px);\r\n      }\r\n    }\r\n\r\n    .GlobalChatAddressBookDrag {\r\n      height: 56px;\r\n    }\r\n  }\r\n\r\n  .GlobalChatAddressBookList {\r\n    width: 280px;\r\n    height: 100%;\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalChatAddressBookInput {\r\n      width: 100%;\r\n      height: 66px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n      -webkit-app-region: drag;\r\n\r\n      .zy-el-autocomplete {\r\n        width: 240px;\r\n        height: var(--zy-height-routine);\r\n        -webkit-app-region: no-drag;\r\n\r\n        .zy-el-input {\r\n          width: 240px;\r\n          height: var(--zy-height-routine);\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatAddressBookScrollbar {\r\n      width: 100%;\r\n      height: calc(100% - 66px);\r\n\r\n      .zy-el-tree {\r\n        padding: 0 20px 20px 20px;\r\n\r\n        .zy-el-tree-node {\r\n          .zy-el-tree-node__content {\r\n            height: auto;\r\n            padding: 10px 0;\r\n            position: relative;\r\n            background: transparent;\r\n          }\r\n        }\r\n      }\r\n\r\n      .GlobalChatAddressBookLabel {\r\n        &::after {\r\n          content: '';\r\n          width: 100%;\r\n          border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n          position: absolute;\r\n          right: 0;\r\n          bottom: 0;\r\n        }\r\n      }\r\n\r\n      .GlobalChatAddressBookItem {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        position: relative;\r\n\r\n        &.is-active {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 38px;\r\n          height: 38px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .GlobalChatAddressBookName {\r\n          width: calc(100% - 54px);\r\n          font-size: 14px;\r\n\r\n          &::after {\r\n            content: '';\r\n            width: calc(100% - 54px);\r\n            border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n            position: absolute;\r\n            right: 0;\r\n            bottom: -10px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatAddressBookDrag {\r\n    width: calc(100% - 280px);\r\n    height: 66px;\r\n    position: relative;\r\n    -webkit-app-region: drag;\r\n\r\n    &::before {\r\n      content: '';\r\n      width: 96px;\r\n      height: 28px;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      background: transparent;\r\n      -webkit-app-region: no-drag;\r\n    }\r\n  }\r\n\r\n  .GlobalChatAddressBookBody {\r\n    width: calc(100% - 280px);\r\n    height: 100%;\r\n\r\n    .GlobalChatAddressBookInfo {\r\n      width: 100%;\r\n      height: 180px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 40px;\r\n      position: relative;\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      -webkit-app-region: drag;\r\n\r\n      &::before {\r\n        content: '';\r\n        width: 96px;\r\n        height: 28px;\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        background: transparent;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .zy-el-image {\r\n        width: 78px;\r\n        height: 78px;\r\n        border-radius: 8%;\r\n        overflow: hidden;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .GlobalChatAddressBookInfoBody {\r\n        padding-left: 16px;\r\n        -webkit-app-region: no-drag;\r\n\r\n        .GlobalChatAddressBookName {\r\n          width: 100%;\r\n          display: flex;\r\n          align-items: center;\r\n          font-size: 18px;\r\n          line-height: 26px;\r\n          font-weight: bold;\r\n          padding-bottom: 6px;\r\n\r\n          span {\r\n            width: 20px;\r\n            height: 20px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-left: 12px;\r\n          }\r\n\r\n          .GlobalChatAddressBookIcon {\r\n            height: 20px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding-left: 20px;\r\n\r\n            .zy-el-icon {\r\n              cursor: pointer;\r\n              font-size: 18px;\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n\r\n            .is-active {\r\n              font-size: 20px;\r\n              color: var(--zy-el-color-warning);\r\n            }\r\n          }\r\n        }\r\n\r\n        .GlobalChatAddressBookText {\r\n          width: 100%;\r\n          height: 24px;\r\n          font-size: 14px;\r\n          line-height: 24px;\r\n          padding-top: 2px;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          span {\r\n            width: 20px;\r\n            height: 20px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 6px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-button {\r\n        position: absolute;\r\n        right: var(--zy-distance-two);\r\n        bottom: 12px;\r\n        height: var(--zy-height-secondary);\r\n        -webkit-app-region: no-drag;\r\n      }\r\n    }\r\n\r\n    .GlobalChatAddressBookUserScroll {\r\n      width: 100%;\r\n      height: calc(100% - 180px);\r\n\r\n      .GlobalChatAddressBookUserBody {\r\n        padding: 20px 40px;\r\n\r\n        .GlobalChatAddressBookUserText {\r\n          width: 100%;\r\n          height: 24px;\r\n          font-size: 14px;\r\n          line-height: 24px;\r\n          padding-top: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatAddressBookAutocomplete {\r\n  .GlobalChatAddressBookItem {\r\n    width: 218px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 10px 0;\r\n    position: relative;\r\n\r\n    &.is-active {\r\n      color: var(--zy-el-color-primary);\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 38px;\r\n      height: 38px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .GlobalChatAddressBookName {\r\n      width: calc(100% - 54px);\r\n      font-size: 14px;\r\n      line-height: normal;\r\n\r\n      &::after {\r\n        content: '';\r\n        width: calc(100% - 54px);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n        position: absolute;\r\n        right: 0;\r\n        bottom: -10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAA4B;;EAI5BA,KAAK,EAAC;AAAwC;;EAE5CA,KAAK,EAAC;AAAoC;;EAT7DC,GAAA;EAiBiBD,KAAK,EAAC;;iBAjBvB;;EAqBmBA,KAAK,EAAC;AAAoC;;EArB7DC,GAAA;EA2BSD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EAE/BA,KAAK,EAAC;AAA+B;kBA9BlD;kBAAA;;EAkCiBA,KAAK,EAAC;AAA2B;;EASnCA,KAAK,EAAC;AAAoC;kBA3CzD;;EA+CeA,KAAK,EAAC;AAAoC;;EAK5CA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAwC;;EAC9CA,KAAK,EAAC;AAAwC;;EAG9CA,KAAK,EAAC;AAAwC;;EAzD7DC,GAAA;EA0DeD,KAAK,EAAC;;;EA1DrBC,GAAA;EA6DeD,KAAK,EAAC;;;EA7DrBC,GAAA;EAgEeD,KAAK,EAAC;;;EAhErBC,GAAA;EAkEeC,KAAuB,EAAvB;IAAA;EAAA;;;EAlEfD,GAAA;EAwESD,KAAK,EAAC;;;;;;;;;;;;uBAvEbG,mBAAA,CAwEM;IAxEDH,KAAK,EADZI,eAAA,EACa,uBAAuB;MAAAC,wBAAA,EAAqCC,MAAA,CAAAC;IAAK;MAC1EC,mBAAA,CAwBM,OAxBNC,UAwBM,GAvBJD,mBAAA,CAUM,OAVNE,UAUM,GATJC,YAAA,CAQkBC,0BAAA;IAZ1BC,UAAA,EAIkCP,MAAA,CAAAQ,OAAO;IAJzC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAIkCV,MAAA,CAAAQ,OAAO,GAAAE,MAAA;IAAA;IAAG,aAAW,EAAEV,MAAA,CAAAW,MAAM;IAAG,mBAAiB,EAAEX,MAAA,CAAAY,WAAW;IAAEC,WAAW,EAAC,IAAI;IACxG,cAAY,EAAC,mCAAmC;IAACC,SAAS,EAAT,EAAS;IAAEC,QAAM,EAAEf,MAAA,CAAAgB;;IACzDC,OAAO,EAAAC,QAAA,CAChB,UAAAC,IAAA;MAAA,IADoBC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MAAA,QACxBlB,mBAAA,CAGM,OAHNmB,UAGM,GAFJhB,YAAA,CAA8FiB,mBAAA;QAAnFC,GAAG,EAAEvB,MAAA,CAAAwB,MAAM,CAACJ,IAAI,CAACK,IAAI,CAACC,KAAK,IAAIN,IAAI,CAACK,IAAI,CAACE,OAAO;QAAGC,GAAG,EAAC,OAAO;QAACC,SAAS,EAAC;wCACpF3B,mBAAA,CAA8E,OAA9E4B,UAA8E,EAAAC,gBAAA,CAA3BX,IAAI,CAACK,IAAI,CAACO,QAAQ,iB;;IATnFC,CAAA;sDAcM5B,YAAA,CAWe6B,uBAAA;IAXDxC,KAAK,EAAC;EAAgC;IAd1DuB,OAAA,EAAAC,QAAA,CAeQ;MAAA,OASU,CATVb,YAAA,CASU8B,kBAAA;QATDC,GAAG,EAAC,SAAS;QAACC,IAAI,EAAJ,EAAI;QAAEC,IAAI,EAAEtC,MAAA,CAAAuC,QAAQ;QAAE,UAAQ,EAAC,IAAI;QAAEC,KAAK,EAAE;UAAAC,MAAA;QAAA;;QACtDxB,OAAO,EAAAC,QAAA,CAChB,UAAAwB,KAAA;UAAA,IADoBC,IAAI,GAAAD,KAAA,CAAJC,IAAI;UAAA,QACsBA,IAAI,CAACC,IAAI,e,cAAvD/C,mBAAA,CAA0F,OAA1FgD,UAA0F,EAAAd,gBAAA,CAAnBY,IAAI,CAACG,KAAK,oBAjB7FC,mBAAA,gBAmBoBJ,IAAI,CAACC,IAAI,e,cADjB/C,mBAAA,CAIM;YAtBlBF,GAAA;YAkBkBD,KAAK,EAlBvBI,eAAA;cAAA,aAkBsE6C,IAAI,CAACK,EAAE,KAAKhD,MAAA,CAAAiD;YAAM;YAAMC,OAAK,WAALA,OAAKA,CAAAxC,MAAA;cAAA,OAAEV,MAAA,CAAAgB,WAAW,CAAC2B,IAAI;YAAA;cAEvGtC,YAAA,CAA8FiB,mBAAA;YAAnFC,GAAG,EAAEvB,MAAA,CAAAwB,MAAM,CAACmB,IAAI,CAAClB,IAAI,CAACC,KAAK,IAAIiB,IAAI,CAAClB,IAAI,CAACE,OAAO;YAAGC,GAAG,EAAC,OAAO;YAACC,SAAS,EAAC;4CACpF3B,mBAAA,CAA8E,OAA9EiD,UAA8E,EAAApB,gBAAA,CAA3BY,IAAI,CAAClB,IAAI,CAACO,QAAQ,iB,yBArBnFoB,UAAA,KAAAL,mBAAA,e;;QAAAd,CAAA;;;IAAAA,CAAA;QA2BiDjC,MAAA,CAAAiD,MAAM,I,cAAnDpD,mBAAA,CA4CM,OA5CNwD,UA4CM,GA3CJnD,mBAAA,CAsBM,OAtBNoD,UAsBM,GArBJjD,YAAA,CAA4FiB,mBAAA;IAAjFC,GAAG,EAAEvB,MAAA,CAAAwB,MAAM,CAACxB,MAAA,CAAAuD,QAAQ,CAAC7B,KAAK,IAAI1B,MAAA,CAAAuD,QAAQ,CAAC5B,OAAO;IAAGC,GAAG,EAAC,OAAO;IAACC,SAAS,EAAC;oCAClF3B,mBAAA,CAkBM,OAlBNsD,WAkBM,GAjBJtD,mBAAA,CAWM;IAXDR,KAAK,EAAC,oCAAoC;IAAEwD,OAAK,EAAAzC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEV,MAAA,CAAAyD,QAAQ,CAACzD,MAAA,CAAAuD,QAAQ;IAAA;MA/BnFG,gBAAA,CAAA3B,gBAAA,CA+ByF/B,MAAA,CAAAuD,QAAQ,CAACvB,QAAQ,IAAG,GACjG,iBAA8B,EAAA2B,gBAAA,GAAA3D,MAAA,CAAAuD,QAAQ,cAAAI,gBAAA,gBAAAA,gBAAA,GAARA,gBAAA,CAAUC,GAAG,cAAAD,gBAAA,uBAAbA,gBAAA,CAAeE,KAAK,a,cAAlDhE,mBAAA,CAAmE;IAhC/EF,GAAA;IAgCkBmE,SAAiB,EAAT9D,MAAA,CAAA+D;0BAhC1BC,WAAA,KAAAjB,mBAAA,gBAiC4C,EAAAkB,iBAAA,GAAAjE,MAAA,CAAAuD,QAAQ,cAAAU,iBAAA,gBAAAA,iBAAA,GAARA,iBAAA,CAAUL,GAAG,cAAAK,iBAAA,uBAAbA,iBAAA,CAAeJ,KAAK,a,cAApDhE,mBAAA,CAAqE;IAjCjFF,GAAA;IAiCkBmE,SAAmB,EAAX9D,MAAA,CAAAkE;0BAjC1BC,WAAA,KAAApB,mBAAA,gBAkCY7C,mBAAA,CAOM,OAPNkE,WAOM,G,uBANwCpE,MAAA,CAAAqE,SAAS,cAAAC,iBAAA,eAATA,iBAAA,CAAWC,QAAQ,CAACvE,MAAA,CAAAiD,MAAM,M,cAAtEuB,YAAA,CAEUC,kBAAA;IArCxB9E,GAAA;IAmCwBuD,OAAK,EAAAzC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEV,MAAA,CAAA0E,eAAe;IAAA;;IAnC9CzD,OAAA,EAAAC,QAAA,CAoCgB;MAAA,OAAQ,CAARb,YAAA,CAAQsE,eAAA,E;;IApCxB1C,CAAA;QAAAc,mBAAA,gB,sBAsC2E/C,MAAA,CAAAqE,SAAS,cAAAO,kBAAA,eAATA,kBAAA,CAAWL,QAAQ,CAACvE,MAAA,CAAAiD,MAAM,K,cAAvFuB,YAAA,CAEUC,kBAAA;IAxCxB9E,GAAA;IAsCuBD,KAAK,EAAC,WAAW;IAAEwD,OAAK,EAAAzC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEV,MAAA,CAAA0E,eAAe;IAAA;;IAtChEzD,OAAA,EAAAC,QAAA,CAuCgB;MAAA,OAAc,CAAdb,YAAA,CAAcwE,qBAAA,E;;IAvC9B5C,CAAA;QAAAc,mBAAA,e,KA2CU7C,mBAAA,CAGM,OAHN4E,WAGM,GAFJ5E,mBAAA,CAAiC;IAA3B4D,SAAmB,EAAX9D,MAAA,CAAA+E;EAAU,wBA5CpCC,WAAA,GAAAtB,gBAAA,CA4C6C,GACjC,GAAA3B,gBAAA,CAAG/B,MAAA,CAAAuD,QAAQ,CAAC0B,MAAM,iB,GAEpB/E,mBAAA,CAA6E,OAA7EgF,WAA6E,EAAAnD,gBAAA,CAA1B/B,MAAA,CAAAuD,QAAQ,CAAC4B,QAAQ,iB,GAEtE9E,YAAA,CAAqE+E,oBAAA;IAA1DxC,IAAI,EAAC,SAAS;IAAEM,OAAK,EAAElD,MAAA,CAAAqF;;IAjD1CpE,OAAA,EAAAC,QAAA,CAiD6D;MAAA,OAAIT,MAAA,QAAAA,MAAA,OAjDjEiD,gBAAA,CAiD6D,MAAI,E;;IAjDjEzB,CAAA;QAmDM5B,YAAA,CAmBe6B,uBAAA;IAnBDxC,KAAK,EAAC;EAAiC;IAnD3DuB,OAAA,EAAAC,QAAA,CAoDQ;MAAA,IAAAoE,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAAA,OAiBM,CAjBN1F,mBAAA,CAiBM,OAjBN2F,WAiBM,GAhBJ3F,mBAAA,CAA0F,OAA1F4F,WAA0F,EAAtC,KAAG,GAAA/D,gBAAA,EAAAuD,iBAAA,GAAGtF,MAAA,CAAAuD,QAAQ,cAAA+B,iBAAA,gBAAAA,iBAAA,GAARA,iBAAA,CAAUS,MAAM,cAAAT,iBAAA,uBAAhBA,iBAAA,CAAkBxC,KAAK,kBACjF5C,mBAAA,CAEM,OAFN8F,WAEM,EAF8C,QAC7C,GAAAjE,gBAAA,CAAG/B,MAAA,CAAAiG,MAAM,CAACjG,MAAA,CAAAuD,QAAQ,CAAC2C,QAAQ,iCAElChG,mBAAA,CAAuF,OAAvFiG,WAAuF,EAAnC,KAAG,GAAApE,gBAAA,CAAG/B,MAAA,CAAAuD,QAAQ,CAAC6C,WAAW,kBAEtE,CAAAb,qBAAA,GAAAvF,MAAA,CAAAuD,QAAQ,CAAC8C,OAAO,cAAAd,qBAAA,eAAhBA,qBAAA,CAAkBhB,QAAQ,4BAAAiB,sBAAA,GAA2BxF,MAAA,CAAAuD,QAAQ,CAAC8C,OAAO,cAAAb,sBAAA,eAAhBA,sBAAA,CAAkBjB,QAAQ,4BAAAkB,sBAAA,GAA2BzF,MAAA,CAAAuD,QAAQ,CAAC8C,OAAO,cAAAZ,sBAAA,eAAhBA,sBAAA,CAAkBlB,QAAQ,2B,cAD5I1E,mBAAA,CAEwC,OAFxCyG,WAEwC,EAD6H,SAC7J,GAAAvE,gBAAA,CAAG/B,MAAA,CAAAuD,QAAQ,CAACgD,WAAW,oBA5DzCxD,mBAAA,gB,0BA6DoE/C,MAAA,CAAAuD,QAAQ,CAAC8C,OAAO,cAAAX,sBAAA,eAAhBA,sBAAA,CAAkBnB,QAAQ,2B,cAApF1E,mBAAA,CAEiC,OAFjC2G,WAEiC,EAF4E,MACxG,GAAAzE,gBAAA,CACD/B,MAAA,CAAAuD,QAAQ,CAACkD,WAAW,oBA/DlC1D,mBAAA,gB,0BAgEoE/C,MAAA,CAAAuD,QAAQ,CAAC8C,OAAO,cAAAV,sBAAA,eAAhBA,sBAAA,CAAkBpB,QAAQ,2B,cAApF1E,mBAAA,CACc,OADd6G,WACc,EAD+F,SACrG,KAjElB3D,mBAAA,gB,0BAkE6C/C,MAAA,CAAAuD,QAAQ,CAAC8C,OAAO,cAAAT,sBAAA,eAAhBA,sBAAA,CAAkBrB,QAAQ,2B,cAA7D1E,mBAAA,CAEM,OAFN8G,WAEM,GADJtG,YAAA,CAAqEL,MAAA;QAAxD6D,KAAK,EAAE7D,MAAA,CAAA4G,QAAQ;QAAE,WAAS,EAAC,KAAK;QAACC,KAAK,EAAC,GAAG;QAAEC,IAAI,EAAE;8CAnE3E/D,mBAAA,e;;IAAAd,CAAA;UAAAc,mBAAA,gB,CAwEkD/C,MAAA,CAAAiD,MAAM,I,cAApDpD,mBAAA,CAA4D,OAA5DkH,WAA4D,KAxEhEhE,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}