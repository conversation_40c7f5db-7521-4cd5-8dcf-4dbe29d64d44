<template>
  <div class="HandUnitSuperNew">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <global-info>
        <global-info-item label="办理单位">
          <suggest-simple-select-unit
            v-model="form.flowHandleOfficeId"
            :filterId="filterOfficeId"
            :max="1"></suggest-simple-select-unit>
        </global-info-item>
        <global-info-item label="办理类型">
          <el-select v-model="form.handleOfficeType" placeholder="请选择办理类型" clearable>
            <el-option v-for="item in handleOfficeType" :key="item.key" :label="item.name" :value="item.key" />
          </el-select>
        </global-info-item>
        <global-info-item label="是否阅读">
          <el-radio-group v-model="form.hasRead">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </global-info-item>
        <global-info-item label="首次阅读时间">
          <xyl-date-picker
            v-model="form.firstReadTime"
            type="datetime"
            value-format="x"
            placeholder="请选择首次阅读时间" />
        </global-info-item>
        <global-info-item label="是否签收" v-if="isPreAssign">
          <el-radio-group v-model="form.hasConfirm">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </global-info-item>
        <global-info-item label="签收时间" v-if="isPreAssign">
          <xyl-date-picker v-model="form.confirmTime" type="datetime" value-format="x" placeholder="选择签收时间" />
        </global-info-item>
        <!-- <global-info-item label="调整截止时间"
                          class="transactDetail">
          <div class="transactDetailBody">
            <div class="transactDetailInfo">
              <xyl-date-picker v-model="form.adjustStopDate"
                              type="datetime"
                              value-format="x"
                              placeholder="请选择调整截止时间" />
            </div>
          </div>
        </global-info-item> -->
        <!-- <global-info-item label="单位答复截止时间"
                          class="transactDetail">
          <div class="transactDetailBody">
            <div class="transactDetailInfo">
              <xyl-date-picker v-model="form.answerStopDate"
                              type="datetime"
                              value-format="x"
                              placeholder="请选择单位答复截止时间" />
            </div>
          </div>
        </global-info-item> -->
        <global-info-item label="办理情况（仅供标记）" class="transactDetail">
          <div class="transactDetailBody">
            <div class="transactDetailInfo">
              <el-select
                v-model="form.suggestionHandleStatus"
                :disabled="form.currentHandleStatus === 'trace'"
                placeholder="请选择内部流程状态"
                clearable>
                <el-option
                  v-for="item in suggestionHandleStatus"
                  :key="item.key"
                  :label="item.name"
                  :value="item.key" />
              </el-select>
              <el-input
                v-model="form.handleStatusContent"
                :disabled="form.currentHandleStatus === 'trace'"
                placeholder="请输入内容"
                type="textarea"
                :rows="5"
                clearable />
            </div>
          </div>
        </global-info-item>
      </global-info>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef, 2)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'HandUnitSuperNew' }
</script>
<script setup>
import api from '@/api'
import { ElMessage } from 'element-plus'
import { reactive, ref, onMounted } from 'vue'

const props = defineProps({
  id: { type: String, default: '' },
  suggestionId: { type: String, default: '' }
})
const emit = defineEmits(['callback'])
const formRef = ref()
const form = reactive({
  hasRead: null, //
  handleOfficeType: '',
  firstReadTime: '',
  adjustStopDate: '',
  answerStopDate: '',
  flowHandleOfficeId: [],
  currentHandleStatus: '',
  suggestionHandleStatus: '',
  handleStatusContent: '',
  hasConfirm: '',
  confirmTime: ''
})
const filterOfficeId = ref([])
const rules = reactive({})
const suggestionHandleStatus = ref([])
const handleOfficeType = ref([
  { key: 'main', name: '主办' },
  { key: 'assist', name: '协办' },
  { key: 'publish', name: '分办' }
])
const isPreAssign = ref(false)

onMounted(() => {
  globalReadConfig()
  dictionaryData()
  handlingPortionList()
  if (props.id) {
    handlingPortionInfo(props.id)
  }
})
const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: ['proposal_enable_pre_assign'] })
  isPreAssign.value = data?.proposal_enable_pre_assign === 'true'
}
const handlingPortionList = async () => {
  var params = {
    pageNo: 1,
    pageSize: 9999,
    query: {
      suggestionId: props.suggestionId
    }
  }
  const { data } = await api.handlingPortionList(params)
  filterOfficeId.value = data?.map((v) => v.flowHandleOfficeId).filter((v) => v !== props.id) || []
}
const handlingPortionInfo = async (id) => {
  const { data } = await api.handlingPortionInfo({ detailId: id })
  form.hasRead = data.hasRead
  form.handleOfficeType = data.handleOfficeType
  form.firstReadTime = data.firstReadTime
  form.adjustStopDate = data.adjustStopDate
  form.answerStopDate = data.answerStopDate
  form.flowHandleOfficeId = [data.flowHandleOfficeId]
  form.currentHandleStatus = data.currentHandleStatus
  form.hasConfirm = data.hasConfirm
  form.confirmTime = data.confirmTime
  form.suggestionHandleStatus = data.suggestionHandleStatus?.value || ''
  form.handleStatusContent = data.handleStatusContent || ''
  if (['main', 'assist'].includes(form.handleOfficeType)) {
    handleOfficeType.value = [
      { key: 'main', name: '主办' },
      { key: 'assist', name: '协办' }
    ]
  } else if (['publish'].includes(form.handleOfficeType)) {
    handleOfficeType.value = [{ key: 'publish', name: '分办' }]
  }
}
const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['suggestion_handle_status'] })
  var { data } = res
  suggestionHandleStatus.value = data.suggestion_handle_status
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}

const globalJson = async (type) => {
  const { code } = await api.globalJson(props.id ? '/cppcc/handlingPortion/edit' : '/cppcc/handlingPortion/add', {
    form: {
      id: props.id,
      suggestionId: props.suggestionId,
      hasRead: form.hasRead,
      handleOfficeType: form.handleOfficeType,
      firstReadTime: form.firstReadTime,
      adjustStopDate: form.adjustStopDate,
      answerStopDate: form.answerStopDate,
      flowHandleOfficeId: form.flowHandleOfficeId.join(','),
      answers: form.answers,
      hasConfirm: form.hasConfirm,
      confirmTime: form.confirmTime,
      currentHandleStatus: form.currentHandleStatus,
      suggestionHandleStatus: form.suggestionHandleStatus,
      handleStatusContent: form.handleStatusContent
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}

const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.HandUnitSuperNew {
  width: 860px;
  height: 100%;

  .global-info {
    padding-bottom: 12px;

    .global-info-item {
      .global-info-label {
        width: 160px;
      }

      .global-info-content {
        width: calc(100% - 160px);
      }
    }

    .zy-el-select {
      min-width: 220px;
    }

    .transactDetail {
      .global-info-content {
        width: calc(100% - 160px);
        padding: 0;

        & > span {
          width: 100%;
          height: 100%;
        }

        .transactDetailBody {
          width: 100%;
          height: 100%;
          display: flex;

          .transactDetailInfo {
            width: calc(100% - 180px);
            padding: var(--zy-distance-five) var(--zy-distance-four);
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .zy-el-select {
              margin-bottom: var(--zy-distance-five);
            }
          }

          .transactDetailButton {
            width: 180px;
            border-left: 1px solid var(--zy-el-border-color-lighter);
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: var(--zy-distance-five) var(--zy-distance-four);

            .zy-el-button {
              --zy-el-button-size: var(--zy-height-secondary);
              border-radius: var(--el-border-radius-small);
              margin: 0;
            }

            .zy-el-button + .zy-el-button {
              margin-top: var(--zy-distance-five);
            }
          }
        }
      }
    }
  }
}
</style>
