<template>
  <div class="AllSuggest">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" :buttonNumber="2" ref="queryRef">
      <template #search>
        <el-popover placement="bottom" title="您可以查找：" trigger="hover" :width="250">
          <div class="tips-UL">
            <div>提案名称</div>
            <div>提案编号</div>
            <div>提案人<strong>(名称前加 n 或 N)</strong></div>
            <div>全部办理单位<strong>(名称前加 d 或 D)</strong></div>
            <div>主办单位<strong>(名称前加 m 或 M)</strong></div>
            <div>协办单位<strong>(名称前加 j 或 J)</strong></div>
          </div>
          <template #reference>
            <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
          </template>
        </el-popover>
        <el-checkbox v-model="isContainMerge" @change="handleChange" label="含被并提案" />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #title="scope">
            <el-link @click="handleDetails(scope.row)" type="primary" class="AllSuggestIsMajorSuggestionLink">
              <span v-if="scope.row.isMajorSuggestion" class="SuggestMajorIcon"></span>
              <span v-if="scope.row.isOpen" class="SuggestOpenIcon"></span>
              {{ scope.row.title }}
            </el-link>
          </template>
          <template #mainHandleOffices="scope">
            <template v-if="scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0">
              {{scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
            <template v-else>
              {{scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
          </template>
          <template #assistHandleOffices="scope">
            <template v-if="scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0">
              {{scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
            <template v-else>
              {{scope.row.assistHandleVoList?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
          </template>
          <!-- <template #publishHandleOffices="scope">
            {{scope.row.publishHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}
          </template> -->
          <template #isMainMergeProposal="scope">
            <div v-if="scope.row.isMainMergeProposal === 1">主并提案</div>
            <div v-if="scope.row.isMainMergeProposal === 0">被并提案</div>
          </template>
        </xyl-global-table>
        <xyl-global-table-button :data="tableButtonList" :elWhetherDisabled="handleElWhetherDisabled"
          @buttonClick="handleCommand" :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="所有提案" :exportId="exportExcelShow ? [] : exportId"
        :params="exportExcelShow ? { ids: exportId, isContainMerge: 1 } : exportParams" module="proposalExportExcel"
        tableId="id_prop_proposal" @excelCallback="callback" :handleExcelData="handleExcelData"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="isShow" name="案号对调">
      <SuggestExchange @callback="callback"></SuggestExchange>
    </xyl-popup-window>
    <xyl-popup-window v-model="show" name="联名人管理">
      <JoinUserManage :id="id"></JoinUserManage>
    </xyl-popup-window>
    <xyl-popup-window v-model="showUnitSuperEdit" name="办理单位管理">
      <HandUnitSuperList :suggestionId="unitSuperEditId" @callback="callback"></HandUnitSuperList>
    </xyl-popup-window>
    <xyl-popup-window v-model="showUnitSuperWayEdit" name="办理方式管理">
      <HandWaySuperEdit :suggestionId="unitSuperEditId" @callback="callback"></HandWaySuperEdit>
    </xyl-popup-window>

    <xyl-popup-window v-model="showCommunication" name="办理单位与委员沟通情况">
      <CommunicationSituation :id="communicationId" type></CommunicationSituation>
    </xyl-popup-window>
    <xyl-popup-window v-model="showSegreeSatisfaction" name="满意度测评管理">
      <SegreeSatisfactionList :id="segreeSatisfactionId" type></SegreeSatisfactionList>
    </xyl-popup-window>
    <xyl-popup-window v-model="sortShow" name="提案重新编号">
      <SuggestSerialNumber @callback="callback"></SuggestSerialNumber>
    </xyl-popup-window>
    <suggestPrint v-if="elPrintWhetherShow" :params="printParams" @callback="callback"></suggestPrint>
  </div>
</template>
<script>
export default { name: 'AllSuggest' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord, suggestExportContent, suggestExportAnswer } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
import SuggestExchange from './component/SuggestExchange'
import JoinUserManage from './component/JoinUserManage'
import SuggestSerialNumber from './component/SuggestSerialNumber'
import suggestPrint from '@/components/suggestPrint/suggestPrint'
import HandUnitSuperList from '../SuperEdit/HandUnitSuperList.vue'
import HandWaySuperEdit from '../SuperEdit/HandWaySuperEdit.vue'
import SegreeSatisfactionList from '../SuperEdit/SegreeSatisfactionList.vue'
import CommunicationSituation from '@/views/SuggestDetail/CommunicationSituation/CommunicationSituation.vue'
const buttonList = [
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'exportContent', name: '导出正文', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' },
  { id: 'exportAnswer', name: '导出答复件', type: 'primary', has: '' },
  { id: 'print', name: '打印', type: 'primary', has: '' },
  { id: 'del', name: '删除', type: '', has: 'del' },
  { id: 'exchange', name: '案号对调', type: 'primary', has: 'exchange' },
  { id: 'emphasis', name: '推荐重点提案', type: 'primary', has: 'emphasis' },
  // { id: 'noEmphasis', name: '撤销重点提案', type: 'primary', has: 'no_emphasis' },
  { id: 'open', name: '设置公开提案', type: 'primary', has: 'open' },
  // { id: 'noOpen', name: '取消公开提案', type: 'primary', has: 'no_open' },
  { id: 'excellent', name: '设置优秀提案', type: 'primary', has: 'excellent' },
  { id: 'leadership', name: '设置领导批示提案', type: 'primary', has: '' },
  { id: 'renumber', name: '提案重新编号', type: 'primary', has: 'renumber' }
  // { id: 'noExcellent', name: '取消优秀提案', type: 'primary', has: 'no_excellent' }
]
const tableButtonList = [
  { id: 'edit', name: '编辑', width: 80, has: 'edit' },
  { id: 'joinUser', name: '联名人管理', width: 110, has: 'join_user', whetherDisabled: true },
  { id: 'superEdit', name: '超级修改', width: 110, has: 'superEdit' },
  { id: 'handUnitSuperWayEdit', name: '办理方式管理', width: 110, has: 'handUnitSuperWayEdit' },
  { id: 'HandWaySuperEdit', name: '办理单位管理', width: 110, has: 'HandWaySuperEdit' },
  { id: 'communication', name: '沟通情况管理', width: 110, has: 'communication' },
  { id: 'segreeSatisfaction', name: '满意度测评管理', width: 110, has: 'segreeSatisfaction' }
]
const id = ref('')
const show = ref(false)
const isShow = ref(false)
const printParams = ref({})
const elPrintWhetherShow = ref(false)
const unitSuperEditId = ref('')
const showUnitSuperEdit = ref(false)
const communicationId = ref('')
const showCommunication = ref(false)
const segreeSatisfactionId = ref('')
const showSegreeSatisfaction = ref(false)
const showUnitSuperWayEdit = ref(false)
const exportExcelShow = ref(false)
const sortShow = ref(false)
const isContainMerge = ref(true)
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  handleDel,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: 'id_prop_proposal', tableApi: 'suggestionList', delApi: 'suggestionDel' })

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds') || '[]')
  if (suggestIds.length) {
    tableQuery.value = { isContainMerge: isContainMerge.value ? 1 : 0, ids: suggestIds }
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    tableQuery.value = { isContainMerge: isContainMerge.value ? 1 : 0 }
    handleQuery()
  }
})
const handleReset = () => {
  keyword.value = ''
  isContainMerge.value = false
  tableQuery.value = { isContainMerge: isContainMerge.value ? 1 : 0 }
  handleQuery()
}
const handleChange = () => {
  tableQuery.value = { isContainMerge: isContainMerge.value ? 1 : 0 }
}
const handleExcelData = (_item) => {
  _item.forEach(v => {
    if (!v.mainHandleOffices) {
      v.mainHandleOffices = v.publishHandleOffices
    }
  })
}
const handleButton = (isType, params) => {
  switch (isType) {
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'exportContent':
      suggestExportContent(handleGetParams())
      break
    case 'exportAnswer':
      suggestExportAnswer(handleGetParams())
      break
    case 'print':
      handleSuggestPrint(handleGetParams())
      break
    case 'export':
      if (tableDataArray.value.length) {
        ElMessageBox.confirm('是否同步导出被并提案?', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        })
          .then(() => {
            exportExcelShow.value = true
            handleExportExcel()
          })
          .catch(() => {
            exportExcelShow.value = false
            handleExportExcel()
          })
      } else {
        exportExcelShow.value = false
        handleExportExcel()
      }
      break
    case 'exchange':
      isShow.value = !isShow.value
      break
    case 'emphasis':
      handleMajor(1)
      break
    case 'noEmphasis':
      handleMajor(0)
      break
    case 'open':
      handleOpen(1)
      break
    case 'noOpen':
      handleOpen(0)
      break
    case 'excellent':
      handleExcellent(1)
      break
    case 'noExcellent':
      handleExcellent(0)
      break
    case 'del':
      handleDel('提案')
      break
    case 'leadership':
      setLeadership()
      break
    case 'renumber':
      sortShow.value = true
      break
    default:
      break
  }
}
const setLeadership = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作将选中的提案设置领导批示提案, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      api.updateLeaderMark({ proposalIds: tableDataArray.value.map(v => v.id).join(','), passStatus: '1' }).then(res => {
        if (res.code == 200) {
          ElMessage({ type: 'success', message: res.message })
          tableRefReset()
          handleQuery()
        }
      })
    }).catch(() => { ElMessage({ type: 'info', message: '已取消设置' }) })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    case 'joinUser':
      id.value = row.id
      show.value = true
      break
    case 'superEdit':
      qiankunMicro.setGlobalState({
        openRoute: { name: '超级修改', path: '/proposal/SuperEdit', query: { id: row.id } }
      })
      break
    case 'handUnitSuperWayEdit':
      unitSuperEditId.value = row.id
      showUnitSuperWayEdit.value = true
      break
    case 'HandWaySuperEdit':
      unitSuperEditId.value = row.id
      showUnitSuperEdit.value = true
      break
    case 'communication':
      communicationId.value = row.id
      showCommunication.value = true
      break
    case 'segreeSatisfaction':
      segreeSatisfactionId.value = row.id
      showSegreeSatisfaction.value = true
      break
    default:
      break
  }
}
const handleElWhetherDisabled = (row, isType) => {
  if (isType === 'joinUser') {
    return !row.isJoinProposal
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } }
  })
}
const handleEdit = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '编辑提案', path: '/proposal/SubmitSuggest', query: { id: item.id } }
  })
}
const handleSuggestPrint = async (data) => {
  if (data.selectId.length) {
    ElMessageBox.confirm('此操作将打印当前选中的提案, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        printParams.value = { ids: data.selectId }
        elPrintWhetherShow.value = true
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消打印' })
      })
  } else {
    ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件打印所有数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        printParams.value = data.params
        elPrintWhetherShow.value = true
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消打印' })
      })
  }
}
const callback = () => {
  tableRefReset()
  handleQuery()
  isShow.value = false
  exportShow.value = false
  elPrintWhetherShow.value = false
  showUnitSuperEdit.value = false
  showCommunication.value = false
  showUnitSuperWayEdit.value = false
  sortShow.value = false
}
// 公开
const handleOpen = (type) => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将${type ? '' : '取消'}公开选中的提案, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionOpen(type)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: `已取消${type ? '公开' : '操作'}` })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionOpen = async (type) => {
  const { code } = await api.suggestionOpen({ ids: tableDataArray.value.map((v) => v.id), isOpen: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: `${type ? '公开' : '取消'}成功` })
    tableRefReset()
    handleQuery()
  }
}
// 重点
const handleMajor = (type) => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将${type ? '选中的提案推荐为重点提案' : '撤销当前选中的重点提案'}, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionMajor(type)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: `已取消${type ? '推荐' : '撤销'}` })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionMajor = async (type) => {
  const { code } = await api.suggestionMajor({ ids: tableDataArray.value.map((v) => v.id), isMajorSuggestion: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: `${type ? '推荐' : '撤销'}成功` })
    tableRefReset()
    handleQuery()
  }
}
// 优秀
const handleExcellent = (type) => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将${type ? '选中的提案推荐为优秀提案' : '撤销当前选中的优秀提案'}, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionExcellent(type)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: `已取消${type ? '推荐' : '撤销'}` })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionExcellent = async (type) => {
  const { code } = await api.suggestionExcellent({ ids: tableDataArray.value.map((v) => v.id), isExcellent: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: `${type ? '推荐' : '撤销'}成功` })
    tableRefReset()
    handleQuery()
  }
}
</script>
<style lang="scss">
.AllSuggest {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .xyl-search {
    .zy-el-checkbox {
      margin-left: 10px;
    }
  }

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }

  .AllSuggestIsMajorSuggestionLink {
    .zy-el-link__inner {
      .SuggestOpenIcon {
        width: 40px;
        height: 19px;
        display: inline-block;
        background: url('@/assets/img/suggest_open_icon.png') no-repeat;
        background-size: 100% 100%;
        margin-right: 6px;
      }

      .SuggestMajorIcon {
        width: 40px;
        height: 19px;
        display: inline-block;
        background: url('@/assets/img/suggest_major_icon.png') no-repeat;
        background-size: 100% 100%;
        margin-right: 6px;
      }
    }
  }

  .suggestPrint {
    width: 790px;
    position: fixed;
    top: -100%;
    left: -100%;
  }
}
</style>
