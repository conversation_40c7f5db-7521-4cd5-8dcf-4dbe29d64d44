<template>
  <Teleport to="body">
    <Transition name="modal-fade">
      <div v-if="modelValue" class="custom-satisfaction-modal" @click="handleMaskClick">
        <div class="modal-container" @click.stop>
          <div class="modal-header">
            <div class="modal-title">
              <el-icon class="title-icon">
                <Star />
              </el-icon>
              满意度调查
            </div>
            <div class="modal-close" @click="handleClose">
              <el-icon>
                <Close />
              </el-icon>
            </div>
          </div>
          <div class="modal-body">
            <SatisfactionSurvey :data="data" @callback="handleCallback"></SatisfactionSurvey>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script>
export default { name: 'CustomSatisfactionModal' }
</script>

<script setup>
import { Close, Star } from '@element-plus/icons-vue'
import SatisfactionSurvey from './SatisfactionSurvey.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleMaskClick = () => {
  emit('update:modelValue', false)
}

const handleCallback = () => {
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.custom-satisfaction-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);

  .modal-container {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    transform: scale(0.9);
    opacity: 0;
    animation: modalSlideIn 0.3s ease-out forwards;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px;
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      .modal-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          font-size: 20px;
          color: #ffd700;
        }
      }

      .modal-close {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
        background: rgba(255, 255, 255, 0.1);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.1);
        }

        .el-icon {
          font-size: 18px;
          color: white;
        }
      }
    }

    .modal-body {
      max-height: calc(90vh - 100px);
      overflow-y: auto;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        
        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }
}

@keyframes modalSlideIn {
  0% {
    transform: scale(0.9) translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

// 过渡动画
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: all 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-from .modal-container,
.modal-fade-leave-to .modal-container {
  transform: scale(0.9) translateY(-20px);
}

// 响应式设计
@media (max-width: 768px) {
  .custom-satisfaction-modal {
    padding: 16px;
    
    .modal-container {
      width: 100%;
      max-width: 100%;
      max-height: calc(100vh - 32px);
      
      .modal-header {
        padding: 16px 20px;
        
        .modal-title {
          font-size: 16px;
        }
      }
      
      .modal-body {
        max-height: calc(100vh - 132px);
      }
    }
  }
}

@media (max-width: 480px) {
  .custom-satisfaction-modal {
    padding: 8px;
    
    .modal-container {
      .modal-header {
        padding: 12px 16px;
        
        .modal-title {
          font-size: 14px;
        }
      }
    }
  }
}
</style> 