<template>
  <div class="GlobalChatBox" @contextmenu.prevent>
    <div class="GlobalChat">
      <GlobalChatNav v-model="navId" :chatTotal="chatTotal" @change="handleChange"></GlobalChatNav>
      <div class="GlobalChatBody" v-show="navId === '1'">
        <GlobalChatView ref="chatViewRef" v-model="chatId" :chatList="chatList" @time="handleTime"
          @refresh="handleRefresh" @send="handleSend"></GlobalChatView>
      </div>
      <div class="GlobalChatBody" v-show="navId === '2'">
        <GlobalChatAddressBook ref="addressBookRef" @send="handleSend"></GlobalChatAddressBook>
      </div>
      <div class="GlobalChatBody" v-show="navId === '3'">
        <GlobalChatGroup ref="groupRef" @send="handleSend"></GlobalChatGroup>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'GlobalChat' }
</script>
<script setup>
import { ref, computed, watch, onUnmounted, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'
import { format } from 'common/js/time.js'
import * as RongIMLib from '@rongcloud/imlib-next'
import { handleChatId, handleChatList } from './js/ChatMethod.js'
const GlobalChatNav = defineAsyncComponent(() => import('./components/GlobalChatNav.vue'))
const GlobalChatView = defineAsyncComponent(() => import('./components/GlobalChatView.vue'))
const GlobalChatAddressBook = defineAsyncComponent(() => import('./components/GlobalChatAddressBook.vue'))
const GlobalChatGroup = defineAsyncComponent(() => import('./components/GlobalChatGroup.vue'))
const store = useStore()
const emit = defineEmits(['callback'])
const rongCloudToken = computed(() => store.getters.getRongCloudToken)
const navId = ref('1')
const chatId = ref('')
const chatList = ref([])
const chatTotal = computed(() => {
  let total = 0
  for (let index = 0; index < chatList.value.length; index++) {
    const item = chatList.value[index]
    if (item.isNotInform !== 1) total += item.count
  }
  emit('callback', total)
  return total
})
const chatViewRef = ref()
const addressBookRef = ref()
const groupRef = ref()
const refreshTime = ref('')
const chatObjectInfo = ref([])
const rongCloudLink = async (token) => {
  const Events = RongIMLib.Events
  RongIMLib.addEventListener(Events.CONNECTED, () => {
    console.log('链接成功')
    handleEventListener()
    getRongCloudSessionList()
  })
  await RongIMLib.connect(token)
}
// const handleMessages = async (conversationType, targetId) => {
//   const res = await RongIMLib.getConversation({ conversationType, targetId })
//   return res
// }
const handleEventListener = async () => {
  const Events = RongIMLib.Events
  RongIMLib.addEventListener(Events.MESSAGES, async (evt) => {
    console.log('新消息来了', evt.messages)
    const newData = []
    const newDataId = []
    for (let index = 0; index < evt.messages.length; index++) {
      const item = evt.messages[index]
      if (!newDataId?.includes(item.targetId)) {
        newDataId.push(item.targetId)
        // const { code, data } = await handleMessages(item.conversationType, item.targetId)
        const { code, data } = await RongIMLib.getConversation({
          conversationType: item.conversationType,
          targetId: item.targetId
        })
        if (data?.targetId === chatId.value) {
          chatViewRef.value?.getNewestMessages()
          if (!code) newData.push({ ...data, unreadMessageCount: 0 })
        } else {
          if (!code) newData.push(data)
        }
      }
    }
    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')
    chatObjectInfo.value = await handleChatId(newData, isRefresh, chatObjectInfo.value)
    chatList.value = await handleChatList(newData, [], chatList.value, chatObjectInfo.value)
    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')
  })
}
const handleTime = (type) => {
  refreshTime.value = type ? format(new Date(), 'YYYY-MM-DD HH') : ''
}
const handleChange = (id) => {
  if (id === '2') addressBookRef.value?.refresh()
  if (id === '3') groupRef.value?.refresh()
}
const handleRefresh = (type, data) => {
  if (type === 'del') chatList.value = chatList.value.filter((v) => v.id !== data.id)
  getRongCloudSessionList()
}
const getRongCloudSessionList = async () => {
  const { code, data, msg } = await RongIMLib.getConversationList()
  if (code === 0) {
    console.log('获取会话列表成功', data)
    const newTemporary = []
    for (let index = 0; index < chatList.value.length; index++) {
      const item = chatList.value[index]
      if (item.isTemporary) newTemporary.push(item)
    }
    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')
    chatObjectInfo.value = await handleChatId(data, isRefresh, chatObjectInfo.value)
    chatList.value = await handleChatList(data, newTemporary, [], chatObjectInfo.value)
    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')
  } else {
    console.log('获取会话列表失败: ', code, msg)
  }
}
const handleSend = (data) => {
  const idList = chatList.value.map((v) => v.id)
  if (idList?.includes(data.id)) {
    chatId.value = data.id
    navId.value = '1'
  } else {
    chatId.value = data.id
    chatList.value = [data, ...chatList.value]
    navId.value = '1'
  }
}
onUnmounted(() => {
  const Events = RongIMLib.Events
  RongIMLib.removeEventListeners(Events.MESSAGES)
  RongIMLib.removeEventListeners(Events.CONNECTED)
  RongIMLib.disconnect().then(() => {
    console.log('成功断开')
  })
})
watch(
  () => rongCloudToken.value,
  () => {
    if (rongCloudToken.value) rongCloudLink(rongCloudToken.value)
  },
  { immediate: true }
)
</script>
<style lang="scss">
.GlobalChatBox {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .GlobalChat {
    width: 980px;
    min-height: 560px;
    max-height: 720px;
  }
}

.GlobalChat {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;

  .zy-el-image {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .GlobalChatBody {
    width: calc(100% - 72px);
    height: 100%;

    .GlobalChatView {
      .GlobalChatViewList {
        .GlobalChatViewListHead {
          height: 56px;
        }

        .GlobalChatViewMessagesList {
          height: calc(100% - 92px);
        }
      }

      .GlobalChatViewDrag {
        height: 56px;
      }

      .GlobalChatWindow {
        .GlobalChatWindowTitle {
          height: 56px;

          .GlobalChatWindowMore {
            margin: 0;
          }
        }

        .GlobalChatWindowScroll {
          height: calc(100% - 222px);

          &.GlobalChatWindowNoChat {
            height: calc(100% - (56px + var(--zy-height)));
          }
        }

        .setting-popup-window {
          height: calc(100% - 56px);
          top: 56px;
        }
      }
    }

    .GlobalChatAddressBook {
      .GlobalChatAddressBookList {
        .GlobalChatAddressBookInput {
          height: 56px;
        }

        .GlobalChatAddressBookScrollbar {
          height: calc(100% - 56px);
        }
      }

      .GlobalChatAddressBookDrag {
        height: 56px;
      }
    }

    .GlobalChatGroup {
      .GlobalChatGroupList {
        .GlobalChatGroupInput {
          height: 56px;
        }

        .GlobalChatGroupScrollbar {
          height: calc(100% - 56px);
        }
      }

      .GlobalChatGroupDrag {
        height: 56px;
      }
    }
  }
}
</style>
