{"ast": null, "code": "import { useRouter } from 'vue-router';\nvar __default__ = {\n  name: 'HomeLayout'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var router = useRouter();\n    var search = function search() {\n      console.log('搜索');\n    };\n    var openLogin = function openLogin() {\n      router.push({\n        name: 'LoginView'\n      });\n    };\n    var __returned__ = {\n      router,\n      search,\n      openLogin,\n      get useRouter() {\n        return useRouter;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["useRouter", "__default__", "name", "router", "search", "console", "log", "openLogin", "push"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/HomeLayout/HomeLayout.vue"], "sourcesContent": ["<template>\r\n  <div class=\"HomeLayout\">\r\n    <div class=\"home-header\">\r\n      <img class=\"home-logo\" src=\"../img/home_toptext_bg.png\" alt=\"logo\" />\r\n      <div class=\"home-header-right\">\r\n        <div class=\"search-box\">\r\n          <input type=\"text\" placeholder=\"请输入搜索内容\" />\r\n          <button class=\"search-btn\" @click=\"search\">\r\n            <img class=\"search-icon\" src=\"../img/search_icon.png\" alt=\"搜索\" />\r\n          </button>\r\n        </div>\r\n        <button class=\"login-btn\" @click=\"openLogin\">\r\n          <img src=\"../img/login_btn_bg.png\" alt=\"登录首页\" />\r\n          <span style=\"color: #003399;\">登录首页</span>\r\n        </button>\r\n        <button class=\"mine-btn\" @click=\"openLogin\">\r\n          <img src=\"../img/mine_btn_bg.png\" alt=\"我的\" />\r\n          <span>\r\n            <img class=\"mine-icon\" src=\"../img/mine_icon.png\" alt=\"我的\" />\r\n            我的\r\n          </span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"home-content\">\r\n      <div class=\"menu-list\">\r\n        <div class=\"menu-item menu-bg1 u-top\" @click=\"openLogin\">\r\n          <img class=\"menu-icon\" src=\"../img/menu_icon1.png\" alt=\"我的工作\" style=\"margin-top: -20px;margin-left: 35px;\" />\r\n          <div class=\"menu-title\" style=\"margin-left: 35px;\">我的工作</div>\r\n        </div>\r\n        <div class=\"menu-item menu-bg2 u-bottom\" @click=\"openLogin\" style=\"margin-left: -65px;height: 400px;\">\r\n          <img class=\"menu-icon\" src=\"../img/menu_icon2.png\" alt=\"我的待办\" style=\"margin-top: -65px;\" />\r\n          <div class=\"menu-title\">我的待办</div>\r\n        </div>\r\n        <div class=\"menu-item menu-bg3 u-bottom\" @click=\"openLogin\" style=\"margin-left: -90px;height: 400px;\">\r\n          <img class=\"menu-icon\" src=\"../img/menu_icon3.png\" alt=\"综合应用\" style=\"margin-top: -65px;\" />\r\n          <div class=\"menu-title\">综合应用</div>\r\n        </div>\r\n        <div class=\"menu-item menu-bg4 u-top\" @click=\"openLogin\" style=\"margin-left: -70px;\">\r\n          <img class=\"menu-icon\" src=\"../img/menu_icon4.png\" alt=\"其他应用\" style=\"margin-top: -20px;margin-right: 50px;\" />\r\n          <div class=\"menu-title\" style=\"margin-right: 50px;\">其他应用</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'HomeLayout' }\r\n</script>\r\n<script setup>\r\nimport { useRouter } from 'vue-router'\r\nconst router = useRouter()\r\nconst search = () => {\r\n  console.log('搜索')\r\n}\r\nconst openLogin = () => {\r\n  router.push({ name: 'LoginView' })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.HomeLayout {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: url(\"../img/home_layout_bg.png\") no-repeat;\r\n  background-size: 100% 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .home-header {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24px 40px 0 40px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    z-index: 2;\r\n    flex-shrink: 0;\r\n\r\n    .home-logo {\r\n      height: 74px;\r\n    }\r\n\r\n    .home-header-right {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n\r\n      .search-box {\r\n        display: flex;\r\n        align-items: center;\r\n        border-radius: 20px;\r\n        padding: 0 0 0 8px;\r\n        height: 36px;\r\n        border: 1px solid #FFFFFF;\r\n        width: 350px;\r\n\r\n        input {\r\n          border: none;\r\n          outline: none;\r\n          height: 100%;\r\n          padding: 0 8px;\r\n          border-radius: 20px 0 0 20px;\r\n          background: rgb(0, 0, 0, 0);\r\n          width: calc(100% - 55px);\r\n          color: #fff;\r\n\r\n          &::placeholder {\r\n            color: #fff;\r\n            opacity: 1;\r\n          }\r\n        }\r\n\r\n        .search-btn {\r\n          background: url(\"../img/search_btn_bg.png\") no-repeat center/cover;\r\n          border: none;\r\n          width: 55px;\r\n          height: 36px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n\r\n          .search-icon {\r\n            width: 18px;\r\n            height: 18px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .login-btn,\r\n      .mine-btn {\r\n        background: none;\r\n        border: none;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          height: 39px;\r\n        }\r\n\r\n        span {\r\n          position: absolute;\r\n          left: 0;\r\n          width: 100%;\r\n          text-align: center;\r\n          color: #fff;\r\n          font-size: 14px;\r\n          line-height: 39px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n\r\n      .mine-btn .mine-icon {\r\n        width: 20px;\r\n        height: 20px;\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .home-content {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: flex-start;\r\n    padding-top: 35vh;\r\n    position: relative;\r\n    z-index: 1;\r\n\r\n    .menu-list {\r\n      width: calc(100% - 240px); // 两边各120px\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: flex-start;\r\n      position: relative;\r\n    }\r\n\r\n    .menu-item {\r\n      flex: 1 1 0;\r\n      max-width: 475px;\r\n      min-width: 220px;\r\n      aspect-ratio: 475/450; // 保持比例\r\n      border-radius: 24px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      position: relative;\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      // box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.15);\r\n      cursor: pointer;\r\n      transition: transform 0.2s;\r\n      margin: 0;\r\n\r\n      &:hover {\r\n        // transform: translateY(-8px) scale(1.03);\r\n        // box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.18);\r\n      }\r\n\r\n      .menu-icon {\r\n        width: 82px;\r\n        height: 82px;\r\n      }\r\n\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 28px;\r\n        font-weight: bold;\r\n        margin-top: 5px;\r\n      }\r\n    }\r\n\r\n    // U形布局\r\n    .u-top {\r\n      margin-top: 0;\r\n    }\r\n\r\n    .u-bottom {\r\n      margin-top: 50px; // 第二、第三个往下\r\n    }\r\n\r\n    .menu-bg1 {\r\n      background-image: url('../img/menu_bg1.png');\r\n    }\r\n\r\n    .menu-bg2 {\r\n      background-image: url('../img/menu_bg2.png');\r\n    }\r\n\r\n    .menu-bg3 {\r\n      background-image: url('../img/menu_bg3.png');\r\n    }\r\n\r\n    .menu-bg4 {\r\n      background-image: url('../img/menu_bg4.png');\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAkDA,SAASA,SAAS,QAAQ,YAAY;AAHtC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAa,CAAC;;;;;IAIrC,IAAMC,MAAM,GAAGH,SAAS,CAAC,CAAC;IAC1B,IAAMI,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;MACnBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;IACnB,CAAC;IACD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtBJ,MAAM,CAACK,IAAI,CAAC;QAAEN,IAAI,EAAE;MAAY,CAAC,CAAC;IACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}