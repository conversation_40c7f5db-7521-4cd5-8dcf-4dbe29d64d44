<template>
  <div class="SubmitSegreeSatisfaction">
    <div class="SubmitSegreeSatisfactionName">提案办理情况征求意见表</div>
    <global-info>
      <global-info-item label="提案标题">{{ details.title }}</global-info-item>
      <global-info-line>
        <global-info-item label="提案编号">{{ details.serialNumber }}</global-info-item>
        <global-info-item label="领衔委员">{{ details.suggestUserName }}</global-info-item>
      </global-info-line>
      <global-info-item label="单位及职务">{{ details.submitUserInfo?.position }}</global-info-item>
      <global-info-item label="办理单位">
        <div v-for="item in details.handleOffices"
             :key="item.handleOfficeId">
          {{ item.handleOfficeType === 'main' ? '主办' : item.handleOfficeType === 'assist' ? '协办' : '分办' }}：{{
            item.handleOfficeName }}
        </div>
      </global-info-item>
      <global-info-item label="联系沟通情况"
                        class="SubmitSegreeSatisfactionInfo">
        <global-info-item label="联系沟通情况"
                          class="SubmitSegreeSatisfactionInfo">
          <global-info-item label="电话/电邮沟通"
                            class="SubmitSegreeSatisfactionInput">
            <el-input v-model="mobileCommunication"
                      placeholder="请输入内容"
                      clearable />
          </global-info-item>
          <global-info-item label="信函沟通"
                            class="SubmitSegreeSatisfactionInput">
            <el-input v-model="letterCommunication"
                      placeholder="请输入内容"
                      clearable />
          </global-info-item>
          <global-info-item label="当面沟通"
                            class="SubmitSegreeSatisfactionInput">
            <el-input v-model="faceCommunication"
                      placeholder="请输入内容"
                      clearable />
          </global-info-item>
          <global-info-item label="未联系"
                            class="SubmitSegreeSatisfactionInput">
            <el-input v-model="nonCommunication"
                      placeholder="请输入内容"
                      clearable />
          </global-info-item>
        </global-info-item>
        <global-info-item label="办理态度"
                          class="SubmitSegreeSatisfactionRadio">
          <el-radio-group v-model="handleManner">
            <el-radio v-for="item in satisfaction"
                      :key="item.key"
                      :label="item.key">{{ item.name }}</el-radio>
          </el-radio-group>
        </global-info-item>
        <global-info-item label="办理结果"
                          class="SubmitSegreeSatisfactionRadio">
          <el-radio-group v-model="handleResult">
            <el-radio v-for="item in satisfaction"
                      :key="item.key"
                      :label="item.key">{{ item.name }}</el-radio>
          </el-radio-group>
        </global-info-item>
      </global-info-item>
      <global-info-item label="对提案工作的建议"
                        class="SubmitSegreeSatisfactionInput">
        <el-input v-model="content"
                  placeholder="请输入内容"
                  type="textarea"
                  :rows="5"
                  clearable />
      </global-info-item>
    </global-info>
    <div class="SubmitSegreeSatisfactionButton">
      <el-button type="primary"
                 @click="submitForm">提交</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'SubmitSegreeSatisfaction' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const details = ref({})
const id = ref('')
const mobileCommunication = ref('')
const letterCommunication = ref('')
const faceCommunication = ref('')
const nonCommunication = ref('')
const handleManner = ref('')
const handleResult = ref('')
const content = ref('')
const satisfaction = ref([])

onMounted(() => {
  dictionaryData()
  suggestionInfo()
  if (props.type) { suggestionSatisfactionInfo() }
})

const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['suggestion_satisfaction'] })
  var { data } = res
  satisfaction.value = data.suggestion_satisfaction
}
const suggestionInfo = async () => {
  const { data } = await api.suggestionInfo({ detailId: props.id })
  details.value = data
}
const suggestionSatisfactionInfo = async () => {
  const { data } = await api.suggestionSatisfactionInfo({ suggestionId: props.id })
  id.value = data.id
  mobileCommunication.value = data.mobileCommunication
  letterCommunication.value = data.letterCommunication
  faceCommunication.value = data.faceCommunication
  nonCommunication.value = data.nonCommunication
  handleManner.value = data.handleManner
  handleResult.value = data.handleResult
  content.value = data.content
}
const submitForm = () => {
  if (!handleManner.value) return ElMessage({ type: 'warning', message: '请选择办理态度！' })
  if (!handleResult.value) return ElMessage({ type: 'warning', message: '请选择办理结果！' })
  globalJson()
}
const globalJson = async () => {
  const { code } = await api.globalJson(id.value ? '/proposalSatisfaction/edit' : '/proposalSatisfaction/add', {
    form: {
      suggestionId: props.id,
      id: id.value,
      mobileCommunication: mobileCommunication.value,
      letterCommunication: letterCommunication.value,
      faceCommunication: faceCommunication.value,
      nonCommunication: nonCommunication.value,
      handleManner: handleManner.value,
      handleResult: handleResult.value,
      content: content.value,
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '提交成功' })
    emit('callback')
  }
}
const resetForm = () => { emit('callback') }
</script>
<style lang="scss">
.SubmitSegreeSatisfaction {
  width: 990px;
  padding: 0 var(--zy-distance-one);
  padding-top: var(--zy-distance-one);

  .SubmitSegreeSatisfactionName {
    font-size: var(--zy-title-font-size);
    font-weight: bold;
    color: var(--zy-el-color-primary);
    border-bottom: 1px solid var(--zy-el-color-primary);
    text-align: center;
    padding: 20px 0;
    margin-bottom: 20px;
  }

  .global-info {
    padding-bottom: 12px;

    .global-info-item {
      .global-info-label {
        width: 160px;
      }

      .global-info-content {
        width: calc(100% - 160px);
      }
    }

    .SubmitSegreeSatisfactionInfo {
      &>.global-info-item {
        &>.global-info-content {
          padding: 0;
          border: 0;

          &>span {
            width: 100%;
          }
        }
      }
    }

    .SubmitSegreeSatisfactionRadio {

      .global-info-content {
        padding-top: 0;
        padding-bottom: 0;

        &>span {
          width: 100%;
        }
      }
    }

    .SubmitSegreeSatisfactionInput {
      .global-info-content {
        padding: 0;

        &>span {
          width: 100%;

          &>.zy-el-input {
            width: 100%;

            .zy-el-input__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          &>.zy-el-textarea {
            width: 100%;

            .zy-el-textarea__inner {
              box-shadow: 0 0 0 0 !important;
            }
          }
        }
      }
    }
  }

  .SubmitSegreeSatisfactionButton {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--zy-distance-two);

    .zy-el-button+.zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
