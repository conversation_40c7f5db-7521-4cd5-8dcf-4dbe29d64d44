{"ast": null, "code": "function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { ref, watch } from 'vue';\nimport XylMenuItem from './LayoutMenuItem.vue';\nvar __default__ = {\n  name: 'LayoutMenu'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: [String, Number],\n    menuData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  emits: ['update:modelValue', 'select'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    watch(function () {\n      return props.modelValue;\n    }, function () {\n      menuId.value = props.modelValue;\n    });\n    watch(function () {\n      return props.menuData;\n    }, function () {\n      menuData.value = props.menuData;\n    });\n    var isCollapse = ref(false);\n    var menuId = ref(props.modelValue);\n    var menuData = ref(props.menuData);\n    var menuRef = ref(null);\n    var currentOpenMenu = ref('');\n    var handleSelect = function handleSelect(key) {\n      emit('update:modelValue', key);\n      _selectData(props.menuData, key);\n    };\n    var handleOpen = function handleOpen(index) {\n      var isFirstLevel = props.menuData.some(function (item) {\n        return item.id === index;\n      });\n      if (isFirstLevel) {\n        if (currentOpenMenu.value) {\n          var _findTopParent = function findTopParent(items, targetId) {\n            var topParent = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n            var _iterator = _createForOfIteratorHelper(items),\n              _step;\n            try {\n              for (_iterator.s(); !(_step = _iterator.n()).done;) {\n                var item = _step.value;\n                if (item.id === targetId) {\n                  return topParent || item.id;\n                }\n                if (item.children.length > 0) {\n                  var result = _findTopParent(item.children, targetId, item.id);\n                  if (result) return result;\n                }\n              }\n            } catch (err) {\n              _iterator.e(err);\n            } finally {\n              _iterator.f();\n            }\n            return null;\n          };\n          var topParentId = _findTopParent(props.menuData, currentOpenMenu.value);\n          if (topParentId && topParentId !== index) {\n            var _menuRef$value;\n            (_menuRef$value = menuRef.value) === null || _menuRef$value === void 0 || _menuRef$value.close(topParentId);\n          }\n        }\n        if (currentOpenMenu.value && currentOpenMenu.value !== index) {\n          var _menuRef$value2;\n          (_menuRef$value2 = menuRef.value) === null || _menuRef$value2 === void 0 || _menuRef$value2.close(currentOpenMenu.value);\n        }\n      }\n      currentOpenMenu.value = index;\n    };\n    var _selectData = function selectData(data, id) {\n      data.forEach(function (item) {\n        if (item.children.length === 0) {\n          if (item.id === id) {\n            emit('select', item);\n          }\n        } else {\n          _selectData(item.children, id);\n        }\n      });\n    };\n    var __returned__ = {\n      props,\n      emit,\n      isCollapse,\n      menuId,\n      menuData,\n      menuRef,\n      currentOpenMenu,\n      handleSelect,\n      handleOpen,\n      selectData: _selectData,\n      ref,\n      watch,\n      XylMenuItem\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "watch", "XylMenuItem", "__default__", "name", "props", "__props", "emit", "__emit", "modelValue", "menuId", "value", "menuData", "isCollapse", "menuRef", "currentOpenMenu", "handleSelect", "key", "selectData", "handleOpen", "index", "isFirstLevel", "some", "item", "id", "findTopParent", "items", "targetId", "topParent", "arguments", "length", "undefined", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "children", "result", "err", "e", "f", "topParentId", "_menuRef$value", "close", "_menuRef$value2", "data", "for<PERSON>ach"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutView/component/LayoutMenu/LayoutMenu.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LayoutMenu\">\r\n    <el-scrollbar>\r\n      <el-menu :class=\"['xyl-menu-body', isCollapse ? 'xyl-menu-collapse' : 'xyl-menu-unfold']\" :collapse=\"isCollapse\"\r\n        :default-active=\"menuId\" @select=\"handleSelect\" @open=\"handleOpen\" ref=\"menuRef\">\r\n        <template v-for=\"item in menuData\">\r\n          <el-sub-menu :index=\"item.id\" :key=\"item.id\" popper-class=\"xyl-menu-popper\" v-if=\"item.children.length\">\r\n            <template #title>\r\n              <el-image fit=\"cover\" :src=\"item.icon\" v-show=\"isCollapse\" />\r\n              <span class=\"xyl-menu-text\">{{ item.name }}</span>\r\n            </template>\r\n            <xyl-menu-item :menuData=\"item.children\"></xyl-menu-item>\r\n          </el-sub-menu>\r\n          <el-menu-item :index=\"item.id\" :key=\"item.id\" v-if=\"!item.children.length\">\r\n            <el-image fit=\"cover\" :src=\"item.icon\" v-show=\"isCollapse\" />\r\n            <template #title>\r\n              <span class=\"xyl-menu-text\">{{ item.name }}</span>\r\n            </template>\r\n          </el-menu-item>\r\n        </template>\r\n      </el-menu>\r\n    </el-scrollbar>\r\n    <div class=\"xyl-menu-icon\">\r\n      <span @click=\"isCollapse = !isCollapse\">\r\n        <el-icon v-if=\"isCollapse\">\r\n          <ArrowRight />\r\n        </el-icon>\r\n        <el-icon v-if=\"!isCollapse\">\r\n          <ArrowLeft />\r\n        </el-icon>\r\n      </span>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutMenu' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch } from 'vue'\r\nimport XylMenuItem from './LayoutMenuItem.vue'\r\nconst props = defineProps({\r\n  modelValue: [String, Number],\r\n  menuData: { type: Array, default: () => [] }\r\n})\r\nconst emit = defineEmits(['update:modelValue', 'select'])\r\n\r\nwatch(() => props.modelValue, () => { menuId.value = props.modelValue })\r\nwatch(() => props.menuData, () => { menuData.value = props.menuData })\r\n\r\nconst isCollapse = ref(false)\r\nconst menuId = ref(props.modelValue)\r\nconst menuData = ref(props.menuData)\r\nconst menuRef = ref(null)\r\nconst currentOpenMenu = ref('')\r\nconst handleSelect = (key) => {\r\n  emit('update:modelValue', key)\r\n  selectData(props.menuData, key)\r\n}\r\nconst handleOpen = (index) => {\r\n  const isFirstLevel = props.menuData.some(item => item.id === index)\r\n  if (isFirstLevel) {\r\n    if (currentOpenMenu.value) {\r\n      const findTopParent = (items, targetId, topParent = null) => {\r\n        for (const item of items) {\r\n          if (item.id === targetId) {\r\n            return topParent || item.id\r\n          }\r\n          if (item.children.length > 0) {\r\n            const result = findTopParent(item.children, targetId, item.id)\r\n            if (result) return result\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      const topParentId = findTopParent(props.menuData, currentOpenMenu.value)\r\n      if (topParentId && topParentId !== index) {\r\n        menuRef.value?.close(topParentId)\r\n      }\r\n    }\r\n    if (currentOpenMenu.value && currentOpenMenu.value !== index) {\r\n      menuRef.value?.close(currentOpenMenu.value)\r\n    }\r\n  }\r\n  currentOpenMenu.value = index\r\n}\r\nconst selectData = (data, id) => {\r\n  data.forEach(item => {\r\n    if (item.children.length === 0) {\r\n      if (item.id === id) { emit('select', item) }\r\n    } else {\r\n      selectData(item.children, id)\r\n    }\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutMenu {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n\r\n  .zy-el-scrollbar {\r\n    padding-top: 16px;\r\n    padding-left: 20px;\r\n    padding-right: 20px;\r\n    background-color: #fff;\r\n  }\r\n\r\n  &:hover {\r\n    .xyl-menu-icon {\r\n      span {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n\r\n  .xyl-menu-body:not(.zy-el-menu--collapse) {\r\n    width: 200px;\r\n  }\r\n\r\n  .zy-el-menu {\r\n    border-right: 0;\r\n    --zy-el-menu-bg-color: #fff;\r\n\r\n    .zy-el-menu-item.is-active {\r\n      font-weight: bold;\r\n      position: relative;\r\n      background: var(--zy-el-color-primary-light-9);\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 3px;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 24px;\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .zy-el-menu-item,\r\n    .zy-el-sub-menu__title {\r\n      padding-top: 10px !important;\r\n      padding-bottom: 10px !important;\r\n    }\r\n\r\n    .zy-el-sub-menu {\r\n      >.zy-el-menu-item {\r\n        margin: 0;\r\n        padding: 10px 40px;\r\n      }\r\n\r\n      .zy-el-sub-menu .zy-el-menu-item {\r\n        padding: 10px 50px;\r\n      }\r\n    }\r\n\r\n    // 一级菜单\r\n    >.zy-el-menu-item,\r\n    >.zy-el-sub-menu>.zy-el-sub-menu__title {\r\n      padding-left: 20px !important;\r\n    }\r\n\r\n    // 二级菜单\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-menu-item,\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu__title {\r\n      padding-left: 35px !important;\r\n    }\r\n\r\n    // 三级菜单\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-menu-item,\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu__title {\r\n      padding-left: 45px !important;\r\n    }\r\n\r\n    // 四级菜单\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-menu-item,\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu__title {\r\n      padding-left: 55px !important;\r\n    }\r\n  }\r\n\r\n  .xyl-menu-unfold {\r\n\r\n    .zy-el-menu-item,\r\n    >.zy-el-sub-menu>.zy-el-sub-menu__title {\r\n      min-width: 200px;\r\n      height: auto;\r\n      min-height: calc(var(--zy-el-menu-sub-item-height));\r\n      line-height: 1;\r\n\r\n      &:not(.zy-el-sub-menu .zy-el-menu-item) {\r\n        margin: 20px 0 10px 0;\r\n        background: url(\"../../../img/icon_layout_menu_bg.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        color: #fff;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .xyl-menu-text {\r\n          font-size: 16px;\r\n          text-align: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .zy-el-sub-menu__title {\r\n      min-width: 200px;\r\n      height: auto;\r\n      min-height: calc(var(--zy-el-menu-sub-item-height));\r\n      line-height: 1;\r\n      padding-right: 40px;\r\n    }\r\n\r\n    .xyl-menu-text {\r\n      display: inline-block;\r\n      width: 100%;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: var(--zy-distance-four) 0;\r\n      text-overflow: clip;\r\n      white-space: normal;\r\n    }\r\n  }\r\n\r\n  .zy-el-menu--collapse {\r\n    .zy-el-menu-item {\r\n      min-width: auto !important;\r\n      height: calc(var(--zy-el-menu-sub-item-height));\r\n      min-height: auto;\r\n      line-height: calc(var(--zy-el-menu-sub-item-height));\r\n    }\r\n\r\n    .zy-el-sub-menu__title {\r\n      min-width: auto !important;\r\n      height: calc(var(--zy-el-menu-sub-item-height));\r\n      min-height: auto;\r\n      line-height: calc(var(--zy-el-menu-sub-item-height));\r\n      padding-right: calc(var(--zy-el-menu-base-level-padding));\r\n    }\r\n\r\n    .xyl-menu-text {\r\n      text-overflow: initial;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .xyl-menu-icon {\r\n    width: 17px;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    span {\r\n      display: none;\r\n      width: 17px;\r\n      height: 83px;\r\n      line-height: 83px;\r\n      background: url(\"../../../img/menu_icon.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n      background-position: center;\r\n      cursor: pointer;\r\n\r\n      .zy-el-icon {\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.xyl-menu-popper {\r\n  .zy-el-menu {\r\n    width: 200px;\r\n  }\r\n\r\n  .zy-el-menu-item.is-active {\r\n    font-weight: bold;\r\n    position: relative;\r\n    background: var(--zy-el-color-primary-light-9);\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 3px;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .zy-el-menu-item {\r\n    min-width: 200px;\r\n    height: auto;\r\n    min-height: calc(var(--zy-el-menu-sub-item-height));\r\n    line-height: 1;\r\n  }\r\n\r\n  .zy-el-sub-menu__title {\r\n    min-width: 200px;\r\n    height: auto;\r\n    min-height: calc(var(--zy-el-menu-sub-item-height));\r\n    line-height: 1;\r\n    padding-right: 40px;\r\n  }\r\n\r\n  .xyl-menu-text {\r\n    display: inline-block;\r\n    width: 100%;\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding: var(--zy-distance-four) 0;\r\n    text-overflow: clip;\r\n    white-space: normal;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;AAsCA,SAASA,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAChC,OAAOC,WAAW,MAAM,sBAAsB;AAJ9C,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAa,CAAC;;;;;;;;;;;;;;;;IAKrC,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAA4C;IAEzDP,KAAK,CAAC;MAAA,OAAMI,KAAK,CAACI,UAAU;IAAA,GAAE,YAAM;MAAEC,MAAM,CAACC,KAAK,GAAGN,KAAK,CAACI,UAAU;IAAC,CAAC,CAAC;IACxER,KAAK,CAAC;MAAA,OAAMI,KAAK,CAACO,QAAQ;IAAA,GAAE,YAAM;MAAEA,QAAQ,CAACD,KAAK,GAAGN,KAAK,CAACO,QAAQ;IAAC,CAAC,CAAC;IAEtE,IAAMC,UAAU,GAAGb,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMU,MAAM,GAAGV,GAAG,CAACK,KAAK,CAACI,UAAU,CAAC;IACpC,IAAMG,QAAQ,GAAGZ,GAAG,CAACK,KAAK,CAACO,QAAQ,CAAC;IACpC,IAAME,OAAO,GAAGd,GAAG,CAAC,IAAI,CAAC;IACzB,IAAMe,eAAe,GAAGf,GAAG,CAAC,EAAE,CAAC;IAC/B,IAAMgB,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAK;MAC5BV,IAAI,CAAC,mBAAmB,EAAEU,GAAG,CAAC;MAC9BC,WAAU,CAACb,KAAK,CAACO,QAAQ,EAAEK,GAAG,CAAC;IACjC,CAAC;IACD,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAIC,KAAK,EAAK;MAC5B,IAAMC,YAAY,GAAGhB,KAAK,CAACO,QAAQ,CAACU,IAAI,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,EAAE,KAAKJ,KAAK;MAAA,EAAC;MACnE,IAAIC,YAAY,EAAE;QAChB,IAAIN,eAAe,CAACJ,KAAK,EAAE;UACzB,IAAMc,cAAa,GAAG,SAAhBA,aAAaA,CAAIC,KAAK,EAAEC,QAAQ,EAAuB;YAAA,IAArBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;YAAA,IAAAG,SAAA,GAAAC,0BAAA,CACnCP,KAAK;cAAAQ,KAAA;YAAA;cAAxB,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAA0B;gBAAA,IAAfd,IAAI,GAAAW,KAAA,CAAAvB,KAAA;gBACb,IAAIY,IAAI,CAACC,EAAE,KAAKG,QAAQ,EAAE;kBACxB,OAAOC,SAAS,IAAIL,IAAI,CAACC,EAAE;gBAC7B;gBACA,IAAID,IAAI,CAACe,QAAQ,CAACR,MAAM,GAAG,CAAC,EAAE;kBAC5B,IAAMS,MAAM,GAAGd,cAAa,CAACF,IAAI,CAACe,QAAQ,EAAEX,QAAQ,EAAEJ,IAAI,CAACC,EAAE,CAAC;kBAC9D,IAAIe,MAAM,EAAE,OAAOA,MAAM;gBAC3B;cACF;YAAC,SAAAC,GAAA;cAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA;YAAA;cAAAR,SAAA,CAAAU,CAAA;YAAA;YACD,OAAO,IAAI;UACb,CAAC;UACD,IAAMC,WAAW,GAAGlB,cAAa,CAACpB,KAAK,CAACO,QAAQ,EAAEG,eAAe,CAACJ,KAAK,CAAC;UACxE,IAAIgC,WAAW,IAAIA,WAAW,KAAKvB,KAAK,EAAE;YAAA,IAAAwB,cAAA;YACxC,CAAAA,cAAA,GAAA9B,OAAO,CAACH,KAAK,cAAAiC,cAAA,eAAbA,cAAA,CAAeC,KAAK,CAACF,WAAW,CAAC;UACnC;QACF;QACA,IAAI5B,eAAe,CAACJ,KAAK,IAAII,eAAe,CAACJ,KAAK,KAAKS,KAAK,EAAE;UAAA,IAAA0B,eAAA;UAC5D,CAAAA,eAAA,GAAAhC,OAAO,CAACH,KAAK,cAAAmC,eAAA,eAAbA,eAAA,CAAeD,KAAK,CAAC9B,eAAe,CAACJ,KAAK,CAAC;QAC7C;MACF;MACAI,eAAe,CAACJ,KAAK,GAAGS,KAAK;IAC/B,CAAC;IACD,IAAMF,WAAU,GAAG,SAAbA,UAAUA,CAAI6B,IAAI,EAAEvB,EAAE,EAAK;MAC/BuB,IAAI,CAACC,OAAO,CAAC,UAAAzB,IAAI,EAAI;QACnB,IAAIA,IAAI,CAACe,QAAQ,CAACR,MAAM,KAAK,CAAC,EAAE;UAC9B,IAAIP,IAAI,CAACC,EAAE,KAAKA,EAAE,EAAE;YAAEjB,IAAI,CAAC,QAAQ,EAAEgB,IAAI,CAAC;UAAC;QAC7C,CAAC,MAAM;UACLL,WAAU,CAACK,IAAI,CAACe,QAAQ,EAAEd,EAAE,CAAC;QAC/B;MACF,CAAC,CAAC;IACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}