<template>
  <el-container class="LayoutViewOne" :style="`background: url('${layoutBg}') no-repeat;background-size: 100% 100%;`">
    <el-header class="LayoutViewOneHeader">
      <div class="LayoutViewOneBox" ref="LayoutViewBox" @click="WorkBenchReturn">
        <div class="LayoutViewOneLogo">
          <el-image :src="systemLogo" fit="cover" />
        </div>
        <div class="LayoutViewOneName">{{ systemNameAreaPrefix === 'true' ? regionName : '' }}{{ systemName }}</div>
      </div>
      <div class="LayoutViewOneHeaderMenu">
        <el-tabs v-model="tabMenu" @tab-change="handleClick">
          <el-tab-pane v-for="item in tabMenuData" :key="item.id" :name="item.id" :label="item.name">
            <template #label>
              <div class="LayoutViewOneHeaderMenuItem" v-if="item.routePath !== '/WorkBench'">{{ item.name }}</div>
              <el-popover trigger="hover" popper-class="LayoutViewOneWorkBenchPopover" transition="zy-el-zoom-in-top"
                v-if="item.routePath === '/WorkBench'">
                <template #reference>
                  <div class="LayoutViewOneHeaderMenuItem">{{ item.name }}</div>
                </template>
                <LayoutViewOneWorkBench :id="item.id" :data="item.children"></LayoutViewOneWorkBench>
              </el-popover>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="LayoutViewOneInfo" ref="LayoutViewInfo">
        <xyl-region v-model="regionId" :data="area" @select="regionSelect"
          :props="{ label: 'name', children: 'children' }"></xyl-region>
        <el-tooltip placement="top" effect="light" :offset="6" :disabled="!role.length">
          <template #content>
            <div class="LayoutViewOneRoleItem" v-for="(item, index) in role" :key="index">{{ item }}</div>
          </template>
          <div class="LayoutViewOneUser">
            <el-image :src="user.image" fit="cover" />
            <span class="forbidSelect">{{ user.userName }}</span>
          </div>
        </el-tooltip>
        <LayoutPersonalDoList>待办</LayoutPersonalDoList>
        <LayoutBoxMessage>消息</LayoutBoxMessage>
        <div class="LayoutViewOneRefresh" v-html="refreshIcon" @click="handleCommand('refresh')" title="重新加载平台"></div>
        <el-dropdown @command="handleCommand">
          <div class="LayoutOperation"></div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="task">系统任务管理器</el-dropdown-item>
              <!-- <el-dropdown-item command="refresh">重新加载平台</el-dropdown-item> -->
              <!-- <el-dropdown-item command="locale">简繁切换</el-dropdown-item>
              <el-dropdown-item command="help">帮助文档</el-dropdown-item> -->
              <el-dropdown-item command="edit_password">修改密码</el-dropdown-item>
              <el-dropdown-item command="exit">安全退出</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    <el-container class="LayoutViewOneContainer">
      <el-aside class="LayoutViewOneAside" v-show="isView">
        <xyl-menu v-model="menuId" :menuData="menuData" @select="menuClick"></xyl-menu>
      </el-aside>
      <el-main class="LayoutViewOneMain"
        :class="{ LayoutViewOneMainView: !isView, LayoutViewOneMainBreadcrumb: !isView && tabData.length > 1 }">
        <xyl-tab v-model="menuId" @tab-click="tabClick" @refresh="handleRefresh" @close="handleClose"
          @closeOther="handleCloseOther" v-show="isView">
          <xyl-tab-item v-for="item in tabData" :key="item.id" :value="item.id">{{ item.name }}</xyl-tab-item>
        </xyl-tab>
        <div class="LayoutViewOneBreadcrumb" v-if="!isView && tabData.length > 1">
          <el-breadcrumb :separator-icon="ArrowRight">
            <el-breadcrumb-item v-for="(item, index) in tabData" :key="`key-${item.id}`"
              @click="handleBreadcrumb(item, index)">
              {{ item.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="LayoutViewOneBody">
          <router-view v-slot="{ Component }">
            <keep-alive :include="keepAliveRoute">
              <component v-if="isMain && isRefresh" :key="$route.fullPath" :is="Component"></component>
            </keep-alive>
          </router-view>
          <SubAppViewport v-for="item in MicroApp" :key="item" v-show="!isMain && isMicroApp === item" :name="item">
          </SubAppViewport>
        </div>
      </el-main>
      <el-aside class="LayoutViewOneFloatingWindow" v-if="whetherAiChat">
        <transition name="width-animation">
          <div class="LayoutViewOneFloatingWindowBody" :style="{ '--ai-chat-target-width': AiChatTargetWidth }"
            v-if="AiChatViewType" v-show="AiChatWindowShow">
            <GlobalAiChat v-model="AiChatWindowShow"></GlobalAiChat>
          </div>
        </transition>
      </el-aside>
    </el-container>
    <xyl-popup-window v-model="helpShow" name="帮助文档">
      <HelpDocument></HelpDocument>
    </xyl-popup-window>
    <xyl-popup-window v-model="editPassWordShow" name="修改密码">
      <EditPassWord :type="verifyEditPassWord" @callback="editPassWordCallback"></EditPassWord>
    </xyl-popup-window>
    <div class="ConstraintEditPassWord" v-if="verifyEditPassWordShow">
      <EditPassWord :type="verifyEditPassWord" @callback="editPassWordCallback"></EditPassWord>
    </div>
    <GlobalRegionSelect v-if="isRegionSelectShow" @callback="regionSelect"></GlobalRegionSelect>
  </el-container>
  <qusetionAnswering></qusetionAnswering>
  <GlobalChatFloating v-if="rongCloudToken"></GlobalChatFloating>
  <GlobalFloatingWindow v-model="AiChatWindowShow" :disabled="AiChatViewType" v-if="whetherAiChat" />
  <GlobalAiControls v-if="whetherAiChat" />
  <suggestPop v-if="isMain && loginHintShow" />
</template>
<script>
export default { name: 'LayoutViewOne' }
</script>
<script setup>
import { defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  qiankun,
  LayoutView,
  ChatMethod,
  AiChatMethod,
  refreshIcon,
  loginHintMethod
} from '../LayoutView/LayoutView.js'
import { systemLogo, systemName, layoutBg, whetherAiChat, systemNameAreaPrefix } from 'common/js/system_var.js'
import { ArrowRight } from '@element-plus/icons-vue'
const HelpDocument = defineAsyncComponent(() => import('../LayoutContainer/components/HelpDocument'))
const EditPassWord = defineAsyncComponent(() => import('../LayoutContainer/components/EditPassWord'))
const LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))
const LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))
const GlobalRegionSelect = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalRegionSelect'))
const GlobalChatFloating = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalChatFloating'))
const GlobalFloatingWindow = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalFloatingWindow'))
const GlobalAiControls = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalAiControls'))
const GlobalAiChat = defineAsyncComponent(() => import('../GlobalAiChat/GlobalAiChat'))
const LayoutViewOneWorkBench = defineAsyncComponent(() => import('./component/LayoutViewOneWorkBench.vue'))
const qusetionAnswering = defineAsyncComponent(() => import('./component/question-answering.vue'))
const suggestPop = defineAsyncComponent(() => import('../LayoutView/component/suggestPop'))
const SubAppViewport = {
  name: 'SubAppViewport',
  props: ['name'],
  template: `<div :id="name" class="subApp-viewport"></div>`
}
const { isMain } = qiankun(useRoute())
const {
  user,
  area,
  role,
  LayoutViewBox,
  LayoutViewInfo,
  helpShow,
  handleCommand,
  editPassWordShow,
  verifyEditPassWord,
  verifyEditPassWordShow,
  editPassWordCallback,
  regionId,
  regionName,
  regionSelect,
  isRegionSelectShow,
  isView,
  tabMenu,
  tabMenuData,
  handleClick,
  menuId,
  menuData,
  menuClick,
  handleBreadcrumb,
  WorkBenchReturn,
  isRefresh,
  keepAliveRoute,
  tabData,
  tabClick,
  handleRefresh,
  handleClose,
  handleCloseOther,
  isMicroApp,
  MicroApp
} = LayoutView(useRoute(), useRouter())
const router = useRouter()
const { rongCloudToken } = ChatMethod()
const { loginHintShow } = loginHintMethod()
const { AiChatTargetWidth, AiChatViewType, AiChatWindowShow } = AiChatMethod()

const handleAiToolBox = () => {
  router.push('/GlobalAiToolBox')
}
</script>
<style lang="scss">
.LayoutViewOne {
  width: 100%;
  height: 100%;

  .LayoutViewOneHeader {
    width: 100%;
    height: 68px;
    display: flex;
    align-items: center;

    .LayoutViewOneBox {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 40px 0 20px;
      cursor: pointer;

      .LayoutViewOneLogo {
        width: 52px;

        .zy-el-image {
          width: 100%;
          display: block;
        }
      }

      .LayoutViewOneName {
        font-size: var(--zy-system-font-size);
        line-height: var(--zy-line-height);
        font-weight: bold;
        color: var(--zy-el-color-primary);
        padding-left: 12px;
      }
    }

    .LayoutViewOneHeaderMenu {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      overflow: hidden;

      .zy-el-tabs {
        width: 100%;

        .zy-el-tabs__header {
          margin: 0;

          .zy-el-tabs__nav-next,
          .zy-el-tabs__nav-prev {
            width: 26px;
            font-size: calc(var(--zy-navigation-font-size) + 4px);
            line-height: 48px;

            .zy-el-icon {
              color: var(--zy-el-text-color-primary);
            }
          }

          .zy-el-tabs__nav-wrap {
            padding: 0 calc(var(--zy-navigation-font-size) + 8px);

            &::after {
              background-color: transparent;
            }

            .zy-el-tabs__item {
              height: 43px;
              line-height: 43px;
              font-weight: normal;
              font-size: var(--zy-navigation-font-size);
            }

            .is-active {
              font-weight: bold;
            }

            .zy-el-tabs__active-bar {
              height: 3px;
            }
          }
        }

        .zy-el-tabs__content {
          display: none;
        }
      }
    }

    .isLayoutViewHeaderMenu {
      .zy-el-tabs {
        display: none;
      }
    }

    .LayoutViewOneInfo {
      display: flex;
      align-items: center;
      padding-left: 20px;

      .LayoutViewOneAiToolBox {
        height: 36px;
        display: flex;
        align-items: center;
        color: #3657c0;
        background: linear-gradient(180deg, #ffffff 0%, #a7fff8 76%, #7cd7f5 100%);
        border-radius: 18px;
        font-size: var(--zy-navigation-font-size);
        line-height: var(--zy-line-height);
        padding-left: calc(var(--zy-navigation-font-size) + 22px);
        padding-right: 16px;
        position: relative;
        cursor: pointer;
        margin-right: 10px;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 16px;
          transform: translateY(-50%);
          width: var(--zy-navigation-font-size);
          height: var(--zy-navigation-font-size);
          background: url('../img/ai_tool_box_icon.png') no-repeat;
          background-size: 100% 100%;
        }
      }

      .xyl-region {
        padding-left: 12px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: calc(var(--zy-height-routine) / 2);
        border: 1px solid var(--zy-el-border-color-lighter);

        .xyl-region-view {
          height: var(--zy-height-routine);

          .xyl-region-img {
            width: 20px;
            height: 20px;
          }

          .xyl-region-name {
            font-size: var(--zy-text-font-size);
          }

          .xyl-region-icon {
            width: calc(var(--zy-text-font-size) + 6px);
            font-size: var(--zy-text-font-size);
          }
        }
      }

      .LayoutViewOneUser {
        display: flex;
        align-items: center;
        cursor: pointer;
        height: var(--zy-height-routine);
        background: rgba(0, 0, 0, 0.1);
        border-radius: calc(var(--zy-height-routine) / 2);
        border: 1px solid var(--zy-el-border-color-lighter);
        margin-left: 10px;

        .zy-el-image {
          width: calc(var(--zy-height-routine) - 2px);
          height: calc(var(--zy-height-routine) - 2px);
          border-radius: 50%;
          overflow: hidden;
        }

        span {
          color: #fff;
          margin-left: 6px;
          padding-right: 12px;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
        }
      }

      .zy-el-badge {
        height: var(--zy-height-routine);
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.1);
        border-radius: calc(var(--zy-height-routine) / 2);
        border: 1px solid var(--zy-el-border-color-lighter);
        margin-left: 10px;

        &+.zy-el-badge {
          margin-left: 20px;
        }

        .LayoutPersonalDoList,
        .LayoutBoxMessage {
          width: auto;
          height: auto;
          padding: 0 12px 0 36px;
          background-size: 20px 20px;
          background-position: 12px center;
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          color: #fff;
        }
      }

      .zy-el-dropdown {
        width: var(--zy-height-routine);
        height: var(--zy-height-routine);
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.1);
        border-radius: calc(var(--zy-height-routine) / 2);
        border: 1px solid var(--zy-el-border-color-lighter);
        margin-left: 10px;
      }

      .LayoutViewOneRefresh {
        width: var(--zy-height-routine);
        height: var(--zy-height-routine);
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.1);
        border-radius: calc(var(--zy-height-routine) / 2);
        border: 1px solid var(--zy-el-border-color-lighter);
        margin-left: 20px;
        cursor: pointer;

        svg {
          width: 24px;
          height: 24px;
        }
      }

      .LayoutPersonalDoList {
        width: 20px;
        height: 20px;
        cursor: pointer;
        background: url('../img/layout_personal_do_list.png') no-repeat;
        background-size: 100% 100%;
      }

      .LayoutBoxMessage {
        width: 20px;
        height: 20px;
        cursor: pointer;
        background: url('../img/layout_box_message.png') no-repeat;
        background-size: 100% 100%;
      }

      .LayoutOperation {
        width: 20px;
        height: 20px;
        cursor: pointer;
        background: url('../img/layout_operation.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .LayoutViewOneContainer {
    width: 100%;
    height: calc(100% - 68px);

    .LayoutViewOneFloatingWindow {
      width: auto;
      height: 100%;

      .LayoutViewOneFloatingWindowBody {
        width: var(--ai-chat-target-width);
        height: 100%;
        background: #fff;
        box-sizing: border-box;
        transform-origin: left center;
        border-left: 1px solid var(--zy-el-border-color-lighter);
      }

      /* 进入动画 */
      .width-animation-enter-active {
        animation: widen 0.2s ease-in-out forwards;
      }

      /* 离开动画 */
      .width-animation-leave-active {
        animation: narrow 0.2s ease-in-out forwards;
      }

      /* 定义进入动画 */
      @keyframes widen {
        from {
          width: 0;
        }

        to {
          width: var(--ai-chat-target-width);
        }
      }

      /* 定义离开动画 */
      @keyframes narrow {
        from {
          width: var(--ai-chat-target-width);
        }

        to {
          width: 0;
        }
      }
    }

    .LayoutViewOneAside {
      width: auto;
    }

    .LayoutViewOneMain {
      height: 100%;
      padding: 0 var(--zy-distance-three) 0 0;

      .LayoutViewOneBreadcrumb {
        width: 100%;
        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px);
        display: flex;
        align-items: center;
        background-color: #fff;
        padding: 0 var(--zy-distance-two);
        border-bottom: 1px solid var(--zy-el-border-color-lighter);

        .zy-el-breadcrumb {
          font-size: var(--zy-name-font-size);

          .zy-el-breadcrumb__inner {
            cursor: pointer;
            font-weight: bold;
            color: var(--zy-el-color-primary);
          }

          .zy-el-breadcrumb__item {
            &:last-child {
              .zy-el-breadcrumb__inner {
                cursor: text;
                font-weight: normal;
                color: var(--zy-el-text-color-regular);
              }
            }
          }
        }
      }

      .LayoutViewOneBody {
        width: 100%;
        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));
        background: #fff;

        .subApp-viewport {
          width: 100%;
          height: 100%;

          >div {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .LayoutViewOneMainView {
      width: 100%;
      padding: 0;

      .LayoutViewOneBody {
        height: 100%;
        background: transparent;
      }
    }

    .LayoutViewOneMainBreadcrumb {
      width: 100%;

      .LayoutViewOneBody {
        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));
      }
    }
  }

  .ConstraintEditPassWord {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .EditPassWord {
      box-shadow: 0px 2px 40px rgba(0, 0, 0, 0.1);
    }
  }
}

.LayoutViewOneRoleItem {
  font-size: var(--zy-text-font-size);
  line-height: var(--zy-line-height);
}

.LayoutViewOneWorkBenchPopover {
  width: 680px !important;
  padding: 0 !important;

  .LayoutViewOneWorkBench {
    width: 100%;
    max-height: 480px;

    .zy-el-scrollbar__wrap {
      max-height: 480px;
    }
  }
}
</style>
