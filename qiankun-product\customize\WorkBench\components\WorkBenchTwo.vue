<template>
  <el-scrollbar always class="WorkBenchTwo">
    <div class="WorkBenchTwoSearch">
      <div class="WorkBenchTwoInput">
        <div class="WorkBenchTwoApplySearch">
          <div v-html="ApplyIcon"></div>
          应用搜索
        </div>
        <el-input v-model="keyword" placeholder="请输入应用名称" />
        <el-icon>
          <Search />
        </el-icon>
      </div>
    </div>
    <div class="WorkBenchTwoBox" v-for="item in WorkBench" :key="item.id">
      <div class="WorkBenchTwoColumn" :class="{ WorkBenchTwoColumnOther: item.id === 'other' && !item.name }">
        {{ item.name }}
      </div>
      <div class="WorkBenchTwoList">
        <div class="WorkBenchTwoItem" v-for="row in item.data" :key="row.id" @click="handleWorkBench(row)">
          <el-image :src="row.icon" fit="cover" />
          <div class="WorkBenchTwoItemBox">
            <div class="WorkBenchTwoItemName" v-html="highlightKeyword(row.name)"></div>
          </div>
        </div>
      </div>
      <div class="WorkBenchTwoMenu">
        <div class="WorkBenchTwoMenuItem" v-for="row in item.menuData" :key="row.id"
          @click="handleWorkBenchMenu(item.dataAll, row)">
          <div class="WorkBenchTwoMenuItemName" v-html="highlightKeyword(row.name)"></div>
          <div class="WorkBenchTwoMenuItemApplyName" v-html="row.applyName"></div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script>
export default { name: 'WorkBench' }
</script>
<script setup>
import api from '@/api'
import { ref, inject, computed, watch, onMounted } from 'vue'
const ApplyIcon = `<svg t="1744701234646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2521" width="16" height="16"><path d="M409.290323 475.354839H175.483871c-36.645161 0-66.064516-29.419355-66.064516-66.064516V175.483871c0-36.129032 29.935484-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.645161-29.419355 66.064516-66.064516 66.064516zM409.290323 914.580645H175.483871c-36.645161 0-66.064516-29.419355-66.064516-66.064516v-233.806452c0-36.645161 29.419355-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.129032-29.419355 66.064516-66.064516 66.064516zM848.516129 914.580645h-233.806452c-36.645161 0-66.064516-29.419355-66.064516-66.064516v-233.806452c0-36.645161 29.419355-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.129032-29.935484 66.064516-66.064516 66.064516zM930.580645 246.193548l-152.774193-152.774193c-25.806452-25.806452-67.612903-25.806452-93.419355 0L531.612903 246.193548c-25.806452 25.806452-25.806452 67.612903 0 93.419355l152.774194 152.774194c25.806452 25.806452 67.612903 25.806452 93.419355 0l152.774193-152.774194c25.806452-26.322581 25.806452-68.129032 0-93.419355z m-185.806451 175.483871c-7.225806 7.225806-19.612903 7.225806-26.83871 0l-115.612903-115.612903c-7.225806-7.225806-7.225806-19.612903 0-26.83871l115.612903-115.612903c7.225806-7.225806 19.612903-7.225806 26.83871 0l115.612903 115.612903c7.225806 7.225806 7.225806 19.612903 0 26.83871l-115.612903 115.612903z" p-id="2522"></path></svg>`
const menuFunction = ref([])
const WorkBenchData = ref([])
const WorkBenchList = inject('WorkBenchList')
const leftMenuData = inject('leftMenuData')
const setOpenPageId = inject('setOpenPageId')
const keyword = ref('')
const WorkBench = computed(() => {
  if (!keyword.value) return WorkBenchData.value
  const newWorkBench = []
  for (let index = 0; index < WorkBenchData.value.length; index++) {
    const item = WorkBenchData.value[index]
    const newApply = []
    const applyChildrenAll = []
    for (let i = 0; i < item.data.length; i++) {
      const apply = item.data[i]
      const applyChildren = filterWorkBenchMenu(apply.children)
      // if (apply.name.includes(keyword.value) || applyChildren.length) {
      //   newApply.push(apply)
      // }
      if (apply.name.includes(keyword.value)) newApply.push(apply)
      if (applyChildren.length) {
        for (let index = 0; index < applyChildren.length; index++) {
          const applyItem = applyChildren[index]
          applyChildrenAll.push({ ...applyItem, applyId: apply.id, applyName: apply.name })
        }
      }
    }
    if (newApply.length || applyChildrenAll.length)
      newWorkBench.push({ ...item, data: newApply, dataAll: item.dataAll, menuData: applyChildrenAll })
  }
  return newWorkBench
})

onMounted(() => {
  dictionaryData()
})

const filterWorkBenchMenu = (data) => {
  let newData = []
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item.children?.length) {
      const children = filterWorkBenchMenu(item.children)
      newData = [...newData, ...children]
    } else {
      if (item.name.includes(keyword.value)) newData.push(item)
    }
  }
  return newData
}

// 添加高亮关键词的函数
const highlightKeyword = (text) => {
  if (!keyword.value || !text) return text
  const regex = new RegExp(`(${keyword.value})`, 'gi')
  return text.replace(regex, '<span style="color: red; font-weight: bold;">$1</span>')
}

const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['menu_function'] })
  var { data } = res
  menuFunction.value = data.menu_function || []
  if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {
    handleWorkBenchData(
      menuFunction.value.map((v) => ({ id: v.key, name: v.name, data: [], dataAll: [], menuData: [] }))
    )
  }
}
const handleWorkBenchData = (arr) => {
  if (!WorkBenchList || !WorkBenchList.value) return

  var other = ''
  var arrData = arr
  var arrIndex = arr.map((v) => v.id)
  for (let i = 0, len = WorkBenchList.value.length; i < len; i++) {
    const item = WorkBenchList.value[i]
    if (item.menuFunction?.value) {
      if (arrIndex.includes(item.menuFunction.value)) {
        arrData.forEach((row) => {
          if (item.menuFunction.value === row.id) {
            console.log(row)
            row.data.push(item)
            row.dataAll.push(item)
          }
        })
      } else {
        arrIndex.push(item.menuFunction.value)
        arrData.push({
          id: item.menuFunction.value,
          name: item.menuFunction.label,
          data: [item],
          dataAll: [item],
          menuData: []
        })
      }
    } else {
      if (other) {
        other.data.push(item)
        other.dataAll.push(item)
      } else {
        other = { id: 'other', data: [item], dataAll: [item], menuData: [] }
      }
    }
  }
  WorkBenchData.value = arrData.filter((v) => v.data.length)
  if (other) {
    WorkBenchData.value.push({ ...other, name: WorkBenchData.value.length ? '其他' : '' })
  }
}
const handleWorkBench = (item) => {
  leftMenuData(item)
}
const handleWorkBenchMenu = (data, item) => {
  setOpenPageId(item.id)
  if (item.applyId) {
    const applyItem = data.find((v) => v.id === item.applyId)
    if (applyItem) leftMenuData(applyItem)
  }
}

watch(
  () => WorkBenchList.value,
  () => {
    if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {
      handleWorkBenchData(menuFunction.value.map((v) => ({ id: v.key, name: v.name, data: [] })))
    }
  },
  { deep: true }
)
</script>
<style lang="scss">
.WorkBenchTwo {
  width: 100%;
  height: 100%;

  .WorkBenchTwoSearch {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0 20px 0;

    .WorkBenchTwoInput {
      width: 600px;
      display: flex;
      align-items: center;
      background: #fff;
      padding: 6px 20px;

      .WorkBenchTwoApplySearch {
        display: flex;
        align-items: center;
        font-weight: bold;
        font-size: var(--zy-navigation-font-size);
        line-height: var(--zy-line-height);
        border-radius: var(--el-border-radius-base);

        &>div {
          width: calc(var(--zy-navigation-font-size) + 2px);
          height: calc(var(--zy-navigation-font-size) + 2px);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 2px;

          svg {
            width: 100%;
            height: 100%;

            path {
              fill: var(--zy-el-color-primary);
            }
          }
        }
      }

      .zy-el-input {
        flex: 1;

        .zy-el-input__wrapper {
          box-shadow: none;
        }
      }

      &>.zy-el-icon {
        color: var(--zy-el-color-primary);
        font-size: var(--zy-navigation-font-size);
      }
    }
  }

  .WorkBenchTwoBox {
    width: 1140px;
    margin: auto;
    padding-left: 40px;

    &:last-child {
      padding-bottom: 40px;
    }

    .WorkBenchTwoColumn {
      padding: 30px 0;
      padding-left: 24px;
      font-size: calc(var(--zy-navigation-font-size) + 2px);
      line-height: var(--zy-line-height);
      font-weight: bold;
      color: #333333;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid var(--zy-el-color-primary);
        transform: translateY(-50%);
      }

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 10px;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid var(--zy-el-color-primary);
        transform: translateY(-50%);
      }
    }

    .WorkBenchTwoColumnOther {
      &::after {
        border-left: 6px solid transparent;
      }

      &::before {
        border-left: 6px solid transparent;
      }
    }

    .WorkBenchTwoList {
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      .WorkBenchTwoItem {
        width: 248px;
        background: #ffffff;
        box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        margin: 0 26px 26px 0;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        padding: 20px 22px;
        cursor: pointer;

        &:hover {
          box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.2);
        }

        .zy-el-image {
          width: 48px;
          height: 48px;
        }

        .WorkBenchTwoItemBox {
          width: calc(100% - 48px);
          height: calc((var(--zy-name-font-size) * var(--zy-line-height)) * 2);
          display: flex;
          flex-wrap: wrap;
          align-items: center;
        }

        .WorkBenchTwoItemName {
          width: 100%;
          line-height: var(--zy-line-height);
          font-size: var(--zy-name-font-size);
          padding-left: 20px;
          font-weight: bold;
          overflow: hidden;
        }
      }
    }

    .WorkBenchTwoMenu {
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      .WorkBenchTwoMenuItem {
        width: 522px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #ffffff;
        box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.1);
        border-radius: var(--el-border-radius-base);
        padding: var(--zy-distance-five);
        padding-left: 30px;
        margin-right: 26px;
        margin-bottom: 16px;
        cursor: pointer;
        position: relative;

        &:hover {
          box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.2);
        }

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 12px;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: var(--zy-el-color-primary);
          transform: translateY(-50%);
        }

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 80%;
          height: 0;
          border-top: 1px dashed var(--zy-el-text-color-secondary);
          transform: translate(-50%, -50%);
        }

        .WorkBenchTwoMenuItemName {
          line-height: var(--zy-line-height);
          font-size: var(--zy-text-font-size);
          color: var(--zy-el-text-color-regular);
          background: #fff;
          font-weight: bold;
          padding-right: 6px;
          position: relative;
          z-index: 2;
        }

        .WorkBenchTwoMenuItemApplyName {
          line-height: var(--zy-line-height);
          font-size: var(--zy-text-font-size);
          color: var(--zy-el-text-color-secondary);
          background: #fff;
          padding-left: 6px;
          position: relative;
          z-index: 2;
        }
      }
    }
  }
}
</style>
