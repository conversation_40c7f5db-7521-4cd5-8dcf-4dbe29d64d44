{"ast": null, "code": "import TinyMceEditor from 'common/components/TinyMceEditor/TinyMceEditor.vue';\nimport GlobalMarkdown from 'common/components/global-markdown/global-markdown.vue';\nimport SuggestSelectUnit from './suggest-select-unit/suggest-select-unit.vue';\nimport SuggestSimpleSelectUnit from './suggest-simple-select-unit/suggest-simple-select-unit.vue';\nvar components = [TinyMceEditor, GlobalMarkdown, SuggestSelectUnit, SuggestSimpleSelectUnit];\nexport default {\n  install(app) {\n    components.forEach(function (v) {\n      return app.component(v.name, v);\n    });\n  }\n};", "map": {"version": 3, "names": ["TinyMceEditor", "GlobalMarkdown", "SuggestSelectUnit", "SuggestSimpleSelectUnit", "components", "install", "app", "for<PERSON>ach", "v", "component", "name"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/components/index.js"], "sourcesContent": ["import TinyMceEditor from 'common/components/TinyMceEditor/TinyMceEditor.vue'\r\nimport GlobalMarkdown from 'common/components/global-markdown/global-markdown.vue'\r\nimport SuggestSelectUnit from './suggest-select-unit/suggest-select-unit.vue'\r\nimport SuggestSimpleSelectUnit from './suggest-simple-select-unit/suggest-simple-select-unit.vue'\r\nconst components = [TinyMceEditor, GlobalMarkdown, SuggestSelectUnit, SuggestSimpleSelectUnit]\r\nexport default { install (app) { components.forEach(v => app.component(v.name, v)) } }\r\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,mDAAmD;AAC7E,OAAOC,cAAc,MAAM,uDAAuD;AAClF,OAAOC,iBAAiB,MAAM,+CAA+C;AAC7E,OAAOC,uBAAuB,MAAM,6DAA6D;AACjG,IAAMC,UAAU,GAAG,CAACJ,aAAa,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,uBAAuB,CAAC;AAC9F,eAAe;EAAEE,OAAOA,CAAEC,GAAG,EAAE;IAAEF,UAAU,CAACG,OAAO,CAAC,UAAAC,CAAC;MAAA,OAAIF,GAAG,CAACG,SAAS,CAACD,CAAC,CAACE,IAAI,EAAEF,CAAC,CAAC;IAAA,EAAC;EAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}