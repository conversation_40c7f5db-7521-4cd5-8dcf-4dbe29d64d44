{"ast": null, "code": "import { renderSlot as _renderSlot, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LayoutBoxMessage\"\n};\nvar _hoisted_2 = {\n  class: \"LayoutBoxMessageBody\"\n};\nvar _hoisted_3 = {\n  class: \"LayoutBoxMessageHead\"\n};\nvar _hoisted_4 = {\n  class: \"LayoutBoxMessageScroll\"\n};\nvar _hoisted_5 = [\"onClick\"];\nvar _hoisted_6 = {\n  class: \"LayoutBoxMessageInfo\"\n};\nvar _hoisted_7 = {\n  class: \"LayoutBoxMessageType\"\n};\nvar _hoisted_8 = {\n  class: \"LayoutBoxMessageTime\"\n};\nvar _hoisted_9 = {\n  class: \"LayoutBoxMessageTitle\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  class: \"LayoutBoxMessageLoadingText\"\n};\nvar _hoisted_11 = {\n  key: 1,\n  class: \"LayoutBoxMessageLoadingText\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_DArrowRight = _resolveComponent(\"DArrowRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  var _component_el_badge = _resolveComponent(\"el-badge\");\n  return _openBlock(), _createBlock(_component_el_badge, {\n    value: $setup.totals\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_popover, {\n        trigger: \"hover\",\n        \"popper-class\": \"LayoutBoxMessagePopover\",\n        transition: \"zy-el-zoom-in-top\"\n      }, {\n        reference: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_1, [_renderSlot(_ctx.$slots, \"default\")])];\n        }),\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", {\n            class: \"LayoutBoxMessageName\"\n          }, [_cache[1] || (_cache[1] = _createTextVNode(\"消息盒子\")), _createElementVNode(\"span\", {\n            onClick: $setup.handleMark\n          }, \"全部已读\")]), _createElementVNode(\"div\", {\n            onClick: _cache[0] || (_cache[0] = function ($event) {\n              return $setup.openPage({\n                key: 'routePath',\n                value: '/interaction/BoxMessage'\n              });\n            }),\n            class: \"LayoutBoxMessageText\"\n          }, [_cache[2] || (_cache[2] = _createTextVNode(\"更多 \")), _createVNode(_component_el_icon, null, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_DArrowRight)];\n            }),\n            _: 1 /* STABLE */\n          })])]), _createVNode(_component_el_scrollbar, {\n            ref: \"scrollRef\",\n            class: \"LayoutBoxMessageScrollbar\",\n            onScroll: $setup.handleScroll\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableData, function (item) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  key: item.id,\n                  class: \"LayoutBoxMessageItem\",\n                  onClick: function onClick($event) {\n                    return $setup.handleDetails(item);\n                  }\n                }, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString(item.moduleName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.format(item.createDate)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_9, _toDisplayString(item.content), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_5);\n              }), 128 /* KEYED_FRAGMENT */)), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \"加载中...\")) : _createCommentVNode(\"v-if\", true), $setup.isShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, \"没有更多了\")) : _createCommentVNode(\"v-if\", true)])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */)])];\n        }),\n        _: 3 /* FORWARDED */\n      })];\n    }),\n    _: 3 /* FORWARDED */\n  }, 8 /* PROPS */, [\"value\"]);\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_el_badge", "value", "$setup", "totals", "default", "_withCtx", "_createVNode", "_component_el_popover", "trigger", "transition", "reference", "_createElementVNode", "_hoisted_1", "_renderSlot", "_ctx", "$slots", "_hoisted_2", "_hoisted_3", "_createTextVNode", "onClick", "handleMark", "_cache", "$event", "openPage", "_component_el_icon", "_component_DArrowRight", "_", "_component_el_scrollbar", "ref", "onScroll", "handleScroll", "_hoisted_4", "_createElementBlock", "_Fragment", "_renderList", "tableData", "item", "id", "handleDetails", "_hoisted_6", "_hoisted_7", "_toDisplayString", "moduleName", "_hoisted_8", "format", "createDate", "_hoisted_9", "content", "_hoisted_5", "loading", "_hoisted_10", "_createCommentVNode", "isShow", "_hoisted_11"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\components\\LayoutBoxMessage.vue"], "sourcesContent": ["<template>\r\n  <el-badge :value=\"totals\">\r\n    <el-popover trigger=\"hover\" popper-class=\"LayoutBoxMessagePopover\" transition=\"zy-el-zoom-in-top\">\r\n      <template #reference>\r\n        <div class=\"LayoutBoxMessage\">\r\n          <slot></slot>\r\n        </div>\r\n      </template>\r\n      <div class=\"LayoutBoxMessageBody\">\r\n        <div class=\"LayoutBoxMessageHead\">\r\n          <div class=\"LayoutBoxMessageName\">消息盒子<span @click=\"handleMark\">全部已读</span></div>\r\n          <div @click=\"openPage({ key: 'routePath', value: '/interaction/BoxMessage' })\" class=\"LayoutBoxMessageText\">更多\r\n            <el-icon>\r\n              <DArrowRight />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <el-scrollbar ref=\"scrollRef\" class=\"LayoutBoxMessageScrollbar\" @scroll=\"handleScroll\">\r\n          <div class=\"LayoutBoxMessageScroll\">\r\n            <div v-for=\"item in tableData\" :key=\"item.id\" class=\"LayoutBoxMessageItem\" @click=\"handleDetails(item)\">\r\n              <div class=\"LayoutBoxMessageInfo\">\r\n                <div class=\"LayoutBoxMessageType\">{{ item.moduleName }}</div>\r\n                <div class=\"LayoutBoxMessageTime\">{{ format(item.createDate) }}</div>\r\n              </div>\r\n              <div class=\"LayoutBoxMessageTitle\">{{ item.content }}</div>\r\n            </div>\r\n            <div class=\"LayoutBoxMessageLoadingText\" v-if=\"loading\">加载中...</div>\r\n            <div class=\"LayoutBoxMessageLoadingText\" v-if=\"isShow\">没有更多了</div>\r\n          </div>\r\n        </el-scrollbar>\r\n      </div>\r\n    </el-popover>\r\n  </el-badge>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutBoxMessage' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, inject, watch } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nconst store = useStore()\r\nconst openPage = inject('openPage')\r\nconst scrollRef = ref()\r\nconst loadingScroll = ref(false)\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst totals = ref(0)\r\nconst isShow = ref(false)\r\nconst loading = ref(true)\r\nconst tableData = ref([])\r\n\r\nonMounted(() => { boxMessageList() })\r\n\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (!scrollRef.value) return\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {\r\n    load()\r\n  }\r\n}\r\nconst load = () => {\r\n  if (pageNo.value * pageSize.value >= totals.value) return\r\n  loadingScroll.value = true\r\n  pageNo.value += 1\r\n  boxMessageList()\r\n}\r\nconst boxMessageList = async () => {\r\n  const { data, total } = await api.boxMessageList({\r\n    pageNo: pageNo.value,\r\n    pageSize: pageSize.value,\r\n    hasRead: 0,\r\n    objectParam: {}\r\n  })\r\n  tableData.value = [...tableData.value, ...data]\r\n  totals.value = total\r\n  loading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  loadingScroll.value = false\r\n}\r\nconst handleDetails = (item) => {\r\n  if (!item.redirectUrl && item.businessCode !== 'system') return ElMessage({ type: 'info', message: `当前${item.moduleName || ''}数据没有跳转路径，请维护好跳转路径在进行查看详情！` })\r\n  if (item.isDisabled) return ElMessage({ type: 'info', message: `当前${item.moduleName || ''}数据已被删除！` })\r\n  openPage({ key: 'routePath', value: '/interaction/BoxMessage' })\r\n  sessionStorage.setItem('BoxMessage', JSON.stringify(item || ''))\r\n}\r\nconst handleMark = () => {\r\n  ElMessageBox.confirm('此操作将把所有的消息设为已读, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { boxMessageRead() }).catch(() => { ElMessage({ type: 'info', message: '已取消操作' }) })\r\n}\r\nconst boxMessageRead = async () => {\r\n  const { code } = await api.boxMessageRead({ businessCode: 'box_message' })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '全部设为已读成功' })\r\n    pageNo.value = 1\r\n    pageSize.value = 10\r\n    totals.value = 0\r\n    isShow.value = false\r\n    loading.value = true\r\n    tableData.value = []\r\n    boxMessageList()\r\n  }\r\n}\r\nwatch(() => store.state.socket, (val) => {\r\n  if (val) {\r\n    store.state.socket.on('message', (res) => {\r\n      const data = JSON.parse(res)\r\n      if (data.messageType === 'box_message') {\r\n        pageNo.value = 1\r\n        pageSize.value = 10\r\n        totals.value = 0\r\n        isShow.value = false\r\n        loading.value = true\r\n        tableData.value = []\r\n        boxMessageList()\r\n      }\r\n    })\r\n  }\r\n})\r\nwatch(() => store.state.boxMessageRefresh, (val) => {\r\n  if (val) {\r\n    pageNo.value = 1\r\n    pageSize.value = 10\r\n    totals.value = 0\r\n    isShow.value = false\r\n    loading.value = true\r\n    tableData.value = []\r\n    boxMessageList()\r\n    store.commit('setBoxMessageRefresh', false)\r\n  }\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutBoxMessagePopover {\r\n  width: 500px !important;\r\n  padding: 0 !important;\r\n\r\n  .LayoutBoxMessageHead {\r\n    padding: var(--zy-distance-five) var(--zy-distance-two);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .LayoutBoxMessageName {\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      color: var(--zy-el-text-color-primary);\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        margin-left: 20px;\r\n        font-weight: normal;\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n      }\r\n    }\r\n\r\n    .LayoutBoxMessageText {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-regular);\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .LayoutBoxMessageScrollbar {\r\n    width: 100%;\r\n    height: 60vh;\r\n\r\n    // .zy-el-scrollbar__wrap {\r\n    //   max-height: 60vh;\r\n    // }\r\n  }\r\n\r\n  .LayoutBoxMessageScroll {\r\n    padding: var(--zy-distance-five) 0;\r\n\r\n    .LayoutBoxMessageItem {\r\n      cursor: pointer;\r\n      padding: var(--zy-distance-five) var(--zy-distance-two);\r\n\r\n      .LayoutBoxMessageInfo {\r\n        display: flex;\r\n        align-items: center;\r\n        position: relative;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 50%;\r\n          right: 0;\r\n          transform: translateY(-50%);\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 50%;\r\n          background: var(--zy-el-color-danger);\r\n        }\r\n\r\n        .LayoutBoxMessageType {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          border: 1px solid var(--zy-el-color-primary);\r\n          border-radius: var(--el-border-radius-small);\r\n          color: var(--zy-el-color-primary);\r\n          padding: 0 12px;\r\n        }\r\n\r\n        .LayoutBoxMessageTime {\r\n          margin-left: 20px;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          color: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n\r\n      .LayoutBoxMessageTitle {\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-primary);\r\n        padding-top: var(--zy-font-name-distance-five);\r\n        text-overflow: -o-ellipsis-lastline;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        display: -webkit-box;\r\n        -webkit-line-clamp: 2;\r\n        line-clamp: 2;\r\n        -webkit-box-orient: vertical;\r\n      }\r\n    }\r\n\r\n    .LayoutBoxMessageLoadingText {\r\n      text-align: center;\r\n      color: var(--zy-el-text-color-regular);\r\n      padding: var(--zy-distance-three) 0;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAIaA,KAAK,EAAC;AAAkB;;EAI1BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAsB;;EAS1BA,KAAK,EAAC;AAAwB;iBAlB7C;;EAoBmBA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAsB;;EAC5BA,KAAK,EAAC;AAAsB;;EAE9BA,KAAK,EAAC;AAAuB;;EAxBhDC,GAAA;EA0BiBD,KAAK,EAAC;;;EA1BvBC,GAAA;EA2BiBD,KAAK,EAAC;;;;;;;;uBA1BrBE,YAAA,CA+BWC,mBAAA;IA/BAC,KAAK,EAAEC,MAAA,CAAAC;EAAM;IAD1BC,OAAA,EAAAC,QAAA,CAEI;MAAA,OA6Ba,CA7BbC,YAAA,CA6BaC,qBAAA;QA7BDC,OAAO,EAAC,OAAO;QAAC,cAAY,EAAC,yBAAyB;QAACC,UAAU,EAAC;;QACjEC,SAAS,EAAAL,QAAA,CAClB;UAAA,OAEM,CAFNM,mBAAA,CAEM,OAFNC,UAEM,GADJC,WAAA,CAAaC,IAAA,CAAAC,MAAA,a;;QALvBX,OAAA,EAAAC,QAAA,CAQM;UAAA,OAsBM,CAtBNM,mBAAA,CAsBM,OAtBNK,UAsBM,GArBJL,mBAAA,CAOM,OAPNM,UAOM,GANJN,mBAAA,CAAiF;YAA5Ed,KAAK,EAAC;UAAsB,I,0BAV3CqB,gBAAA,CAU4C,MAAI,IAAAP,mBAAA,CAAqC;YAA9BQ,OAAK,EAAEjB,MAAA,CAAAkB;UAAU,GAAE,MAAI,E,GACpET,mBAAA,CAIM;YAJAQ,OAAK,EAAAE,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEpB,MAAA,CAAAqB,QAAQ;gBAAAzB,GAAA;gBAAAG,KAAA;cAAA;YAAA;YAA0DJ,KAAK,EAAC;wCAX/FqB,gBAAA,CAWsH,KAC1G,IAAAZ,YAAA,CAEUkB,kBAAA;YAdtBpB,OAAA,EAAAC,QAAA,CAac;cAAA,OAAe,CAAfC,YAAA,CAAemB,sBAAA,E;;YAb7BC,CAAA;kBAiBQpB,YAAA,CAYeqB,uBAAA;YAZDC,GAAG,EAAC,WAAW;YAAC/B,KAAK,EAAC,2BAA2B;YAAEgC,QAAM,EAAE3B,MAAA,CAAA4B;;YAjBjF1B,OAAA,EAAAC,QAAA,CAkBU;cAAA,OAUM,CAVNM,mBAAA,CAUM,OAVNoB,UAUM,I,kBATJC,mBAAA,CAMMC,SAAA,QAzBlBC,WAAA,CAmBgChC,MAAA,CAAAiC,SAAS,EAnBzC,UAmBwBC,IAAI;qCAAhBJ,mBAAA,CAMM;kBAN0BlC,GAAG,EAAEsC,IAAI,CAACC,EAAE;kBAAExC,KAAK,EAAC,sBAAsB;kBAAEsB,OAAK,WAALA,OAAKA,CAAAG,MAAA;oBAAA,OAAEpB,MAAA,CAAAoC,aAAa,CAACF,IAAI;kBAAA;oBACnGzB,mBAAA,CAGM,OAHN4B,UAGM,GAFJ5B,mBAAA,CAA6D,OAA7D6B,UAA6D,EAAAC,gBAAA,CAAxBL,IAAI,CAACM,UAAU,kBACpD/B,mBAAA,CAAqE,OAArEgC,UAAqE,EAAAF,gBAAA,CAAhCvC,MAAA,CAAA0C,MAAM,CAACR,IAAI,CAACS,UAAU,kB,GAE7DlC,mBAAA,CAA2D,OAA3DmC,UAA2D,EAAAL,gBAAA,CAArBL,IAAI,CAACW,OAAO,iB,iBAxBhEC,UAAA;8CA0B2D9C,MAAA,CAAA+C,OAAO,I,cAAtDjB,mBAAA,CAAoE,OAApEkB,WAAoE,EAAZ,QAAM,KA1B1EC,mBAAA,gBA2B2DjD,MAAA,CAAAkD,MAAM,I,cAArDpB,mBAAA,CAAkE,OAAlEqB,WAAkE,EAAX,OAAK,KA3BxEF,mBAAA,e;;YAAAzB,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}