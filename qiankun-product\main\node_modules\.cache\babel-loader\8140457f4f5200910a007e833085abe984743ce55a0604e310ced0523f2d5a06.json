{"ast": null, "code": "import { ref, watch } from 'vue';\nvar __default__ = {\n  name: 'XylMenuItem'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    menuData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var menuData = ref(props.menuData);\n    watch(function () {\n      return props.menuData;\n    }, function () {\n      menuData.value = props.menuData;\n    });\n    var __returned__ = {\n      props,\n      menuData,\n      ref,\n      watch\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "watch", "__default__", "name", "props", "__props", "menuData", "value"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutView/component/LayoutMenu/LayoutMenuItem.vue"], "sourcesContent": ["<template>\r\n  <template v-for=\"item in menuData\">\r\n    <el-sub-menu :index=\"item.id\" :key=\"item.id\" popper-class=\"xyl-menu-popper\" v-if=\"item.children.length\">\r\n      <template #title><span class=\"xyl-menu-text\">{{ item.name }}</span></template>\r\n      <xyl-menu-item :menuData=\"item.children\"></xyl-menu-item>\r\n    </el-sub-menu>\r\n    <el-menu-item :index=\"item.id\" :key=\"item.id\" v-if=\"!item.children.length\">\r\n      <template #title><span class=\"xyl-menu-text\">{{ item.name }}</span></template>\r\n    </el-menu-item>\r\n  </template>\r\n</template>\r\n<script>\r\nexport default { name: 'XylMenuItem' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch } from 'vue'\r\nconst props = defineProps({\r\n  menuData: { type: Array, default: () => [] }\r\n})\r\nconst menuData = ref(props.menuData)\r\nwatch(() => props.menuData, () => { menuData.value = props.menuData })\r\n</script>\r\n"], "mappings": "AAeA,SAASA,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAHhC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAc,CAAC;;;;;;;;;;;;;IAItC,IAAMC,KAAK,GAAGC,OAEZ;IACF,IAAMC,QAAQ,GAAGN,GAAG,CAACI,KAAK,CAACE,QAAQ,CAAC;IACpCL,KAAK,CAAC;MAAA,OAAMG,KAAK,CAACE,QAAQ;IAAA,GAAE,YAAM;MAAEA,QAAQ,CAACC,KAAK,GAAGH,KAAK,CAACE,QAAQ;IAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}