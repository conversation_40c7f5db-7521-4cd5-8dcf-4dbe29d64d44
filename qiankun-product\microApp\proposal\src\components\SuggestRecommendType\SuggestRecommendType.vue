<template>
  <div class="SuggestRecommendType">
    <div class="SuggestRecommendTypeTitle">根据历年提案分类数据，小助手为您智能推荐了提案的类别，点击他们就会对号入座哦~</div>
    <div class="SuggestRecommendTypeName">提案大类：</div>
    <div class="SuggestRecommendTypeBody">
      <div class="SuggestRecommendTypeItem"
           :class="{ 'is-active': item._id === typeId }"
           v-for="item in typeData"
           @mouseenter="handleMouseEnter(item)"
           @mouseleave="handleMouseLeave(item)"
           @click="handleTypeClick(item)"
           :key="item._id">{{ item.name }}</div>
    </div>
    <div class="SuggestRecommendTypeName"
         v-if="secondType.length">提案小类：</div>
    <div class="SuggestRecommendTypeBody"
         v-if="secondType.length">
      <div class="SuggestRecommendTypeItem"
           v-for="item in secondType"
           @click="handleTypeChildClick(item)"
           :key="item._id">{{ item.name }}</div>
    </div>
  </div>
</template>
<script>
export default { name: 'SuggestRecommendType' }
</script>
<script setup>
import api from '@/api'
import { ref, watch } from 'vue'
const props = defineProps({ id: { type: String, default: '' }, content: { type: String, default: '' } })
const emit = defineEmits(['callback', 'select'])
const typeId = ref([])
const typeData = ref([])
const typeChild = ref([])
const secondType = ref([])

const commonMethod = async () => {
  try {
    const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区
    const { data } = await api.commonType({ id: props.id, areaId: AreaId, dbName: 'thinktank', type: '1', is_refresh: '1', content: props.content })
    typeData.value = data
    if (typeData.value.length) { emit('callback', true, true) } else { emit('callback', false, false) }
  } catch (err) {
    emit('callback', false, false)
  }
}
const handleMouseEnter = (item) => {
  secondType.value = item.secondType
}
const handleMouseLeave = () => {
  secondType.value = typeId.value ? typeChild.value : []
}
const handleTypeClick = (item) => {
  typeId.value = item._id
  typeChild.value = item.secondType
  secondType.value = item.secondType
  emit('select', item)
}
const handleTypeChildClick = (item) => {
  emit('select', item, typeId.value)
}
watch(() => props.content, () => {
  if (props.content) { commonMethod() } else { emit('callback', false, false) }
}, { immediate: true })
</script>
<style lang="scss">
.SuggestRecommendType {
  width: 380px;
  padding: var(--zy-distance-three) var(--zy-distance-two);
  padding-top: 0;

  .SuggestRecommendTypeTitle {
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    padding-bottom: var(--zy-font-name-distance-five);
    color: var(--zy-el-color-primary);
  }

  .SuggestRecommendTypeName {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding-bottom: var(--zy-font-name-distance-five);
  }

  .SuggestRecommendTypeBody {
    display: flex;
    flex-wrap: wrap;

    .SuggestRecommendTypeItem {
      height: var(--zy-height-routine);
      line-height: var(--zy-height-routine);
      font-size: var(--zy-text-font-size);
      background-color: var(--zy-el-color-info-light-9);
      padding: 0 var(--zy-distance-five);
      margin-right: var(--zy-distance-five);
      margin-bottom: var(--zy-distance-five);
      border-radius: var(--el-border-radius-small);
      cursor: pointer;

      &:hover {
        background-color: var(--zy-el-color-info-light-8);
      }
    }

    .is-active {
      background-color: var(--zy-el-color-info-light-8);
    }
  }
}
</style>
