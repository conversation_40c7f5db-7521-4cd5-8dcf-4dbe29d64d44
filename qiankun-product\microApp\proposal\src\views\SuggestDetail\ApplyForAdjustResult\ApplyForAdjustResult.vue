<template>
  <div class="ApplyForAdjustResult">
    <div class="ApplyForAdjustResultName">提案单位申请调整结果</div>
    <el-empty v-if="!dataList.length"
              description="暂无记录" />
    <global-info v-for="item in dataList"
                 :key="item.id">
      <div v-for="row in item.applyOfficeInfo"
           :key="row.applyAdjustId">
        <global-info-line>
          <global-info-item label="申请单位">{{ row.officeName }}</global-info-item>
          <global-info-item label="申请时间">{{ format(row.applyTime) }}</global-info-item>
        </global-info-line>
        <global-info-item label="申请调整理由">
          <pre>{{ row.applyReason }}</pre>
        </global-info-item>
      </div>
      <global-info-item label="调整时间">{{ format(item.adjustTime) }}</global-info-item>
      <global-info-item label="调整前">
        <div v-if="item.oldMain">主办：{{ item.oldMain }}</div>
        <div v-if="item.oldAssist">协办：{{ item.oldAssist }}</div>
        <div v-if="item.oldPublish">分办：{{ item.oldPublish }}</div>
      </global-info-item>
      <global-info-item label="调整后">
        <div v-if="item.newMain">主办：{{ item.newMain }}</div>
        <div v-if="item.newAssist">协办：{{ item.newAssist }}</div>
        <div v-if="item.newPublish">分办：{{ item.newPublish }}</div>
      </global-info-item>
    </global-info>
  </div>
</template>
<script>
export default { name: 'ApplyForAdjustResult' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { format } from 'common/js/time.js'
const props = defineProps({ id: { type: String, default: '' } })
const dataList = ref([])
onMounted(() => { handingPortionAdjustRecord() })

const handingPortionAdjustRecord = async () => {
  const { data } = await api.handingPortionAdjustRecord({ suggestionId: props.id })
  dataList.value = data.map(v => ({
    id: v.recordId,
    adjustTime: v.adjustTime,
    applyOfficeInfo: v.applyOfficeInfo,
    oldMain: v.beforeOffices.filter(v => v.type === 'main').map(v => v.officeName).join('、'),
    oldAssist: v.beforeOffices.filter(v => v.type === 'assist').map(v => v.officeName).join('、'),
    oldPublish: v.beforeOffices.filter(v => v.type === 'publish').map(v => v.officeName).join('、'),
    newMain: v.afterOffices.filter(v => v.type === 'main').map(v => v.officeName).join('、'),
    newAssist: v.afterOffices.filter(v => v.type === 'assist').map(v => v.officeName).join('、'),
    newPublish: v.afterOffices.filter(v => v.type === 'publish').map(v => v.officeName).join('、')
  }))
}
</script>
<style lang="scss">
.ApplyForAdjustResult {
  width: 990px;
  padding: 0 var(--zy-distance-one);
  padding-top: var(--zy-distance-one);

  .ApplyForAdjustResultName {
    font-size: var(--zy-title-font-size);
    font-weight: bold;
    color: var(--zy-el-color-primary);
    border-bottom: 1px solid var(--zy-el-color-primary);
    text-align: center;
    padding: 20px 0;
    margin-bottom: 20px;
  }

  .global-info {
    padding-bottom: 12px;

    .global-info-item {
      .global-info-label {
        width: 160px;
      }

      .global-info-content {
        width: calc(100% - 160px);
      }
    }
  }
}
</style>
