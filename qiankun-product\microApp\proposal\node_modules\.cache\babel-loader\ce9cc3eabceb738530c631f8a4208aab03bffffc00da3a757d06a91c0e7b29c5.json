{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport SuggestClueDetails from './components/SuggestClueDetails';\nimport { clueExportWord } from '@/assets/js/suggestExportWord';\nvar __default__ = {\n  name: 'SuggestClueRegister'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var detailsShow = ref(false);\n    var id = ref('');\n    var _GlobalTable = GlobalTable({\n        tableApi: 'proposalClueList',\n        tableDataObj: {\n          query: {\n            ifPublish: '1'\n          }\n        }\n      }),\n      keyword = _GlobalTable.keyword,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      handleGetParams = _GlobalTable.handleGetParams;\n    onActivated(function () {\n      handleQuery();\n    });\n    var buttonList = [{\n      id: 'exportWord',\n      name: '导出Word',\n      type: 'primary',\n      has: 'export'\n    }];\n    var handleDetail = function handleDetail(item) {\n      id.value = item.id;\n      console.log(id.value, 'id.value');\n      detailsShow.value = true;\n    };\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'exportWord':\n          clueExportWord(handleGetParams());\n          break;\n        default:\n          break;\n      }\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var __returned__ = {\n      detailsShow,\n      id,\n      keyword,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      handleTableSelect,\n      handleGetParams,\n      buttonList,\n      handleDetail,\n      handleButton,\n      handleReset,\n      ref,\n      onActivated,\n      get format() {\n        return format;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get SuggestClueDetails() {\n        return SuggestClueDetails;\n      },\n      get clueExportWord() {\n        return clueExportWord;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "format", "GlobalTable", "SuggestClueDetails", "clueExportWord", "__default__", "name", "detailsShow", "id", "_GlobalTable", "tableApi", "tableDataObj", "query", "ifPublish", "keyword", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "handleTableSelect", "handleGetParams", "buttonList", "type", "has", "handleDetail", "item", "value", "console", "log", "handleButton", "isType", "handleReset"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestClue/SuggestClueRegister/SuggestClueRegister.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestClueRegister\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column label=\"标题\"\r\n                         min-width=\"280\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            <el-link @click=\"handleDetail(scope.row)\"\r\n                     type=\"primary\">{{ scope.row.title }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"线索类别\"\r\n                         min-width=\"120\"\r\n                         prop=\"proposalClueType.label\" />\r\n        <el-table-column label=\"线索来源\"\r\n                         width=\"120\"\r\n                         prop=\"terminalName\" />\r\n        <el-table-column label=\"提供者\"\r\n                         min-width=\"120\"\r\n                         prop=\"createByName\" />\r\n        <el-table-column label=\"提交时间\"\r\n                         width=\"190\">\r\n          <template #default=\"scope\">{{ format(scope.row.createDate) }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"采纳次数\"\r\n                         width=\"120\"\r\n                         prop=\"useTimes\" />\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"detailsShow\"\r\n                      name=\"提案线索详情\">\r\n      <SuggestClueDetails :id=\"id\"></SuggestClueDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestClueRegister' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SuggestClueDetails from './components/SuggestClueDetails'\r\nimport { clueExportWord } from '@/assets/js/suggestExportWord'\r\nconst detailsShow = ref(false)\r\nconst id = ref('')\r\n\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleGetParams,\r\n} = GlobalTable({ tableApi: 'proposalClueList', tableDataObj: { query: { ifPublish: '1' } } })\r\n\r\nonActivated(() => {\r\n  handleQuery()\r\n})\r\n\r\nconst buttonList = [\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: 'export' },\r\n]\r\n\r\nconst handleDetail = (item) => {\r\n  id.value = item.id\r\n  console.log(id.value, 'id.value')\r\n  detailsShow.value = true\r\n}\r\n\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      clueExportWord(handleGetParams())\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestClueRegister {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAqEA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,SAASC,cAAc,QAAQ,+BAA+B;AAP9D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAsB,CAAC;;;;;IAQ9C,IAAMC,WAAW,GAAGR,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAMS,EAAE,GAAGT,GAAG,CAAC,EAAE,CAAC;IAElB,IAAAU,YAAA,GAWIP,WAAW,CAAC;QAAEQ,QAAQ,EAAE,kBAAkB;QAAEC,YAAY,EAAE;UAAEC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAI;QAAE;MAAE,CAAC,CAAC;MAV5FC,OAAO,GAAAL,YAAA,CAAPK,OAAO;MACPC,QAAQ,GAAAN,YAAA,CAARM,QAAQ;MACRC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,MAAM,GAAAR,YAAA,CAANQ,MAAM;MACNC,QAAQ,GAAAT,YAAA,CAARS,QAAQ;MACRC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,SAAS,GAAAX,YAAA,CAATW,SAAS;MACTC,WAAW,GAAAZ,YAAA,CAAXY,WAAW;MACXC,iBAAiB,GAAAb,YAAA,CAAjBa,iBAAiB;MACjBC,eAAe,GAAAd,YAAA,CAAfc,eAAe;IAGjBvB,WAAW,CAAC,YAAM;MAChBqB,WAAW,CAAC,CAAC;IACf,CAAC,CAAC;IAEF,IAAMG,UAAU,GAAG,CACjB;MAAEhB,EAAE,EAAE,YAAY;MAAEF,IAAI,EAAE,QAAQ;MAAEmB,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAS,CAAC,CACrE;IAED,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,IAAI,EAAK;MAC7BpB,EAAE,CAACqB,KAAK,GAAGD,IAAI,CAACpB,EAAE;MAClBsB,OAAO,CAACC,GAAG,CAACvB,EAAE,CAACqB,KAAK,EAAE,UAAU,CAAC;MACjCtB,WAAW,CAACsB,KAAK,GAAG,IAAI;IAC1B,CAAC;IAED,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,YAAY;UACf7B,cAAc,CAACmB,eAAe,CAAC,CAAC,CAAC;UACjC;QACF;UACE;MACJ;IACF,CAAC;IAED,IAAMW,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBpB,OAAO,CAACe,KAAK,GAAG,EAAE;MAClBR,WAAW,CAAC,CAAC;IACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}