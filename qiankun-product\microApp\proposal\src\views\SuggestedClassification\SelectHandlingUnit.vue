<template>
  <div class="SelectHandlingUnit">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="办理方式" prop="transactType">
        <el-select v-model="form.transactType" placeholder="请选择办理方式" @change="transactTypeChange" clearable>
          <el-option label="主办/协办" value="main_assist" />
          <el-option label="分办" value="publish" />
        </el-select>
      </el-form-item>
      <template v-if="form.transactType === 'main_assist'">
        <el-form-item label="主办单位" prop="mainHandleOfficeId" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.mainHandleOfficeId" :filterId="form.handleOfficeIds"
            :max="1"></suggest-simple-select-unit>
        </el-form-item>
      </template>
      <template v-if="form.transactType === 'main_assist'">
        <el-form-item label="协办单位" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.handleOfficeIds"
            :filterId="form.mainHandleOfficeId"></suggest-simple-select-unit>
        </el-form-item>
      </template>
      <template v-if="form.transactType === 'publish'">
        <el-form-item label="分办单位" prop="handleOfficeIds" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.handleOfficeIds"></suggest-simple-select-unit>
        </el-form-item>
      </template>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SelectHandlingUnit' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({ ids: { type: Array, default: () => ([]) } })
const emit = defineEmits(['callback'])
const formRef = ref()
const form = reactive({
  transactType: '', // 请选择办理方式 默认主办/协办
  mainHandleOfficeId: [],
  handleOfficeIds: []
})
const rules = reactive({
  transactType: [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }],
  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],
  handleOfficeIds: [{ type: 'array', required: false, message: '请选择协办单位', trigger: ['blur', 'change'] }]
})
const transactTypeChange = () => {
  if (form.transactType === 'main_assist') {
    rules.mainHandleOfficeId = [{ type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else if (form.transactType === 'publish') {
    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else {
    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  }
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const globalJson = async () => {
  var params = {
    suggestionIds: props.ids,
    handleOfficeType: form.transactType || null, // 办理方式
    mainHandleOfficeId: form.mainHandleOfficeId.join('') || null, // 主办单位
    handleOfficeIds: form.handleOfficeIds || null, // 协办或分办单位
  }
  const { code } = await api.reqProposalBatchComplete('add', params)
  if (code === 200) {
    ElMessage({ type: 'success', message: '成功' })
    emit('callback', 'submit')
  }
}
const resetForm = () => {
  form.mainHandleOfficeId = []
  form.handleOfficeIds = []
  emit('callback')
}

// 监听主办单位变化，清除验证错误
watch(() => form.mainHandleOfficeId, (newVal) => {
  if (newVal && newVal.length > 0 && formRef.value) {
    // 清除主办单位字段的验证错误
    formRef.value.clearValidate('mainHandleOfficeId')
  }
}, { deep: true })

// 监听分办单位变化，清除验证错误
watch(() => form.handleOfficeIds, (newVal) => {
  if (newVal && newVal.length > 0 && formRef.value) {
    // 清除分办单位字段的验证错误
    formRef.value.clearValidate('handleOfficeIds')
  }
}, { deep: true })

onMounted(() => { })

</script>
<style lang="scss">
.suggest-simple-select-unit {
  border: 1px solid #ebebeb;
  border-radius: 4px;
}

.SelectHandlingUnit {
  width: 680px;
}
</style>
