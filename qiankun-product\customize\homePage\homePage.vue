<!--
 * @FileDescription: 首页展示
 * @Author: 雷子涛
 * @Date: 2023/7/3
 * @LastEditors: 雷子涛
 * @LastEditTime: 2023/7/23
 -->
<template style="overflow: hidden;">
  <el-scrollbar class="homePage">
    <section class="zy-el-container">
      <main class="zy-el-list">
        <div class="zy-el-box leftMain">
          <div class="box-item pad-20 news">
            <div class="box-column mar-bom-18">
              <div class="item-caption">
                <img src="../img/news.png" />最新<span class="item-font">资讯</span>
              </div>
              <!-- <p class="item-more" @click="openWinNews">更多>></p> -->
            </div>
            <div class="news-list" v-if="newsData.length">
              <div v-for="(item, index) in newsData" :key="index" @click="newsHandle(item)">
                <div class="news-header" v-if="index == 0">
                  <img v-if="item.infoPic" :src="imgUrl(item.infoPic.split(',')[0])" />
                  <div class="title-box">
                    <h3 class="title-h3">{{ item.infoTitle }}</h3>
                    <p class="title-des">{{ getHtmlText(item.infoContent) }}</p>
                    <p class="title-time">{{ formatTime(item.pubTime) }}</p>
                  </div>
                </div>
                <p class="common-font" v-else>· {{ item.infoTitle }}</p>
              </div>
            </div>
            <el-empty description="暂无数据" v-else size="20" />
          </div>
          <div class="box-item pad-20 notice">
            <div class="box-column mar-bom-18">
              <div class="item-caption">
                <img src="../img/notice.png" />通知<span class="item-font">公告</span>
              </div>
              <!-- <p class="item-more" @click="openWinNotice" style="margin-right: 10px">
                更多>>
              </p> -->
            </div>

            <el-scrollbar class="notice-list" ref="noticeRef" v-if="noticeData.length">
              <div>
                <div class="notice-list-itemBox" v-for="(item, index) in noticeData" :key="index"
                  @click="noticeHandle(item)">
                  <div class="text-T">
                    <p class="top-text" v-show="item.isTop === 1">置顶</p>
                    <h3 class="txt-hid-1">{{ item.theme }}</h3>
                  </div>

                  <div class="text">
                    <p>{{ item.publishOfficeId }}</p>
                    <div class="text-box">
                      <p class="receipt" v-show="item.isReceipt">回执</p>
                      <p class="attachment" v-show="item.attachmentIds">附件</p>
                      <p>{{ timestampToTime(item.createDate) }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </el-scrollbar>
            <el-empty description="暂无数据" v-else size="20" />
          </div>
          <div class="box-item pad-20 tool">
            <div class="box-column mar-bom-18">
              <div class="item-caption">
                <img src="../img/tools.png" />系统工具
              </div>
            </div>
            <div class="tool-list">
              <div class="tool-list-item" @click="setFontType">
                <img src="../img/complicated_simple.png" />
                <p>繁简切换</p>
              </div>
              <div class="tool-list-item" @click="openWinFeed">
                <img src="../img/feedback.png" />
                <p>意见反馈</p>
              </div>
              <!-- <div class="tool-list-item">
                <img src="../img/service.png" />
                <p>在线客服</p>
              </div> -->
              <div class="tool-list-item" @click="openHelpFile">
                <img src="../img/help_document.png" />
                <p>帮助文档</p>
              </div>
            </div>
          </div>
        </div>
        <div class="zy-el-box centerMain">
          <div class="box-item pad-22 statist flexBox">
            <div class="box-column">
              <div class="item-caption">数据统计概览</div>
            </div>
            <div class="statist-list">
              <div class="statist-list-item">
                <div class="statistics_icon"></div>
                <p class="statist-title">用户总数（人）</p>
                <p class="statist-nums">{{ overview.userAmount }}</p>
              </div>
              <div class="statist-list-item">
                <div class="statistics_icon"></div>
                <p class="statist-title">累计登录（次）</p>
                <p class="statist-nums">{{ overview.allLoginTimes }}</p>
              </div>
              <div class="statist-list-item">
                <div class="statistics_icon"></div>
                <p class="statist-title">今日登录（次）</p>
                <p class="statist-nums">{{ overview.dayLoginTimes }}</p>
              </div>
              <div class="statist-list-item">
                <div class="statistics_icon"></div>
                <p class="statist-title">单位总数（个）</p>
                <p class="statist-nums">{{ overview.officeAmount }}</p>
              </div>
            </div>
          </div>
          <div class="box-item pad-22 represen">
            <div class="box-column mar-bom-20">
              <div class="item-caption">{{ statistiName() }}</div>
            </div>
            <div id="represenChart"></div>
          </div>
          <div class="box-item pad-22 system-user">
            <div class="box-column mar-bom-20">
              <div class="item-caption">系统活跃情况</div>
            </div>
            <div id="systemUserChart"></div>
          </div>
        </div>
        <div class="zy-el-box rightMain">
          <div class="box-item pad-20 group">
            <div class="box-column">
              <div class="item-caption">
                最新<span class="item-font">动态</span>
                <span class="special-font">&nbsp;&nbsp;&nbsp;(仅展示最新{{ activities.length }}条)</span>
              </div>
            </div>
            <el-scrollbar class="dynamic-new">
              <div>
                <div class="time-line">
                  <el-timeline :reverse="true">
                    <el-timeline-item v-for="(_item, index) in activities" :key="index" size="normal"
                      :hollow="_item.eventName == '登录系统' ? false : true" type="primary">
                      <div class="dynamic-time">
                        <p>{{ timestampToTimeNoYear(_item.eventDate) }}</p>
                      </div>
                      <div class="ml10">
                        <div class="list-title">
                          <p>{{ _item.userName + ' ' + _item.eventName }}</p>
                        </div>
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
            </el-scrollbar>
          </div>
          <div class="box-item pad-20 unit">
            <div class="box-column">
              <div class="item-caption">各机关单位使用情况</div>
            </div>
            <div class="unit-list">
              <el-table :data="officeData" ref="table1" :header-cell-style="{
                'font-size': '14px',
                'font-weight': 'bold',
                color: '#000000',
                'text-align': 'center',
              }" :cell-style="{ 'font-size': '16px', 'text-align': 'center' }" height="480" stripe style="width: 100%">
                <el-table-column prop="officeName" label="机关单位名称" :show-overflow-tooltip="true" />
                <el-table-column prop="currentMonthAmount" label="本月使用次数" :show-overflow-tooltip="true" />
                <el-table-column prop="amount" label="累计使用次数" :show-overflow-tooltip="true" />
              </el-table>
            </div>
          </div>
        </div>
      </main>
    </section>
  </el-scrollbar>
  <xyl-popup-window v-model="newsShow" name="详情">
    <AllInformationDetail :id="id" @callback="callback"></AllInformationDetail>
  </xyl-popup-window>

  <xyl-popup-window v-model="noticeShow" name="详情">
    <NoticeAnnouncementDetails :id="id" @callback="callback"></NoticeAnnouncementDetails>
  </xyl-popup-window>
  <xyl-popup-window v-model="qrShow" :name="`扫码查看（${openConfig.systemPlatform === 'CPPCC' ? '数字政协' : '数字人大'
    }App）`">
    <div class="qrcode-box">
      <div class="printQrcodeRef">
        <qrcode-vue :value="shareUrl" :size="300" level="H" />
      </div>
    </div>
  </xyl-popup-window>
  <xyl-popup-window v-model="feedbackShow" name="意见反馈">
    <FeedBack :mobile="user.mobile" @callback="feedbackCallback"></FeedBack>
  </xyl-popup-window>
  <xyl-popup-window v-model="HelpFileShow" name="帮助文档">
    <HelpDocument></HelpDocument>
  </xyl-popup-window>
</template>
<script>
export default { name: 'homePage' }
</script>
<script setup>
import api from '@/api'
import QrcodeVue from 'qrcode.vue'
import { onMounted, ref, reactive, inject, onUnmounted, markRaw, nextTick } from 'vue'
import * as echarts from 'echarts'
import { openConfig } from 'common/js/system_var.js'
import { get_font_family, change_font_family } from 'common/js/utils'
import { user } from 'common/js/system_var.js'
import AllInformationDetail from './components/AllInformationDetail'
import NoticeAnnouncementDetails from './components/NoticeAnnouncementDetails'
import FeedBack from './components/FeedBack'
import HelpDocument from '../LayoutContainer/components/HelpDocument'
const newsData = ref([])
const openPage = inject('openPage')

const shareUrl = ref('')
const qrShow = ref(false)

const newsShow = ref(false)
const noticeShow = ref(false)

const id = ref('')

const imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')

const overview = ref({})

const noticeData = ref([])

const memberData = ref([])


const systemData = ref([])

const officeData = ref([])

const activities = ref([])

const statistiName = () => {
  var systemPlatform = openConfig.value?.systemPlatform || ''
  return systemPlatform === 'CPPCC' ? '委员活跃情况' : '代表活跃情况'
}

//切换字体 cn简体 tw繁体
const setFontType = () => {
  if (get_font_family() == "cn") {
    change_font_family("tw")
    return
  }
  change_font_family("cn")
}


//时间戳转换
const timestampToTime = (time) => {
  var time = new Date(time);
  var y = time.getFullYear(); //getFullYear方法以四位数字返回年份
  var M = (time.getMonth() + 1) < 10 ? '0' + (time.getMonth() + 1) : time.getMonth() + 1; // getMonth方法从 Date 对象返回月份 (0 ~ 11)，返回结果需要手动加一
  var d = (time.getDate()) < 10 ? '0' + (time.getDate()) : time.getDate(); // getDate方法从 Date 对象返回一个月中的某一天 (1 ~ 31)
  var h = (time.getHours()) < 10 ? '0' + (time.getHours()) : time.getHours(); // getHours方法返回 Date 对象的小时 (0 ~ 23)
  var m = (time.getMinutes()) < 10 ? '0' + (time.getMinutes()) : time.getMinutes(); // getMinutes方法返回 Date 对象的分钟 (0 ~ 59)
  var s = (time.getSeconds()) < 10 ? '0' + (time.getSeconds()) : time.getSeconds(); // getSeconds方法返回 Date 对象的秒数 (0 ~ 59)
  return y + '-' + M + '-' + d + ' ' + h + ':' + m + ':' + s;
}

//时间戳转换-不带年份
const timestampToTimeNoYear = (time) => {
  var time = new Date(time);
  var y = time.getFullYear(); //getFullYear方法以四位数字返回年份
  var M = (time.getMonth() + 1) < 10 ? '0' + (time.getMonth() + 1) : time.getMonth() + 1; // getMonth方法从 Date 对象返回月份 (0 ~ 11)，返回结果需要手动加一
  var d = (time.getDate()) < 10 ? '0' + (time.getDate()) : time.getDate(); // getDate方法从 Date 对象返回一个月中的某一天 (1 ~ 31)
  var h = (time.getHours()) < 10 ? '0' + (time.getHours()) : time.getHours(); // getHours方法返回 Date 对象的小时 (0 ~ 23)
  var m = (time.getMinutes()) < 10 ? '0' + (time.getMinutes()) : time.getMinutes(); // getMinutes方法返回 Date 对象的分钟 (0 ~ 59)
  var s = (time.getSeconds()) < 10 ? '0' + (time.getSeconds()) : time.getSeconds(); // getSeconds方法返回 Date 对象的秒数 (0 ~ 59)
  return M + '月' + d + '日' + h + ':' + m;
}


const getHtmlText = (val) => {
  if (val != null && val != "") {
    val = val.replace(/<\/?style[^>]*>[\s\S]*?<\/style>/ig, '') //去除特殊style标签
    var re1 = new RegExp("<.+?>|&.+?;", "g"); //匹配html标签的正则表达式，"g"是搜索匹配多个符合的内容
    var msg = val.replace(re1, ""); //执行替换成空字符
    msg = msg.replace(/\s/g, ""); //去掉所有的空格（中文空格、英文空格都会被替换）
    msg = msg.replace(/[\r\n]/g, ""); //去掉所有的换行符
    return msg.substr(0, 150); //获文本文字内容的前100个字符
  }
  else return ''
}

const table1 = ref();
const noticeRef = ref()
const scroll = (tableBody) => {
  let isScroll = true;
  const dom1 = tableBody.getElementsByClassName("zy-el-scrollbar__wrap")[0];

  //鼠标放上去，停止滚动；移开，继续滚动
  dom1.addEventListener("mouseover", () => {
    isScroll = false;
  });
  dom1.addEventListener("mouseout", () => {
    isScroll = true;
  });

  setInterval(() => {
    if (isScroll) {
      dom1.scrollTop += 1;
      if (dom1.clientHeight + dom1.scrollTop == dom1.scrollHeight) {
        dom1.scrollTop = 0;
      }
    }
  }, 100);
}
const resizeChart = () => {
  if (represenChart.value) { represenChart.value.resize(); }
  if (systemUserChart.value) { systemUserChart.value.resize(); }
};

const represenChart = ref(null);
const option1 = ref({
  xAxis: {
    type: 'category',
    boundaryGap: true,
    data: [],
    axisLine: {
      show: false  //隐藏x轴线
    },
    axisTick: {
      show: false //隐藏x轴刻度
    }
  },
  tooltip: {
    show: true,
    trigger: 'item',
    axisPointer: {
      // 坐标轴指示器，坐标轴触发有效
      type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
    },
  },
  color: ['#F9A300', '#F3D300FF'],
  legend: {// 图例
    orient: 'horizontal',//horizontal 水平显示，vertical 垂直显示
    bottom: '10%',
    left: 'center',
    top: 0,
    itemGap: 20,
    width: 600, // 单行图例的宽度
    // data:["登录人数","登录次数"]
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985',
      },
    },
  },
  grid: {
    left: "8%",
    top: "10%",
    right: "0%",
    bottom: "8%"
  },
  title: [
    {
      subtext: "单位/次", //副标题
      // itemGap: 6, //主副标题间距
      right: '0%', //标题的位置 默认是left，其余还有center、right属性
      top: '-4%',
      subtextStyle: {
        color: "#767881",
        fontSize: 13,
        textAlign: 'center'
      }
    }
  ],
  yAxis: {
    type: 'value',
    name: '单位/人'
  },
  series: [
    {
      name: "登录人数",
      data: [],
      type: 'bar',
      backgroundStyle: {
        color: 'rgba(180, 180, 180, 0.2)'
      },
      barWidth: 20,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#ADD736' // 0% 处的颜色
          }, {
            offset: 1, color: '#F3D300FF' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }
    }, {
      name: "登录次数",
      data: [],
      type: 'line',
      showSymbol: false, //去掉折线上的小圆点
      smooth: true,
      legend: {// 图例
        orient: 'horizontal',//horizontal 水平显示，vertical 垂直显示
        bottom: '5%',
        left: 'center',
        top: 0,
        itemGap: 20,
        width: 600, // 单行图例的宽度
      },
      //设置折线颜色和粗细
      lineStyle: {
        width: 1,
        color: "#ADD736",
      },
      // 区域填充样式
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#F3D300' // 0% 处的颜色
          }, {
            offset: 0.6, color: '#f3d30000' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      },
    }
  ]
})
const initChrt_1 = (name, amount, userAmount) => {
  option1.value.xAxis.data = name
  option1.value.series[0].data = userAmount
  option1.value.series[1].data = amount
  //代表活跃情况
  represenChart.value = markRaw(echarts.init(document.getElementById('represenChart')))
  represenChart.value.setOption(option1.value)
}

const systemUserChart = ref(null)
const option2 = ref({
  xAxis: {
    type: 'category',
    boundaryGap: true,
    data: [],
    axisLine: {
      show: false  //隐藏x轴线
    },
    axisTick: {
      show: false //隐藏x轴刻度
    }
  },
  title: [
    {
      subtext: "单位/次", //副标题
      // itemGap: 6, //主副标题间距
      right: '0%', //标题的位置 默认是left，其余还有center、right属性
      top: '-4%',
      subtextStyle: {
        color: "#767881",
        fontSize: 13,
        textAlign: 'center'
      }
    }
  ],
  legend: {// 图例
    orient: 'horizontal',//horizontal 水平显示，vertical 垂直显示
    bottom: '10%',
    left: 'center',
    top: 0,
    itemGap: 20,
    width: 600, // 单行图例的宽度
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985',
      },
    },
  },
  grid: {
    left: "8%",
    top: "10%",
    right: "0%",
    bottom: "8%"
  },
  yAxis: {
    type: 'value',
    name: '单位/人'
  },
  series: [
    {
      name: '登录人数',
      data: [],
      type: 'bar',
      backgroundStyle: {
        color: 'rgba(180, 180, 180, 0.2)'
      },
      barWidth: 20,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#6AB9C3' // 0% 处的颜色
          }, {
            offset: 1, color: '#6ab9c35c' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }
    },
    {
      name: '登录次数',
      data: [],
      type: 'line',
      showSymbol: false, //去掉折线上的小圆点
      smooth: true,
      //设置折线颜色和粗细
      lineStyle: {
        width: 1,
        color: "#6CA8FD",
      },
      // 区域填充样式
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#6CA8FD' // 0% 处的颜色
          }, {
            offset: 0.6, color: '#6ca8fd00' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      },
    }
  ]
})
const initChrt_2 = ((name, amount, userAmount) => {
  option2.value.xAxis.data = name
  option2.value.series[0].data = userAmount
  option2.value.series[1].data = amount
  //系统活跃情况
  systemUserChart.value = markRaw(echarts.init(document.getElementById('systemUserChart')))
  systemUserChart.value.setOption(option2.value)
})

const formatTime = (time) => {
  time = Number(time)
  return new Date(time).toLocaleString()
}
const isHengImg = ref(true)
//资讯
const getNewsData = async () => {
  let params = {
    businessCode: 'informationContent',
    topTotalCount: '5',
  }
  const res = await api.homePageDocument(params)
  var { data } = res
  newsData.value = data
  nextTick(() => {
    if (newsData.value.length > 0 && newsData.value[0].infoPic) {
      const newImg = document.getElementById('newImg0')
      // 容器宽高比例
      const proportion = newImg.clientWidth / newImg.clientHeight
      const firstImageSrc = imgUrl(newsData.value[0].infoPic.split(',')[0])
      const img = new Image()
      img.src = firstImageSrc
      img.onload = () => {
        isHengImg.value = img.naturalWidth / img.naturalHeight > proportion
      }
      img.onerror = () => {
        console.error('无法加载第一张图片以获取尺寸。')
      }
    }
  })
}

//公告
const getnotiData = async () => {
  let params = {
    isAnd: 1,
    isSelectForManager: 1,
    pageNo: 1,
    pageSize: 8,
    query: {
      isDraft: 0
    },
    tableId: "id_message_notification"
  }
  const res = await api.noticeHomePage(params)
  var { data } = res
  noticeData.value = data //data
  // scroll_2(noticeRef.value.$refs.scrollbarRef)
}

//综合统计
const synthesize = async () => {
  let params = {
    businessCode: 'userCompositeCount',
    topTotalCount: '5'
  }
  const res = await api.homePageDocument(params)
  var { data } = res
  overview.value = data
}

//月度统计
const monthly = async () => {
  let params = {
    businessCode: 'loginComposite',
    topTotalCount: '5'
  }
  const res = await api.homePageDocument(params)
  var { data } = res
  memberData.value = data.memberMonthCountItems
  systemData.value = data.monthCountItems
  officeData.value = data.officeCountItems
  if (data.length != 0) {
    initChrt_1(memberData.value.map((v) => { return v.name }), memberData.value.map((v) => { return v.amount }), memberData.value.map((v) => { return v.userAmount }))
    initChrt_2(systemData.value.map((v) => { return v.name }), systemData.value.map((v) => { return v.amount }), systemData.value.map((v) => { return v.userAmount }))
  }
  scroll(table1.value.$refs.bodyWrapper);
}


//最新动态
const getDynamic = async () => {
  let params = {
    businessCode: 'systemEvent',
    topTotalCount: '5'
  }
  const res = await api.homePageDocument(params)
  var { data } = res
  activities.value = data
}

const newsHandle = (item) => {
  id.value = item.id
  newsShow.value = true
}

const noticeHandle = (item) => {
  id.value = item.id
  noticeShow.value = true
}

const callback = () => {
  id.value = ""
  newsShow.value = false
  noticeShow.value = false
  qrShow.value = false
}

const openWinNotice = async () => {
  // 不采用app扫码方式 跳转列表 2024.10.28
  // const { data } = await api.globalReadOpenConfig({ codes: ['appShareAddress'] })
  // shareUrl.value = `${data.appShareAddress}pages/index/index.html?%7B%22n%22:%22mo_notice_list%22,%22u%22:%22../mo_notice_list/mo_notice_list.stml%22,%22p%22:%7B%22headTheme%22:%22#FFF%22,%22appTheme%22:%22#BC1D1D%22,%22areaId%22:%22${user.value.areaId}%22,%22v%22:%221%22%7D%7D`
  // qrShow.value = true
  openPage({ key: 'routePath', value: '/interaction/NoticeAnnouncementList' })
}

const openWinNews = async () => {
  // 不采用app扫码方式 跳转列表 2024.10.28
  // const { data } = await api.globalReadOpenConfig({ codes: ['appShareAddress'] })
  // shareUrl.value = `${data.appShareAddress}pages/index/index.html?%7B%22n%22:%22mo_news%22,%22u%22:%22../mo_news/mo_news.stml%22,%22p%22:%7B%22headTheme%22:%22#FFF%22,%22appTheme%22:%22#BC1D1D%22,%22areaId%22:%22${user.value.areaId}%22,%22v%22:%221%22%7D%7D`
  // qrShow.value = true
  openPage({ key: 'routePath', value: '/information/AllInformationPublicList?moduleId=1' })
}

// 意见反馈
const feedbackShow = ref(false)
const openWinFeed = async () => { feedbackShow.value = true }
const feedbackCallback = (i) => {
  feedbackShow.value = false
}

//帮助文档
const HelpFileShow = ref(false)
const openHelpFile = () => {
  HelpFileShow.value = true
}

// 在组件即将卸载前执行的操作
onUnmounted(() => {
  if (represenChart.value) { represenChart.value.dispose(); }
  if (systemUserChart.value) { systemUserChart.value.dispose(); }
  window.removeEventListener('resize', resizeChart);
});

onMounted(() => {
  getNewsData()
  synthesize()
  monthly()
  getnotiData()
  getDynamic()
  window.addEventListener('resize', resizeChart);
})

</script>

<style lang="scss" scoped>
body {
  margin: 0;
  padding: 0;
}

.qrcode-box {
  padding: 20px;
  width: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}



.printQrcodeRef {
  padding: 20px;
  background-color: #fff;
}

.homePage {
  width: 100%;
  height: 100%;
  background: #f8f8f8;

  .zy-el-container {
    width: 100%;
    height: 100%;
  }

  .zy-el-list {
    width: 100%;
    padding: 0 16px;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .zy-el-box {
    display: flex;
    flex-direction: column;
    margin-right: 16px;
    padding: 16px 0;

    .box-item {
      background: #ffffff;
      border-radius: 5px;
      margin-bottom: 16px;
    }

    .flexBox {
      display: flex;
      flex-direction: column;
    }
  }

  .txt-hid-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover {
      color: var(--zy-el-color-primary-light-3);
    }
  }

  .leftMain {
    flex: 1;
    width: 35%;
  }

  .centerMain {
    min-width: 450px;
    width: 35%;
  }

  .rightMain {
    width: 30%;
  }

  .zy-el-box .box-item:nth-last-child(1) {
    margin-bottom: 0;
  }

  .mar-bom-18 {
    margin-bottom: 18px;
  }

  .mar-bom-20 {
    margin-bottom: 20px;
  }

  .pad-20 {
    padding: 20px 30px;
  }

  .pad-22 {
    padding: 20px 30px 25px;
  }

  .box-column {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;

    .item-caption {
      font-size: 18px;
      font-weight: bold;
      display: flex;
      align-items: center;

      .item-font {
        color: var(--zy-el-color-primary);
      }

      .special-font {
        font-weight: normal;
        font-size: 14px;
        color: #999999;
      }

      img {
        margin-right: 6px;
        background: var(--zy-el-color-primary);
      }
    }

    .item-more {
      font-size: 14px;
      color: grey;
      cursor: pointer;

      &:hover {
        color: var(--zy-el-color-primary-light-3);
      }
    }
  }

  .news {
    width: 100%;
    height: 470px;

    .news-list {
      height: auto;

      .news-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 30px;
        cursor: pointer;
        height: 190px;

        .new_img {
          flex: 0 0 70%;
          height: 100%;
          position: relative;
          margin-right: 20px;

          .background-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              // object-fit: cover;
              filter: blur(2px);
            }
          }

          .overlay-img {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            // max-width: 100%;
            height: 100%;
            z-index: 2;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              height: 100%;
              // object-fit: cover;
            }
          }

          .overlay-img-h {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            // max-width: 100%;
            height: 100%;
            z-index: 2;
            overflow: hidden;
            display: flex;
            align-items: center;

            img {
              width: 100%;
              // height: 100%;
              // object-fit: cover;
            }
          }
        }

        .title-box {
          flex: 1;
          height: 100%;
          display: flex;
          flex-direction: column;

          .title-h3 {
            font-size: 16px;
            line-height: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333333;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;

            &:hover {
              color: var(--zy-el-color-primary-light-3);
            }
          }

          .title-des {
            font-size: 14px;
            color: #666666;
            margin-bottom: 10px;
            line-height: 21px;
            height: 84px;
            -webkit-line-clamp: 4;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .title-time {
            font-size: 16px;
            color: #999999;
            margin-top: auto;
          }
        }
      }

      .common-font {
        font-size: 16px;
        margin-bottom: 30px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;

        &:hover {
          color: var(--zy-el-color-primary-light-3);
        }
      }
    }

    .news-list .common-font:child(4) {
      margin-bottom: 0;
    }
  }

  .notice {
    width: 100%;
    height: 326px;

    .notice-list {
      height: 244px;

      .notice-list-itemBox {
        margin-bottom: 18px;
        margin-right: 10px;

        .text-T {
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-bottom: 6px;
          cursor: pointer;

          .top-text {
            background: var(--zy-el-color-primary-light-9);
            color: var(--zy-el-color-primary);
            padding: 1px 3px;
            margin-right: 3px;
            min-width: 40px;
          }

          h3 {
            font-size: 16px;
            font-weight: 400;
          }
        }

        .text {
          display: flex;
          justify-content: space-between;
          cursor: pointer;

          .text-box {
            display: flex;
            align-items: center;
            flex-direction: row;

            .receipt {
              font-size: 14px;
              line-height: 18px;
              color: var(--zy-el-color-warning);
              padding: 0 8px;
              border-radius: 10px;
              border: 1px solid var(--zy-el-color-warning);
              margin-right: 10px;
            }

            .attachment {
              font-size: 14px;
              line-height: 18px;
              color: #135dcdff;
              padding: 0 8px;
              border-radius: 10px;
              border: 1px solid #135dcdff;
              margin-right: 10px;
            }
          }

          p {
            font-size: 14px;
            color: gray;
          }
        }
      }
    }

    .notice-list .notice-list-itemBox:nth-last-child(1) {
      margin-bottom: 0px;
    }
  }

  .tool {
    width: 100%;
    height: 162px;

    .tool-list {
      display: flex;

      .tool-list-item {
        width: 129px;
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f9f9fa;
        margin-right: 20px;
        border-radius: 5px;
        cursor: pointer;

        &:hover {
          // box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.12);
          box-shadow: 3px 6px 8px 3px rgba(0, 0, 0, 0.12);
          color: var(--zy-el-color-primary-light-3);
          transform: translate(0px, -6px);
        }

        img {
          width: 24px;
          height: 24px;
        }

        p {
          font-size: 16px;
          text-align: center;
        }
      }
    }
  }

  .statist {
    width: 100%;
    height: 184px;

    .statist-list {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      flex: 1;
      align-items: center;

      .statist-list-item {
        width: 50%;
        display: flex;

        .statist-title {
          font-size: 16px;
        }

        .statist-nums {
          font-size: 18px;
          font-weight: bold;
        }
      }
    }

    .statist-list .statist-list-item:nth-child(2n-1) {
      .statistics_icon {
        width: 20px;
        height: 20px;
        background: url(../img/statistics_orange.png);
        margin-right: 4px;
      }

      .statist-nums {
        color: #ef8331;
      }
    }

    .statist-list .statist-list-item:nth-child(2n) {
      .statistics_icon {
        width: 20px;
        height: 20px;
        background: url(../img/statistics_green.png);
        margin-right: 4px;
      }

      .statist-nums {
        color: #77bf70;
      }
    }

    .statist-list .statist-list-item:nth-child(5) {
      margin-bottom: 0;
    }

    .statist-list .statist-list-item:nth-child(6) {
      margin-bottom: 0;
    }
  }

  .represen {
    width: 100%;
    height: 387px;

    #represenChart {
      width: 100%;
      height: 286px;
    }
  }

  .system-user {
    width: 100%;
    height: 387px;

    #systemUserChart {
      width: 100%;
      height: 286px;
    }
  }

  .group {
    width: 100%;
    height: 386px;

    #groupChart {
      width: 360px;
      height: 312px;
    }
  }

  .unit {
    width: 100%;
    height: 588px;

    .unit-list {
      width: 100%;
      height: 506px;
    }
  }

  .example-showcase .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
  }

  .dynamic-new {
    height: 90%;
  }

  .time-line {
    padding: 20px 0 0 110px;
    width: 100%;

    .list-title {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333ff;
    }

    //左侧时间
    .dynamic-time {
      color: #666666ff;
      position: absolute;
      left: -110px;
      font-size: 14px;
      top: 1px;
    }
  }
}

@media screen and (max-width: 1280px) {
  .news-header {
    display: flex;
    margin-bottom: 30px;
    height: 100%;
    cursor: pointer;
    flex-direction: column;

    img {
      max-width: 100%;
      height: 172px;
      margin-right: 20px;
    }
  }
}
</style>
