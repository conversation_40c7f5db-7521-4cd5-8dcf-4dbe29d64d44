<template>
  <div class="onLineUserList">
    <div class="globalTable">
      <el-table ref="tableRef"
                row-key="id"
                :data="tableData">
        <el-table-column type="selection"
                         reserve-selection
                         width="60"
                         fixed />
        <el-table-column label="序号"
                         width="80"
                         prop="sort" />
        <el-table-column label="姓名"
                         min-width="160"
                         prop="userName" />
        <el-table-column label="所属地区"
                         min-width="160"
                         prop="areaName" />
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo"
                     v-model:page-size="pageSize"
                     :page-sizes="pageSizes"
                     layout="total, sizes, prev, pager, next, jumper"
                     @size-change="handleQuery"
                     @current-change="handleQuery"
                     :total="totals"
                     background />
    </div>
  </div>
</template>
<script>
export default { name: 'onLineUserList' }
</script>
<script setup>
import { onMounted } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
const {
  // keyword,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  handleQuery,
} = GlobalTable({
  tableApi: 'onlineaccount'
})

onMounted(() => { handleQuery() })

</script>
<style lang="scss">
.onLineUserList {
  width: 600px;
  height: calc(85vh - 52px);
  padding: 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2)));
  }
}
</style>
