{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestNumbering\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"termYearId\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"届次\",\n        \"min-width\": \"160\",\n        prop: \"companyUser\",\n        \"show-overflow-tooltip\": \"\"\n      }, {\n        default: _withCtx(function (scope) {\n          var _scope$row$termYear, _scope$row$termYear2;\n          return [_createTextVNode(_toDisplayString((_scope$row$termYear = scope.row.termYear) === null || _scope$row$termYear === void 0 || (_scope$row$termYear = _scope$row$termYear.circlesType) === null || _scope$row$termYear === void 0 ? void 0 : _scope$row$termYear.label) + _toDisplayString((_scope$row$termYear2 = scope.row.termYear) === null || _scope$row$termYear2 === void 0 || (_scope$row$termYear2 = _scope$row$termYear2.boutType) === null || _scope$row$termYear2 === void 0 ? void 0 : _scope$row$termYear2.label), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"大会编号前缀\",\n        \"min-width\": \"160\",\n        prop: \"meetingNumberPrefix\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"大会起始编号\",\n        \"min-width\": \"160\",\n        prop: \"meetingStartNumber\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"平时编号前缀\",\n        \"min-width\": \"160\",\n        prop: \"usualNumberPrefix\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"平时起始编号\",\n        \"min-width\": \"160\",\n        prop: \"usualStartNumber\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"大会时间\",\n        \"min-width\": \"360\",\n        prop: \"companyUser\",\n        \"show-overflow-tooltip\": \"\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString($setup.format(scope.row.meetingStartDate)) + \" - \" + _toDisplayString($setup.format(scope.row.meetingEndDate)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_xyl_global_table_button, {\n        data: $setup.tableButtonList,\n        onButtonClick: $setup.handleCommand\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: $setup.id ? '编辑' : '新增'\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SuggestNumberingSubmit\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "ref", "data", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "default", "_component_el_table_column", "type", "width", "fixed", "label", "prop", "scope", "_scope$row$termYear", "_scope$row$termYear2", "_createTextVNode", "_toDisplayString", "row", "termYear", "circlesType", "boutType", "format", "meetingStartDate", "meetingEndDate", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "handleCommand", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "show", "name", "id", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\SuggestNumbering.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestNumbering\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"termYearId\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column label=\"届次\"\r\n                         min-width=\"160\"\r\n                         prop=\"companyUser\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">{{ scope.row.termYear?.circlesType?.label }}{{ scope.row.termYear?.boutType?.label }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"大会编号前缀\"\r\n                         min-width=\"160\"\r\n                         prop=\"meetingNumberPrefix\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"大会起始编号\"\r\n                         min-width=\"160\"\r\n                         prop=\"meetingStartNumber\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"平时编号前缀\"\r\n                         min-width=\"160\"\r\n                         prop=\"usualNumberPrefix\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"平时起始编号\"\r\n                         min-width=\"160\"\r\n                         prop=\"usualStartNumber\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"大会时间\"\r\n                         min-width=\"360\"\r\n                         prop=\"companyUser\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            {{ format(scope.row.meetingStartDate) }} - {{ format(scope.row.meetingEndDate) }}\r\n          </template>\r\n        </el-table-column>\r\n        <xyl-global-table-button :data=\"tableButtonList\"\r\n                                 @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      :name=\"id ? '编辑' : '新增'\">\r\n      <SuggestNumberingSubmit :id=\"id\"\r\n                              @callback=\"callback\"></SuggestNumberingSubmit>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestNumbering' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SuggestNumberingSubmit from './component/SuggestNumberingSubmit'\r\nconst buttonList = [\r\n  { id: 'new', name: '新增', type: 'primary', has: 'new' }\r\n  // { id: 'del', name: '删除', type: '', has: 'del' }\r\n]\r\nconst tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: 'edit' }]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset\r\n} = GlobalTable({ tableApi: 'suggestionTermYearList', delApi: 'suggestionTermYearDel', valId: 'termYearId' })\r\n\r\nonActivated(() => { handleQuery() })\r\n\r\nconst handleButton = (id) => {\r\n  switch (id) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'del':\r\n      handleDel('届次编号')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleNew = () => {\r\n  id.value = ''\r\n  show.value = true\r\n}\r\nconst handleEdit = (item) => {\r\n  id.value = item.termYearId\r\n  show.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestNumbering {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAYtBA,KAAK,EAAC;AAAa;;EA6CnBA,KAAK,EAAC;AAAkB;;;;;;;;;uBAzD/BC,mBAAA,CAwEM,OAxENC,UAwEM,GAvEJC,YAAA,CAUoBC,4BAAA;IAVAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IACvBC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IACvBC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC1BC,UAAU,EAAEN,MAAA,CAAAM;;IACnBC,MAAM,EAAAC,QAAA,CACf;MAAA,OAGsB,CAHtBX,YAAA,CAGsBY,mBAAA;QAV9BC,UAAA,EAO2BV,MAAA,CAAAW,OAAO;QAPlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAO2Bb,MAAA,CAAAW,OAAO,GAAAE,MAAA;QAAA;QAChBC,WAAW,EAAC,QAAQ;QACnBC,OAAK,EATxBC,SAAA,CASgChB,MAAA,CAAAC,WAAW;QACzBgB,SAAS,EAAT;;;IAVlBC,CAAA;uCAaIC,mBAAA,CA4CM,OA5CNC,UA4CM,GA3CJvB,YAAA,CA0CWwB,mBAAA;IA1CDC,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,YAAY;IACnBC,IAAI,EAAEvB,MAAA,CAAAwB,SAAS;IACfC,QAAM,EAAEzB,MAAA,CAAA0B,iBAAiB;IACzBC,WAAU,EAAE3B,MAAA,CAAA0B;;IAlB7BE,OAAA,EAAApB,QAAA,CAmBQ;MAAA,OAGyB,CAHzBX,YAAA,CAGyBgC,0BAAA;QAHRC,IAAI,EAAC,WAAW;QAChB,mBAAiB,EAAjB,EAAiB;QACjBC,KAAK,EAAC,IAAI;QACVC,KAAK,EAAL;UACjBnC,YAAA,CAMkBgC,0BAAA;QANDI,KAAK,EAAC,IAAI;QACV,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,aAAa;QAClB,uBAAqB,EAArB;;QACJN,OAAO,EAAApB,QAAA,CAAS,UAA4C2B,KAA9C;UAAA,IAAAC,mBAAA,EAAAC,oBAAA;UAAA,QA3BnCC,gBAAA,CAAAC,gBAAA,EAAAH,mBAAA,GA2BwCD,KAAK,CAACK,GAAG,CAACC,QAAQ,cAAAL,mBAAA,gBAAAA,mBAAA,GAAlBA,mBAAA,CAAoBM,WAAW,cAAAN,mBAAA,uBAA/BA,mBAAA,CAAiCH,KAAK,IAAAM,gBAAA,EAAAF,oBAAA,GAAMF,KAAK,CAACK,GAAG,CAACC,QAAQ,cAAAJ,oBAAA,gBAAAA,oBAAA,GAAlBA,oBAAA,CAAoBM,QAAQ,cAAAN,oBAAA,uBAA5BA,oBAAA,CAA8BJ,KAAK,iB;;QA3BvHf,CAAA;UA8BQrB,YAAA,CAGyCgC,0BAAA;QAHxBI,KAAK,EAAC,QAAQ;QACd,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,qBAAqB;QAC1B,uBAAqB,EAArB;UACjBrC,YAAA,CAGyCgC,0BAAA;QAHxBI,KAAK,EAAC,QAAQ;QACd,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,oBAAoB;QACzB,uBAAqB,EAArB;UACjBrC,YAAA,CAGyCgC,0BAAA;QAHxBI,KAAK,EAAC,QAAQ;QACd,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,mBAAmB;QACxB,uBAAqB,EAArB;UACjBrC,YAAA,CAGyCgC,0BAAA;QAHxBI,KAAK,EAAC,QAAQ;QACd,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,kBAAkB;QACvB,uBAAqB,EAArB;UACjBrC,YAAA,CAOkBgC,0BAAA;QAPDI,KAAK,EAAC,MAAM;QACZ,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,aAAa;QAClB,uBAAqB,EAArB;;QACJN,OAAO,EAAApB,QAAA,CAChB,UAAwC2B,KADjB;UAAA,QAlDnCG,gBAAA,CAAAC,gBAAA,CAmDevC,MAAA,CAAA4C,MAAM,CAACT,KAAK,CAACK,GAAG,CAACK,gBAAgB,KAAI,KAAG,GAAAN,gBAAA,CAAGvC,MAAA,CAAA4C,MAAM,CAACT,KAAK,CAACK,GAAG,CAACM,cAAc,kB;;QAnDzF5B,CAAA;UAsDQrB,YAAA,CACgFkD,kCAAA;QADtDxB,IAAI,EAAEvB,MAAA,CAAAgD,eAAe;QACrBC,aAAW,EAAEjD,MAAA,CAAAkD;;;IAvD/ChC,CAAA;4DA0DIC,mBAAA,CASM,OATNgC,UASM,GARJtD,YAAA,CAO4BuD,wBAAA;IAPLC,WAAW,EAAErD,MAAA,CAAAsD,MAAM;IA3DhD,wBAAA1C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2D0Cb,MAAA,CAAAsD,MAAM,GAAAzC,MAAA;IAAA;IACnB,WAAS,EAAEb,MAAA,CAAAuD,QAAQ;IA5DhD,qBAAA3C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA4DwCb,MAAA,CAAAuD,QAAQ,GAAA1C,MAAA;IAAA;IAC1B,YAAU,EAAEb,MAAA,CAAAwD,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAE1D,MAAA,CAAAC,WAAW;IACxB0D,eAAc,EAAE3D,MAAA,CAAAC,WAAW;IAC3B2D,KAAK,EAAE5D,MAAA,CAAA6D,MAAM;IACdC,UAAU,EAAV;qHAEjBjE,YAAA,CAImBkE,2BAAA;IAxEvBrD,UAAA,EAoE+BV,MAAA,CAAAgE,IAAI;IApEnC,uBAAApD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoE+Bb,MAAA,CAAAgE,IAAI,GAAAnD,MAAA;IAAA;IACZoD,IAAI,EAAEjE,MAAA,CAAAkE,EAAE;;IArE/BtC,OAAA,EAAApB,QAAA,CAsEM;MAAA,OACsE,CADtEX,YAAA,CACsEG,MAAA;QAD7CkE,EAAE,EAAElE,MAAA,CAAAkE,EAAE;QACNC,UAAQ,EAAEnE,MAAA,CAAAoE;;;IAvEzClD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}