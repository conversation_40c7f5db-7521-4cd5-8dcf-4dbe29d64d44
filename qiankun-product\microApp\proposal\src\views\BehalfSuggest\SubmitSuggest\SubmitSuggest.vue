<template>
  <el-scrollbar always class="SubmitSuggest" v-loading="loading" :lement-loading-text="loadingText">
    <div class="SubmitSuggestBody">
      <div style="position:absolute;right: -13%;top: 0%;" v-if="queryType == 'review'">
        <div class="detailsPrint" title="打印提案" @click="handleSuggestPrint">打印提案</div>
        <div class="detailsExportInfo" title="导出提案word" @click="handleExportWord">导出提案word</div>
      </div>
      <div class="SubmitSuggestNameBody">
        <dynamic-title templateCode="proposal_title" v-if="ProposalYear.description"
          :titles="ProposalYear.description"></dynamic-title>
        <dynamic-title templateCode="proposal_title" v-else></dynamic-title>
      </div>
      <el-form ref="formRef" :model="form" :rules="rules" inline :show-message="false" class="globalPaperForm">
        <el-form-item label="提案提交类型" v-if="!typeShow" prop="suggestSubmitWay" class="SubmitSuggestTitle">
          <el-radio-group v-model="form.suggestSubmitWay" @change="submitTypeChange">
            <el-radio label="cppcc_member">委员提案</el-radio>
            <el-radio label="team">集体提案</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="提案标题" prop="title" class="SubmitSuggestTitle">
          <el-input v-model="form.title" placeholder="请输入提案标题" show-word-limit :maxlength="suggestTitleNumber"
            clearable />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="提案者" prop="suggestUserId"
          class="SubmitSuggestLeft">
          <input-select-person v-model="form.suggestUserId" placeholder="请选择提案者" :disabled="disabled" :tabCode="tabCode"
            @callback="userCallback" />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="委员证号">
          <el-input v-model="form.cardNumber" disabled />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="界别" class="SubmitSuggestLeft">
          <el-input v-model="form.sectorType" disabled />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="联系电话">
          <el-input v-model="form.mobile" disabled />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="通讯地址" class="SubmitSuggestTitle">
          <el-input v-model="form.callAddress" disabled />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'team'" label="提案者" prop="delegationId"
          class="SubmitSuggestTitle">
          <el-select v-model="form.delegationId" :disabled="isDisabled" placeholder="请选择集体提案单位" clearable>
            <el-option v-for="item in delegationData" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'team' && (AreaId == '370500' || AreaId == '370523')"
          label="主要撰稿人" prop="writerUserId" class="SubmitSuggestTitle">
          <input-select-person v-model="form.writerUserId" placeholder="请选择主要撰稿人" :disabled="disabled"
            :tabCode="tabCode" @callback="userCallback" />
        </el-form-item>
        <el-form-item label="是否联名提案" prop="isJoinProposal" class="SubmitSuggestTitle">
          <el-radio-group v-model="form.isJoinProposal" @change="JoinChange">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="提案联名人" prop="joinUsers" v-if="form.isJoinProposal" class="SubmitSuggestTitle">
          <simple-select-person v-model="form.joinUsers" placeholder="请选择提案联名人"
            :filterUser="form.suggestUserId ? [form.suggestUserId] : []" :tabCode="['cppccMember']"
            @callback="unitedCallback"></simple-select-person>
          <template v-if="whetherUseIntelligentize && queryType !== 'review'">
            <intelligent-assistant v-model:elIsShow="elIsShow" v-model="visibleIsShow">
              <SuggestRecommendUser :params="userParams" @callback="userInitCallback" @select="userSelect">
              </SuggestRecommendUser>
            </intelligent-assistant>
          </template>
        </el-form-item>
        <el-form-item label="得分占比分配"
          v-if="scoreProportionData && scoreProportionData.length > 0 && isFlag && (AreaId == '370500' || AreaId == '370505' || AreaId == '370522')"
          class="SubmitSuggestTitle">
          <el-table ref="tableRef" row-key="id" border :data="scoreProportionData"
            style="margin: 5px 20px;border: 1px solid #ccc;">
            <el-table-column label="姓名" min-width="100" show-overflow-tooltip align="center">
              <template #default="scope">{{ scope.row.userName }}</template>
            </el-table-column>
            <el-table-column label="占比" min-width="100" show-overflow-tooltip align="center">
              <template #default="scope">
                <el-input v-model="scope.row.scoreProportion" style="width: 100px;" type="number"
                  @input="handleInput(scope.row)" @blur="handleBlur(scope.row)" />&nbsp;&nbsp;%
              </template>
            </el-table-column>
          </el-table>
          <p v-if="totalExceeds" style="color: red;margin:0 20px;">占比总和不能超过100%</p>
          <p v-if="totalInsufficient" style="color: red;margin:0 20px;">占比总和不足100%</p>
        </el-form-item>
        <el-form-item label="提案内容" prop="content" class="SubmitSuggestTitle SubmitSuggestButton">
          <!-- <div class="SubmitSuggestContentNumber">事实清楚，建议明确，不超过{{ suggestContentNumber }}字</div> -->
          <div></div>
          <el-button @click="handleSimilarity(false)"
            v-if="whetherUseIntelligentize && queryType === 'review' && reviewShow" type="primary">
            相似度查询
          </el-button>
        </el-form-item>
        <TinyMceEditor v-model="form.content" :setting="tinyMceSetting" :max_count="suggestContentNumber"
          @count="handleContentCount" @blur="handleContentBlur" v-if="queryType !== 'review'" textRectify
          :placeholder="`事实清楚，建议明确，不超过${suggestContentNumber}字`" />
        <div v-html="form.content" v-else class="content_box"></div>
        <div style="margin:10px 40px 50px;" v-if="queryType == 'review'">
          <div class="detailsPrints" title="打印提案" @click="handleSuggestPrint">打印提案</div>
          <div class="detailsExportInfos" title="导出提案word" @click="handleExportWord">导出提案word</div>
        </div>
        <!-- <el-form-item label="上传附件" class="SubmitSuggestFormUpload">
          <xyl-upload-file :fileData="fileData" @fileUpload="fileUpload" />
        </el-form-item> -->
        <el-form-item label="提案相关情况" class="SubmitSuggestFormItem"
          style="border-top: 1px solid var(--zy-el-color-primary);">
          <div class="SubmitSuggestFormInfo" v-if="suggestOpenTypeName">
            <div class="SubmitSuggestFormInfoText">{{ suggestOpenTypeName }}：</div>
            <el-radio-group v-model="form.suggestOpenType">
              <el-radio v-for="item in suggestOpenType" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="suggestSurveyTypeName">
            <div class="SubmitSuggestFormInfoText">{{ suggestSurveyTypeName }}：</div>
            <el-radio-group v-model="form.suggestSurveyType">
              <el-radio v-for="item in suggestSurveyType" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="isMakeMineJobName">
            <div class="SubmitSuggestFormInfoText">{{ isMakeMineJobName }}：</div>
            <el-radio-group v-model="form.isMakeMineJob">
              <el-radio v-for="item in isMakeMineJob" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="notHandleTimeTypeName">
            <div class="SubmitSuggestFormInfoText">{{ notHandleTimeTypeName }}：</div>
            <el-radio-group v-model="form.notHandleTimeType">
              <el-radio v-for="item in notHandleTimeType" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="isHopeEnhanceTalkName">
            <div class="SubmitSuggestFormInfoText">{{ isHopeEnhanceTalkName }}：</div>
            <el-radio-group v-model="form.isHopeEnhanceTalk">
              <el-radio v-for="item in isHopeEnhanceTalk" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="isNeedPaperAnswerName">
            <div class="SubmitSuggestFormInfoText">{{ isNeedPaperAnswerName }}：</div>
            <el-radio-group v-model="form.isNeedPaperAnswer">
              <el-radio v-for="item in isNeedPaperAnswer" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <!-- <el-form-item label="希望送交办单位" class="SubmitSuggestTitle">
          <suggest-simple-select-unit v-model="form.hopeHandleOfficeIds"></suggest-simple-select-unit>
        </el-form-item> -->
        <el-form-item class="SubmitSuggestContactPerson" label="提案联系人">
          <div class="SubmitSuggestContactPersonHead">
            <div class="SubmitSuggestContactPersonItem row2">提案联系人姓名</div>
            <div class="SubmitSuggestContactPersonItem row2">提案联系人电话</div>
            <div class="SubmitSuggestContactPersonItem row3">联系人通讯地址</div>
            <div class="SubmitSuggestContactPersonItem row1">操作</div>
          </div>
          <div class="SubmitSuggestContactPersonBody" v-for="item in contactPersonList" :key="item.id">
            <div class="SubmitSuggestContactPersonItem row2">
              <el-input placeholder="请输入联系人姓名" v-model="item.contactName" clearable></el-input>
            </div>
            <div class="SubmitSuggestContactPersonItem row2">
              <el-input placeholder="请输入联系人电话" v-model="item.contactPhone" clearable></el-input>
            </div>
            <div class="SubmitSuggestContactPersonItem row3">
              <el-input placeholder="请输入联系人通讯地址" v-model="item.contactAddress" clearable></el-input>
            </div>
            <div class="SubmitSuggestContactPersonItem row1">
              <el-link @click="newContactPerson" v-if="contactPersonList.length">
                <el-icon>
                  <CirclePlus />
                </el-icon>
              </el-link>
              <el-link v-if="contactPersonList.length > 1" @click="delContactPerson(item.id)">
                <el-icon>
                  <Remove />
                </el-icon>
              </el-link>
            </div>
          </div>
        </el-form-item>
        <div class="globalPaperFormButton" v-if="queryType !== 'review'">
          <el-button type="primary" @click="submitForm(formRef, 0)">提交提案</el-button>
          <el-button @click="submitForm(formRef, 1)"
            v-if="(!route.query.anewId && !route.query.id) || queryType === 'draft'">
            存为草稿
          </el-button>
          <el-button @click="resetForm" v-if="!route.query.id">重置</el-button>
          <el-button @click="resetForm" v-if="route.query.id">取消</el-button>
        </div>
      </el-form>
      <div v-if="queryType === 'review'" class="SuggestSegmentation"></div>
      <keep-alive>
        <SuggestReviewDetail :id="route.query.id" :name="route.query.reviewName" :title="form.title"
          :content="form.content" :SuggestBigType="form.SuggestBigType" :SuggestSmallType="form.SuggestSmallType"
          :hopeHandleOfficeIds="form.hopeHandleOfficeIds" v-if="queryType === 'review' && reviewShow"
          @editCallback="editCallback" @callback="resetForm"></SuggestReviewDetail>
      </keep-alive>
    </div>
    <suggestPrint v-if="elPrintWhetherShow" :params="printParams" @callback="callback"></suggestPrint>
    <xyl-popup-window v-model="show" name="相似度查询">
      <SimilarityQuery :type="isShow" :id="route.query.id" :title="form.title" :content="form.content"
        @callback="handleSimilarityCallback"></SimilarityQuery>
    </xyl-popup-window>
    <xyl-popup-window v-model="unitedProportionShow" name="得分占比分配">
      <ScoreProportion :data="scoreProportionData" @callback="scoreProportionCallback"></ScoreProportion>
    </xyl-popup-window>
  </el-scrollbar>
</template>
<script>
export default { name: 'SubmitSuggest' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { user, whetherUseIntelligentize } from 'common/js/system_var.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage, ElMessageBox } from 'element-plus'
import SimilarityQuery from '@/components/SimilarityQuery/SimilarityQuery.vue'
import SuggestRecommendUser from '@/components/SuggestRecommendUser/SuggestRecommendUser.vue'
import DynamicTitle from '@/components/global-dynamic-title/global-dynamic-title.vue'
import SuggestReviewDetail from '@/views/SuggestReview/component/SuggestReviewDetail.vue'
import suggestPrint from '@/components/suggestPrint/suggestPrint'
import { filterTableData } from '@/assets/js/suggestExportWord'
import { exportWordHtmlObj } from 'common/config/MicroGlobal'
import ScoreProportion from './ScoreProportion.vue'

const route = useRoute()
const store = useStore()
const loading = ref(false)
const loadingText = ref('')
const AreaId = ref(sessionStorage.getItem('AreaId'))
const formRef = ref()
const cachedScoreData = ref(null)
const isRequesting = ref(false)
const initialScoreData = ref(null)
const form = reactive({
  suggestSubmitWay: 'cppcc_member',
  title: '', // 提案标题
  suggestUserId: '',
  writerUserId: '',
  cardNumber: '',
  sectorType: '',
  mobile: '',
  callAddress: '',
  delegationId: '',
  isJoinProposal: 0,
  joinUsers: [],
  content: '',
  suggestOpenType: 'open_all',
  suggestSurveyType: '3',
  isMakeMineJob: '1',
  notHandleTimeType: '1',
  isHopeEnhanceTalk: '1',
  isNeedPaperAnswer: '1',
  hopeHandleOfficeIds: []
})
const rules = reactive({
  suggestSubmitWay: [{ required: true, message: '请选择提案提交类型', trigger: ['blur', 'change'] }],
  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }],
  suggestUserId: [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }],
  delegationId: [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }],
  isJoinProposal: [{ required: true, message: '请选择是否联名提案', trigger: ['blur', 'change'] }],
  joinUsers: [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]
})

const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const tinyMceSetting = {
  tp_layout_options: {
    style: {
      'text-align': 'justify',
      'text-indent': '2em',
      'line-height': '28pt',
      'font-size': '16pt',
      'font-family': '仿宋_GB2312'
    },
    tagsStyle: {
      span: {
        'text-align': 'justify',
        'text-indent': '2em',
        'line-height': '28pt',
        'font-size': '16pt',
        'font-family': '仿宋_GB2312'
      }
    }
  },
  contextmenu: false,
  paste_postprocess: (plugin, args) => {
    nextTick(() => {
      args.target.execCommand('mceTpLayout')
      nextTick(() => {
        args.target.selection.collapse()
      })
    })
  },
  import_word_callback: (editor) => {
    nextTick(() => {
      editor.execCommand('mceTpLayout')
      nextTick(() => {
        editor.selection.collapse()
      })
    })
  }
}
const suggestTitleNumber = ref(30)
const suggestContentNumber = ref(2000)
const suggestMinSimilar = ref(0)
const termYearId = ref('')
const contentCount = ref(0)
const fileData = ref([])
const delegationData = ref([])
const suggestOpenTypeName = ref('')
const suggestSurveyTypeName = ref('')
const notHandleTimeTypeName = ref('')
const isHopeEnhanceTalkName = ref('')
const isMakeMineJobName = ref('')
const isNeedPaperAnswerName = ref('')
const suggestOpenType = ref([])
const suggestSurveyType = ref([])
const notHandleTimeType = ref([])
const isHopeEnhanceTalk = ref([])
const isMakeMineJob = ref([])
const isNeedPaperAnswer = ref([])
const contactPersonList = ref([{ id: guid(), contactName: '', contactPhone: '', contactAddress: '' }])
const typeShow = ref(false)
const disabled = ref(false)
const isDisabled = ref(false)
const tabCode = ref(['cppccMember'])
const reviewShow = ref(false)
const queryType = ref('')

const show = ref(false)
const isShow = ref(false)
const elIsShow = ref(false)
const visibleIsShow = ref(false)
const userParams = ref({})
const ProposalYear = ref({})

const printParams = ref({})
const elPrintWhetherShow = ref(false)

const unitedProportionShow = ref(false)
const scoreProportionData = ref([])
const totalExceeds = ref(false)
const totalInsufficient = ref(false)
const isFlag = ref(false)
const hasLoadedScoreData = ref(false)
let timers = null
const debounce = (fn, delay) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

const handleInput = debounce((row) => {
  if (row.scoreProportion !== undefined && !/^[0-9]*$/.test(row.scoreProportion)) {
    row.scoreProportion = row.scoreProportion.replace(/[^0-9]/g, '')
  }
}, 300)

const handleBlur = debounce((row) => {
  let filledTotal = 0
  let unfilledCount = 0
  scoreProportionData.value.forEach(item => {
    if (item.scoreProportion && !isNaN(item.scoreProportion)) {
      filledTotal += parseInt(item.scoreProportion, 10)
    } else {
      unfilledCount++
    }
  })

  if (filledTotal > 100) {
    totalExceeds.value = true
    totalInsufficient.value = false
    row.scoreProportion = ''
    return
  }

  if (unfilledCount > 0) {
    const remaining = 100 - filledTotal
    if (remaining < 0) {
      totalExceeds.value = true
      totalInsufficient.value = false
      row.scoreProportion = ''
      return
    }
    if (unfilledCount === 1) {
      scoreProportionData.value.forEach(item => {
        if (!item.scoreProportion || isNaN(item.scoreProportion)) {
          item.scoreProportion = remaining.toString()
        }
      })
    }
  }

  if (filledTotal < 100 && unfilledCount === 0) {
    totalInsufficient.value = true
    totalExceeds.value = false
  } else {
    totalInsufficient.value = false
  }

  if (filledTotal === 100 && unfilledCount === 0) {
    totalExceeds.value = false
    totalInsufficient.value = false
  }
}, 300)

// const debouncedUpdateScoreProportion = debounce(() => {
//   updateScoreProportionList()
// }, 300)

onActivated(() => {
  qiankunMicro.setGlobalState({ AiChatCode: 'ai-intelligent-write-chat' })
  const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''
  if (openAiParams) {
    qiankunMicro.setGlobalState({
      AiChatConfig: {
        AiChatWindow: true,
        AiChatFile: openAiParams.fileData,
        AiChatParams: { tool: openAiParams.toolId, param: { isPage: '1' } },
        AiChatSendMessage: openAiParams.toolContent
      }
    })
    sessionStorage.setItem('openAiParams', JSON.stringify(''))
    timers = setTimeout(() => {
      qiankunMicro.setGlobalState({
        AiChatConfig: {
          AiChatWindow: true,
          AiChatFile: openAiParams.fileData,
          AiChatParams: {}
        }
      })
    }, 2000)
  }
  queryType.value = route.query.type
  if (route.query.clueListId) {
    proposalClueInfo()
  }
  globalReadConfig()
  termYearCurrent()
  dictionaryData()
  dictionaryNameData()
  getProposalYear()
  if (queryType.value === 'draft' || route.query.anewId) {
    typeShow.value = true
    disabled.value = true
  }
  if (route.query.id || route.query.anewId) {
    typeShow.value = true
    suggestionInfo()
  } else {
    tabCode.value = ['cppccMember']
    if (user.value.specialRoleKeys.includes('team_office_user')) {
      typeShow.value = true
    }
    if (user.value.specialRoleKeys.includes('cppcc_member')) {
      typeShow.value = true
      disabled.value = true
      form.suggestUserId = user.value.id
      cppccMemberInfo(user.value.id)
    } else {
      if (user.value.specialRoleKeys.includes('team_office_user')) {
        form.suggestSubmitWay = 'team'
      }
    }
    if (
      user.value.specialRoleKeys.includes('team_office_user') &&
      user.value.specialRoleKeys.includes('cppcc_member')
    ) {
      typeShow.value = false
    }
    if (user.value.specialRoleKeys.includes('admin')) {
      form.suggestSubmitWay = 'cppcc_member'
      typeShow.value = false
      disabled.value = false
      teamOfficeSelect({})
    } else {
      if (user.value.specialRoleKeys.includes('team_office_user')) {
        teamOfficeSelect({ isSelectMine: 1 })
      } else {
        teamOfficeSelect({})
      }
    }
    submitTypeChange()
  }
  if (!route.query.id) {
    cachedScoreData.value = null
    initialScoreData.value = null
    hasLoadedScoreData.value = false
  }
})
onDeactivated(() => {
  if (timers) {
    clearTimeout(timers)
    timers = null
  }
  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })
  qiankunMicro.setGlobalState({
    AiChatConfig: {
      AiChatWindow: false,
      AiChatFile: [],
      AiChatParams: {}
    }
  })
  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })
  // elAiChatClass.AiChatHistory()
})
onBeforeUnmount(() => {
  if (timers) {
    clearTimeout(timers)
    timers = null
  }
  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })
  qiankunMicro.setGlobalState({
    AiChatConfig: {
      AiChatWindow: false,
      AiChatFile: [],
      AiChatParams: {}
    }
  })
  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })
  // elAiChatClass.AiChatHistory()
})

const updateScoreProportionList = async () => {
  if (isRequesting.value) {
    return
  }

  const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId
  if (!proposerId) {
    isFlag.value = false
    return
  }

  if (route.query.id && !hasLoadedScoreData.value) {
    try {
      isRequesting.value = true
      const res = await api.globalJson('/proposalAllocationScore/info', {
        detailId: route.query.id
      })
      if (res.data && res.data.length > 0) {
        initialScoreData.value = res.data
        cachedScoreData.value = res.data
        scoreProportionData.value = res.data
        isFlag.value = true
        hasLoadedScoreData.value = true
        return
      }
    } catch (error) {
      if (error.code !== 'ERR_CANCELED') {
        console.error('获取得分占比数据失败:', error)
      }
    } finally {
      isRequesting.value = false
    }
  }

  if (scoreProportionData.value.length > 0 && hasLoadedScoreData.value) {
    return
  }

  let newList = []

  const existingProposer = scoreProportionData.value.find(person => person.id === proposerId)
  if (existingProposer) {
    newList.push({
      ...existingProposer,
      scoreProportion: existingProposer.scoreProportion || ''
    })
  } else {
    try {
      isRequesting.value = true
      const { data } = await api.cppccMemberInfo({ detailId: proposerId })
      newList.push({
        id: proposerId,
        userId: proposerId,
        userName: data.userName,
        scoreProportion: ''
      })
    } catch (error) {
      console.error('获取提案者信息失败:', error)
    } finally {
      isRequesting.value = false
    }
  }

  if (form.joinUsers && form.joinUsers.length > 0) {
    const existingJoinUsers = scoreProportionData.value.filter(person =>
      form.joinUsers.includes(person.id)
    )

    const newJoinUserIds = form.joinUsers.filter(id =>
      !existingJoinUsers.some(user => user.id === id)
    )

    if (newJoinUserIds.length > 0) {
      try {
        isRequesting.value = true
        const newJoinUsersInfo = await Promise.all(
          newJoinUserIds.map(async (userId) => {
            const { data } = await api.cppccMemberInfo({ detailId: userId })
            return {
              id: userId,
              userId: userId,
              userName: data.userName,
              scoreProportion: ''
            }
          })
        )
        newList = [...newList, ...existingJoinUsers, ...newJoinUsersInfo]
      } catch (error) {
        console.error('获取联名人信息失败:', error)
      } finally {
        isRequesting.value = false
      }
    } else {
      newList = [...newList, ...existingJoinUsers]
    }
  }

  const updatedList = newList.map(newItem => {
    const initialItem = initialScoreData.value?.find(item => item.id === newItem.id)
    const existingItem = scoreProportionData.value.find(item => item.id === newItem.id)
    return {
      ...newItem,
      scoreProportion: initialItem?.scoreProportion || existingItem?.scoreProportion || newItem.scoreProportion
    }
  })

  scoreProportionData.value = updatedList
  isFlag.value = true
  hasLoadedScoreData.value = true
}

const handleExportWord = () => {
  if (!form.content) return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行导出！' })
  suggestionWord({ ids: [route.query.id] })
}
const handleSuggestPrint = async () => {
  if (!form.content) return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行打印！' })
  printParams.value = { ids: [route.query.id] }
  elPrintWhetherShow.value = true
}
const callback = (type) => {
  elPrintWhetherShow.value = false
  if (type) {
    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
  }
}
const suggestionWord = async (params) => {
  const { data } = await api.suggestionWord(params)
  if (data.length) {
    var wordData = {}
    for (let index = 0; index < data.length; index++) {
      wordData = filterTableData(data[index])
    }
    exportWordHtmlObj({ code: 'proposalDetails', name: wordData.docName, key: 'content', data: wordData })
  }
}
const handleSimilarity = (isType) => {
  if (!form.content) return ElMessage({ type: 'warning', message: '请输入提案内容进行相似度查询！' })
  sessionStorage.setItem('TextQueryToolTitle', form.title)
  sessionStorage.setItem('TextQueryToolContent', form.content)
  isShow.value = isType
  show.value = true
}
const handleSimilarityCallback = (type) => {
  if (type) globalJson(0)
  show.value = false
}
const handleContentBlur = () => {
  userParams.value = { authorId: form.suggestUserId, content: form.content }
}
const userInitCallback = (isElIsShow, isVisibleIsShow) => {
  elIsShow.value = isElIsShow
  visibleIsShow.value = isVisibleIsShow
}
const userSelect = (item) => {
  if (!form.joinUsers.includes(item.id)) {
    form.joinUsers = [...form.joinUsers, item.id]
  }
}
const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({
    codes: ['suggestTitleNumber', 'suggestContentNumber', 'suggestMinSimilar']
  })
  if (data.suggestTitleNumber) {
    suggestTitleNumber.value = Number(data.suggestTitleNumber)
  }
  if (data.suggestContentNumber) {
    suggestContentNumber.value = Number(data.suggestContentNumber)
  }
  if (data.suggestMinSimilar) {
    suggestMinSimilar.value = Number(data.suggestMinSimilar)
  }
}
const getProposalYear = async () => {
  const { data } = await api.getProposalYear()
  ProposalYear.value = data.length ? data[0] : {}
}
const termYearCurrent = async () => {
  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })
  termYearId.value = data.id
}
const dictionaryData = async () => {
  const { data } = await api.dictionaryData({
    dictCodes: [
      'suggest_open_type',
      'suggest_survey_type',
      'not_handle_time_type',
      'is_hope_enhance_talk',
      'is_make_mine_job',
      'is_need_paper_answer'
    ]
  })
  suggestOpenType.value = data.suggest_open_type
  suggestSurveyType.value = data.suggest_survey_type
  notHandleTimeType.value = data.not_handle_time_type
  isHopeEnhanceTalk.value = data.is_hope_enhance_talk
  isMakeMineJob.value = data.is_make_mine_job
  isNeedPaperAnswer.value = data.is_need_paper_answer
}
const dictionaryNameData = async () => {
  const { data } = await api.dictionaryNameData({
    dictCodes: [
      'suggest_open_type',
      'suggest_survey_type',
      'not_handle_time_type',
      'is_hope_enhance_talk',
      'is_make_mine_job',
      'is_need_paper_answer'
    ]
  })
  suggestOpenTypeName.value = data.suggest_open_type
  suggestSurveyTypeName.value = data.suggest_survey_type
  notHandleTimeTypeName.value = data.not_handle_time_type
  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk
  isMakeMineJobName.value = data.is_make_mine_job
  isNeedPaperAnswerName.value = data.is_need_paper_answer
}
const proposalClueInfo = async () => {
  const { data } = await api.proposalClueInfo({ detailId: route.query.clueListId })
  form.title = data.title
  form.content = data.content
}
const isLock = ref(false)
const lockVo = ref({})
const suggestionInfo = async () => {
  try {
    const res = await api.suggestionInfo({
      detailId: route.query.id || route.query.anewId,
      isOpenWithLock: queryType.value === 'review' ? 1 : null
    })
    var { data } = res
    reviewShow.value = true
    lockVo.value = data.lockVo
    isLock.value = route.query.type == 'review' && data.lockVo.isLock == 1 && data.lockVo.lockUserId != user.value.id
    form.suggestSubmitWay = data.suggestSubmitWay
    if (form.suggestSubmitWay === 'cppcc_member') {
      tabCode.value = ['cppccMember']
      form.suggestUserId = data.suggestUserId
      if (data.suggestUserId) {
        cppccMemberInfo(data.suggestUserId)
      }
    }
    if (form.suggestSubmitWay === 'team') {
      isDisabled.value = true
      form.delegationId = data.delegationId
      delegationData.value = data.delegationId ? [{ id: data.delegationId, name: data.delegationName }] : []
    }
    submitTypeChange()
    form.title = data.title
    form.SuggestBigType = data.bigThemeId
    form.SuggestSmallType = data.smallThemeId
    // SuggestBigTypeChange()
    form.termYearId = data.termYearId
    form.content = data.content.replace(/<p>/g, '<p style="font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;">');
    handleContentBlur()
    form.isJoinProposal = data.isJoinProposal
    JoinChange()
    form.suggestOpenType = data.suggestOpenType?.value
    form.suggestSurveyType = data.suggestSurveyType?.value
    form.isMakeMineJob = data.isMakeMineJob?.value
    form.notHandleTimeType = data.notHandleTimeType?.value
    form.isHopeEnhanceTalk = data.isHopeEnhanceTalk?.value
    form.isNeedPaperAnswer = data.isNeedPaperAnswer?.value
    form.writerUserId = data.jordan
    fileData.value = data.attachments || []
    form.hopeHandleOfficeIds = data.hopeHandleOfficeIds?.map((v) => v.officeId) || []
    form.joinUsers = data.joinUsers?.map((v) => v.userId) || []
    if (data.contacters?.length) {
      contactPersonList.value = data.contacters.map((v) => ({
        id: v.id,
        contactName: v.contacterName,
        contactPhone: v.contacterMobile,
        contactAddress: v.contacterAddress
      }))
    }
    userParams.value = { authorId: form.suggestUserId, content: form.content }

    if (form.isJoinProposal && (form.suggestUserId || form.writerUserId)) {
      isFlag.value = true
      hasLoadedScoreData.value = false
      await updateScoreProportionList()
    }
  } catch (err) {
    if (err.code === 500) {
      if (route.query.id && queryType.value === 'review') {
        reviewShow.value = false
        qiankunMicro.setGlobalState({
          closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }
        })
      }
    }
  }
}
const submitTypeChange = () => {
  if (form.suggestSubmitWay === 'cppcc_member') {
    rules.suggestUserId = [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }]
    rules.delegationId = [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]
  } else if (form.suggestSubmitWay === 'team') {
    rules.suggestUserId = [{ required: false, message: '请选择提案者', trigger: ['blur', 'change'] }]
    rules.delegationId = [{ required: true, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]
  }
  form.joinUsers = []
  scoreProportionData.value = []
  isFlag.value = false
}
const JoinChange = () => {
  if (form.isJoinProposal) {
    rules.joinUsers = [{ type: 'array', required: true, message: '请选择提案联名人', trigger: ['blur', 'change'] }]
  } else {
    rules.joinUsers = [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]
    scoreProportionData.value = []
    isFlag.value = false
  }
}
const handleContentCount = (count) => {
  contentCount.value = count
}
watch(() => form.suggestUserId, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    const newData = scoreProportionData.value.filter(
      person => String(person.id) !== String(oldValue)
    )
    scoreProportionData.value = newData
  }
}
)
watch(() => form.writerUserId, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    const newData = scoreProportionData.value.filter(
      person => String(person.id) !== String(oldValue)
    )
    scoreProportionData.value = newData
  }
}
)
const userCallback = async (data) => {
  if (data) {
    cppccMemberInfo(data.id)
    form.joinUsers = form.joinUsers.filter((v) => v !== data.id)
    if (form.isJoinProposal && form.joinUsers.length > 0) {
      await updateScoreProportionList()
    }
  } else {
    form.cardNumber = ''
    form.sectorType = ''
    form.mobile = ''
    form.callAddress = ''
    scoreProportionData.value = []
    isFlag.value = false
  }
  userParams.value = { authorId: form.suggestUserId, content: form.content }
}
const cppccMemberInfo = async (userId) => {
  const { data } = await api.cppccMemberInfo({ detailId: userId })
  form.cardNumber = data.cardNumberCppcc
  form.sectorType = data.sectorType?.label
  form.mobile = data.mobile
  form.callAddress = data.callAddress
}
const teamOfficeSelect = async (params) => {
  const { data } = await api.teamOfficeSelect(params)
  if (data.length) {
    if (user.value.specialRoleKeys.includes('team_office_user')) {
      isDisabled.value = true
      form.delegationId = data[0].id
    }
  }
  delegationData.value = data
}
const newContactPerson = () => {
  contactPersonList.value.push({ id: guid(), contactName: '', contactPhone: '', contactAddress: '' })
}
const delContactPerson = (id) => {
  contactPersonList.value = contactPersonList.value.filter((v) => v.id !== id)
}
const submitForm = async (formEl, type, cb) => {
  if (!formEl) return
  if (contentCount.value > suggestContentNumber.value) {
    ElMessage({ type: 'warning', message: `当前输入的提案内容超过了${suggestContentNumber.value}字，不允许提交！` })
    return
  }
  if (contentCount.value < 200 && !queryType.value) {
    ElMessage({ type: 'warning', message: '提案字数不得少于200字！' })
    return
  }
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (whetherUseIntelligentize.value && !cb) {
        if (type) {
          globalJson(type, cb)
        } else {
          ElMessageBox.confirm('系统将为您进行相似度查询，是否同意执行操作？', '提示', {
            closeOnClickModal: false,
            confirmButtonText: '同意',
            cancelButtonText: '跳过'
          })
            .then(() => {
              handleSimilarity(true)
            })
            .catch(() => {
              globalJson(type, cb)
            })
        }
      } else {
        globalJson(type, cb)
      }
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const editCallback = (cb) => {
  submitForm(formRef.value, 0, cb)
}
const globalJson = async (type, cb) => {
  const resultScoreProportion = scoreProportionData.value.map(item => ({
    userId: item.userId || item.id,
    scoreProportion: item.scoreProportion
  }))
  try {
    const { code } = await api.globalJson(route.query.id ? '/proposal/edit' : '/proposal/add', {
      form: {
        id: route.query.id,
        suggestSubmitWay: form.suggestSubmitWay,
        title: form.title, // 提案标题
        suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,
        delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,
        content: form.content,
        isJoinProposal: form.isJoinProposal,
        suggestOpenType: form.suggestOpenType,
        suggestSurveyType: form.suggestSurveyType,
        isMakeMineJob: form.isMakeMineJob,
        notHandleTimeType: form.notHandleTimeType,
        isHopeEnhanceTalk: form.isHopeEnhanceTalk,
        isNeedPaperAnswer: form.isNeedPaperAnswer,
        termYearId: route.query.id ? form.termYearId : ProposalYear.value.value,
        attachmentIds: fileData.value.map((v) => v.id)
      },
      objectParam: {
        teamMainAuthor: form.writerUserId ? form.writerUserId : ''
      },
      isSaveDraft: type,
      arrayParam: resultScoreProportion,
      joinUsers: form.isJoinProposal ? form.joinUsers : [],
      hopeHandleOfficeIds: form.hopeHandleOfficeIds,
      contacters: contactPersonList.value
        .filter(
          (v) =>
            v.contactName.replace(/(^\s*)|(\s*$)/g, '') ||
            v.contactPhone.replace(/(^\s*)|(\s*$)/g, '') ||
            v.contactAddress.replace(/(^\s*)|(\s*$)/g, '')
        )
        .map((v) => ({
          contacterName: v.contactName,
          contacterMobile: v.contactPhone,
          contacterAddress: v.contactAddress
        }))
    })
    if (code === 200) {
      cachedScoreData.value = null
      initialScoreData.value = null
      hasLoadedScoreData.value = false
      if (route.query.clueListId) {
        proposalClueUse()
      } else {
        if (cb) {
          return cb()
        }
        ElMessage({ type: 'success', message: route.query.id ? '编辑成功' : '提交成功' })
        if (route.query.id || route.query.anewId || route.query.clueListId) {
          qiankunMicro.setGlobalState({
            closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }
          })
        } else {
          store.commit('setRefreshRoute', 'SubmitSuggest')
          setTimeout(() => {
            store.commit('setRefreshRoute', '')
          }, 222)
        }
      }
    }
  } catch (err) {
    loading.value = false
  }
}
const proposalClueUse = async () => {
  const { code } = await api.proposalClueUse({ detailId: route.query.clueListId })
  if (code === 200) {
    ElMessage({ type: 'success', message: '提交成功' })
    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
  }
}
const resetForm = () => {
  if (route.query.id || route.query.anewId || route.query.clueListId) {
    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
  } else {
    store.commit('setRefreshRoute', 'SubmitSuggest')
    setTimeout(() => {
      store.commit('setRefreshRoute', '')
    }, 222)
  }
}

watch(
  () => form.joinUsers,
  async (newVal, oldVal) => {
    if (form.isJoinProposal) {
      const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId
      if (proposerId) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          await updateScoreProportionList()
        }
      }
    }
  },
  { deep: true }
)

watch(
  [() => form.suggestUserId, () => form.writerUserId],
  async (newValues, oldValues) => {
    if (newValues[0] !== oldValues[0] || newValues[1] !== oldValues[1]) {
      if (form.isJoinProposal && form.joinUsers.length > 0) {
        await updateScoreProportionList()
      }
    }
  }
)

const unitedCallback = async (_data) => {
  if (_data && _data.length > 0) {
    await updateScoreProportionList()
  } else {
    const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId
    if (proposerId) {
      const proposer = scoreProportionData.value.find(person => person.id === proposerId)
      scoreProportionData.value = proposer ? [proposer] : []
      isFlag.value = true
    } else {
      scoreProportionData.value = []
      isFlag.value = false
    }
  }
}
</script>
<style lang="scss">
.SubmitSuggest {
  width: 100%;
  height: 100%;

  .SubmitSuggestBody {
    width: 990px;
    margin: 20px auto;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
    position: relative;

    .detailsPrints,
    .detailsExportInfos {
      color: #3657c0;
      font-size: 18px;
      line-height: var(--zy-line-height);
      padding-left: 35px;
      margin-bottom: 20px;
      position: relative;
      cursor: pointer;
    }

    .detailsPrints {
      background: url("../../../assets/img/suggest_details_print.png") no-repeat;
      background-size: 30px 30px;
      background-position: left center;
    }

    .detailsExportInfos {
      background: url("../../../assets/img/suggest_details_export_info.png") no-repeat;
      background-size: 30px 30px;
      background-position: left center;
    }

    .detailsPrint,
    .detailsExportInfo {
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
      padding-left: 30px;
      margin-bottom: 20px;
      position: relative;
      cursor: pointer;
    }

    .detailsPrint {
      background: url("../../../assets/img/suggest_details_print.png") no-repeat;
      background-size: 20px 20px;
      background-position: left center;
    }

    .detailsExportInfo {
      background: url("../../../assets/img/suggest_details_export_info.png") no-repeat;
      background-size: 20px 20px;
      background-position: left center;
    }

    .SubmitSuggestNameBody {
      padding: var(--zy-distance-one);
      padding-bottom: 0;

      .global-dynamic-title {
        border-bottom: 3px solid var(--zy-el-color-primary);
      }
    }

    .globalPaperForm {
      width: 100%;
      padding: var(--zy-distance-one);
      padding-top: 0;

      .zy-el-form-item {
        width: 50%;
        margin: 0;
        border-bottom: 1px solid var(--zy-el-color-primary);

        .zy-el-form-item__label {
          width: 138px;
          justify-content: center;
        }

        .zy-el-form-item__content {
          border-left: 1px solid var(--zy-el-color-primary);
          border-right: 1px solid transparent;

          &>.simple-select-person {
            box-shadow: 0 0 0 0 !important;
          }

          &>.zy-el-input,
          .zy-el-input-number {
            width: 100%;

            .zy-el-input__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          &>.zy-el-select,
          .zy-el-select-v2 {
            .zy-el-select__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          &>.zy-el-radio-group {
            padding-left: 15px;
          }

          &>.zy-el-date-editor {
            width: 100%;

            &>.zy-el-input__wrapper {
              width: 100%;
              box-shadow: 0 0 0 0 !important;
            }
          }
        }
      }

      .SubmitSuggestLeft {
        .zy-el-form-item__content {
          border-right-color: var(--zy-el-color-primary);
        }
      }

      .SubmitSuggestTitle {
        width: 100%;

        .zy-el-form-item__content {
          border-right-color: transparent;
        }
      }

      .SubmitSuggestButton {
        .zy-el-form-item__content {
          flex-wrap: nowrap;
          justify-content: space-between;

          .SubmitSuggestContentNumber {
            padding: 0 10px;
            color: var(--zy-el-color-error);
            font-size: var(--zy-text-font-size);
            line-height: var(--zy-line-height);
          }

          .SubmitSuggestUpload {
            margin-left: 12px;
            margin-right: 12px;
          }

          .zy-el-button {
            --zy-el-button-size: var(--zy-height-routine);
          }
        }
      }

      .TinyMceEditor {
        border-bottom: 1px solid var(--zy-el-color-primary);
      }

      .content_box p span {
        line-height: 1.5 !important;
        font-size: 21px !important;
      }

      .SubmitSuggestFormUpload {
        width: 100%;

        .zy-el-form-item__content {
          padding: 15px;
          border-right-color: transparent;

          .SubmitSuggestFormInfo {
            width: 100%;
            display: flex;
          }
        }
      }

      .SubmitSuggestFormItem {
        width: 100%;

        .zy-el-form-item__content {
          padding: 0 15px;
          border-right-color: transparent;

          .SubmitSuggestFormInfo {
            width: 100%;
            display: flex;
          }
        }
      }

      .SubmitSuggestContactPerson {
        width: 100%;

        .SubmitSuggestContactPersonHead,
        .SubmitSuggestContactPersonBody {
          width: 100%;
          display: flex;
        }

        .SubmitSuggestContactPersonBody {
          border-top: 1px solid var(--zy-el-color-primary);
        }

        .row1 {
          flex: 1;
        }

        .row2 {
          flex: 2;
        }

        .row3 {
          flex: 3;
        }

        .SubmitSuggestContactPersonItem {
          height: 40px;
          line-height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;

          &>.zy-el-input {
            width: 100%;

            .zy-el-input__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          .zy-el-link {
            font-size: 18px;
            line-height: 24px;
          }

          .zy-el-link+.zy-el-link {
            margin-left: 12px;
          }
        }

        .SubmitSuggestContactPersonItem+.SubmitSuggestContactPersonItem {
          border-left: 1px solid var(--zy-el-color-primary);
        }
      }

      .globalPaperFormButton {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 22px;

        .zy-el-button+.zy-el-button {
          margin-left: var(--zy-distance-two);
        }
      }
    }

    .SuggestSegmentation {
      width: 100%;
      height: 10px;
      background-color: var(--zy-el-color-info-light-9);
    }
  }
}
</style>
