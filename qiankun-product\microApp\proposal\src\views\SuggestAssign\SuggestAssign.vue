<template>
  <div class="SuggestAssign">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-popover placement="bottom" title="您可以查找：" trigger="hover" :width="250">
          <div class="tips-UL">
            <div>提案名称</div>
            <div>提案编号</div>
            <div>提案人<strong>(名称前加 n 或 N)</strong></div>
            <div>全部办理单位<strong>(名称前加 d 或 D)</strong></div>
            <div>主办单位<strong>(名称前加 m 或 M)</strong></div>
            <div>协办单位<strong>(名称前加 j 或 J)</strong></div>
          </div>
          <template #reference>
            <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
          </template>
        </el-popover>
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #mainHandleOffices="scope">
            <template v-if="scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0">
              {{scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
            <template v-else>
              {{scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
          </template>
          <template #assistHandleOffices="scope">
            <template v-if="scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0">
              {{scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
            <template v-else>
              {{scope.row.assistHandleVoList?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
          </template>
          <!-- <template #publishHandleOffices="scope">
            {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}
          </template> -->
        </xyl-global-table>
        <xyl-global-table-button :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel :name="route.query.moduleName" :exportId="exportId" :params="exportParams"
        module="proposalExportExcel" :tableId="route.query.tableId" @excelCallback="callback"
        :handleExcelData="handleExcelData"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="show" name="批量交办">
      <SuggestBatchAssign :id="id" @callback="callback"></SuggestBatchAssign>
    </xyl-popup-window>
    <xyl-popup-window v-model="unitShow" name="交办承办单位" :beforeClose="callback">
      <SuggestBatchAssignUnit :id="id" @callback="callback"></SuggestBatchAssignUnit>
    </xyl-popup-window>
    <xyl-popup-window v-model="sendBackShow" name="退回政协交办">
      <SuggestBatchSendBack :id="id" @callback="callback"></SuggestBatchSendBack>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestAssign' }
</script>
<script setup>
import { ref, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import SuggestBatchAssign from './component/SuggestBatchAssign.vue'
import SuggestBatchAssignUnit from './component/SuggestBatchAssignUnit.vue'
import SuggestBatchSendBack from './component/SuggestBatchSendBack.vue'
import { ElMessage } from 'element-plus'
const route = useRoute()
const buttonList = [
  // { id: 'next', name: '批量交办', type: 'primary', has: 'next' },
  { id: 'sendBack', name: '退回政协交办', type: 'primary', has: 'send_back' },
  { id: 'nextUnit', name: '批量交办', type: 'primary', has: 'next_unit' },
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
]
const id = ref([])
const show = ref(false)
const unitShow = ref(false)
const sendBackShow = ref(false)
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: route.query.tableId, tableApi: 'suggestionList' })

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    handleQuery()
  }
})
const handleExcelData = (_item) => {
  _item.forEach(v => {
    if (!v.mainHandleOffices) {
      v.mainHandleOffices = v.publishHandleOffices
    }
  })
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'next':
      if (tableDataArray.value.length) {
        id.value = tableDataArray.value.map((v) => v.id)
        show.value = true
      } else {
        ElMessage({ type: 'warning', message: '请至少选择一条数据' })
      }
      break
    case 'sendBack':
      if (tableDataArray.value.length) {
        id.value = tableDataArray.value.map((v) => v.id)
        sendBackShow.value = true
      } else {
        ElMessage({ type: 'warning', message: '请至少选择一条数据' })
      }
      break
    case 'nextUnit':
      if (tableDataArray.value.length) {
        id.value = tableDataArray.value.map((v) => v.id)
        unitShow.value = true
      } else {
        ElMessage({ type: 'warning', message: '请至少选择一条数据' })
      }
      break
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: {
      name: '提案详情',
      path: '/proposal/SuggestDetail',
      query: { id: item.id, moduleName: route.query.moduleName, type: 'assign' }
    }
  })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
  show.value = false
  unitShow.value = false
  sendBackShow.value = false
}
</script>
<style lang="scss">
.SuggestAssign {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
