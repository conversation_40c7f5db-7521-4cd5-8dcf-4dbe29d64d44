{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestClueSubmit\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_input_select_person = _resolveComponent(\"input-select-person\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"线索标题\",\n        prop: \"title\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.title,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.title = $event;\n            }),\n            placeholder: \"请输入线索标题\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.form.terminalName == '第三方' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"提供者\",\n        prop: \"furnishName\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.furnishName,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.furnishName = $event;\n            }),\n            \"show-word-limit\": \"\",\n            placeholder: \"请输入提供者\",\n            disabled: \"\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"提供者\",\n        prop: \"furnish\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_input_select_person, {\n            modelValue: $setup.form.furnish,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.furnish = $event;\n            }),\n            placeholder: \"请选择提供者\",\n            tabCode: $setup.tabCode,\n            onCallback: $setup.userCallback\n          }, null, 8 /* PROPS */, [\"modelValue\", \"tabCode\"])];\n        }),\n        _: 1 /* STABLE */\n      })), _createVNode(_component_el_form_item, {\n        label: \"线索类别\",\n        prop: \"proposalClueType\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.proposalClueType,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.proposalClueType = $event;\n            }),\n            placeholder: \"请选择线索类别\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.proposalClueType, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.id,\n                  label: item.label,\n                  value: item.id\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"线索内容\",\n        prop: \"content\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.content = $event;\n            }),\n            \"show-word-limit\": \"\",\n            type: \"textarea\",\n            placeholder: \"请输入线索内容\",\n            rows: \"6\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[5] || (_cache[5] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[6] || (_cache[6] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[7] || (_cache[7] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "title", "_cache", "$event", "placeholder", "clearable", "_", "terminalName", "_createBlock", "key", "furnishName", "disabled", "_component_input_select_person", "furnish", "tabCode", "onCallback", "userCallback", "_component_el_select", "proposalClueType", "_Fragment", "_renderList", "item", "_component_el_option", "id", "value", "content", "type", "rows", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueSubmit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestClueSubmit\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"线索标题\"\r\n                    prop=\"title\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.title\"\r\n                  placeholder=\"请输入线索标题\"\r\n                  clearable />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"提供者\"\r\n                    prop=\"furnishName\"\r\n                    v-if=\"form.terminalName == '第三方'\">\r\n        <el-input v-model=\"form.furnishName\"\r\n                  show-word-limit\r\n                  placeholder=\"请输入提供者\"\r\n                  disabled\r\n                  clearable />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"提供者\"\r\n                    prop=\"furnish\"\r\n                    v-else>\r\n        <input-select-person v-model=\"form.furnish\"\r\n                             placeholder=\"请选择提供者\"\r\n                             :tabCode=\"tabCode\"\r\n                             @callback=\"userCallback\" />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"线索类别\"\r\n                    prop=\"proposalClueType\">\r\n        <el-select v-model=\"form.proposalClueType\"\r\n                   placeholder=\"请选择线索类别\">\r\n          <el-option v-for=\"item in proposalClueType\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.label\"\r\n                     :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"线索内容\"\r\n                    prop=\"content\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.content\"\r\n                  show-word-limit\r\n                  type=\"textarea\"\r\n                  placeholder=\"请输入线索内容\"\r\n                  rows=\"6\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestClueSubmit' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, computed, onMounted } from 'vue'\r\nimport { selectUser } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst tabCode = computed(() => selectUser.value.suggestClue)\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  id: '',\r\n  title: '',\r\n  proposalClueType: '',\r\n  content: '',\r\n  furnish: '',\r\n  furnishName: '',\r\n  furnish_mobile: '',\r\n  terminalName: ''\r\n})\r\nconst rules = reactive({\r\n  title: [{ required: true, message: '请输入线索标题', trigger: ['blur', 'change'] }],\r\n  furnish: [{ required: true, message: '请输入选择提供者', trigger: ['blur', 'change'] }],\r\n  proposalClueType: [{ required: true, message: '请选择线索类别', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请输入线索内容', trigger: ['blur', 'change'] }]\r\n})\r\nconst proposalClueType = ref([])\r\n\r\nconsole.log(form.terminalName, 'terminalName')\r\n\r\nonMounted(() => {\r\n  dictionaryData()\r\n  if (props.id) { proposalClueInfo() }\r\n})\r\n\r\nconst dictionaryData = async () => {\r\n  const { data } = await api.dictionaryData({ dictCodes: ['proposal_clue_type'] })\r\n  proposalClueType.value = data.proposal_clue_type\r\n}\r\nconst proposalClueInfo = async () => {\r\n  const res = await api.proposalClueInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.id = props.id\r\n  form.title = data.title\r\n  form.content = data.content\r\n  form.furnish = data.furnish\r\n  form.furnishName = data.furnishName\r\n  form.furnish_mobile = data.furnish_mobile\r\n  form.proposalClueType = data.proposalClueType.value\r\n  form.terminalName = data.terminalName\r\n}\r\n\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst userCallback = (data) => {\r\n  if (data) {\r\n    form.furnish = data.id\r\n    form.furnishName = data.userName\r\n    form.furnish_mobile = data.mobile\r\n  }\r\n}\r\n\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/proposalClue/edit' : '/proposalClue/add', {\r\n    form: {\r\n      id: props.id || null,\r\n      title: form.title,\r\n      content: form.content,\r\n      proposalClueType: form.proposalClueType,\r\n      furnish: form.furnish,\r\n      furnishName: form.furnishName,\r\n      furnishMobile: form.furnish_mobile,\r\n      terminalName: form.terminalName || \"PC\",\r\n    },\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestClueSubmit {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAsDrBA,KAAK,EAAC;AAAkB;;;;;;;;;uBAtDjCC,mBAAA,CA4DM,OA5DNC,UA4DM,GA3DJC,YAAA,CA0DUC,kBAAA;IA1DDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAMe,CANfT,YAAA,CAMeU,uBAAA;QANDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC,OAAO;QACZf,KAAK,EAAC;;QAV1BW,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YAb9BC,UAAA,EAW2BV,MAAA,CAAAC,IAAI,CAACU,KAAK;YAXrC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAW2Bb,MAAA,CAAAC,IAAI,CAACU,KAAK,GAAAE,MAAA;YAAA;YACnBC,WAAW,EAAC,SAAS;YACrBC,SAAS,EAAT;;;QAblBC,CAAA;UAkB0BhB,MAAA,CAAAC,IAAI,CAACgB,YAAY,a,cAFrCC,YAAA,CAQeZ,uBAAA;QAxBrBa,GAAA;QAgBoBZ,KAAK,EAAC,KAAK;QACXC,IAAI,EAAC;;QAjBzBJ,OAAA,EAAAC,QAAA,CAmBQ;UAAA,OAIsB,CAJtBT,YAAA,CAIsBa,mBAAA;YAvB9BC,UAAA,EAmB2BV,MAAA,CAAAC,IAAI,CAACmB,WAAW;YAnB3C,uBAAAR,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAmB2Bb,MAAA,CAAAC,IAAI,CAACmB,WAAW,GAAAP,MAAA;YAAA;YACzB,iBAAe,EAAf,EAAe;YACfC,WAAW,EAAC,QAAQ;YACpBO,QAAQ,EAAR,EAAQ;YACRN,SAAS,EAAT;;;QAvBlBC,CAAA;2BA0BME,YAAA,CAOeZ,uBAAA;QAjCrBa,GAAA;QA0BoBZ,KAAK,EAAC,KAAK;QACXC,IAAI,EAAC;;QA3BzBJ,OAAA,EAAAC,QAAA,CA6BQ;UAAA,OAGgD,CAHhDT,YAAA,CAGgD0B,8BAAA;YAhCxDZ,UAAA,EA6BsCV,MAAA,CAAAC,IAAI,CAACsB,OAAO;YA7BlD,uBAAAX,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA6BsCb,MAAA,CAAAC,IAAI,CAACsB,OAAO,GAAAV,MAAA;YAAA;YACrBC,WAAW,EAAC,QAAQ;YACnBU,OAAO,EAAExB,MAAA,CAAAwB,OAAO;YAChBC,UAAQ,EAAEzB,MAAA,CAAA0B;;;QAhCxCV,CAAA;WAmCMpB,YAAA,CASeU,uBAAA;QATDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QApCzBJ,OAAA,EAAAC,QAAA,CAqCQ;UAAA,OAMY,CANZT,YAAA,CAMY+B,oBAAA;YA3CpBjB,UAAA,EAqC4BV,MAAA,CAAAC,IAAI,CAAC2B,gBAAgB;YArCjD,uBAAAhB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAqC4Bb,MAAA,CAAAC,IAAI,CAAC2B,gBAAgB,GAAAf,MAAA;YAAA;YAC9BC,WAAW,EAAC;;YAtC/BV,OAAA,EAAAC,QAAA,CAuCqB;cAAA,OAAgC,E,kBAA3CX,mBAAA,CAG8BmC,SAAA,QA1CxCC,WAAA,CAuCoC9B,MAAA,CAAA4B,gBAAgB,EAvCpD,UAuC4BG,IAAI;qCAAtBb,YAAA,CAG8Bc,oBAAA;kBAFlBb,GAAG,EAAEY,IAAI,CAACE,EAAE;kBACZ1B,KAAK,EAAEwB,IAAI,CAACxB,KAAK;kBACjB2B,KAAK,EAAEH,IAAI,CAACE;;;;YA1ClCjB,CAAA;;;QAAAA,CAAA;UA6CMpB,YAAA,CASeU,uBAAA;QATDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC,SAAS;QACdf,KAAK,EAAC;;QA/C1BW,OAAA,EAAAC,QAAA,CAgDQ;UAAA,OAKsB,CALtBT,YAAA,CAKsBa,mBAAA;YArD9BC,UAAA,EAgD2BV,MAAA,CAAAC,IAAI,CAACkC,OAAO;YAhDvC,uBAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAgD2Bb,MAAA,CAAAC,IAAI,CAACkC,OAAO,GAAAtB,MAAA;YAAA;YACrB,iBAAe,EAAf,EAAe;YACfuB,IAAI,EAAC,UAAU;YACftB,WAAW,EAAC,SAAS;YACrBuB,IAAI,EAAC,GAAG;YACRtB,SAAS,EAAT;;;QArDlBC,CAAA;UAuDMsB,mBAAA,CAIM,OAJNC,UAIM,GAHJ3C,YAAA,CACsD4C,oBAAA;QAD3CJ,IAAI,EAAC,SAAS;QACbK,OAAK,EAAA7B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA0C,UAAU,CAAC1C,MAAA,CAAA2C,OAAO;QAAA;;QAzD7CvC,OAAA,EAAAC,QAAA,CAyDgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAzDlDgC,gBAAA,CAyDgD,IAAE,E;;QAzDlD5B,CAAA;UA0DQpB,YAAA,CAA4C4C,oBAAA;QAAhCC,OAAK,EAAEzC,MAAA,CAAA6C;MAAS;QA1DpCzC,OAAA,EAAAC,QAAA,CA0DsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA1DxCgC,gBAAA,CA0DsC,IAAE,E;;QA1DxC5B,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}