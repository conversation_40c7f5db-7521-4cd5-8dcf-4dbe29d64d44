<template>
  <div
    class="IntelligentErrorCorrection"
    v-loading="loading"
    :element-loading-spinner="svg"
    :lement-loading-text="loadingText"
    element-loading-svg-view-box="-10, -10, 50, 50">
    <div class="IntelligentErrorCorrectionHead">
      <div class="IntelligentErrorCorrectionButton">
        <div class="IntelligentErrorCorrectionButtonItem">
          <el-button type="primary" @click="handleImport">文档导入</el-button>
        </div>
        <div class="IntelligentErrorCorrectionButtonItem">
          <el-button type="primary">去一键排版</el-button>
          <el-button type="primary" @click="handleExportWord">导出</el-button>
        </div>
      </div>
      <div class="IntelligentErrorCorrectionButton">
        <div class="IntelligentErrorCorrectionButtonItem">
          <el-button type="primary" @click="handleWrongWord">智能纠错</el-button>
        </div>
        <div class="IntelligentErrorCorrectionButtonItem">
          <el-button type="primary" @click="handleAllReplace">全部替换</el-button>
        </div>
      </div>
    </div>
    <div class="IntelligentErrorCorrectionBody">
      <div class="IntelligentErrorCorrectionBodyLeft">
        <TinyMceEditor ref="wordRef" v-model="content" :setting="setting" :content_style="content_style" />
      </div>
      <div class="IntelligentErrorCorrectionBodyRight">
        <div class="globalTable">
          <el-table :data="checklist">
            <el-table-column label="错误类型" min-width="120" prop="type.name" />
            <el-table-column label="错误内容" min-width="120">
              <template #default="scope">
                <el-link @click="handleDetails(scope.row)" :disabled="arrId.includes(scope.row.id)" type="primary">
                  {{ scope.row.word }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="修改建议" min-width="120">
              <template #default="scope">{{ scope.row?.suggest[0] || scope.row?.explanation }}</template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right" class-name="globalTableCustom">
              <template #default="scope">
                <el-button
                  @click="handleReplace(scope.row)"
                  :disabled="arrId.includes(scope.row.id) || !scope.row?.suggest?.length"
                  type="primary"
                  plain>
                  替换
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'IntelligentErrorCorrection' }
</script>

<script setup>
import api from '@/api'
import { ref } from 'vue'
import { useStore } from 'vuex'
import { setting, content_style, guid, trigerUpload } from '../../AiToolBox/AiToolBox.js'
import { elAttr } from './IntelligentErrorCorrection.js'
import { ElMessage } from 'element-plus'

const store = useStore()

const svg =
  '<path class="path" d="M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15" style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>'

const loading = ref(false)
const loadingText = ref('')
const wordRef = ref()
const oldId = ref('')
const arrId = ref([])
const content = ref('')
const checklist = ref([])

const handleImport = () => {
  trigerUpload().then((file) => {
    const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
    const isShow = ['doc', 'docx', 'wps'].includes(fileType)
    if (!isShow) return ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'wps'].join('、')}格式!` })
    loading.value = true
    fileWordUpload(file)
  })
}
const fileWordUpload = async (file) => {
  try {
    const param = new FormData()
    param.append('file', file)
    const { data } = await api.fileword2html(param)
    content.value = data
      .replace(/<\/?html[^>]*>/g, '')
      .replace(/<head\b[^<]*(?:(?!<\/head>)<[^<]*)*<\/head>/gi, '')
      .replace(/<\/?body[^>]*>/g, '')
      .replace(/<\/?div[^>]*>/g, '')
    loading.value = false
  } catch (err) {
    loading.value = false
  }
}
const handleWrongWord = async () => {
  if (!content.value) return ElMessage({ type: 'warning', message: '请先输入内容在进行智能纠错！' })
  loading.value = true
  const { data } = await api.typingVerification({
    text: content.value.replace(/&ldquo;/gi, '“').replace(/&rdquo;/gi, '”')
  })
  oldId.value = ''
  arrId.value = []
  checklist.value = data?.checklist?.map((v) => ({ ...v, id: guid() })) || []
  content.value = data?.replace_text || ''
  loading.value = false
}
const handleDetails = (row) => {
  const iframe = wordRef.value?.getEditor()?.iframeElement
  const body = iframe?.contentWindow?.document?.body
  const elList = body?.childNodes || []
  if (oldId.value) {
    const oldObj = elAttr(elList, oldId.value)
    if (oldObj.elArr.length) {
      for (let index = 0; index < oldObj.elArr.length; index++) {
        const item = oldObj.elArr[index]
        if (!index) iframe.contentWindow.scrollTo(0, item.offsetTop - 100)
        item.style.color = ''
        item.style.backgroundColor = ''
      }
    }
  }
  oldId.value = row.position + ''
  const obj = elAttr(elList, row.position + '')
  if (obj.elArr.length) {
    for (let index = 0; index < obj.elArr.length; index++) {
      const item = obj.elArr[index]
      if (!index) iframe.contentWindow.scrollTo(0, item.offsetTop - 100)
      item.style.color = '#fff'
      item.style.backgroundColor = 'red'
    }
  }
}
const handleReplace = (row) => {
  const iframe = wordRef.value?.getEditor()?.iframeElement
  const body = iframe?.contentWindow?.document?.body
  const elList = body?.childNodes || []
  const obj = elAttr(elList, row.position + '')
  if (obj.elArr.length > 1) {
    let styleStr = ''
    for (let key in obj.styleObj) {
      styleStr += `${key}:${obj.styleObj[key]};`
    }
    for (let index = 0; index < obj.elArr.length; index++) {
      const item = obj.elArr[index]
      const elParent = item
      if (!index) {
        elParent.insertAdjacentHTML('beforebegin', `<span style="${styleStr}">${row.suggest[0]}</span>`)
      }
      elParent.parentNode.removeChild(elParent)
    }
  } else {
    obj.elArr[0].insertAdjacentHTML('beforebegin', row.suggest[0])
    obj.elArr[0].parentNode.removeChild(obj.elArr[0])
  }
  arrId.value.push(row.id)
}
const handleAllReplace = () => {
  for (let index = 0; index < checklist.value.length; index++) {
    const item = checklist.value[index]
    if (!arrId.value.includes(item.id) && item?.suggest?.length) {
      handleReplace(item)
    }
  }
}
const handleExportWord = () => {
  store.commit('setExportWordHtmlObj', {
    code: 'exportWord',
    name: '智能纠错 --- 文档导出',
    key: 'content',
    data: { content: content.value }
  })
}
</script>
<style lang="scss">
.IntelligentErrorCorrection {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .IntelligentErrorCorrectionHead {
    width: 100%;
    padding: var(--zy-distance-two) 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .IntelligentErrorCorrectionButton {
      width: 796px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      & + .IntelligentErrorCorrectionButton {
        width: calc(100% - 840px);
      }
      .IntelligentErrorCorrectionButtonItem {
        display: flex;
        align-items: center;
      }
    }
  }
  .IntelligentErrorCorrectionBody {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-two) * 2)));
    display: flex;
    justify-content: space-between;
    padding-bottom: var(--zy-distance-two);
    .IntelligentErrorCorrectionBodyLeft {
      width: 820px;
      height: 100%;
      .TinyMceEditor {
        height: 100%;
        .tox-tinymce {
          border: none;
        }
        .tox-editor-header {
          width: 796px;
          border: 1px solid #ccc;
          border-bottom: none;
          margin: auto;
          margin-right: 30px;
        }
      }
    }
    .IntelligentErrorCorrectionBodyRight {
      width: calc(100% - 840px);
      height: 100%;
      .globalTable {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
