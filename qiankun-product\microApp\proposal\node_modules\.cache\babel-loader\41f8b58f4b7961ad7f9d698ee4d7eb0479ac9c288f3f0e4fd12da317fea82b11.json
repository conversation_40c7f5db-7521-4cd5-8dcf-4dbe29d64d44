{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { exportWordHtmlList, exportWordHtmlObj, extendDownloadFile } from 'common/config/MicroGlobal';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { format } from 'common/js/time.js';\nvar whetherDataType = function whetherDataType(obj) {\n  var toString = Object.prototype.toString;\n  var map = {\n    '[object Boolean]': 'boolean',\n    '[object Number]': 'number',\n    '[object String]': 'string',\n    '[object Function]': 'function',\n    '[object Array]': 'array',\n    '[object Date]': 'date',\n    '[object RegExp]': 'regExp',\n    '[object Undefined]': 'undefined',\n    '[object Null]': 'null',\n    '[object Object]': 'object'\n  };\n  return map[toString.call(obj)];\n};\nvar group = function group(array, subGroupLength) {\n  var index = 0;\n  var newArray = [];\n  while (index < array.length) {\n    newArray.push(array.slice(index, index += subGroupLength));\n  }\n  return newArray;\n};\nexport var filterTableData = function filterTableData(row) {\n  var _row$joinUsers;\n  row.content = row.content.replace(/<p>/g, '<p style=\"font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;\">');\n  var rowObj = {};\n  for (var key in row) {\n    var type = whetherDataType(row[key]);\n    if (type === 'array') {\n      rowObj[key] = row[key] || [];\n    } else if (type === 'object') {\n      rowObj[key] = row[key] || {};\n      rowObj[key + 'Name'] = row[key].dictName || row[key].name || '';\n      rowObj[key + 'View'] = row[key].itemName || '';\n    } else {\n      rowObj[key] = row[key] || '';\n    }\n  }\n  rowObj.docName = `${row.serialNumber || row.streamNumber}_${row.title}`;\n  if (row.title.length > 21) {\n    rowObj.titleto = true;\n    rowObj.contentTitle = row.title.slice(0, 21);\n    rowObj.contentTitle1 = row.title.slice(21);\n  } else {\n    rowObj.titleshowto = true;\n    rowObj.contentTitle = row.title;\n    rowObj.contentTitle1 = '';\n  }\n  if (row.title.length > 23) {\n    rowObj.titleone = row.title.slice(0, 23);\n    rowObj.titletwo = row.title.slice(23);\n    rowObj.titletwoArr = group(row.title.slice(23), 28);\n    rowObj.titleshow = true;\n  } else {\n    rowObj.titleone = row.title;\n    rowObj.titleshow = false;\n  }\n  if (row !== null && row !== void 0 && (_row$joinUsers = row.joinUsers) !== null && _row$joinUsers !== void 0 && _row$joinUsers.length) {\n    rowObj.joinUser = `${row.joinUsers[0].userName}（等${row.joinUsers.length}人）`;\n  } else {\n    rowObj.joinUser = '';\n  }\n  if (row !== null && row !== void 0 && row.mainHandleOffice) {\n    rowObj.direct = true;\n  } else if (row !== null && row !== void 0 && row.publishHandleOffice) {\n    rowObj.jointly = true;\n  }\n  return rowObj;\n};\nvar suggestionWord = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(params) {\n    var _yield$api$suggestion, data, wordData, index, create;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _context.next = 2;\n          return api.suggestionWord(_objectSpread(_objectSpread({}, params), {}, {\n            isContainMerge: 1\n          }));\n        case 2:\n          _yield$api$suggestion = _context.sent;\n          data = _yield$api$suggestion.data;\n          wordData = [];\n          data.forEach(function (v) {\n            var _v$circlesType, _v$boutType, _v$party;\n            v.handleType = v.handleType === 'publish' ? '分办' : '主办/协办';\n            v.assistHandleOffice = v.handleType === '分办' ? v.publishHandleOffice : v.assistHandleOffice;\n            v.circlesTypeName = (_v$circlesType = v.circlesType) === null || _v$circlesType === void 0 ? void 0 : _v$circlesType.name;\n            v.boutTypeName = (_v$boutType = v.boutType) === null || _v$boutType === void 0 ? void 0 : _v$boutType.name;\n            v.partyName = (_v$party = v.party) === null || _v$party === void 0 ? void 0 : _v$party.name;\n          });\n          for (index = 0; index < data.length; index++) {\n            wordData.push(filterTableData(data[index]));\n          }\n          create = {\n            url: '/proposal/loadDocAnswerZip',\n            params: params\n          };\n          exportWordHtmlList({\n            create: create,\n            code: 'proposalDetails',\n            name: '提案导出Word',\n            key: 'content',\n            wordNameKey: 'docName',\n            data: wordData\n          });\n        case 9:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  }));\n  return function suggestionWord(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nvar suggestionContent = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(params) {\n    var _yield$api$suggestion2, data, wordData, index;\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          _context2.next = 2;\n          return api.suggestionWord(params);\n        case 2:\n          _yield$api$suggestion2 = _context2.sent;\n          data = _yield$api$suggestion2.data;\n          wordData = [];\n          for (index = 0; index < data.length; index++) {\n            wordData.push(filterTableData(data[index]));\n          }\n          if (wordData.length === 1) {\n            exportWordHtmlObj({\n              code: 'proposalContentDetails',\n              name: wordData[0].docName,\n              key: 'content',\n              data: wordData[0]\n            });\n          } else {\n            exportWordHtmlList({\n              code: 'proposalContentDetails',\n              name: '提案导出正文',\n              key: 'content',\n              wordNameKey: 'docName',\n              data: wordData\n            });\n          }\n        case 7:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  }));\n  return function suggestionContent(_x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport var suggestExportWord = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {\n    var isOpen,\n      _args3 = arguments;\n    return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          isOpen = _args3.length > 1 && _args3[1] !== undefined ? _args3[1] : false;\n          if (data.selectId.length) {\n            ElMessageBox.confirm('此操作将当前选中的提案导出word, 是否继续?', '提示', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            }).then(function () {\n              suggestionWord(isOpen ? {\n                ids: data.selectId,\n                tableId: data.params.tableId\n              } : {\n                ids: data.selectId\n              });\n            }).catch(function () {\n              ElMessage({\n                type: 'info',\n                message: '已取消导出'\n              });\n            });\n          } else {\n            ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出所有数据?', '提示', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            }).then(function () {\n              suggestionWord(data.params);\n            }).catch(function () {\n              ElMessage({\n                type: 'info',\n                message: '已取消导出'\n              });\n            });\n          }\n        case 2:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3);\n  }));\n  return function suggestExportWord(_x3) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nexport var suggestExportContent = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(data) {\n    return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          if (data.selectId.length) {\n            ElMessageBox.confirm('此操作将当前选中的提案导出正文, 是否继续?', '提示', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            }).then(function () {\n              suggestionContent({\n                ids: data.selectId\n              });\n            }).catch(function () {\n              ElMessage({\n                type: 'info',\n                message: '已取消导出'\n              });\n            });\n          } else {\n            ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出所有数据?', '提示', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            }).then(function () {\n              suggestionContent(data.params);\n            }).catch(function () {\n              ElMessage({\n                type: 'info',\n                message: '已取消导出'\n              });\n            });\n          }\n        case 1:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return function suggestExportContent(_x4) {\n    return _ref4.apply(this, arguments);\n  };\n}();\nexport var clueExportWord = /*#__PURE__*/function () {\n  var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5(data) {\n    return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n      while (1) switch (_context5.prev = _context5.next) {\n        case 0:\n          if (data.selectId.length) {\n            ElMessageBox.confirm('此操作将当前选中的提案线索导出word, 是否继续?', '提示', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            }).then(function () {\n              clueWord({\n                ids: data.selectId\n              });\n            }).catch(function () {\n              ElMessage({\n                type: 'info',\n                message: '已取消导出'\n              });\n            });\n          } else {\n            ElMessageBox.confirm('当前没有选择提案线索，是否根据列表筛选条件导出所有数据?', '提示', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            }).then(function () {\n              clueWord(data.params);\n            }).catch(function () {\n              ElMessage({\n                type: 'info',\n                message: '已取消导出'\n              });\n            });\n          }\n        case 1:\n        case \"end\":\n          return _context5.stop();\n      }\n    }, _callee5);\n  }));\n  return function clueExportWord(_x5) {\n    return _ref5.apply(this, arguments);\n  };\n}();\nvar clueWord = /*#__PURE__*/function () {\n  var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6(params) {\n    var _yield$api$clueWord, data, wordData, index, rowData;\n    return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n      while (1) switch (_context6.prev = _context6.next) {\n        case 0:\n          _context6.next = 2;\n          return api.clueWord(params);\n        case 2:\n          _yield$api$clueWord = _context6.sent;\n          data = _yield$api$clueWord.data;\n          wordData = [];\n          for (index = 0; index < data.length; index++) {\n            rowData = data[index];\n            rowData.proposalClueTypeLabel = '';\n            rowData.createDate = format(rowData.createDate);\n            rowData.furnishMobile = rowData.furnishMobile ? rowData.furnishMobile : '';\n            if (rowData.proposalClueType) {\n              rowData.proposalClueTypeLabel = rowData.proposalClueType.label;\n            }\n            wordData.push(rowData);\n          }\n          exportWordHtmlList({\n            code: 'proposalClueDetails',\n            name: '提案线索导出Word',\n            key: 'content',\n            wordNameKey: 'title',\n            data: wordData\n          });\n        case 7:\n        case \"end\":\n          return _context6.stop();\n      }\n    }, _callee6);\n  }));\n  return function clueWord(_x6) {\n    return _ref6.apply(this, arguments);\n  };\n}();\nexport var suggestExportAnswer = /*#__PURE__*/function () {\n  var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7(data) {\n    return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n      while (1) switch (_context7.prev = _context7.next) {\n        case 0:\n          if (data.selectId.length) {\n            ElMessageBox.confirm('此操作将当前选中的提案导出答复件, 是否继续?', '提示', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            }).then(function () {\n              suggestionAnswer({\n                ids: data.selectId\n              });\n            }).catch(function () {\n              ElMessage({\n                type: 'info',\n                message: '已取消导出'\n              });\n            });\n          } else {\n            ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出答复件?', '提示', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            }).then(function () {\n              suggestionAnswer(data.params);\n            }).catch(function () {\n              ElMessage({\n                type: 'info',\n                message: '已取消导出'\n              });\n            });\n          }\n        case 1:\n        case \"end\":\n          return _context7.stop();\n      }\n    }, _callee7);\n  }));\n  return function suggestExportAnswer(_x7) {\n    return _ref7.apply(this, arguments);\n  };\n}();\nvar suggestionAnswer = function suggestionAnswer(params) {\n  extendDownloadFile({\n    url: '/proposal/loadDocAnswerZip',\n    params: params,\n    fileSize: 0,\n    fileName: '提案答复件.zip',\n    fileType: 'zip'\n  });\n};", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread", "arguments", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "asyncGeneratorStep", "_asyncToGenerator", "_next", "_throw", "api", "exportWordHtmlList", "exportWordHtmlObj", "extendDownloadFile", "ElMessage", "ElMessageBox", "format", "whetherDataType", "obj", "toString", "map", "group", "array", "subGroupLength", "index", "newArray", "filterTableData", "row", "_row$joinUsers", "content", "replace", "row<PERSON><PERSON><PERSON>", "key", "dictName", "itemName", "doc<PERSON>ame", "serialNumber", "streamNumber", "title", "titleto", "contentTitle", "contentTitle1", "titleshowto", "titleone", "titletwo", "titletwoArr", "titleshow", "joinUsers", "joinUser", "userName", "mainHandleOffice", "direct", "publishHandleOffice", "jointly", "<PERSON><PERSON><PERSON>", "_ref", "_callee", "params", "_yield$api$suggestion", "data", "wordData", "_callee$", "_context", "isContainMerge", "_v$circlesType", "_v$boutType", "_v$party", "handleType", "assistHandleOffice", "circlesTypeName", "circlesType", "boutTypeName", "boutType", "partyName", "party", "url", "code", "wordNameKey", "_x", "<PERSON><PERSON><PERSON><PERSON>", "_ref2", "_callee2", "_yield$api$suggestion2", "_callee2$", "_context2", "_x2", "suggestExportWord", "_ref3", "_callee3", "isOpen", "_args3", "_callee3$", "_context3", "undefined", "selectId", "confirm", "confirmButtonText", "cancelButtonText", "ids", "tableId", "message", "_x3", "suggestExportContent", "_ref4", "_callee4", "_callee4$", "_context4", "_x4", "clueExportWord", "_ref5", "_callee5", "_callee5$", "_context5", "clueWord", "_x5", "_ref6", "_callee6", "_yield$api$clueWord", "rowData", "_callee6$", "_context6", "proposalClueTypeLabel", "createDate", "furnishMobile", "proposalClueType", "label", "_x6", "suggestExportAnswer", "_ref7", "_callee7", "_callee7$", "_context7", "suggestion<PERSON>nswer", "_x7", "fileSize", "fileName", "fileType"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/assets/js/suggestExportWord.js"], "sourcesContent": ["import api from '@/api'\r\nimport { exportWordHtmlList, exportWordHtmlObj, extendDownloadFile } from 'common/config/MicroGlobal'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { format } from 'common/js/time.js'\r\nconst whetherDataType = (obj) => {\r\n  var toString = Object.prototype.toString\r\n  var map = {\r\n    '[object Boolean]': 'boolean',\r\n    '[object Number]': 'number',\r\n    '[object String]': 'string',\r\n    '[object Function]': 'function',\r\n    '[object Array]': 'array',\r\n    '[object Date]': 'date',\r\n    '[object RegExp]': 'regExp',\r\n    '[object Undefined]': 'undefined',\r\n    '[object Null]': 'null',\r\n    '[object Object]': 'object'\r\n  }\r\n  return map[toString.call(obj)]\r\n}\r\nconst group = (array, subGroupLength) => {\r\n  let index = 0\r\n  let newArray = []\r\n  while (index < array.length) {\r\n    newArray.push(array.slice(index, (index += subGroupLength)))\r\n  }\r\n  return newArray\r\n}\r\nexport const filterTableData = (row) => {\r\n  row.content = row.content.replace(/<p>/g, '<p style=\"font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;\">');\r\n  var rowObj = {}\r\n  for (let key in row) {\r\n    const type = whetherDataType(row[key])\r\n    if (type === 'array') {\r\n      rowObj[key] = row[key] || []\r\n    } else if (type === 'object') {\r\n      rowObj[key] = row[key] || {}\r\n      rowObj[key + 'Name'] = row[key].dictName || row[key].name || ''\r\n      rowObj[key + 'View'] = row[key].itemName || ''\r\n    } else {\r\n      rowObj[key] = row[key] || ''\r\n    }\r\n  }\r\n  rowObj.docName = `${row.serialNumber || row.streamNumber}_${row.title}`\r\n  if (row.title.length > 21) {\r\n    rowObj.titleto = true\r\n    rowObj.contentTitle = row.title.slice(0, 21)\r\n    rowObj.contentTitle1 = row.title.slice(21)\r\n  } else {\r\n    rowObj.titleshowto = true\r\n    rowObj.contentTitle = row.title\r\n    rowObj.contentTitle1 = ''\r\n  }\r\n  if (row.title.length > 23) {\r\n    rowObj.titleone = row.title.slice(0, 23)\r\n    rowObj.titletwo = row.title.slice(23)\r\n    rowObj.titletwoArr = group(row.title.slice(23), 28)\r\n    rowObj.titleshow = true\r\n  } else {\r\n    rowObj.titleone = row.title\r\n    rowObj.titleshow = false\r\n  }\r\n  if (row?.joinUsers?.length) {\r\n    rowObj.joinUser = `${row.joinUsers[0].userName}（等${row.joinUsers.length}人）`\r\n  } else {\r\n    rowObj.joinUser = ''\r\n  }\r\n  if (row?.mainHandleOffice) {\r\n    rowObj.direct = true\r\n  } else if (row?.publishHandleOffice) {\r\n    rowObj.jointly = true\r\n  }\r\n  return rowObj\r\n}\r\n\r\nconst suggestionWord = async (params) => {\r\n  const { data } = await api.suggestionWord({ ...params, isContainMerge: 1 })\r\n  var wordData = []\r\n  data.forEach(v => {\r\n    v.handleType = v.handleType === 'publish' ? '分办' : '主办/协办'\r\n    v.assistHandleOffice = v.handleType === '分办' ? v.publishHandleOffice : v.assistHandleOffice\r\n    v.circlesTypeName = v.circlesType?.name\r\n    v.boutTypeName = v.boutType?.name\r\n    v.partyName = v.party?.name\r\n  })\r\n  for (let index = 0; index < data.length; index++) {\r\n    wordData.push(filterTableData(data[index]))\r\n  }\r\n  const create = { url: '/proposal/loadDocAnswerZip', params: params }\r\n  exportWordHtmlList({\r\n    create: create,\r\n    code: 'proposalDetails',\r\n    name: '提案导出Word',\r\n    key: 'content',\r\n    wordNameKey: 'docName',\r\n    data: wordData\r\n  })\r\n}\r\n\r\nconst suggestionContent = async (params) => {\r\n  const { data } = await api.suggestionWord(params)\r\n  var wordData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    wordData.push(filterTableData(data[index]))\r\n  }\r\n  if (wordData.length === 1) {\r\n    exportWordHtmlObj({ code: 'proposalContentDetails', name: wordData[0].docName, key: 'content', data: wordData[0] })\r\n  } else {\r\n    exportWordHtmlList({\r\n      code: 'proposalContentDetails',\r\n      name: '提案导出正文',\r\n      key: 'content',\r\n      wordNameKey: 'docName',\r\n      data: wordData\r\n    })\r\n  }\r\n}\r\n\r\nexport const suggestExportWord = async (data, isOpen = false) => {\r\n  if (data.selectId.length) {\r\n    ElMessageBox.confirm('此操作将当前选中的提案导出word, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        suggestionWord(isOpen ? { ids: data.selectId, tableId: data.params.tableId } : { ids: data.selectId })\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消导出' })\r\n      })\r\n  } else {\r\n    ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出所有数据?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        suggestionWord(data.params)\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消导出' })\r\n      })\r\n  }\r\n}\r\nexport const suggestExportContent = async (data) => {\r\n  if (data.selectId.length) {\r\n    ElMessageBox.confirm('此操作将当前选中的提案导出正文, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        suggestionContent({ ids: data.selectId })\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消导出' })\r\n      })\r\n  } else {\r\n    ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出所有数据?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        suggestionContent(data.params)\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消导出' })\r\n      })\r\n  }\r\n}\r\nexport const clueExportWord = async (data) => {\r\n  if (data.selectId.length) {\r\n    ElMessageBox.confirm('此操作将当前选中的提案线索导出word, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        clueWord({ ids: data.selectId })\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消导出' })\r\n      })\r\n  } else {\r\n    ElMessageBox.confirm('当前没有选择提案线索，是否根据列表筛选条件导出所有数据?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        clueWord(data.params)\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消导出' })\r\n      })\r\n  }\r\n}\r\n\r\nconst clueWord = async (params) => {\r\n  const { data } = await api.clueWord(params)\r\n  var wordData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    let rowData = data[index]\r\n    rowData.proposalClueTypeLabel = ''\r\n    rowData.createDate = format(rowData.createDate)\r\n    rowData.furnishMobile = rowData.furnishMobile ? rowData.furnishMobile : ''\r\n    if (rowData.proposalClueType) {\r\n      rowData.proposalClueTypeLabel = rowData.proposalClueType.label\r\n    }\r\n    wordData.push(rowData)\r\n  }\r\n  exportWordHtmlList({\r\n    code: 'proposalClueDetails',\r\n    name: '提案线索导出Word',\r\n    key: 'content',\r\n    wordNameKey: 'title',\r\n    data: wordData\r\n  })\r\n}\r\n\r\nexport const suggestExportAnswer = async (data) => {\r\n  if (data.selectId.length) {\r\n    ElMessageBox.confirm('此操作将当前选中的提案导出答复件, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        suggestionAnswer({ ids: data.selectId })\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消导出' })\r\n      })\r\n  } else {\r\n    ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出答复件?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        suggestionAnswer(data.params)\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消导出' })\r\n      })\r\n  }\r\n}\r\n\r\nconst suggestionAnswer = (params) => {\r\n  extendDownloadFile({\r\n    url: '/proposal/loadDocAnswerZip',\r\n    params: params,\r\n    fileSize: 0,\r\n    fileName: '提案答复件.zip',\r\n    fileType: 'zip'\r\n  })\r\n}\r\n"], "mappings": "+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,QAAAvG,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAqG,qBAAA,QAAAjG,CAAA,GAAAJ,MAAA,CAAAqG,qBAAA,CAAAxG,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAkG,MAAA,WAAAvG,CAAA,WAAAC,MAAA,CAAAuG,wBAAA,CAAA1G,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAkC,KAAA,CAAA1G,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2G,cAAA5G,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAA2G,SAAA,CAAA/B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAA4G,SAAA,CAAA3G,CAAA,IAAA2G,SAAA,CAAA3G,CAAA,QAAAA,CAAA,OAAAqG,OAAA,CAAApG,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA4G,eAAA,CAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA4G,yBAAA,GAAA5G,MAAA,CAAA6G,gBAAA,CAAAhH,CAAA,EAAAG,MAAA,CAAA4G,yBAAA,CAAA9G,CAAA,KAAAsG,OAAA,CAAApG,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAuG,wBAAA,CAAAzG,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA8G,gBAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA+G,cAAA,CAAA/G,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAiH,eAAAhH,CAAA,QAAAS,CAAA,GAAAwG,YAAA,CAAAjH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAwG,aAAAjH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAwG,WAAA,kBAAAnH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAkH,MAAA,GAAAC,MAAA,EAAApH,CAAA;AAAA,SAAAqH,mBAAAjH,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAgH,kBAAAlH,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA6G,SAAA,aAAArB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAAwH,MAAAnH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,UAAApH,CAAA,cAAAoH,OAAApH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,WAAApH,CAAA,KAAAmH,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,kBAAkB,QAAQ,2BAA2B;AACrG,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,GAAG,EAAK;EAC/B,IAAIC,QAAQ,GAAGhI,MAAM,CAACC,SAAS,CAAC+H,QAAQ;EACxC,IAAIC,GAAG,GAAG;IACR,kBAAkB,EAAE,SAAS;IAC7B,iBAAiB,EAAE,QAAQ;IAC3B,iBAAiB,EAAE,QAAQ;IAC3B,mBAAmB,EAAE,UAAU;IAC/B,gBAAgB,EAAE,OAAO;IACzB,eAAe,EAAE,MAAM;IACvB,iBAAiB,EAAE,QAAQ;IAC3B,oBAAoB,EAAE,WAAW;IACjC,eAAe,EAAE,MAAM;IACvB,iBAAiB,EAAE;EACrB,CAAC;EACD,OAAOA,GAAG,CAACD,QAAQ,CAACrG,IAAI,CAACoG,GAAG,CAAC,CAAC;AAChC,CAAC;AACD,IAAMG,KAAK,GAAG,SAARA,KAAKA,CAAIC,KAAK,EAAEC,cAAc,EAAK;EACvC,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,QAAQ,GAAG,EAAE;EACjB,OAAOD,KAAK,GAAGF,KAAK,CAACxD,MAAM,EAAE;IAC3B2D,QAAQ,CAAChE,IAAI,CAAC6D,KAAK,CAACxC,KAAK,CAAC0C,KAAK,EAAGA,KAAK,IAAID,cAAe,CAAC,CAAC;EAC9D;EACA,OAAOE,QAAQ;AACjB,CAAC;AACD,OAAO,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,GAAG,EAAK;EAAA,IAAAC,cAAA;EACtCD,GAAG,CAACE,OAAO,GAAGF,GAAG,CAACE,OAAO,CAACC,OAAO,CAAC,MAAM,EAAE,4FAA4F,CAAC;EACvI,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIC,GAAG,IAAIL,GAAG,EAAE;IACnB,IAAM/G,IAAI,GAAGqG,eAAe,CAACU,GAAG,CAACK,GAAG,CAAC,CAAC;IACtC,IAAIpH,IAAI,KAAK,OAAO,EAAE;MACpBmH,MAAM,CAACC,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC,IAAI,EAAE;IAC9B,CAAC,MAAM,IAAIpH,IAAI,KAAK,QAAQ,EAAE;MAC5BmH,MAAM,CAACC,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC,IAAI,CAAC,CAAC;MAC5BD,MAAM,CAACC,GAAG,GAAG,MAAM,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC,CAACC,QAAQ,IAAIN,GAAG,CAACK,GAAG,CAAC,CAAC9D,IAAI,IAAI,EAAE;MAC/D6D,MAAM,CAACC,GAAG,GAAG,MAAM,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC,CAACE,QAAQ,IAAI,EAAE;IAChD,CAAC,MAAM;MACLH,MAAM,CAACC,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC,IAAI,EAAE;IAC9B;EACF;EACAD,MAAM,CAACI,OAAO,GAAG,GAAGR,GAAG,CAACS,YAAY,IAAIT,GAAG,CAACU,YAAY,IAAIV,GAAG,CAACW,KAAK,EAAE;EACvE,IAAIX,GAAG,CAACW,KAAK,CAACxE,MAAM,GAAG,EAAE,EAAE;IACzBiE,MAAM,CAACQ,OAAO,GAAG,IAAI;IACrBR,MAAM,CAACS,YAAY,GAAGb,GAAG,CAACW,KAAK,CAACxD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5CiD,MAAM,CAACU,aAAa,GAAGd,GAAG,CAACW,KAAK,CAACxD,KAAK,CAAC,EAAE,CAAC;EAC5C,CAAC,MAAM;IACLiD,MAAM,CAACW,WAAW,GAAG,IAAI;IACzBX,MAAM,CAACS,YAAY,GAAGb,GAAG,CAACW,KAAK;IAC/BP,MAAM,CAACU,aAAa,GAAG,EAAE;EAC3B;EACA,IAAId,GAAG,CAACW,KAAK,CAACxE,MAAM,GAAG,EAAE,EAAE;IACzBiE,MAAM,CAACY,QAAQ,GAAGhB,GAAG,CAACW,KAAK,CAACxD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACxCiD,MAAM,CAACa,QAAQ,GAAGjB,GAAG,CAACW,KAAK,CAACxD,KAAK,CAAC,EAAE,CAAC;IACrCiD,MAAM,CAACc,WAAW,GAAGxB,KAAK,CAACM,GAAG,CAACW,KAAK,CAACxD,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACnDiD,MAAM,CAACe,SAAS,GAAG,IAAI;EACzB,CAAC,MAAM;IACLf,MAAM,CAACY,QAAQ,GAAGhB,GAAG,CAACW,KAAK;IAC3BP,MAAM,CAACe,SAAS,GAAG,KAAK;EAC1B;EACA,IAAInB,GAAG,aAAHA,GAAG,gBAAAC,cAAA,GAAHD,GAAG,CAAEoB,SAAS,cAAAnB,cAAA,eAAdA,cAAA,CAAgB9D,MAAM,EAAE;IAC1BiE,MAAM,CAACiB,QAAQ,GAAG,GAAGrB,GAAG,CAACoB,SAAS,CAAC,CAAC,CAAC,CAACE,QAAQ,KAAKtB,GAAG,CAACoB,SAAS,CAACjF,MAAM,IAAI;EAC7E,CAAC,MAAM;IACLiE,MAAM,CAACiB,QAAQ,GAAG,EAAE;EACtB;EACA,IAAIrB,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEuB,gBAAgB,EAAE;IACzBnB,MAAM,CAACoB,MAAM,GAAG,IAAI;EACtB,CAAC,MAAM,IAAIxB,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEyB,mBAAmB,EAAE;IACnCrB,MAAM,CAACsB,OAAO,GAAG,IAAI;EACvB;EACA,OAAOtB,MAAM;AACf,CAAC;AAED,IAAMuB,cAAc;EAAA,IAAAC,IAAA,GAAAhD,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAqF,QAAOC,MAAM;IAAA,IAAAC,qBAAA,EAAAC,IAAA,EAAAC,QAAA,EAAApC,KAAA,EAAAhH,MAAA;IAAA,OAAAzB,mBAAA,GAAAuB,IAAA,UAAAuJ,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAlF,IAAA,GAAAkF,QAAA,CAAA7G,IAAA;QAAA;UAAA6G,QAAA,CAAA7G,IAAA;UAAA,OACXyD,GAAG,CAAC4C,cAAc,CAAA1D,aAAA,CAAAA,aAAA,KAAM6D,MAAM;YAAEM,cAAc,EAAE;UAAC,EAAE,CAAC;QAAA;UAAAL,qBAAA,GAAAI,QAAA,CAAApH,IAAA;UAAnEiH,IAAI,GAAAD,qBAAA,CAAJC,IAAI;UACRC,QAAQ,GAAG,EAAE;UACjBD,IAAI,CAAC9H,OAAO,CAAC,UAAAJ,CAAC,EAAI;YAAA,IAAAuI,cAAA,EAAAC,WAAA,EAAAC,QAAA;YAChBzI,CAAC,CAAC0I,UAAU,GAAG1I,CAAC,CAAC0I,UAAU,KAAK,SAAS,GAAG,IAAI,GAAG,OAAO;YAC1D1I,CAAC,CAAC2I,kBAAkB,GAAG3I,CAAC,CAAC0I,UAAU,KAAK,IAAI,GAAG1I,CAAC,CAAC2H,mBAAmB,GAAG3H,CAAC,CAAC2I,kBAAkB;YAC3F3I,CAAC,CAAC4I,eAAe,IAAAL,cAAA,GAAGvI,CAAC,CAAC6I,WAAW,cAAAN,cAAA,uBAAbA,cAAA,CAAe9F,IAAI;YACvCzC,CAAC,CAAC8I,YAAY,IAAAN,WAAA,GAAGxI,CAAC,CAAC+I,QAAQ,cAAAP,WAAA,uBAAVA,WAAA,CAAY/F,IAAI;YACjCzC,CAAC,CAACgJ,SAAS,IAAAP,QAAA,GAAGzI,CAAC,CAACiJ,KAAK,cAAAR,QAAA,uBAAPA,QAAA,CAAShG,IAAI;UAC7B,CAAC,CAAC;UACF,KAASsD,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGmC,IAAI,CAAC7F,MAAM,EAAE0D,KAAK,EAAE,EAAE;YAChDoC,QAAQ,CAACnG,IAAI,CAACiE,eAAe,CAACiC,IAAI,CAACnC,KAAK,CAAC,CAAC,CAAC;UAC7C;UACMhH,MAAM,GAAG;YAAEmK,GAAG,EAAE,4BAA4B;YAAElB,MAAM,EAAEA;UAAO,CAAC;UACpE9C,kBAAkB,CAAC;YACjBnG,MAAM,EAAEA,MAAM;YACdoK,IAAI,EAAE,iBAAiB;YACvB1G,IAAI,EAAE,UAAU;YAChB8D,GAAG,EAAE,SAAS;YACd6C,WAAW,EAAE,SAAS;YACtBlB,IAAI,EAAEC;UACR,CAAC,CAAC;QAAA;QAAA;UAAA,OAAAE,QAAA,CAAA/E,IAAA;MAAA;IAAA,GAAAyE,OAAA;EAAA,CACH;EAAA,gBAtBKF,cAAcA,CAAAwB,EAAA;IAAA,OAAAvB,IAAA,CAAA5D,KAAA,OAAAE,SAAA;EAAA;AAAA,GAsBnB;AAED,IAAMkF,iBAAiB;EAAA,IAAAC,KAAA,GAAAzE,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA8G,SAAOxB,MAAM;IAAA,IAAAyB,sBAAA,EAAAvB,IAAA,EAAAC,QAAA,EAAApC,KAAA;IAAA,OAAAzI,mBAAA,GAAAuB,IAAA,UAAA6K,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAxG,IAAA,GAAAwG,SAAA,CAAAnI,IAAA;QAAA;UAAAmI,SAAA,CAAAnI,IAAA;UAAA,OACdyD,GAAG,CAAC4C,cAAc,CAACG,MAAM,CAAC;QAAA;UAAAyB,sBAAA,GAAAE,SAAA,CAAA1I,IAAA;UAAzCiH,IAAI,GAAAuB,sBAAA,CAAJvB,IAAI;UACRC,QAAQ,GAAG,EAAE;UACjB,KAASpC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGmC,IAAI,CAAC7F,MAAM,EAAE0D,KAAK,EAAE,EAAE;YAChDoC,QAAQ,CAACnG,IAAI,CAACiE,eAAe,CAACiC,IAAI,CAACnC,KAAK,CAAC,CAAC,CAAC;UAC7C;UACA,IAAIoC,QAAQ,CAAC9F,MAAM,KAAK,CAAC,EAAE;YACzB8C,iBAAiB,CAAC;cAAEgE,IAAI,EAAE,wBAAwB;cAAE1G,IAAI,EAAE0F,QAAQ,CAAC,CAAC,CAAC,CAACzB,OAAO;cAAEH,GAAG,EAAE,SAAS;cAAE2B,IAAI,EAAEC,QAAQ,CAAC,CAAC;YAAE,CAAC,CAAC;UACrH,CAAC,MAAM;YACLjD,kBAAkB,CAAC;cACjBiE,IAAI,EAAE,wBAAwB;cAC9B1G,IAAI,EAAE,QAAQ;cACd8D,GAAG,EAAE,SAAS;cACd6C,WAAW,EAAE,SAAS;cACtBlB,IAAI,EAAEC;YACR,CAAC,CAAC;UACJ;QAAC;QAAA;UAAA,OAAAwB,SAAA,CAAArG,IAAA;MAAA;IAAA,GAAAkG,QAAA;EAAA,CACF;EAAA,gBAjBKF,iBAAiBA,CAAAM,GAAA;IAAA,OAAAL,KAAA,CAAArF,KAAA,OAAAE,SAAA;EAAA;AAAA,GAiBtB;AAED,OAAO,IAAMyF,iBAAiB;EAAA,IAAAC,KAAA,GAAAhF,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAqH,SAAO7B,IAAI;IAAA,IAAA8B,MAAA;MAAAC,MAAA,GAAA7F,SAAA;IAAA,OAAA9G,mBAAA,GAAAuB,IAAA,UAAAqL,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAhH,IAAA,GAAAgH,SAAA,CAAA3I,IAAA;QAAA;UAAEwI,MAAM,GAAAC,MAAA,CAAA5H,MAAA,QAAA4H,MAAA,QAAAG,SAAA,GAAAH,MAAA,MAAG,KAAK;UAC1D,IAAI/B,IAAI,CAACmC,QAAQ,CAAChI,MAAM,EAAE;YACxBiD,YAAY,CAACgF,OAAO,CAAC,0BAA0B,EAAE,IAAI,EAAE;cACrDC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBrL,IAAI,EAAE;YACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;cACVmH,cAAc,CAACmC,MAAM,GAAG;gBAAES,GAAG,EAAEvC,IAAI,CAACmC,QAAQ;gBAAEK,OAAO,EAAExC,IAAI,CAACF,MAAM,CAAC0C;cAAQ,CAAC,GAAG;gBAAED,GAAG,EAAEvC,IAAI,CAACmC;cAAS,CAAC,CAAC;YACxG,CAAC,CAAC,CACD1G,KAAK,CAAC,YAAM;cACX0B,SAAS,CAAC;gBAAElG,IAAI,EAAE,MAAM;gBAAEwL,OAAO,EAAE;cAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;UACN,CAAC,MAAM;YACLrF,YAAY,CAACgF,OAAO,CAAC,4BAA4B,EAAE,IAAI,EAAE;cACvDC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBrL,IAAI,EAAE;YACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;cACVmH,cAAc,CAACK,IAAI,CAACF,MAAM,CAAC;YAC7B,CAAC,CAAC,CACDrE,KAAK,CAAC,YAAM;cACX0B,SAAS,CAAC;gBAAElG,IAAI,EAAE,MAAM;gBAAEwL,OAAO,EAAE;cAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;UACN;QAAC;QAAA;UAAA,OAAAR,SAAA,CAAA7G,IAAA;MAAA;IAAA,GAAAyG,QAAA;EAAA,CACF;EAAA,gBA1BYF,iBAAiBA,CAAAe,GAAA;IAAA,OAAAd,KAAA,CAAA5F,KAAA,OAAAE,SAAA;EAAA;AAAA,GA0B7B;AACD,OAAO,IAAMyG,oBAAoB;EAAA,IAAAC,KAAA,GAAAhG,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAqI,SAAO7C,IAAI;IAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAAmM,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA9H,IAAA,GAAA8H,SAAA,CAAAzJ,IAAA;QAAA;UAC7C,IAAI0G,IAAI,CAACmC,QAAQ,CAAChI,MAAM,EAAE;YACxBiD,YAAY,CAACgF,OAAO,CAAC,wBAAwB,EAAE,IAAI,EAAE;cACnDC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBrL,IAAI,EAAE;YACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;cACV4I,iBAAiB,CAAC;gBAAEmB,GAAG,EAAEvC,IAAI,CAACmC;cAAS,CAAC,CAAC;YAC3C,CAAC,CAAC,CACD1G,KAAK,CAAC,YAAM;cACX0B,SAAS,CAAC;gBAAElG,IAAI,EAAE,MAAM;gBAAEwL,OAAO,EAAE;cAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;UACN,CAAC,MAAM;YACLrF,YAAY,CAACgF,OAAO,CAAC,4BAA4B,EAAE,IAAI,EAAE;cACvDC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBrL,IAAI,EAAE;YACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;cACV4I,iBAAiB,CAACpB,IAAI,CAACF,MAAM,CAAC;YAChC,CAAC,CAAC,CACDrE,KAAK,CAAC,YAAM;cACX0B,SAAS,CAAC;gBAAElG,IAAI,EAAE,MAAM;gBAAEwL,OAAO,EAAE;cAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;UACN;QAAC;QAAA;UAAA,OAAAM,SAAA,CAAA3H,IAAA;MAAA;IAAA,GAAAyH,QAAA;EAAA,CACF;EAAA,gBA1BYF,oBAAoBA,CAAAK,GAAA;IAAA,OAAAJ,KAAA,CAAA5G,KAAA,OAAAE,SAAA;EAAA;AAAA,GA0BhC;AACD,OAAO,IAAM+G,cAAc;EAAA,IAAAC,KAAA,GAAAtG,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA2I,SAAOnD,IAAI;IAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAAyM,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAApI,IAAA,GAAAoI,SAAA,CAAA/J,IAAA;QAAA;UACvC,IAAI0G,IAAI,CAACmC,QAAQ,CAAChI,MAAM,EAAE;YACxBiD,YAAY,CAACgF,OAAO,CAAC,4BAA4B,EAAE,IAAI,EAAE;cACvDC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBrL,IAAI,EAAE;YACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;cACV8K,QAAQ,CAAC;gBAAEf,GAAG,EAAEvC,IAAI,CAACmC;cAAS,CAAC,CAAC;YAClC,CAAC,CAAC,CACD1G,KAAK,CAAC,YAAM;cACX0B,SAAS,CAAC;gBAAElG,IAAI,EAAE,MAAM;gBAAEwL,OAAO,EAAE;cAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;UACN,CAAC,MAAM;YACLrF,YAAY,CAACgF,OAAO,CAAC,8BAA8B,EAAE,IAAI,EAAE;cACzDC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBrL,IAAI,EAAE;YACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;cACV8K,QAAQ,CAACtD,IAAI,CAACF,MAAM,CAAC;YACvB,CAAC,CAAC,CACDrE,KAAK,CAAC,YAAM;cACX0B,SAAS,CAAC;gBAAElG,IAAI,EAAE,MAAM;gBAAEwL,OAAO,EAAE;cAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;UACN;QAAC;QAAA;UAAA,OAAAY,SAAA,CAAAjI,IAAA;MAAA;IAAA,GAAA+H,QAAA;EAAA,CACF;EAAA,gBA1BYF,cAAcA,CAAAM,GAAA;IAAA,OAAAL,KAAA,CAAAlH,KAAA,OAAAE,SAAA;EAAA;AAAA,GA0B1B;AAED,IAAMoH,QAAQ;EAAA,IAAAE,KAAA,GAAA5G,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAiJ,SAAO3D,MAAM;IAAA,IAAA4D,mBAAA,EAAA1D,IAAA,EAAAC,QAAA,EAAApC,KAAA,EAAA8F,OAAA;IAAA,OAAAvO,mBAAA,GAAAuB,IAAA,UAAAiN,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA5I,IAAA,GAAA4I,SAAA,CAAAvK,IAAA;QAAA;UAAAuK,SAAA,CAAAvK,IAAA;UAAA,OACLyD,GAAG,CAACuG,QAAQ,CAACxD,MAAM,CAAC;QAAA;UAAA4D,mBAAA,GAAAG,SAAA,CAAA9K,IAAA;UAAnCiH,IAAI,GAAA0D,mBAAA,CAAJ1D,IAAI;UACRC,QAAQ,GAAG,EAAE;UACjB,KAASpC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGmC,IAAI,CAAC7F,MAAM,EAAE0D,KAAK,EAAE,EAAE;YAC5C8F,OAAO,GAAG3D,IAAI,CAACnC,KAAK,CAAC;YACzB8F,OAAO,CAACG,qBAAqB,GAAG,EAAE;YAClCH,OAAO,CAACI,UAAU,GAAG1G,MAAM,CAACsG,OAAO,CAACI,UAAU,CAAC;YAC/CJ,OAAO,CAACK,aAAa,GAAGL,OAAO,CAACK,aAAa,GAAGL,OAAO,CAACK,aAAa,GAAG,EAAE;YAC1E,IAAIL,OAAO,CAACM,gBAAgB,EAAE;cAC5BN,OAAO,CAACG,qBAAqB,GAAGH,OAAO,CAACM,gBAAgB,CAACC,KAAK;YAChE;YACAjE,QAAQ,CAACnG,IAAI,CAAC6J,OAAO,CAAC;UACxB;UACA3G,kBAAkB,CAAC;YACjBiE,IAAI,EAAE,qBAAqB;YAC3B1G,IAAI,EAAE,YAAY;YAClB8D,GAAG,EAAE,SAAS;YACd6C,WAAW,EAAE,OAAO;YACpBlB,IAAI,EAAEC;UACR,CAAC,CAAC;QAAA;QAAA;UAAA,OAAA4D,SAAA,CAAAzI,IAAA;MAAA;IAAA,GAAAqI,QAAA;EAAA,CACH;EAAA,gBApBKH,QAAQA,CAAAa,GAAA;IAAA,OAAAX,KAAA,CAAAxH,KAAA,OAAAE,SAAA;EAAA;AAAA,GAoBb;AAED,OAAO,IAAMkI,mBAAmB;EAAA,IAAAC,KAAA,GAAAzH,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA8J,SAAOtE,IAAI;IAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAA4N,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAvJ,IAAA,GAAAuJ,SAAA,CAAAlL,IAAA;QAAA;UAC5C,IAAI0G,IAAI,CAACmC,QAAQ,CAAChI,MAAM,EAAE;YACxBiD,YAAY,CAACgF,OAAO,CAAC,yBAAyB,EAAE,IAAI,EAAE;cACpDC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBrL,IAAI,EAAE;YACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;cACViM,gBAAgB,CAAC;gBAAElC,GAAG,EAAEvC,IAAI,CAACmC;cAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CACD1G,KAAK,CAAC,YAAM;cACX0B,SAAS,CAAC;gBAAElG,IAAI,EAAE,MAAM;gBAAEwL,OAAO,EAAE;cAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;UACN,CAAC,MAAM;YACLrF,YAAY,CAACgF,OAAO,CAAC,2BAA2B,EAAE,IAAI,EAAE;cACtDC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBrL,IAAI,EAAE;YACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;cACViM,gBAAgB,CAACzE,IAAI,CAACF,MAAM,CAAC;YAC/B,CAAC,CAAC,CACDrE,KAAK,CAAC,YAAM;cACX0B,SAAS,CAAC;gBAAElG,IAAI,EAAE,MAAM;gBAAEwL,OAAO,EAAE;cAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;UACN;QAAC;QAAA;UAAA,OAAA+B,SAAA,CAAApJ,IAAA;MAAA;IAAA,GAAAkJ,QAAA;EAAA,CACF;EAAA,gBA1BYF,mBAAmBA,CAAAM,GAAA;IAAA,OAAAL,KAAA,CAAArI,KAAA,OAAAE,SAAA;EAAA;AAAA,GA0B/B;AAED,IAAMuI,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI3E,MAAM,EAAK;EACnC5C,kBAAkB,CAAC;IACjB8D,GAAG,EAAE,4BAA4B;IACjClB,MAAM,EAAEA,MAAM;IACd6E,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}