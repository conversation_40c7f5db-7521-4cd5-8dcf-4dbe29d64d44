<template>
  <el-scrollbar always class="SubmitProposalClue">
    <div class="SubmitProposalClueBody">
      <div class="SubmitProposalClueNameBody">
        <div class="SubmitProposalClueName">提案线索</div>
      </div>
      <el-form ref="formRef" :model="form" :rules="rules" inline :show-message="false" class="globalPaperForm">
        <el-form-item label="提案标题" prop="title" class="SubmitProposalClueTitle">
          <el-input v-model="form.title" placeholder="请输入提案标题" clearable />
        </el-form-item>
        <el-form-item label="提案大类" class="SubmitProposalClueTitle">
          <el-select v-model="form.bigTheme" placeholder="请选择提案大类" clearable>
            <el-option v-for="item in bigTheme" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="提案内容" prop="content" class="SubmitProposalClueTitle"></el-form-item>
        <TinyMceEditor v-model="form.content" />
        <div class="globalPaperFormButton">
          <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
          <el-button @click="resetForm">取消</el-button>
        </div>
      </el-form>
    </div>
  </el-scrollbar>
</template>
<script>
export default { name: 'SubmitProposalClue' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage } from 'element-plus'

const route = useRoute()
const formRef = ref()
const form = reactive({
  title: '', // 提案标题
  bigTheme: '',
  content: ''
})
const rules = reactive({
  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }]
})
const bigTheme = ref([])

onActivated(() => {
  suggestionThemeSelect()
  if (route.query.id) { proposalClueInfo() }
})
const suggestionThemeSelect = async () => {
  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })
  var { data } = res
  bigTheme.value = data
}
const proposalClueInfo = async () => {
  const { data } = await api.proposalClueInfo({ detailId: route.query.id })
  form.title = data.title
  form.bigTheme = data.bigThemeId
  form.content = data.content
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(route.query.id ? '/proposalClue/edit' : '/proposalClue/add', {
    form: { title: form.title, bigTheme: form.bigTheme, content: form.content }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: route.query.id ? '编辑成功' : '提交成功' })
    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
  }
}
const resetForm = () => {
  qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
}
</script>
<style lang="scss">
.SubmitProposalClue {
  width: 100%;
  height: 100%;

  .SubmitProposalClueBody {
    width: 990px;
    margin: 20px auto;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);

    .SubmitProposalClueNameBody {
      padding: var(--zy-distance-one);
      padding-bottom: 0;

      .SubmitProposalClueName {
        padding: 20px 40px;
        font-weight: bold;
        text-align: center;
        color: var(--zy-el-color-primary);
        font-size: var(--zy-title-font-size);
        line-height: var(--zy-line-height);
        border-bottom: 3px solid var(--zy-el-color-primary);
      }
    }

    .globalPaperForm {
      width: 100%;
      padding: var(--zy-distance-one);
      padding-top: 0;

      .zy-el-form-item {
        width: 50%;
        margin: 0;
        border-bottom: 1px solid var(--zy-el-color-primary);

        .zy-el-form-item__label {
          width: 138px;
          justify-content: center;
        }

        .zy-el-form-item__content {
          border-left: 1px solid var(--zy-el-color-primary);
          border-right: 1px solid transparent;

          &>.zy-el-input,
          .zy-el-input-number {
            width: 100%;

            .zy-el-input__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          &>.zy-el-select,
          .zy-el-select-v2 {
            .zy-el-select__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          &>.zy-el-radio-group {
            padding-left: 15px;
          }

          &>.zy-el-date-editor {
            width: 100%;

            &>.zy-el-input__wrapper {
              width: 100%;
              box-shadow: 0 0 0 0 !important;
            }
          }
        }
      }

      .SubmitProposalClueTitle {
        width: 100%;

        .zy-el-form-item__content {
          border-right-color: transparent;
        }
      }

      .TinyMceEditor {
        border-bottom: 1px solid var(--zy-el-color-primary);
      }

      .globalPaperFormButton {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 22px;

        .zy-el-button+.zy-el-button {
          margin-left: var(--zy-distance-two);
        }
      }
    }
  }
}
</style>
