import { ref, watch, onMounted, computed, nextTick, provide, onUnmounted } from 'vue'
import api from '@/api'
import { useStore } from 'vuex'
import config from 'common/config'
import { qiankunActions, loadFilterApp } from '@/qiankun'
import { handleCompareVersion } from 'common/js/CheckVersion'
import { globalReadOpenConfig } from 'common/js/GlobalMethod'
import { get_font_family, change_font_family } from 'common/js/utils'
import elementResizeDetectorMaker from 'element-resize-detector'
import unauthorized from 'common/components/unauthorized'
import { ElMessage, ElMessageBox } from 'element-plus'
export const refreshIcon = `<svg t="1743990581190" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="52145" width="30" height="30"><path d="M512 128a384 384 0 1 1 0 768A384 384 0 0 1 512 128z m0 48.192C335.296 176.192 174.08 335.36 174.08 512c0 176.704 161.216 336.512 337.92 336.512S849.152 688.704 849.152 512 688.704 176.192 512 176.192z" fill="#ffffff" p-id="52146"></path><path d="M244.352 506.688h73.6c0-123.2 86.016-220.032 196.48-215.68 36.8 0 69.568 13.248 102.272 30.848l-36.8 39.68c-20.48-8.96-45.056-17.664-69.568-17.664-77.76 0-143.232 70.4-143.232 162.816h73.6l-98.176 110.08-98.176-110.08zM705.088 515.84c0 117.248-86.848 217.28-194.368 217.28-37.248 0-70.4-17.344-103.424-34.752l37.184-39.04c20.736 8.704 45.568 17.344 70.4 17.344 78.592 0 144.768-69.504 144.768-160.768H581.056l99.328-108.608 99.2 108.544h-74.496z" fill="#ffffff" p-id="52147"></path></svg>`

export const LayoutView = (route, router) => {
  const store = useStore()
  const menuIcon = `${config.API_URL}/pageImg/open/menuIcon`

  const erd = elementResizeDetectorMaker()
  const left = ref(0)
  const width = ref('')
  const LayoutViewBox = ref(null)
  const LayoutViewInfo = ref(null)
  const editPassWordShow = ref(false)
  const verifyEditPassWord = ref('')
  const verifyEditPassWordShow = ref(false)
  const MicroApp = ref([])
  const MicroAppObj = ref({})
  // 用户信息
  const user = computed(() => store.getters.getUserFn)
  // 地区信息
  const area = computed(() => store.getters.getAreaFn)
  // 角色信息
  const role = computed(() => store.getters.getRoleFn)
  const openConfig = computed(() => store.getters.getReadOpenConfig)
  const helpShow = ref(false)

  onMounted(() => {
    handleRegionSelect()
    nextTick(() => {
      erd.listenTo(LayoutViewBox.value, (element) => {
        left.value = element.offsetWidth
      })
      erd.listenTo(LayoutViewInfo.value, (element) => {
        width.value = `width: calc(100% - ${element.offsetWidth + 16}px);`
      })
    })
  })
  const handleRegionSelect = () => {
    const oldRegionInfo = sessionStorage.getItem('oldRegionInfo') || ''
    const isRegionSelect = sessionStorage.getItem('isRegionSelect') || ''
    if (user.value?.accountId !== '1' && user.value?.areaTotal > 1 && oldRegionInfo && !isRegionSelect) {
      isRegionSelectShow.value = true
    }
  }
  const handleCommand = (type) => {
    if (type === 'task') {
      store.commit('setGlobalCentralControlObj', { show: true })
    } else if (type === 'refresh') {
      // window.location.reload(true)
      // window.location.reload(window.location.href)
      window.location.href = `${config.mainPath}?v=${new Date().getTime()}`
    } else if (type === 'locale') {
      if (get_font_family() === 'cn') return change_font_family('tw')
      if (get_font_family() === 'tw') return change_font_family('cn')
    } else if (type === 'help') {
      helpShow.value = true
    } else if (type === 'edit_password') {
      verifyEditPassWord.value = ''
      editPassWordShow.value = true
    } else if (type === 'exit') {
      handleExit()
    } else {
      ElMessage({ type: 'info', message: '正在开发中！' })
    }
  }
  const editPassWordCallback = (type) => {
    if (type) {
      loginOut('请使用新密码重新登录！')
    }
    editPassWordShow.value = false
  }
  const handleExit = () => {
    ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        loginOut('已安全退出！')
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消退出' })
      })
  }
  const loginOut = async (text) => {
    const { code } = await api.loginOut()
    if (code === 200) {
      sessionStorage.clear()
      const goal_login_router_path = localStorage.getItem('goal_login_router_path')
      if (goal_login_router_path) {
        const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''
        router.push({
          path: goal_login_router_path,
          query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}
        })
      } else {
        router.push({ path: '/LoginView' })
      }
      store.commit('setState')
      globalReadOpenConfig()
      // store.state.socket.disconnect()
      // store.state.socket = null
      ElMessage({ message: text, showClose: true, type: 'success' })
    }
  }

  // 地区id
  const regionId = ref('')
  const regionName = ref('')
  const isRegionSelectShow = ref(false)
  const regionSelect = (item) => {
    regionName.value = item.name
    isRegionSelectShow.value = false
    sessionStorage.setItem('AreaRow', JSON.stringify(item))
    if (user.value?.areaId === item.id) return
    verifyLoginUser(item)
  }
  const verifyLoginUser = async (item) => {
    const { code } = await api.verifyLoginUser({}, item.id)
    if (code === 200) {
      tabMenu.value = ''
      sessionStorage.setItem('AreaId', item.id)
      globalReadOpenConfig()
      store.dispatch('loginUser', 'login')
      store.commit('setBoxMessageRefresh', true)
      store.commit('setPersonalDoRefresh', true)
    } else {
      regionId.value = user.value?.areaId
      unauthorized({ name: item.name })
    }
  }
  watch(
    () => user.value,
    () => {
      regionId.value = user.value?.areaId
      if (user.value?.accountId) {
        const verify = sessionStorage.getItem('verify')
        if (verify && !Number(verify) && user.value?.accountId !== '1') {
          if (openConfig.value?.forbidWeakPassword === 'true') {
            nextTick(() => {
              verifyEditPassWord.value = 'yes'
              verifyEditPassWordShow.value = true
            })
          } else {
            nextTick(() => {
              verifyEditPassWord.value = 'no'
              editPassWordShow.value = true
            })
          }
        }
      }
    },
    { immediate: true }
  )
  // 菜单过滤
  const filterMenu = (menuList) => {
    let newMenuList = []
    for (let i = 0, len = menuList.length; i < len; i++) {
      newMenuList.push({
        id: menuList[i].menuId,
        name: menuList[i].name,
        routePath: menuList[i].routePath,
        menuFunction: menuList[i].menuFunction,
        menuRouteType: menuList[i].menuRouteType,
        icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,
        has: menuList[i].permissions,
        children: filterMenu(menuList[i].children || [])
      })
    }
    return newMenuList
  }
  // 顶部菜单id
  const tabMenu = ref('')
  // 顶部菜单data
  const tabMenuData = computed(() => filterMenu(store.getters.getMenuFn || []))
  // 是否显示左侧菜单
  const isView = ref(false)
  // 工作台跳转具体应用
  const isChildView = ref(false)
  // 具体打开页面id
  const isOpen = ref(false)
  // 具体打开页面id
  const openPageId = ref('')
  const openPageObj = ref({})
  // 打开页面工作台应用id
  const openPageChildId = ref('')
  // 工作台对象
  const WorkBenchObj = ref({})
  // 工作台子应用data
  const WorkBenchList = ref([])
  // 具体工作台子应用对象
  const childData = ref({})
  watch(
    () => tabMenuData.value,
    () => {
      isView.value = false
      isChildView.value = false
      WorkBenchObj.value = {}
      WorkBenchList.value = []
      childData.value = {}
      if (tabMenuData.value.length) {
        const query = JSON.parse(sessionStorage.getItem('query')) || {}
        if (query.openPageValue) {
          openPage({ key: query.openPageKey || 'id', value: query.openPageValue })
        } else {
          nextTick(() => {
            tabMenu.value = tabMenuData.value[0]?.id
            handleClick()
          })
        }
      }
    },
    { immediate: true }
  )
  // 顶部菜单切换事件
  const handleClick = () => {
    if (process.env.NODE_ENV !== 'development') {
      const detection_version = openConfig.value.DetectionVersion || ''
      if (detection_version !== 'true') handleCompareVersion()
    }
    menuId.value = ''
    menuData.value = []
    tabData.value = []
    isTabData.value = []
    keepAliveRoute.value = []
    nextTick(() => {
      for (let i = 0, len = tabMenuData.value.length; i < len; i++) {
        const item = tabMenuData.value[i]
        if (tabMenu.value === item.id) {
          sessionStorage.setItem('has', JSON.stringify(item))
          if (['/WorkBench', '/WorkBenchCopy'].includes(item.routePath)) { // 切换到工作台
            isView.value = false
            router.push({ path: item.routePath, query: routePathData(item.routePath) })
            WorkBenchObj.value = item
            WorkBenchList.value = item.children
            nextTick(() => {
              if (openPageChildId.value) {
                if (openPageChildId.value === openPageId.value) {
                  openPageId.value = ''
                  openPageObj.value = {}
                }
                for (let r = 0, length = item.children.length; r < length; r++) {
                  if (item.children[r].id === openPageChildId.value) {
                    leftMenuData(item.children[r])
                  }
                }
              }
            })
          } else {
            if (item.routePath.includes('/GlobalHome')) {
              if (openPageId.value) {
                WorkBenchObj.value = item
                leftMenuData(item, false)
              } else {
                isView.value = false
                router.push({ path: item.routePath, query: routePathData(item.routePath) })
              }
              return
            }
            // 不是工作台页面判断是否有子级菜单
            if (item.children && item.children.length) {
              // 有子级菜单按照左侧菜单显示
              leftMenuData(item, true)
            } else {
              if (['3', '4'].includes(item.menuRouteType.value)) {
                isView.value = false
                isChildView.value = false
                router.push({
                  path: item.routePath,
                  query: { ...routePathData(item.routePath), menuRouteType: item.menuRouteType.value }
                })
              } else {
                // const menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath
                // if (routePath(menuUrl) === '/') {
                //   isView.value = false
                //   const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath) }
                //   router.push({ path: item.routePath, query: query })
                //   const mainAppName = [mainRoutePath(item.routePath)]
                //   keepAliveRoute.value = mainAppName
                // } else {
                leftMenuData(item, true)
                // }
              }
            }
          }
        }
      }
    })
  }
  const WorkBenchMenu = (tabMenuId, tabMenuChildren) => {
    tabMenu.value = tabMenuId
    menuId.value = ''
    menuData.value = []
    tabData.value = []
    isTabData.value = []
    keepAliveRoute.value = []
    leftMenuData(tabMenuChildren)
  }
  const leftMenuData = (item, type) => {
    // 显示左侧菜单方法
    // 不是工作台页面判断是否有子级菜单
    if (item.children && item.children.length) {
      // 有子级菜单按照左侧菜单显示
      if (type) {
        isView.value = true
        isChildView.value = false
      } else {
        isView.value = true
        isChildView.value = true
        childData.value = item
      }
      menuData.value = item.children
      const obj = openPageId.value ? menuOpenPage(item.children) : menuDefault(item.children)
      menuId.value = obj.id
      menuClick(obj)
      nextTick(() => {
        openPageId.value = ''
        openPageChildId.value = ''
      })
    } else {
      if (['3', '4'].includes(item.menuRouteType.value)) {
        isView.value = false
        isChildView.value = true
        childData.value = item
        router.push({
          path: item.routePath,
          query: { ...routePathData(item.routePath), menuRouteType: item.menuRouteType.value }
        })
      } else {
        if (type) {
          isView.value = false
          isChildView.value = false
        } else {
          isView.value = false
          isChildView.value = true
          childData.value = item
        }
        menuData.value = [item]
        const obj = openPageId.value ? menuOpenPage([item]) : menuDefault([item])
        menuId.value = obj.id
        menuClick(obj)
        nextTick(() => {
          openPageId.value = ''
          openPageChildId.value = ''
        })
      }
    }
  }

  const menuDefault = (data) => {
    // 获取左侧菜单第一个菜单
    var defaultObj = {}
    for (let i = 0, len = data.length; i < len; i++) {
      if (i === 0) {
        if (data[i].children.length === 0) {
          defaultObj = data[i]
        } else {
          defaultObj = menuDefault(data[i].children)
        }
      }
    }
    return defaultObj
  }
  const menuOpenPage = (data) => {
    // 获取左侧菜单第一个菜单
    var defaultObj = {}
    for (let i = 0, len = data.length; i < len; i++) {
      if (openPageId.value === data[i].id) {
        defaultObj = data[i]
      }
      if (data[i].children.length) {
        const obj = menuOpenPage(data[i].children)
        defaultObj = obj.id ? obj : defaultObj
      }
    }
    return defaultObj
  }
  const menuId = ref('')
  const menuData = ref([])
  const menuClick = (item) => {
    handleCloseOther(item.id)
    // 左侧菜单点击事件
    if (!tabData.value.length) {
      qiankunActions.setGlobalState({ keepAliveRoute: [] })
    }
    if (!tabData.value.map((v) => v.id).includes(item.id)) {
      tabData.value.push(item)
    }
    tabClick()
  }
  const WorkBenchReturn = () => {
    // 工作台具体应用返回工作台
    if (isChildView.value) {
      isView.value = false
      isChildView.value = false
      handleClick()
    }
  }
  const handleBreadcrumb = (item, index) => {
    if (index + 1 === tabData.value.length) return
    const newTabData = tabData.value.slice(0, index + 1)
    const delTabData = tabData.value.slice(index + 1).map((v) => v.id)
    tabData.value = newTabData
    isTabData.value = isTabData.value.filter((item) => !delTabData.includes(item.id))
    const mainAppList = tabData.value.filter(
      (v) => routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/'
    )
    const mainAppName = Array.from(new Set(mainAppList.map((v) => mainRoutePath(v.routePath))))
    keepAliveRoute.value = mainAppName
    menuId.value = item.id
    tabClick()
  }

  const tabData = ref([]) // tab数据
  const isTabData = ref([])
  const isMicroApp = ref('')
  const noMicroApp = ref([])
  const keepAliveRoute = ref([])
  const add_msg = (a, b) => a.filter((v) => b.indexOf(v) === -1)
  // const delete_msg = (a, b) => b.filter(v => a.indexOf(v) === -1)
  const tabClick = () => {
    const microAppName = Object.keys(config.microApp)
    const MicroAppData = Array.from(new Set(tabData.value.map((v) => routePath(v.routePath)))).filter((v) =>
      microAppName.includes(v)
    )
    const addMicroApp = add_msg(MicroAppData, MicroApp.value)
    // const delMicroApp = delete_msg(MicroAppData, MicroApp.value)
    MicroApp.value = [...MicroApp.value, ...addMicroApp]
    if (!addMicroApp.length) {
      menuRouterPush()
      return
    }
    nextTick(() => {
      for (let i = 0, len = addMicroApp.length; i < len; i++) {
        const v = addMicroApp[i]
        if (!MicroAppObj.value[v]) {
          MicroAppObj.value[v] = loadFilterApp(v)
          MicroAppObj.value[v].loadPromise
            .then(() => {
              MicroAppObj.value[v].mountPromise.then(() => {
                qiankunActions.setGlobalState({
                  theme: store.getters.getThemeFn,
                  user: store.getters.getUserFn,
                  menu: store.getters.getMenuFn,
                  area: store.getters.getAreaFn,
                  role: store.getters.getRoleFn,
                  readConfig: store.getters.getReadConfig,
                  readOpenConfig: store.getters.getReadOpenConfig
                })
              })
            })
            .catch((err) => {
              noMicroApp.value.push(v)
            })
        }
      }
      setTimeout(() => {
        menuRouterPush()
      }, 52)
    })
  }
  const menuRouterPush = () => {
    for (let i = 0, len = tabData.value.length; i < len; i++) {
      const item = tabData.value[i]
      if (menuId.value === item.id) {
        sessionStorage.setItem('has', JSON.stringify(item))
        const menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath
        const getMicroName = routePath(menuUrl)
        isMicroApp.value = getMicroName
        if (MicroApp.value.includes(getMicroName) && !noMicroApp.value.includes(getMicroName)) {
          const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath), ...item.query }
          router.push({ path: item.routePath, query: query })
          MicroAppObj.value[getMicroName].mountPromise.then(() => {
            qiankunActions.setGlobalState({ keepAliveRoute: tabData.value.map((v) => v.routePath) })
          })
        } else {
          if (getMicroName === '/') {
            const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath), ...item.query }
            router.push({ path: item.routePath, query: query })
            const mainAppList = tabData.value.filter(
              (v) => routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/'
            )
            const mainAppName = Array.from(new Set(mainAppList.map((v) => mainRoutePath(v.routePath))))
            keepAliveRoute.value = mainAppName
          } else {
            router.push({ path: '/NotFoundPage' })
          }
        }
      }
    }
  }
  const mainRoutePath = (url) => {
    let path = ''
    const start = url.indexOf('/') + 1
    const end = url.indexOf('?')
    if (end === -1) {
      path = url.substring(1)
    } else {
      path = url.substring(start, end)
    }
    return path
  }
  const routePathData = (href) => {
    let params = {}
    href = href.substring(href.indexOf('?') + 1)
    let arr = href.split('&')
    arr.forEach((item) => {
      let a = item.split('=')
      params[a[0]] = a[1]
    })
    return params
  }
  // 获取第一个斜杠和第二个斜杠之间的内容
  const routePath = (url) => {
    let path = '' // 第二个斜杠前内容
    const first = url.indexOf('/') + 1 // 从第一个斜杠算起（+1表示不包括该斜杠）
    const kong = url.indexOf(' ', first) // 第一个斜杠后的第一个空格
    const heng = url.indexOf('/', first) // 第一个斜杠后的第一个斜杠（即第二个斜杠）
    if (heng === -1) {
      path = url.substring(1, kong)
    } else {
      path = url.substring(1, heng)
    }
    return path
  }
  watch(
    () => store.state.openRoute,
    (val) => {
      if (val.path) {
        openRoute(val)
      }
    },
    { immediate: true }
  )
  watch(
    () => store.state.closeOpenRoute,
    (val) => {
      if (val.closeId) {
        delRoute(val)
      }
    },
    { immediate: true }
  )

  const openRoute = (val) => {
    if (isTabData.value.map((v) => v.isData).includes(JSON.stringify(val))) {
      for (let i = 0, len = isTabData.value.length; i < len; i++) {
        const item = isTabData.value[i]
        if (item.isData === JSON.stringify(val)) {
          menuId.value = item.id
          tabClick()
        }
      }
    } else {
      const id = guid()
      isTabData.value.push({ id: id, isData: JSON.stringify(val) })
      tabData.value.push({
        id,
        name: val.name,
        routePath: val.path,
        query: { ...val.query, routeId: id, oldRouteId: menuId.value }
      })
      menuId.value = id
      tabClick()
    }
    qiankunActions.setGlobalState({ openRoute: { name: '', path: '', query: {} } })
  }
  const delRoute = (val) => {
    if (val.openId) {
      isTabData.value = isTabData.value.filter((item) => item.id !== val.closeId)
      tabData.value = tabData.value.filter((item) => item.id !== val.closeId)
      menuId.value = val.openId
      tabClick()
    } else {
      handleClose(val.closeId)
    }
    qiankunActions.setGlobalState({ closeOpenRoute: { openId: '', closeId: '' } })
  }
  const isRefresh = ref(true)
  const handleRefresh = (id) => {
    if (route.meta.moduleName === 'main') {
      keepAliveRoute.value = keepAliveRoute.value.filter((v) => v !== route.name)
      isRefresh.value = false
      setTimeout(() => {
        keepAliveRoute.value.push(route.name)
        isRefresh.value = true
      }, 200)
    } else {
      for (let i = 0, len = tabData.value.length; i < len; i++) {
        const item = tabData.value[i]
        if (item.id === id) {
          qiankunActions.setGlobalState({ refreshRoute: item.routePath })
          setTimeout(() => {
            qiankunActions.setGlobalState({ refreshRoute: '' })
          }, 222)
        }
      }
    }
  }
  const handleClose = (id) => {
    if (menuId.value === id) {
      for (let i = 0, len = tabData.value.length; i < len; i++) {
        const item = tabData.value[i]
        if (item.id === id) {
          menuId.value = tabData.value[i ? i - 1 : 1].id
          tabClick()
        }
      }
    }
    isTabData.value = isTabData.value.filter((item) => item.id !== id)
    tabData.value = tabData.value.filter((item) => item.id !== id)
  }
  const handleCloseOther = (id) => {
    isTabData.value = isTabData.value.filter((item) => item.id === id)
    tabData.value = tabData.value.filter((item) => item.id === id)
    menuId.value = id
    tabClick()
  }

  const openPageMenu = (nodes, key, value) => {
    if (!nodes || !nodes.length) return [] // eslint-disable-line
    const children = []
    for (let node of nodes) {
      if (node[key] === value && !openPageId.value) {
        openPageId.value = node.id
        openPageObj.value = node
      }
      node = Object.assign({}, node)
      const sub = openPageMenu(node.children, key, value)
      if ((sub && sub.length) || node[key] === value) {
        sub.length && (node.children = sub)
        children.push(node)
      }
    }
    return children.length ? children : [] // eslint-disable-line
  }
  const openPage = ({ key = 'id', value }) => {
    if (isOpen.value) return
    isOpen.value = true
    const openPagedata = openPageMenu(tabMenuData.value, key, value)[0] || {}
    if (openPagedata.id) {
      if (tabMenu.value === openPagedata.id) {
        if (openPagedata.routePath === '/WorkBench') {
          if (openPagedata.id === openPageId.value) {
            openPageId.value = ''
            openPageObj.value = {}
            WorkBenchReturn()
            return
          }
          if (openPagedata.children[0]?.id === childData.value.id) {
            menuId.value = openPageId.value
            menuClick(openPageObj.value)
            openPageId.value = ''
            openPageObj.value = {}
            nextTick(() => {
              if (isOpen.value) {
                isOpen.value = false
              }
            })
          } else {
            WorkBenchReturn()
            setTimeout(() => {
              nextTick(() => {
                openPageChildId.value = openPagedata.children[0]?.id
                handleClick()
                nextTick(() => {
                  if (isOpen.value) {
                    isOpen.value = false
                  }
                })
              })
            }, 200)
          }
        } else {
          if (openPagedata.routePath.includes('/GlobalHome')) {
            handleClick()
            nextTick(() => {
              if (isOpen.value) {
                isOpen.value = false
              }
            })
            return
          }
          menuId.value = openPageId.value
          menuClick(openPageObj.value)
          openPageId.value = ''
          openPageObj.value = {}
          nextTick(() => {
            if (isOpen.value) {
              isOpen.value = false
            }
          })
        }
      } else {
        if (isChildView.value) {
          WorkBenchReturn()
          setTimeout(() => {
            nextTick(() => {
              tabMenu.value = openPagedata.id
              if (openPagedata.id === openPageId.value) {
                openPageId.value = ''
                openPageObj.value = {}
              } else {
                openPageChildId.value = openPagedata.children[0]?.id
              }
              handleClick()
              nextTick(() => {
                if (isOpen.value) {
                  isOpen.value = false
                }
              })
            })
          }, 200)
        } else {
          tabMenu.value = openPagedata.id
          if (openPagedata.id === openPageId.value) {
            openPageId.value = ''
            openPageObj.value = {}
          } else {
            openPageChildId.value = openPagedata.children[0]?.id
          }
          handleClick()
          nextTick(() => {
            if (isOpen.value) {
              isOpen.value = false
            }
          })
        }
      }
    } else {
      isOpen.value = false
      ElMessage({ type: 'warning', message: '未检测到你有此菜单！' })
    }
  }
  const setOpenPageId = (id) => {
    openPageId.value = id
  }

  const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      var r = (Math.random() * 16) | 0,
        v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }

  onUnmounted(() => {
    for (let i = 0, len = MicroApp.value.length; i < len; i++) {
      const v = MicroApp.value[i]
      MicroAppObj.value[v].unmount()
      MicroAppObj.value[v].unmountPromise.then(() => {
        MicroAppObj.value[v] = null
      })
    }
    MicroApp.value = []
  })

  provide('WorkBenchList', WorkBenchList)
  provide('WorkBenchMenu', WorkBenchMenu)
  provide('leftMenuData', leftMenuData)
  provide('setOpenPageId', setOpenPageId)
  provide('openPage', openPage)
  provide('openRoute', openRoute)
  provide('delRoute', delRoute)
  provide('regionId', regionId)
  provide('regionSelect', regionSelect)
  provide('area', area)
  return {
    user, area, role, left, width, LayoutViewBox, LayoutViewInfo, helpShow, handleCommand,
    editPassWordShow, verifyEditPassWord, verifyEditPassWordShow, editPassWordCallback,
    regionId, regionName, regionSelect, isRegionSelectShow, isView, isChildView, tabMenu, tabMenuData, handleClick,
    menuId, menuData, menuClick, handleBreadcrumb, WorkBenchObj, WorkBenchList, childData, WorkBenchReturn,
    isRefresh, keepAliveRoute, tabData, tabClick, handleRefresh, handleClose, handleCloseOther,
    isMicroApp, MicroApp, openPage, leftMenuData
  }
}
export const qiankun = (route) => {
  const isMain = ref(false)
  const isMainPage = () => {
    isMain.value = route.meta.moduleName === 'main'
  }

  watch(
    () => route,
    () => {
      isMainPage()
    },
    { deep: true, immediate: true }
  )
  onMounted(() => {
    isMainPage(route)
  })
  return { isMain }
}

export const ChatMethod = () => {
  const store = useStore()
  const rongCloudToken = computed(() => store.getters.getRongCloudToken)
  return { rongCloudToken }
}

export const AiChatMethod = () => {
  const store = useStore()
  const AiChatWidth = computed(() => store.state.AiChatWidth)
  const AiChatTargetWidth = computed(() => `${AiChatWidth.value}px`)
  const AiChatViewType = ref(false)
  const AiChatWindowShow = ref(false)
  // 自动吸附到最近的侧边
  const handleResizeFloatingWindow = () => {
    if (window.innerWidth > 1280 + 400) {
      const width = window.innerWidth - 1280 > 520 ? 520 : 400
      store.commit('setAiChatWidth', width)
      if (!AiChatViewType.value) AiChatWindowShow.value = false
      AiChatViewType.value = true
    } else {
      store.commit('setAiChatWidth', 400)
      if (AiChatViewType.value) AiChatWindowShow.value = false
      AiChatViewType.value = false
    }
  }
  onMounted(() => {
    handleResizeFloatingWindow()
    window.addEventListener('resize', handleResizeFloatingWindow)
  })
  onUnmounted(() => {
    window.removeEventListener('resize', handleResizeFloatingWindow)
  })
  return { AiChatTargetWidth, AiChatViewType, AiChatWindowShow }
}

export const loginHintMethod = () => {
  const store = useStore()
  const user = computed(() => store.state.user)
  const openConfig = computed(() => store.getters.getReadOpenConfig)
  const systemPlatform = computed(() => openConfig.value?.systemPlatform)
  const loginHintShow = ref(false)
  const hasDuplicates = (arr1, arr2) => {
    const set = new Set(arr1)
    return arr2.some((item) => set.has(item))
  }
  const getLoginHintConfig = async () => {
    const { data } = await api.globalReadOpenConfig({ codes: ['loginPopShow'] })
    if (data.loginPopShow) {
      loginHintShow.value = data.loginPopShow == 'true'
    } else {
      loginHintShow.value = false
    }
  }
  onMounted(() => { })
  watch(
    () => user.value,
    () => {
      if (user.value.id) {
        const specialRoleKeys = user.value.specialRoleKeys || []
        const npcRoleKeys = ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member']
        const cppccRoleKeys = ['proposal_committee', 'suggestion_office_user', 'cppcc_member']
        const canSee = systemPlatform.value === 'CPPCC' ? cppccRoleKeys : npcRoleKeys
        loginHintShow.value = hasDuplicates(specialRoleKeys, canSee)
        if (loginHintShow.value) getLoginHintConfig()
      }
    },
    { immediate: true }
  )
  return { loginHintShow }
}
