{"ast": null, "code": "import { onActivated, ref } from 'vue';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { suggestExportWord } from '@/assets/js/suggestExportWord';\nvar __default__ = {\n  name: 'SuggestApplyForPostpone'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [{\n      id: 'exportWord',\n      name: '导出Word',\n      type: 'primary',\n      has: ''\n    }, {\n      id: 'export',\n      name: '导出Excel',\n      type: 'primary',\n      has: ''\n    }];\n    var _GlobalTable = GlobalTable({\n        tableId: 'id_prop_proposal_delayed',\n        tableApi: 'suggestionList'\n      }),\n      keyword = _GlobalTable.keyword,\n      queryRef = _GlobalTable.queryRef,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableHead = _GlobalTable.tableHead,\n      tableData = _GlobalTable.tableData,\n      exportId = _GlobalTable.exportId,\n      exportParams = _GlobalTable.exportParams,\n      exportShow = _GlobalTable.exportShow,\n      handleQuery = _GlobalTable.handleQuery,\n      handleSortChange = _GlobalTable.handleSortChange,\n      handleHeaderClass = _GlobalTable.handleHeaderClass,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      tableRefReset = _GlobalTable.tableRefReset,\n      handleGetParams = _GlobalTable.handleGetParams,\n      handleEditorCustom = _GlobalTable.handleEditorCustom,\n      handleExportExcel = _GlobalTable.handleExportExcel,\n      tableQuery = _GlobalTable.tableQuery;\n    onActivated(function () {\n      var suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'));\n      if (suggestIds) {\n        tableQuery.value.ids = suggestIds;\n        handleQuery();\n        setTimeout(function () {\n          sessionStorage.removeItem('suggestIds');\n          tableQuery.value.ids = [];\n        }, 1000);\n      } else {\n        tableQuery.value.queryDelayHistory = queryDelayHistory.value;\n        handleQuery();\n      }\n    });\n    var queryDelayHistory = ref('');\n    var handleLabel = function handleLabel(value) {\n      queryDelayHistory.value = value;\n      tableQuery.value.queryDelayHistory = value;\n      handleQuery();\n    };\n    var handleExcelData = function handleExcelData(_item) {\n      _item.forEach(function (v) {\n        if (!v.mainHandleOffices) {\n          v.mainHandleOffices = v.publishHandleOffices;\n        }\n      });\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'exportWord':\n          suggestExportWord(handleGetParams());\n          break;\n        case 'export':\n          handleExportExcel();\n          break;\n        default:\n          break;\n      }\n    };\n    var handleTableClick = function handleTableClick(key, row) {\n      switch (key) {\n        case 'details':\n          handleDetails(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleDetails = function handleDetails(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '提案详情',\n          path: '/proposal/SuggestDetail',\n          query: {\n            id: item.id,\n            type: 'postpone'\n          }\n        }\n      });\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      exportShow.value = false;\n    };\n    var __returned__ = {\n      buttonList,\n      keyword,\n      queryRef,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableHead,\n      tableData,\n      exportId,\n      exportParams,\n      exportShow,\n      handleQuery,\n      handleSortChange,\n      handleHeaderClass,\n      handleTableSelect,\n      tableRefReset,\n      handleGetParams,\n      handleEditorCustom,\n      handleExportExcel,\n      tableQuery,\n      queryDelayHistory,\n      handleLabel,\n      handleExcelData,\n      handleReset,\n      handleButton,\n      handleTableClick,\n      handleDetails,\n      callback,\n      onActivated,\n      ref,\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get suggestExportWord() {\n        return suggestExportWord;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["onActivated", "ref", "GlobalTable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestExportWord", "__default__", "name", "buttonList", "id", "type", "has", "_GlobalTable", "tableId", "tableApi", "keyword", "queryRef", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableHead", "tableData", "exportId", "exportParams", "exportShow", "handleQuery", "handleSortChange", "handleHeaderClass", "handleTableSelect", "tableRefReset", "handleGetParams", "handleEditorCustom", "handleExportExcel", "tableQuery", "suggestIds", "JSON", "parse", "sessionStorage", "getItem", "value", "ids", "setTimeout", "removeItem", "query<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleLabel", "handleExcelData", "_item", "for<PERSON>ach", "v", "mainHandleOffices", "publishHandleOffices", "handleReset", "handleButton", "isType", "handleTableClick", "key", "row", "handleDetails", "item", "setGlobalState", "openRoute", "path", "query", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestApplyForPostpone/SuggestApplyForPostpone.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestApplyForPostpone\">\r\n    <xyl-label v-model=\"queryDelayHistory\" @labelClick=\"handleLabel\">\r\n      <xyl-label-item :value=\"''\">待审查</xyl-label-item>\r\n      <xyl-label-item :value=\"'1'\">已审查</xyl-label-item>\r\n    </xyl-label>\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\" :data=\"tableHead\" ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\" @sort-change=\"handleSortChange\" :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\"\r\n          :noTooltip=\"['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\">\r\n          <template #mainHandleOffices=\"scope\">\r\n            <template v-if=\"scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0\">\r\n              {{scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}\r\n            </template>\r\n            <template v-else>\r\n              {{scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}\r\n            </template>\r\n          </template>\r\n          <template #assistHandleOffices=\"scope\">\r\n            <template v-if=\"scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0\">\r\n              {{scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}\r\n            </template>\r\n            <template v-else>\r\n              {{scope.row.assistHandleVoList?.map(v => v.flowHandleOfficeName).join('、')}}\r\n            </template>\r\n          </template>\r\n          <!-- <template #publishHandleOffices=\"scope\">\r\n            {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n          </template> -->\r\n        </xyl-global-table>\r\n        <xyl-global-table-button :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\" name=\"导出Excel\">\r\n      <xyl-export-excel name=\"申请延期提案\" :exportId=\"exportId\" :params=\"exportParams\" module=\"proposalExportExcel\"\r\n        tableId=\"id_prop_proposal_delayed\" @excelCallback=\"callback\"\r\n        :handleExcelData=\"handleExcelData\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestApplyForPostpone' }\r\n</script>\r\n<script setup>\r\nimport { onActivated, ref } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nconst buttonList = [\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n]\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel,\r\n  tableQuery\r\n} = GlobalTable({ tableId: 'id_prop_proposal_delayed', tableApi: 'suggestionList' })\r\n\r\nonActivated(() => {\r\n  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))\r\n  if (suggestIds) {\r\n    tableQuery.value.ids = suggestIds\r\n    handleQuery()\r\n    setTimeout(() => {\r\n      sessionStorage.removeItem('suggestIds')\r\n      tableQuery.value.ids = []\r\n    }, 1000)\r\n  } else {\r\n    tableQuery.value.queryDelayHistory = queryDelayHistory.value\r\n    handleQuery()\r\n  }\r\n})\r\n\r\nconst queryDelayHistory = ref('')\r\nconst handleLabel = (value) => {\r\n  queryDelayHistory.value = value\r\n  tableQuery.value.queryDelayHistory = value\r\n  handleQuery()\r\n}\r\nconst handleExcelData = (_item) => {\r\n  _item.forEach(v => {\r\n    if (!v.mainHandleOffices) {\r\n      v.mainHandleOffices = v.publishHandleOffices\r\n    }\r\n  })\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams())\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id, type: 'postpone' } }\r\n  })\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  exportShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestApplyForPostpone {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px + 60px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAyDA,SAASA,WAAW,EAAEC,GAAG,QAAQ,KAAK;AACtC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,+BAA+B;AANjE,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAA0B,CAAC;;;;;IAOlD,IAAMC,UAAU,GAAG,CACjB;MAAEC,EAAE,EAAE,YAAY;MAAEF,IAAI,EAAE,QAAQ;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,EAC9D;MAAEF,EAAE,EAAE,QAAQ;MAAEF,IAAI,EAAE,SAAS;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,CAC5D;IACD,IAAAC,YAAA,GAsBIT,WAAW,CAAC;QAAEU,OAAO,EAAE,0BAA0B;QAAEC,QAAQ,EAAE;MAAiB,CAAC,CAAC;MArBlFC,OAAO,GAAAH,YAAA,CAAPG,OAAO;MACPC,QAAQ,GAAAJ,YAAA,CAARI,QAAQ;MACRC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MACRC,MAAM,GAAAN,YAAA,CAANM,MAAM;MACNC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,QAAQ,GAAAR,YAAA,CAARQ,QAAQ;MACRC,SAAS,GAAAT,YAAA,CAATS,SAAS;MACTC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,SAAS,GAAAX,YAAA,CAATW,SAAS;MACTC,QAAQ,GAAAZ,YAAA,CAARY,QAAQ;MACRC,YAAY,GAAAb,YAAA,CAAZa,YAAY;MACZC,UAAU,GAAAd,YAAA,CAAVc,UAAU;MACVC,WAAW,GAAAf,YAAA,CAAXe,WAAW;MACXC,gBAAgB,GAAAhB,YAAA,CAAhBgB,gBAAgB;MAChBC,iBAAiB,GAAAjB,YAAA,CAAjBiB,iBAAiB;MACjBC,iBAAiB,GAAAlB,YAAA,CAAjBkB,iBAAiB;MACjBC,aAAa,GAAAnB,YAAA,CAAbmB,aAAa;MACbC,eAAe,GAAApB,YAAA,CAAfoB,eAAe;MACfC,kBAAkB,GAAArB,YAAA,CAAlBqB,kBAAkB;MAClBC,iBAAiB,GAAAtB,YAAA,CAAjBsB,iBAAiB;MACjBC,UAAU,GAAAvB,YAAA,CAAVuB,UAAU;IAGZlC,WAAW,CAAC,YAAM;MAChB,IAAMmC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;MACnE,IAAIJ,UAAU,EAAE;QACdD,UAAU,CAACM,KAAK,CAACC,GAAG,GAAGN,UAAU;QACjCT,WAAW,CAAC,CAAC;QACbgB,UAAU,CAAC,YAAM;UACfJ,cAAc,CAACK,UAAU,CAAC,YAAY,CAAC;UACvCT,UAAU,CAACM,KAAK,CAACC,GAAG,GAAG,EAAE;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLP,UAAU,CAACM,KAAK,CAACI,iBAAiB,GAAGA,iBAAiB,CAACJ,KAAK;QAC5Dd,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IAEF,IAAMkB,iBAAiB,GAAG3C,GAAG,CAAC,EAAE,CAAC;IACjC,IAAM4C,WAAW,GAAG,SAAdA,WAAWA,CAAIL,KAAK,EAAK;MAC7BI,iBAAiB,CAACJ,KAAK,GAAGA,KAAK;MAC/BN,UAAU,CAACM,KAAK,CAACI,iBAAiB,GAAGJ,KAAK;MAC1Cd,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMoB,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;MACjCA,KAAK,CAACC,OAAO,CAAC,UAAAC,CAAC,EAAI;QACjB,IAAI,CAACA,CAAC,CAACC,iBAAiB,EAAE;UACxBD,CAAC,CAACC,iBAAiB,GAAGD,CAAC,CAACE,oBAAoB;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBtC,OAAO,CAAC0B,KAAK,GAAG,EAAE;MAClBd,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAM2B,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,YAAY;UACflD,iBAAiB,CAAC2B,eAAe,CAAC,CAAC,CAAC;UACpC;QACF,KAAK,QAAQ;UACXE,iBAAiB,CAAC,CAAC;UACnB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMsB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,GAAG,EAAEC,GAAG,EAAK;MACrC,QAAQD,GAAG;QACT,KAAK,SAAS;UACZE,aAAa,CAACD,GAAG,CAAC;UAClB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9BxD,YAAY,CAACyD,cAAc,CAAC;QAC1BC,SAAS,EAAE;UAAEvD,IAAI,EAAE,MAAM;UAAEwD,IAAI,EAAE,yBAAyB;UAAEC,KAAK,EAAE;YAAEvD,EAAE,EAAEmD,IAAI,CAACnD,EAAE;YAAEC,IAAI,EAAE;UAAW;QAAE;MACvG,CAAC,CAAC;IACJ,CAAC;IACD,IAAMuD,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBlC,aAAa,CAAC,CAAC;MACfJ,WAAW,CAAC,CAAC;MACbD,UAAU,CAACe,KAAK,GAAG,KAAK;IAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}