<template>
  <div class="global-dynamic-title">
    <div class="global-dynamic-name">{{ title }}
      <template v-if="hasPermission('dynamic_title') && !props.disabled">
        <el-tooltip effect="dark"
                    placement="top"
                    v-if="!show"
                    content="点击修改表头">
          <span class="global-dynamic-icon"
                @click="handleEdit"></span>
        </el-tooltip>
        <el-tooltip effect="dark"
                    placement="top"
                    v-if="show"
                    content="点击确定修改">
          <span class="global-dynamic-determine"
                @click="dynamicTextEdit">
            <el-icon>
              <Finished />
            </el-icon>
          </span>
        </el-tooltip>
      </template>
    </div>
    <div class="global-dynamic-input-list"
         v-if="show">
      <GlobalDynamicInput v-for="item in formArr"
                          :key="item.id"
                          v-model="item.text"
                          :disabled="item.disabled" />
    </div>
  </div>
</template>
<script>
export default { name: 'DynamicTitle' }
</script>
<script setup>
import api from '@/api'
import { ref, watch } from 'vue'
import { hasPermission } from 'common/js/permissions'
import GlobalDynamicInput from './global-dynamic-input.vue'
import { ElMessage } from 'element-plus'
const props = defineProps({
  templateCode: { type: String, default: '' },
  params: { type: Object, default: () => ({}) },
  disabled: { type: Boolean, default: false },
  titles: { type: String, default: '' }
})
const emit = defineEmits(['callback'])

const id = ref('')
const title = ref('')
const show = ref(false)
const formArr = ref([])

const dynamicTextByCode = async () => {
  const { data } = await api.dynamicTextByCode({ templateCode: props.templateCode, ...props.params })
  if (props.titles) {
    title.value = props.titles
  } else {
    title.value = data
  }
  emit('callback', data)
}
const handleEdit = () => {
  show.value = true
  dynamicTextInfo()
}
const splitFunc = (array, label) => {
  const outArray = []
  for (let i = 0; i < array.length; i++) {
    let newArr = []
    const splitArr = array[i].split(label)
    for (let j = 0; j < splitArr.length; j++) {
      newArr.push(splitArr[j])
      if (j < splitArr.length - 1) {
        newArr.push(label)
      }
    }
    outArray.push(newArr)
  }
  return outArray
}
const dynamicTextInfo = async () => {
  const { data } = await api.dynamicTextInfo({ templateCode: props.templateCode, ...props.params })
  formArr.value = []
  id.value = data.id
  let array = [data.templateContent]
  const labels = data.templateContent.match(/\{[^\}]+\}/g) // eslint-disable-line
  for (let i = 0; i < labels.length; i++) {
    const twoArray = splitFunc(array, labels[i])
    const oneArray = twoArray.reduce(function (a, b) { return a.concat(b) })
    array = oneArray.filter(item => item !== '')
  }
  var is = true
  for (let i = 0; i < array.length; i++) {
    if (array[i].match(/\{[^\}]+\}/g)) { // eslint-disable-line
      if (is) {
        formArr.value.push({ id: formArr.value.length + '', text: '', disabled: false })
      }
      is = true
      formArr.value.push({ id: formArr.value.length + '', text: array[i], disabled: true })
      if (i === array.length - 1) {
        formArr.value.push({ id: formArr.value.length + '', text: '', disabled: false })
      }
    } else {
      is = false
      formArr.value.push({ id: formArr.value.length + '', text: array[i], disabled: false })
    }
  }
}
const dynamicTextEdit = async () => {
  var templateContent = ''
  for (let index = 0; index < formArr.value.length; index++) {
    const item = formArr.value[index]
    templateContent += item.text
  }
  const { code } = await api.dynamicTextEdit({
    form: {
      id: id.value,
      templateCode: props.templateCode,
      templateContent: templateContent,
      ...props.params
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '编辑成功' })
    dynamicTextByCode()
    show.value = false
  }
}
watch(() => props.params, () => {
  dynamicTextByCode()
  dynamicTextInfo()
}, { immediate: true })
</script>
<style lang="scss">
.global-dynamic-title {
  width: 100%;
  border-bottom: 2px solid var(--zy-el-color-primary);

  .global-dynamic-name {
    position: relative;
    padding: 20px 40px;
    font-weight: bold;
    text-align: center;
    color: var(--zy-el-color-primary);
    font-size: var(--zy-title-font-size);
    line-height: var(--zy-line-height);

    .global-dynamic-icon {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      background: url("./img/global_dynamic_title_edit.png") no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }

    .global-dynamic-determine {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      font-size: 26px;
      display: flex;
      align-items: center;
      color: var(--zy-el-text-color-regular);
    }
  }

  .global-dynamic-input-list {
    display: flex;
    justify-content: center;
    padding-bottom: 20px;

    .zy-el-input__wrapper {
      border-radius: 0;
    }
  }
}
</style>
