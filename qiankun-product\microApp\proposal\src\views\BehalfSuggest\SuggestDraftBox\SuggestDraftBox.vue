<template>
  <div class="SuggestDraftBox">
    <xyl-search-button
      @queryClick="handleQuery"
      @resetClick="handleReset"
      @handleButton="handleButton"
      :buttonList="buttonList"
      :data="tableHead"
      ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table
        ref="tableRef"
        row-key="id"
        :data="tableData"
        @select="handleTableSelect"
        @select-all="handleTableSelect"
        @sort-change="handleSortChange"
        :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"></xyl-global-table>
        <xyl-global-table-button
          :data="tableButtonList"
          @buttonClick="handleCommand"
          :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel
        name="提案草稿箱"
        :exportId="exportId"
        :params="exportParams"
        module="proposalExportExcel"
        tableId="id_prop_proposal_saveDraft"
        @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestDraftBox' }
</script>
<script setup>
import api from '@/api'
import { onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
const buttonList = [
  { id: 'next', name: '批量提交', type: 'primary', has: '' },
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' },
  { id: 'del', name: '删除', type: 'primary', has: '' }
]
const tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: '' }]
const route = useRoute()
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  handleDel,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: 'id_prop_proposal_saveDraft', tableApi: 'suggestionList', delApi: 'suggestionDel' })

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    handleQuery()
  }
})
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'next':
      handleNext()
      break
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    case 'del':
      handleDel('提案')
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } }
  })
}
const handleEdit = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '编辑提案', path: '/proposal/SubmitSuggest', query: { id: item.id, type: 'draft' } }
  })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
}
const handleNext = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作会将当前选中的草稿提案提交, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionBatchComplete()
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消提交' })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionBatchComplete = async () => {
  const { code } = await api.suggestionBatchComplete({
    suggestionIds: tableDataArray.value.map((v) => v.id),
    nextNodeId: route.query.nextNode || 'prepareVerify'
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '提交成功' })
    tableRefReset()
    handleQuery()
  }
}
</script>
<style lang="scss">
.SuggestDraftBox {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
