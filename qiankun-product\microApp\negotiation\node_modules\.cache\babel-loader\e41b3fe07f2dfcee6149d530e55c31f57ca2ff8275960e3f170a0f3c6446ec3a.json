{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n// 导入封装的方法\nimport HTTP from 'common/http';\nimport GlobalApi from 'common/http/GlobalApi';\nvar api = _objectSpread(_objectSpread({}, GlobalApi), {}, {\n  userBatch(params) {\n    // 用户批量操作\n    return HTTP.json('/user/batch', params);\n  },\n  consultUserList(params) {\n    // 相关单位管理员列表\n    return HTTP.json('/consultUser/list', params);\n  },\n  consultUserInfo(params) {\n    // 相关单位管理员详情\n    return HTTP.json('/consultUser/info', params);\n  },\n  consultUserDel(params) {\n    // 相关单位管理员删除\n    return HTTP.json('/consultUser/dels', params);\n  },\n  consultCompanyList(params) {\n    // 相关单位管理列表\n    return HTTP.json('/consultCompany/list', params);\n  },\n  consultCompanyInfo(params) {\n    // 相关单位管理详情\n    return HTTP.json('/consultCompany/info', params);\n  },\n  consultCompanyDel(params) {\n    // 相关单位管理删除\n    return HTTP.json('/consultCompany/dels', params);\n  },\n  consultCompanyUsing(params) {\n    // 相关单位管理启用\n    return HTTP.json('/consultCompany/using', params);\n  },\n  consultCompanyUnUsing(params) {\n    // 相关单位管理禁用\n    return HTTP.json('/consultCompany/unUsing', params);\n  },\n  consultCompanySelect(params) {\n    // 相关单位管理下拉选\n    return HTTP.json('/consultCompany/select', params);\n  },\n  consultShapeList(params) {\n    // 协商形式列表\n    return HTTP.json('/consultShape/list', params);\n  },\n  consultShapeInfo(params) {\n    // 协商形式详情\n    return HTTP.json('/consultShape/info', params);\n  },\n  consultShapeDel(params) {\n    // 协商形式删除\n    return HTTP.json('/consultShape/dels', params);\n  },\n  consultShapeSelect(params) {\n    // 协商形式下拉选\n    return HTTP.json('/consultShape/select', params);\n  },\n  consultPlanLoadFormData(params) {\n    // 协商形式下拉选单位动态表头\n    return HTTP.json('/consultPlan/loadFormData', params);\n  },\n  consultPlanList(params) {\n    // 协商计划列表\n    return HTTP.json('/consultPlan/list', params);\n  },\n  consultPlanInfo(params) {\n    // 协商计划详情\n    return HTTP.json('/consultPlan/info', params);\n  },\n  consultPlanDel(params) {\n    // 协商计划删除\n    return HTTP.json('/consultPlan/dels', params);\n  },\n  consultPlanSelect(params) {\n    // 协商计划下拉选\n    return HTTP.json('/consultPlan/select', params);\n  },\n  consultActivityList(params) {\n    // 协商活动列表\n    return HTTP.json('/consultActivity/list', params);\n  },\n  consultActivityInfo(params) {\n    // 协商活动详情\n    return HTTP.json('/consultActivity/info', params);\n  },\n  consultActivityDel(params) {\n    // 协商活动删除\n    return HTTP.json('/consultActivity/dels', params);\n  },\n  consultActivityVariableList(params) {\n    // 协商活动流程节点列表\n    return HTTP.json('/consultActivityVariable/list', params);\n  },\n  consultActivityVariableInfo(params) {\n    // 协商活动流程节点详情\n    return HTTP.json('/consultActivityVariable/info', params);\n  },\n  consultActivityVariableDel(params) {\n    // 协商活动流程节点删除\n    return HTTP.json('/consultActivityVariable/dels', params);\n  },\n  consultRegulationBannerTree(params) {\n    // 协商制度栏目列表\n    return HTTP.json('/consultRegulationBanner/tree', params);\n  },\n  consultRegulationBannerInfo(params) {\n    // 协商制度栏目详情\n    return HTTP.json('/consultRegulationBanner/info', params);\n  },\n  consultRegulationBannerDel(params) {\n    // 协商制度栏目删除\n    return HTTP.json('/consultRegulationBanner/dels', params);\n  },\n  consultActivityConferenceList(params) {\n    // 远程协商列表\n    return HTTP.json('/consultActivityConference/list', params);\n  },\n  consultActivityConferenceInfo(params) {\n    // 远程协商详情\n    return HTTP.json('/consultActivityConference/info', params);\n  },\n  consultActivityConferenceDel(params) {\n    // 远程协商删除\n    return HTTP.json('/consultActivityConference/dels', params);\n  },\n  consultActivityConferenceUploadList(params) {\n    // 远程协商回放列表\n    return HTTP.json('/consultActivityConference/uploadList', params);\n  },\n  consultActivityConferenceUpload(params) {\n    // 远程协商回放新增\n    return HTTP.json('/consultActivityConference/upload', params);\n  },\n  consultActivityConferenceDelUpload(params) {\n    // 远程协商回放删除\n    return HTTP.json('/consultActivityConference/delUpload', params);\n  },\n  consultRegulationList(params) {\n    // 协商制度列表\n    return HTTP.json('/consultRegulation/list', params);\n  },\n  consultRegulationInfo(params) {\n    // 协商制度详情\n    return HTTP.json('/consultRegulation/info', params);\n  },\n  consultRegulationDel(params) {\n    // 协商制度删除\n    return HTTP.json('/consultRegulation/dels', params);\n  },\n  opinioncollectList(params) {\n    // 会议发言所有列表\n    return HTTP.json('/conferenceSpeaking/list', params);\n  },\n  opinioncollectManageList(params) {\n    // 会议发言管理列表\n    return HTTP.json('/conferenceSpeaking/managelist', params);\n  },\n  opinioncollectAdd(params) {\n    // 新增会议发言\n    return HTTP.json('/conferenceSpeaking/add', params);\n  },\n  opinioncollectDels(params) {\n    // 删除会议发言\n    return HTTP.json('/conferenceSpeaking/dels', params);\n  },\n  opinioncollectEdit(params) {\n    // 修改会议发言\n    return HTTP.json('/conferenceSpeaking/edit', params);\n  },\n  opinioncollectInfo(params) {\n    // 会议发言详情\n    return HTTP.json('/conferenceSpeaking/info', params);\n  },\n  opinioncollectPublish(params) {\n    // 会议发言发布\n    return HTTP.json('/conferenceSpeaking/publish', params);\n  },\n  opinioncollectIsTop(params) {\n    // 会议发言置顶\n    return HTTP.json('/conferenceSpeaking/istop', params);\n  },\n  commentAdd(params) {\n    // 会议发言提报\n    return HTTP.json('/comment/add', params);\n  },\n  commentCheckPass(params) {\n    // 评论审核通过\n    return HTTP.json('/comment/check/pass', params);\n  },\n  commentCheckNoPass(params) {\n    // 评论审核不通过\n    return HTTP.json('/comment/check/nopass', params);\n  },\n  opinioncollectUseroffice(params) {\n    // 查询本用户下的发布单位\n    return HTTP.json('/conferenceSpeaking/useroffice', params);\n  },\n  activityTypeSelect(params) {\n    // 活动类型下拉选\n    return HTTP.json('/activitytype/selecttree', params);\n  },\n  activityList(params) {\n    // 活动列表\n    return HTTP.json('/servantactivity/list', params);\n  },\n  activityInfo(params) {\n    // 活动详情\n    return HTTP.json('/servantactivity/info', params);\n  },\n  activityDel(params) {\n    // 活动删除\n    return HTTP.json('/servantactivity/dels', params);\n  },\n  activitydocList(params) {\n    // 活动材料列表\n    return HTTP.json('/activitydoc/managelist', params);\n  },\n  activitydocOpenList(params) {\n    // 活动材料公开列表\n    return HTTP.json('/activitydoc/list', params);\n  },\n  activitydocInfo(params) {\n    // 活动材料详情\n    return HTTP.json('/activitydoc/info', params);\n  },\n  activitydocDel(params) {\n    // 活动材料删除\n    return HTTP.json('/activitydoc/dels', params);\n  },\n  activitydocIsPublic(params) {\n    // 活动材料公开\n    return HTTP.json('/activitydoc/ispublic', params);\n  },\n  userJoin(params) {\n    // 推荐用户\n    return HTTP.json('/common/activityRecommendation', params, {\n      noErrorTip: true\n    });\n  },\n  videoConnectionList(params) {\n    // 视频会议列表\n    return HTTP.json('/videoConnection/list', params);\n  },\n  videoConnectionInfo(params) {\n    // 视频会议详情\n    return HTTP.json('/videoConnection/info', params);\n  },\n  videoConnectionDel(params) {\n    // 视频会议删除\n    return HTTP.json('/videoConnection/dels', params);\n  },\n  networkDiscussionsList(params) {\n    // 网络议政管理列表\n    return HTTP.json('/opinioncollect/managelist', params);\n  },\n  networkDiscussionsAdd(params) {\n    // 新增网络议政\n    return HTTP.json('/opinioncollect/add', params);\n  },\n  networkDiscussionsDels(params) {\n    // 删除网络议政\n    return HTTP.json('/opinioncollect/dels', params);\n  },\n  networkDiscussionsEdit(params) {\n    // 修改网络议政\n    return HTTP.json('/opinioncollect/edit', params);\n  },\n  networkDiscussionsInfo(params) {\n    // 网络议政详情\n    return HTTP.json('/opinioncollect/info', params);\n  },\n  networkDiscussionsPublish(params) {\n    // 网络议政发布\n    return HTTP.json('/opinioncollect/publish', params);\n  },\n  networkDiscussionsIsTop(params) {\n    // 网络议政置顶\n    return HTTP.json('/opinioncollect/istop', params);\n  },\n  networkDiscussionsUseroffice(params) {\n    // 查询本用户下的发布单位\n    return HTTP.json('/opinioncollect/useroffice', params);\n  },\n  longShortLinkExchange(url) {\n    // 长链接转短链接\n    return HTTP.get(`/longShortLink/exchange?longUrl=${url}`);\n  },\n  feedbackList(params) {\n    // 反馈记录列表\n    return HTTP.json('/feedback/list', params);\n  },\n  feedbackAdd(params) {\n    // 新增反馈记录\n    return HTTP.json('/feedback/add', params);\n  },\n  feedbackInfo(params) {\n    // 反馈记录详情\n    return HTTP.json('/feedback/info', params);\n  },\n  feedbackDels(params) {\n    // 删除反馈记录\n    return HTTP.json('/feedback/dels', params);\n  },\n  feedbackEdit(params) {\n    // 编辑反馈记录\n    return HTTP.json('/feedback/edit', params);\n  },\n  studypaperList(params) {\n    // 问卷调查列表\n    return HTTP.json('studypaper/managelist', params);\n  },\n  studypaperInfo(params) {\n    // 问卷调查详情\n    return HTTP.json('/studypaper/info', params);\n  },\n  studypaperDel(params) {\n    // 问卷调查删除\n    return HTTP.json('/studypaper/dels', params);\n  },\n  studypaperDetailquestion(params) {\n    // 结果统计\n    return HTTP.json('/studypaper/detailquestion', params);\n  },\n  studypaperRevoke(params) {\n    // 问卷调查撤回\n    return HTTP.json('/studypaper/revoke', params);\n  },\n  studyexamineList(params) {\n    // 参与情况\n    return HTTP.json('/studyexamine/list', params);\n  },\n  studypaperDetail(params) {\n    // 考试详情\n    return HTTP.json('/studypaper/detail', params);\n  },\n  studyexamineResult(params) {\n    // 考试结果\n    return HTTP.json('/studyexamine/result', params);\n  },\n  // studypaperExampaper (params) { // 考试详情\n  //   return HTTP.json('/studypaper/exampaper', params)\n  // },\n\n  // studypaperPublish (params) { // 考试发布\n  //   return HTTP.json('/studypaper/publish', params)\n  // },\n\n  // studypaperTotal (params) { // 考试统计\n  //   return HTTP.json('/studypaper/total', params)\n  // },\n  studytypeList(params) {\n    // 题库类型列表\n    return HTTP.json('/studytype/list', params);\n  },\n  studytypeInfo(params) {\n    // 题库类型详情\n    return HTTP.json('/studytype/info', params);\n  },\n  studytypeDel(params) {\n    // 题库类型删除\n    return HTTP.json('/studytype/dels', params);\n  },\n  studytopicList(params) {\n    // 题库列表\n    return HTTP.json('/studytopic/list', params);\n  },\n  studytopicInfo(params) {\n    // 题库详情\n    return HTTP.json('/studytopic/info', params);\n  },\n  studytopicDel(params) {\n    // 题库删除\n    return HTTP.json('/studytopic/dels', params);\n  },\n  studytopicMove(params) {\n    // 题库移动\n    return HTTP.json('/studytopic/move', params);\n  },\n  studytopicType(params) {\n    // 题库题型字典\n    return HTTP.json('/studytopic/type', params);\n  },\n  studypapertopicList(params) {\n    // 题库导入列表查询\n    return HTTP.json('/studypapertopic/list', params);\n  },\n  studypapertopicInfo(params) {\n    // 题库导入详情\n    return HTTP.json('/studypapertopic/info', params);\n  },\n  studypapertopicImport(params) {\n    // 题库导入生成新id\n    return HTTP.json('/studypapertopic/import', params);\n  },\n  studypapertopicDel(params) {\n    // 题库导入删除\n    return HTTP.json('/studypapertopic/dels', params);\n  },\n  studypapertopicMytopics(params) {\n    // 任务发布\n    return HTTP.json('/studypapertopic/mytopics', params);\n  },\n  microAdviceMyListCount(params) {\n    // 我的成果统计\n    return HTTP.json('/microAdvice/myListCount', params);\n  },\n  microAdviceInfo(params) {\n    // 成果详情\n    return HTTP.json('/microAdvice/info', params);\n  },\n  microAdviceMyList(params) {\n    // 我的成果管理列表\n    return HTTP.json('/microAdvice/myList', params);\n  },\n  microAdviceDels(params) {\n    // 成果删除\n    return HTTP.json('/microAdvice/dels', params);\n  },\n  microAdviceList(params) {\n    // 成果管理列表\n    return HTTP.json('/microAdvice/list', params);\n  },\n  microAdviceGroupList(params) {\n    // 成果办理列表\n    return HTTP.json('/microAdvice/groupList', params);\n  },\n  microAdviceGroupListCount(params) {\n    // 成果办理统计\n    return HTTP.json('/microAdvice/groupListCount', params);\n  },\n  complete(params) {\n    // 协商成果审核\n    return HTTP.json('/microadviceflow/complete', params);\n  },\n  microAdviceGroupSelector(params) {\n    // 办理单位\n    return HTTP.json('/microAdvice/group/selector', params);\n  },\n  microAdviceUnderAreaSelector(params) {\n    // 下级地区\n    return HTTP.json('/microAdvice/underArea/selector', params);\n  },\n  manageJointly(params) {\n    // 协办\n    return HTTP.json('/microAdvice/manageJointly', params);\n  },\n  microFlowRecordEdit(params) {\n    // 回复编辑\n    return HTTP.json('/microFlowRecord/edit', params);\n  }\n});\nexport default api;", "map": {"version": 3, "names": ["HTTP", "GlobalApi", "api", "_objectSpread", "userBatch", "params", "json", "consultUserList", "consultUserInfo", "consultUserDel", "consultCompanyList", "consultCompanyInfo", "consultCompanyDel", "consultCompanyUsing", "consultCompanyUnUsing", "consultCompanySelect", "consultShapeList", "consultShapeInfo", "consultShapeDel", "consultShapeSelect", "consultPlanLoadFormData", "consultPlanList", "consultPlanInfo", "consultPlanDel", "consultPlanSelect", "consultActivityList", "consultActivityInfo", "consultActivityDel", "consultActivityVariableList", "consultActivityVariableInfo", "consultActivityVariableDel", "consultRegulationBannerTree", "consultRegulationBannerInfo", "consultRegulationBannerDel", "consultActivityConferenceList", "consultActivityConferenceInfo", "consultActivityConferenceDel", "consultActivityConferenceUploadList", "consultActivityConferenceUpload", "consultActivityConferenceDelUpload", "consultRegulationList", "consultRegulationInfo", "consultRegulationDel", "opinioncollectList", "opinioncollectManageList", "opinioncollectAdd", "opinioncollectDels", "opinioncollectEdit", "opinioncollectInfo", "opinioncollectPublish", "opinioncollectIsTop", "commentAdd", "commentCheckPass", "commentCheckNoPass", "opinioncollectUseroffice", "activityTypeSelect", "activityList", "activityInfo", "activityDel", "activitydocList", "activitydocOpenList", "activitydocInfo", "activitydocDel", "activitydocIsPublic", "userJoin", "noErrorTip", "videoConnectionList", "videoConnectionInfo", "videoConnectionDel", "networkDiscussionsList", "networkDiscussionsAdd", "networkDiscussionsDels", "networkDiscussionsEdit", "networkDiscussionsInfo", "networkDiscussionsPublish", "networkDiscussionsIsTop", "networkDiscussionsUseroffice", "longShortLinkExchange", "url", "get", "feedbackList", "feedbackAdd", "feedbackInfo", "feedbackDels", "feedbackEdit", "studypaperList", "studypaperInfo", "studypaperDel", "studypaperDetailquestion", "studypaperRevoke", "studyexamineList", "studypaperDetail", "studyexamineResult", "studytypeList", "studytypeInfo", "studytypeDel", "studytopicList", "studytopicInfo", "studytopicDel", "studytopicMove", "studytopicType", "studypapertopicList", "studypapertopicInfo", "studypapertopicImport", "studypapertopicDel", "studypapertopicMytopics", "microAdviceMyListCount", "microAdviceInfo", "microAdviceMyList", "microAdviceDels", "microAdviceList", "microAdviceGroupList", "microAdviceGroupListCount", "complete", "microAdviceGroupSelector", "microAdviceUnderAreaSelector", "manageJointly", "microFlowRecordEdit"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/negotiation/src/api/index.js"], "sourcesContent": ["// 导入封装的方法\r\nimport HTTP from 'common/http'\r\nimport GlobalApi from 'common/http/GlobalApi'\r\nconst api = {\r\n  ...GlobalApi,\r\n  userBatch (params) { // 用户批量操作\r\n    return HTTP.json('/user/batch', params)\r\n  },\r\n  consultUserList (params) { // 相关单位管理员列表\r\n    return HTTP.json('/consultUser/list', params)\r\n  },\r\n  consultUserInfo (params) { // 相关单位管理员详情\r\n    return HTTP.json('/consultUser/info', params)\r\n  },\r\n  consultUserDel (params) { // 相关单位管理员删除\r\n    return HTTP.json('/consultUser/dels', params)\r\n  },\r\n  consultCompanyList (params) { // 相关单位管理列表\r\n    return HTTP.json('/consultCompany/list', params)\r\n  },\r\n  consultCompanyInfo (params) { // 相关单位管理详情\r\n    return HTTP.json('/consultCompany/info', params)\r\n  },\r\n  consultCompanyDel (params) { // 相关单位管理删除\r\n    return HTTP.json('/consultCompany/dels', params)\r\n  },\r\n  consultCompanyUsing (params) { // 相关单位管理启用\r\n    return HTTP.json('/consultCompany/using', params)\r\n  },\r\n  consultCompanyUnUsing (params) { // 相关单位管理禁用\r\n    return HTTP.json('/consultCompany/unUsing', params)\r\n  },\r\n  consultCompanySelect (params) { // 相关单位管理下拉选\r\n    return HTTP.json('/consultCompany/select', params)\r\n  },\r\n  consultShapeList (params) { // 协商形式列表\r\n    return HTTP.json('/consultShape/list', params)\r\n  },\r\n  consultShapeInfo (params) { // 协商形式详情\r\n    return HTTP.json('/consultShape/info', params)\r\n  },\r\n  consultShapeDel (params) { // 协商形式删除\r\n    return HTTP.json('/consultShape/dels', params)\r\n  },\r\n  consultShapeSelect (params) { // 协商形式下拉选\r\n    return HTTP.json('/consultShape/select', params)\r\n  },\r\n  consultPlanLoadFormData (params) { // 协商形式下拉选单位动态表头\r\n    return HTTP.json('/consultPlan/loadFormData', params)\r\n  },\r\n  consultPlanList (params) { // 协商计划列表\r\n    return HTTP.json('/consultPlan/list', params)\r\n  },\r\n  consultPlanInfo (params) { // 协商计划详情\r\n    return HTTP.json('/consultPlan/info', params)\r\n  },\r\n  consultPlanDel (params) { // 协商计划删除\r\n    return HTTP.json('/consultPlan/dels', params)\r\n  },\r\n  consultPlanSelect (params) { // 协商计划下拉选\r\n    return HTTP.json('/consultPlan/select', params)\r\n  },\r\n  consultActivityList (params) { // 协商活动列表\r\n    return HTTP.json('/consultActivity/list', params)\r\n  },\r\n  consultActivityInfo (params) { // 协商活动详情\r\n    return HTTP.json('/consultActivity/info', params)\r\n  },\r\n  consultActivityDel (params) { // 协商活动删除\r\n    return HTTP.json('/consultActivity/dels', params)\r\n  },\r\n  consultActivityVariableList (params) { // 协商活动流程节点列表\r\n    return HTTP.json('/consultActivityVariable/list', params)\r\n  },\r\n  consultActivityVariableInfo (params) { // 协商活动流程节点详情\r\n    return HTTP.json('/consultActivityVariable/info', params)\r\n  },\r\n  consultActivityVariableDel (params) { // 协商活动流程节点删除\r\n    return HTTP.json('/consultActivityVariable/dels', params)\r\n  },\r\n  consultRegulationBannerTree (params) { // 协商制度栏目列表\r\n    return HTTP.json('/consultRegulationBanner/tree', params)\r\n  },\r\n  consultRegulationBannerInfo (params) { // 协商制度栏目详情\r\n    return HTTP.json('/consultRegulationBanner/info', params)\r\n  },\r\n  consultRegulationBannerDel (params) { // 协商制度栏目删除\r\n    return HTTP.json('/consultRegulationBanner/dels', params)\r\n  },\r\n  consultActivityConferenceList (params) { // 远程协商列表\r\n    return HTTP.json('/consultActivityConference/list', params)\r\n  },\r\n  consultActivityConferenceInfo (params) { // 远程协商详情\r\n    return HTTP.json('/consultActivityConference/info', params)\r\n  },\r\n  consultActivityConferenceDel (params) { // 远程协商删除\r\n    return HTTP.json('/consultActivityConference/dels', params)\r\n  },\r\n  consultActivityConferenceUploadList (params) { // 远程协商回放列表\r\n    return HTTP.json('/consultActivityConference/uploadList', params)\r\n  },\r\n  consultActivityConferenceUpload (params) { // 远程协商回放新增\r\n    return HTTP.json('/consultActivityConference/upload', params)\r\n  },\r\n  consultActivityConferenceDelUpload (params) { // 远程协商回放删除\r\n    return HTTP.json('/consultActivityConference/delUpload', params)\r\n  },\r\n  consultRegulationList (params) { // 协商制度列表\r\n    return HTTP.json('/consultRegulation/list', params)\r\n  },\r\n  consultRegulationInfo (params) { // 协商制度详情\r\n    return HTTP.json('/consultRegulation/info', params)\r\n  },\r\n  consultRegulationDel (params) { // 协商制度删除\r\n    return HTTP.json('/consultRegulation/dels', params)\r\n  },\r\n  opinioncollectList (params) { // 会议发言所有列表\r\n    return HTTP.json('/conferenceSpeaking/list', params)\r\n  },\r\n  opinioncollectManageList (params) { // 会议发言管理列表\r\n    return HTTP.json('/conferenceSpeaking/managelist', params)\r\n  },\r\n  opinioncollectAdd (params) { // 新增会议发言\r\n    return HTTP.json('/conferenceSpeaking/add', params)\r\n  },\r\n  opinioncollectDels (params) { // 删除会议发言\r\n    return HTTP.json('/conferenceSpeaking/dels', params)\r\n  },\r\n  opinioncollectEdit (params) { // 修改会议发言\r\n    return HTTP.json('/conferenceSpeaking/edit', params)\r\n  },\r\n  opinioncollectInfo (params) { // 会议发言详情\r\n    return HTTP.json('/conferenceSpeaking/info', params)\r\n  },\r\n  opinioncollectPublish (params) { // 会议发言发布\r\n    return HTTP.json('/conferenceSpeaking/publish', params)\r\n  },\r\n  opinioncollectIsTop (params) { // 会议发言置顶\r\n    return HTTP.json('/conferenceSpeaking/istop', params)\r\n  },\r\n  commentAdd (params) { // 会议发言提报\r\n    return HTTP.json('/comment/add', params)\r\n  },\r\n  commentCheckPass (params) { // 评论审核通过\r\n    return HTTP.json('/comment/check/pass', params)\r\n  },\r\n  commentCheckNoPass (params) {// 评论审核不通过\r\n    return HTTP.json('/comment/check/nopass', params)\r\n  },\r\n  opinioncollectUseroffice (params) { // 查询本用户下的发布单位\r\n    return HTTP.json('/conferenceSpeaking/useroffice', params)\r\n  },\r\n  activityTypeSelect (params) { // 活动类型下拉选\r\n    return HTTP.json('/activitytype/selecttree', params)\r\n  },\r\n  activityList (params) { // 活动列表\r\n    return HTTP.json('/servantactivity/list', params)\r\n  },\r\n  activityInfo (params) { // 活动详情\r\n    return HTTP.json('/servantactivity/info', params)\r\n  },\r\n  activityDel (params) { // 活动删除\r\n    return HTTP.json('/servantactivity/dels', params)\r\n  },\r\n  activitydocList (params) { // 活动材料列表\r\n    return HTTP.json('/activitydoc/managelist', params)\r\n  },\r\n  activitydocOpenList (params) { // 活动材料公开列表\r\n    return HTTP.json('/activitydoc/list', params)\r\n  },\r\n  activitydocInfo (params) { // 活动材料详情\r\n    return HTTP.json('/activitydoc/info', params)\r\n  },\r\n  activitydocDel (params) { // 活动材料删除\r\n    return HTTP.json('/activitydoc/dels', params)\r\n  },\r\n  activitydocIsPublic (params) { // 活动材料公开\r\n    return HTTP.json('/activitydoc/ispublic', params)\r\n  },\r\n  userJoin (params) { // 推荐用户\r\n    return HTTP.json('/common/activityRecommendation', params, { noErrorTip: true })\r\n  },\r\n  videoConnectionList (params) { // 视频会议列表\r\n    return HTTP.json('/videoConnection/list', params)\r\n  },\r\n  videoConnectionInfo (params) { // 视频会议详情\r\n    return HTTP.json('/videoConnection/info', params)\r\n  },\r\n  videoConnectionDel (params) { // 视频会议删除\r\n    return HTTP.json('/videoConnection/dels', params)\r\n  },\r\n  networkDiscussionsList (params) { // 网络议政管理列表\r\n    return HTTP.json('/opinioncollect/managelist', params)\r\n  },\r\n  networkDiscussionsAdd (params) { // 新增网络议政\r\n    return HTTP.json('/opinioncollect/add', params)\r\n  },\r\n  networkDiscussionsDels (params) { // 删除网络议政\r\n    return HTTP.json('/opinioncollect/dels', params)\r\n  },\r\n  networkDiscussionsEdit (params) { // 修改网络议政\r\n    return HTTP.json('/opinioncollect/edit', params)\r\n  },\r\n  networkDiscussionsInfo (params) { // 网络议政详情\r\n    return HTTP.json('/opinioncollect/info', params)\r\n  },\r\n  networkDiscussionsPublish (params) { // 网络议政发布\r\n    return HTTP.json('/opinioncollect/publish', params)\r\n  },\r\n  networkDiscussionsIsTop (params) { // 网络议政置顶\r\n    return HTTP.json('/opinioncollect/istop', params)\r\n  },\r\n  networkDiscussionsUseroffice (params) { // 查询本用户下的发布单位\r\n    return HTTP.json('/opinioncollect/useroffice', params)\r\n  },\r\n  longShortLinkExchange (url) { // 长链接转短链接\r\n    return HTTP.get(`/longShortLink/exchange?longUrl=${url}`)\r\n  },\r\n  feedbackList (params) { // 反馈记录列表\r\n    return HTTP.json('/feedback/list', params)\r\n  },\r\n  feedbackAdd (params) { // 新增反馈记录\r\n    return HTTP.json('/feedback/add', params)\r\n  },\r\n  feedbackInfo (params) { // 反馈记录详情\r\n    return HTTP.json('/feedback/info', params)\r\n  },\r\n  feedbackDels (params) { // 删除反馈记录\r\n    return HTTP.json('/feedback/dels', params)\r\n  },\r\n  feedbackEdit (params) { // 编辑反馈记录\r\n    return HTTP.json('/feedback/edit', params)\r\n  },\r\n  studypaperList (params) { // 问卷调查列表\r\n    return HTTP.json('studypaper/managelist', params)\r\n  },\r\n  studypaperInfo (params) { // 问卷调查详情\r\n    return HTTP.json('/studypaper/info', params)\r\n  },\r\n  studypaperDel (params) { // 问卷调查删除\r\n    return HTTP.json('/studypaper/dels', params)\r\n  },\r\n  studypaperDetailquestion (params) { // 结果统计\r\n    return HTTP.json('/studypaper/detailquestion', params)\r\n  },\r\n  studypaperRevoke (params) { // 问卷调查撤回\r\n    return HTTP.json('/studypaper/revoke', params)\r\n  },\r\n  studyexamineList (params) { // 参与情况\r\n    return HTTP.json('/studyexamine/list', params)\r\n  },\r\n  studypaperDetail (params) { // 考试详情\r\n    return HTTP.json('/studypaper/detail', params)\r\n  },\r\n  studyexamineResult (params) { // 考试结果\r\n    return HTTP.json('/studyexamine/result', params)\r\n  },\r\n  // studypaperExampaper (params) { // 考试详情\r\n  //   return HTTP.json('/studypaper/exampaper', params)\r\n  // },\r\n\r\n  // studypaperPublish (params) { // 考试发布\r\n  //   return HTTP.json('/studypaper/publish', params)\r\n  // },\r\n\r\n  // studypaperTotal (params) { // 考试统计\r\n  //   return HTTP.json('/studypaper/total', params)\r\n  // },\r\n  studytypeList (params) { // 题库类型列表\r\n    return HTTP.json('/studytype/list', params)\r\n  },\r\n  studytypeInfo (params) { // 题库类型详情\r\n    return HTTP.json('/studytype/info', params)\r\n  },\r\n  studytypeDel (params) { // 题库类型删除\r\n    return HTTP.json('/studytype/dels', params)\r\n  },\r\n  studytopicList (params) { // 题库列表\r\n    return HTTP.json('/studytopic/list', params)\r\n  },\r\n  studytopicInfo (params) { // 题库详情\r\n    return HTTP.json('/studytopic/info', params)\r\n  },\r\n  studytopicDel (params) { // 题库删除\r\n    return HTTP.json('/studytopic/dels', params)\r\n  },\r\n  studytopicMove (params) { // 题库移动\r\n    return HTTP.json('/studytopic/move', params)\r\n  },\r\n  studytopicType (params) { // 题库题型字典\r\n    return HTTP.json('/studytopic/type', params)\r\n  },\r\n  studypapertopicList (params) { // 题库导入列表查询\r\n    return HTTP.json('/studypapertopic/list', params)\r\n  },\r\n  studypapertopicInfo (params) { // 题库导入详情\r\n    return HTTP.json('/studypapertopic/info', params)\r\n  },\r\n  studypapertopicImport (params) { // 题库导入生成新id\r\n    return HTTP.json('/studypapertopic/import', params)\r\n  },\r\n  studypapertopicDel (params) { // 题库导入删除\r\n    return HTTP.json('/studypapertopic/dels', params)\r\n  },\r\n  studypapertopicMytopics (params) { // 任务发布\r\n    return HTTP.json('/studypapertopic/mytopics', params)\r\n  },\r\n  microAdviceMyListCount (params) { // 我的成果统计\r\n    return HTTP.json('/microAdvice/myListCount', params)\r\n  },\r\n  microAdviceInfo (params) { // 成果详情\r\n    return HTTP.json('/microAdvice/info', params)\r\n  },\r\n  microAdviceMyList (params) { // 我的成果管理列表\r\n    return HTTP.json('/microAdvice/myList', params)\r\n  },\r\n  microAdviceDels (params) { // 成果删除\r\n    return HTTP.json('/microAdvice/dels', params)\r\n  },\r\n  microAdviceList (params) { // 成果管理列表\r\n    return HTTP.json('/microAdvice/list', params)\r\n  },\r\n  microAdviceGroupList (params) { // 成果办理列表\r\n    return HTTP.json('/microAdvice/groupList', params)\r\n  },\r\n  microAdviceGroupListCount (params) { // 成果办理统计\r\n    return HTTP.json('/microAdvice/groupListCount', params)\r\n  },\r\n  complete (params) { // 协商成果审核\r\n    return HTTP.json('/microadviceflow/complete', params)\r\n  },\r\n  microAdviceGroupSelector (params) { // 办理单位\r\n    return HTTP.json('/microAdvice/group/selector', params)\r\n  },\r\n  microAdviceUnderAreaSelector (params) { // 下级地区\r\n    return HTTP.json('/microAdvice/underArea/selector', params)\r\n  },\r\n  manageJointly (params) { // 协办\r\n    return HTTP.json('/microAdvice/manageJointly', params)\r\n  },\r\n  microFlowRecordEdit (params) { // 回复编辑\r\n    return HTTP.json('/microFlowRecord/edit', params)\r\n  },\r\n}\r\nexport default api\r\n"], "mappings": ";;;;;AAAA;AACA,OAAOA,IAAI,MAAM,aAAa;AAC9B,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,IAAMC,GAAG,GAAAC,aAAA,CAAAA,aAAA,KACJF,SAAS;EACZG,SAASA,CAAEC,MAAM,EAAE;IAAE;IACnB,OAAOL,IAAI,CAACM,IAAI,CAAC,aAAa,EAAED,MAAM,CAAC;EACzC,CAAC;EACDE,eAAeA,CAAEF,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDG,eAAeA,CAAEH,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDI,cAAcA,CAAEJ,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDK,kBAAkBA,CAAEL,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDM,kBAAkBA,CAAEN,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDO,iBAAiBA,CAAEP,MAAM,EAAE;IAAE;IAC3B,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDQ,mBAAmBA,CAAER,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDS,qBAAqBA,CAAET,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDU,oBAAoBA,CAAEV,MAAM,EAAE;IAAE;IAC9B,OAAOL,IAAI,CAACM,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDW,gBAAgBA,CAAEX,MAAM,EAAE;IAAE;IAC1B,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDY,gBAAgBA,CAAEZ,MAAM,EAAE;IAAE;IAC1B,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDa,eAAeA,CAAEb,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDc,kBAAkBA,CAAEd,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDe,uBAAuBA,CAAEf,MAAM,EAAE;IAAE;IACjC,OAAOL,IAAI,CAACM,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACDgB,eAAeA,CAAEhB,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDiB,eAAeA,CAAEjB,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDkB,cAAcA,CAAElB,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDmB,iBAAiBA,CAAEnB,MAAM,EAAE;IAAE;IAC3B,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDoB,mBAAmBA,CAAEpB,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDqB,mBAAmBA,CAAErB,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDsB,kBAAkBA,CAAEtB,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDuB,2BAA2BA,CAAEvB,MAAM,EAAE;IAAE;IACrC,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACDwB,2BAA2BA,CAAExB,MAAM,EAAE;IAAE;IACrC,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACDyB,0BAA0BA,CAAEzB,MAAM,EAAE;IAAE;IACpC,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACD0B,2BAA2BA,CAAE1B,MAAM,EAAE;IAAE;IACrC,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACD2B,2BAA2BA,CAAE3B,MAAM,EAAE;IAAE;IACrC,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACD4B,0BAA0BA,CAAE5B,MAAM,EAAE;IAAE;IACpC,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACD6B,6BAA6BA,CAAE7B,MAAM,EAAE;IAAE;IACvC,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiC,EAAED,MAAM,CAAC;EAC7D,CAAC;EACD8B,6BAA6BA,CAAE9B,MAAM,EAAE;IAAE;IACvC,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiC,EAAED,MAAM,CAAC;EAC7D,CAAC;EACD+B,4BAA4BA,CAAE/B,MAAM,EAAE;IAAE;IACtC,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiC,EAAED,MAAM,CAAC;EAC7D,CAAC;EACDgC,mCAAmCA,CAAEhC,MAAM,EAAE;IAAE;IAC7C,OAAOL,IAAI,CAACM,IAAI,CAAC,uCAAuC,EAAED,MAAM,CAAC;EACnE,CAAC;EACDiC,+BAA+BA,CAAEjC,MAAM,EAAE;IAAE;IACzC,OAAOL,IAAI,CAACM,IAAI,CAAC,mCAAmC,EAAED,MAAM,CAAC;EAC/D,CAAC;EACDkC,kCAAkCA,CAAElC,MAAM,EAAE;IAAE;IAC5C,OAAOL,IAAI,CAACM,IAAI,CAAC,sCAAsC,EAAED,MAAM,CAAC;EAClE,CAAC;EACDmC,qBAAqBA,CAAEnC,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDoC,qBAAqBA,CAAEpC,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDqC,oBAAoBA,CAAErC,MAAM,EAAE;IAAE;IAC9B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDsC,kBAAkBA,CAAEtC,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDuC,wBAAwBA,CAAEvC,MAAM,EAAE;IAAE;IAClC,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACDwC,iBAAiBA,CAAExC,MAAM,EAAE;IAAE;IAC3B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDyC,kBAAkBA,CAAEzC,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACD0C,kBAAkBA,CAAE1C,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACD2C,kBAAkBA,CAAE3C,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACD4C,qBAAqBA,CAAE5C,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACD6C,mBAAmBA,CAAE7C,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACD8C,UAAUA,CAAE9C,MAAM,EAAE;IAAE;IACpB,OAAOL,IAAI,CAACM,IAAI,CAAC,cAAc,EAAED,MAAM,CAAC;EAC1C,CAAC;EACD+C,gBAAgBA,CAAE/C,MAAM,EAAE;IAAE;IAC1B,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDgD,kBAAkBA,CAAEhD,MAAM,EAAE;IAAC;IAC3B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDiD,wBAAwBA,CAAEjD,MAAM,EAAE;IAAE;IAClC,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACDkD,kBAAkBA,CAAElD,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDmD,YAAYA,CAAEnD,MAAM,EAAE;IAAE;IACtB,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDoD,YAAYA,CAAEpD,MAAM,EAAE;IAAE;IACtB,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDqD,WAAWA,CAAErD,MAAM,EAAE;IAAE;IACrB,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDsD,eAAeA,CAAEtD,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDuD,mBAAmBA,CAAEvD,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDwD,eAAeA,CAAExD,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDyD,cAAcA,CAAEzD,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACD0D,mBAAmBA,CAAE1D,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACD2D,QAAQA,CAAE3D,MAAM,EAAE;IAAE;IAClB,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgC,EAAED,MAAM,EAAE;MAAE4D,UAAU,EAAE;IAAK,CAAC,CAAC;EAClF,CAAC;EACDC,mBAAmBA,CAAE7D,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACD8D,mBAAmBA,CAAE9D,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACD+D,kBAAkBA,CAAE/D,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDgE,sBAAsBA,CAAEhE,MAAM,EAAE;IAAE;IAChC,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACDiE,qBAAqBA,CAAEjE,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDkE,sBAAsBA,CAAElE,MAAM,EAAE;IAAE;IAChC,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDmE,sBAAsBA,CAAEnE,MAAM,EAAE;IAAE;IAChC,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDoE,sBAAsBA,CAAEpE,MAAM,EAAE;IAAE;IAChC,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDqE,yBAAyBA,CAAErE,MAAM,EAAE;IAAE;IACnC,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDsE,uBAAuBA,CAAEtE,MAAM,EAAE;IAAE;IACjC,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDuE,4BAA4BA,CAAEvE,MAAM,EAAE;IAAE;IACtC,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACDwE,qBAAqBA,CAAEC,GAAG,EAAE;IAAE;IAC5B,OAAO9E,IAAI,CAAC+E,GAAG,CAAC,mCAAmCD,GAAG,EAAE,CAAC;EAC3D,CAAC;EACDE,YAAYA,CAAE3E,MAAM,EAAE;IAAE;IACtB,OAAOL,IAAI,CAACM,IAAI,CAAC,gBAAgB,EAAED,MAAM,CAAC;EAC5C,CAAC;EACD4E,WAAWA,CAAE5E,MAAM,EAAE;IAAE;IACrB,OAAOL,IAAI,CAACM,IAAI,CAAC,eAAe,EAAED,MAAM,CAAC;EAC3C,CAAC;EACD6E,YAAYA,CAAE7E,MAAM,EAAE;IAAE;IACtB,OAAOL,IAAI,CAACM,IAAI,CAAC,gBAAgB,EAAED,MAAM,CAAC;EAC5C,CAAC;EACD8E,YAAYA,CAAE9E,MAAM,EAAE;IAAE;IACtB,OAAOL,IAAI,CAACM,IAAI,CAAC,gBAAgB,EAAED,MAAM,CAAC;EAC5C,CAAC;EACD+E,YAAYA,CAAE/E,MAAM,EAAE;IAAE;IACtB,OAAOL,IAAI,CAACM,IAAI,CAAC,gBAAgB,EAAED,MAAM,CAAC;EAC5C,CAAC;EACDgF,cAAcA,CAAEhF,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDiF,cAAcA,CAAEjF,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDkF,aAAaA,CAAElF,MAAM,EAAE;IAAE;IACvB,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDmF,wBAAwBA,CAAEnF,MAAM,EAAE;IAAE;IAClC,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACDoF,gBAAgBA,CAAEpF,MAAM,EAAE;IAAE;IAC1B,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDqF,gBAAgBA,CAAErF,MAAM,EAAE;IAAE;IAC1B,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDsF,gBAAgBA,CAAEtF,MAAM,EAAE;IAAE;IAC1B,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDuF,kBAAkBA,CAAEvF,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACD;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACAwF,aAAaA,CAAExF,MAAM,EAAE;IAAE;IACvB,OAAOL,IAAI,CAACM,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACDyF,aAAaA,CAAEzF,MAAM,EAAE;IAAE;IACvB,OAAOL,IAAI,CAACM,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACD0F,YAAYA,CAAE1F,MAAM,EAAE;IAAE;IACtB,OAAOL,IAAI,CAACM,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACD2F,cAAcA,CAAE3F,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACD4F,cAAcA,CAAE5F,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACD6F,aAAaA,CAAE7F,MAAM,EAAE;IAAE;IACvB,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACD8F,cAAcA,CAAE9F,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACD+F,cAAcA,CAAE/F,MAAM,EAAE;IAAE;IACxB,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDgG,mBAAmBA,CAAEhG,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDiG,mBAAmBA,CAAEjG,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDkG,qBAAqBA,CAAElG,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDmG,kBAAkBA,CAAEnG,MAAM,EAAE;IAAE;IAC5B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDoG,uBAAuBA,CAAEpG,MAAM,EAAE;IAAE;IACjC,OAAOL,IAAI,CAACM,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACDqG,sBAAsBA,CAAErG,MAAM,EAAE;IAAE;IAChC,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDsG,eAAeA,CAAEtG,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDuG,iBAAiBA,CAAEvG,MAAM,EAAE;IAAE;IAC3B,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDwG,eAAeA,CAAExG,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDyG,eAAeA,CAAEzG,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACD0G,oBAAoBA,CAAE1G,MAAM,EAAE;IAAE;IAC9B,OAAOL,IAAI,CAACM,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACD2G,yBAAyBA,CAAE3G,MAAM,EAAE;IAAE;IACnC,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACD4G,QAAQA,CAAE5G,MAAM,EAAE;IAAE;IAClB,OAAOL,IAAI,CAACM,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACD6G,wBAAwBA,CAAE7G,MAAM,EAAE;IAAE;IAClC,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACD8G,4BAA4BA,CAAE9G,MAAM,EAAE;IAAE;IACtC,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiC,EAAED,MAAM,CAAC;EAC7D,CAAC;EACD+G,aAAaA,CAAE/G,MAAM,EAAE;IAAE;IACvB,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACDgH,mBAAmBA,CAAEhH,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD;AAAC,EACF;AACD,eAAeH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}