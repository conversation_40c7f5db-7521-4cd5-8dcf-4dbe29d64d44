<template>
  <div class="UnitSummaryReport">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" searchPopover>
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
      <template #searchPopover>
        <el-select v-model="termYearId" placeholder="请选择届次" clearable>
          <el-option v-for="item in termYearData" :key="item.key" :label="item.name" :value="item.key" />
        </el-select>
        <el-select v-model="hasUpload" @change="queryChange" placeholder="请选择是否上传" clearable>
          <el-option value="1" label="已上传" />
          <el-option value="0" label="未上传" />
        </el-select>
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <el-table-column label="办理单位" min-width="220" prop="handleOfficeName">
          <template #default="scope">
            <el-link type="primary" @click="handleDetails(scope.row)">{{ scope.row.handleOfficeName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="届次" min-width="120" prop="termYearName" />
        <el-table-column label="是否上传" width="120" class-name="globalTableIcon">
          <template #default="scope">
            <el-icon :class="[scope.row.hasUpload ? 'globalTableCheck' : 'globalTableClose']">
              <CircleCheck v-if="scope.row.hasUpload" />
              <CircleClose v-if="!scope.row.hasUpload" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column label="经办人" min-width="160" prop="uploadUserName" />
        <el-table-column label="联系电话" min-width="160" prop="contactMobile" />
        <el-table-column label="创建时间" width="180">
          <template #default="scope">{{ format(scope.row.createDate) }}</template>
        </el-table-column>
        <xyl-global-table-button :data="tableButtonList" @buttonClick="handleCommand"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="show" name="上传">
      <SubmitUnitSummaryReport :id="id" @callback="callback"></SubmitUnitSummaryReport>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow" name="详情">
      <UnitSummaryReportDetails :id="id"></UnitSummaryReportDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'UnitSummaryReport' }
</script>
<script setup>
import api from '@/api'
import { format } from 'common/js/time.js'
import { ref, onActivated } from 'vue'
import { saveAs } from 'file-saver'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import SubmitUnitSummaryReport from './component/SubmitUnitSummaryReport'
import UnitSummaryReportDetails from './component/UnitSummaryReportDetails'
const buttonList = [
  { id: 'refresh', name: '刷新', type: 'primary', has: 'refresh' },
  { id: 'rush', name: '催办', type: 'primary', has: 'rush' },
  { id: 'exportFile', name: '导出附件', type: 'primary', has: 'export_file' }
]
const tableButtonList = [{ id: 'edit', name: '上传', width: 100, has: 'edit' }]
const id = ref('')
const show = ref(false)
const detailsShow = ref(false)
const hasUpload = ref('')
const termYearId = ref('')
const termYearData = ref([])
const {
  keyword,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  handleQuery,
  tableDataArray,
  handleTableSelect,
  tableRefReset,
  tableQuery
} = GlobalTable({ tableApi: 'handleOfficeReportList' })

onActivated(() => {
  handleQuery()
  termYearCurrent()
  termYearSelect()
})
// 获取当前届次
const termYearCurrent = async () => {
  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })
  if (!termYearId.value) { termYearId.value = data.id }
}
const termYearSelect = async () => {
  const { data } = await api.termYearSelect({ termYearType: 'cppcc_member' })
  termYearData.value = data
}

const handleButton = (isType) => {
  switch (isType) {
    case 'refresh':
      handleOfficeReportFlush()
      break
    case 'rush':
      ElMessageBox.confirm('此操作将发送短信提醒当前选中的办理单位上传总结报告, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        handleOfficeReportPress()
      }).catch(() => { ElMessage({ type: 'info', message: '已取消操作' }) })
      break
    case 'exportFile':
      handleBatch()
      break
    default:
      break
  }
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  id.value = item.id
  detailsShow.value = true
}
const queryChange = () => {
  tableQuery.value = { query: { termYearId: termYearId.value || null, hasUpload: hasUpload.value || null } }
}
const handleReset = () => {
  keyword.value = ''
  hasUpload.value = ''
  termYearId.value = ''
  termYearCurrent()
  tableQuery.value = { query: { termYearId: termYearId.value || null, hasUpload: hasUpload.value || null } }
  handleQuery()
}
const handleEdit = (item) => {
  id.value = item.id
  show.value = true
}
const callback = () => {
  tableRefReset()
  handleQuery()
  show.value = false
}
const handleBatch = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作将当前选中数据的附件导出zip, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => { handleOfficeReportZip() }).catch(() => { ElMessage({ type: 'info', message: '已取消导出' }) })
  } else {
    ElMessageBox.confirm('当前没有选择数据，是否根据列表筛选条件导出所有数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => { handleOfficeReportZip() }).catch(() => { ElMessage({ type: 'info', message: '已取消导出' }) })
  }
}
const handleOfficeReportFlush = async () => {
  const { code } = await api.handleOfficeReportFlush()
  if (code === 200) {
    ElMessage({ type: 'success', message: '刷新成功' })
    tableRefReset()
    handleQuery()
  }
}
const handleOfficeReportPress = async () => {
  const { code } = await api.handleOfficeReportPress({ ids: tableDataArray.value.map(v => v.id) })
  if (code === 200) {
    ElMessage({ type: 'success', message: '催办成功' })
    tableRefReset()
    handleQuery()
  }
}
const handleOfficeReportZip = async () => {
  const res = await api.handleOfficeReportZip({
    ids: tableDataArray.value.map(v => v.id),
    keyword: keyword.value, query: { termYearId: termYearId.value || null, hasUpload: hasUpload.value || null }
  })
  saveAs(res, '总结报告附件.zip')
  tableRefReset()
  handleQuery()
}
</script>
<style lang="scss">
.UnitSummaryReport {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
