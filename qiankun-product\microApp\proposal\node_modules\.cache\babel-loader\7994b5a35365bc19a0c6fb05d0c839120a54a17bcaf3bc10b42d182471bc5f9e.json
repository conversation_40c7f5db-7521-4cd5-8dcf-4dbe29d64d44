{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestRecommendUnit\"\n};\nvar _hoisted_2 = {\n  class: \"SuggestRecommendUnitBody\"\n};\nvar _hoisted_3 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"SuggestRecommendUnitTitle\"\n  }, \"根据办理单位职能匹配、历史建议交办办理数据以及希望送交办单位三个维度综合分析，小助手建议您将本提案交由以下办理单位办理，点击他们就会对号入座哦~~\", -1 /* HOISTED */)), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"SuggestRecommendUnitName\"\n  }, \"办理单位：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.unitData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"SuggestRecommendUnitItem\",\n      onClick: function onClick($event) {\n        return $setup.handleUnitClick(item);\n      },\n      key: item.id\n    }, _toDisplayString(item.name), 9 /* TEXT, PROPS */, _hoisted_3);\n  }), 128 /* KEYED_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$setup", "unitData", "item", "onClick", "$event", "handleUnitClick", "key", "id", "name", "_hoisted_3"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendUnit\\SuggestRecommendUnit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestRecommendUnit\">\r\n    <div class=\"SuggestRecommendUnitTitle\">根据办理单位职能匹配、历史建议交办办理数据以及希望送交办单位三个维度综合分析，小助手建议您将本提案交由以下办理单位办理，点击他们就会对号入座哦~~</div>\r\n    <div class=\"SuggestRecommendUnitName\">办理单位：</div>\r\n    <div class=\"SuggestRecommendUnitBody\">\r\n      <div class=\"SuggestRecommendUnitItem\"\r\n           v-for=\"item in unitData\"\r\n           @click=\"handleUnitClick(item)\"\r\n           :key=\"item.id\">{{ item.name }}</div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestRecommendUnit' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, watch } from 'vue'\r\nconst props = defineProps({ params: { type: Object, default: () => ({}) } })\r\nconst emit = defineEmits(['callback', 'select'])\r\nconst unitData = ref([])\r\n\r\nconst commonMethod = async () => {\r\n  try {\r\n    const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区\r\n    const { data } = await api.commonUnit({ areaId: AreaId, dbName: 'thinktank', type: '1', ...props.params })\r\n    unitData.value = data || []\r\n    if (unitData.value.length) { emit('callback', true, true) } else { emit('callback', false, false) }\r\n  } catch (err) {\r\n    emit('callback', false, false)\r\n  }\r\n}\r\nconst handleUnitClick = (item) => { emit('select', item) }\r\nwatch(() => props.params, () => {\r\n  if (props.params.content) { commonMethod() } else { emit('callback', false, false) }\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestRecommendUnit {\r\n  width: 360px;\r\n  padding: var(--zy-distance-two);\r\n  padding-top: 0;\r\n\r\n  .SuggestRecommendUnitTitle {\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding-bottom: var(--zy-font-name-distance-five);\r\n    color: var(--zy-el-color-primary);\r\n  }\r\n\r\n  .SuggestRecommendUnitName {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding-bottom: var(--zy-font-name-distance-five);\r\n  }\r\n\r\n  .SuggestRecommendUnitBody {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n\r\n    .SuggestRecommendUnitItem {\r\n      height: var(--zy-height-routine);\r\n      line-height: var(--zy-height-routine);\r\n      font-size: var(--zy-text-font-size);\r\n      background-color: var(--zy-el-color-info-light-9);\r\n      padding: 0 var(--zy-distance-five);\r\n      margin-right: var(--zy-distance-five);\r\n      margin-bottom: var(--zy-distance-five);\r\n      border-radius: var(--el-border-radius-small);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EAG1BA,KAAK,EAAC;AAA0B;iBAJzC;;uBACEC,mBAAA,CASM,OATNC,UASM,G,0BARJC,mBAAA,CAAsH;IAAjHH,KAAK,EAAC;EAA2B,GAAC,2EAAyE,sB,0BAChHG,mBAAA,CAAiD;IAA5CH,KAAK,EAAC;EAA0B,GAAC,OAAK,sBAC3CG,mBAAA,CAKM,OALNC,UAKM,I,kBAJJH,mBAAA,CAGyCI,SAAA,QAR/CC,WAAA,CAM0BC,MAAA,CAAAC,QAAQ,EANlC,UAMkBC,IAAI;yBADhBR,mBAAA,CAGyC;MAHpCD,KAAK,EAAC,0BAA0B;MAE/BU,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAEJ,MAAA,CAAAK,eAAe,CAACH,IAAI;MAAA;MAC3BI,GAAG,EAAEJ,IAAI,CAACK;wBAAOL,IAAI,CAACM,IAAI,wBARtCC,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}