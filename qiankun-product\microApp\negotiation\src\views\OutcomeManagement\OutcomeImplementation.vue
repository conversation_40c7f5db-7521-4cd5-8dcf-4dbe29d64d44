<template>
  <div class="OutcomeImplementation">
    <xyl-label v-model="labelId" @labelClick="labelClick">
      <xyl-label-item value="2">处理中<span>{{ myListCount.handing || 0 }}</span></xyl-label-item>
      <xyl-label-item value="3">已回复<span>{{ myListCount.hasReply || 0 }}</span></xyl-label-item>
      <xyl-label-item value="1">所有<span>{{ myListCount.all || 0 }}</span></xyl-label-item>
    </xyl-label>
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
      <template #searchPopover>
        <xyl-date-picker v-model="year" placeholder="请选择年份" @change="queryChange" value-format="YYYY" type="year" />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"></xyl-global-table>
        <xyl-global-table-button :data="tableButtonList" :max="2" :elWhetherShow="handleElWhetherShow"
          @buttonClick="handleCommand" :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="showExportExcel" name="导出Excel">
      <xyl-export-excel module="microAdviceGroupExcel" tableId="id_micro_advice" name="微建议办理" :exportId="exportId"
        :params="exportExcelParams" @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="show" :name="id ? '编辑微建议' : '新增微建议'">
      <SubmitMinSuggestManage :id="id" @callback="submitCallback"></SubmitMinSuggestManage>
    </xyl-popup-window>
    <xyl-popup-window v-model="replyShow" name="回复">
      <SubmitReply :id="id" userType="groupReply" @callback="replyCallback"></SubmitReply>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'OutcomeImplementation' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated } from 'vue'
// import { filterTableData } from '@/assets/js/publicOpinionExportWord'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { useRoute } from 'vue-router'
// import SubmitReply from "./component/SubmitReply";
// import { ElMessage, ElMessageBox } from 'element-plus'
import { exportWordHtmlList, exportWordHtmlObj, qiankunMicro } from "common/config/MicroGlobal";
import { ElMessage, ElMessageBox } from 'element-plus'
const route = useRoute()
const tableButtonList = [
  { id: 'showReply', name: '回复', width: 100, has: '', whetherShow: true },
  { id: 'showSignfor', name: '签收', width: 30, has: '', whetherShow: true },
  { id: 'showVisaRefusal', name: '拒签', width: 30, has: '', whetherShow: true }
]
const buttonList = [
  { id: 'export', name: '导出excel', type: 'primary', has: 'export' },
  { id: 'exportWord', name: '导出word', type: 'primary', has: 'export' },
]
const showExportExcel = ref(false)
const exportExcelParams = ref({})
const exportId = ref([])
const id = ref('')
const myListCount = ref({})
const show = ref(false)
const replyShow = ref(false)
const labelId = ref('2')
const year = ref('')
// const areas = ref([])
const status = ref('handing')
// const statusSelector = ref([])

const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  exportShow,
  tableDataArray,
  handleDel,
  handleQuery,
  handleTableSelect,
  handleSortChange,
  handleHeaderClass,
  handleEditorCustom,
  tableHead,
  tableQuery,
} = GlobalTable({
  tableId: 'id_micro_negotiate',
  tableApi: 'microAdviceGroupList',
  delApi: 'microAdviceDels',
  tableDataObj: {
    orderBys: [
      {
        columnId: 'id_micro_advice_create_date',
        isDesc: 1
      }
    ],
    type: 'pushNegotiateGroup'
  }
})

onActivated(() => {
  tableQuery.value = { status: status.value }
  microAdviceGroupListCountQuery()
  handleQuery()
})

const handleElWhetherShow = (row, isType) => {
  if (isType === 'showReply') {
    return row.negotiateStatus === 1
  } else if (isType === 'showSignfor') {
    return row.negotiateStatus === 0
  } else if (isType === 'showVisaRefusal') {
    return row.negotiateStatus === 0
  }
}
const exportExcel = () => {
  exportId.value = tableDataArray.value.map(item => item.id)
  exportExcelParams.value = {
    where: queryRef.value.getWheres(),
    ids: route.query.ids ? JSON.parse(route.query.ids) : [],
    status: status.value
  }
  showExportExcel.value = true
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'showReply':
      handleReply(row)
      break
    case 'showSignfor':
      ElMessageBox.confirm(`此操作将签收当前的数据, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => { handleSignFor(row, 1) }).catch(() => { ElMessage({ type: 'info', message: `已取消签收` }) })
      break
    case 'showVisaRefusal':
      ElMessageBox.confirm(`此操作将拒签当前的数据, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => { handleSignFor(row, 2) }).catch(() => { ElMessage({ type: 'info', message: `已取消拒签` }) })
      break
    default:
      break
  }
}

const handleSignFor = async (item, type) => {
  const res = await api.microFlowRecordComplete({
    detailId: item.id,
    status: type
  })
  console.log('res', res)
}

const handleButton = (id) => {
  switch (id) {
    case 'new':
      handleNew()
      break
    case 'del':
      handleDel('数据')
      break
    case 'export':
      exportExcel()
      break
    case 'exportWord':
      handleExportWord()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  console.log('row===>>', row)
  switch (key) {
    case 'details':
      qiankunMicro.setGlobalState({ openRoute: { name: `成果详情`, path: '/negotiation/OutcomeManageDetails', query: { id: row.id, userType: row.showReply ? 'groupReply' : '' } } })
      break
    case 'comment':
      qiankunMicro.setGlobalState({ openRoute: { name: `评论`, path: '/minSuggest/MinSuggestComment', query: { id: row.id, type: 'min_suggest' } } })
      break
    default:
      break
  }
}
// const filterTableData = (data) => {
//   var rowObj = {}
//   for (let key in data) {
//     if (rowObj[key] === null) {
//       rowObj[key] = ''
//     } else if (['colarName'].includes(key)) {
//       rowObj['groupName'] = `${rowObj['colarName']}领办`
//     } else if (['submitDate'].includes(key)) {
//       rowObj[key] = format(data[key], 'YYYY-MM-DD hh:mm')
//     } else {
//       rowObj[key] = data[key] || ''
//     }
//   }
//   return rowObj
// }
const handleExportWord = () => {
  let data = []
  if (tableDataArray.value.length > 0) {
    data = tableDataArray.value
  } else {
    data = tableData.value
  }
  var wordData = []
  // for (let index = 0; index < data.length; index++) {
  //   wordData.push(filterTableData(data[index]))
  // }
  activitydocDownloadcheck(wordData)
}
const activitydocDownloadcheck = async (wordData) => {
  if (wordData.length === 0) {
    ElMessage({ type: 'warning', message: '暂无可导出数据' })
    return
  }
  if (wordData.length === 1) {
    exportWordHtmlObj({ code: 'minSuggestList', name: wordData[0].title, key: 'content', data: wordData[0] })
  } else {
    exportWordHtmlList({ code: 'minSuggestList', name: `微建议`, key: 'content', wordNameKey: 'title', data: wordData })

  }
}
const labelClick = (val) => {
  labelId.value = val
  switch (val) {
    case '1':
      status.value = ''
      break
    case '2':
      status.value = 'handing'
      break
    case '3':
      status.value = 'hasReply'
      break
    default:
      break
  }
  tableQuery.value = { year: year.value || null, status: status.value }
  handleQuery()
}

const microAdviceGroupListCountQuery = async () => {
  const { data } = await api.microAdviceGroupListCount()
  myListCount.value = data
}

const handleReset = () => {
  keyword.value = ''
  year.value = ''
  tableQuery.value = { year: null }
  handleQuery()
}

const queryChange = () => {
  tableQuery.value = { year: year.value || null, status: status.value }
}

const handleNew = () => {
  id.value = ''
  show.value = true
}

const handleReply = (item) => {
  qiankunMicro.setGlobalState({ openRoute: { name: `协商成果回复`, path: '/negotiation/OutcomeManageDetails', query: { id: item.id, userType: 'groupReply' } } })
}

const submitCallback = () => { // 新增编辑
  show.value = false
  microAdviceGroupListCountQuery()
  handleQuery()
}


const callback = () => {
  showExportExcel.value = false
  exportShow.value = false
  handleQuery()
}

const replyCallback = () => { // 回复回调
  replyShow.value = false
  handleQuery()
  microAdviceGroupListCountQuery()
}
</script>
<style lang="scss">
.OutcomeImplementation {
  width: 100%;
  height: 100%;
  padding: 10px 20px;

  .xyl-label-item {
    span {
      border: 1px solid var(--zy-el-color-primary);
      border-radius: 30px;
      padding: 0px 15px;
      margin-left: 5px;
      font-weight: normal;
      color: var(--zy-el-color-primary);
    }
  }

  .is-active {
    span {
      background-color: #fff;
      border-radius: 30px;
      padding: 0px 15px;
      margin-left: 5px;
      font-weight: normal;
      color: var(--zy-el-color-primary);
    }
  }

  .globalTable {
    width: 100%;
    height: calc(100% - 180px);
  }

  .link_title {
    color: #3657C0;
    cursor: pointer;
  }

  .link_comment {
    cursor: pointer;
  }
}
</style>
