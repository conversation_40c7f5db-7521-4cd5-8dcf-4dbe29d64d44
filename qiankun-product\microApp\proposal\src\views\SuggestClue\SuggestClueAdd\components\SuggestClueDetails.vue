<template>
  <el-scrollbar always class="SuggestClueDetails">
    <div class="ProposalClueDetailBody">
      <div class="SubmitProposalClueName">提案线索详情</div>
      <div class="ProposalClueDetailTitle">{{ details.title }}</div>
      <div class="ProposalClueDetailInfo">
        <div class="ProposalClueDetailInfoItem">提供者：{{ details.furnishName }}</div>
        <div class="ProposalClueDetailInfoItem">提交时间：{{ format(details.createDate) }}</div>
      </div>
      <div class="ProposalClueDetailInfo">
        <div class="ProposalClueDetailInfoItem">线索类别：{{ typeLabel }}</div>
        <div class="ProposalClueDetailInfoItem">线索来源：{{ details.terminalName }}</div>
      </div>
      <div class="ProposalClueDetailContent" v-html="details.content"></div>
      <div class="globalFormButton">
        <el-button type="primary" @click="clueNeed()">线索引用</el-button>
      </div>
    </div>
  </el-scrollbar>
</template>
<script>
export default { name: 'SuggestClueDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { format } from 'common/js/time.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
const props = defineProps({ id: { type: String, default: '' } })
const details = ref({})
const typeLabel = ref('')

onMounted(() => { if (props.id) { proposalClueInfo() } })

const proposalClueInfo = async () => {
  const { data } = await api.proposalClueInfo({ detailId: props.id })
  typeLabel.value = data.proposalClueType ? data.proposalClueType.label : ''
  details.value = data
}

const clueNeed = () => {
  qiankunMicro.setGlobalState({ openRoute: { name: '提交提案', path: '/proposal/SubmitSuggest', query: { clueListId: props.id } } })
}
</script>
<style lang="scss">
.SuggestClueDetails {
  min-width: 780px;
  height: 100%;

  .ProposalClueDetailBody {
    max-width: 780px;
    margin: 20px auto;
    background-color: #fff;
    // box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
    padding: var(--zy-distance-one);


    .SubmitProposalClueName {
      padding: 20px 40px;
      font-weight: bold;
      text-align: center;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-title-font-size);
      line-height: var(--zy-line-height);
      border-bottom: 3px solid var(--zy-el-color-primary);
    }

    .ProposalClueDetailTitle {
      width: 100%;
      padding: 10px 0;
      font-weight: bold;
      font-size: var(--zy-title-font-size);
      line-height: var(--zy-line-height);
    }

    .ProposalClueDetailInfo {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .ProposalClueDetailInfoItem {
        width: 50%;
        padding: 10px 0;
      }
    }

    .ProposalClueDetailInfoItem {
      width: 100%;
      padding: 5px 0;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);

      span {
        font-weight: bold;
      }
    }

    .ProposalClueDetailContent {
      padding: 20px 0;
      overflow: hidden;
      line-height: var(--zy-line-height);

      img,
      video {
        max-width: 100%;
        height: auto !important;
      }

      table {
        max-width: 100%;
        border-collapse: collapse;
        border-spacing: 0;

        tr {
          page-break-inside: avoid;
        }
      }
    }
  }
}
</style>
