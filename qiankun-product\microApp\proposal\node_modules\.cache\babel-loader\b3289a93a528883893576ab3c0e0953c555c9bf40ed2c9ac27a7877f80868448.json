{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { ElMessage } from 'element-plus';\nimport SubmitMergerProposal from './SubmitMergerProposal';\nvar __default__ = {\n  name: 'ManualMergeProposal'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [{\n      id: 'merge',\n      name: '合并提案',\n      type: 'primary',\n      has: ''\n    }];\n    var mergeData = ref([]);\n    var mergeShow = ref(false);\n    var _GlobalTable = GlobalTable({\n        tableId: 'id_prop_proposal_prepare_merge_proposal',\n        tableApi: 'suggestionList'\n      }),\n      keyword = _GlobalTable.keyword,\n      queryRef = _GlobalTable.queryRef,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableHead = _GlobalTable.tableHead,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery,\n      tableDataArray = _GlobalTable.tableDataArray,\n      handleSortChange = _GlobalTable.handleSortChange,\n      handleHeaderClass = _GlobalTable.handleHeaderClass,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      handleEditorCustom = _GlobalTable.handleEditorCustom,\n      tableRefReset = _GlobalTable.tableRefReset;\n    onActivated(function () {\n      handleQuery();\n    });\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleButton = function handleButton(isType, params) {\n      switch (isType) {\n        case 'merge':\n          handleMerge();\n          break;\n        default:\n          break;\n      }\n    };\n    var handleTableClick = function handleTableClick(key, row) {\n      switch (key) {\n        case 'details':\n          handleDetails(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleDetails = function handleDetails(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '提案详情',\n          path: '/proposal/SuggestDetail',\n          query: {\n            id: item.id\n          }\n        }\n      });\n    };\n    var handleMerge = function handleMerge() {\n      if (tableDataArray.value.length <= 1) return ElMessage({\n        type: 'warning',\n        message: '请至少选择两条提案并案！'\n      });\n      mergeData.value = tableDataArray.value;\n      mergeShow.value = true;\n    };\n    var callback = function callback(type) {\n      if (type) {\n        tableRefReset();\n      }\n      handleQuery();\n      mergeShow.value = false;\n    };\n    var __returned__ = {\n      buttonList,\n      mergeData,\n      mergeShow,\n      keyword,\n      queryRef,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableHead,\n      tableData,\n      handleQuery,\n      tableDataArray,\n      handleSortChange,\n      handleHeaderClass,\n      handleTableSelect,\n      handleEditorCustom,\n      tableRefReset,\n      handleReset,\n      handleButton,\n      handleTableClick,\n      handleDetails,\n      handleMerge,\n      callback,\n      ref,\n      onActivated,\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get SubmitMergerProposal() {\n        return SubmitMergerProposal;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "GlobalTable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ElMessage", "SubmitMergerProposal", "__default__", "name", "buttonList", "id", "type", "has", "mergeData", "mergeShow", "_GlobalTable", "tableId", "tableApi", "keyword", "queryRef", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableHead", "tableData", "handleQuery", "tableDataArray", "handleSortChange", "handleHeaderClass", "handleTableSelect", "handleEditorCustom", "tableRefReset", "handleReset", "value", "handleButton", "isType", "params", "handleMerge", "handleTableClick", "key", "row", "handleDetails", "item", "setGlobalState", "openRoute", "path", "query", "length", "message", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/MergerProposal/component/ManualMergeProposal.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ManualMergeProposal\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\" :data=\"tableHead\" :buttonNumber=\"2\" ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\" @sort-change=\"handleSortChange\" :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\"\r\n          :noTooltip=\"['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\">\r\n          <template #mainHandleOffices=\"scope\">\r\n            <template v-if=\"scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0\">\r\n              {{ scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n            </template>\r\n            <template v-else>\r\n              {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n            </template>\r\n          </template>\r\n          <template #assistHandleOffices=\"scope\">\r\n            <template v-if=\"scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0\">\r\n              {{ scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n            </template>\r\n            <template v-else>\r\n              {{ scope.row.assistHandleVoList?.map(v => v.flowHandleOfficeName).join('、') }}\r\n            </template>\r\n          </template>\r\n          <!-- <template #publishHandleOffices=\"scope\">\r\n            {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}\r\n          </template> -->\r\n        </xyl-global-table>\r\n        <xyl-global-table-button :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"mergeShow\" name=\"合并提案\">\r\n      <SubmitMergerProposal :isSimilar=\"false\" :mergeData=\"mergeData\" @callback=\"callback\"></SubmitMergerProposal>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ManualMergeProposal' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\nimport SubmitMergerProposal from './SubmitMergerProposal'\r\nconst buttonList = [{ id: 'merge', name: '合并提案', type: 'primary', has: '' }]\r\nconst mergeData = ref([])\r\nconst mergeShow = ref(false)\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  handleQuery,\r\n  tableDataArray,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  handleEditorCustom,\r\n  tableRefReset\r\n} = GlobalTable({ tableId: 'id_prop_proposal_prepare_merge_proposal', tableApi: 'suggestionList' })\r\n\r\nonActivated(() => { handleQuery() })\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType, params) => {\r\n  switch (isType) {\r\n    case 'merge':\r\n      handleMerge()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } } })\r\n}\r\nconst handleMerge = () => {\r\n  if (tableDataArray.value.length <= 1) return ElMessage({ type: 'warning', message: '请至少选择两条提案并案！' })\r\n  mergeData.value = tableDataArray.value\r\n  mergeShow.value = true\r\n}\r\nconst callback = (type) => {\r\n  if (type) { tableRefReset() }\r\n  handleQuery()\r\n  mergeShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ManualMergeProposal {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAmDA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,oBAAoB,MAAM,wBAAwB;AAPzD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAsB,CAAC;;;;;IAQ9C,IAAMC,UAAU,GAAG,CAAC;MAAEC,EAAE,EAAE,OAAO;MAAEF,IAAI,EAAE,MAAM;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,CAAC;IAC5E,IAAMC,SAAS,GAAGZ,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMa,SAAS,GAAGb,GAAG,CAAC,KAAK,CAAC;IAC5B,IAAAc,YAAA,GAiBIZ,WAAW,CAAC;QAAEa,OAAO,EAAE,yCAAyC;QAAEC,QAAQ,EAAE;MAAiB,CAAC,CAAC;MAhBjGC,OAAO,GAAAH,YAAA,CAAPG,OAAO;MACPC,QAAQ,GAAAJ,YAAA,CAARI,QAAQ;MACRC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MACRC,MAAM,GAAAN,YAAA,CAANM,MAAM;MACNC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,QAAQ,GAAAR,YAAA,CAARQ,QAAQ;MACRC,SAAS,GAAAT,YAAA,CAATS,SAAS;MACTC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,SAAS,GAAAX,YAAA,CAATW,SAAS;MACTC,WAAW,GAAAZ,YAAA,CAAXY,WAAW;MACXC,cAAc,GAAAb,YAAA,CAAda,cAAc;MACdC,gBAAgB,GAAAd,YAAA,CAAhBc,gBAAgB;MAChBC,iBAAiB,GAAAf,YAAA,CAAjBe,iBAAiB;MACjBC,iBAAiB,GAAAhB,YAAA,CAAjBgB,iBAAiB;MACjBC,kBAAkB,GAAAjB,YAAA,CAAlBiB,kBAAkB;MAClBC,aAAa,GAAAlB,YAAA,CAAbkB,aAAa;IAGf/B,WAAW,CAAC,YAAM;MAAEyB,WAAW,CAAC,CAAC;IAAC,CAAC,CAAC;IACpC,IAAMO,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBhB,OAAO,CAACiB,KAAK,GAAG,EAAE;MAClBR,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMS,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAEC,MAAM,EAAK;MACvC,QAAQD,MAAM;QACZ,KAAK,OAAO;UACVE,WAAW,CAAC,CAAC;UACb;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,GAAG,EAAEC,GAAG,EAAK;MACrC,QAAQD,GAAG;QACT,KAAK,SAAS;UACZE,aAAa,CAACD,GAAG,CAAC;UAClB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9BxC,YAAY,CAACyC,cAAc,CAAC;QAAEC,SAAS,EAAE;UAAEtC,IAAI,EAAE,MAAM;UAAEuC,IAAI,EAAE,yBAAyB;UAAEC,KAAK,EAAE;YAAEtC,EAAE,EAAEkC,IAAI,CAAClC;UAAG;QAAE;MAAE,CAAC,CAAC;IACvH,CAAC;IACD,IAAM6B,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIX,cAAc,CAACO,KAAK,CAACc,MAAM,IAAI,CAAC,EAAE,OAAO5C,SAAS,CAAC;QAAEM,IAAI,EAAE,SAAS;QAAEuC,OAAO,EAAE;MAAe,CAAC,CAAC;MACpGrC,SAAS,CAACsB,KAAK,GAAGP,cAAc,CAACO,KAAK;MACtCrB,SAAS,CAACqB,KAAK,GAAG,IAAI;IACxB,CAAC;IACD,IAAMgB,QAAQ,GAAG,SAAXA,QAAQA,CAAIxC,IAAI,EAAK;MACzB,IAAIA,IAAI,EAAE;QAAEsB,aAAa,CAAC,CAAC;MAAC;MAC5BN,WAAW,CAAC,CAAC;MACbb,SAAS,CAACqB,KAAK,GAAG,KAAK;IACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}