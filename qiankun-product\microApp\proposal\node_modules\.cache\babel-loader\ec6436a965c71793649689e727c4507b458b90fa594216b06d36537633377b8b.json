{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestTrackTransactDetail\"\n};\nvar _hoisted_2 = {\n  class: \"SuggestTrackTransactDetailBody\"\n};\nvar _hoisted_3 = {\n  class: \"SuggestTrackTransactUnit\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_global_info_item = _resolveComponent(\"global-info-item\");\n  var _component_global_info_line = _resolveComponent(\"global-info-line\");\n  var _component_global_info = _resolveComponent(\"global-info\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"SuggestTrackTransactDetailNameBody\"\n  }, [_createElementVNode(\"div\", {\n    class: \"SuggestTrackTransactDetailName\"\n  }, [_createElementVNode(\"div\", null, \"跟踪办理审查\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dataList, function (item) {\n    return _openBlock(), _createBlock(_component_global_info, {\n      key: item.id\n    }, {\n      default: _withCtx(function () {\n        return [_createVNode(_component_global_info_line, null, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_global_info_item, {\n              label: \"申请单位\"\n            }, {\n              default: _withCtx(function () {\n                return [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", null, _toDisplayString(item.handleOfficeName), 1 /* TEXT */), !item.verifyStatus ? (_openBlock(), _createBlock(_component_el_link, {\n                  key: 0,\n                  onClick: function onClick($event) {\n                    return $setup.handleTrackTransact(item);\n                  },\n                  type: \"primary\"\n                }, {\n                  default: _withCtx(function () {\n                    return _toConsumableArray(_cache[3] || (_cache[3] = [_createTextVNode(\"审查\")]));\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n              label: \"申请时间\"\n            }, {\n              default: _withCtx(function () {\n                return [_createTextVNode(_toDisplayString($setup.format(item.createDate)), 1 /* TEXT */)];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n          label: \"申请理由\"\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"pre\", null, _toDisplayString(item.applyReason), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), item.verifyStatus ? (_openBlock(), _createBlock(_component_global_info_item, {\n          key: 0,\n          label: \"是否同意申请\"\n        }, {\n          default: _withCtx(function () {\n            return [_createTextVNode(_toDisplayString(item.verifyStatus === 1 ? '同意申请' : '驳回'), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)) : _createCommentVNode(\"v-if\", true), item.verifyStatus ? (_openBlock(), _createBlock(_component_global_info_item, {\n          key: 1,\n          label: item.verifyStatus === 1 ? '备注' : '驳回理由'\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"pre\", null, _toDisplayString(item.verifySuggestion), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"])) : _createCommentVNode(\"v-if\", true), item.verifyStatus === 1 ? (_openBlock(), _createBlock(_component_global_info_item, {\n          key: 2,\n          label: \"跟踪办理答复文件\"\n        }, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_el_link, {\n              onClick: _cache[0] || (_cache[0] = function ($event) {\n                return $setup.isTrackTransactReply = !$setup.isTrackTransactReply;\n              }),\n              type: \"primary\"\n            }, {\n              default: _withCtx(function () {\n                return _toConsumableArray(_cache[4] || (_cache[4] = [_createTextVNode(\"填写跟踪办理答复文件\")]));\n              }),\n              _: 1 /* STABLE */\n            })];\n          }),\n          _: 1 /* STABLE */\n        })) : _createCommentVNode(\"v-if\", true)];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */);\n  }), 128 /* KEYED_FRAGMENT */))]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"跟踪办理审查\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SuggestTrackTransactReview\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.isTrackTransactReply,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.isTrackTransactReply = $event;\n    }),\n    name: \"填写跟踪办理答复文件\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SubmitSuggestTrackTransactReply\"], {\n        suggestId: $setup.props.id,\n        unitId: $setup.transactId,\n        traceId: $setup.tracesInfo.id,\n        onCallback: $setup.handleCallback\n      }, null, 8 /* PROPS */, [\"suggestId\", \"unitId\", \"traceId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$setup", "dataList", "item", "_createBlock", "_component_global_info", "key", "id", "default", "_withCtx", "_createVNode", "_component_global_info_line", "_component_global_info_item", "label", "_hoisted_3", "_toDisplayString", "handleOfficeName", "verifyStatus", "_component_el_link", "onClick", "$event", "handleTrackTransact", "type", "_toConsumableArray", "_cache", "_createTextVNode", "_", "_createCommentVNode", "format", "createDate", "applyReason", "verifySuggestion", "isTrackTransactReply", "_component_xyl_popup_window", "modelValue", "show", "name", "onCallback", "callback", "suggestId", "props", "unitId", "transactId", "traceId", "tracesInfo", "handleCallback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\component\\SuggestTrackTransactDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestTrackTransactDetail\">\r\n    <div class=\"SuggestTrackTransactDetailNameBody\">\r\n      <div class=\"SuggestTrackTransactDetailName\">\r\n        <div>跟踪办理审查</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"SuggestTrackTransactDetailBody\">\r\n      <global-info v-for=\"item in dataList\"\r\n                   :key=\"item.id\">\r\n        <global-info-line>\r\n          <global-info-item label=\"申请单位\">\r\n            <div class=\"SuggestTrackTransactUnit\">\r\n              <span>{{ item.handleOfficeName }}</span>\r\n              <el-link v-if=\"!item.verifyStatus\"\r\n                       @click=\"handleTrackTransact(item)\"\r\n                       type=\"primary\">审查</el-link>\r\n            </div>\r\n          </global-info-item>\r\n          <global-info-item label=\"申请时间\">{{ format(item.createDate) }}</global-info-item>\r\n        </global-info-line>\r\n        <global-info-item label=\"申请理由\">\r\n          <pre>{{ item.applyReason }}</pre>\r\n        </global-info-item>\r\n        <global-info-item v-if=\"item.verifyStatus\"\r\n                          label=\"是否同意申请\">{{ item.verifyStatus === 1 ? '同意申请' : '驳回' }}</global-info-item>\r\n        <global-info-item v-if=\"item.verifyStatus\"\r\n                          :label=\"item.verifyStatus === 1 ? '备注' : '驳回理由'\">\r\n          <pre>{{ item.verifySuggestion }}</pre>\r\n        </global-info-item>\r\n        <global-info-item label=\"跟踪办理答复文件\"\r\n                          v-if=\"item.verifyStatus === 1\">\r\n          <el-link @click=\"isTrackTransactReply = !isTrackTransactReply\"\r\n                   type=\"primary\">填写跟踪办理答复文件</el-link>\r\n        </global-info-item>\r\n      </global-info>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      name=\"跟踪办理审查\">\r\n      <SuggestTrackTransactReview :id=\"id\"\r\n                                  @callback=\"callback\"></SuggestTrackTransactReview>\r\n    </xyl-popup-window>\r\n\r\n    <xyl-popup-window v-model=\"isTrackTransactReply\"\r\n                      name=\"填写跟踪办理答复文件\">\r\n      <SubmitSuggestTrackTransactReply :suggestId=\"props.id\"\r\n                                       :unitId=\"transactId\"\r\n                                       :traceId=\"tracesInfo.id\"\r\n                                       @callback=\"handleCallback\"></SubmitSuggestTrackTransactReply>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestTrackTransactDetail' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated, computed } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport SuggestTrackTransactReview from './SuggestTrackTransactReview.vue'\r\nimport SubmitSuggestTrackTransactReply from '@/views/UnitSuggestDetail/component/SubmitSuggestTrackTransactReply.vue'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  transactUnitObj: { type: Object, default: () => ({}) },\r\n})\r\nconst emit = defineEmits(['refresh'])\r\n\r\nconst tracesInfo = computed(() => props.transactUnitObj?.traces?.filter(v => !v.hasAnswer)[0] || {})\r\nconst transactId = computed(() => props.transactUnitObj?.handlerOffice?.id)\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst dataList = ref([])\r\nconst isTrackTransactReply = ref(false)\r\n\r\n\r\nonActivated(() => { handingPortionTraceList() })\r\n\r\nconst handingPortionTraceList = async () => {\r\n  const res = await api.handingPortionTraceList({ query: { suggestionId: props.id } })\r\n  var { data } = res\r\n  dataList.value = data.filter(v => !v.hasAnswer)\r\n}\r\nconst handleTrackTransact = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst callback = (type) => {\r\n  handingPortionTraceList()\r\n  show.value = false\r\n  isTrackTransactReply.value = false\r\n  if (type) { emit('refresh') }\r\n}\r\nconst handleCallback = () => {\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestTrackTransactDetail {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SuggestTrackTransactDetailNameBody {\r\n    padding: 0 var(--zy-distance-one);\r\n    padding-top: var(--zy-distance-one);\r\n\r\n    .SuggestTrackTransactDetailName {\r\n      width: 100%;\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      position: relative;\r\n      text-align: center;\r\n\r\n      div {\r\n        display: inline-block;\r\n        background-color: #fff;\r\n        position: relative;\r\n        z-index: 2;\r\n        padding: 0 20px;\r\n      }\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 100%;\r\n        height: 1px;\r\n        background-color: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .SuggestTrackTransactDetailBody {\r\n    padding: var(--zy-distance-one);\r\n    padding-bottom: 0;\r\n\r\n    .global-info {\r\n      padding-bottom: 12px;\r\n\r\n      .global-info-item {\r\n        .global-info-label {\r\n          width: 160px;\r\n        }\r\n\r\n        .global-info-content {\r\n          width: calc(100% - 160px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .SuggestTrackTransactUnit {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .zy-el-link {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;EACOA,KAAK,EAAC;AAA4B;;EAMhCA,KAAK,EAAC;AAAgC;;EAK9BA,KAAK,EAAC;AAA0B;;;;;;;uBAX/CC,mBAAA,CAiDM,OAjDNC,UAiDM,G,0BAhDJC,mBAAA,CAIM;IAJDH,KAAK,EAAC;EAAoC,IAC7CG,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAgC,IACzCG,mBAAA,CAAiB,aAAZ,QAAM,E,wBAGfA,mBAAA,CA6BM,OA7BNC,UA6BM,I,kBA5BJH,mBAAA,CA2BcI,SAAA,QAnCpBC,WAAA,CAQkCC,MAAA,CAAAC,QAAQ,EAR1C,UAQ0BC,IAAI;yBAAxBC,YAAA,CA2BcC,sBAAA;MA1BAC,GAAG,EAAEH,IAAI,CAACI;;MAT9BC,OAAA,EAAAC,QAAA,CAUQ;QAAA,OAUmB,CAVnBC,YAAA,CAUmBC,2BAAA;UApB3BH,OAAA,EAAAC,QAAA,CAWU;YAAA,OAOmB,CAPnBC,YAAA,CAOmBE,2BAAA;cAPDC,KAAK,EAAC;YAAM;cAXxCL,OAAA,EAAAC,QAAA,CAYY;gBAAA,OAKM,CALNZ,mBAAA,CAKM,OALNiB,UAKM,GAJJjB,mBAAA,CAAwC,cAAAkB,gBAAA,CAA/BZ,IAAI,CAACa,gBAAgB,kB,CACdb,IAAI,CAACc,YAAY,I,cAAjCb,YAAA,CAEoCc,kBAAA;kBAhBlDZ,GAAA;kBAewBa,OAAK,WAALA,OAAKA,CAAAC,MAAA;oBAAA,OAAEnB,MAAA,CAAAoB,mBAAmB,CAAClB,IAAI;kBAAA;kBAChCmB,IAAI,EAAC;;kBAhB5Bd,OAAA,EAAAC,QAAA,CAgBsC;oBAAA,OAAAc,kBAAA,CAAEC,MAAA,QAAAA,MAAA,OAhBxCC,gBAAA,CAgBsC,IAAE,E;;kBAhBxCC,CAAA;oEAAAC,mBAAA,e;;cAAAD,CAAA;0CAmBUhB,YAAA,CAA+EE,2BAAA;cAA7DC,KAAK,EAAC;YAAM;cAnBxCL,OAAA,EAAAC,QAAA,CAmByC;gBAAA,OAA6B,CAnBtEgB,gBAAA,CAAAV,gBAAA,CAmB4Cd,MAAA,CAAA2B,MAAM,CAACzB,IAAI,CAAC0B,UAAU,kB;;cAnBlEH,CAAA;;;UAAAA,CAAA;sCAqBQhB,YAAA,CAEmBE,2BAAA;UAFDC,KAAK,EAAC;QAAM;UArBtCL,OAAA,EAAAC,QAAA,CAsBU;YAAA,OAAiC,CAAjCZ,mBAAA,CAAiC,aAAAkB,gBAAA,CAAzBZ,IAAI,CAAC2B,WAAW,iB;;UAtBlCJ,CAAA;sCAwBgCvB,IAAI,CAACc,YAAY,I,cAAzCb,YAAA,CACiGQ,2BAAA;UAzBzGN,GAAA;UAyB0BO,KAAK,EAAC;;UAzBhCL,OAAA,EAAAC,QAAA,CAyByC;YAAA,OAA6C,CAzBtFgB,gBAAA,CAAAV,gBAAA,CAyB4CZ,IAAI,CAACc,YAAY,uC;;UAzB7DS,CAAA;wCAAAC,mBAAA,gBA0BgCxB,IAAI,CAACc,YAAY,I,cAAzCb,YAAA,CAGmBQ,2BAAA;UA7B3BN,GAAA;UA2B2BO,KAAK,EAAEV,IAAI,CAACc,YAAY;;UA3BnDT,OAAA,EAAAC,QAAA,CA4BU;YAAA,OAAsC,CAAtCZ,mBAAA,CAAsC,aAAAkB,gBAAA,CAA9BZ,IAAI,CAAC4B,gBAAgB,iB;;UA5BvCL,CAAA;0DAAAC,mBAAA,gBA+BgCxB,IAAI,CAACc,YAAY,U,cADzCb,YAAA,CAImBQ,2BAAA;UAlC3BN,GAAA;UA8B0BO,KAAK,EAAC;;UA9BhCL,OAAA,EAAAC,QAAA,CAgCU;YAAA,OAC4C,CAD5CC,YAAA,CAC4CQ,kBAAA;cADlCC,OAAK,EAAAK,MAAA,QAAAA,MAAA,gBAAAJ,MAAA;gBAAA,OAAEnB,MAAA,CAAA+B,oBAAoB,IAAI/B,MAAA,CAAA+B,oBAAoB;cAAA;cACpDV,IAAI,EAAC;;cAjCxBd,OAAA,EAAAC,QAAA,CAiCkC;gBAAA,OAAAc,kBAAA,CAAUC,MAAA,QAAAA,MAAA,OAjC5CC,gBAAA,CAiCkC,YAAU,E;;cAjC5CC,CAAA;;;UAAAA,CAAA;cAAAC,mBAAA,e;;MAAAD,CAAA;;oCAqCIhB,YAAA,CAImBuB,2BAAA;IAzCvBC,UAAA,EAqC+BjC,MAAA,CAAAkC,IAAI;IArCnC,uBAAAX,MAAA,QAAAA,MAAA,gBAAAJ,MAAA;MAAA,OAqC+BnB,MAAA,CAAAkC,IAAI,GAAAf,MAAA;IAAA;IACbgB,IAAI,EAAC;;IAtC3B5B,OAAA,EAAAC,QAAA,CAuCM;MAAA,OAC8E,CAD9EC,YAAA,CAC8ET,MAAA;QADjDM,EAAE,EAAEN,MAAA,CAAAM,EAAE;QACN8B,UAAQ,EAAEpC,MAAA,CAAAqC;;;IAxC7CZ,CAAA;qCA2CIhB,YAAA,CAMmBuB,2BAAA;IAjDvBC,UAAA,EA2C+BjC,MAAA,CAAA+B,oBAAoB;IA3CnD,uBAAAR,MAAA,QAAAA,MAAA,gBAAAJ,MAAA;MAAA,OA2C+BnB,MAAA,CAAA+B,oBAAoB,GAAAZ,MAAA;IAAA;IAC7BgB,IAAI,EAAC;;IA5C3B5B,OAAA,EAAAC,QAAA,CA6CM;MAAA,OAG8F,CAH9FC,YAAA,CAG8FT,MAAA;QAH5DsC,SAAS,EAAEtC,MAAA,CAAAuC,KAAK,CAACjC,EAAE;QACnBkC,MAAM,EAAExC,MAAA,CAAAyC,UAAU;QAClBC,OAAO,EAAE1C,MAAA,CAAA2C,UAAU,CAACrC,EAAE;QACtB8B,UAAQ,EAAEpC,MAAA,CAAA4C;;;IAhDlDnB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}