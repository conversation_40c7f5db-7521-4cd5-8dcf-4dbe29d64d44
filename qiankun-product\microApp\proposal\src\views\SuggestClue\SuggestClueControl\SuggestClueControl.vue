<template>
  <div class="SuggestClueControl">
    <xyl-search-button @queryClick="handleQuery"
                       @resetClick="handleReset"
                       @handleButton="handleButton"
                       :buttonList="buttonList">
      <template #search>
        <el-input v-model="keyword"
                  placeholder="请输入关键词"
                  @keyup.enter="handleQuery"
                  clearable />
        <el-select v-model="proposalClueType"
                   @change="queryChange"
                   placeholder="请选择线索类别"
                   clearable>
          <el-option v-for="item in proposalClueTypeData"
                     :key="item.id"
                     :label="item.label"
                     :value="item.id" />
        </el-select>
      </template>

    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef"
                row-key="id"
                :data="tableData"
                @select="handleTableSelect"
                @select-all="handleTableSelect">
        <el-table-column type="selection"
                         reserve-selection
                         width="60"
                         fixed />
        <el-table-column label="标题"
                         min-width="280"
                         show-overflow-tooltip>
          <template #default="scope">
            <el-link @click="handleDetail(scope.row)"
                     type="primary">{{ scope.row.title }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="线索类别"
                         min-width="120"
                         prop="proposalClueType.label" />
        <el-table-column label="线索来源"
                         width="120"
                         prop="terminalName" />
        <el-table-column label="提供者"
                         min-width="120"
                         prop="furnishName" />
        <el-table-column label="联系电话"
                         min-width="180"
                         prop="furnishMobile" />
        <el-table-column label="是否选登"
                         min-width="120">
          <template #default="scope">
            <el-icon
                     :class="[!scope.row.ifPublish ? 'globalTableClock' : scope.row.ifPublish === 1 ? 'globalTableCheck' : 'globalTableClose']">
              <CircleCheck v-if="scope.row.ifPublish === 1" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column label="提供时间"
                         width="190">
          <template #default="scope">{{ format(scope.row.createDate) }}</template>
        </el-table-column>
        <el-table-column label="采纳次数"
                         width="120"
                         prop="useTimes" />
        <el-table-column width="180"
                         fixed="right"
                         class-name="globalTableCustom">
          <template #header>操作</template>
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)"
                       type="primary"
                       plain>编辑</el-button>
            <el-button v-if="scope.row.ifPublish == 1"
                       @click="handleSubmit(scope.row)"
                       type="primary"
                       plain>撤回</el-button>
            <el-button v-else
                       @click="handleSubmit(scope.row)"
                       type="primary"
                       plain>选登</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo"
                     v-model:page-size="pageSize"
                     :page-sizes="pageSizes"
                     layout="total, sizes, prev, pager, next, jumper"
                     @size-change="handleQuery"
                     @current-change="handleQuery"
                     :total="totals"
                     background />
    </div>
    <xyl-popup-window v-model="show"
                      :name="id ? '编辑线索' : '新增线索'">
      <SuggestClueSubmit :id="id"
                         @callback="callback"></SuggestClueSubmit>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      name="提案线索详情">
      <SuggestClueDetails :id="id"></SuggestClueDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestClueControl' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated } from 'vue'
import { format } from 'common/js/time.js'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { clueExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage } from 'element-plus'
import SuggestClueSubmit from './components/SuggestClueSubmit'
import SuggestClueDetails from './components/SuggestClueDetails'
const buttonList = [
  { id: 'new', name: '新增', type: 'primary', has: 'new' },
  { id: 'exportWord', name: '导出Word', type: 'primary', has: 'export' },
  { id: 'del', name: '删除', type: '', has: 'del' }
]
const id = ref('')
const show = ref(false)
const detailsShow = ref(false)
const proposalClueType = ref('')
const proposalClueTypeData = ref([])
const {
  keyword,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  tableQuery,
  handleQuery,
  handleTableSelect,
  handleDel,
  tableRefReset,
  handleGetParams
} = GlobalTable({ tableApi: 'proposalClueList', delApi: 'proposalClueDel' })

onActivated(() => {
  handleQuery()
  dictionaryData()
})
const dictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: ['proposal_clue_type'] })
  proposalClueTypeData.value = data.proposal_clue_type
}
const handleButton = (isType) => {
  switch (isType) {
    case 'new':
      handleNew()
      break
    case 'del':
      handleDel('提案线索')
      break
    case 'exportWord':
      clueExportWord(handleGetParams())
      break
    default:
      break
  }
}

const handleReset = () => {
  keyword.value = ''
  proposalClueType.value = ''
  tableQuery.value = { query: { proposalClueType: proposalClueType.value || null } }
  handleQuery()
}
const handleNew = () => {
  id.value = ''
  show.value = true
}
const handleEdit = (item) => {
  id.value = item.id
  show.value = true
}
const handleSubmit = (item) => {
  globalJson(item)
}

const globalJson = async (row) => {
  const { code } = await api.globalJson('/proposalClue/edit', { form: { id: row.id, ifPublish: row.ifPublish ? '0' : '1' }, })
  if (code === 200) {
    ElMessage({ type: 'success', message: row.ifPublish ? '成功撤回' : '选登成功' })
    callback()
  }
}
const handleDetail = (item) => {
  id.value = item.id
  detailsShow.value = true
}

const queryChange = () => {
  tableQuery.value = { query: { proposalClueType: proposalClueType.value || null } }
}

const callback = () => {
  show.value = false
  tableRefReset()
  handleQuery()
}

</script>
<style lang="scss">
.SuggestClueControl {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .xyl-search-button {
    .xyl-button {
      width: calc(100% - 660px);
    }

    .xyl-search {
      width: 660px;

      .zy-el-select {
        margin-left: 20px;
      }
    }
  }

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
