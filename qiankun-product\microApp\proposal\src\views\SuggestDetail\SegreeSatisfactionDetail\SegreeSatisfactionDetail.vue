<template>
  <div class="SegreeSatisfactionDetail">
    <div class="SegreeSatisfactionDetailName">提案办理情况征求意见表</div>
    <global-info>
      <global-info-item label="提案标题">{{ info.title }}</global-info-item>
      <global-info-line>
        <global-info-item label="提案编号">{{ info.serialNumber }}</global-info-item>
        <global-info-item label="领衔委员">{{ info.suggestUserName }}</global-info-item>
      </global-info-line>
      <global-info-item label="单位及职务">{{ info.submitUserInfo?.position }}</global-info-item>
      <global-info-item label="办理单位">
        <div v-for="item in info.handleOffices"
             :key="item.handleOfficeId">
          {{ item.handleOfficeType === 'main' ? '主办' : item.handleOfficeType === 'assist' ? '协办' : '分办' }}：{{
            item.handleOfficeName }}
        </div>
      </global-info-item>
      <global-info-item label="提案办理情况"
                        class="SegreeSatisfactionDetailInfo">
        <global-info-item label="联系沟通情况"
                          class="SegreeSatisfactionDetailInfo">
          <global-info-item label="电话/电邮沟通">{{ details.mobileCommunication }}</global-info-item>
          <global-info-item label="信函沟通">{{ details.letterCommunication }}</global-info-item>
          <global-info-item label="当面沟通">{{ details.faceCommunication }}</global-info-item>
          <global-info-item label="未联系">{{ details.nonCommunication }}</global-info-item>
        </global-info-item>
        <global-info-item label="办理态度">{{ details.handleMannerName }}</global-info-item>
        <global-info-item label="办理结果">{{ details.handleResultName }}</global-info-item>
      </global-info-item>
      <global-info-item label="对提案工作的建议">
        <pre>{{ details.content }}</pre>
      </global-info-item>
    </global-info>
  </div>
</template>
<script>
export default { name: 'SegreeSatisfactionDetail' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
const props = defineProps({ id: { type: String, default: '' }, suggestId: { type: String, default: '' }, type: { type: Boolean, default: false } })

const info = ref({})
const details = ref({})


onMounted(() => {
  suggestionInfo()
  if (props.id) { suggestionSatisfactionInfo() }
  if (props.type) { suggestionSatisfactiondetails() }
})

const suggestionInfo = async () => {
  const { data } = await api.suggestionInfo({ detailId: props.suggestId })
  info.value = data
}
const suggestionSatisfactionInfo = async () => {
  const { data } = await api.suggestionSatisfactionInfo({ detailId: props.id })
  details.value = data
}
const suggestionSatisfactiondetails = async () => {
  const { data } = await api.suggestionSatisfactionInfo({ suggestionId: props.suggestId })
  details.value = data
}
</script>
<style lang="scss">
.SegreeSatisfactionDetail {
  width: 990px;
  padding: 0 var(--zy-distance-one);
  padding-top: var(--zy-distance-one);

  .SegreeSatisfactionDetailName {
    font-size: var(--zy-title-font-size);
    font-weight: bold;
    color: var(--zy-el-color-primary);
    border-bottom: 1px solid var(--zy-el-color-primary);
    text-align: center;
    padding: 20px 0;
    margin-bottom: 20px;
  }

  .global-info {
    padding-bottom: 12px;

    .global-info-item {
      .global-info-label {
        width: 160px;
      }

      .global-info-content {
        width: calc(100% - 160px);
      }
    }

    .SegreeSatisfactionDetailInfo {
      &>.global-info-item {
        &>.global-info-content {
          padding: 0;
          border: 0;

          &>span {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
