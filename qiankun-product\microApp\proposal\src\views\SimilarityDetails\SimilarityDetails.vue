<template>
  <div class="SimilarityDetails" v-loading="loading">
    <div class="globalTable">
      <el-table :data="tableData">
        <el-table-column label="当前提案" min-width="280" prop="paragraph_one">
          <template #default="scope">
            <div class="SimilarityDetailsItem" v-html="scope.row.paragraph_one"></div>
          </template>
        </el-table-column>
        <el-table-column label="相似提案" min-width="280" prop="paragraph_two">
          <template #default="scope">
            <div class="SimilarityDetailsItem" v-html="scope.row.paragraph_two"></div>
          </template>
        </el-table-column>
        <el-table-column label="相似度" min-width="120" prop="similarity">
          <template #default="scope">
            <div class="SimilarityDetailsItem">{{ scope.row.similarity }}%</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default { name: 'SimilarityDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, onActivated } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const loading = ref(false)
const detailsOne = ref({})
const detailsTwo = ref({})
const tableData = ref([])
onMounted(() => {
  console.log(route.query, 'route.query')
  if (!loading.value && route.query.oneId && route.query.twoId) {
    loading.value = true
    suggestionInfoOne()
  }
})
onActivated(() => {
  console.log(route.query, 'route.query')
  if (!loading.value && route.query.oneId && route.query.twoId) {
    loading.value = true
    suggestionInfoOne()
  }
})
const suggestionInfoOne = async () => {
  const { data: dataOne } = await api.suggestionInfo({ detailId: route.query.oneId })
  const { data: dataTwo } = await api.suggestionInfo({ detailId: route.query.twoId })
  detailsOne.value = dataOne
  detailsTwo.value = dataTwo
  compareSimilarRates()
}

const compareSimilarRates = async () => {
  const params = {
    // text1: detailsOne.value.content.replace(/<[^>]*>/g, '').replace(/&ldquo;/ig, '“').replace(/&rdquo;/ig, '”'),
    // text2: detailsTwo.value.content.replace(/<[^>]*>/g, '').replace(/&ldquo;/ig, '“').replace(/&rdquo;/ig, '”')
    text1: detailsOne.value.content,
    text2: detailsTwo.value.content
  }
  const { data } = await api.compareSimilarRates(params)
  console.log(data, '处理后的数据')
  tableData.value = data.map(item => ({
    ...item,
    paragraph_one: item.paragraph_one.replace(/<[^>]*>/g, ''),
    paragraph_two: item.paragraph_two.replace(/<[^>]*>/g, '')
  }))
  loading.value = false
}
</script>
<style lang="scss">
.SimilarityDetails {
  width: 100%;
  height: 100%;
  padding: var(--zy-distance-four);

  .globalTable {
    width: 100%;
    height: 100%;

    .zy-el-table {
      .zy-el-table__cell {
        border-right: 1px solid var(--zy-el-table-border-color) !important;
        border-bottom: 1px solid var(--zy-el-table-border-color) !important;
      }
    }
  }
}
</style>
