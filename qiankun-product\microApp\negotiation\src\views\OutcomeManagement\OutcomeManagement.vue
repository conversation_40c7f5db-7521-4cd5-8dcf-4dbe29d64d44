<template>
  <div class="OutcomeManagement">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
        <xyl-date-picker v-model="year" placeholder="请选择年份" @change="queryChange" value-format="YYYY" type="year" />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"></xyl-global-table>
        <xyl-global-table-button :data="tableButtonList" :max="13" :elWhetherShow="handleElWhetherShow"
          @buttonClick="handleCommand" :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="showExportExcel" name="导出Excel">
      <xyl-export-excel tableId="id_micro_advice" module="microAdviceManageExcel" name="微建议管理" :exportId="exportId"
        :params="exportExcelParams" @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="show" :name="id ? '编辑微建议' : '新增微建议'">
      <SubmitMinSuggestManage :id="id" @callback="submitCallback"></SubmitMinSuggestManage>
    </xyl-popup-window>
    <xyl-popup-window v-model="examineShow" :name="nextNodeId ? '不予受理' : '审核'">
      <SubmitExamine :id="id" width="800px" :nextNodeId="nextNodeId" @callback="examineCallback"></SubmitExamine>
    </xyl-popup-window>
    <xyl-popup-window v-model="handleShow" name="交办">
      <SubmitHandle :id="id" @callback="examineCallback"></SubmitHandle>
    </xyl-popup-window>
    <xyl-popup-window v-model="replyManageShow" name="回复管理">
      <SubmitReplyManage :id="id" @callback="examineCallback"></SubmitReplyManage>
    </xyl-popup-window>
    <xyl-popup-window v-model="replyShow" name="回复">
      <SubmitReply :id="id" userType="manageReply" @callback="examineCallback"></SubmitReply>
    </xyl-popup-window>
    <xyl-popup-window v-model="joinManageShow" name="协办管理">
      <SubmitJoinManage :id="id" @callback="examineCallback"></SubmitJoinManage>
    </xyl-popup-window>
    <xyl-popup-window v-model="applyShow" :name="applyName">
      <SubmitApplyChange :id="id" :type="applyType" :disabled="disabled" @callback="examineCallback">
      </SubmitApplyChange>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'OutcomeManagement' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated } from 'vue'
// import { format } from 'common/js/time.js'
import { GlobalTable } from 'common/js/GlobalTable.js'
// import { exportWordHtmlList } from 'common/config/MicroGlobal'
// import { publicOpinionExportWord } from '@/assets/js/publicOpinionExportWord'
// import SubmitMinSuggestManage from './component/SubmitMinSuggestManage';
// import SubmitHandle from "./component/SubmitHandle";
// import SubmitExamine from "./component/SubmitExamine";
// import SubmitReply from "./component/SubmitReply";
// import SubmitApplyChange from './component/SubmitApplyChange';
// import SubmitReplyManage from "./component/SubmitReplyManage";
// import SubmitJoinManage from "./component/SubmitJoinManage";
import { ElMessage, ElMessageBox } from 'element-plus'
import { qiankunMicro } from "common/config/MicroGlobal";
import { useRoute } from 'vue-router'
const route = useRoute()
const tableButtonList = [{ id: 'showEdit', name: '编辑', width: 30, has: '', whetherShow: true },
{ id: 'showAudit', name: '审核', width: 30, has: '', whetherShow: true },
{ id: 'showReply', name: '回复', width: 30, has: '', whetherShow: true },
{ id: 'showHandle', name: '交办', width: 30, has: '', whetherShow: true },
{ id: 'showApplyChange', name: '申请调整', width: 40, has: '', whetherShow: true },
{ id: 'showBack', name: '退回', width: 30, has: '', whetherShow: true },
{ id: 'showReject', name: '不予处理', width: 40, has: '', whetherShow: true },
{ id: 'showChangeAudit', name: '申诉审核', width: 40, has: '', whetherShow: true },
{ id: 'showReplyManage', name: '回复管理', width: 40, has: '', whetherShow: true },
{ id: 'showJoinManage', name: '协办', width: 30, has: '', whetherShow: true },
{ id: 'showEvaluationDetail', name: '查看评价', width: 40, has: '', whetherShow: true },
{ id: 'showCancelColar', name: '取消领办', width: 40, has: '', whetherShow: true }
]
const buttonList = [
  // { id: 'new', name: '新增', type: 'primary', has: '' },
  { id: 'export', name: '导出excel', type: 'primary', has: 'export' },
  { id: 'exportWord', name: '导出word', type: 'primary', has: 'export' },
  { id: 'del', name: '删除', type: '', has: 'del' },
  { id: 'batchToAudit', name: '一键还原待审核', type: '', has: 'batch_audit' },
  { id: 'batchToReply', name: '一键还原未回复', type: '', has: 'batch_reply' },
]
const showExportExcel = ref(false)
const id = ref('')
const exportId = ref([])
const show = ref(false)
const examineShow = ref(false)
const replyManageShow = ref(false)
const handleShow = ref(false)
const replyShow = ref(false)
const applyShow = ref(false)
const joinManageShow = ref(false)
const hasReply = ref('')
const applyName = ref('')
const applyType = ref('')
const nextNodeId = ref('')
const disabled = ref(false)
const hasEvaluation = ref('')
const year = ref('')
const exportExcelParams = ref({})

const {
  keyword,
  tableRef,
  queryRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  exportShow,
  tableDataArray,
  handleQuery,
  handleTableSelect,
  handleSortChange,
  handleHeaderClass,
  handleEditorCustom,
  // handleGetParams,
  tableHead,
  tableQuery,
} = GlobalTable(
  {
    tableId: 'id_micro_negotiate',
    tableApi: 'microAdviceList',
    delApi: 'microAdviceDels',
    tableDataObj: {
      orderBys: [
        {
          columnId: 'id_micro_advice_create_date',
          isDesc: 1
        }
      ],
      query: {
        curry: '2'
      }
    }
  })
const ids = ref([])

onActivated(() => {
  if (route.query.id) {
    ids.value = JSON.parse(route.query.id)
    tableQuery.value = { ids: ids.value }
  }
  handleQuery()
})

const handleElWhetherShow = (row, isType) => {
  if (isType === 'showEdit') {
    return row.showEdit === 1
  } else if (isType === 'showAudit') {
    return row.showAudit === 1
  } else if (isType === 'showReply') {
    return row.showReply === 1
  } else if (isType === 'showHandle') {
    return row.showHandle === 1
  } else if (isType === 'showApplyChange') {
    return row.showApplyChange === 1
  } else if (isType === 'showBack') {
    return row.showBack === 1
  } else if (isType === 'showReject') {
    return row.showReject === 1
  } else if (isType === 'showChangeAudit') {
    return row.showChangeAudit === 1
  } else if (isType === 'showReplyManage') {
    return row.showReplyManage === 1
  } else if (isType === 'showJoinManage') {
    return row.showJoinManage === 1
  } else if (isType === 'showEvaluationDetail') {
    return row.showEvaluationDetail === 1
  } else if (isType === 'showCancelColar') {
    return row.showCancelColar === 1
  }
}

const handleCommand = (row, isType) => {
  switch (isType) {
    case 'showEdit':
      handleEdit(row)
      break
    case 'showAudit':
      handleExamine(row)
      break
    case 'showReply':
      handleReply(row)
      break
    case 'showHandle':
      onHandle(row)
      break
    case 'showApplyChange':
      handleApply(row)
      break
    case 'showBack':
      handleBack(row)
      break
    case 'showReject':
      handleReject(row)
      break
    case 'showChangeAudit':
      handleChangeAudit(row)
      break
    case 'showReplyManage':
      handleReplyManage(row)
      break
    case 'showJoinManage':
      handleJoinManage(row)
      break
    case 'showEvaluationDetail':
      handleEvaluationDetail(row)
      break
    case 'showCancelColar':
      handleCancelColar(row)
      break
    default:
      break
  }
}

const handleButton = (id) => {
  switch (id) {
    case 'new':
      handleNew()
      break
    case 'del':
      handleDel()
      break
    case 'export':
      exportExcel()
      break
    case 'batchToAudit':
      batchToAudit()
      break
    case 'exportWord':
      // handleExportWord()
      // publicOpinionExportWord(handleGetParams(), 'microAdviceList')
      break
    case 'batchToReply':
      batchToReply()
      break
    default:
      break
  }
}

const handleDel = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将删除当前选中的数据, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => { globalDel() }).catch(() => { ElMessage({ type: 'info', message: `已取消删除` }) })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const globalDel = async () => {
  const { code, data } = await api.microAdviceDels({ ids: tableDataArray.value.map(v => v.id) })
  if (code === 200) {
    ElMessage({ type: 'success', dangerouslyUseHTMLString: true, message: data })
    tableDataArray.value = []
    tableRef.value.clearSelection()
    handleQuery()
  }
}

const exportExcel = () => {
  exportId.value = tableDataArray.value.map(item => item.id)
  exportExcelParams.value = {
    where: queryRef.value.getWheres(),
    ids: route.query.ids ? JSON.parse(route.query.ids) : []
  }
  showExportExcel.value = true
}


const batchToReply = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将一键还原未回复当前选中的数据, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const { code, data } = await api.batchToUnAnswer({ ids: tableDataArray.value.map(v => v.id) })
      if (code === 200) {
        ElMessage({ type: 'success', dangerouslyUseHTMLString: true, message: data })
        handleQuery()
      }
    }).catch(() => { ElMessage({ type: 'info', message: `已取消一键还原待未回复` }) })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}

const batchToAudit = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将一键还原待审核当前选中的数据, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const { code, data } = await api.batchToAudit({ ids: tableDataArray.value.map(v => v.id) })
      if (code === 200) {
        ElMessage({ type: 'success', dangerouslyUseHTMLString: true, message: data })
        handleQuery()
      }
    }).catch(() => { ElMessage({ type: 'info', message: `已取消一键还原待审核` }) })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}

const handleReset = () => {
  keyword.value = ''
  hasReply.value = ''
  hasEvaluation.value = ''
  year.value = ''
  tableQuery.value = { year: year.value || null }
  handleQuery()
}

const queryChange = () => {
  tableQuery.value = { year: year.value || null }
}

const handleNew = () => {
  // id.value = ''
  // show.value = true
  qiankunMicro.setGlobalState({ openRoute: { name: `微建议新增`, path: '/negotiation/OutcomeReportingNew' } })
}

const handleApply = (item) => {
  applyName.value = '申请调整'
  applyType.value = 'applyChange'
  id.value = item.id
  applyShow.value = true
}
const handleBack = (item) => {
  applyName.value = '退回'
  applyType.value = 'backUnder'
  id.value = item.id
  applyShow.value = true
}

const handleChangeAudit = (item) => {
  applyName.value = '申诉审核'
  applyType.value = 'changeAudit'
  id.value = item.id
  applyShow.value = true
}
const handleEvaluationDetail = (item) => {
  applyName.value = '查看评价'
  applyType.value = 'memberEvaluation'
  disabled.value = true
  id.value = item.id
  applyShow.value = true
}

const handleJoinManage = (item) => {
  id.value = item.id
  joinManageShow.value = true
}

const handleExamine = (item) => { // 审核
  // id.value = item.id
  // examineShow.value = true
  qiankunMicro.setGlobalState({ openRoute: { name: `成果详情`, path: '/negotiation/OutcomeManageDetails', query: { id: item.id } } })
}

const handleReject = (item) => { // 不予受理
  id.value = item.id
  nextNodeId.value = 'passAudit'
  examineShow.value = true
}

const handleReply = (item) => { // 回复
  // id.value = item.id
  // replyShow.value = true
  qiankunMicro.setGlobalState({ openRoute: { name: `成果详情`, path: '/negotiation/OutcomeManageDetails', query: { id: item.id, userType: 'manageReply' } } })
}

const handleReplyManage = (item) => { // 回复管理
  id.value = item.id
  replyManageShow.value = true
}

const onHandle = (item) => { // 交办
  // id.value = item.id
  // handleShow.value = true
  qiankunMicro.setGlobalState({ openRoute: { name: `成果详情`, path: '/negotiation/OutcomeManageDetails', query: { id: item.id } } })
}
const handleEdit = (item) => {
  // id.value = item.id
  // show.value = true
  qiankunMicro.setGlobalState({ openRoute: { name: `编辑成果`, path: '/negotiation/OutcomeReportingNew', query: { id: item.id } } })
}

const submitCallback = () => { // 新增编辑
  show.value = false
  handleQuery()
}

const callback = () => {
  showExportExcel.value = false
  exportShow.value = false
  handleQuery()
}

const examineCallback = () => { // 审核回调
  examineShow.value = false
  handleShow.value = false
  replyManageShow.value = false
  replyShow.value = false
  joinManageShow.value = false
  applyShow.value = false
  handleQuery()
}

const handleCancelColar = (item) => {
  ElMessageBox.confirm(`是否取消领办?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const { code } = await api.complete({ microAdviceId: item.id, nextNodeId: 'cancelColar' })
    if (code === 200) {
      ElMessage({ type: 'success', message: `取消领办成功` })
      handleQuery()
    }
  }).catch(() => { ElMessage({ type: 'info', message: `已取消` }) })
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      qiankunMicro.setGlobalState({ openRoute: { name: `成果详情`, path: '/negotiation/OutcomeManageDetails', query: { id: row.id, userType: row.showReply ? 'manageReply' : '' } } })
      break
    case 'comment':
      qiankunMicro.setGlobalState({ openRoute: { name: `评论`, path: '/minSuggest/MinSuggestComment', query: { id: row.id, type: 'min_suggest' } } })
      break
    default:
      break
  }
}

</script>
<style lang="scss">
.OutcomeManagement {
  width: 100%;
  height: 100%;
  padding: 10px 20px;

  .xyl-search {
    width: 460px;

    .zy-el-date-editor {
      //width: 120px;
      margin-left: 20px;
    }

    .zy-el-select,
    .zy-el-date-editor {
      margin-left: 20px;
    }
  }

  .globalTable {
    width: 100%;
    height: calc(100% - 116px);
  }
}
</style>
