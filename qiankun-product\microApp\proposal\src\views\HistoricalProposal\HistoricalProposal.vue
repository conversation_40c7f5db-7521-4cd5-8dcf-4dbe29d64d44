<template>
  <div class="HistoricalProposal">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" searchPopover :buttonNumber="4">
      <template #search>
        <el-input v-model="serialNumber" placeholder="请输入案号" @change="queryChange" clearable />
        <el-input v-model="keyword" placeholder="请输入标题" @change="queryChange" clearable />
      </template>
      <template #searchPopover>
        <el-date-picker v-model="time" type="datetimerange" value-format="x" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" @change="queryChange">
        </el-date-picker>
        <el-input v-model="suggestUserId" placeholder="请输入提案者" @change="queryChange" clearable />
        <el-input v-model="handlingUnit" placeholder="请输入办理单位名称" @change="queryChange" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <el-table-column label="案号" width="80" prop="serialNumber" />
        <el-table-column label="标题" min-width="220" prop="proposalName" show-overflow-tooltip>
          <template #default="scope">
            <el-link type="primary" @click="handleDetails(scope.row)">{{ scope.row.proposalName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="120" prop="processStatus" show-overflow-tooltip />
        <el-table-column label="提案者" min-width="120" prop="suggestUserId" show-overflow-tooltip />
        <el-table-column label="届次" min-width="140" prop="termYearId" show-overflow-tooltip />
        <el-table-column label="办理单位" min-width="140" prop="handlingUnit" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.handlingUnit }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="办理情况" min-width="200" prop="handlingContent" /> -->
        <!-- <el-table-column label="委员评价" min-width="120" prop="memberEvaluationName" /> -->
        <el-table-column label="提交时间" min-width="160" prop="submitDate">
          <template #default="scope">{{ format(scope.row.submitDate, 'YYYY-MM-DD') }}</template>
        </el-table-column>
        <xyl-global-table-button :data="tableButtonList" @buttonClick="handleCommand"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="历史提案" :exportId="exportId" :params="exportParams" module="propProposalHistoricalExcel"
        @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="show" :name="id ? '编辑' : '新增'">
      <HistoricalProposalNew :id="id" @callback="callback"></HistoricalProposalNew>
    </xyl-popup-window>
    <xyl-popup-window v-model="infoShow" name="详情">
      <HistoricalProposalDetails :id="infoId"></HistoricalProposalDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'HistoricalProposal' }
</script>
<script setup>
import { ref, onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import HistoricalProposalNew from './HistoricalProposalNew'
import HistoricalProposalDetails from './HistoricalProposalDetails'
import { format } from 'common/js/time.js'
import { batchDownloadFile } from 'common/config/MicroGlobal'
import { ElMessage, ElMessageBox } from 'element-plus'
const buttonList = [
  { id: 'new', name: '新增', type: 'primary', has: '' },
  { id: 'del', name: '删除', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' },
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' }
]
const tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: '' }]
const id = ref('')
const show = ref(false)
const serialNumber = ref('')
const suggestUserId = ref('')
const handlingUnit = ref('')
const time = ref([])
const infoShow = ref(false)
const infoId = ref('')
const {
  keyword,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleTableSelect,
  handleDel,
  tableRefReset,
  tableQuery,
  handleExportExcel,
  // handleGetParams
} = GlobalTable({ tableApi: 'proposalHistoryV2List', delApi: 'proposalHistoryV2Dels' })

onActivated(() => {
  handleQuery()
})
const handleButton = (id) => {
  switch (id) {
    case 'new':
      handleNew()
      break
    case 'del':
      handleDel('单位')
      break
    case 'export':
      handleExportExcel()
      break
    case 'exportWord':
      if (tableDataArray.value.length) {
        var selectIds = tableDataArray.value.flatMap(item => [...(item.fileInfoList?.map(file => file.id) || []), ...(item.replyFileInfoList?.map(file => file.id) || [])]).filter(Boolean)
        batchDownloadFile({ params: selectIds, fileSize: 0, fileName: '历史提案文件.zip', fileType: 'zip' })
      } else {
        var allIds = tableData.value.flatMap(item => [...(item.fileInfoList?.map(file => file.id) || []), ...(item.replyFileInfoList?.map(file => file.id) || [])]).filter(Boolean)
        ElMessageBox.confirm('当前没有选择历史提案，是否根据列表筛选条件导出所有数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          batchDownloadFile({ params: allIds, fileSize: 0, fileName: '历史提案文件.zip', fileType: 'zip' })
        }).catch(() => {
          ElMessage({ type: 'info', message: '已取消导出' })
        })
      }
      break
    default:
      break
  }
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  infoId.value = item.id
  infoShow.value = true
}
const handleReset = () => {
  keyword.value = ''
  serialNumber.value = ''
  suggestUserId.value = ''
  handlingUnit.value = ''
  time.value = []
  tableQuery.value = {
    startTime: time.value[0] || null,
    endTime: time.value[1] || null,
    query: {
      serialNumber: serialNumber.value || null,
      suggestUserId: suggestUserId.value || null,
      handlingUnit: handlingUnit.value || null
    }
  }
  handleQuery()
}
const handleNew = () => {
  id.value = ''
  show.value = true
}
const handleEdit = (item) => {
  id.value = item.id
  show.value = true
}
const queryChange = () => {
  tableQuery.value = {
    startTime: time.value[0] || null,
    endTime: time.value[1] || null,
    query: {
      serialNumber: serialNumber.value || null,
      suggestUserId: suggestUserId.value || null,
      handlingUnit: handlingUnit.value || null
    }
  }
}
const callback = () => {
  tableRefReset()
  handleQuery()
  show.value = false
  exportShow.value = false
}
</script>
<style lang="scss">
.HistoricalProposal {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }

  .zy-el-input {
    margin-left: 15px;
  }
}
</style>
