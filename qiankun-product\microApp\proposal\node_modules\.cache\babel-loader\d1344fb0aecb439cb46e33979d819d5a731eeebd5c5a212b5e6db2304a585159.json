{"ast": null, "code": "import { onActivated } from 'vue';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { suggestExportWord } from '@/assets/js/suggestExportWord';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'PersonalAllSuggest'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [\n    // { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\n    {\n      id: 'export',\n      name: '导出Excel',\n      type: 'primary',\n      has: ''\n    }];\n    var _GlobalTable = GlobalTable({\n        tableId: 'id_prop_proposal_member_view',\n        tableApi: 'suggestionList'\n      }),\n      keyword = _GlobalTable.keyword,\n      queryRef = _GlobalTable.queryRef,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableHead = _GlobalTable.tableHead,\n      tableData = _GlobalTable.tableData,\n      exportId = _GlobalTable.exportId,\n      exportParams = _GlobalTable.exportParams,\n      exportShow = _GlobalTable.exportShow,\n      handleQuery = _GlobalTable.handleQuery,\n      handleSortChange = _GlobalTable.handleSortChange,\n      handleHeaderClass = _GlobalTable.handleHeaderClass,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      tableRefReset = _GlobalTable.tableRefReset,\n      handleGetParams = _GlobalTable.handleGetParams,\n      handleEditorCustom = _GlobalTable.handleEditorCustom,\n      handleExportExcel = _GlobalTable.handleExportExcel;\n    onActivated(function () {\n      handleQuery();\n    });\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'exportWord':\n          suggestExportWord(handleGetParams());\n          break;\n        case 'export':\n          handleExportExcel();\n          break;\n        default:\n          break;\n      }\n    };\n    var handleTableClick = function handleTableClick(key, row) {\n      switch (key) {\n        case 'details':\n          handleDetails(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleDetails = function handleDetails(item) {\n      if (item.ownOrJoin) {\n        qiankunMicro.setGlobalState({\n          openRoute: {\n            name: '提案详情',\n            path: '/proposal/SuggestDetail',\n            query: {\n              id: item.id\n            }\n          }\n        });\n      } else {\n        var _item$suggestOpenType;\n        if (((_item$suggestOpenType = item.suggestOpenType) === null || _item$suggestOpenType === void 0 ? void 0 : _item$suggestOpenType.value) === 'open_all') {\n          qiankunMicro.setGlobalState({\n            openRoute: {\n              name: '提案详情',\n              path: '/proposal/SuggestDetail',\n              query: {\n                id: item.id,\n                logo: 'Personal'\n              }\n            }\n          });\n        } else {\n          ElMessage({\n            type: 'warning',\n            message: '当前提案仅公开标题，无法查看详情'\n          });\n        }\n      }\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      exportShow.value = false;\n    };\n    var __returned__ = {\n      buttonList,\n      keyword,\n      queryRef,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableHead,\n      tableData,\n      exportId,\n      exportParams,\n      exportShow,\n      handleQuery,\n      handleSortChange,\n      handleHeaderClass,\n      handleTableSelect,\n      tableRefReset,\n      handleGetParams,\n      handleEditorCustom,\n      handleExportExcel,\n      handleReset,\n      handleButton,\n      handleTableClick,\n      handleDetails,\n      callback,\n      onActivated,\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get suggestExportWord() {\n        return suggestExportWord;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["onActivated", "GlobalTable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestExportWord", "ElMessage", "__default__", "name", "buttonList", "id", "type", "has", "_GlobalTable", "tableId", "tableApi", "keyword", "queryRef", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableHead", "tableData", "exportId", "exportParams", "exportShow", "handleQuery", "handleSortChange", "handleHeaderClass", "handleTableSelect", "tableRefReset", "handleGetParams", "handleEditorCustom", "handleExportExcel", "handleReset", "value", "handleButton", "isType", "handleTableClick", "key", "row", "handleDetails", "item", "ownOrJoin", "setGlobalState", "openRoute", "path", "query", "_item$suggestOpenType", "suggestOpenType", "logo", "message", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/PersonalAllSuggest/PersonalAllSuggest.vue"], "sourcesContent": ["<template>\r\n  <div class=\"PersonalAllSuggest\">\r\n    <xyl-search-button\r\n      @queryClick=\"handleQuery\"\r\n      @resetClick=\"handleReset\"\r\n      @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\"\r\n      :data=\"tableHead\"\r\n      ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table\r\n        ref=\"tableRef\"\r\n        row-key=\"id\"\r\n        :data=\"tableData\"\r\n        @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\"\r\n        @sort-change=\"handleSortChange\"\r\n        :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\">\r\n          <template #title=\"scope\">\r\n            <el-link @click=\"handleDetails(scope.row)\" type=\"primary\" class=\"AllSuggestIsMajorSuggestionLink\">\r\n              <span v-if=\"scope.row.isMajorSuggestion\" class=\"SuggestMajorIcon\"></span>\r\n              <span v-if=\"scope.row.isOpen\" class=\"SuggestOpenIcon\"></span>\r\n              {{ scope.row.title }}\r\n            </el-link>\r\n          </template>\r\n          <template #mainHandleOffices=\"scope\">\r\n            {{ scope.row.mainHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}\r\n          </template>\r\n          <template #assistHandleOffices=\"scope\">\r\n            {{ scope.row.assistHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}\r\n          </template>\r\n          <template #publishHandleOffices=\"scope\">\r\n            {{ scope.row.publishHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}\r\n          </template>\r\n        </xyl-global-table>\r\n        <xyl-global-table-button :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination\r\n        v-model:currentPage=\"pageNo\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleQuery\"\r\n        @current-change=\"handleQuery\"\r\n        :total=\"totals\"\r\n        background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\" name=\"导出Excel\">\r\n      <xyl-export-excel\r\n        name=\"所有提案\"\r\n        :exportId=\"exportId\"\r\n        :params=\"exportParams\"\r\n        module=\"proposalExportExcel\"\r\n        tableId=\"id_prop_proposal_member_view\"\r\n        @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'PersonalAllSuggest' }\r\n</script>\r\n<script setup>\r\nimport { onActivated } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nimport { ElMessage } from 'element-plus'\r\nconst buttonList = [\r\n  // { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n]\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel\r\n} = GlobalTable({ tableId: 'id_prop_proposal_member_view', tableApi: 'suggestionList' })\r\n\r\nonActivated(() => {\r\n  handleQuery()\r\n})\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams())\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  if (item.ownOrJoin) {\r\n    qiankunMicro.setGlobalState({\r\n      openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } }\r\n    })\r\n  } else {\r\n    if (item.suggestOpenType?.value === 'open_all') {\r\n      qiankunMicro.setGlobalState({\r\n        openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id, logo: 'Personal' } }\r\n      })\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '当前提案仅公开标题，无法查看详情' })\r\n    }\r\n  }\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  exportShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.PersonalAllSuggest {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n\r\n  .AllSuggestIsMajorSuggestionLink {\r\n    .zy-el-link__inner {\r\n      .SuggestOpenIcon {\r\n        width: 40px;\r\n        height: 19px;\r\n        display: inline-block;\r\n        background: url('@/assets/img/suggest_open_icon.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        margin-right: 6px;\r\n      }\r\n      .SuggestMajorIcon {\r\n        width: 40px;\r\n        height: 19px;\r\n        display: inline-block;\r\n        background: url('@/assets/img/suggest_major_icon.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        margin-right: 6px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAsEA,SAASA,WAAW,QAAQ,KAAK;AACjC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,SAAS,QAAQ,cAAc;AAPxC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAqB,CAAC;;;;;IAQ7C,IAAMC,UAAU,GAAG;IACjB;IACA;MAAEC,EAAE,EAAE,QAAQ;MAAEF,IAAI,EAAE,SAAS;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,CAC5D;IACD,IAAAC,YAAA,GAqBIV,WAAW,CAAC;QAAEW,OAAO,EAAE,8BAA8B;QAAEC,QAAQ,EAAE;MAAiB,CAAC,CAAC;MApBtFC,OAAO,GAAAH,YAAA,CAAPG,OAAO;MACPC,QAAQ,GAAAJ,YAAA,CAARI,QAAQ;MACRC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MACRC,MAAM,GAAAN,YAAA,CAANM,MAAM;MACNC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,QAAQ,GAAAR,YAAA,CAARQ,QAAQ;MACRC,SAAS,GAAAT,YAAA,CAATS,SAAS;MACTC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,SAAS,GAAAX,YAAA,CAATW,SAAS;MACTC,QAAQ,GAAAZ,YAAA,CAARY,QAAQ;MACRC,YAAY,GAAAb,YAAA,CAAZa,YAAY;MACZC,UAAU,GAAAd,YAAA,CAAVc,UAAU;MACVC,WAAW,GAAAf,YAAA,CAAXe,WAAW;MACXC,gBAAgB,GAAAhB,YAAA,CAAhBgB,gBAAgB;MAChBC,iBAAiB,GAAAjB,YAAA,CAAjBiB,iBAAiB;MACjBC,iBAAiB,GAAAlB,YAAA,CAAjBkB,iBAAiB;MACjBC,aAAa,GAAAnB,YAAA,CAAbmB,aAAa;MACbC,eAAe,GAAApB,YAAA,CAAfoB,eAAe;MACfC,kBAAkB,GAAArB,YAAA,CAAlBqB,kBAAkB;MAClBC,iBAAiB,GAAAtB,YAAA,CAAjBsB,iBAAiB;IAGnBjC,WAAW,CAAC,YAAM;MAChB0B,WAAW,CAAC,CAAC;IACf,CAAC,CAAC;IACF,IAAMQ,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBpB,OAAO,CAACqB,KAAK,GAAG,EAAE;MAClBT,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMU,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,YAAY;UACflC,iBAAiB,CAAC4B,eAAe,CAAC,CAAC,CAAC;UACpC;QACF,KAAK,QAAQ;UACXE,iBAAiB,CAAC,CAAC;UACnB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMK,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,GAAG,EAAEC,GAAG,EAAK;MACrC,QAAQD,GAAG;QACT,KAAK,SAAS;UACZE,aAAa,CAACD,GAAG,CAAC;UAClB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9B,IAAIA,IAAI,CAACC,SAAS,EAAE;QAClBzC,YAAY,CAAC0C,cAAc,CAAC;UAC1BC,SAAS,EAAE;YAAEvC,IAAI,EAAE,MAAM;YAAEwC,IAAI,EAAE,yBAAyB;YAAEC,KAAK,EAAE;cAAEvC,EAAE,EAAEkC,IAAI,CAAClC;YAAG;UAAE;QACrF,CAAC,CAAC;MACJ,CAAC,MAAM;QAAA,IAAAwC,qBAAA;QACL,IAAI,EAAAA,qBAAA,GAAAN,IAAI,CAACO,eAAe,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBb,KAAK,MAAK,UAAU,EAAE;UAC9CjC,YAAY,CAAC0C,cAAc,CAAC;YAC1BC,SAAS,EAAE;cAAEvC,IAAI,EAAE,MAAM;cAAEwC,IAAI,EAAE,yBAAyB;cAAEC,KAAK,EAAE;gBAAEvC,EAAE,EAAEkC,IAAI,CAAClC,EAAE;gBAAE0C,IAAI,EAAE;cAAW;YAAE;UACvG,CAAC,CAAC;QACJ,CAAC,MAAM;UACL9C,SAAS,CAAC;YAAEK,IAAI,EAAE,SAAS;YAAE0C,OAAO,EAAE;UAAmB,CAAC,CAAC;QAC7D;MACF;IACF,CAAC;IACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBtB,aAAa,CAAC,CAAC;MACfJ,WAAW,CAAC,CAAC;MACbD,UAAU,CAACU,KAAK,GAAG,KAAK;IAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}