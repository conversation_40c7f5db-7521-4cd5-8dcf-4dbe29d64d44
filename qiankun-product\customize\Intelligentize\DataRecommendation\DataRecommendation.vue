<template>
  <div class="DataRecommendation">
    <div class="DataRecommendationTips">以下数据来源于中国司法大数据研究院(最高人民法院信息中心下属研究院)</div>
    <div class="DataRecommendationSeach">
      <el-input v-model="articleTitle" @keyup.enter="handleKeyWord" placeholder="请输入" clearable />
      <el-button @click="handleKeyWord" type="primary">智能推荐</el-button>
      <el-button @click="showMove = !showMove">{{ showMove ? '收起工具' : '搜索工具' }}</el-button>
    </div>
    <!-- <div class="DataRecommendationTips">
      <span class="DataRecommendationTipsName">以下数据来源于中国司法大数据研究院(最高人民法院信息中心下属研究院)</span>
      <span class="DataRecommendationTipsMove" @click="showMove = !showMove">
        {{ showMove ? '收起工具' : '搜索工具' }}
      </span>
    </div> -->
    <div class="DataRecommendationMove" v-show="showMove">
      <div class="DataRecommendationMoveSelect">
        <el-tree-select v-model="effecLevelId" :data="effecLevelData" check-strictly filterable
          :props="{ value: 'dictCode', label: 'dictLabel', children: 'children', isLeaf: 'isLeaf' }"
          :render-after-expand="false" placeholder="效力级别" @change="handleQuery" clearable />
        <el-tree-select v-model="publishOfficeId" :data="publishOfficeData" check-strictly filterable
          :props="{ value: 'dictCode', label: 'dictLabel', children: 'children', isLeaf: 'isLeaf' }"
          :render-after-expand="false" placeholder="发布机构" @change="handleQuery" clearable />
        <el-select v-model="timeLiness" placeholder="时效性" @change="handleQuery" clearable>
          <el-option v-for="item in timeLinessData" :key="item.dictCode" :label="item.dictLabel"
            :value="item.dictCode"></el-option>
        </el-select>
      </div>
      <div class="DataRecommendationMoveSort">
        <div class="DataRecommendationMoveSortItem" @click="handleSort('publish_date_time')">
          <span>发布日期</span>
          <span :class="[
            'DataRecommendationMoveSortItemSort',
            {
              'is-ascending': orderBy === 'publish_date_time' && direction === 'asc',
              'is-descending': orderBy === 'publish_date_time' && direction === 'desc'
            }
          ]">
            <i class="sort-icon ascending" @click.stop="handleSortRow('publish_date_time', 'asc')"></i>
            <i class="sort-icon descending" @click.stop="handleSortRow('publish_date_time', 'desc')"></i>
          </span>
        </div>
        <div class="DataRecommendationMoveSortItem" @click="handleSort('implement_date')">
          <span>实施日期</span>
          <span :class="[
            'DataRecommendationMoveSortItemSort',
            {
              'is-ascending': orderBy === 'implement_date' && direction === 'asc',
              'is-descending': orderBy === 'implement_date' && direction === 'desc'
            }
          ]">
            <i class="sort-icon ascending" @click.stop="handleSortRow('implement_date', 'asc')"></i>
            <i class="sort-icon descending" @click.stop="handleSortRow('implement_date', 'desc')"></i>
          </span>
        </div>
      </div>
    </div>
    <el-scrollbar :class="['DataRecommendationListScrollbar', { DataRecommendationShowMoveScrollbar: showMove }]"
      v-loading="loading" :lement-loading-text="loadingText" always>
      <div class="DataRecommendationItem" v-for="item in tableData" :key="item.articleId"
        @click="handleTableDetails(item)">
        <div class="DataRecommendationItemTitle" v-html="item.articleTitle"></div>
        <div class="DataRecommendationItemContent">
          <el-breadcrumb separator=" | " class="globalFormBreadcrumb">
            <el-breadcrumb-item v-if="item?.statuteInfo?.timeLinessName">
              {{ item?.statuteInfo?.timeLinessName }}
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="item?.statuteInfo?.effectLevelName">
              {{ item?.statuteInfo?.effectLevelName }}
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="item?.statuteInfo?.publishOfficeName">
              {{ item?.statuteInfo?.publishOfficeName }}
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="item?.statuteInfo?.publishNum">
              {{ item?.statuteInfo?.publishNum }}
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="item?.statuteInfo?.publishDate">
              {{ item?.statuteInfo?.publishDate }}发布
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="item?.statuteInfo?.implementDate">
              {{ item?.statuteInfo?.implementDate }}实施
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
    </el-scrollbar>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <!-- <div class="DataRecommendationRecommend">
          <DouBaoIntelligentize ref="DouBaoIntelligentizeRef"
                                :keyword="articleTitle"></DouBaoIntelligentize>
        </div> -->
  </div>
</template>
<script>
export default { name: 'DataRecommendation' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, onActivated, onDeactivated, watch, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import config from 'common/config/index'
// import DouBaoIntelligentize from '../Intelligentize/DouBaoIntelligentize.vue'
import { defaultPageSize, pageSizes } from 'common/js/system_var.js'
const articleTitle = ref('')
const totals = ref(0)
const pageNo = ref(1)
const pageSize = ref(defaultPageSize.value)
const tableData = ref([])
const orderBy = ref('publish_date_time')
// const orderByData = ref([
//   { id: 'publish_date_time', name: '按发布日期排序' },
//   { id: 'implement_date', name: '按实施日期排序' },
// ])
const direction = ref('desc')
// const directionData = ref([
//   { id: 'desc', name: '降序' },
//   { id: 'asc', name: '升序' },
// ])
const showMove = ref(true)
const loading = ref(false)
const loadingText = ref('')
// const DouBaoIntelligentizeRef = ref()
const effecLevelId = ref('')
const effecLevelData = ref([])
const timeLiness = ref('')
const timeLinessData = ref([])
const publishOfficeId = ref('')
const publishOfficeData = ref([])
const store = useStore()
// const cacheData = ref([])
onMounted(() => {
  // getSelectData()
  handleQuery()
})
onActivated(() => {
  store.commit('setAiChatCode', 'ai-zx-meterials-chat')
})

onDeactivated(() => {
  store.commit('setAiChatCode', 'test_chat')
  store.commit('setAiChatSendMessage', '')
  store.commit('setAiChatWindow', false)
})
onUnmounted(() => {
  store.commit('setAiChatCode', 'test_chat')
  store.commit('setAiChatSendMessage', '')
  store.commit('setAiChatWindow', false)
})
const getSelectData = () => {
  hadoopLawseesTimeLiness()
  hadoopLawseesEffecLevel()
  hadoopLawseesPublishOffice()
}
// const handleSort = (type = '') => {
//   if (orderBy.value === type) {
//     orderBy.value = type || 'publish_date_time'
//     direction.value = direction.value === 'desc' ? 'asc' : 'desc'
//   } else {
//     orderBy.value = type || 'publish_date_time'
//     direction.value = 'desc'
//   }
//   handleQuery()
// }
const handleSort = (type) => {
  if (orderBy.value === type) {
    direction.value = direction.value === 'asc' ? 'desc' : 'asc'
  } else {
    orderBy.value = type
    direction.value = 'desc'
  }
  handleQuery()
}
const handleSortRow = (type, dir) => {
  orderBy.value = type
  direction.value = dir
  handleQuery()
}
// 监听content变化，无论是手动输入还是文件导入都会触发
watch(articleTitle, (newValue) => {
  // store.commit('setAiChatSendMessage', newValue)
})
const handleKeyWord = () => {
  loading.value = true
  if (articleTitle.value) {
    // DouBaoIntelligentizeRef.value.textClick(articleTitle.value)
    store.commit('setAiChatCode', 'ai-zx-meterials-chat')
    store.commit('setAiChatSendMessage', articleTitle.value)
    store.commit('setAiChatWindow', true)
  }
  handleQuery()
}
const handleQuery = () => {
  loading.value = true
  getSelectData()
  hadoopLawseesList()
}
const removeLabelContent = (text = '') => {
  return text
    .replace(/<\/?html[^>]*>/g, '')
    .replace(/<head\b[^<]*(?:(?!<\/head>)<[^<]*)*<\/head>/gi, '')
    .replace(/<\/?body[^>]*>/g, '')
    .replace(/<\/?div[^>]*>/g, '')
    .replace(/<\/?span[^>]*>/g, '')
    .replace(/<\/?p[^>]*>/g, '')
    .replace(/<\/?div[^>]*>/g, '')
    .replace(/<\/?font[^>]*>/g, '')
    .replace(/<\/?p[^>]*>/g, '')
    .replace(/&nbsp;/gi, '')
}
const hadoopLawseesList = async () => {
  var params = {
    articleTitle: articleTitle.value,
    effecLevelId: effecLevelId.value,
    publishOfficeId: publishOfficeId.value,
    timeLiness: timeLiness.value,
    orderBy: orderBy.value,
    direction: direction.value,
    page: pageNo.value,
    page: pageNo.value,
    size: pageSize.value
  }
  const { data } = await api.hadoopLawseesList(params)
  tableData.value = data?.content || []
  totals.value = data?.totalElements || 0
  loading.value = false
}
const handleTableDetails = (row) => {
  const token = sessionStorage.getItem('token') || ''
  window.open(
    `${config.mainPath}DataRecommendationOpen?id=${row.articleId}&title=${removeLabelContent(
      row.articleTitle
    )}&token=${token}`,
    '_blank'
  )
}
const hadoopLawseesTimeLiness = async () => {
  const { data } = await api.hadoopLawseesTimeLiness({
    articleTitle: articleTitle.value,
    effecLevelId: effecLevelId.value,
    publishOfficeId: publishOfficeId.value,
    timeLiness: timeLiness.value
  })
  timeLinessData.value = data
}
const hadoopLawseesPublishOffice = async () => {
  const { data } = await api.hadoopLawseesPublishOffice({
    articleTitle: articleTitle.value,
    effecLevelId: effecLevelId.value,
    publishOfficeId: publishOfficeId.value,
    timeLiness: timeLiness.value
  })
  publishOfficeData.value = data
}
const hadoopLawseesEffecLevel = async (resolve, id) => {
  const { data } = await api.hadoopLawseesEffecLevel({
    articleTitle: articleTitle.value,
    effecLevelId: effecLevelId.value,
    publishOfficeId: publishOfficeId.value,
    timeLiness: timeLiness.value
  })
  effecLevelData.value = data
}
</script>
<style lang="scss">
.DataRecommendation {
  width: 100%;
  height: 100%;
  background: #fff;

  .DataRecommendationTips {
    width: 100%;
    height: 42px;
    padding: 0 20px;
    line-height: 42px;
    font-weight: bold;
    font-size: var(--zy-text-font-size);
    color: var(--zy-el-color-primary);
  }

  .DataRecommendationSeach {
    width: 100%;
    display: flex;
    padding: 0 20px var(--zy-distance-four) 20px;

    .zy-el-input {
      margin-right: 20px;
    }
  }

  .DataRecommendationMove {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px var(--zy-distance-four) 20px;

    .zy-el-select {
      width: 220px;
    }

    .DataRecommendationMoveSelect {
      flex: 1;
      display: flex;
      align-items: center;

      .zy-el-select {
        min-width: 120px;
        max-width: 160px;
        margin-right: var(--zy-distance-two);
      }
    }

    .DataRecommendationMoveSort {
      display: flex;
      align-items: center;

      .DataRecommendationMoveSortItem+.DataRecommendationMoveSortItem {
        margin-left: var(--zy-distance-two);
      }

      .DataRecommendationMoveSortItem {
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
        cursor: pointer;

        .DataRecommendationMoveSortItemSort {
          display: inline-flex;
          align-items: center;
          flex-direction: column;
          height: 14px;
          width: 24px;
          vertical-align: middle;
          cursor: pointer;
          overflow: initial;
          position: relative;
        }

        .sort-icon {
          width: 0;
          height: 0;
          border: 5px solid transparent;
          position: absolute;
          left: 7px;
        }

        .ascending {
          border-bottom-color: var(--zy-el-text-color-placeholder);
          top: -5px;
        }

        .descending {
          border-top-color: var(--zy-el-text-color-placeholder);
          bottom: -3px;
        }

        .is-ascending {
          .ascending {
            border-bottom-color: var(--zy-el-color-primary);
          }
        }

        .is-descending {
          .descending {
            border-top-color: var(--zy-el-color-primary);
          }
        }
      }
    }
  }

  .DataRecommendationListScrollbar {
    width: 100%;
    height: calc(100% - (var(--zy-height) + var(--zy-distance-four) + 84px));

    .DataRecommendationItem {
      width: 100%;
      padding: 0 20px var(--zy-distance-two) 20px;
      cursor: pointer;

      .DataRecommendationItemTitle {
        font-weight: bold;
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);
        padding: var(--zy-font-name-distance-five) 0;

        &:hover {
          color: var(--zy-el-color-primary);
        }
      }

      .DataRecommendationItemContent {
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
      }
    }
  }

  .DataRecommendationShowMoveScrollbar {
    height: calc(100% - ((var(--zy-height) * 2) + (var(--zy-distance-four) * 2) + 84px));
  }

  .globalPagination {
    padding: 0 20px;
  }
}
</style>
