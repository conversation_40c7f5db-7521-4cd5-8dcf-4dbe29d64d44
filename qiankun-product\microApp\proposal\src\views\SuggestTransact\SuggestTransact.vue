<template>
  <div class="SuggestTransact">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-popover placement="bottom" title="您可以查找：" trigger="hover" :width="250">
          <div class="tips-UL">
            <div>提案名称</div>
            <div>提案编号</div>
            <div>提案人<strong>(名称前加 n 或 N)</strong></div>
            <div>全部办理单位<strong>(名称前加 d 或 D)</strong></div>
            <div>主办单位<strong>(名称前加 m 或 M)</strong></div>
            <div>协办单位<strong>(名称前加 j 或 J)</strong></div>
          </div>
          <template #reference>
            <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
          </template>
        </el-popover>
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['hasRead', 'mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #hasRead="scope">
            <div class="SuggestRead" v-if="scope.row.hasRead"></div>
            <div class="SuggestUnRead" v-if="!scope.row.hasRead"></div>
          </template>
          <template #mainHandleOffices="scope">
            <template v-if="scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0">
              <el-popover placement="top-start" v-for="item in scope.row.mainHandleOffices" :key="item.id"
                :disabled="item.users == null" popper-class="SuggestUnitPopper">
                <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                  {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
                </div>
                <div class="SuggestUnitPopperText">经办人：
                  <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">{{ index == 0 ? '' :
                    '，' }}{{ items.userName }}（{{ items.mobile }}）</span>
                </div>
                <template #reference>
                  <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                    {{ item.flowHandleOfficeName }}</span>
                </template>
              </el-popover>
            </template>
            <template v-if="scope.row.publishHandleOffices && scope.row.publishHandleOffices.length > 0">
              <el-popover placement="top-start" v-for="(item, i) in scope.row.publishHandleOffices" :key="item.id"
                :disabled="item.users == null" popper-class="SuggestUnitPopper">
                <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                  {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
                </div>
                <div class="SuggestUnitPopperText">经办人：
                  <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">{{ index == 0 ? '' :
                    '，' }}{{ items.userName }}（{{ items.mobile }}）</span>
                </div>
                <template #reference>
                  <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                    {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}</span>
                </template>
              </el-popover>
            </template>
          </template>
          <template #assistHandleOffices="scope">
            <template v-if="scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0">
              <el-popover placement="top-start" v-for="(item, i) in scope.row.assistHandleOffices" :key="item.id"
                :disabled="item.users == null" popper-class="SuggestUnitPopper">
                <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                  {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
                </div>
                <div class="SuggestUnitPopperText">经办人：
                  <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">{{ index == 0 ? '' :
                    '，' }}{{ items.userName }}（{{ items.mobile }}）</span>
                </div>
                <template #reference>
                  <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                    {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}</span>
                </template>
              </el-popover>
            </template>
            <template v-else>
              <el-popover placement="top-start" v-for="(item, i) in scope.row.assistHandleVoList" :key="item.id"
                :disabled="item.users == null" popper-class="SuggestUnitPopper">
                <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                  {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
                </div>
                <div class="SuggestUnitPopperText">经办人：
                  <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">{{ index == 0 ? '' :
                    '，' }}{{ items.userName }}（{{ items.mobile }}）</span>
                </div>
                <template #reference>
                  <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                    {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}</span>
                </template>
              </el-popover>
            </template>
          </template>
          <!-- <template #publishHandleOffices="scope">
            <el-popover placement="top-start" v-for="(item, i) in scope.row.publishHandleOffices" :key="item.id"
              :disabled="item.users == null" popper-class="SuggestUnitPopper">
              <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
              </div>
              <div class="SuggestUnitPopperText">
                经办人：
                <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">
                  {{ index == 0 ? '' : '，' }}{{ items.userName }}（{{ items.mobile }}）
                </span>
              </div>
              <template #reference>
                <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                  {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}
                </span>
              </template>
            </el-popover>
          </template> -->
        </xyl-global-table>
        <xyl-global-table-button label="" :editCustomTableHead="handleEditorCustom">
          <template #default="scope">
            <el-tooltip effect="dark" :content="timeText(scope.row.massingAnswerStopDate)" placement="top-start">
              <div :style="timeColor(scope.row.massingAnswerStopDate)" class="SuggestTimeIcon"></div>
            </el-tooltip>
          </template>
        </xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="办理中提案" :exportId="exportId" :params="exportParams" module="proposalExportExcel"
        tableId="id_prop_proposal_suggestionHandling" @excelCallback="callback"
        :handleExcelData="handleExcelData"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="answerShow" name="调整答复截止时间">
      <SuggestAnswerTime :id="id" @callback="callback"></SuggestAnswerTime>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestTransact' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
import SuggestAnswerTime from './component/SuggestAnswerTime.vue'
const buttonList = [
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' },
  { id: 'view', name: '催办查看', type: 'primary', has: '' },
  { id: 'reply', name: '催办答复', type: 'primary', has: '' },
  { id: 'modification', name: '修改答复截止时间', type: 'primary', has: 'modification' }
]
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: 'id_prop_proposal_suggestionHandling', tableApi: 'suggestionList' })

const id = ref([])
const answerShow = ref(false)

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    handleQuery()
  }
})
const handleExcelData = (_item) => {
  _item.forEach(v => {
    if (!v.mainHandleOffices) {
      v.mainHandleOffices = v.publishHandleOffices
    }
  })
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    case 'view':
      handleView()
      break
    case 'reply':
      handleReply()
      break
    case 'modification':
      if (tableDataArray.value.length) {
        id.value = tableDataArray.value.map((v) => v.id)
        answerShow.value = true
      } else {
        ElMessage({ type: 'warning', message: '请至少选择一条数据' })
      }
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id, type: 'transact' } }
  })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
  answerShow.value = false
}
const colorObj = (state, type) => {
  var color = { color: '#000' }
  if (state === 'has_answer') {
    color.color = '#4fcc72'
  } else if (state === 'handling') {
    color.color = '#fbd536'
  } else if (state === 'apply_adjust') {
    color.color = '#ca6063'
  } else if (state === 'suggestionHandling') {
    color.color = '#fbd536'
  }
  if (type) {
    color = { color: '#000' }
  }
  return color
}
const timeColor = (time) => {
  var color = { backgroundColor: 'red' }
  if (time > Date.now() + 3600 * 1000 * 24 * 30) {
    color.backgroundColor = 'yellow'
  }
  if (time > Date.now() + 3600 * 1000 * 24 * 60) {
    color.backgroundColor = 'green'
  }
  return color
}
const timeText = (time) => {
  var text = '答复期限小于30天'
  if (time > Date.now() + 3600 * 1000 * 24 * 30) {
    text = '答复期限大于等于30天小于60天'
  }
  if (time > Date.now() + 3600 * 1000 * 24 * 60) {
    text = '答复期限大于60天'
  }
  return text
}
const handleView = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作将会提醒选中的提案的办理单位尽快查看提案, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionPress('noticeHandleOfficeLook')
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消提醒' })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const handleReply = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作将会提醒选中的提案的办理单位尽快答复提案, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionPress('noticeHandleOfficeAnswer')
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消提醒' })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionPress = async (type) => {
  const { code } = await api.suggestionPress({ ids: tableDataArray.value.map((v) => v.id), pressMessageCode: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: '提醒成功' })
    tableRefReset()
    handleQuery()
  }
}
</script>
<style lang="scss">
.SuggestTransact {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }

  .SuggestTimeIcon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: auto;
  }

  .SuggestRead {
    width: 26px;
    height: 26px;
    background: url('../../assets/img/suggest_details_read.png') no-repeat;
    background-size: 100% 100%;
    background-color: var(--zy-el-color-info);
  }

  .SuggestUnRead {
    width: 26px;
    height: 26px;
    background: url('../../assets/img/suggest_details_unread.png') no-repeat;
    background-size: 100% 100%;
    background-color: var(--zy-el-color-danger);
  }
}

.SuggestUnitPopper {
  width: 500px !important;

  .SuggestUnitPopperName {
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    padding-bottom: var(--zy-font-name-distance-five);
  }

  .SuggestUnitPopperText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
  }
}
</style>
