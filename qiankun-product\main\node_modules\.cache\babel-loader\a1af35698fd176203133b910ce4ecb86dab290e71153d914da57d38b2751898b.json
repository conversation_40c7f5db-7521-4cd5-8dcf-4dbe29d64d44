{"ast": null, "code": "// 导入封装的方法\nimport HTTP from 'common/http';\nvar customizeApi = {\n  loginImg(params) {\n    return HTTP.json('/wind/runner/loginImg', params);\n  },\n  loginImgRegion(params, areaId) {\n    // 登录获取用户信息\n    return HTTP.json('/wind/runner/loginImg', params, {\n      areaId: areaId\n    });\n  },\n  readOpenConfigRegion(params, areaId) {\n    // 通用公开取配置接口\n    return HTTP.json('/config/openRead', params, {\n      areaId: areaId\n    });\n  },\n  miduDataScribePoll(params) {\n    // 预警接口\n    return HTTP.json('/miduData/scribe/poll', params);\n  },\n  miduDataTopicList(params) {\n    // 榜单列表接口\n    return HTTP.json('/miduData/topic/list', params);\n  },\n  miduDataTopicDetail(params) {\n    // 榜单列表接口\n    return HTTP.json('/miduData/topic/detail', params);\n  },\n  aigptChatSceneDetail(params) {\n    // AI智能场景管理\n    return HTTP.json('/aigptChatScene/detail', params);\n  },\n  aigptChatInitList(params) {\n    // AI智能场景提示语\n    return HTTP.json('/aigptChatInit/list', params);\n  },\n  aigptChatToolsList(params) {\n    // AI智能场景工具管理\n    return HTTP.json('/aigptChatTools/list', params);\n  },\n  aigptChatClusterList(params) {\n    // AI智能场景会话列表\n    return HTTP.json('/aigptChatCluster/list', params);\n  },\n  aigptChatLogsList(params) {\n    // AI智能场景历史会话记录\n    return HTTP.json('/aigptChatLogs/list', params);\n  },\n  aiScheduleExists(params) {\n    // 判断当天是否有会客\n    return HTTP.json('/aiSchedule/exists', params);\n  },\n  aigptReportRecordTypeList(params) {\n    // 智能报告记录\n    return HTTP.json('/aigptReportRecord/typeList', params);\n  },\n  aigptReportRecordList(params) {\n    // 智能报告记录\n    return HTTP.json('/aigptReportRecord/list', params);\n  },\n  aigptReportRecordInfo(params) {\n    // 智能报告记录\n    return HTTP.json('/aigptReportRecord/info', params);\n  },\n  aigptReportRecordDel(params) {\n    // 智能报告记录\n    return HTTP.json('/aigptReportRecord/dels', params);\n  },\n  loginLastAreaId(params) {\n    // 通过编号获取首页数据\n    return HTTP.json('/login/lastAreaId', params);\n  },\n  homePageDocument(params) {\n    // 通过编号获取首页数据\n    return HTTP.json('/homepage/load', params);\n  },\n  noticeHomePage(params) {\n    // 首页公共数据\n    return HTTP.json('/notification/list', params);\n  },\n  newsContentInfo(params) {\n    // 资讯详情数据\n    return HTTP.json('/newsContent/info', params);\n  },\n  newsContentInfo(params) {\n    // 资讯详情数据\n    return HTTP.json('/newsContent/info', params);\n  },\n  NoticeAnnouncementInfo(params) {\n    // 通知公告详情\n    return HTTP.json('/notification/info', params);\n  },\n  reqPersonSuggestionAdd(params) {\n    // 新增个人帮助建议\n    return HTTP.json('/personSuggestion/add', params);\n  },\n  reqPersonSuggestion(params) {\n    // 历史反馈建议\n    return HTTP.json('/personSuggestion/list', params);\n  },\n  reqRoleHelpNote(params) {\n    // 帮助文档\n    return HTTP.json('/roleHelpNote/list', params);\n  },\n  userInfoByAccount(params) {\n    // 获取用户信息\n    return HTTP.json('/user/infoByAccount', params, {\n      noErrorTip: true\n    });\n  },\n  userInfo(params) {\n    // 获取用户信息\n    return HTTP.json('/user/info', params, {\n      noErrorTip: true\n    });\n  },\n  chatGroupInfo(params) {\n    // 获取群组信息\n    return HTTP.json('/chatGroup/info', params, {\n      noErrorTip: true\n    });\n  },\n  chatGroupAdd(params) {\n    // 群组新增\n    return HTTP.json('/chatGroup/add', params, {\n      terminal: 'APP'\n    });\n  },\n  chatGroupEdit(params) {\n    // 群组编辑\n    return HTTP.json('/chatGroup/edit', params, {\n      terminal: 'APP'\n    });\n  },\n  chatGroupDel(params) {\n    // 群组编辑\n    return HTTP.json('/chatGroup/dels', params, {\n      terminal: 'APP'\n    });\n  },\n  chatGroupList(params) {\n    // 获取群组列表\n    return HTTP.json('/chatGroup/list', params, {\n      terminal: 'APP'\n    });\n  },\n  chatGroupMemberList(params) {\n    // 获取群组人员列表\n    return HTTP.json('/chatGroupMember/list', params);\n  },\n  chatGroupFileAdd(params) {\n    // 获取群组人员列表\n    return HTTP.json('/chatGroupFile/add', params);\n  },\n  VoteList(params) {\n    // 投票列表\n    return HTTP.json('/voteTopic/list', params);\n  },\n  VoteInfo(params) {\n    // 投票详情\n    return HTTP.json('/voteTopic/info', params);\n  },\n  VoteDel(params) {\n    // 投票删除\n    return HTTP.json('/voteTopic/dels', params);\n  },\n  VoteCount(params) {\n    // 投票统计\n    return HTTP.json('/voteTopic/count', params);\n  },\n  VoteUserList(params) {\n    // 投票统计\n    return HTTP.json('/voteTopicOptionUser/list', params);\n  },\n  relationBookMemberOftenList(params) {\n    // 获取常用人员列表\n    return HTTP.json('/relationBookMember/oftenList', params);\n  },\n  relationBookMemberSetOften(params) {\n    // 设置常用人员列表\n    return HTTP.json('/relationBookMember/setOften', params);\n  },\n  openClueBankHot(params) {\n    // 对外开放-人大政协线索库热榜分析接口\n    return HTTP.json('/open/clueBankHot', params);\n  },\n  openClueBankLabel(params) {\n    // 对外开放-人大政协线索库类别热点分析接口\n    return HTTP.json('/open/clueBankLabel', params);\n  },\n  openSmartSearch(params) {\n    // 对外开放-人大政协线索库检索接口\n    return HTTP.json('/open/smartSearch', params);\n  },\n  publicOpinionMonitoring(params) {\n    // 对外开放-舆情监督接口\n    return HTTP.json('/open/publicOpinionMonitoring', params);\n  },\n  publicChartAnalysis(params) {\n    // 对外开放-舆情监督统计图表\n    return HTTP.json('/open/publicChartAnalysis', params);\n  },\n  faceDetect(params) {\n    // 法眼-获取人脸识别url\n    return HTTP.json('/face/faceDetect', params);\n  },\n  faceResult(params) {\n    // 法眼-获取人脸识别结果\n    return HTTP.json('/face/faceResult', params);\n  },\n  lawsuitResult(params) {\n    // 法眼-获取涉诉结果\n    return HTTP.json('/face/lawsuitResult', params);\n  },\n  backReview(params) {\n    // 法眼-获取背景审查\n    return HTTP.json('/face/backReview', params);\n  },\n  employRisks(params) {\n    // 法眼-获取用户风险\n    return HTTP.json('/face/employRisks', params);\n  },\n  faceHistory(params) {\n    // 法眼-获取历史记录\n    return HTTP.json('/face/history', params);\n  },\n  knowledgeQuiz(params) {\n    // 对外开放-智能生成知识问答接口\n    return HTTP.json('/open/knowledgeQuiz', params);\n  },\n  knowledgeFiles(params) {\n    // 对外开放-知识库文件上传\n    return HTTP.json('/open/knowledgeFiles', params);\n  },\n  knowledgeUpload(params) {\n    // 对外开放-知识库文件上传\n    return HTTP.fileUpload('/open/knowledgeUpload', params, function () {});\n  },\n  knowledgeDelete(params) {\n    // 对外开放-智能生成知识库文件删除接口\n    return HTTP.json('/open/knowledgeDelete', params);\n  },\n  // 智能生成摘要 接口\n  chatIntelligentAnswer(params) {\n    // 智能摘要生成接口———chat工程\n    // , { responseType: 'stream' }\n    return HTTP.json('/chat/IntelligentAnswer', params);\n  },\n  wordApiWordToHtml(params) {\n    // word转html\n    return HTTP.fileUpload('/wordApi/wordToHtml', params, function () {});\n  },\n  wordApiPaintedWord(params) {\n    // 版本对比-花脸稿接口\n    return HTTP.fileUpload('/wordApi/paintedWord', params, function () {});\n  },\n  wordApiContrastWord(params) {\n    // 版本对比-对照表接口\n    return HTTP.fileUpload('/wordApi/textContrastWord', params, function () {});\n  },\n  hadoopLawseesList(params) {\n    // 法规检索\n    return HTTP.json('/hadoop_api/datax/lawsees/list', params);\n  },\n  hadoopLawseesInfo(params) {\n    // 法规详情\n    return HTTP.json('/hadoop_api/datax/lawsees/info', params);\n  },\n  hadoopLawseesEffecLevel(params) {\n    // 效力级别统计\n    return HTTP.json('/hadoop_api/datax/lawsees/effecLevel', params);\n  },\n  hadoopLawseesTimeLiness(params) {\n    // 时效性统计\n    return HTTP.json('/hadoop_api/datax/lawsees/timeLiness', params);\n  },\n  hadoopLawseesPublishOffice(params) {\n    // 发布机构(地区)\n    return HTTP.json('/hadoop_api/datax/lawsees/publishOffice', params);\n  },\n  initMessage(params) {\n    // 发布机构(地区)\n    return HTTP.json('/chat/questionLists', params);\n  },\n  filewordhtml(params, callback, id) {\n    // word转html\n    return HTTP.fileUpload('/file/word2html', params, callback, id);\n  },\n  wordApiTextComparison(params) {\n    // 智能工具版本对比\n    return HTTP.fileUpload('/wordApi/textComparison', params, function () {});\n  },\n  newsContentList(params) {\n    // 首页资讯\n    return HTTP.json('/newsContent/list', params);\n  },\n  // newsColumnList (params) { // 首页资讯栏目\n  //   return HTTP.json('/newsColumn/list', params)\n  // },\n  newsColumnList(params) {\n    // 首页资讯栏目\n    return HTTP.json('/newsColumn/app/list', params);\n  },\n  userOrganizationList(params) {\n    // 获取用户可访问的组织列表\n    return HTTP.json('/uniter/org/manager/find/login', params);\n  },\n  setUserOrganization(params) {\n    // 设置用户当前组织\n    return HTTP.json('/uniterOrganize/list', params);\n  },\n  aigptChatLogsList(params) {\n    // 对话记录\n    return HTTP.json('/aigptStatistics/recentList', params);\n  },\n  hotWordPlusList(params) {\n    // 加强版热词列表\n    return HTTP.json('/hotWordPlus/list', params);\n  },\n  hotWordPlusInfo(params) {\n    // 加强版热词详情\n    return HTTP.json('/hotWordPlus/info', params);\n  },\n  hotWordPlusDels(params) {\n    // 加强版热词删除\n    return HTTP.json('/hotWordPlus/dels', params);\n  },\n  hotWordPlusEdit(params) {\n    // 加强版热词编辑\n    return HTTP.json('/hotWordPlus/edit', params);\n  },\n  hotWordPlusSignNeutral(params) {\n    // 标记中性词\n    return HTTP.json('/hotWordPlus/signNeutral', params);\n  },\n  hotWordPlusSignShow(params) {\n    // 批显示\n    return HTTP.json('/hotWordPlus/signShow', params);\n  },\n  videoConnectionInfo(params) {\n    // 视频会议详情\n    return HTTP.json('/videoConnection/info', params);\n  },\n  praisesAdd(params) {\n    // 新增点赞\n    return HTTP.json('/praises/add', params);\n  },\n  praisesDels(params) {\n    // 删除点赞\n    return HTTP.json('/praises/dels', params);\n  },\n  liveWatchCountAdd(params) {\n    // 添加访问记录\n    return HTTP.json('/liveWatchCount/add', params);\n  }\n};\nexport default customizeApi;", "map": {"version": 3, "names": ["HTTP", "customizeApi", "loginImg", "params", "json", "loginImgRegion", "areaId", "readOpenConfigRegion", "miduDataScribePoll", "miduDataTopicList", "miduDataTopicDetail", "aigptChatSceneDetail", "aigptChatInitList", "aigptChatToolsList", "aigptChatClusterList", "aigptChatLogsList", "aiScheduleExists", "aigptReportRecordTypeList", "aigptReportRecordList", "aigptReportRecordInfo", "aigptReportRecordDel", "loginLastAreaId", "homePageDocument", "noticeHomePage", "newsContentInfo", "NoticeAnnouncementInfo", "reqPersonSuggestionAdd", "reqPersonSuggestion", "reqRoleHelpNote", "userInfoByAccount", "noErrorTip", "userInfo", "chatGroupInfo", "chatGroupAdd", "terminal", "chatGroupEdit", "chatGroupDel", "chatGroupList", "chatGroupMemberList", "chatGroupFileAdd", "VoteList", "VoteInfo", "VoteDel", "VoteCount", "VoteUserList", "relationBookMemberOftenList", "relationBookMemberSetOften", "openClueBankHot", "openClueBankLabel", "openSmartSearch", "publicOpinionMonitoring", "publicChartAnalysis", "faceDetect", "faceResult", "lawsuitResult", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "employRisks", "faceHistory", "knowledgeQuiz", "knowledgeFiles", "knowledgeUpload", "fileUpload", "knowledgeDelete", "chatIntelligentAnswer", "wordApiWordToHtml", "wordApiPaintedWord", "wordApiContrastWord", "hadoopLawseesList", "hadoopLawseesInfo", "hadoopLawseesEffecLevel", "hadoopLawseesTimeLiness", "hadoopLawseesPublishOffice", "initMessage", "filewordhtml", "callback", "id", "wordApiTextComparison", "newsContentList", "newsColumnList", "userOrganizationList", "setUserOrganization", "hotWordPlusList", "hotWordPlusInfo", "hotWordPlusDels", "hotWordPlusEdit", "hotWordPlusSignNeutral", "hotWordPlusSignShow", "videoConnectionInfo", "praisesAdd", "praises<PERSON><PERSON>", "liveWatchCountAdd"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/config/api.js"], "sourcesContent": ["// 导入封装的方法\r\nimport HTTP from 'common/http'\r\nconst customizeApi = {\r\n  loginImg (params) {\r\n    return HTTP.json('/wind/runner/loginImg', params)\r\n  },\r\n  loginImgRegion (params, areaId) {\r\n    // 登录获取用户信息\r\n    return HTTP.json('/wind/runner/loginImg', params, { areaId: areaId })\r\n  },\r\n  readOpenConfigRegion (params, areaId) {\r\n    // 通用公开取配置接口\r\n    return HTTP.json('/config/openRead', params, { areaId: areaId })\r\n  },\r\n  miduDataScribePoll (params) { // 预警接口\r\n    return HTTP.json('/miduData/scribe/poll', params)\r\n  },\r\n  miduDataTopicList (params) { // 榜单列表接口\r\n    return HTTP.json('/miduData/topic/list', params)\r\n  },\r\n  miduDataTopicDetail (params) { // 榜单列表接口\r\n    return HTTP.json('/miduData/topic/detail', params)\r\n  },\r\n  aigptChatSceneDetail (params) { // AI智能场景管理\r\n    return HTTP.json('/aigptChatScene/detail', params)\r\n  },\r\n  aigptChatInitList (params) { // AI智能场景提示语\r\n    return HTTP.json('/aigptChatInit/list', params)\r\n  },\r\n  aigptChatToolsList (params) { // AI智能场景工具管理\r\n    return HTTP.json('/aigptChatTools/list', params)\r\n  },\r\n  aigptChatClusterList (params) { // AI智能场景会话列表\r\n    return HTTP.json('/aigptChatCluster/list', params)\r\n  },\r\n  aigptChatLogsList (params) { // AI智能场景历史会话记录\r\n    return HTTP.json('/aigptChatLogs/list', params)\r\n  },\r\n  aiScheduleExists (params) { // 判断当天是否有会客\r\n    return HTTP.json('/aiSchedule/exists', params)\r\n  },\r\n  aigptReportRecordTypeList (params) { // 智能报告记录\r\n    return HTTP.json('/aigptReportRecord/typeList', params)\r\n  },\r\n  aigptReportRecordList (params) { // 智能报告记录\r\n    return HTTP.json('/aigptReportRecord/list', params)\r\n  },\r\n  aigptReportRecordInfo (params) { // 智能报告记录\r\n    return HTTP.json('/aigptReportRecord/info', params)\r\n  },\r\n  aigptReportRecordDel (params) { // 智能报告记录\r\n    return HTTP.json('/aigptReportRecord/dels', params)\r\n  },\r\n  loginLastAreaId (params) { // 通过编号获取首页数据\r\n    return HTTP.json('/login/lastAreaId', params)\r\n  },\r\n  homePageDocument (params) { // 通过编号获取首页数据\r\n    return HTTP.json('/homepage/load', params)\r\n  },\r\n  noticeHomePage (params) { // 首页公共数据\r\n    return HTTP.json('/notification/list', params)\r\n  },\r\n  newsContentInfo (params) { // 资讯详情数据\r\n    return HTTP.json('/newsContent/info', params)\r\n  },\r\n  newsContentInfo (params) { // 资讯详情数据\r\n    return HTTP.json('/newsContent/info', params)\r\n  },\r\n  NoticeAnnouncementInfo (params) { // 通知公告详情\r\n    return HTTP.json('/notification/info', params)\r\n  },\r\n  reqPersonSuggestionAdd (params) { // 新增个人帮助建议\r\n    return HTTP.json('/personSuggestion/add', params)\r\n  },\r\n  reqPersonSuggestion (params) { // 历史反馈建议\r\n    return HTTP.json('/personSuggestion/list', params)\r\n  },\r\n  reqRoleHelpNote (params) { // 帮助文档\r\n    return HTTP.json('/roleHelpNote/list', params)\r\n  },\r\n  userInfoByAccount (params) { // 获取用户信息\r\n    return HTTP.json('/user/infoByAccount', params, { noErrorTip: true })\r\n  },\r\n  userInfo (params) { // 获取用户信息\r\n    return HTTP.json('/user/info', params, { noErrorTip: true })\r\n  },\r\n  chatGroupInfo (params) { // 获取群组信息\r\n    return HTTP.json('/chatGroup/info', params, { noErrorTip: true })\r\n  },\r\n  chatGroupAdd (params) { // 群组新增\r\n    return HTTP.json('/chatGroup/add', params, { terminal: 'APP' })\r\n  },\r\n  chatGroupEdit (params) { // 群组编辑\r\n    return HTTP.json('/chatGroup/edit', params, { terminal: 'APP' })\r\n  },\r\n  chatGroupDel (params) { // 群组编辑\r\n    return HTTP.json('/chatGroup/dels', params, { terminal: 'APP' })\r\n  },\r\n  chatGroupList (params) { // 获取群组列表\r\n    return HTTP.json('/chatGroup/list', params, { terminal: 'APP' })\r\n  },\r\n  chatGroupMemberList (params) { // 获取群组人员列表\r\n    return HTTP.json('/chatGroupMember/list', params)\r\n  },\r\n  chatGroupFileAdd (params) { // 获取群组人员列表\r\n    return HTTP.json('/chatGroupFile/add', params)\r\n  },\r\n  VoteList (params) { // 投票列表\r\n    return HTTP.json('/voteTopic/list', params)\r\n  },\r\n  VoteInfo (params) { // 投票详情\r\n    return HTTP.json('/voteTopic/info', params)\r\n  },\r\n  VoteDel (params) { // 投票删除\r\n    return HTTP.json('/voteTopic/dels', params)\r\n  },\r\n  VoteCount (params) { // 投票统计\r\n    return HTTP.json('/voteTopic/count', params)\r\n  },\r\n  VoteUserList (params) { // 投票统计\r\n    return HTTP.json('/voteTopicOptionUser/list', params)\r\n  },\r\n  relationBookMemberOftenList (params) { // 获取常用人员列表\r\n    return HTTP.json('/relationBookMember/oftenList', params)\r\n  },\r\n  relationBookMemberSetOften (params) { // 设置常用人员列表\r\n    return HTTP.json('/relationBookMember/setOften', params)\r\n  },\r\n  openClueBankHot (params) { // 对外开放-人大政协线索库热榜分析接口\r\n    return HTTP.json('/open/clueBankHot', params)\r\n  },\r\n  openClueBankLabel (params) { // 对外开放-人大政协线索库类别热点分析接口\r\n    return HTTP.json('/open/clueBankLabel', params)\r\n  },\r\n  openSmartSearch (params) { // 对外开放-人大政协线索库检索接口\r\n    return HTTP.json('/open/smartSearch', params)\r\n  },\r\n  publicOpinionMonitoring (params) { // 对外开放-舆情监督接口\r\n    return HTTP.json('/open/publicOpinionMonitoring', params)\r\n  },\r\n  publicChartAnalysis (params) { // 对外开放-舆情监督统计图表\r\n    return HTTP.json('/open/publicChartAnalysis', params)\r\n  },\r\n  faceDetect (params) { // 法眼-获取人脸识别url\r\n    return HTTP.json('/face/faceDetect', params)\r\n  },\r\n  faceResult (params) { // 法眼-获取人脸识别结果\r\n    return HTTP.json('/face/faceResult', params)\r\n  },\r\n  lawsuitResult (params) { // 法眼-获取涉诉结果\r\n    return HTTP.json('/face/lawsuitResult', params)\r\n  },\r\n  backReview (params) { // 法眼-获取背景审查\r\n    return HTTP.json('/face/backReview', params)\r\n  },\r\n  employRisks (params) { // 法眼-获取用户风险\r\n    return HTTP.json('/face/employRisks', params)\r\n  },\r\n  faceHistory (params) { // 法眼-获取历史记录\r\n    return HTTP.json('/face/history', params)\r\n  },\r\n  knowledgeQuiz (params) { // 对外开放-智能生成知识问答接口\r\n    return HTTP.json('/open/knowledgeQuiz', params)\r\n  },\r\n  knowledgeFiles (params) { // 对外开放-知识库文件上传\r\n    return HTTP.json('/open/knowledgeFiles', params)\r\n  },\r\n  knowledgeUpload (params) { // 对外开放-知识库文件上传\r\n    return HTTP.fileUpload('/open/knowledgeUpload', params, () => { })\r\n  },\r\n  knowledgeDelete (params) { // 对外开放-智能生成知识库文件删除接口\r\n    return HTTP.json('/open/knowledgeDelete', params)\r\n  },\r\n  // 智能生成摘要 接口\r\n  chatIntelligentAnswer (params) { // 智能摘要生成接口———chat工程\r\n    // , { responseType: 'stream' }\r\n    return HTTP.json('/chat/IntelligentAnswer', params)\r\n  },\r\n  wordApiWordToHtml (params) { // word转html\r\n    return HTTP.fileUpload('/wordApi/wordToHtml', params, () => { })\r\n  },\r\n  wordApiPaintedWord (params) { // 版本对比-花脸稿接口\r\n    return HTTP.fileUpload('/wordApi/paintedWord', params, () => { })\r\n  },\r\n  wordApiContrastWord (params) { // 版本对比-对照表接口\r\n    return HTTP.fileUpload('/wordApi/textContrastWord', params, () => { })\r\n  },\r\n  hadoopLawseesList (params) { // 法规检索\r\n    return HTTP.json('/hadoop_api/datax/lawsees/list', params)\r\n  },\r\n  hadoopLawseesInfo (params) { // 法规详情\r\n    return HTTP.json('/hadoop_api/datax/lawsees/info', params)\r\n  },\r\n  hadoopLawseesEffecLevel (params) { // 效力级别统计\r\n    return HTTP.json('/hadoop_api/datax/lawsees/effecLevel', params)\r\n  },\r\n  hadoopLawseesTimeLiness (params) { // 时效性统计\r\n    return HTTP.json('/hadoop_api/datax/lawsees/timeLiness', params)\r\n  },\r\n  hadoopLawseesPublishOffice (params) { // 发布机构(地区)\r\n    return HTTP.json('/hadoop_api/datax/lawsees/publishOffice', params)\r\n  },\r\n  initMessage (params) { // 发布机构(地区)\r\n    return HTTP.json('/chat/questionLists', params)\r\n  },\r\n  filewordhtml (params, callback, id) {\r\n    // word转html\r\n    return HTTP.fileUpload('/file/word2html', params, callback, id)\r\n  },\r\n  wordApiTextComparison (params) { // 智能工具版本对比\r\n    return HTTP.fileUpload('/wordApi/textComparison', params, () => { })\r\n  },\r\n  newsContentList (params) { // 首页资讯\r\n    return HTTP.json('/newsContent/list', params)\r\n  },\r\n  // newsColumnList (params) { // 首页资讯栏目\r\n  //   return HTTP.json('/newsColumn/list', params)\r\n  // },\r\n  newsColumnList (params) { // 首页资讯栏目\r\n    return HTTP.json('/newsColumn/app/list', params)\r\n  },\r\n  userOrganizationList (params) { // 获取用户可访问的组织列表\r\n    return HTTP.json('/uniter/org/manager/find/login', params)\r\n  },\r\n  setUserOrganization (params) { // 设置用户当前组织\r\n    return HTTP.json('/uniterOrganize/list', params)\r\n  },\r\n  aigptChatLogsList (params) { // 对话记录\r\n    return HTTP.json('/aigptStatistics/recentList', params)\r\n  },\r\n  hotWordPlusList (params) { // 加强版热词列表\r\n    return HTTP.json('/hotWordPlus/list', params)\r\n  },\r\n  hotWordPlusInfo (params) { // 加强版热词详情\r\n    return HTTP.json('/hotWordPlus/info', params)\r\n  },\r\n  hotWordPlusDels (params) { // 加强版热词删除\r\n    return HTTP.json('/hotWordPlus/dels', params)\r\n  },\r\n  hotWordPlusEdit (params) { // 加强版热词编辑\r\n    return HTTP.json('/hotWordPlus/edit', params)\r\n  },\r\n  hotWordPlusSignNeutral (params) { // 标记中性词\r\n    return HTTP.json('/hotWordPlus/signNeutral', params)\r\n  },\r\n  hotWordPlusSignShow (params) { // 批显示\r\n    return HTTP.json('/hotWordPlus/signShow', params)\r\n  },\r\n  videoConnectionInfo (params) { // 视频会议详情\r\n    return HTTP.json('/videoConnection/info', params)\r\n  },\r\n  praisesAdd (params) { // 新增点赞\r\n    return HTTP.json('/praises/add', params)\r\n  },\r\n  praisesDels (params) { // 删除点赞\r\n    return HTTP.json('/praises/dels', params)\r\n  },\r\n  liveWatchCountAdd (params) { // 添加访问记录\r\n    return HTTP.json('/liveWatchCount/add', params)\r\n  },\r\n}\r\nexport default customizeApi\r\n"], "mappings": "AAAA;AACA,OAAOA,IAAI,MAAM,aAAa;AAC9B,IAAMC,YAAY,GAAG;EACnBC,QAAQA,CAAEC,MAAM,EAAE;IAChB,OAAOH,IAAI,CAACI,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDE,cAAcA,CAAEF,MAAM,EAAEG,MAAM,EAAE;IAC9B;IACA,OAAON,IAAI,CAACI,IAAI,CAAC,uBAAuB,EAAED,MAAM,EAAE;MAAEG,MAAM,EAAEA;IAAO,CAAC,CAAC;EACvE,CAAC;EACDC,oBAAoBA,CAAEJ,MAAM,EAAEG,MAAM,EAAE;IACpC;IACA,OAAON,IAAI,CAACI,IAAI,CAAC,kBAAkB,EAAED,MAAM,EAAE;MAAEG,MAAM,EAAEA;IAAO,CAAC,CAAC;EAClE,CAAC;EACDE,kBAAkBA,CAAEL,MAAM,EAAE;IAAE;IAC5B,OAAOH,IAAI,CAACI,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDM,iBAAiBA,CAAEN,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDO,mBAAmBA,CAAEP,MAAM,EAAE;IAAE;IAC7B,OAAOH,IAAI,CAACI,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDQ,oBAAoBA,CAAER,MAAM,EAAE;IAAE;IAC9B,OAAOH,IAAI,CAACI,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDS,iBAAiBA,CAAET,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDU,kBAAkBA,CAAEV,MAAM,EAAE;IAAE;IAC5B,OAAOH,IAAI,CAACI,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDW,oBAAoBA,CAAEX,MAAM,EAAE;IAAE;IAC9B,OAAOH,IAAI,CAACI,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDY,iBAAiBA,CAAEZ,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDa,gBAAgBA,CAAEb,MAAM,EAAE;IAAE;IAC1B,OAAOH,IAAI,CAACI,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDc,yBAAyBA,CAAEd,MAAM,EAAE;IAAE;IACnC,OAAOH,IAAI,CAACI,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACDe,qBAAqBA,CAAEf,MAAM,EAAE;IAAE;IAC/B,OAAOH,IAAI,CAACI,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDgB,qBAAqBA,CAAEhB,MAAM,EAAE;IAAE;IAC/B,OAAOH,IAAI,CAACI,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDiB,oBAAoBA,CAAEjB,MAAM,EAAE;IAAE;IAC9B,OAAOH,IAAI,CAACI,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDkB,eAAeA,CAAElB,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDmB,gBAAgBA,CAAEnB,MAAM,EAAE;IAAE;IAC1B,OAAOH,IAAI,CAACI,IAAI,CAAC,gBAAgB,EAAED,MAAM,CAAC;EAC5C,CAAC;EACDoB,cAAcA,CAAEpB,MAAM,EAAE;IAAE;IACxB,OAAOH,IAAI,CAACI,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDqB,eAAeA,CAAErB,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDqB,eAAeA,CAAErB,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDsB,sBAAsBA,CAAEtB,MAAM,EAAE;IAAE;IAChC,OAAOH,IAAI,CAACI,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDuB,sBAAsBA,CAAEvB,MAAM,EAAE;IAAE;IAChC,OAAOH,IAAI,CAACI,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDwB,mBAAmBA,CAAExB,MAAM,EAAE;IAAE;IAC7B,OAAOH,IAAI,CAACI,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDyB,eAAeA,CAAEzB,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACD0B,iBAAiBA,CAAE1B,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,qBAAqB,EAAED,MAAM,EAAE;MAAE2B,UAAU,EAAE;IAAK,CAAC,CAAC;EACvE,CAAC;EACDC,QAAQA,CAAE5B,MAAM,EAAE;IAAE;IAClB,OAAOH,IAAI,CAACI,IAAI,CAAC,YAAY,EAAED,MAAM,EAAE;MAAE2B,UAAU,EAAE;IAAK,CAAC,CAAC;EAC9D,CAAC;EACDE,aAAaA,CAAE7B,MAAM,EAAE;IAAE;IACvB,OAAOH,IAAI,CAACI,IAAI,CAAC,iBAAiB,EAAED,MAAM,EAAE;MAAE2B,UAAU,EAAE;IAAK,CAAC,CAAC;EACnE,CAAC;EACDG,YAAYA,CAAE9B,MAAM,EAAE;IAAE;IACtB,OAAOH,IAAI,CAACI,IAAI,CAAC,gBAAgB,EAAED,MAAM,EAAE;MAAE+B,QAAQ,EAAE;IAAM,CAAC,CAAC;EACjE,CAAC;EACDC,aAAaA,CAAEhC,MAAM,EAAE;IAAE;IACvB,OAAOH,IAAI,CAACI,IAAI,CAAC,iBAAiB,EAAED,MAAM,EAAE;MAAE+B,QAAQ,EAAE;IAAM,CAAC,CAAC;EAClE,CAAC;EACDE,YAAYA,CAAEjC,MAAM,EAAE;IAAE;IACtB,OAAOH,IAAI,CAACI,IAAI,CAAC,iBAAiB,EAAED,MAAM,EAAE;MAAE+B,QAAQ,EAAE;IAAM,CAAC,CAAC;EAClE,CAAC;EACDG,aAAaA,CAAElC,MAAM,EAAE;IAAE;IACvB,OAAOH,IAAI,CAACI,IAAI,CAAC,iBAAiB,EAAED,MAAM,EAAE;MAAE+B,QAAQ,EAAE;IAAM,CAAC,CAAC;EAClE,CAAC;EACDI,mBAAmBA,CAAEnC,MAAM,EAAE;IAAE;IAC7B,OAAOH,IAAI,CAACI,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDoC,gBAAgBA,CAAEpC,MAAM,EAAE;IAAE;IAC1B,OAAOH,IAAI,CAACI,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDqC,QAAQA,CAAErC,MAAM,EAAE;IAAE;IAClB,OAAOH,IAAI,CAACI,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACDsC,QAAQA,CAAEtC,MAAM,EAAE;IAAE;IAClB,OAAOH,IAAI,CAACI,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACDuC,OAAOA,CAAEvC,MAAM,EAAE;IAAE;IACjB,OAAOH,IAAI,CAACI,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACDwC,SAASA,CAAExC,MAAM,EAAE;IAAE;IACnB,OAAOH,IAAI,CAACI,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDyC,YAAYA,CAAEzC,MAAM,EAAE;IAAE;IACtB,OAAOH,IAAI,CAACI,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACD0C,2BAA2BA,CAAE1C,MAAM,EAAE;IAAE;IACrC,OAAOH,IAAI,CAACI,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACD2C,0BAA0BA,CAAE3C,MAAM,EAAE;IAAE;IACpC,OAAOH,IAAI,CAACI,IAAI,CAAC,8BAA8B,EAAED,MAAM,CAAC;EAC1D,CAAC;EACD4C,eAAeA,CAAE5C,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACD6C,iBAAiBA,CAAE7C,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACD8C,eAAeA,CAAE9C,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACD+C,uBAAuBA,CAAE/C,MAAM,EAAE;IAAE;IACjC,OAAOH,IAAI,CAACI,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACDgD,mBAAmBA,CAAEhD,MAAM,EAAE;IAAE;IAC7B,OAAOH,IAAI,CAACI,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACDiD,UAAUA,CAAEjD,MAAM,EAAE;IAAE;IACpB,OAAOH,IAAI,CAACI,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDkD,UAAUA,CAAElD,MAAM,EAAE;IAAE;IACpB,OAAOH,IAAI,CAACI,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDmD,aAAaA,CAAEnD,MAAM,EAAE;IAAE;IACvB,OAAOH,IAAI,CAACI,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDoD,UAAUA,CAAEpD,MAAM,EAAE;IAAE;IACpB,OAAOH,IAAI,CAACI,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDqD,WAAWA,CAAErD,MAAM,EAAE;IAAE;IACrB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDsD,WAAWA,CAAEtD,MAAM,EAAE;IAAE;IACrB,OAAOH,IAAI,CAACI,IAAI,CAAC,eAAe,EAAED,MAAM,CAAC;EAC3C,CAAC;EACDuD,aAAaA,CAAEvD,MAAM,EAAE;IAAE;IACvB,OAAOH,IAAI,CAACI,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDwD,cAAcA,CAAExD,MAAM,EAAE;IAAE;IACxB,OAAOH,IAAI,CAACI,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDyD,eAAeA,CAAEzD,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAAC6D,UAAU,CAAC,uBAAuB,EAAE1D,MAAM,EAAE,YAAM,CAAE,CAAC,CAAC;EACpE,CAAC;EACD2D,eAAeA,CAAE3D,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACD;EACA4D,qBAAqBA,CAAE5D,MAAM,EAAE;IAAE;IAC/B;IACA,OAAOH,IAAI,CAACI,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACD6D,iBAAiBA,CAAE7D,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAAC6D,UAAU,CAAC,qBAAqB,EAAE1D,MAAM,EAAE,YAAM,CAAE,CAAC,CAAC;EAClE,CAAC;EACD8D,kBAAkBA,CAAE9D,MAAM,EAAE;IAAE;IAC5B,OAAOH,IAAI,CAAC6D,UAAU,CAAC,sBAAsB,EAAE1D,MAAM,EAAE,YAAM,CAAE,CAAC,CAAC;EACnE,CAAC;EACD+D,mBAAmBA,CAAE/D,MAAM,EAAE;IAAE;IAC7B,OAAOH,IAAI,CAAC6D,UAAU,CAAC,2BAA2B,EAAE1D,MAAM,EAAE,YAAM,CAAE,CAAC,CAAC;EACxE,CAAC;EACDgE,iBAAiBA,CAAEhE,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACDiE,iBAAiBA,CAAEjE,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACDkE,uBAAuBA,CAAElE,MAAM,EAAE;IAAE;IACjC,OAAOH,IAAI,CAACI,IAAI,CAAC,sCAAsC,EAAED,MAAM,CAAC;EAClE,CAAC;EACDmE,uBAAuBA,CAAEnE,MAAM,EAAE;IAAE;IACjC,OAAOH,IAAI,CAACI,IAAI,CAAC,sCAAsC,EAAED,MAAM,CAAC;EAClE,CAAC;EACDoE,0BAA0BA,CAAEpE,MAAM,EAAE;IAAE;IACpC,OAAOH,IAAI,CAACI,IAAI,CAAC,yCAAyC,EAAED,MAAM,CAAC;EACrE,CAAC;EACDqE,WAAWA,CAAErE,MAAM,EAAE;IAAE;IACrB,OAAOH,IAAI,CAACI,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDsE,YAAYA,CAAEtE,MAAM,EAAEuE,QAAQ,EAAEC,EAAE,EAAE;IAClC;IACA,OAAO3E,IAAI,CAAC6D,UAAU,CAAC,iBAAiB,EAAE1D,MAAM,EAAEuE,QAAQ,EAAEC,EAAE,CAAC;EACjE,CAAC;EACDC,qBAAqBA,CAAEzE,MAAM,EAAE;IAAE;IAC/B,OAAOH,IAAI,CAAC6D,UAAU,CAAC,yBAAyB,EAAE1D,MAAM,EAAE,YAAM,CAAE,CAAC,CAAC;EACtE,CAAC;EACD0E,eAAeA,CAAE1E,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACD;EACA;EACA;EACA2E,cAAcA,CAAE3E,MAAM,EAAE;IAAE;IACxB,OAAOH,IAAI,CAACI,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACD4E,oBAAoBA,CAAE5E,MAAM,EAAE;IAAE;IAC9B,OAAOH,IAAI,CAACI,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACD6E,mBAAmBA,CAAE7E,MAAM,EAAE;IAAE;IAC7B,OAAOH,IAAI,CAACI,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDY,iBAAiBA,CAAEZ,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACD8E,eAAeA,CAAE9E,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACD+E,eAAeA,CAAE/E,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDgF,eAAeA,CAAEhF,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDiF,eAAeA,CAAEjF,MAAM,EAAE;IAAE;IACzB,OAAOH,IAAI,CAACI,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDkF,sBAAsBA,CAAElF,MAAM,EAAE;IAAE;IAChC,OAAOH,IAAI,CAACI,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDmF,mBAAmBA,CAAEnF,MAAM,EAAE;IAAE;IAC7B,OAAOH,IAAI,CAACI,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDoF,mBAAmBA,CAAEpF,MAAM,EAAE;IAAE;IAC7B,OAAOH,IAAI,CAACI,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDqF,UAAUA,CAAErF,MAAM,EAAE;IAAE;IACpB,OAAOH,IAAI,CAACI,IAAI,CAAC,cAAc,EAAED,MAAM,CAAC;EAC1C,CAAC;EACDsF,WAAWA,CAAEtF,MAAM,EAAE;IAAE;IACrB,OAAOH,IAAI,CAACI,IAAI,CAAC,eAAe,EAAED,MAAM,CAAC;EAC3C,CAAC;EACDuF,iBAAiBA,CAAEvF,MAAM,EAAE;IAAE;IAC3B,OAAOH,IAAI,CAACI,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD;AACF,CAAC;AACD,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}