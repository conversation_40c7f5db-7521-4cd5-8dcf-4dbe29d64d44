<template>
  <div class="SuggestControls">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-popover placement="bottom" title="您可以查找：" trigger="hover" :width="250">
          <div class="tips-UL">
            <div>提案名称</div>
            <div>提案编号</div>
            <div>提案人<strong>(名称前加 n 或 N)</strong></div>
            <div>全部办理单位<strong>(名称前加 d 或 D)</strong></div>
            <div>主办单位<strong>(名称前加 m 或 M)</strong></div>
            <div>协办单位<strong>(名称前加 j 或 J)</strong></div>
          </div>
          <template #reference>
            <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
          </template>
        </el-popover>
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"></xyl-global-table>
        <xyl-global-table-button :data="tableButtonList" :elWhetherShow="handleElWhetherShow"
          @buttonClick="handleCommand" :max="route.query.tableId == 'id_prop_proposal_main' ? 3 : 1"
          :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel :name="route.query.moduleName" :exportId="exportId" :params="exportParams"
        module="proposalExportExcel" :tableId="route.query.tableId" @excelCallback="callback"
        :handleExcelData="handleExcelData"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="setRemindTypeShow" :name="canEdit ? '设置督办类型' : '上传督办报告'">
      <SetRemindType :rowId="rowId" :superviseInfoId="superviseInfoId" :canEdit="canEdit"
        @callback="remindTypeCallback" />
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestControls' }
</script>
<script setup>
import { ref } from 'vue'
import api from '@/api'
import { onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
import SetRemindType from './setRemindType.vue'
const route = useRoute()
const buttonList = [
  { id: 'noEmphasis', name: '撤销重点提案', type: 'primary', has: 'no_emphasis' },
  { id: 'noOpen', name: '取消公开提案', type: 'primary', has: 'no_open' },
  { id: 'noExcellent', name: '取消优秀提案', type: 'primary', has: 'no_excellent' },
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
]
const tableButtonList = [
  { id: 'edit', name: '编辑', width: 100, has: 'edit' },
  { id: 'setRemindType', name: '设置督办类型', width: 120, has: 'setRemindType', whetherShow: true },
  { id: 'uploadRemindReport', name: '上传督办报告', width: 120, has: 'uploadRemindReport', whetherShow: true }
]
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: route.query.tableId, tableApi: 'suggestionList' })

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    handleQuery()
  }
})
const handleExcelData = (_item) => {
  _item.forEach(v => {
    if (!v.mainHandleOffices) {
      v.mainHandleOffices = v.publishHandleOffices
    }
  })
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'noEmphasis':
      handleMajor(0)
      break
    case 'noOpen':
      handleOpen(0)
      break
    case 'noExcellent':
      handleExcellent(0)
      break
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleElWhetherShow = (row, isType) => {
  if (isType == 'setRemindType') {
    return route.query.tableId == 'id_prop_proposal_main' &&
      (JSON.parse(sessionStorage.getItem('user')).specialRoleKeys.includes('admin') ||
        JSON.parse(sessionStorage.getItem('user')).specialRoleKeys.includes('proposal_committee'))
      ? true
      : false
  } else if (isType == 'uploadRemindReport') {
    return route.query.tableId == 'id_prop_proposal_main' &&
      (row.superviseLeader == JSON.parse(sessionStorage.getItem('user')).id ||
        (row.superviseGroup && row.superviseGroup == JSON.parse(sessionStorage.getItem('user')).committee.value))
      ? true
      : false
  }
}

const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    case 'setRemindType':
      handleSetRemindType(row, true)
      break
    case 'uploadRemindReport':
      handleSetRemindType(row, false)
      // handleUploadRemindReport(row)
      break
    default:
      break
  }
}
const rowId = ref('')
const superviseInfoId = ref('')
const setRemindTypeShow = ref(false)
const canEdit = ref(false)
const remindTypeCallback = () => {
  setRemindTypeShow.value = false
  handleQuery()
}
const handleSetRemindType = (row, type) => {
  rowId.value = row.id
  superviseInfoId.value = row.superviseInfoId
  canEdit.value = type
  setRemindTypeShow.value = true
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: {
      name: '提案详情',
      path: '/proposal/SuggestDetail',
      query: { id: item.id, superviseInfoId: item.superviseInfoId }
    }
  })
}
const handleEdit = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '编辑提案', path: '/proposal/SubmitSuggest', query: { id: item.id } }
  })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
}
// 公开
const handleOpen = (type) => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将${type ? '' : '取消'}公开选中的提案, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionOpen(type)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: `已取消${type ? '公开' : '操作'}` })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionOpen = async (type) => {
  const { code } = await api.suggestionOpen({ ids: tableDataArray.value.map((v) => v.id), isOpen: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: `${type ? '公开' : '取消'}成功` })
    tableRefReset()
    handleQuery()
  }
}
// 重点
const handleMajor = (type) => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将${type ? '选中的提案推荐为重点提案' : '撤销当前选中的重点提案'}, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionMajor(type)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: `已取消${type ? '推荐' : '撤销'}` })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionMajor = async (type) => {
  const { code } = await api.suggestionMajor({ ids: tableDataArray.value.map((v) => v.id), isMajorSuggestion: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: `${type ? '推荐' : '撤销'}成功` })
    tableRefReset()
    handleQuery()
  }
}
// 优秀
const handleExcellent = (type) => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm(`此操作将${type ? '选中的提案推荐为优秀提案' : '撤销当前选中的优秀提案'}, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionExcellent(type)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: `已取消${type ? '推荐' : '撤销'}` })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionExcellent = async (type) => {
  const { code } = await api.suggestionExcellent({ ids: tableDataArray.value.map((v) => v.id), isExcellent: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: `${type ? '推荐' : '撤销'}成功` })
    tableRefReset()
    handleQuery()
  }
}
</script>
<style lang="scss">
.SuggestControls {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
