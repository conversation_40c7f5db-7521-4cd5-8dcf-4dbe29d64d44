<!--
 * @Description: 提案细分
 -->
<template>
  <el-scrollbar always class="SuggestedSubdivide" v-loading="loading" :lement-loading-text="loadingText">
    <div class="SubmitSuggestBody">
      <div class="leftBox">
        <div class="titleClas">
          <el-icon>
            <DArrowLeft />
          </el-icon>
          <div class="titleMidC">
            <img class="iconCla" src="../../assets/img/column.png" alt="">
            未选择办理单位
          </div>
          <el-icon>
            <DArrowRight />
          </el-icon>
        </div>
        <div>
          <div class="tipsClas">
            <el-checkbox style="display: none;" v-model="checkAll" :indeterminate="isIndeterminate"
              @change="handleCheckAllChange">checkAll</el-checkbox>
            <div class="sugclass" style="padding-bottom: 5px;">
              <div class="leftCTip">提案大类：</div>
              <el-select v-model="form.SuggestBigType" placeholder="请选择提案大类" @change="SuggestBigTypeChange" clearable>
                <el-option v-for="item in BigTypeArr" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </div>
            <div class="sugclass" style="padding-top: 5px;">
              <div class="leftCTip">提案小类：</div>
              <el-select v-model="form.SuggestSmallType" placeholder="请选择提案小类" clearable>
                <el-option v-for="item in SmallTypeArr" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </div>
          </div>
          <div class="contentCla">
            <el-scrollbar always class="scrollbarClas">
              <el-checkbox-group v-model="checkListLeft" @change="handleCheckedCitiesChange" class="checkBoxClas">
                <el-checkbox v-for="item in leftArr" :key="item.id" :label="item.id">
                  <!-- <el-tooltip effect="dark"
                              :show-after="500"
                              :content="item.title"
                              placement="top-start"> -->
                  <div @click.prevent="chekDetail(item)" :title="item.title" class="titleTips ellipsis"> <span
                      v-if="item.streamNumber">[{{ item.streamNumber }}]</span>
                    {{ item.title }}
                  </div>
                  <!-- </el-tooltip> -->
                </el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>
            <el-pagination class="paginationCla" v-model:currentPage="SugData.pageNo"
              v-model:page-size="SugData.pageSize" :page-sizes="SugData.pageSizes"
              layout="sizes, prev, pager, next, total" @size-change="ChangeSize" @current-change="ChangePageNo"
              :pager-count="5" :total="SugData.total" small />
          </div>
        </div>
      </div>
      <div class="middleBox">
        <div class="midTop">
          <img class="iconCla2" src="../../assets/img/swop.png" alt="">
        </div>
        <div class="midButtom">
          <el-button style="margin: 0px 0px 10px 0px;" :disabled="toCategory" class="btn" type="primary" :icon="Right"
            @click="Category()">选择</el-button>
          <el-button style="margin: 20px 0px 0px 0px;" type="primary" class="btn" :disabled="toBack" @click="sendBack()"
            :icon="Back">退回</el-button>
        </div>
      </div>
      <div class="rightBox">
        <div class="titleClasRight">
          <el-icon>
            <DArrowLeft />
          </el-icon>
          <div class="titleMidC">
            <img class="iconCla" src="../../assets/img/column.png" alt="">
            已选择办理单位
          </div>
          <el-icon>
            <DArrowRight />
          </el-icon>
        </div>
        <div>
          <div class="tipsClasRight">
            <el-checkbox style="display: none;" v-model="checkAllRight" :indeterminate="isIndeterminateRight"
              @change="handleCheckAllChangeRight">checkAll</el-checkbox>

          </div>
          <div class="contentClaRight">
            <el-scrollbar always class="scrollbarClasRight">
              <el-checkbox-group v-model="checkListRight" @change="handleCheckedCitiesChangeRight" class="checkBoxClas">
                <el-checkbox v-for="item in rightArr" :key="item.id" :label="item.id">
                  <!-- <el-tooltip effect="dark"
                              :show-after="500"
                              :content="item.title"
                              placement="top-start"> -->
                  <div @click.prevent="chekDetail(item)" :title="item.title" class="titleTips ellipsis"><span
                      v-if="item.streamNumber">[{{ item.streamNumber }}]</span>
                    {{ item.title }}
                  </div>
                  <!-- </el-tooltip> -->
                </el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>
            <el-pagination class="paginationClaRight" v-model:currentPage="SugDataRight.pageNo"
              v-model:page-size="SugDataRight.pageSize" :page-sizes="SugDataRight.pageSizes"
              layout="sizes, prev, pager, next, total" @size-change="ChangeSizeRight"
              @current-change="ChangePageNoRight" :pager-count="5" :total="SugDataRight.total" small />
          </div>
        </div>
      </div>
    </div>
    <xyl-popup-window v-model="show" name="选择办理单位">
      <SelectHandlingUnit :ids="checkListLeft" @callback="SelectCallback"></SelectHandlingUnit>
    </xyl-popup-window>
  </el-scrollbar>
</template>
<script>
export default {
  components: { SelectHandlingUnit }, name: 'SuggestedSubdivide'
}
</script>
<script setup>
import api from '@/api'
import { Back, Right } from '@element-plus/icons-vue'
import { reactive, ref, onActivated, watch } from 'vue'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage } from 'element-plus'
import SelectHandlingUnit from './SelectHandlingUnit.vue'

const loading = ref(false)
const loadingText = ref('')
const show = ref(false)
const toCategory = ref(false)
const toBack = ref(false)
const BigTypeArr = ref([])
const SmallTypeArr = ref([])
const form = reactive({
  SuggestBigType: '', // 提案大类
  SuggestBigTypeName: '',
  SuggestSmallType: '', // 提案小类
  SuggestSmallTypeName: ''
})

const SuggestBigTypeChange = () => {
  if (form.SuggestBigType) {
    for (let index = 0; index < BigTypeArr.value.length; index++) {
      const item = BigTypeArr.value[index]
      if (item.id === form.SuggestBigType) {
        form.SuggestBigTypeName = item.name
        form.SuggestSmallType = ''
        SmallTypeArr.value = item.children
      }
    }
  } else {
    form.SuggestBigTypeName = ''
    form.SuggestSmallType = ''
    SmallTypeArr.value = []
  }
}
const suggestionThemeSelect = async () => {
  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })
  var { data } = res
  BigTypeArr.value = data
}
const chekDetail = (item) => {
  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } } })
}

onActivated(() => {
  suggestionThemeSelect()
  RightInfo()
})

const SelectCallback = (submit) => {
  if (submit) {
    leftInfo()
    RightInfo()
  }
  show.value = false
}

// 未选择办理单位
const SugData = reactive({
  total: 0,
  pageNo: 1,
  pageSize: 20,
  pageSizes: [10, 20, 50, 80]
})
const checkAll = ref(false)
const isIndeterminate = ref(true)
const checkListLeft = ref([])
const leftArr = ref([])
const ChangePageNo = (i) => {
  SugData.pageNo = i
  leftInfo()
}
const ChangeSize = (i) => {
  SugData.pageSize = i
  leftInfo()
}
const handleCheckAllChange = (val) => {
  checkListLeft.value = val ? leftArr.value.map(v => v.id) : []
  isIndeterminate.value = false
}
const handleCheckedCitiesChange = (val) => {
  const checkedCount = val.length
  checkAll.value = checkedCount === leftArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < leftArr.value.length
}
const leftInfo = async () => {
  try {
    var params = {
      keyword: '',
      pageNo: SugData.pageNo,
      pageSize: SugData.pageSize,
      bigThemeId: form.SuggestBigType,
      smallThemeId: form.SuggestSmallType
    }
    const res = await api.reqFindOfficeProposal('empty', params) //查询未分类提案
    var { data, total } = res
    leftArr.value = data
    SugData.total = total
    checkListLeft.value = []
  } catch (err) {
    // console.log('🚀 ~ file: SuggestedSubdivide.vue:176 ~ leftInfo ~ err:', err)
  }
}

//已选择办理单位
const SugDataRight = reactive({
  total: 0,
  pageNo: 1,
  pageSize: 20,
  pageSizes: [10, 20, 50, 80]
})
const checkListRight = ref([])
const rightArr = ref([])
const checkAllRight = ref(false)
const isIndeterminateRight = ref(true)
const ChangePageNoRight = (i) => {
  SugDataRight.pageNo = i
  RightInfo()
}
const ChangeSizeRight = (i) => {
  SugDataRight.pageSize = i
  RightInfo()
}
const handleCheckAllChangeRight = (val) => {
  checkListRight.value = val ? rightArr.value.map(v => v.id) : []
  isIndeterminateRight.value = false
}
const handleCheckedCitiesChangeRight = (val) => {
  const checkedCount = val.length
  checkAllRight.value = checkedCount === rightArr.value.length
  isIndeterminateRight.value = checkedCount > 0 && checkedCount < rightArr.value.length
}
const RightInfo = async () => {
  try {
    var params = {
      keyword: '',
      pageNo: SugDataRight.pageNo,
      pageSize: SugDataRight.pageSize
    }
    const res = await api.reqFindOfficeProposal('notempty', params) //查询已分类提案
    var { data, total } = res
    rightArr.value = data
    SugDataRight.total = total
    checkListRight.value = []
  } catch (err) {
    // console.log('🚀 ~ file: SuggestedSubdivide.vue:215 ~ RightInfo ~ err:', err)
  }
}

// 操作按钮
const Category = async () => {
  show.value = true
}
const sendBack = async () => {
  var idsArr = checkListRight.value
  const res = await api.reqProposalBatchComplete('clear', { suggestionIds: idsArr }) //查询已分类提案
  var { code, message } = res
  if (code == 200) {
    ElMessage.success(message)
    leftInfo()
    RightInfo()
  }
}

watch(() => checkListLeft.value, (val) => {
  if (val) {
    toCategory.value = val.length > 0
    if (val.length > 0) {
      toCategory.value = false
    } else {
      toCategory.value = true
    }
  }
}, { immediate: true })
watch(() => checkListRight.value, (val) => {
  if (val) {
    toBack.value = val.length > 0
    if (val.length > 0) {
      toBack.value = false
    } else {
      toBack.value = true
    }
  }
}, { immediate: true })
watch(() => form.SuggestBigType, (val) => {
  leftInfo()
}, { immediate: true })
watch(() => form.SuggestSmallType, (nowVal, oldVal) => {
  if (nowVal) {
    BigTypeArr.value.forEach((v) => {
      if (form.SuggestBigType === v.id) {
        v.children.forEach((vv) => { if (vv.id === nowVal) { form.SuggestSmallTypeName = vv.name } })
      }
    })
    leftInfo()
  } else { form.SuggestSmallTypeName = '' }
  if (oldVal && nowVal === '') { leftInfo() }
}, { immediate: false })

</script>
<style lang="scss">
.SuggestedSubdivide {
  width: 100%;
  height: 100%;

  .popCLa {
    width: 800px;
    min-height: 400px;
  }

  .SubmitSuggestBody {
    padding: 20px 20px 10px 20px;
    width: 1000px;
    margin: 10px auto;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
    display: flex;
    justify-content: space-evenly;

    .leftBox {
      width: 400px;

      .titleClas {
        background: #999999;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        font-size: 22px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        margin-bottom: 10px;
        padding: 0 20px;

        .titleMidC {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 16px;
        }

        .iconCla {
          height: 24px;
          padding-right: 12px;
        }
      }

      .tipsClas {
        font-size: 14px;
        font-weight: 400;
        color: #999999;

        .sugclass {
          display: flex;
          align-items: center;

          .leftCTip {
            font-weight: bold;
            color: #333333;
          }

          .zy-el-select {
            width: 240px;
          }
        }
      }

      .contentCla {
        margin-top: 10px;

        .scrollbarClas {
          height: calc(100vh - 372px);
          border: 1px solid #cccccc;
          padding: 0 10px;

          .checkBoxClas {
            display: flex;
            flex-direction: column;

            .titleTips {
              width: 350px;

              &:hover {
                color: var(--zy-el-color-primary);
              }
            }
          }
        }
      }

      .paginationCla {
        padding-top: 6px;
        overflow-x: auto;
      }
    }

    .middleBox {
      width: 100px;
      display: flex;
      flex-direction: column;

      .midTop {
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;

        .iconCla2 {
          height: 40px;
        }
      }

      .midButtom {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-bottom: 100px;
      }
    }

    .rightBox {
      width: 400px;

      .titleClasRight {
        background: var(--zy-el-color-primary);
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        font-size: 22px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        margin-bottom: 10px;
        padding: 0 20px;

        .titleMidC {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 16px;
        }

        .iconCla {
          height: 24px;
          padding-right: 12px;
        }
      }

      .tipsClasRight {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
      }

      .contentClaRight {
        margin-top: 10px;

        .scrollbarClasRight {
          height: calc(100vh - 280px);
          border: 1px solid #cccccc;
          padding: 0 10px;

          .checkBoxClas {
            display: flex;
            flex-direction: column;

            .titleTips {
              width: 350px;

              &:hover {
                color: var(--zy-el-color-primary);
              }
            }
          }
        }
      }

      .paginationClaRight {
        padding-top: 6px;
        overflow-x: auto;
      }
    }
  }
}
</style>
