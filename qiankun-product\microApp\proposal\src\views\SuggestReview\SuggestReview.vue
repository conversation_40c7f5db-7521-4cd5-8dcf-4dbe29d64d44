<template>
  <div class="SuggestReview">
    <xyl-search-button
      @queryClick="handleQuery"
      @resetClick="handleReset"
      @handleButton="handleButton"
      :buttonList="buttonList"
      :data="tableHead"
      ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table
        ref="tableRef"
        row-key="id"
        :data="tableData"
        @select="handleTableSelect"
        @select-all="handleTableSelect"
        @sort-change="handleSortChange"
        :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <el-table-column label="锁定情况" width="100">
          <template #default="scope">
            <el-tooltip
              effect="dark"
              v-if="scope.row.lockVo?.isLock"
              :content="`提案已被锁定（由${scope.row.lockVo?.lockUserName}于${format(
                scope.row.lockVo?.lockDate
              )} 锁定）`"
              placement="top-start">
              <el-icon class="SuggestReviewUnlockIcon">
                <Lock />
              </el-icon>
            </el-tooltip>
          </template>
        </el-table-column>
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"></xyl-global-table>
        <xyl-global-table-button
          :data="tableButtonList"
          @buttonClick="handleCommand"
          :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel
        :name="route.query.moduleName"
        :exportId="exportId"
        :params="exportParams"
        module="proposalExportExcel"
        :tableId="route.query.tableId"
        @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestReview' }
</script>
<script setup>
import api from '@/api'
import { onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { format } from 'common/js/time.js'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
const route = useRoute()
const buttonList = [
  { id: 'next', name: '批量审查', type: 'primary', has: 'next' },
  { id: 'unlock', name: '解锁提案', type: 'primary', has: 'unlock' },
  { id: 'refresh', name: '刷新锁定情况', type: 'primary', has: 'refresh' },
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
]
const tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: 'edit' }]
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: route.query.tableId, tableApi: 'suggestionList' })

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    handleQuery()
  }
})
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'next':
      handleNext()
      break
    case 'unlock':
      handleUnlock()
      break
    case 'refresh':
      handleQuery()
      break
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: {
      name: '提案详情',
      path: '/proposal/SubmitSuggest',
      query: { id: item.id, reviewName: route.query.reviewName, type: 'review' }
    }
  })
}
const handleEdit = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '编辑提案', path: '/proposal/SubmitSuggest', query: { id: item.id } }
  })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
}

// 启用/禁用
const handleUnlock = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作将解锁选中的提案, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionUnlock()
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消解锁' })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionUnlock = async () => {
  const { code } = await api.suggestionUnlock({ ids: tableDataArray.value.map((v) => v.id) })
  if (code === 200) {
    ElMessage({ type: 'success', message: '解锁成功' })
    tableRefReset()
    handleQuery()
  }
}
const handleNext = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作会将当前选中的提案审查通过, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionBatchComplete()
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消审查' })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionBatchComplete = async () => {
  const { code } = await api.suggestionBatchComplete({
    suggestionIds: tableDataArray.value.map((v) => v.id),
    nextNodeId: route.query.nextNode || 'prepareSubmitHandle'
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '审查成功' })
    tableRefReset()
    handleQuery()
  }
}
</script>
<style lang="scss">
.SuggestReview {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));

    .SuggestReviewUnlockIcon {
      position: absolute;
      top: 50%;
      left: 16px;
      transform: translateY(-50%);
      z-index: 2;
      font-size: 22px;
    }
  }
}
</style>
