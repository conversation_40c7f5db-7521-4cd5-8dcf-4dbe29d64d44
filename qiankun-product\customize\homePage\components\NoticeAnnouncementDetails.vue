<template>
  <el-scrollbar class="NoticeAnnouncementDetails">
    <!-- <div class="detailsFunctionBox">
      <div class="detailsFunction">
        <div class="detailsPrint"
             @click="handlePrint">打印</div>
        <div class="detailsExportWord"
             @click="handleExportWord">导出Word</div>
      </div>
    </div> -->
    <div class="NoticeAnnouncementDetailsBody">
      <div class="NoticeAnnouncementDetailsPrint" ref="printRef">
        <div class="NoticeAnnouncementDetailsName">{{ details.theme }}</div>
        <div class="NoticeAnnouncementDetailsInfo">
          <div class="NoticeAnnouncementDetailsType no-print">{{ details.channelName }}</div>
          <div class="NoticeAnnouncementDetailsText">发布部门：{{ details.publishOfficeName }}</div>
          <div class="NoticeAnnouncementDetailsText">发布时间：{{ format(details.publishTime) }}</div>
          <div class="NoticeAnnouncementDetailsText no-print">阅读量：{{ quantity }}</div>
        </div>
        <div class="NoticeAnnouncementDetailsContent" v-html="details.content"></div>
      </div>
      <xyl-global-file :fileData="details.attachments"></xyl-global-file>
    </div>
  </el-scrollbar>
</template>
<script>
export default { name: 'NoticeAnnouncementDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { format } from 'common/js/time.js'
import { exportWordHtmlObj } from 'common/config/MicroGlobal'
import { Print } from 'common/js/print'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' } })
const route = useRoute()
const printRef = ref()
const quantity = ref(0)
const details = ref({})
onMounted(() => {
  clickRecord()
  NoticeAnnouncementInfo()
})
const NoticeAnnouncementInfo = async () => {
  const res = await api.NoticeAnnouncementInfo({ detailId: props.id })
  var { data } = res
  details.value = data
}
const clickRecord = async () => {
  const { code } = await api.clickRecord(`notification/${props.id}`)
  if (code === 200) {
    clickAcquire()
  }
}
const clickAcquire = async () => {
  const { data } = await api.clickAcquire(`notification/${props.id}`)
  quantity.value = data
}
// const handleExportWord = () => {
//   if (JSON.stringify(details.value) === '{}') return ElMessage({ type: 'warning', message: '请等待通知公告详情加载完成再进行导出！' })
//   exportWordHtmlObj({ code: 'noticeAnnouncementDetails', name: details.value.theme, key: 'content', data: { ...details.value, publishTime: format(details.value.publishTime) } })
// }
// const handlePrint = () => {
//   Print.init(printRef.value)
// }
</script>
<style lang="scss">
.NoticeAnnouncementDetails {
  width: 990px;
  height: 100%;

  .zy-el-scrollbar__view {
    padding: 0 20px;
  }

  // .detailsFunctionBox {
  //   width: 990px;
  //   position: absolute;
  //   top: 20px;
  //   left: 50%;
  //   transform: translateX(-50%);
  //   margin-right: 200px;

  //   .detailsFunction {
  //     position: absolute;
  //     top: 0;
  //     right: 0;
  //     transform: translateX(112%);

  //     .detailsPrint,
  //     .detailsExportWord {
  //       font-size: var(--zy-text-font-size);
  //       line-height: var(--zy-line-height);
  //       padding-left: 30px;
  //       margin-bottom: 20px;
  //       position: relative;
  //       cursor: pointer;
  //     }

  //     .detailsPrint {
  //       background: url("../../assets/img/details_print.png") no-repeat;
  //       background-size: 20px 20px;
  //       background-position: left center;
  //     }

  //     .detailsExportWord {
  //       background: url("../../assets/img/details_export_word.png") no-repeat;
  //       background-size: 20px 20px;
  //       background-position: left center;
  //     }
  //   }
  // }

  .NoticeAnnouncementDetailsBody {
    max-width: 990px;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
    position: relative;
    padding: 20px 40px;
    margin: 20px auto;
  }
}

.NoticeAnnouncementDetailsPrint {
  width: 100%;

  .NoticeAnnouncementDetailsName {
    font-size: var(--zy-title-font-size);
    text-align: center;
    padding: 0 40px;
    line-height: var(--zy-line-height);
  }

  .NoticeAnnouncementDetailsInfo {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0 10px 0;
    border-bottom: 1px solid #eee;

    .NoticeAnnouncementDetailsType {
      color: var(--zy-el-color-primary);
      border: 1px solid var(--zy-el-color-primary);
      font-size: var(--zy-text-font-size);
      margin-right: 12px;
      padding: 0 6px;
      border-radius: 2px;
    }

    .NoticeAnnouncementDetailsText {
      margin-right: 58px;
      font-size: var(--zy-text-font-size);
    }
  }

  .NoticeAnnouncementDetailsContent {
    padding: 20px 0;
    overflow: hidden;
    line-height: var(--zy-line-height);

    img,
    video {
      max-width: 100%;
      height: auto !important;
    }

    table {
      border-collapse: collapse;
      border-spacing: 0;

      tr {
        page-break-inside: avoid;
      }
    }
  }
}
</style>
