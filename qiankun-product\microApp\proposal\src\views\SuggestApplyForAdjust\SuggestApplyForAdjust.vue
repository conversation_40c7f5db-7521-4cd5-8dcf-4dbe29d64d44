<template>
  <div class="SuggestApplyForAdjust">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #mainHandleOffices="scope">
            <template v-if="scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0">
              {{scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
            <template v-else>
              {{scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
          </template>
          <template #assistHandleOffices="scope">
            <template v-if="scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0">
              {{scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
            <template v-else>
              {{scope.row.assistHandleVoList?.map(v => v.flowHandleOfficeName).join('、')}}
            </template>
          </template>
          <!-- <template #publishHandleOffices="scope">
            {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}
          </template> -->
        </xyl-global-table>
        <xyl-global-table-button :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="申请调整提案" :exportId="exportId" :params="exportParams" module="proposalExportExcel"
        tableId="id_prop_proposal_adjust" @excelCallback="callback"
        :handleExcelData="handleExcelData"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestApplyForAdjust' }
</script>
<script setup>
import { onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
const buttonList = [
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
]
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: 'id_prop_proposal_adjust', tableApi: 'suggestionList' })

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    handleQuery()
  }
})
const handleExcelData = (_item) => {
  _item.forEach(v => {
    if (!v.mainHandleOffices) {
      v.mainHandleOffices = v.publishHandleOffices
    }
  })
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id, type: 'adjust' } }
  })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
}
</script>
<style lang="scss">
.SuggestApplyForAdjust {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
