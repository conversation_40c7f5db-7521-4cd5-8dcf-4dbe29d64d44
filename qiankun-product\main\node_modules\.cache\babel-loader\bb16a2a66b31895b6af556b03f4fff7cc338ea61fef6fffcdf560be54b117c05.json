{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalAiChatHistoryScroll\"\n};\nvar _hoisted_2 = {\n  key: 1,\n  class: \"GlobalAiChatHistoryLoadingText\"\n};\nvar _hoisted_3 = {\n  key: 2,\n  class: \"GlobalAiChatHistoryLoadingText\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_tree = _resolveComponent(\"el-tree\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  return _openBlock(), _createBlock(_component_el_popover, {\n    visible: $setup.show,\n    \"onUpdate:visible\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.show = $event;\n    }),\n    trigger: \"click\",\n    placement: \"bottom-start\",\n    \"popper-class\": \"GlobalAiChatHistoryPopover\",\n    transition: \"zy-el-zoom-in-top\"\n  }, {\n    reference: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        class: \"GlobalAiChatDialogueHistory\"\n      }, [_createElementVNode(\"span\", {\n        innerHTML: $setup.historyIcon\n      })])];\n    }),\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_scrollbar, {\n        ref: \"scrollRef\",\n        class: \"GlobalAiChatHistoryScrollbar\",\n        onScroll: $setup.handleScroll\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_1, [$setup.tableData.length ? (_openBlock(), _createBlock(_component_el_tree, {\n            key: 0,\n            ref: \"treeRef\",\n            \"node-key\": \"id\",\n            \"highlight-current\": \"\",\n            data: $setup.tableData,\n            props: {\n              children: 'children',\n              label: 'userQuestion'\n            },\n            onNodeClick: $setup.handleNodeClick\n          }, null, 8 /* PROPS */, [\"data\"])) : _createCommentVNode(\"v-if\", true), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, \"加载中...\")) : _createCommentVNode(\"v-if\", true), $setup.isShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, \"没有更多了\")) : _createCommentVNode(\"v-if\", true)])];\n        }),\n        _: 1 /* STABLE */\n      }, 512 /* NEED_PATCH */)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"visible\"]);\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_el_popover", "visible", "$setup", "show", "_cache", "$event", "trigger", "placement", "transition", "reference", "_withCtx", "_createElementVNode", "innerHTML", "historyIcon", "default", "_createVNode", "_component_el_scrollbar", "ref", "onScroll", "handleScroll", "_hoisted_1", "tableData", "length", "_component_el_tree", "data", "props", "children", "label", "onNodeClick", "handleNodeClick", "_createCommentVNode", "loading", "_createElementBlock", "_hoisted_2", "isShow", "_hoisted_3", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalAiChat\\GlobalAiChatHistory.vue"], "sourcesContent": ["<template>\r\n  <el-popover v-model:visible=\"show\" trigger=\"click\" placement=\"bottom-start\" popper-class=\"GlobalAiChatHistoryPopover\"\r\n    transition=\"zy-el-zoom-in-top\">\r\n    <template #reference>\r\n      <div class=\"GlobalAiChatDialogueHistory\"><span v-html=\"historyIcon\"></span></div>\r\n    </template>\r\n    <el-scrollbar ref=\"scrollRef\" class=\"GlobalAiChatHistoryScrollbar\" @scroll=\"handleScroll\">\r\n      <div class=\"GlobalAiChatHistoryScroll\">\r\n        <el-tree ref=\"treeRef\" node-key=\"id\" highlight-current :data=\"tableData\"\r\n          :props=\"{ children: 'children', label: 'userQuestion' }\" @node-click=\"handleNodeClick\"\r\n          v-if=\"tableData.length\" />\r\n        <div class=\"GlobalAiChatHistoryLoadingText\" v-if=\"loading\">加载中...</div>\r\n        <div class=\"GlobalAiChatHistoryLoadingText\" v-if=\"isShow\">没有更多了</div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </el-popover>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChatHistory' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick, watch } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: [String, Number]\r\n})\r\nconst emit = defineEmits(['update:modelValue', 'select'])\r\nconst historyIcon =\r\n  '<svg t=\"1741231838961\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"6487\" width=\"26\" height=\"26\"><path d=\"M896 234.666667H128V149.333333h768v85.333334zM341.333333 661.333333H128v-85.333333h213.333333v85.333333zM426.666667 448H128v-85.333333h298.666667v85.333333zM426.666667 874.666667H128v-85.333334h298.666667v85.333334zM682.666667 448a170.666667 170.666667 0 1 0 0 341.333333 170.666667 170.666667 0 0 0 0-341.333333z m-256 170.666667c0-141.376 114.624-256 256-256s256 114.624 256 256-114.624 256-256 256-256-114.624-256-256z\" fill=\"#000000\" p-id=\"6488\"></path><path d=\"M725.333333 490.666667v128h-85.333333v-128h85.333333z\" fill=\"#000000\" p-id=\"6489\"></path><path d=\"M810.666667 682.666667h-170.666667v-85.333334h170.666667v85.333334z\" fill=\"#000000\" p-id=\"6490\"></path></svg>'\r\nconst scrollRef = ref()\r\nconst loadingScroll = ref(false)\r\nconst treeRef = ref()\r\nconst show = ref(false)\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst totals = ref(0)\r\nconst isShow = ref(false)\r\nconst loading = ref(true)\r\nconst tableId = ref('')\r\nconst tableInfo = ref({})\r\nconst tableData = ref([])\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\n\r\nonMounted(() => {\r\n  aigptChatClusterList()\r\n})\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (!scrollRef.value) return\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {\r\n    load()\r\n  }\r\n}\r\nconst load = () => {\r\n  if (pageNo.value * pageSize.value >= totals.value) return\r\n  loadingScroll.value = true\r\n  pageNo.value += 1\r\n  aigptChatClusterList()\r\n}\r\nconst aigptChatClusterList = async () => {\r\n  const { data, total } = await api.aigptChatClusterList({\r\n    key: guid(),\r\n    pageNo: pageNo.value,\r\n    pageSize: pageSize.value\r\n  })\r\n  tableData.value = [...tableData.value, ...data]\r\n  totals.value = total\r\n  loading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  selectedMethods(tableData.value, tableId.value)\r\n  loadingScroll.value = false\r\n}\r\nconst handleNodeClick = (data) => {\r\n  show.value = false\r\n  emit('update:modelValue', data.id)\r\n}\r\n// 首次进来默认选中\r\nconst selectedMethods = (data, id, type) => {\r\n  data.forEach((item) => {\r\n    if (item.id === id) {\r\n      if (JSON.stringify(tableInfo.value) !== JSON.stringify(item)) {\r\n        tableInfo.value = item\r\n        emit('select', tableInfo.value, type)\r\n        nextTick(() => {\r\n          treeRef.value.setCurrentKey(id)\r\n        })\r\n      } else {\r\n        nextTick(() => {\r\n          treeRef.value.setCurrentKey(id)\r\n        })\r\n      }\r\n    }\r\n    if (item.children && item.children.length) selectedMethods(item.children, id, type)\r\n  })\r\n}\r\nconst handleCurrentChat = async (code, id) => {\r\n  const { data } = await api.aigptChatClusterList({\r\n    key: guid(),\r\n    pageNo: 1,\r\n    pageSize: 1,\r\n    query: { chatBusinessScene: code, businessId: id || null }\r\n  })\r\n  if (data.length) {\r\n    emit('update:modelValue', data[0].id)\r\n    tableInfo.value = data[0]\r\n    emit('select', tableInfo.value)\r\n    nextTick(() => {\r\n      treeRef.value.setCurrentKey(data[0].id)\r\n    })\r\n  }\r\n}\r\nconst handleRefresh = async () => {\r\n  const { data, total } = await api.aigptChatClusterList({ pageNo: 1, pageSize: tableData.value.length })\r\n  tableData.value = data\r\n  totals.value = total\r\n  loading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  selectedMethods(tableData.value, tableId.value, true)\r\n}\r\nwatch(\r\n  () => props.modelValue,\r\n  () => {\r\n    tableId.value = props.modelValue\r\n    if (tableId.value !== tableInfo.value.id) tableInfo.value = {}\r\n    nextTick(() => {\r\n      treeRef.value?.setCurrentKey(null)\r\n    })\r\n    selectedMethods(tableData.value, tableId.value, false)\r\n  },\r\n  { immediate: true }\r\n)\r\ndefineExpose({ refresh: handleRefresh, handleCurrentChat })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiChatHistoryPopover {\r\n  width: 320px !important;\r\n  padding: 0 !important;\r\n\r\n  .GlobalAiChatHistoryScrollbar {\r\n    width: 100%;\r\n    height: 320px;\r\n\r\n    .zy-el-tree-node {\r\n      width: 100%;\r\n\r\n      .zy-el-tree-node__content {\r\n        width: 100%;\r\n        height: var(--zy-height);\r\n\r\n        .zy-el-tree-node__label {\r\n          width: calc(100% - 24px);\r\n          padding-right: 12px;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n\r\n      &.is-current {\r\n        &>.zy-el-tree-node__content {\r\n          .zy-el-tree-node__label {\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatHistoryScroll {\r\n    padding: var(--zy-distance-five) 0;\r\n\r\n    .GlobalAiChatHistoryItem {\r\n      cursor: pointer;\r\n      padding: var(--zy-distance-five) var(--zy-distance-two);\r\n\r\n      .GlobalAiChatHistoryTime {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-secondary);\r\n      }\r\n\r\n      .GlobalAiChatHistoryTitle {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-primary);\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatHistoryLoadingText {\r\n      width: 100%;\r\n      height: var(--zy-height);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: var(--zy-el-text-color-secondary);\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAOWA,KAAK,EAAC;AAA2B;;EAP5CC,GAAA;EAWaD,KAAK,EAAC;;;EAXnBC,GAAA;EAYaD,KAAK,EAAC;;;;;;uBAXjBE,YAAA,CAcaC,qBAAA;IAdOC,OAAO,EAAEC,MAAA,CAAAC,IAAI;IADnC,oBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAC+BH,MAAA,CAAAC,IAAI,GAAAE,MAAA;IAAA;IAAEC,OAAO,EAAC,OAAO;IAACC,SAAS,EAAC,cAAc;IAAC,cAAY,EAAC,4BAA4B;IACnHC,UAAU,EAAC;;IACAC,SAAS,EAAAC,QAAA,CAClB;MAAA,OAAiF,CAAjFC,mBAAA,CAAiF;QAA5Ed,KAAK,EAAC;MAA6B,IAACc,mBAAA,CAAkC;QAA5BC,SAAoB,EAAZV,MAAA,CAAAW;MAAW,G;;IAJxEC,OAAA,EAAAJ,QAAA,CAMI;MAAA,OAQe,CARfK,YAAA,CAQeC,uBAAA;QARDC,GAAG,EAAC,WAAW;QAACpB,KAAK,EAAC,8BAA8B;QAAEqB,QAAM,EAAEhB,MAAA,CAAAiB;;QANhFL,OAAA,EAAAJ,QAAA,CAOM;UAAA,OAMM,CANNC,mBAAA,CAMM,OANNS,UAMM,GAHIlB,MAAA,CAAAmB,SAAS,CAACC,MAAM,I,cAFxBvB,YAAA,CAE4BwB,kBAAA;YAVpCzB,GAAA;YAQiBmB,GAAG,EAAC,SAAS;YAAC,UAAQ,EAAC,IAAI;YAAC,mBAAiB,EAAjB,EAAiB;YAAEO,IAAI,EAAEtB,MAAA,CAAAmB,SAAS;YACpEI,KAAK,EAAE;cAAAC,QAAA;cAAAC,KAAA;YAAA,CAA+C;YAAGC,WAAU,EAAE1B,MAAA,CAAA2B;+CAThFC,mBAAA,gBAW0D5B,MAAA,CAAA6B,OAAO,I,cAAzDC,mBAAA,CAAuE,OAAvEC,UAAuE,EAAZ,QAAM,KAXzEH,mBAAA,gBAY0D5B,MAAA,CAAAgC,MAAM,I,cAAxDF,mBAAA,CAAqE,OAArEG,UAAqE,EAAX,OAAK,KAZvEL,mBAAA,e;;QAAAM,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}