<template>
  <div class="batchExport">
    <div class="batchExportSearch">
      <div class="batchExportSearchItem">
        <el-select v-model="termYearId" placeholder="请选择届次" clearable>
          <el-option v-for="item in termYearData" :key="item.key" :label="item.name" :value="item.key" />
        </el-select>
      </div>
      <div class="batchExportSearchItem">
        <el-select v-model="proposalTypeVal" placeholder="提案分类" clearable>
          <el-option v-for="item in proposalTypeData" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </div>
      <div class="batchExportSearchItem">
        <el-select v-model="proposalStatusVal" placeholder="提案状态" clearable>
          <el-option v-for="item in proposalStatusData" :key="item.nodeId" :label="item.statusName"
            :value="item.nodeId" />
        </el-select>
      </div>
    </div>
    <div class="batchExportSearchItem" style="margin-top: 10px;">
      <suggest-simple-select-unit v-model="unitIds"></suggest-simple-select-unit>
    </div>
    <div style="margin-top: 10px;">
      <el-button @click="handleSearch()" type="primary">检索</el-button>
      <el-button @click="handleExportExcel()" type="primary">导出Excel</el-button>
    </div>
    <div class="batchExportText">检索到{{ proposalNum }}件提案数据，请在下方选择导出文件类型</div>
    <div class="batchExportSearchItems">
      <el-select v-model="fileType" placeholder="文件类型" clearable>
        <el-option v-for="item in fileTypeData" :key="item.key" :label="item.name" :value="item.key" />
      </el-select>
    </div>
    <div style="margin-top: 20px;" class="batchExportSearchBtn">
      <el-button @click="exportFile()" type="primary">导出文件</el-button>
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="提案文件" module="proposalUnitExportExcel" :params="exportParams"
        @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'batchExport' }
</script>
<script setup>
import api from '@/api'
import { onActivated, ref } from 'vue'
// import { suggestExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage } from 'element-plus'
import { exportWordHtmlList, batchDownloadFile } from "common/config/MicroGlobal";
import { filterTableData } from '@/assets/js/suggestExportWord'
const termYearId = ref('')
const termYearData = ref([])
const proposalTypeVal = ref('')
const proposalTypeData = ref([])
const proposalStatusVal = ref('')
const proposalStatusData = ref([])
const unitIds = ref('')
const proposalNum = ref(0)
const fileType = ref('')
const fileTypeData = ref([
  { name: '提案文件', key: '1' },
  { name: '答复件pdf版', key: 'A' },
  { name: '答复件Word版', key: 'C' },
  { name: '征询意见表', key: 'B' }
])
const exportShow = ref(false)
const exportParams = ref({})
onActivated(() => {
  termYearSelect()
  proposalThemeSelectData()
  getProposalStatus()
})

// 获取届次
const termYearSelect = async () => {
  const { data } = await api.termYearSelect({ termYearType: 'cppcc_member' })
  termYearData.value = data
}
// 获取提案分类
const proposalThemeSelectData = async () => {
  const { data } = await api.proposalThemeSelect()
  proposalTypeData.value = data
}
// 获取提案状态
const getProposalStatus = async () => {
  const { data } = await api.getProposalStatus()
  proposalStatusData.value = data
}
// 检索
const handleSearch = async () => {
  if (!termYearId.value) {
    return ElMessage({ type: 'warning', message: '请选择届次' })
  }
  const { data, code } = await api.getProposalExportCount({
    teamYearId: termYearId.value,
    type: proposalTypeVal.value,
    status: proposalStatusVal.value,
    unitIds: unitIds.value ? unitIds.value.join(',') : '',
  })
  if (code == 200) {
    ElMessage({ type: 'success', message: '查询成功' })
    proposalNum.value = data.count
  }
}
// 导出excle
const handleExportExcel = () => {
  if (!termYearId.value) {
    return ElMessage({ type: 'warning', message: '请选择届次' })
  }
  exportParams.value = {
    teamYearId: termYearId.value,
    type: proposalTypeVal.value,
    status: proposalStatusVal.value,
    unitIds: unitIds.value ? unitIds.value.join(',') : '',
  }
  exportShow.value = true
}
// 导出文件
const exportFile = async () => {
  console.log('导出文件')
  if (!termYearId.value) {
    return ElMessage({ type: 'warning', message: '请选择届次' })
  }
  if (!fileType.value) {
    return ElMessage({ type: 'warning', message: '请选择导出类型' })
  }
  const params = {
    teamYearId: termYearId.value,
    type: proposalTypeVal.value,
    status: proposalStatusVal.value,
    unitIds: unitIds.value ? unitIds.value.join(',') : '',
    fileSuffix: fileType.value
  }
  const { data } = await api.getProposalExport(params)
  console.log('data===>', data)
  var wordData = []
  if (fileType.value == '1') {
    data.forEach(v => {
      v.handleType = v.handleType === 'publish' ? '分办' : '主办/协办'
      v.assistHandleOffice = v.handleType === '分办' ? v.publishHandleOffice : v.assistHandleOffice
      v.circlesTypeName = v.circlesType?.name
      v.boutTypeName = v.boutType?.name
      v.partyName = v.party?.name
    })
    for (let index = 0; index < data.length; index++) {
      wordData.push(filterTableData(data[index]))
    }
    const create = { url: '/proposal/loadDocAnswerZip', params: params }
    exportWordHtmlList({ create: create, code: 'proposalDetails', name: '提案文件', key: 'content', wordNameKey: 'docName', data: wordData })
  } else if (fileType.value == 'A') {
    const attachmentsRedIds = data.flatMap(item => item.attachmentsRed.map(file => file.id));
    batchDownloadFile({ params: attachmentsRedIds, fileSize: 0, fileName: '答复件pdf.zip', fileType: 'zip' })
  } else if (fileType.value == 'C') {
    const attachmentsWordIds = data.flatMap(item => item.attachmentsWord.map(file => file.id));
    batchDownloadFile({ params: attachmentsWordIds, fileSize: 0, fileName: '答复件word.zip', fileType: 'zip' })
  } else if (fileType.value == 'B') {
    const attachmentsOpinionIds = data.flatMap(item => item.attachmentsOpinion.map(file => file.id));
    batchDownloadFile({ params: attachmentsOpinionIds, fileSize: 0, fileName: '征询意见表.zip', fileType: 'zip' })
  }
}
const callback = () => {
  exportShow.value = false
}
</script>
<style lang="scss">
.batchExport {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .batchExportSearch {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 20px;

    .batchExportSearchItem {
      width: 220px;
      margin-right: 10px;
    }
  }

  .batchExportText {
    margin: 20px 0;
  }

  .batchExportSearchItems {
    width: 220px;
    margin-right: 10px;
    margin-top: 20px;
  }

  .batchExportSearchBtn {
    margin-top: 20px;
  }

  .suggest-simple-select-unit {
    width: 60%;
    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;
    border-radius: var(--zy-el-input-border-radius, var(--zy-el-border-radius-base));
  }
}
</style>
