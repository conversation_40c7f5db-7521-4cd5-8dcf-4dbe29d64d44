<template>
  <el-container class="GlobalAiToolBox">
    <el-aside class="GlobalAiToolBoxNav">
      <div class="GlobalAiToolBoxHead">
        <div class="GlobalAiToolBoxLogo">
          <el-image :src="systemLogo" fit="cover" />
        </div>
        <div class="GlobalAiToolBoxName ellipsis">
          {{ systemNameAreaPrefix === 'true' ? regionName : '' }}{{ systemName }}
        </div>
      </div>
      <div class="GlobalAiToolBoxTabBody">
        <div class="GlobalAiToolBoxTab">
          <div class="GlobalAiToolBoxTabIcon"></div>
          <div class="GlobalAiToolBoxTabTitle" @click="handleClick">首页</div>
        </div>
      </div>
      <el-scrollbar
        class="GlobalAiToolBoxNav"
        :style="`background: url('${navBottom}') no-repeat;background-size: 100% auto;background-position: bottom center;`">
        <div class="GlobalAiToolBoxNavItem" v-for="item in navList" :key="item.id">
          <div class="GlobalAiToolBoxNavTitle">{{ item.title }}</div>
          <div class="GlobalAiToolBoxNavList">
            <div
              class="GlobalAiToolBoxNavToolItem"
              v-for="tool in item.tool"
              :key="tool.id"
              :class="{ 'is-active': toolId == tool.id }"
              @click="handleToolClick(tool)">
              <div class="GlobalAiToolBoxNavToolItemIcon">
                <el-icon><Menu /></el-icon>
              </div>
              <div class="GlobalAiToolBoxNavToolItemTitle">{{ tool.name }}</div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </el-aside>
    <el-main
      class="GlobalAiToolBoxMain"
      :style="`background: url('${mainTop}') no-repeat #f7f7f7;background-size: 100% auto;background-position: top right;`">
      <div class="GlobalAiToolBoxMainHead">
        <div class="GlobalAiToolBoxMainHeadLeft">
          <div class="GlobalAiToolBoxMainHeadItem">{{ toolName }}</div>
        </div>
        <div class="GlobalAiToolBoxMainHeadRight">
          <div class="GlobalAiToolBoxUser">
            <el-image :src="user.image" fit="cover" />
            <span class="forbidSelect">{{ user.userName }}</span>
          </div>
          <div class="GlobalAiToolBoxExit" v-html="exitIcon" @click="handleExit"></div>
        </div>
      </div>
      <div class="AiToolBoxBody">
        <keep-alive>
          <component :is="AiToolBoxElement[toolId]" />
        </keep-alive>
      </div>
    </el-main>
  </el-container>
</template>

<script setup>
import api from '@/api'
import config from 'common/config'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { globalReadOpenConfig } from 'common/js/GlobalMethod.js'
import { user, systemLogo, systemName, systemNameAreaPrefix } from 'common/js/system_var.js'
import { AiToolBoxElement } from './AiToolBox.js'
import { ElMessageBox, ElMessage } from 'element-plus'
const router = useRouter()
const store = useStore()
const mainTop = computed(() => `${config.API_URL}/pageImg/open/GlobalAiToolBoxMainTop?areaId=${user.value.areaId}`)
const navBottom = computed(() => `${config.API_URL}/pageImg/open/GlobalAiToolBoxNavBottom?areaId=${user.value.areaId}`)
const exitIcon = `<svg t="1739268585892" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2352" width="24" height="24"><path d="M0 192v640c0 70.7 57.3 128 128 128h352c17.7 0 32-14.3 32-32s-14.3-32-32-32H128c-35.3 0-64-28.7-64-64V192c0-35.3 28.7-64 64-64h352c17.7 0 32-14.3 32-32s-14.3-32-32-32H128C57.3 64 0 121.3 0 192z" p-id="2353" fill="#666666"></path><path d="M1013.3 488.3L650.9 160.7c-41.2-37.2-106.9-8-106.9 47.5V339c0 4.4-3.6 8-8 8H224c-17.7 0-32 14.3-32 32v266c0 17.7 14.3 32 32 32h312c4.4 0 8 3.6 8 8v130.9c0 55.5 65.8 84.7 106.9 47.5l362.4-327.6c14.1-12.8 14.1-34.8 0-47.5zM256 597V427c0-8.8 7.2-16 16-16h304c17.7 0 32-14.3 32-32V244.9c0-13.9 16.4-21.2 26.7-11.9L938 506.1c3.5 3.2 3.5 8.7 0 11.9L634.7 791c-10.3 9.3-26.7 2-26.7-11.9V645c0-17.7-14.3-32-32-32H272c-8.8 0-16-7.2-16-16z" p-id="2354" fill="#666666"></path></svg>`
const regionName = ref('')
const toolId = ref('IntelligentErrorCorrection')
const toolName = ref('智能纠错')
const navList = ref([
  {
    id: '1',
    title: '工作通用工具',
    tool: [
      { id: 'IntelligentErrorCorrection', name: '智能纠错' },
      { id: 'OneClickLayout', name: '一件排版' },
      { id: 'ContentExtraction', name: '内容提炼' },
      { id: 'IntelligentManuscriptMerging', name: '智能合稿' },
      { id: 'TextComparison', name: '文本比对' },
      { id: 'TextPolishing', name: '文本润色' },
      { id: 'TextExpansion', name: '文本扩写' },
      { id: 'TextContinuation', name: '文本续写' },
      { id: 'TextRewrite', name: '文本重写' },
      { id: 'TextRecognition', name: '文本识别' }
    ]
  },
  {
    id: '2',
    title: '业务专用工具',
    tool: [{ id: 'ProposalAuxiliaryWriting', name: '提案辅助撰写' }]
  }
])
const handleToolClick = (tool) => {
  toolId.value = tool.id
  toolName.value = tool.name
}
const handleClick = () => {
  router.push('/')
}
const handleExit = () => {
  ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      loginOut('已安全退出！')
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消退出' })
    })
}
const loginOut = async (text) => {
  const { code } = await api.loginOut()
  if (code === 200) {
    sessionStorage.clear()
    const goal_login_router_path = localStorage.getItem('goal_login_router_path')
    if (goal_login_router_path) {
      const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''
      router.push({
        path: goal_login_router_path,
        query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}
      })
    } else {
      router.push({ path: '/LoginView' })
    }
    store.commit('setState')
    globalReadOpenConfig()
    // store.state.socket.disconnect()
    // store.state.socket = null
    ElMessage({ message: text, showClose: true, type: 'success' })
  }
}
</script>
<style lang="scss">
.GlobalAiToolBox {
  width: 100%;
  height: 100%;
  .GlobalAiToolBoxNav {
    width: calc((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four));
    height: 100%;
    background: var(--zy-el-color-primary);

    .GlobalAiToolBoxHead {
      height: 78px;
      display: flex;
      align-items: center;
      padding: 0 var(--zy-distance-two);

      .GlobalAiToolBoxLogo {
        width: 50px;

        .zy-el-image {
          width: 100%;
          display: block;
        }
      }

      .GlobalAiToolBoxName {
        width: calc(100% - 40px);
        color: #fff;
        font-weight: bold;
        font-size: 22px;
        padding-left: 12px;
      }
    }
    .GlobalAiToolBoxTabBody {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: var(--zy-distance-one);
      .GlobalAiToolBoxTab {
        width: 182px;
        height: 36px;
        display: flex;
        align-items: center;
        padding-right: var(--zy-font-name-distance-five);
        position: relative;
        &::before {
          content: '';
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 18px;
          box-sizing: border-box;
          border: 1px solid #ffffff;
          z-index: 1;
        }
        .GlobalAiToolBoxTabIcon {
          width: 110px;
          height: 36px;
          border-radius: 18px;
          background: url('../img/global_ai_tool_box_icon.png') no-repeat #e0e8ff;
          background-size: 72px auto;
          background-position: center center;
          position: relative;
          z-index: 2;
        }
        .GlobalAiToolBoxTabTitle {
          width: calc(100% - 110px);
          cursor: pointer;
          color: #fff;
          text-align: center;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
          position: relative;
          z-index: 2;
        }
      }
    }
    .GlobalAiToolBoxNav {
      width: 100%;
      height: calc(100% - (78px + 36px + var(--zy-distance-one)));
      .GlobalAiToolBoxNavItem {
        width: 100%;
        padding: 0 var(--zy-distance-two);
        .GlobalAiToolBoxNavTitle {
          width: 100%;
          color: #fff;
          font-weight: bold;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
        }
        .GlobalAiToolBoxNavList {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: space-between;
          padding: var(--zy-distance-four) 0;
          .GlobalAiToolBoxNavToolItem {
            width: 112px;
            height: 112px;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            border: 1px solid transparent;
            background: rgba(255, 255, 255, 0.3);
            border-radius: var(--el-border-radius-base);
            border-image: linear-gradient(131deg, rgba(255, 255, 255, 0.2), rgba(224, 232, 255, 0.1)) 1 1;
            margin-bottom: var(--zy-distance-four);
            cursor: pointer;
            &:hover {
              background: rgba(255, 255, 255, 0.9);
              border: 1px solid rgba(255, 255, 255, 1);
              .GlobalAiToolBoxNavToolItemTitle {
                color: var(--zy-el-text-color-primary);
              }
            }
            &.is-active {
              background: rgba(255, 255, 255, 1);
              border: 1px solid rgba(255, 255, 255, 1);
              .GlobalAiToolBoxNavToolItemIcon {
                background: var(--zy-el-color-primary);
              }
              .GlobalAiToolBoxNavToolItemTitle {
                color: var(--zy-el-text-color-primary);
              }
            }
            .GlobalAiToolBoxNavToolItemIcon {
              width: 52px;
              height: 52px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: var(--zy-el-color-primary-light-3);
              border-radius: var(--el-border-radius-base);
              margin: var(--zy-font-text-distance-five) 0;
              .zy-el-icon {
                font-size: 38px;
                color: #fff;
              }
            }
            .GlobalAiToolBoxNavToolItemTitle {
              width: 100%;
              color: #fff;
              text-align: center;
              font-size: var(--zy-text-font-size);
              line-height: var(--zy-line-height);
              padding-top: var(--zy-font-text-distance-five);
            }
          }
        }
      }
    }
  }
  .GlobalAiToolBoxMain {
    width: calc(100% - ((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four)));
    height: 100%;
    padding: 0;
    .GlobalAiToolBoxMainHead {
      width: 100%;
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 var(--zy-distance-two);
      padding-top: 10px;

      .GlobalAiToolBoxUser {
        display: flex;
        align-items: center;
        padding-right: var(--zy-distance-two);
        cursor: pointer;

        .zy-el-image {
          height: 38px;
          width: 38px;
          border-radius: 50%;
          overflow: hidden;
        }

        span {
          margin-left: 8px;
          font-size: var(--zy-name-font-size);
          line-height: var(--zy-line-height);
        }
      }
      .GlobalAiToolBoxMainHeadLeft {
        display: flex;
        align-items: center;
        .GlobalAiToolBoxMainHeadItem {
          width: 100%;
          font-weight: bold;
          font-size: var(--zy-name-font-size);
          padding: var(--zy-font-name-distance-five);
          border-bottom: 3px solid var(--zy-el-color-primary);
        }
      }
      .GlobalAiToolBoxMainHeadRight {
        display: flex;
        align-items: center;
        .GlobalAiToolBoxExit {
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }
    }
    .AiToolBoxBody {
      width: 100%;
      height: calc(100% - 68px);
    }
  }
}
</style>
