{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted, computed, defineAsyncComponent } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { systemLogo, systemName, loginNameLineFeedPosition, appDownloadUrl, systemLoginContact } from 'common/js/system_var.js';\nimport { LoginView } from '../LoginView/LoginView.js';\nvar __default__ = {\n  name: 'LoginViewRegion'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var QrcodeVue = defineAsyncComponent(function () {\n      return import('qrcode.vue');\n    });\n    var ResetPassword = defineAsyncComponent(function () {\n      return import('../LoginView/component/ResetPassword.vue');\n    });\n    var route = useRoute();\n    var show = ref(false);\n    var imgList = ref([]);\n    var localAreaName = ref('');\n    var localSystemName = ref('');\n    var localLoginNameLineFeedPosition = ref('');\n    var loginSystemName = computed(function () {\n      var name = (localAreaName.value || '') + (localSystemName.value || systemName.value);\n      var num = Number(localLoginNameLineFeedPosition.value || loginNameLineFeedPosition.value || '0') || 0;\n      return num ? name.substring(0, num) + '\\n' + name.substring(num) : name;\n    });\n    var globalData = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$loginImgRe, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.loginImgRegion({}, route.query.areaId);\n            case 2:\n              _yield$api$loginImgRe = _context.sent;\n              data = _yield$api$loginImgRe.data;\n              data.forEach(function (item) {\n                item.imgPath = api.fileURL(item.imgPath);\n              });\n              imgList.value = data;\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function globalData() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var globalReadConfig = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _yield$api$readOpenCo, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.readOpenConfigRegion({\n                codes: ['systemName', 'localAreaName', 'loginNameLineFeedPosition']\n              }, route.query.areaId);\n            case 2:\n              _yield$api$readOpenCo = _context2.sent;\n              data = _yield$api$readOpenCo.data;\n              localSystemName.value = (data === null || data === void 0 ? void 0 : data.systemName) || '';\n              localAreaName.value = (data === null || data === void 0 ? void 0 : data.localAreaName) || '';\n              localLoginNameLineFeedPosition.value = (data === null || data === void 0 ? void 0 : data.loginNameLineFeedPosition) || '';\n            case 7:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function globalReadConfig() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var _LoginView = LoginView('/LoginViewRegion', {\n        areaId: route.query.areaId\n      }),\n      loginVerifyShow = _LoginView.loginVerifyShow,\n      whetherVerifyCode = _LoginView.whetherVerifyCode,\n      loginDisabled = _LoginView.loginDisabled,\n      loading = _LoginView.loading,\n      checked = _LoginView.checked,\n      LoginForm = _LoginView.LoginForm,\n      form = _LoginView.form,\n      rules = _LoginView.rules,\n      countDownText = _LoginView.countDownText,\n      slideVerify = _LoginView.slideVerify,\n      disabled = _LoginView.disabled,\n      loginQrcode = _LoginView.loginQrcode,\n      loginQrcodeShow = _LoginView.loginQrcodeShow,\n      handleBlur = _LoginView.handleBlur,\n      handleGetVerifyCode = _LoginView.handleGetVerifyCode,\n      onAgain = _LoginView.onAgain,\n      onSuccess = _LoginView.onSuccess,\n      submitForm = _LoginView.submitForm,\n      loginInfo = _LoginView.loginInfo,\n      refresh = _LoginView.refresh,\n      hideQrcode = _LoginView.hideQrcode;\n    onMounted(function () {\n      loginInfo();\n      globalData();\n      globalReadConfig();\n    });\n    var __returned__ = {\n      QrcodeVue,\n      ResetPassword,\n      route,\n      show,\n      imgList,\n      localAreaName,\n      localSystemName,\n      localLoginNameLineFeedPosition,\n      loginSystemName,\n      globalData,\n      globalReadConfig,\n      loginVerifyShow,\n      whetherVerifyCode,\n      loginDisabled,\n      loading,\n      checked,\n      LoginForm,\n      form,\n      rules,\n      countDownText,\n      slideVerify,\n      disabled,\n      loginQrcode,\n      loginQrcodeShow,\n      handleBlur,\n      handleGetVerifyCode,\n      onAgain,\n      onSuccess,\n      submitForm,\n      loginInfo,\n      refresh,\n      hideQrcode,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      computed,\n      defineAsyncComponent,\n      get useRoute() {\n        return useRoute;\n      },\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get loginNameLineFeedPosition() {\n        return loginNameLineFeedPosition;\n      },\n      get appDownloadUrl() {\n        return appDownloadUrl;\n      },\n      get systemLoginContact() {\n        return systemLoginContact;\n      },\n      get LoginView() {\n        return LoginView;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "computed", "defineAsyncComponent", "useRoute", "systemLogo", "systemName", "loginNameLineFeedPosition", "appDownloadUrl", "systemLoginContact", "<PERSON><PERSON><PERSON>ie<PERSON>", "__default__", "QrcodeVue", "ResetPassword", "route", "show", "imgList", "localAreaName", "localSystemName", "localLoginNameLineFeedPosition", "loginSystemName", "num", "Number", "substring", "globalData", "_ref2", "_callee", "_yield$api$loginImgRe", "data", "_callee$", "_context", "loginImgRegion", "query", "areaId", "item", "imgPath", "fileURL", "globalReadConfig", "_ref3", "_callee2", "_yield$api$readOpenCo", "_callee2$", "_context2", "readOpenConfigRegion", "codes", "_<PERSON><PERSON><PERSON>ie<PERSON>", "loginVerifyShow", "whetherVerifyCode", "loginDisabled", "loading", "checked", "LoginForm", "form", "rules", "countDownText", "slideVerify", "disabled", "loginQrcode", "loginQrcodeShow", "handleBlur", "handleGetVerifyCode", "onAgain", "onSuccess", "submitForm", "loginInfo", "refresh", "hideQrcode"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LoginViewRegion/LoginViewRegion.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LoginViewRegion\">\r\n    <el-carousel class=\"LoginViewRegionCarousel\" v-if=\"imgList.length\" height=\"100%\">\r\n      <el-carousel-item class=\"LoginViewRegionCarouselBox\" v-for=\"item in imgList\" :key=\"item.id\">\r\n        <el-image :src=\"item.imgPath\" loading=\"lazy\" fit=\"cover\" />\r\n      </el-carousel-item>\r\n    </el-carousel>\r\n    <div class=\"LoginViewRegionBox\">\r\n      <div class=\"LoginViewRegionLogo\">\r\n        <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n      </div>\r\n      <div class=\"LoginViewRegionName\" v-html=\"loginSystemName\"></div>\r\n      <el-form ref=\"LoginForm\" :model=\"form\" :rules=\"rules\" class=\"LoginViewRegionForm\">\r\n        <el-form-item prop=\"account\">\r\n          <el-input v-model=\"form.account\" placeholder=\"账号/手机号\" @blur=\"handleBlur\" clearable />\r\n        </el-form-item>\r\n        <el-form-item prop=\"password\">\r\n          <el-input type=\"password\" v-model=\"form.password\" placeholder=\"密码\" show-password clearable />\r\n        </el-form-item>\r\n        <el-form-item class=\"smsValidation\" v-if=\"loginVerifyShow && whetherVerifyCode\" prop=\"verifyCode\">\r\n          <el-input v-model=\"form.verifyCode\" placeholder=\"短信验证码\" clearable></el-input>\r\n          <el-button type=\"primary\" @click=\"handleGetVerifyCode\" :disabled=\"countDownText != '获取验证码'\">\r\n            {{ countDownText }}\r\n          </el-button>\r\n        </el-form-item>\r\n        <div class=\"LoginViewRegionSlideVerify\" v-if=\"loginVerifyShow && !whetherVerifyCode\">\r\n          <xyl-slide-verify ref=\"slideVerify\" @again=\"onAgain\" @success=\"onSuccess\" :disabled=\"disabled\" />\r\n        </div>\r\n        <div class=\"LoginViewRegionFormOperation\">\r\n          <el-checkbox v-model=\"checked\">记住用户名和密码</el-checkbox>\r\n          <div class=\"LoginViewRegionFormOperationText\" @click=\"show = !show\">忘记密码？</div>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"submitForm(LoginForm)\" class=\"LoginViewRegionFormButton\" :loading=\"loading\"\r\n          :disabled=\"loginDisabled\">\r\n          {{ loading ? '登录中' : '登录' }}\r\n        </el-button>\r\n      </el-form>\r\n      <div class=\"LoginViewRegionOperation\" v-if=\"appDownloadUrl\">\r\n        <div class=\"LoginViewRegionOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\" @show=\"refresh\" @hide=\"hideQrcode\">\r\n            <div class=\"LoginViewRegionQrCodeBox\">\r\n              <div class=\"LoginViewRegionQrCodeNameBody\">\r\n                <div class=\"LoginViewRegionQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewRegionQrCodeName\">APP扫码登录</div>\r\n              </div>\r\n              <div class=\"LoginViewRegionQrCodeRefreshBody\">\r\n                <qrcode-vue :value=\"loginQrcode\" :size=\"120\" />\r\n                <div class=\"LoginViewRegionQrCodeRefresh\" v-show=\"loginQrcodeShow\">\r\n                  <el-button type=\"primary\" @click=\"refresh\">刷新</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"LoginViewRegionQrCodeText\">请使用{{ systemName }}APP扫码登录</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewRegionQrCode\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewRegionOperationText\">APP扫码登录</div>\r\n        </div>\r\n        <div class=\"LoginViewRegionOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\">\r\n            <div class=\"LoginViewRegionQrCodeBox\">\r\n              <div class=\"LoginViewRegionQrCodeNameBody\">\r\n                <div class=\"LoginViewRegionQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewRegionQrCodeName\">手机APP下载</div>\r\n              </div>\r\n              <qrcode-vue :value=\"appDownloadUrl\" :size=\"120\" />\r\n              <div class=\"LoginViewRegionQrCodeText\">使用其他软件扫码下载{{ systemName }}APP</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewRegionApp\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewRegionOperationText\">手机APP下载</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"LoginViewRegionSystemTips\" v-if=\"systemLoginContact\">{{ systemLoginContact }}</div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"重置密码\">\r\n      <ResetPassword @callback=\"show = !show\"></ResetPassword>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LoginViewRegion' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, computed, defineAsyncComponent } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport {\r\n  systemLogo,\r\n  systemName,\r\n  loginNameLineFeedPosition,\r\n  appDownloadUrl,\r\n  systemLoginContact\r\n} from 'common/js/system_var.js'\r\nimport { LoginView } from '../LoginView/LoginView.js'\r\nconst QrcodeVue = defineAsyncComponent(() => import('qrcode.vue'))\r\nconst ResetPassword = defineAsyncComponent(() => import('../LoginView/component/ResetPassword.vue'))\r\nconst route = useRoute()\r\nconst show = ref(false)\r\nconst imgList = ref([])\r\nconst localAreaName = ref('')\r\nconst localSystemName = ref('')\r\nconst localLoginNameLineFeedPosition = ref('')\r\nconst loginSystemName = computed(() => {\r\n  const name = (localAreaName.value || '') + (localSystemName.value || systemName.value)\r\n  const num = Number(localLoginNameLineFeedPosition.value || loginNameLineFeedPosition.value || '0') || 0\r\n  return num ? name.substring(0, num) + '\\n' + name.substring(num) : name\r\n})\r\nconst globalData = async () => {\r\n  const { data } = await api.loginImgRegion({}, route.query.areaId)\r\n  data.forEach((item) => {\r\n    item.imgPath = api.fileURL(item.imgPath)\r\n  })\r\n  imgList.value = data\r\n}\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.readOpenConfigRegion(\r\n    { codes: ['systemName', 'localAreaName', 'loginNameLineFeedPosition'] },\r\n    route.query.areaId\r\n  )\r\n  localSystemName.value = data?.systemName || ''\r\n  localAreaName.value = data?.localAreaName || ''\r\n  localLoginNameLineFeedPosition.value = data?.loginNameLineFeedPosition || ''\r\n}\r\nconst {\r\n  loginVerifyShow,\r\n  whetherVerifyCode,\r\n  loginDisabled,\r\n  loading,\r\n  checked,\r\n  LoginForm,\r\n  form,\r\n  rules,\r\n  countDownText,\r\n  slideVerify,\r\n  disabled,\r\n  loginQrcode,\r\n  loginQrcodeShow,\r\n  handleBlur,\r\n  handleGetVerifyCode,\r\n  onAgain,\r\n  onSuccess,\r\n  submitForm,\r\n  loginInfo,\r\n  refresh,\r\n  hideQrcode\r\n} = LoginView('/LoginViewRegion', { areaId: route.query.areaId })\r\nonMounted(() => {\r\n  loginInfo()\r\n  globalData()\r\n  globalReadConfig()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.LoginViewRegion {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n\r\n  .LoginViewRegionCarousel {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n\r\n    .LoginViewRegionCarouselBox {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .zy-el-carousel__indicators--horizontal {\r\n      .zy-el-carousel__button {\r\n        width: 16px;\r\n        height: 16px;\r\n        border-radius: 50%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewRegionBox {\r\n    padding: var(--zy-distance-one);\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    padding-bottom: var(--zy-distance-two);\r\n    border-radius: var(--el-border-radius-base);\r\n    margin-right: 80px;\r\n    background: #fff;\r\n    position: relative;\r\n    z-index: 2;\r\n\r\n    .LoginViewRegionLogo {\r\n      width: 60px;\r\n      margin: auto;\r\n      margin-bottom: var(--zy-distance-two);\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .LoginViewRegionName {\r\n      width: 320px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-align: center;\r\n      font-size: var(--zy-system-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      letter-spacing: 2px;\r\n      padding-bottom: var(--zy-distance-one);\r\n      white-space: pre-wrap;\r\n      margin: auto;\r\n    }\r\n\r\n    .LoginViewRegionForm {\r\n      width: 320px;\r\n      margin: auto;\r\n      padding-bottom: var(--zy-distance-one);\r\n\r\n      input:-webkit-autofill {\r\n        transition: background-color 5000s ease-in-out 0s;\r\n      }\r\n\r\n      .zy-el-form-item {\r\n        margin-bottom: var(--zy-form-distance-bottom);\r\n      }\r\n\r\n      .LoginViewRegionFormButton {\r\n        width: 100%;\r\n      }\r\n\r\n      .smsValidation {\r\n        .zy-el-form-item__content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n        }\r\n\r\n        .zy-el-input {\r\n          width: 56%;\r\n        }\r\n      }\r\n\r\n      .LoginViewRegionSlideVerify {\r\n        margin-bottom: var(--zy-distance-five);\r\n      }\r\n\r\n      .LoginViewRegionFormOperation {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: var(--zy-distance-three);\r\n\r\n        .zy-el-checkbox {\r\n          height: var(--zy-height-secondary);\r\n        }\r\n\r\n        .LoginViewRegionFormOperationText {\r\n          cursor: pointer;\r\n          color: var(--zy-el-color-primary);\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewRegionOperation {\r\n      width: 100%;\r\n      padding-bottom: var(--zy-distance-two);\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .LoginViewRegionOperationBox {\r\n        margin: 0 var(--zy-distance-two);\r\n        cursor: pointer;\r\n\r\n        .LoginViewRegionQrCode {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url('../img/login_qr_code.png');\r\n          background-size: 100% 100%;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewRegionApp {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url('../img/login_app.png') no-repeat;\r\n          background-size: auto 100%;\r\n          background-position: center;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewRegionOperationText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: var(--el-border-radius-small) 0;\r\n          text-align: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewRegionForm+.LoginViewRegionSystemTips {\r\n      padding-top: var(--zy-distance-one);\r\n    }\r\n\r\n    .LoginViewRegionSystemTips {\r\n      color: var(--zy-el-text-color-secondary);\r\n      font-size: var(--zy-text-font-size);\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.LoginViewRegionQrCodeBox {\r\n  width: 320px;\r\n  background-color: #fff;\r\n\r\n  canvas {\r\n    display: block;\r\n    margin: auto;\r\n  }\r\n\r\n  .LoginViewRegionQrCodeNameBody {\r\n    padding: var(--zy-distance-three);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LoginViewRegionQrCodeLogo {\r\n      width: 26px;\r\n      margin-right: 6px;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .LoginViewRegionQrCodeName {\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .LoginViewRegionQrCodeRefreshBody {\r\n    position: relative;\r\n\r\n    .LoginViewRegionQrCodeRefresh {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 120px;\r\n      height: 120px;\r\n      background-color: rgba(000, 000, 000, 0.6);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .zy-el-button {\r\n        --zy-el-button-size: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewRegionQrCodeText {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding: var(--zy-distance-three);\r\n    color: var(--zy-el-color-primary);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA4FA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,KAAK;AACpE,SAASC,QAAQ,QAAQ,YAAY;AACrC,SACEC,UAAU,EACVC,UAAU,EACVC,yBAAyB,EACzBC,cAAc,EACdC,kBAAkB,QACb,yBAAyB;AAChC,SAASC,SAAS,QAAQ,2BAA2B;AAbrD,IAAAC,WAAA,GAAe;EAAEvC,IAAI,EAAE;AAAkB,CAAC;;;;;IAc1C,IAAMwC,SAAS,GAAGT,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,YAAY,CAAC;IAAA,EAAC;IAClE,IAAMU,aAAa,GAAGV,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,0CAA0C,CAAC;IAAA,EAAC;IACpG,IAAMW,KAAK,GAAGV,QAAQ,CAAC,CAAC;IACxB,IAAMW,IAAI,GAAGf,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMgB,OAAO,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMiB,aAAa,GAAGjB,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMkB,eAAe,GAAGlB,GAAG,CAAC,EAAE,CAAC;IAC/B,IAAMmB,8BAA8B,GAAGnB,GAAG,CAAC,EAAE,CAAC;IAC9C,IAAMoB,eAAe,GAAGlB,QAAQ,CAAC,YAAM;MACrC,IAAM9B,IAAI,GAAG,CAAC6C,aAAa,CAACtH,KAAK,IAAI,EAAE,KAAKuH,eAAe,CAACvH,KAAK,IAAI2G,UAAU,CAAC3G,KAAK,CAAC;MACtF,IAAM0H,GAAG,GAAGC,MAAM,CAACH,8BAA8B,CAACxH,KAAK,IAAI4G,yBAAyB,CAAC5G,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC;MACvG,OAAO0H,GAAG,GAAGjD,IAAI,CAACmD,SAAS,CAAC,CAAC,EAAEF,GAAG,CAAC,GAAG,IAAI,GAAGjD,IAAI,CAACmD,SAAS,CAACF,GAAG,CAAC,GAAGjD,IAAI;IACzE,CAAC,CAAC;IACF,IAAMoD,UAAU;MAAA,IAAAC,KAAA,GAAA/B,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqD,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAA3I,mBAAA,GAAAuB,IAAA,UAAAqH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAhD,IAAA,GAAAgD,QAAA,CAAA3E,IAAA;YAAA;cAAA2E,QAAA,CAAA3E,IAAA;cAAA,OACM4C,GAAG,CAACgC,cAAc,CAAC,CAAC,CAAC,EAAEjB,KAAK,CAACkB,KAAK,CAACC,MAAM,CAAC;YAAA;cAAAN,qBAAA,GAAAG,QAAA,CAAAlF,IAAA;cAAzDgF,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZA,IAAI,CAAC7F,OAAO,CAAC,UAACmG,IAAI,EAAK;gBACrBA,IAAI,CAACC,OAAO,GAAGpC,GAAG,CAACqC,OAAO,CAACF,IAAI,CAACC,OAAO,CAAC;cAC1C,CAAC,CAAC;cACFnB,OAAO,CAACrH,KAAK,GAAGiI,IAAI;YAAA;YAAA;cAAA,OAAAE,QAAA,CAAA7C,IAAA;UAAA;QAAA,GAAAyC,OAAA;MAAA,CACrB;MAAA,gBANKF,UAAUA,CAAA;QAAA,OAAAC,KAAA,CAAA7B,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMf;IACD,IAAM0C,gBAAgB;MAAA,IAAAC,KAAA,GAAA5C,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkE,SAAA;QAAA,IAAAC,qBAAA,EAAAZ,IAAA;QAAA,OAAA3I,mBAAA,GAAAuB,IAAA,UAAAiI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAAvF,IAAA;YAAA;cAAAuF,SAAA,CAAAvF,IAAA;cAAA,OACA4C,GAAG,CAAC4C,oBAAoB,CAC7C;gBAAEC,KAAK,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,2BAA2B;cAAE,CAAC,EACvE9B,KAAK,CAACkB,KAAK,CAACC,MACd,CAAC;YAAA;cAAAO,qBAAA,GAAAE,SAAA,CAAA9F,IAAA;cAHOgF,IAAI,GAAAY,qBAAA,CAAJZ,IAAI;cAIZV,eAAe,CAACvH,KAAK,GAAG,CAAAiI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEtB,UAAU,KAAI,EAAE;cAC9CW,aAAa,CAACtH,KAAK,GAAG,CAAAiI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEX,aAAa,KAAI,EAAE;cAC/CE,8BAA8B,CAACxH,KAAK,GAAG,CAAAiI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAErB,yBAAyB,KAAI,EAAE;YAAA;YAAA;cAAA,OAAAmC,SAAA,CAAAzD,IAAA;UAAA;QAAA,GAAAsD,QAAA;MAAA,CAC7E;MAAA,gBARKF,gBAAgBA,CAAA;QAAA,OAAAC,KAAA,CAAA1C,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQrB;IACD,IAAAkD,UAAA,GAsBInC,SAAS,CAAC,kBAAkB,EAAE;QAAEuB,MAAM,EAAEnB,KAAK,CAACkB,KAAK,CAACC;MAAO,CAAC,CAAC;MArB/Da,eAAe,GAAAD,UAAA,CAAfC,eAAe;MACfC,iBAAiB,GAAAF,UAAA,CAAjBE,iBAAiB;MACjBC,aAAa,GAAAH,UAAA,CAAbG,aAAa;MACbC,OAAO,GAAAJ,UAAA,CAAPI,OAAO;MACPC,OAAO,GAAAL,UAAA,CAAPK,OAAO;MACPC,SAAS,GAAAN,UAAA,CAATM,SAAS;MACTC,IAAI,GAAAP,UAAA,CAAJO,IAAI;MACJC,KAAK,GAAAR,UAAA,CAALQ,KAAK;MACLC,aAAa,GAAAT,UAAA,CAAbS,aAAa;MACbC,WAAW,GAAAV,UAAA,CAAXU,WAAW;MACXC,QAAQ,GAAAX,UAAA,CAARW,QAAQ;MACRC,WAAW,GAAAZ,UAAA,CAAXY,WAAW;MACXC,eAAe,GAAAb,UAAA,CAAfa,eAAe;MACfC,UAAU,GAAAd,UAAA,CAAVc,UAAU;MACVC,mBAAmB,GAAAf,UAAA,CAAnBe,mBAAmB;MACnBC,OAAO,GAAAhB,UAAA,CAAPgB,OAAO;MACPC,SAAS,GAAAjB,UAAA,CAATiB,SAAS;MACTC,UAAU,GAAAlB,UAAA,CAAVkB,UAAU;MACVC,SAAS,GAAAnB,UAAA,CAATmB,SAAS;MACTC,OAAO,GAAApB,UAAA,CAAPoB,OAAO;MACPC,UAAU,GAAArB,UAAA,CAAVqB,UAAU;IAEZjE,SAAS,CAAC,YAAM;MACd+D,SAAS,CAAC,CAAC;MACXxC,UAAU,CAAC,CAAC;MACZa,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}