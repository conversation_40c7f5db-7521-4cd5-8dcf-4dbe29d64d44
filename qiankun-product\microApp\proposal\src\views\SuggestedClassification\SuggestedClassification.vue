<!--
 * @Description: 提案分类
 -->
<template>
  <el-scrollbar always class="SuggestedClassification">
    <div class="SubmitSuggestBody">
      <div class="leftBox">
        <div class="titleClas">
          <el-icon>
            <DArrowLeft />
          </el-icon>
          <div class="titleMidC">
            <img class="iconCla" src="../../assets/img/column.png" alt="">
            未分类提案
          </div>
          <el-icon>
            <DArrowRight />
          </el-icon>
        </div>
        <div>
          <div class="tipsClas">
            <el-checkbox style="display: none;" v-model="checkAll" :indeterminate="isIndeterminate"
              @change="handleCheckAllChange">checkAll</el-checkbox>
            提示：请先确定右侧提案类别再点击“分类”
          </div>
          <div class="contentCla">
            <el-scrollbar always class="scrollbarClas" v-loading="loading" :lement-loading-text="loadingText">
              <el-checkbox-group v-model="checkListLeft" @change="handleCheckedCitiesChange" class="checkBoxClas">
                <el-checkbox v-for="item in leftArr" :key="item.id" :label="item.id">
                  <!-- <el-tooltip effect="dark"
                              :show-after="500"
                              :content="item.title"
                              placement="top-start"> -->
                  <div @click.prevent="chekDetail(item)" class="titleTips ellipsis" :title="item.title"><span
                      v-if="item.streamNumber">[{{ item.streamNumber }}]</span>
                    {{ item.title }}
                  </div>
                  <!-- </el-tooltip> -->
                </el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>
            <el-pagination class="paginationCla" v-model:currentPage="SugData.pageNo"
              v-model:page-size="SugData.pageSize" :page-sizes="SugData.pageSizes"
              layout="sizes, prev, pager, next, total" @size-change="ChangeSize" @current-change="ChangePageNo"
              :pager-count="5" :total="SugData.total" small />
          </div>
        </div>
      </div>
      <div class="middleBox">
        <div class="midTop">
          <img class="iconCla2" src="../../assets/img/swop.png" alt="">
        </div>
        <div class="midButtom">
          <el-button style="margin: 0px 0px 10px 0px;" :disabled="toCategory" class="btn" type="primary" :icon="Right"
            @click="Category()">分类</el-button>
          <el-button style="margin: 20px 0px 0px 0px;" type="primary" class="btn" :disabled="toBack" @click="sendBack()"
            :icon="Back">退回</el-button>
        </div>
      </div>
      <div class="rightBox">
        <div class="titleClasRight">
          <el-icon>
            <DArrowLeft />
          </el-icon>
          <div class="titleMidC">
            <img class="iconCla" src="../../assets/img/column.png" alt="">
            已分类提案
          </div>
          <el-icon>
            <DArrowRight />
          </el-icon>
        </div>
        <div>
          <div class="tipsClasRight">
            <el-checkbox style="display: none;" v-model="checkAllRight" :indeterminate="isIndeterminateRight"
              @change="handleCheckAllChangeRight">checkAll</el-checkbox>
            <div class="sugclass" style="padding-bottom: 5px;">
              <div class="leftCTip requiredStar">提案大类：</div>
              <el-select v-model="form.SuggestBigType" placeholder="请选择提案大类" @change="SuggestBigTypeChange" clearable>
                <el-option v-for="item in BigTypeArr" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </div>
            <div class="sugclass" style="padding-top: 5px;">
              <div class="leftCTip" style="margin-left:10px ;">提案小类：</div>
              <el-select v-model="form.SuggestSmallType" placeholder="请选择提案小类" clearable>
                <el-option v-for="item in SmallTypeArr" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </div>
          </div>
          <div class="contentClaRight">
            <el-scrollbar always class="scrollbarClasRight" v-loading="loadingRight">
              <el-checkbox-group v-model="checkListRight" @change="handleCheckedCitiesChangeRight" class="checkBoxClas">
                <el-checkbox v-for="item in rightArr" :key="item.id" :label="item.id">
                  <!-- <el-tooltip effect="dark"
                              :show-after="500"
                              :content="item.title"
                              placement="top-start"> -->
                  <div @click.prevent="chekDetail(item)" :title="item.title" class="titleTips ellipsis"><span
                      v-if="item.streamNumber">[{{ item.streamNumber }}]</span>
                    {{ item.title }}
                  </div>
                  <!-- </el-tooltip> -->
                </el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>
            <el-pagination class="paginationClaRight" v-model:currentPage="SugDataRight.pageNo"
              v-model:page-size="SugDataRight.pageSize" :page-sizes="SugDataRight.pageSizes"
              layout="sizes, prev, pager, next, total" @size-change="ChangeSizeRight"
              @current-change="ChangePageNoRight" :pager-count="5" :total="SugDataRight.total" small />
          </div>
        </div>
      </div>
    </div>

  </el-scrollbar>
</template>
<script>
export default { name: 'SuggestedClassification' }
</script>
<script setup>
import api from '@/api'
import { Back, Right } from '@element-plus/icons-vue'
import { reactive, ref, onActivated, watch } from 'vue'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage, ElMessageBox } from 'element-plus'
const loading = ref(false)
const loadingText = ref('')
const loadingRight = ref(false)

const toCategory = ref(false)
const toBack = ref(false)

const form = reactive({
  SuggestBigType: '', // 提案大类
  SuggestBigTypeName: '',
  SuggestSmallType: '', // 提案小类
  SuggestSmallTypeName: '',
  transactType: '', // 请选择办理方式
  mainHandleOfficeId: [],
  handleOfficeIds: []
})
const BigTypeArr = ref([])
const SmallTypeArr = ref([])

const SuggestBigTypeChange = () => {
  if (form.SuggestBigType) {
    for (let index = 0; index < BigTypeArr.value.length; index++) {
      const item = BigTypeArr.value[index]
      if (item.id === form.SuggestBigType) {
        form.SuggestBigTypeName = item.name
        form.SuggestSmallType = ''
        SmallTypeArr.value = item.children
      }
    }
  } else {
    form.SuggestBigTypeName = ''
    form.SuggestSmallType = ''
    SmallTypeArr.value = []
  }
}
const suggestionThemeSelect = async () => {
  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })
  var { data } = res
  BigTypeArr.value = data
}

const chekDetail = (item) => {
  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } } })
}

onActivated(() => {
  suggestionThemeSelect()
  leftInfo()
})

// 未分类提案
const SugData = reactive({
  total: 0,
  pageNo: 1,
  pageSize: 20,
  pageSizes: [10, 20, 50, 80]
})
const checkAll = ref(false)
const isIndeterminate = ref(true)
const checkListLeft = ref([])
const leftArr = ref([])
const ChangePageNo = (i) => {
  SugData.pageNo = i
  leftInfo()
}
const ChangeSize = (i) => {
  SugData.pageSize = i
  leftInfo()
}
const handleCheckAllChange = (val) => {
  checkListLeft.value = val ? leftArr.value.map(v => v.id) : []
  isIndeterminate.value = false
}
const handleCheckedCitiesChange = (val) => {
  const checkedCount = val.length
  checkAll.value = checkedCount === leftArr.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < leftArr.value.length
}
const leftInfo = async () => {
  try {
    // loading.value = true
    var params = {
      keyword: '',
      pageNo: SugData.pageNo,
      pageSize: SugData.pageSize,
    }
    const res = await api.reqProposalEmpty('empty', params) //查询未分类提案
    var { data, total, code } = res
    if (code === 200) {
      // loading.value = false
    }
    leftArr.value = data
    SugData.total = total
    checkListLeft.value = []
  } catch (err) {
    // loading.value = false
  }
}


//已分类提案
const SugDataRight = reactive({
  total: 0,
  pageNo: 1,
  pageSize: 20,
  pageSizes: [10, 20, 50, 80]
})
const checkListRight = ref([])
const rightArr = ref([])
const checkAllRight = ref(false)
const isIndeterminateRight = ref(true)
const ChangePageNoRight = (i) => {
  SugDataRight.pageNo = i
  RightInfo()
}
const ChangeSizeRight = (i) => {
  SugDataRight.pageSize = i
  RightInfo()
}
const handleCheckAllChangeRight = (val) => {
  checkListRight.value = val ? rightArr.value.map(v => v.id) : []
  isIndeterminateRight.value = false
}
const handleCheckedCitiesChangeRight = (val) => {
  const checkedCount = val.length
  checkAllRight.value = checkedCount === rightArr.value.length
  isIndeterminateRight.value = checkedCount > 0 && checkedCount < rightArr.value.length
}
const RightInfo = async () => {
  try {
    // loadingRight.value = true
    var params = {
      keyword: '',
      pageNo: SugDataRight.pageNo,
      pageSize: SugDataRight.pageSize,
      bigThemeId: form.SuggestBigType,
      smallThemeId: form.SuggestSmallType
    }
    const res = await api.reqProposalEmpty('notempty', params) //查询已分类提案
    var { data, total, code } = res
    if (code === 200) {
      // loadingRight.value = false
    }
    rightArr.value = data
    SugDataRight.total = total
    checkListRight.value = []
  } catch (err) {
    // loadingRight.value = false
  }
}

const Category = async () => {
  if (form.SuggestBigType) {
    //调取分类操作接口
    var idsArr = checkListLeft.value
    var params = {
      ids: idsArr,
      bigThemeId: form.SuggestBigType,
      bigThemeName: form.SuggestBigTypeName,
      smallThemeId: form.SuggestSmallType,
      smallThemeName: form.SuggestSmallTypeName
    }
    const res = await api.reqProposalTheme('add', params) //查询已分类提案
    var { code } = res
    if (code == 200) {
      ElMessage.success('分类成功')
      leftInfo()
      RightInfo()
    }
  } else {
    ElMessageBox.alert(`请先确定右侧提案类别再点击“分类”`, '提示', {
      confirmButtonText: '确定',
      type: 'warning'
    }).then(() => { }).catch(() => { })
  }
}
const sendBack = async () => {
  var idsArr = checkListRight.value
  const res = await api.reqProposalTheme('clear', { ids: idsArr }) //查询已分类提案
  var { code, message } = res
  if (code == 200) {
    ElMessage.success(message)
    leftInfo()
    RightInfo()
  }
}

watch(() => checkListLeft.value, (val) => {
  if (val) {
    toCategory.value = val.length > 0
    if (val.length > 0) {
      toCategory.value = false
    } else {
      toCategory.value = true
    }
  }
}, { immediate: true })
watch(() => checkListRight.value, (val) => {
  if (val) {
    toBack.value = val.length > 0
    if (val.length > 0) {
      toBack.value = false
    } else {
      toBack.value = true
    }
  }
}, { immediate: true })
watch(() => form.SuggestBigType, (val) => {
  RightInfo()
}, { immediate: true })
watch(() => form.SuggestSmallType, (nowVal, oldVal) => {
  if (nowVal) {
    BigTypeArr.value.forEach((v) => {
      if (form.SuggestBigType === v.id) {
        v.children.forEach((vv) => { if (vv.id === nowVal) { form.SuggestSmallTypeName = vv.name } })
      }
    })
    RightInfo()
  } else { form.SuggestSmallTypeName = '' }
  if (oldVal && nowVal === '') { RightInfo() }
}, { immediate: false })

</script>
<style lang="scss">
.SuggestedClassification {
  width: 100%;
  height: 100%;

  .SubmitSuggestBody {
    padding: 20px 20px 10px 20px;
    width: 1000px;
    margin: 10px auto;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
    display: flex;
    justify-content: space-evenly;

    .leftBox {
      width: 400px;

      .titleClas {
        background: #999999;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        font-size: 22px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        margin-bottom: 10px;
        padding: 0 20px;

        .titleMidC {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 16px;
        }

        .iconCla {
          height: 24px;
          padding-right: 12px;
        }
      }

      .tipsClas {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
      }

      .contentCla {
        margin-top: 10px;

        .scrollbarClas {
          height: calc(100vh - 310px);
          border: 1px solid #cccccc;
          padding: 0 10px;

          .checkBoxClas {
            display: flex;
            flex-direction: column;

            .titleTips {
              width: 350px;

              &:hover {
                color: var(--zy-el-color-primary);
              }
            }
          }
        }
      }

      .paginationCla {
        padding-top: 6px;
        overflow-x: auto;
      }
    }

    .middleBox {
      width: 100px;
      display: flex;
      flex-direction: column;

      .midTop {
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;

        .iconCla2 {
          height: 40px;
        }
      }

      .midButtom {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-bottom: 100px;
      }
    }

    .rightBox {
      width: 400px;

      .titleClasRight {
        background: var(--zy-el-color-primary);
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        font-size: 22px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        margin-bottom: 10px;
        padding: 0 20px;

        .titleMidC {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 16px;
        }

        .iconCla {
          height: 24px;
          padding-right: 12px;
        }
      }

      .tipsClasRight {
        font-size: 14px;
        font-weight: 400;
        color: #999999;

        .sugclass {
          display: flex;
          align-items: center;

          .leftCTip {
            font-weight: bold;
            color: #333333;
          }

          .zy-el-select {
            width: 240px;
          }

          .requiredStar::before {
            content: "*";
            color: var(--zy-el-color-danger);
            margin-right: 4px;
          }
        }
      }

      .contentClaRight {
        margin-top: 10px;

        .scrollbarClasRight {
          height: calc(100vh - 372px);
          border: 1px solid #cccccc;
          padding: 0 10px;

          .checkBoxClas {
            display: flex;
            flex-direction: column;

            .titleTips {
              width: 350px;

              &:hover {
                color: var(--zy-el-color-primary);
              }
            }
          }
        }
      }

      .paginationClaRight {
        padding-top: 6px;
        overflow-x: auto;
      }
    }
  }
}
</style>
