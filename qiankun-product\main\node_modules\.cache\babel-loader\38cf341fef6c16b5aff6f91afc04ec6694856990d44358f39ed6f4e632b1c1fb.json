{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, vShow as _vShow, withDirectives as _withDirectives, openBlock as _openBlock, createElement<PERSON><PERSON> as _createElementBlock, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, withCtx as _withCtx, createBlock as _createBlock, createTextVNode as _createTextVNode, resolveDynamicComponent as _resolveDynamicComponent, KeepAlive as _KeepAlive, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, Transition as _Transition } from \"vue\";\nimport _imports_0 from '../img/login_btn_bg.png';\nimport _imports_1 from '../img/icon_layout_home.png';\nimport _imports_2 from '../img/icon_layout_news.png';\nimport _imports_3 from '../img/icon_layout_system.png';\nvar _hoisted_1 = {\n  class: \"LayoutViewBox\"\n};\nvar _hoisted_2 = [\"innerHTML\"];\nvar _hoisted_3 = {\n  class: \"LayoutViewInfo\",\n  ref: \"LayoutViewInfo\"\n};\nvar _hoisted_4 = [\"src\"];\nvar _hoisted_5 = {\n  key: 0,\n  class: \"LayoutViewBreadcrumb\"\n};\nvar _hoisted_6 = {\n  class: \"LayoutViewBody\"\n};\nvar _hoisted_7 = {\n  key: 1,\n  class: \"ConstraintEditPassWord\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_xyl_region = _resolveComponent(\"xyl-region\");\n  var _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  var _component_el_header = _resolveComponent(\"el-header\");\n  var _component_el_aside = _resolveComponent(\"el-aside\");\n  var _component_xyl_tab_item = _resolveComponent(\"xyl-tab-item\");\n  var _component_xyl_tab = _resolveComponent(\"xyl-tab\");\n  var _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  var _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  var _component_router_view = _resolveComponent(\"router-view\");\n  var _component_el_main = _resolveComponent(\"el-main\");\n  var _component_el_container = _resolveComponent(\"el-container\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createVNode(_component_el_container, {\n    class: \"LayoutView\"\n  }, {\n    default: _withCtx(function () {\n      return [$setup.route.name != 'WorkBenchCopy' ? (_openBlock(), _createBlock(_component_el_header, {\n        key: 0,\n        class: \"LayoutViewHeader\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_image, {\n            class: \"LayoutViewLogo\",\n            src: $setup.systemLogo,\n            fit: \"contain\"\n          }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", {\n            class: \"LayoutViewName\",\n            innerHTML: $setup.systemName\n          }, null, 8 /* PROPS */, _hoisted_2)]), _createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" <el-dropdown @command=\\\"handleFontSizeChange\\\">\\r\\n          <span class=\\\"LayoutViewFontSize\\\">\\r\\n            {{ currentFontSizeLabel }} <el-icon><arrow-down /></el-icon>\\r\\n          </span>\\r\\n          <template #dropdown>\\r\\n            <el-dropdown-menu>\\r\\n              <el-dropdown-item command=\\\"default\\\">默认字号</el-dropdown-item>\\r\\n              <el-dropdown-item command=\\\"large\\\">大字号</el-dropdown-item>\\r\\n              <el-dropdown-item command=\\\"xlarge\\\">超大字号</el-dropdown-item>\\r\\n            </el-dropdown-menu>\\r\\n          </template>\\r\\n</el-dropdown> \"), _withDirectives(_createVNode(_component_xyl_region, {\n            modelValue: $setup.regionId,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.regionId = $event;\n            }),\n            data: $setup.area,\n            onSelect: $setup.regionSelect,\n            props: {\n              label: 'name',\n              children: 'children'\n            }\n          }, null, 8 /* PROPS */, [\"modelValue\", \"data\", \"onSelect\"]), [[_vShow, !$setup.isChildView]]), $setup.route.name == 'homePage' ? (_openBlock(), _createElementBlock(\"button\", {\n            key: 0,\n            class: \"new_version\",\n            onClick: $setup.newVersion\n          }, _cache[8] || (_cache[8] = [_createElementVNode(\"img\", {\n            src: _imports_0,\n            alt: \"\"\n          }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"新版本\", -1 /* HOISTED */)]))) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n            class: \"LayoutViewMenuItem\",\n            onClick: $setup.returnHome\n          }, _cache[9] || (_cache[9] = [_createElementVNode(\"img\", {\n            src: _imports_1,\n            alt: \"首页\"\n          }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"首页\", -1 /* HOISTED */)])), false ? (_openBlock(), _createElementBlock(\"div\", {\n            key: 1,\n            class: \"LayoutViewMenuItem\",\n            onClick: $setup.openInformation\n          }, _cache[10] || (_cache[10] = [_createElementVNode(\"img\", {\n            src: _imports_2,\n            alt: \"资讯\"\n          }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"资讯\", -1 /* HOISTED */)]))) : _createCommentVNode(\"v-if\", true), $setup.isSys ? (_openBlock(), _createElementBlock(\"div\", {\n            key: 2,\n            class: \"LayoutViewMenuItem\",\n            onClick: $setup.sysManagement\n          }, _cache[11] || (_cache[11] = [_createElementVNode(\"img\", {\n            src: _imports_3,\n            alt: \"系统\"\n          }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, \"系统\", -1 /* HOISTED */)]))) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_tooltip, {\n            placement: \"top\",\n            effect: \"light\",\n            offset: 6,\n            disabled: !$setup.role.length\n          }, {\n            content: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.role, function (item, index) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  class: \"LayoutViewRoleItem\",\n                  key: index\n                }, _toDisplayString(item), 1 /* TEXT */);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            default: _withCtx(function () {\n              return [_createElementVNode(\"div\", {\n                class: \"LayoutViewMenuItem\",\n                onClick: $setup.sysUser\n              }, [_createElementVNode(\"img\", {\n                src: $setup.user.image,\n                alt: \"\",\n                class: \"avatar\"\n              }, null, 8 /* PROPS */, _hoisted_4), _createElementVNode(\"span\", null, _toDisplayString($setup.user.userName), 1 /* TEXT */)])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"disabled\"]), _createElementVNode(\"div\", {\n            class: \"LayoutViewMenuItem\",\n            onClick: _cache[1] || (_cache[1] = function ($event) {\n              return $setup.handleCommand('exit');\n            })\n          }, _cache[12] || (_cache[12] = [_createElementVNode(\"span\", null, \"退出\", -1 /* HOISTED */)]))], 512 /* NEED_PATCH */)];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_container, {\n        class: \"LayoutViewContainer\",\n        style: _normalizeStyle($setup.route.name != 'WorkBenchCopy' ? 'height: calc(100% - 120px);' : 'height: 100%;')\n      }, {\n        default: _withCtx(function () {\n          return [_withDirectives(_createVNode(_component_el_aside, {\n            class: \"LayoutViewAside\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode($setup[\"LayoutMenu\"], {\n                modelValue: $setup.menuId,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n                  return $setup.menuId = $event;\n                }),\n                menuData: $setup.menuData,\n                onSelect: $setup.menuClick\n              }, null, 8 /* PROPS */, [\"modelValue\", \"menuData\", \"onSelect\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.isView]]), _createVNode(_component_el_main, {\n            class: _normalizeClass([\"LayoutViewMain\", {\n              LayoutViewMainView: !$setup.isView,\n              LayoutViewMainBreadcrumb: !$setup.isView && $setup.tabData.length > 1\n            }])\n          }, {\n            default: _withCtx(function () {\n              return [_withDirectives(_createVNode(_component_xyl_tab, {\n                modelValue: $setup.menuId,\n                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n                  return $setup.menuId = $event;\n                }),\n                onTabClick: $setup.tabClick,\n                onRefresh: $setup.handleRefresh,\n                onClose: $setup.handleClose,\n                feature: \"\",\n                onCloseOther: $setup.handleCloseOther\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tabData, function (item) {\n                    return _openBlock(), _createBlock(_component_xyl_tab_item, {\n                      key: item.id,\n                      value: item.id\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\", \"onTabClick\", \"onRefresh\", \"onClose\", \"onCloseOther\"]), [[_vShow, $setup.isView]]), !$setup.isView && $setup.tabData.length > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_breadcrumb, {\n                \"separator-icon\": $setup.ArrowRight\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tabData, function (item, index) {\n                    return _openBlock(), _createBlock(_component_el_breadcrumb_item, {\n                      key: `key-${item.id}`,\n                      onClick: function onClick($event) {\n                        return $setup.handleBreadcrumb(item, index);\n                      }\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"separator-icon\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_router_view, null, {\n                default: _withCtx(function (_ref) {\n                  var Component = _ref.Component;\n                  return [(_openBlock(), _createBlock(_KeepAlive, {\n                    include: $setup.keepAliveRoute\n                  }, [$setup.isMain && $setup.isRefresh ? (_openBlock(), _createBlock(_resolveDynamicComponent(Component), {\n                    key: _ctx.$route.fullPath\n                  })) : _createCommentVNode(\"v-if\", true)], 1032 /* PROPS, DYNAMIC_SLOTS */, [\"include\"]))];\n                }),\n                _: 1 /* STABLE */\n              }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.MicroApp, function (item) {\n                return _withDirectives((_openBlock(), _createBlock($setup[\"SubAppViewport\"], {\n                  key: item,\n                  name: item\n                }, null, 8 /* PROPS */, [\"name\"])), [[_vShow, !$setup.isMain && $setup.isMicroApp === item]]);\n              }), 128 /* KEYED_FRAGMENT */))])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"class\"]), $setup.whetherAiChat ? (_openBlock(), _createBlock(_component_el_aside, {\n            key: 0,\n            class: \"LayoutViewFloatingWindow\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_Transition, {\n                name: \"width-animation\"\n              }, {\n                default: _withCtx(function () {\n                  return [$setup.AiChatViewType ? _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n                    key: 0,\n                    class: \"LayoutViewFloatingWindowBody\",\n                    style: _normalizeStyle({\n                      '--ai-chat-target-width': $setup.AiChatTargetWidth\n                    })\n                  }, [_createVNode($setup[\"GlobalAiChat\"], {\n                    modelValue: $setup.AiChatWindowShow,\n                    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n                      return $setup.AiChatWindowShow = $event;\n                    })\n                  }, null, 8 /* PROPS */, [\"modelValue\"])], 4 /* STYLE */)), [[_vShow, $setup.AiChatWindowShow]]) : _createCommentVNode(\"v-if\", true)];\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"style\"]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.helpShow,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n          return $setup.helpShow = $event;\n        }),\n        name: \"帮助文档\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"HelpDocument\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.editPassWordShow,\n        \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n          return $setup.editPassWordShow = $event;\n        }),\n        name: \"修改密码\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"EditPassWord\"], {\n            type: $setup.verifyEditPassWord,\n            onCallback: $setup.editPassWordCallback\n          }, null, 8 /* PROPS */, [\"type\", \"onCallback\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), $setup.verifyEditPassWordShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createVNode($setup[\"EditPassWord\"], {\n        type: $setup.verifyEditPassWord,\n        onCallback: $setup.editPassWordCallback\n      }, null, 8 /* PROPS */, [\"type\", \"onCallback\"])])) : _createCommentVNode(\"v-if\", true), $setup.isRegionSelectShow ? (_openBlock(), _createBlock($setup[\"GlobalRegionSelect\"], {\n        key: 2,\n        onCallback: $setup.regionSelect\n      }, null, 8 /* PROPS */, [\"onCallback\"])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode($setup[\"qusetionAnswering\"]), $setup.rongCloudToken ? (_openBlock(), _createBlock($setup[\"GlobalChatFloating\"], {\n    key: 0\n  })) : _createCommentVNode(\"v-if\", true), $setup.whetherAiChat ? (_openBlock(), _createBlock($setup[\"GlobalFloatingWindow\"], {\n    key: 1,\n    modelValue: $setup.AiChatWindowShow,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n      return $setup.AiChatWindowShow = $event;\n    }),\n    disabled: $setup.AiChatViewType\n  }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])) : _createCommentVNode(\"v-if\", true), $setup.whetherAiChat ? (_openBlock(), _createBlock($setup[\"GlobalAiControls\"], {\n    key: 2\n  })) : _createCommentVNode(\"v-if\", true), $setup.isMain && $setup.loginHintShow ? (_openBlock(), _createBlock($setup[\"suggestPop\"], {\n    key: 3\n  })) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "_imports_3", "class", "ref", "key", "_createElementBlock", "_Fragment", "_createVNode", "_component_el_container", "default", "_withCtx", "$setup", "route", "name", "_createBlock", "_component_el_header", "_createElementVNode", "_hoisted_1", "_component_el_image", "src", "systemLogo", "fit", "innerHTML", "systemName", "_hoisted_2", "_hoisted_3", "_createCommentVNode", "_component_xyl_region", "modelValue", "regionId", "_cache", "$event", "data", "area", "onSelect", "regionSelect", "props", "label", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "newVersion", "alt", "returnHome", "openInformation", "isSys", "sysManagement", "_component_el_tooltip", "placement", "effect", "offset", "disabled", "role", "length", "content", "_renderList", "item", "index", "sysUser", "user", "image", "_hoisted_4", "_toDisplayString", "userName", "_", "handleCommand", "style", "_normalizeStyle", "_component_el_aside", "menuId", "menuData", "menuClick", "<PERSON><PERSON><PERSON><PERSON>", "_component_el_main", "_normalizeClass", "LayoutViewMainView", "LayoutViewMainBreadcrumb", "tabData", "_component_xyl_tab", "onTabClick", "tabClick", "onRefresh", "handleRefresh", "onClose", "handleClose", "feature", "onCloseOther", "handleCloseOther", "_component_xyl_tab_item", "id", "value", "_createTextVNode", "_hoisted_5", "_component_el_breadcrumb", "ArrowRight", "_component_el_breadcrumb_item", "handleBreadcrumb", "_hoisted_6", "_component_router_view", "_ref", "Component", "_KeepAlive", "include", "keepAliveRoute", "is<PERSON><PERSON>", "isRefresh", "_resolveDynamicComponent", "_ctx", "$route", "fullPath", "MicroApp", "isMicroApp", "whetherAiChat", "_Transition", "AiChatViewType", "AiChatTargetWidth", "AiChatWindowShow", "_component_xyl_popup_window", "helpShow", "editPassWordShow", "type", "verifyEditPassWord", "onCallback", "editPassWordCallback", "verifyEditPassWordShow", "_hoisted_7", "isRegionSelectShow", "rongCloudToken", "loginHintShow"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutView\\LayoutView.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"LayoutView\">\r\n    <el-header class=\"LayoutViewHeader\" v-if=\"route.name != 'WorkBenchCopy'\">\r\n      <div class=\"LayoutViewBox\">\r\n        <el-image class=\"LayoutViewLogo\" :src=\"systemLogo\" fit=\"contain\" />\r\n        <div class=\"LayoutViewName\" v-html=\"systemName\"></div>\r\n      </div>\r\n      <div class=\"LayoutViewInfo\" ref=\"LayoutViewInfo\">\r\n        <!-- <el-dropdown @command=\"handleFontSizeChange\">\r\n          <span class=\"LayoutViewFontSize\">\r\n            {{ currentFontSizeLabel }} <el-icon><arrow-down /></el-icon>\r\n          </span>\r\n          <template #dropdown>\r\n            <el-dropdown-menu>\r\n              <el-dropdown-item command=\"default\">默认字号</el-dropdown-item>\r\n              <el-dropdown-item command=\"large\">大字号</el-dropdown-item>\r\n              <el-dropdown-item command=\"xlarge\">超大字号</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </template>\r\n</el-dropdown> -->\r\n        <xyl-region v-model=\"regionId\" :data=\"area\" @select=\"regionSelect\" v-show=\"!isChildView\"\r\n          :props=\"{ label: 'name', children: 'children' }\"></xyl-region>\r\n        <button v-if=\"route.name == 'homePage'\" class=\"new_version\" @click=\"newVersion\">\r\n          <img src=\"../img/login_btn_bg.png\" alt=\"\" />\r\n          <span>新版本</span>\r\n        </button>\r\n        <div class=\"LayoutViewMenuItem\" @click=\"returnHome\">\r\n          <img src=\"../img/icon_layout_home.png\" alt=\"首页\" />\r\n          <span>首页</span>\r\n        </div>\r\n        <div class=\"LayoutViewMenuItem\" @click=\"openInformation\" v-if=\"false\">\r\n          <img src=\"../img/icon_layout_news.png\" alt=\"资讯\" />\r\n          <span>资讯</span>\r\n        </div>\r\n        <div class=\"LayoutViewMenuItem\" @click=\"sysManagement\" v-if=\"isSys\">\r\n          <img src=\"../img/icon_layout_system.png\" alt=\"系统\" />\r\n          <span>系统</span>\r\n        </div>\r\n        <el-tooltip placement=\"top\" effect=\"light\" :offset=\"6\" :disabled=\"!role.length\">\r\n          <template #content>\r\n            <div class=\"LayoutViewRoleItem\" v-for=\"(item, index) in role\" :key=\"index\">{{ item }}</div>\r\n          </template>\r\n          <div class=\"LayoutViewMenuItem\" @click=\"sysUser\">\r\n            <img :src=\"user.image\" alt=\"\" class=\"avatar\" />\r\n            <span>{{ user.userName }}</span>\r\n          </div>\r\n        </el-tooltip>\r\n        <div class=\"LayoutViewMenuItem\" @click=\"handleCommand('exit')\">\r\n          <span>退出</span>\r\n        </div>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"LayoutViewContainer\"\r\n      :style=\"route.name != 'WorkBenchCopy' ? 'height: calc(100% - 120px);' : 'height: 100%;'\">\r\n      <el-aside class=\"LayoutViewAside\" v-show=\"isView\">\r\n        <LayoutMenu v-model=\"menuId\" :menuData=\"menuData\" @select=\"menuClick\"></LayoutMenu>\r\n      </el-aside>\r\n      <el-main class=\"LayoutViewMain\"\r\n        :class=\"{ LayoutViewMainView: !isView, LayoutViewMainBreadcrumb: (!isView && (tabData.length > 1)) }\">\r\n        <xyl-tab v-model=\"menuId\" @tab-click=\"tabClick\" @refresh=\"handleRefresh\" @close=\"handleClose\" feature\r\n          @closeOther=\"handleCloseOther\" v-show=\"isView\">\r\n          <xyl-tab-item v-for=\"item in tabData\" :key=\"item.id\" :value=\"item.id\">{{ item.name }}</xyl-tab-item>\r\n        </xyl-tab>\r\n        <div class=\"LayoutViewBreadcrumb\" v-if=\"!isView && tabData.length > 1\">\r\n          <el-breadcrumb :separator-icon=\"ArrowRight\">\r\n            <el-breadcrumb-item v-for=\"(item, index) in tabData\" :key=\"`key-${item.id}`\"\r\n              @click=\"handleBreadcrumb(item, index)\">\r\n              {{ item.name }}\r\n            </el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"LayoutViewBody\">\r\n          <router-view v-slot=\"{ Component }\">\r\n            <keep-alive :include=\"keepAliveRoute\">\r\n              <component v-if=\"isMain && isRefresh\" :key=\"$route.fullPath\" :is=\"Component\"></component>\r\n            </keep-alive>\r\n          </router-view>\r\n          <SubAppViewport v-for=\"item in MicroApp\" :key=\"item\" v-show=\"!isMain && isMicroApp === item\" :name=\"item\">\r\n          </SubAppViewport>\r\n        </div>\r\n      </el-main>\r\n      <el-aside class=\"LayoutViewFloatingWindow\" v-if=\"whetherAiChat\">\r\n        <transition name=\"width-animation\">\r\n          <div class=\"LayoutViewFloatingWindowBody\" :style=\"{ '--ai-chat-target-width': AiChatTargetWidth }\"\r\n            v-if=\"AiChatViewType\" v-show=\"AiChatWindowShow\">\r\n            <GlobalAiChat v-model=\"AiChatWindowShow\"></GlobalAiChat>\r\n          </div>\r\n        </transition>\r\n      </el-aside>\r\n    </el-container>\r\n    <xyl-popup-window v-model=\"helpShow\" name=\"帮助文档\">\r\n      <HelpDocument></HelpDocument>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"editPassWordShow\" name=\"修改密码\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </xyl-popup-window>\r\n    <div class=\"ConstraintEditPassWord\" v-if=\"verifyEditPassWordShow\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </div>\r\n    <GlobalRegionSelect v-if=\"isRegionSelectShow\" @callback=\"regionSelect\"></GlobalRegionSelect>\r\n  </el-container>\r\n  <qusetionAnswering></qusetionAnswering>\r\n  <GlobalChatFloating v-if=\"rongCloudToken\"></GlobalChatFloating>\r\n  <GlobalFloatingWindow v-model=\"AiChatWindowShow\" :disabled=\"AiChatViewType\" v-if=\"whetherAiChat\" />\r\n  <GlobalAiControls v-if=\"whetherAiChat\" />\r\n  <suggestPop v-if=\"isMain && loginHintShow\" />\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutView' }\r\n</script>\r\n<script setup>\r\nimport { defineAsyncComponent, ref, onMounted, nextTick } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { qiankun, LayoutView, ChatMethod, AiChatMethod, refreshIcon, loginHintMethod } from './LayoutView.js'\r\nimport {\r\n  systemLogo,\r\n  systemName,\r\n  whetherAiChat,\r\n  systemNameAreaPrefix,\r\n  layoutNameBg,\r\n  layoutChildBg,\r\n  layoutChildNameBg\r\n} from 'common/js/system_var.js'\r\nimport { ArrowRight } from '@element-plus/icons-vue'\r\nconst HelpDocument = defineAsyncComponent(() => import('../LayoutContainer/components/HelpDocument'))\r\nconst EditPassWord = defineAsyncComponent(() => import('../LayoutContainer/components/EditPassWord'))\r\n// const LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))\r\n// const LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))\r\nconst GlobalRegionSelect = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalRegionSelect'))\r\nconst GlobalChatFloating = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalChatFloating'))\r\nconst GlobalFloatingWindow = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalFloatingWindow'))\r\nconst GlobalAiControls = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalAiControls'))\r\nconst GlobalAiChat = defineAsyncComponent(() => import('../GlobalAiChat/GlobalAiChat'))\r\nconst LayoutMenu = defineAsyncComponent(() => import('./component/LayoutMenu/LayoutMenu.vue'))\r\nconst suggestPop = defineAsyncComponent(() => import('./component/suggestPop'))\r\nconst qusetionAnswering = defineAsyncComponent(() => import('./component/question-answering.vue'))\r\nconst SubAppViewport = {\r\n  name: 'SubAppViewport',\r\n  props: ['name'],\r\n  template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\r\n}\r\nconst router = useRouter()\r\nconst { isMain } = qiankun(useRoute())\r\nconst route = useRoute()\r\n// const currentFontSizeLabel = ref('切换字号')\r\nconst isSys = ref(false)\r\n// const loginSystemName = computed(() => {\r\n//   const name = (platformAreaName.value || '') + systemName.value\r\n//   const num = Number(loginNameLineFeedPosition.value || '0') || 0\r\n//   return num ? name.substring(0, num) + '\\n' + name.substring(num) : name\r\n// })\r\nconst {\r\n  user, area, role, left, width, LayoutViewBox, LayoutViewInfo, helpShow, handleCommand,\r\n  editPassWordShow, verifyEditPassWord, verifyEditPassWordShow, editPassWordCallback,\r\n  regionId, regionName, regionSelect, isRegionSelectShow, isView, isChildView, tabMenu, tabMenuData, handleClick,\r\n  menuId, menuData, menuClick, handleBreadcrumb, WorkBenchObj, childData, WorkBenchReturn,\r\n  isRefresh, keepAliveRoute, tabData, tabClick, handleRefresh, handleClose, handleCloseOther,\r\n  isMicroApp, MicroApp, openPage, leftMenuData, WorkBenchList\r\n} = LayoutView(useRoute(), useRouter())\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    setTimeout(() => {\r\n      const roleList = JSON.parse(sessionStorage.getItem('role'))\r\n      console.log('当前角色===>', roleList)\r\n      if (roleList) { isSys.value = roleList?.includes('管理员') }\r\n    }, 1000)\r\n  })\r\n})\r\nconst { rongCloudToken } = ChatMethod()\r\nconst { AiChatTargetWidth, AiChatViewType, AiChatWindowShow } = AiChatMethod()\r\n\r\n// const handleFontSizeChange = (command) => {\r\n//   if (command === 'default') currentFontSizeLabel.value = '默认字号'\r\n//   if (command === 'large') currentFontSizeLabel.value = '大字号'\r\n//   if (command === 'xlarge') currentFontSizeLabel.value = '超大字号'\r\n//   // 这里可以加实际字号切换逻辑\r\n// }\r\nconst returnHome = () => {\r\n  if (isChildView.value) {\r\n    WorkBenchReturn()\r\n  } else {\r\n    openPage({ key: 'routePath', value: '/homePage' })\r\n  }\r\n}\r\n\r\n// 切换新版本\r\nconst newVersion = () => {\r\n  console.log('切换到新版本')\r\n  openPage({ key: 'routePath', value: '/WorkBenchCopy' })\r\n}\r\n// 资讯\r\nconst openInformation = () => {\r\n  openPage({ key: 'routePath', value: '/information/AllInformation?moduleId=1&moduleName=资讯' })\r\n  // const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')\r\n  // const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '新闻资讯')\r\n  // leftMenuData(systemOperation)\r\n}\r\n// 系统管理\r\nconst sysManagement = () => {\r\n  const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')\r\n  const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '系统运维')\r\n  leftMenuData(systemOperation)\r\n}\r\n// 跳转到我的\r\nconst sysUser = () => {\r\n  const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')\r\n  const myOperation = filtered.flatMap(item => item.children).find(child => child.name === '我的')\r\n  leftMenuData(myOperation)\r\n}\r\n\r\nconst { loginHintShow } = loginHintMethod()\r\n\r\nconst handleAiToolBox = () => {\r\n  router.push('/GlobalAiToolBox')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutView {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .LayoutViewHeader {\r\n    height: 120px;\r\n    background: url(\"../img/layout_top_bg1.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n    position: relative;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .LayoutViewBox {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      box-sizing: border-box;\r\n      position: relative;\r\n      z-index: 2;\r\n      flex-shrink: 0;\r\n\r\n      .LayoutViewLogo {\r\n        height: 75px;\r\n        width: 75px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .LayoutViewName {\r\n        font-size: 37px;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        margin-left: 15px;\r\n        letter-spacing: 5px;\r\n      }\r\n    }\r\n\r\n    .LayoutViewInfo {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 32px;\r\n\r\n      .new_version {\r\n        background: none;\r\n        border: none;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          height: 39px;\r\n        }\r\n\r\n        span {\r\n          position: absolute;\r\n          left: 0;\r\n          width: 100%;\r\n          text-align: center;\r\n          color: rgb(0, 51, 152);\r\n          font-size: 14px;\r\n          line-height: 39px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n\r\n      .LayoutViewFontSize {\r\n        color: #fff;\r\n        font-size: 18px;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-right: 24px;\r\n\r\n        .el-icon {\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n\r\n      .LayoutViewMenuItem {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        color: #fff;\r\n        font-size: 18px;\r\n        cursor: pointer;\r\n        margin-right: 24px;\r\n\r\n        img {\r\n          width: 35px;\r\n          height: 35px;\r\n        }\r\n\r\n        .avatar {\r\n          width: 35px;\r\n          height: 35px;\r\n          border-radius: 50%;\r\n          margin: 0 8px;\r\n          object-fit: cover;\r\n          border: 2px solid #fff;\r\n          background: #eee;\r\n        }\r\n\r\n        span {\r\n          font-size: 16px;\r\n          margin-top: 4px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LayoutViewContainer {\r\n    width: 100%;\r\n    // height: 100%;\r\n    //  height: calc(100% - 62px);\r\n    background: var(--zy-el-color-info-light-9);\r\n\r\n    .LayoutViewFloatingWindow {\r\n      width: auto;\r\n      height: 100%;\r\n\r\n      .LayoutViewFloatingWindowBody {\r\n        width: var(--ai-chat-target-width);\r\n        height: 100%;\r\n        background: #fff;\r\n        box-sizing: border-box;\r\n        transform-origin: left center;\r\n        border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      /* 进入动画 */\r\n      .width-animation-enter-active {\r\n        animation: widen 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 离开动画 */\r\n      .width-animation-leave-active {\r\n        animation: narrow 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 定义进入动画 */\r\n      @keyframes widen {\r\n        from {\r\n          width: 0;\r\n        }\r\n\r\n        to {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n      }\r\n\r\n      /* 定义离开动画 */\r\n      @keyframes narrow {\r\n        from {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n\r\n        to {\r\n          width: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewAside {\r\n      width: auto;\r\n    }\r\n\r\n    .LayoutViewMain {\r\n      height: 100%;\r\n      padding: var(--zy-distance-three) var(--zy-distance-three) 0 0;\r\n\r\n      .LayoutViewBreadcrumb {\r\n        width: 100%;\r\n        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px);\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: #fff;\r\n        padding: 0 var(--zy-distance-two);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .zy-el-breadcrumb {\r\n          font-size: var(--zy-name-font-size);\r\n\r\n          .zy-el-breadcrumb__inner {\r\n            cursor: pointer;\r\n            font-weight: bold;\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n\r\n          .zy-el-breadcrumb__item {\r\n            &:last-child {\r\n              .zy-el-breadcrumb__inner {\r\n                cursor: text;\r\n                font-weight: normal;\r\n                color: var(--zy-el-text-color-regular);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .LayoutViewBody {\r\n        width: 100%;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n        background-color: #fff;\r\n\r\n        .subApp-viewport {\r\n          width: 100%;\r\n          height: 100%;\r\n\r\n          >div {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewMainView {\r\n      width: 100%;\r\n      padding: 0;\r\n      background: #f8f8f8;\r\n\r\n      .LayoutViewBody {\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .LayoutViewMainBreadcrumb {\r\n      width: 100%;\r\n\r\n      .LayoutViewBody {\r\n        position: relative;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n      }\r\n    }\r\n  }\r\n\r\n  .ConstraintEditPassWord {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 999;\r\n    background-color: #fff;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .EditPassWord {\r\n      box-shadow: 0px 2px 40px rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.LayoutViewRoleItem {\r\n  font-size: var(--zy-text-font-size);\r\n  line-height: var(--zy-line-height);\r\n}\r\n</style>\r\n"], "mappings": ";OAuBeA,UAA6B;OAI7BC,UAAiC;OAIjCC,UAAiC;OAIjCC,UAAmC;;EAhCvCC,KAAK,EAAC;AAAe;iBAHhC;;EAOWA,KAAK,EAAC,gBAAgB;EAACC,GAAG,EAAC;;iBAPtC;;EAAAC,GAAA;EA+DaF,KAAK,EAAC;;;EAQNA,KAAK,EAAC;AAAgB;;EAvEnCE,GAAA;EAgGSF,KAAK,EAAC;;;;;;;;;;;;;;;;uBAhGfG,mBAAA,CAAAC,SAAA,SACEC,YAAA,CAmGeC,uBAAA;IAnGDN,KAAK,EAAC;EAAY;IADlCO,OAAA,EAAAC,QAAA,CAEa;MAAA,OAyDZ,CAzD6CC,MAAA,CAAAC,KAAK,CAACC,IAAI,uB,cAApDC,YAAA,CAiDYC,oBAAA;QAnDhBX,GAAA;QAEeF,KAAK,EAAC;;QAFrBO,OAAA,EAAAC,QAAA,CAGM;UAAA,OAGM,CAHNM,mBAAA,CAGM,OAHNC,UAGM,GAFJV,YAAA,CAAmEW,mBAAA;YAAzDhB,KAAK,EAAC,gBAAgB;YAAEiB,GAAG,EAAER,MAAA,CAAAS,UAAU;YAAEC,GAAG,EAAC;4CACvDL,mBAAA,CAAsD;YAAjDd,KAAK,EAAC,gBAAgB;YAACoB,SAAmB,EAAXX,MAAA,CAAAY;kCAL5CC,UAAA,E,GAOMR,mBAAA,CA2CM,OA3CNS,UA2CM,GA1CJC,mBAAA,kkBAWU,E,gBACVnB,YAAA,CACgEoB,qBAAA;YArBxEC,UAAA,EAoB6BjB,MAAA,CAAAkB,QAAQ;YApBrC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoB6BpB,MAAA,CAAAkB,QAAQ,GAAAE,MAAA;YAAA;YAAGC,IAAI,EAAErB,MAAA,CAAAsB,IAAI;YAAGC,QAAM,EAAEvB,MAAA,CAAAwB,YAAY;YAC9DC,KAAK,EAAE;cAAAC,KAAA;cAAAC,QAAA;YAAA;kFADkE3B,MAAA,CAAA4B,WAAW,E,GAEzE5B,MAAA,CAAAC,KAAK,CAACC,IAAI,kB,cAAxBR,mBAAA,CAGS;YAzBjBD,GAAA;YAsBgDF,KAAK,EAAC,aAAa;YAAEsC,OAAK,EAAE7B,MAAA,CAAA8B;wCAClEzB,mBAAA,CAA4C;YAAvCG,GAA6B,EAA7BrB,UAA6B;YAAC4C,GAAG,EAAC;sCACvC1B,mBAAA,CAAgB,cAAV,KAAG,oB,MAxBnBU,mBAAA,gBA0BQV,mBAAA,CAGM;YAHDd,KAAK,EAAC,oBAAoB;YAAEsC,OAAK,EAAE7B,MAAA,CAAAgC;wCACtC3B,mBAAA,CAAkD;YAA7CG,GAAiC,EAAjCpB,UAAiC;YAAC2C,GAAG,EAAC;sCAC3C1B,mBAAA,CAAe,cAAT,IAAE,oB,IAEqD,KAAK,I,cAApEX,mBAAA,CAGM;YAjCdD,GAAA;YA8BaF,KAAK,EAAC,oBAAoB;YAAEsC,OAAK,EAAE7B,MAAA,CAAAiC;0CACtC5B,mBAAA,CAAkD;YAA7CG,GAAiC,EAAjCnB,UAAiC;YAAC0C,GAAG,EAAC;sCAC3C1B,mBAAA,CAAe,cAAT,IAAE,oB,MAhClBU,mBAAA,gBAkCqEf,MAAA,CAAAkC,KAAK,I,cAAlExC,mBAAA,CAGM;YArCdD,GAAA;YAkCaF,KAAK,EAAC,oBAAoB;YAAEsC,OAAK,EAAE7B,MAAA,CAAAmC;0CACtC9B,mBAAA,CAAoD;YAA/CG,GAAmC,EAAnClB,UAAmC;YAACyC,GAAG,EAAC;sCAC7C1B,mBAAA,CAAe,cAAT,IAAE,oB,MApClBU,mBAAA,gBAsCQnB,YAAA,CAQawC,qBAAA;YARDC,SAAS,EAAC,KAAK;YAACC,MAAM,EAAC,OAAO;YAAEC,MAAM,EAAE,CAAC;YAAGC,QAAQ,GAAGxC,MAAA,CAAAyC,IAAI,CAACC;;YAC3DC,OAAO,EAAA5C,QAAA,CACgB;cAAA,OAA6B,E,kBAA7DL,mBAAA,CAA2FC,SAAA,QAxCvGiD,WAAA,CAwCoE5C,MAAA,CAAAyC,IAAI,EAxCxE,UAwCoDI,IAAI,EAAEC,KAAK;qCAAnDpD,mBAAA,CAA2F;kBAAtFH,KAAK,EAAC,oBAAoB;kBAAgCE,GAAG,EAAEqD;oCAAUD,IAAI;;;YAxC9F/C,OAAA,EAAAC,QAAA,CA0CU;cAAA,OAGM,CAHNM,mBAAA,CAGM;gBAHDd,KAAK,EAAC,oBAAoB;gBAAEsC,OAAK,EAAE7B,MAAA,CAAA+C;kBACtC1C,mBAAA,CAA+C;gBAAzCG,GAAG,EAAER,MAAA,CAAAgD,IAAI,CAACC,KAAK;gBAAElB,GAAG,EAAC,EAAE;gBAACxC,KAAK,EAAC;sCA3ChD2D,UAAA,GA4CY7C,mBAAA,CAAgC,cAAA8C,gBAAA,CAAvBnD,MAAA,CAAAgD,IAAI,CAACI,QAAQ,iB;;YA5ClCC,CAAA;2CA+CQhD,mBAAA,CAEM;YAFDd,KAAK,EAAC,oBAAoB;YAAEsC,OAAK,EAAAV,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEpB,MAAA,CAAAsD,aAAa;YAAA;0CACnDjD,mBAAA,CAAe,cAAT,IAAE,oB;;QAhDlBgD,CAAA;YAAAtC,mBAAA,gBAoDInB,YAAA,CAqCeC,uBAAA;QArCDN,KAAK,EAAC,qBAAqB;QACtCgE,KAAK,EArDZC,eAAA,CAqDcxD,MAAA,CAAAC,KAAK,CAACC,IAAI;;QArDxBJ,OAAA,EAAAC,QAAA,CAsDM;UAAA,OAEW,C,gBAFXH,YAAA,CAEW6D,mBAAA;YAFDlE,KAAK,EAAC;UAAiB;YAtDvCO,OAAA,EAAAC,QAAA,CAuDQ;cAAA,OAAmF,CAAnFH,YAAA,CAAmFI,MAAA;gBAvD3FiB,UAAA,EAuD6BjB,MAAA,CAAA0D,MAAM;gBAvDnC,uBAAAvC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAuD6BpB,MAAA,CAAA0D,MAAM,GAAAtC,MAAA;gBAAA;gBAAGuC,QAAQ,EAAE3D,MAAA,CAAA2D,QAAQ;gBAAGpC,QAAM,EAAEvB,MAAA,CAAA4D;;;YAvDnEP,CAAA;8CAsDgDrD,MAAA,CAAA6D,MAAM,E,GAGhDjE,YAAA,CAuBUkE,kBAAA;YAvBDvE,KAAK,EAzDpBwE,eAAA,EAyDqB,gBAAgB;cAAAC,kBAAA,GACEhE,MAAA,CAAA6D,MAAM;cAAAI,wBAAA,GAA8BjE,MAAA,CAAA6D,MAAM,IAAK7D,MAAA,CAAAkE,OAAO,CAACxB,MAAM;YAAA;;YA1DpG5C,OAAA,EAAAC,QAAA,CA2DQ;cAAA,OAGU,C,gBAHVH,YAAA,CAGUuE,kBAAA;gBA9DlBlD,UAAA,EA2D0BjB,MAAA,CAAA0D,MAAM;gBA3DhC,uBAAAvC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OA2D0BpB,MAAA,CAAA0D,MAAM,GAAAtC,MAAA;gBAAA;gBAAGgD,UAAS,EAAEpE,MAAA,CAAAqE,QAAQ;gBAAGC,SAAO,EAAEtE,MAAA,CAAAuE,aAAa;gBAAGC,OAAK,EAAExE,MAAA,CAAAyE,WAAW;gBAAEC,OAAO,EAAP,EAAO;gBAClGC,YAAU,EAAE3E,MAAA,CAAA4E;;gBA5DvB9E,OAAA,EAAAC,QAAA,CA6DwB;kBAAA,OAAuB,E,kBAArCL,mBAAA,CAAoGC,SAAA,QA7D9GiD,WAAA,CA6DuC5C,MAAA,CAAAkE,OAAO,EA7D9C,UA6D+BrB,IAAI;yCAAzB1C,YAAA,CAAoG0E,uBAAA;sBAA7DpF,GAAG,EAAEoD,IAAI,CAACiC,EAAE;sBAAGC,KAAK,EAAElC,IAAI,CAACiC;;sBA7D5EhF,OAAA,EAAAC,QAAA,CA6DgF;wBAAA,OAAe,CA7D/FiF,gBAAA,CAAA7B,gBAAA,CA6DmFN,IAAI,CAAC3C,IAAI,iB;;sBA7D5FmD,CAAA;;;;gBAAAA,CAAA;iHA4DiDrD,MAAA,CAAA6D,MAAM,E,IAGN7D,MAAA,CAAA6D,MAAM,IAAI7D,MAAA,CAAAkE,OAAO,CAACxB,MAAM,Q,cAAjEhD,mBAAA,CAOM,OAPNuF,UAOM,GANJrF,YAAA,CAKgBsF,wBAAA;gBALA,gBAAc,EAAElF,MAAA,CAAAmF;cAAU;gBAhEpDrF,OAAA,EAAAC,QAAA,CAiEgC;kBAAA,OAAgC,E,kBAApDL,mBAAA,CAGqBC,SAAA,QApEjCiD,WAAA,CAiEwD5C,MAAA,CAAAkE,OAAO,EAjE/D,UAiEwCrB,IAAI,EAAEC,KAAK;yCAAvC3C,YAAA,CAGqBiF,6BAAA;sBAHiC3F,GAAG,SAASoD,IAAI,CAACiC,EAAE;sBACtEjD,OAAK,WAALA,OAAKA,CAAAT,MAAA;wBAAA,OAAEpB,MAAA,CAAAqF,gBAAgB,CAACxC,IAAI,EAAEC,KAAK;sBAAA;;sBAlElDhD,OAAA,EAAAC,QAAA,CAmEc;wBAAA,OAAe,CAnE7BiF,gBAAA,CAAA7B,gBAAA,CAmEiBN,IAAI,CAAC3C,IAAI,iB;;sBAnE1BmD,CAAA;;;;gBAAAA,CAAA;yDAAAtC,mBAAA,gBAuEQV,mBAAA,CAQM,OARNiF,UAQM,GAPJ1F,YAAA,CAIc2F,sBAAA;gBA5ExBzF,OAAA,EAAAC,QAAA,CAyEY,UAAAyF,IAAA;kBAAA,IADqBC,SAAS,GAAAD,IAAA,CAATC,SAAS;kBAAA,S,cAC9BtF,YAAA,CAEauF,UAAA;oBAFAC,OAAO,EAAE3F,MAAA,CAAA4F;kBAAc,IACjB5F,MAAA,CAAA6F,MAAM,IAAI7F,MAAA,CAAA8F,SAAS,I,cAApC3F,YAAA,CAAyF4F,wBA1EvG,CA0EgFN,SAAS;oBAApChG,GAAG,EAAEuG,IAAA,CAAAC,MAAM,CAACC;wBA1EjEnF,mBAAA,e;;gBAAAsC,CAAA;qCA6EU3D,mBAAA,CACiBC,SAAA,QA9E3BiD,WAAA,CA6EyC5C,MAAA,CAAAmG,QAAQ,EA7EjD,UA6EiCtD,IAAI;sDAA3B1C,YAAA,CACiBH,MAAA;kBADyBP,GAAG,EAAEoD,IAAI;kBAA2C3C,IAAI,EAAE2C;+DAAtC7C,MAAA,CAAA6F,MAAM,IAAI7F,MAAA,CAAAoG,UAAU,KAAKvD,IAAI,E;;;YA7ErGQ,CAAA;wCAiFuDrD,MAAA,CAAAqG,aAAa,I,cAA9DlG,YAAA,CAOWsD,mBAAA;YAxFjBhE,GAAA;YAiFgBF,KAAK,EAAC;;YAjFtBO,OAAA,EAAAC,QAAA,CAkFQ;cAAA,OAKa,CALbH,YAAA,CAKa0G,WAAA;gBALDpG,IAAI,EAAC;cAAiB;gBAlF1CJ,OAAA,EAAAC,QAAA,CAwI6B;kBAAA,OAaN,CAjELC,MAAA,CAAAuG,cAAc,G,+BADtB7G,mBAAA,CAGM;oBAtFhBD,GAAA;oBAmFeF,KAAK,EAAC,8BAA8B;oBAAEgE,KAAK,EAnF1DC,eAAA;sBAAA,0BAmFwFxD,MAAA,CAAAwG;oBAAiB;sBAE7F5G,YAAA,CAAwDI,MAAA;oBArFpEiB,UAAA,EAqFmCjB,MAAA,CAAAyG,gBAAgB;oBArFnD,uBAAAtF,MAAA,QAAAA,MAAA,gBAAAC,MAAA;sBAAA,OAqFmCpB,MAAA,CAAAyG,gBAAgB,GAAArF,MAAA;oBAAA;uFADTpB,MAAA,CAAAyG,gBAAgB,E,IApF1D1F,mBAAA,e;;gBAAAsC,CAAA;;;YAAAA,CAAA;gBAAAtC,mBAAA,e;;QAAAsC,CAAA;oCA0FIzD,YAAA,CAEmB8G,2BAAA;QA5FvBzF,UAAA,EA0F+BjB,MAAA,CAAA2G,QAAQ;QA1FvC,uBAAAxF,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA0F+BpB,MAAA,CAAA2G,QAAQ,GAAAvF,MAAA;QAAA;QAAElB,IAAI,EAAC;;QA1F9CJ,OAAA,EAAAC,QAAA,CA2FM;UAAA,OAA6B,CAA7BH,YAAA,CAA6BI,MAAA,kB;;QA3FnCqD,CAAA;yCA6FIzD,YAAA,CAEmB8G,2BAAA;QA/FvBzF,UAAA,EA6F+BjB,MAAA,CAAA4G,gBAAgB;QA7F/C,uBAAAzF,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA6F+BpB,MAAA,CAAA4G,gBAAgB,GAAAxF,MAAA;QAAA;QAAElB,IAAI,EAAC;;QA7FtDJ,OAAA,EAAAC,QAAA,CA8FM;UAAA,OAAyF,CAAzFH,YAAA,CAAyFI,MAAA;YAA1E6G,IAAI,EAAE7G,MAAA,CAAA8G,kBAAkB;YAAGC,UAAQ,EAAE/G,MAAA,CAAAgH;;;QA9F1D3D,CAAA;yCAgG8CrD,MAAA,CAAAiH,sBAAsB,I,cAAhEvH,mBAAA,CAEM,OAFNwH,UAEM,GADJtH,YAAA,CAAyFI,MAAA;QAA1E6G,IAAI,EAAE7G,MAAA,CAAA8G,kBAAkB;QAAGC,UAAQ,EAAE/G,MAAA,CAAAgH;2DAjG1DjG,mBAAA,gBAmG8Bf,MAAA,CAAAmH,kBAAkB,I,cAA5ChH,YAAA,CAA4FH,MAAA;QAnGhGP,GAAA;QAmGmDsH,UAAQ,EAAE/G,MAAA,CAAAwB;iDAnG7DT,mBAAA,e;;IAAAsC,CAAA;MAqGEzD,YAAA,CAAuCI,MAAA,wBACbA,MAAA,CAAAoH,cAAc,I,cAAxCjH,YAAA,CAA+DH,MAAA;IAtGjEP,GAAA;EAAA,MAAAsB,mBAAA,gBAuGoFf,MAAA,CAAAqG,aAAa,I,cAA/FlG,YAAA,CAAmGH,MAAA;IAvGrGP,GAAA;IAAAwB,UAAA,EAuGiCjB,MAAA,CAAAyG,gBAAgB;IAvGjD,uBAAAtF,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAuGiCpB,MAAA,CAAAyG,gBAAgB,GAAArF,MAAA;IAAA;IAAGoB,QAAQ,EAAExC,MAAA,CAAAuG;yDAvG9DxF,mBAAA,gBAwG0Bf,MAAA,CAAAqG,aAAa,I,cAArClG,YAAA,CAAyCH,MAAA;IAxG3CP,GAAA;EAAA,MAAAsB,mBAAA,gBAyGoBf,MAAA,CAAA6F,MAAM,IAAI7F,MAAA,CAAAqH,aAAa,I,cAAzClH,YAAA,CAA6CH,MAAA;IAzG/CP,GAAA;EAAA,MAAAsB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}