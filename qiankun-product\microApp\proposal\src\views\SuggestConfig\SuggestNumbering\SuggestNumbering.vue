<template>
  <div class="SuggestNumbering">
    <xyl-search-button @queryClick="handleQuery"
                       @resetClick="handleReset"
                       @handleButton="handleButton"
                       :buttonList="buttonList">
      <template #search>
        <el-input v-model="keyword"
                  placeholder="请输入关键词"
                  @keyup.enter="handleQuery"
                  clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef"
                row-key="termYearId"
                :data="tableData"
                @select="handleTableSelect"
                @select-all="handleTableSelect">
        <el-table-column type="selection"
                         reserve-selection
                         width="60"
                         fixed />
        <el-table-column label="届次"
                         min-width="160"
                         prop="companyUser"
                         show-overflow-tooltip>
          <template #default="scope">{{ scope.row.termYear?.circlesType?.label }}{{ scope.row.termYear?.boutType?.label }}
          </template>
        </el-table-column>
        <el-table-column label="大会编号前缀"
                         min-width="160"
                         prop="meetingNumberPrefix"
                         show-overflow-tooltip />
        <el-table-column label="大会起始编号"
                         min-width="160"
                         prop="meetingStartNumber"
                         show-overflow-tooltip />
        <el-table-column label="平时编号前缀"
                         min-width="160"
                         prop="usualNumberPrefix"
                         show-overflow-tooltip />
        <el-table-column label="平时起始编号"
                         min-width="160"
                         prop="usualStartNumber"
                         show-overflow-tooltip />
        <el-table-column label="大会时间"
                         min-width="360"
                         prop="companyUser"
                         show-overflow-tooltip>
          <template #default="scope">
            {{ format(scope.row.meetingStartDate) }} - {{ format(scope.row.meetingEndDate) }}
          </template>
        </el-table-column>
        <xyl-global-table-button :data="tableButtonList"
                                 @buttonClick="handleCommand"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo"
                     v-model:page-size="pageSize"
                     :page-sizes="pageSizes"
                     layout="total, sizes, prev, pager, next, jumper"
                     @size-change="handleQuery"
                     @current-change="handleQuery"
                     :total="totals"
                     background />
    </div>
    <xyl-popup-window v-model="show"
                      :name="id ? '编辑' : '新增'">
      <SuggestNumberingSubmit :id="id"
                              @callback="callback"></SuggestNumberingSubmit>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestNumbering' }
</script>
<script setup>
import { ref, onActivated } from 'vue'
import { format } from 'common/js/time.js'
import { GlobalTable } from 'common/js/GlobalTable.js'
import SuggestNumberingSubmit from './component/SuggestNumberingSubmit'
const buttonList = [
  { id: 'new', name: '新增', type: 'primary', has: 'new' }
  // { id: 'del', name: '删除', type: '', has: 'del' }
]
const tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: 'edit' }]
const id = ref('')
const show = ref(false)
const {
  keyword,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  handleQuery,
  handleTableSelect,
  handleDel,
  tableRefReset
} = GlobalTable({ tableApi: 'suggestionTermYearList', delApi: 'suggestionTermYearDel', valId: 'termYearId' })

onActivated(() => { handleQuery() })

const handleButton = (id) => {
  switch (id) {
    case 'new':
      handleNew()
      break
    case 'del':
      handleDel('届次编号')
      break
    default:
      break
  }
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    default:
      break
  }
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleNew = () => {
  id.value = ''
  show.value = true
}
const handleEdit = (item) => {
  id.value = item.termYearId
  show.value = true
}
const callback = () => {
  tableRefReset()
  handleQuery()
  show.value = false
}
</script>
<style lang="scss">
.SuggestNumbering {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
