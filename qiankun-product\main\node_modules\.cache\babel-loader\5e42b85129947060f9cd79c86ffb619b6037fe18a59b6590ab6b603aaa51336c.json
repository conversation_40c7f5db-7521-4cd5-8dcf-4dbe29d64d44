{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createBlock as _createBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"xyl-menu-text\"\n};\nvar _hoisted_2 = {\n  class: \"xyl-menu-text\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_xyl_menu_item = _resolveComponent(\"xyl-menu-item\");\n  var _component_el_sub_menu = _resolveComponent(\"el-sub-menu\");\n  var _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  return _openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.menuData, function (item) {\n    return _openBlock(), _createElementBlock(_Fragment, null, [item.children.length ? (_openBlock(), _createBlock(_component_el_sub_menu, {\n      index: item.id,\n      key: item.id,\n      \"popper-class\": \"xyl-menu-popper\"\n    }, {\n      title: _withCtx(function () {\n        return [_createElementVNode(\"span\", _hoisted_1, _toDisplayString(item.name), 1 /* TEXT */)];\n      }),\n      default: _withCtx(function () {\n        return [_createVNode(_component_xyl_menu_item, {\n          menuData: item.children\n        }, null, 8 /* PROPS */, [\"menuData\"])];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"index\"])) : _createCommentVNode(\"v-if\", true), !item.children.length ? (_openBlock(), _createBlock(_component_el_menu_item, {\n      index: item.id,\n      key: item.id\n    }, {\n      title: _withCtx(function () {\n        return [_createElementVNode(\"span\", _hoisted_2, _toDisplayString(item.name), 1 /* TEXT */)];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"index\"])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n  }), 256 /* UNKEYED_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_Fragment", "_renderList", "$setup", "menuData", "item", "children", "length", "_createBlock", "_component_el_sub_menu", "index", "id", "key", "title", "_withCtx", "_createElementVNode", "_hoisted_1", "_toDisplayString", "name", "default", "_createVNode", "_component_xyl_menu_item", "_", "_createCommentVNode", "_component_el_menu_item", "_hoisted_2"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutView\\component\\LayoutMenu\\LayoutMenuItem.vue"], "sourcesContent": ["<template>\r\n  <template v-for=\"item in menuData\">\r\n    <el-sub-menu :index=\"item.id\" :key=\"item.id\" popper-class=\"xyl-menu-popper\" v-if=\"item.children.length\">\r\n      <template #title><span class=\"xyl-menu-text\">{{ item.name }}</span></template>\r\n      <xyl-menu-item :menuData=\"item.children\"></xyl-menu-item>\r\n    </el-sub-menu>\r\n    <el-menu-item :index=\"item.id\" :key=\"item.id\" v-if=\"!item.children.length\">\r\n      <template #title><span class=\"xyl-menu-text\">{{ item.name }}</span></template>\r\n    </el-menu-item>\r\n  </template>\r\n</template>\r\n<script>\r\nexport default { name: 'XylMenuItem' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch } from 'vue'\r\nconst props = defineProps({\r\n  menuData: { type: Array, default: () => [] }\r\n})\r\nconst menuData = ref(props.menuData)\r\nwatch(() => props.menuData, () => { menuData.value = props.menuData })\r\n</script>\r\n"], "mappings": ";;EAG6BA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAe;;;;;2BANhDC,mBAAA,CAQWC,SAAA,QATbC,WAAA,CAC2BC,MAAA,CAAAC,QAAQ,EADnC,UACmBC,IAAI;yBADvBL,mBAAA,CAAAC,SAAA,SAEsFI,IAAI,CAACC,QAAQ,CAACC,MAAM,I,cAAtGC,YAAA,CAGcC,sBAAA;MAHAC,KAAK,EAAEL,IAAI,CAACM,EAAE;MAAGC,GAAG,EAAEP,IAAI,CAACM,EAAE;MAAE,cAAY,EAAC;;MAC7CE,KAAK,EAAAC,QAAA,CAAC;QAAA,OAAkD,CAAlDC,mBAAA,CAAkD,QAAlDC,UAAkD,EAAAC,gBAAA,CAAnBZ,IAAI,CAACa,IAAI,iB;;MAH/DC,OAAA,EAAAL,QAAA,CAIM;QAAA,OAAyD,CAAzDM,YAAA,CAAyDC,wBAAA;UAAzCjB,QAAQ,EAAEC,IAAI,CAACC;;;MAJrCgB,CAAA;sDAAAC,mBAAA,gB,CAMyDlB,IAAI,CAACC,QAAQ,CAACC,MAAM,I,cAAzEC,YAAA,CAEegB,uBAAA;MAFAd,KAAK,EAAEL,IAAI,CAACM,EAAE;MAAGC,GAAG,EAAEP,IAAI,CAACM;;MAC7BE,KAAK,EAAAC,QAAA,CAAC;QAAA,OAAkD,CAAlDC,mBAAA,CAAkD,QAAlDU,UAAkD,EAAAR,gBAAA,CAAnBZ,IAAI,CAACa,IAAI,iB;;MAP/DI,CAAA;sDAAAC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}