{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ProposalClueDetailBody\"\n};\nvar _hoisted_2 = {\n  class: \"ProposalClueDetailTitle\"\n};\nvar _hoisted_3 = {\n  class: \"ProposalClueDetailInfo\"\n};\nvar _hoisted_4 = {\n  class: \"ProposalClueDetailInfoItem\"\n};\nvar _hoisted_5 = {\n  class: \"ProposalClueDetailInfoItem\"\n};\nvar _hoisted_6 = {\n  class: \"ProposalClueDetailInfo\"\n};\nvar _hoisted_7 = {\n  class: \"ProposalClueDetailInfoItem\"\n};\nvar _hoisted_8 = {\n  class: \"ProposalClueDetailInfoItem\"\n};\nvar _hoisted_9 = {\n  style: {\n    \"white-space\": \"pre-wrap\",\n    \"line-height\": \"30px\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    always: \"\",\n    class: \"SuggestClueDetails\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n        class: \"SubmitProposalClueName\"\n      }, \"提案线索详情\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, _toDisplayString($setup.details.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, \"提供者：\" + _toDisplayString($setup.details.furnishName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, \"提交时间：\" + _toDisplayString($setup.format($setup.details.createDate)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, \"线索类别：\" + _toDisplayString($setup.typeLabel), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, \"线索来源：\" + _toDisplayString($setup.details.terminalName), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_createElementVNode(\"pre\", _hoisted_9, _toDisplayString($setup.details.content), 1 /* TEXT */)])])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "style", "_createBlock", "_component_el_scrollbar", "always", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_toDisplayString", "$setup", "details", "title", "_hoisted_3", "_hoisted_4", "furnishName", "_hoisted_5", "format", "createDate", "_hoisted_6", "_hoisted_7", "typeLabel", "_hoisted_8", "terminalName", "_hoisted_9", "content", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueDetails.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"SuggestClueDetails\">\r\n    <div class=\"ProposalClueDetailBody\">\r\n      <div class=\"SubmitProposalClueName\">提案线索详情</div>\r\n      <div class=\"ProposalClueDetailTitle\">{{ details.title }}</div>\r\n      <div class=\"ProposalClueDetailInfo\">\r\n        <div class=\"ProposalClueDetailInfoItem\">提供者：{{ details.furnishName }}</div>\r\n        <div class=\"ProposalClueDetailInfoItem\">提交时间：{{ format(details.createDate) }}</div>\r\n      </div>\r\n      <div class=\"ProposalClueDetailInfo\">\r\n        <div class=\"ProposalClueDetailInfoItem\">线索类别：{{ typeLabel }}</div>\r\n        <div class=\"ProposalClueDetailInfoItem\">线索来源：{{ details.terminalName }}</div>\r\n      </div>\r\n      <div>\r\n        <pre style=\"white-space: pre-wrap;line-height: 30px;\">{{ details.content }}</pre>\r\n      </div>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestClueDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst details = ref({})\r\nconst typeLabel = ref('')\r\n\r\nonMounted(() => { if (props.id) { proposalClueInfo() } })\r\n\r\nconst proposalClueInfo = async () => {\r\n  const { data } = await api.proposalClueInfo({ detailId: props.id })\r\n  typeLabel.value = data.proposalClueType ? data.proposalClueType.label : ''\r\n  details.value = data\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestClueDetails {\r\n  min-width: 780px;\r\n  height: 100%;\r\n\r\n  .ProposalClueDetailBody {\r\n    max-width: 780px;\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n    // box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    padding: var(--zy-distance-one);\r\n\r\n\r\n    .SubmitProposalClueName {\r\n      padding: 20px 40px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-title-font-size);\r\n      line-height: var(--zy-line-height);\r\n      border-bottom: 3px solid var(--zy-el-color-primary);\r\n    }\r\n\r\n    .ProposalClueDetailTitle {\r\n      width: 100%;\r\n      padding: 10px 0;\r\n      font-weight: bold;\r\n      font-size: var(--zy-title-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .ProposalClueDetailInfo {\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .ProposalClueDetailInfoItem {\r\n        width: 50%;\r\n        padding: 10px 0;\r\n      }\r\n    }\r\n\r\n    .ProposalClueDetailInfoItem {\r\n      width: 100%;\r\n      padding: 5px 0;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n\r\n      span {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n\r\n    .ProposalClueDetailContent {\r\n      padding: 20px 0;\r\n      overflow: hidden;\r\n      line-height: 60px;\r\n      line-height: var(--zy-line-height);\r\n\r\n      img,\r\n      video {\r\n        max-width: 100%;\r\n        height: auto !important;\r\n      }\r\n\r\n      table {\r\n        max-width: 100%;\r\n        border-collapse: collapse;\r\n        border-spacing: 0;\r\n\r\n        tr {\r\n          page-break-inside: avoid;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAyB;;EAC/BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAClCA,KAAK,EAAC;AAA4B;;EAEpCA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAClCA,KAAK,EAAC;AAA4B;;EAGlCC,KAAgD,EAAhD;IAAA;IAAA;EAAA;AAAgD;;;uBAb3DC,YAAA,CAgBeC,uBAAA;IAhBDC,MAAM,EAAN,EAAM;IAACJ,KAAK,EAAC;;IAD7BK,OAAA,EAAAC,QAAA,CAEI;MAAA,OAcM,CAdNC,mBAAA,CAcM,OAdNC,UAcM,G,0BAbJD,mBAAA,CAAgD;QAA3CP,KAAK,EAAC;MAAwB,GAAC,QAAM,sBAC1CO,mBAAA,CAA8D,OAA9DE,UAA8D,EAAAC,gBAAA,CAAtBC,MAAA,CAAAC,OAAO,CAACC,KAAK,kBACrDN,mBAAA,CAGM,OAHNO,UAGM,GAFJP,mBAAA,CAA2E,OAA3EQ,UAA2E,EAAnC,MAAI,GAAAL,gBAAA,CAAGC,MAAA,CAAAC,OAAO,CAACI,WAAW,kBAClET,mBAAA,CAAmF,OAAnFU,UAAmF,EAA3C,OAAK,GAAAP,gBAAA,CAAGC,MAAA,CAAAO,MAAM,CAACP,MAAA,CAAAC,OAAO,CAACO,UAAU,kB,GAE3EZ,mBAAA,CAGM,OAHNa,UAGM,GAFJb,mBAAA,CAAkE,OAAlEc,UAAkE,EAA1B,OAAK,GAAAX,gBAAA,CAAGC,MAAA,CAAAW,SAAS,kBACzDf,mBAAA,CAA6E,OAA7EgB,UAA6E,EAArC,OAAK,GAAAb,gBAAA,CAAGC,MAAA,CAAAC,OAAO,CAACY,YAAY,iB,GAEtEjB,mBAAA,CAEM,cADJA,mBAAA,CAAiF,OAAjFkB,UAAiF,EAAAf,gBAAA,CAAxBC,MAAA,CAAAC,OAAO,CAACc,OAAO,iB;;IAdhFC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}