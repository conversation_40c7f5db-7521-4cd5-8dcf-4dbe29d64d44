{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport SubmitCollectiveProposalUnit from './component/SubmitCollectiveProposalUnit';\nvar __default__ = {\n  name: 'CollectiveProposalUnit'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [{\n      id: 'new',\n      name: '新增',\n      type: 'primary',\n      has: 'new'\n    },\n    // { id: 'import', name: 'Excel导入', type: 'primary', has: 'import' },\n    // { id: 'export', name: '导出Excel', type: 'primary', has: 'export' },\n    {\n      id: 'del',\n      name: '删除',\n      type: '',\n      has: 'del'\n    }];\n    var tableButtonList = [{\n      id: 'edit',\n      name: '编辑',\n      width: 100,\n      has: 'edit'\n    }];\n    var id = ref('');\n    var show = ref(false);\n    var importShow = ref(false);\n    var _GlobalTable = GlobalTable({\n        tableApi: 'teamOfficeList',\n        delApi: 'teamOfficeDel'\n      }),\n      keyword = _GlobalTable.keyword,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      exportId = _GlobalTable.exportId,\n      exportParams = _GlobalTable.exportParams,\n      exportShow = _GlobalTable.exportShow,\n      handleQuery = _GlobalTable.handleQuery,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      handleDel = _GlobalTable.handleDel,\n      tableRefReset = _GlobalTable.tableRefReset,\n      handleExportExcel = _GlobalTable.handleExportExcel;\n    onActivated(function () {\n      handleQuery();\n    });\n    var handleButton = function handleButton(id) {\n      switch (id) {\n        case 'new':\n          handleNew();\n          break;\n        case 'import':\n          importShow.value = true;\n          break;\n        case 'export':\n          handleExportExcel();\n          break;\n        case 'del':\n          handleDel('单位');\n          break;\n        default:\n          break;\n      }\n    };\n    var handleCommand = function handleCommand(row, isType) {\n      switch (isType) {\n        case 'edit':\n          handleEdit(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleNew = function handleNew() {\n      id.value = '';\n      show.value = true;\n    };\n    var handleEdit = function handleEdit(item) {\n      id.value = item.id;\n      show.value = true;\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      show.value = false;\n      exportShow.value = false;\n      importShow.value = false;\n    };\n    var __returned__ = {\n      buttonList,\n      tableButtonList,\n      id,\n      show,\n      importShow,\n      keyword,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      exportId,\n      exportParams,\n      exportShow,\n      handleQuery,\n      handleTableSelect,\n      handleDel,\n      tableRefReset,\n      handleExportExcel,\n      handleButton,\n      handleCommand,\n      handleReset,\n      handleNew,\n      handleEdit,\n      callback,\n      ref,\n      onActivated,\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get SubmitCollectiveProposalUnit() {\n        return SubmitCollectiveProposalUnit;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "GlobalTable", "SubmitCollectiveProposalUnit", "__default__", "name", "buttonList", "id", "type", "has", "tableButtonList", "width", "show", "importShow", "_GlobalTable", "tableApi", "del<PERSON><PERSON>", "keyword", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "exportId", "exportParams", "exportShow", "handleQuery", "handleTableSelect", "handleDel", "tableRefReset", "handleExportExcel", "handleButton", "handleNew", "value", "handleCommand", "row", "isType", "handleEdit", "handleReset", "item", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestConfig/CollectiveProposalUnit/CollectiveProposalUnit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CollectiveProposalUnit\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column label=\"序号\"\r\n                         width=\"80\"\r\n                         prop=\"sort\" />\r\n        <el-table-column label=\"单位名称\"\r\n                         min-width=\"220\"\r\n                         prop=\"name\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"类型\"\r\n                         min-width=\"220\"\r\n                         prop=\"teamOfficeTheme\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            <span>\r\n              {{ scope.row.teamOfficeTheme.name }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"联系人\"\r\n                         min-width=\"160\"\r\n                         prop=\"contactUser\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"联系电话\"\r\n                         min-width=\"160\"\r\n                         prop=\"contactPhone\"\r\n                         show-overflow-tooltip />\r\n        <xyl-global-table-button :data=\"tableButtonList\"\r\n                                 @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\"\r\n                      name=\"导出Excel\">\r\n      <xyl-export-excel name=\"办理单位管理\"\r\n                        :exportId=\"exportId\"\r\n                        :params=\"exportParams\"\r\n                        module=\"suggestionOfficeExportExcel\"\r\n                        @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"importShow\"\r\n                      name=\"Excel导入\">\r\n      <xyl-import-excel name=\"办理单位管理\"\r\n                        type=\"proposalOfficeImportExcel\"\r\n                        @callback=\"callback\"></xyl-import-excel>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      :name=\"id ? '编辑单位' : '新增单位'\">\r\n      <SubmitCollectiveProposalUnit :id=\"id\"\r\n                                    @callback=\"callback\"></SubmitCollectiveProposalUnit>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'CollectiveProposalUnit' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SubmitCollectiveProposalUnit from './component/SubmitCollectiveProposalUnit'\r\nconst buttonList = [\r\n  { id: 'new', name: '新增', type: 'primary', has: 'new' },\r\n  // { id: 'import', name: 'Excel导入', type: 'primary', has: 'import' },\r\n  // { id: 'export', name: '导出Excel', type: 'primary', has: 'export' },\r\n  { id: 'del', name: '删除', type: '', has: 'del' }\r\n]\r\nconst tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: 'edit' }]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst importShow = ref(false)\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset,\r\n  handleExportExcel\r\n} = GlobalTable({ tableApi: 'teamOfficeList', delApi: 'teamOfficeDel' })\r\n\r\nonActivated(() => { handleQuery() })\r\n\r\nconst handleButton = (id) => {\r\n  switch (id) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'import':\r\n      importShow.value = true\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    case 'del':\r\n      handleDel('单位')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleNew = () => {\r\n  id.value = ''\r\n  show.value = true\r\n}\r\nconst handleEdit = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n  exportShow.value = false\r\n  importShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.CollectiveProposalUnit {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(\r\n      100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px)\r\n    );\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAuFA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,4BAA4B,MAAM,0CAA0C;AALnF,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAyB,CAAC;;;;;IAMjD,IAAMC,UAAU,GAAG,CACjB;MAAEC,EAAE,EAAE,KAAK;MAAEF,IAAI,EAAE,IAAI;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAM,CAAC;IACtD;IACA;IACA;MAAEF,EAAE,EAAE,KAAK;MAAEF,IAAI,EAAE,IAAI;MAAEG,IAAI,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAM,CAAC,CAChD;IACD,IAAMC,eAAe,GAAG,CAAC;MAAEH,EAAE,EAAE,MAAM;MAAEF,IAAI,EAAE,IAAI;MAAEM,KAAK,EAAE,GAAG;MAAEF,GAAG,EAAE;IAAO,CAAC,CAAC;IAC7E,IAAMF,EAAE,GAAGP,GAAG,CAAC,EAAE,CAAC;IAClB,IAAMY,IAAI,GAAGZ,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMa,UAAU,GAAGb,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAAc,YAAA,GAgBIZ,WAAW,CAAC;QAAEa,QAAQ,EAAE,gBAAgB;QAAEC,MAAM,EAAE;MAAgB,CAAC,CAAC;MAftEC,OAAO,GAAAH,YAAA,CAAPG,OAAO;MACPC,QAAQ,GAAAJ,YAAA,CAARI,QAAQ;MACRC,MAAM,GAAAL,YAAA,CAANK,MAAM;MACNC,MAAM,GAAAN,YAAA,CAANM,MAAM;MACNC,QAAQ,GAAAP,YAAA,CAARO,QAAQ;MACRC,SAAS,GAAAR,YAAA,CAATQ,SAAS;MACTC,SAAS,GAAAT,YAAA,CAATS,SAAS;MACTC,QAAQ,GAAAV,YAAA,CAARU,QAAQ;MACRC,YAAY,GAAAX,YAAA,CAAZW,YAAY;MACZC,UAAU,GAAAZ,YAAA,CAAVY,UAAU;MACVC,WAAW,GAAAb,YAAA,CAAXa,WAAW;MACXC,iBAAiB,GAAAd,YAAA,CAAjBc,iBAAiB;MACjBC,SAAS,GAAAf,YAAA,CAATe,SAAS;MACTC,aAAa,GAAAhB,YAAA,CAAbgB,aAAa;MACbC,iBAAiB,GAAAjB,YAAA,CAAjBiB,iBAAiB;IAGnB9B,WAAW,CAAC,YAAM;MAAE0B,WAAW,CAAC,CAAC;IAAC,CAAC,CAAC;IAEpC,IAAMK,YAAY,GAAG,SAAfA,YAAYA,CAAIzB,EAAE,EAAK;MAC3B,QAAQA,EAAE;QACR,KAAK,KAAK;UACR0B,SAAS,CAAC,CAAC;UACX;QACF,KAAK,QAAQ;UACXpB,UAAU,CAACqB,KAAK,GAAG,IAAI;UACvB;QACF,KAAK,QAAQ;UACXH,iBAAiB,CAAC,CAAC;UACnB;QACF,KAAK,KAAK;UACRF,SAAS,CAAC,IAAI,CAAC;UACf;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMM,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAEC,MAAM,EAAK;MACrC,QAAQA,MAAM;QACZ,KAAK,MAAM;UACTC,UAAU,CAACF,GAAG,CAAC;UACf;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBtB,OAAO,CAACiB,KAAK,GAAG,EAAE;MAClBP,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMM,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB1B,EAAE,CAAC2B,KAAK,GAAG,EAAE;MACbtB,IAAI,CAACsB,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CAAIE,IAAI,EAAK;MAC3BjC,EAAE,CAAC2B,KAAK,GAAGM,IAAI,CAACjC,EAAE;MAClBK,IAAI,CAACsB,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMO,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBX,aAAa,CAAC,CAAC;MACfH,WAAW,CAAC,CAAC;MACbf,IAAI,CAACsB,KAAK,GAAG,KAAK;MAClBR,UAAU,CAACQ,KAAK,GAAG,KAAK;MACxBrB,UAAU,CAACqB,KAAK,GAAG,KAAK;IAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}