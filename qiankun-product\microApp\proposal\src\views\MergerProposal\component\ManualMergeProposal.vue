<template>
  <div class="ManualMergeProposal">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" :buttonNumber="2" ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #mainHandleOffices="scope">
            <template v-if="scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0">
              {{ scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}
            </template>
            <template v-else>
              {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}
            </template>
          </template>
          <template #assistHandleOffices="scope">
            <template v-if="scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0">
              {{ scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}
            </template>
            <template v-else>
              {{ scope.row.assistHandleVoList?.map(v => v.flowHandleOfficeName).join('、') }}
            </template>
          </template>
          <!-- <template #publishHandleOffices="scope">
            {{ scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、') }}
          </template> -->
        </xyl-global-table>
        <xyl-global-table-button :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="mergeShow" name="合并提案">
      <SubmitMergerProposal :isSimilar="false" :mergeData="mergeData" @callback="callback"></SubmitMergerProposal>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'ManualMergeProposal' }
</script>
<script setup>
import { ref, onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage } from 'element-plus'
import SubmitMergerProposal from './SubmitMergerProposal'
const buttonList = [{ id: 'merge', name: '合并提案', type: 'primary', has: '' }]
const mergeData = ref([])
const mergeShow = ref(false)
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  handleQuery,
  tableDataArray,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  handleEditorCustom,
  tableRefReset
} = GlobalTable({ tableId: 'id_prop_proposal_prepare_merge_proposal', tableApi: 'suggestionList' })

onActivated(() => { handleQuery() })
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType, params) => {
  switch (isType) {
    case 'merge':
      handleMerge()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } } })
}
const handleMerge = () => {
  if (tableDataArray.value.length <= 1) return ElMessage({ type: 'warning', message: '请至少选择两条提案并案！' })
  mergeData.value = tableDataArray.value
  mergeShow.value = true
}
const callback = (type) => {
  if (type) { tableRefReset() }
  handleQuery()
  mergeShow.value = false
}
</script>
<style lang="scss">
.ManualMergeProposal {
  width: 100%;
  height: 100%;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
