<template>
  <div class="WorkBenchCopy">
    <div class="background-layer" :class="{ 'blur-bg': activeMenuIndex !== null }"
      :style="{ backgroundImage: `url(${backgroundImage})` }"></div>
    <div class="content-layer" @click="handleContentClick">
      <div class="WorkBenchHeader">
        <div class="WorkBenchHeaderLogoName">
          <el-image class="WorkBenchHeaderLogo" :src="systemLogo" fit="contain" />
          <div class="WorkBenchHeaderName" v-html="systemName"></div>
        </div>
        <div class="WorkBenchHeaderRight">
          <!-- <span class="WorkBenchHeaderLink">操作指南</span> -->
          <xyl-region v-model="regionId" :data="area" @select="regionSelect"
            :props="{ label: 'name', children: 'children' }"></xyl-region>
          <button class="old_version" @click="oldVersion">
            <img src="../img/login_btn_bg.png" alt="" />
            <span>旧版本</span>
          </button>
          <span class="WorkBenchHeaderLink" @click="sysManagement" v-if="isSys">系统管理</span>
          <div class="WorkBenchHeaderUser" @click="sysUser">
            <img class="WorkBenchHeaderAvatar" :src="user.image" alt="头像" />
            <span class="WorkBenchHeaderName">{{ user.userName }}</span>
          </div>
          <span class="WorkBenchHeaderLogout WorkBenchHeaderLink" @click="handleExit">退出</span>
        </div>
      </div>
      <div class="WorkBenchContent">
        <div class="menu-container">
          <div class="menu-list" :class="{ 'has-active': activeMenuIndex !== null }">
            <div v-for="(item, index) in menuItems" :key="index" class="menu-item" :class="[
              { active: activeMenuIndex === index },
              index === 0 ? 'menu-item-first' : '',
              index === 1 ? 'menu-item-second' : '',
              index === 2 ? 'menu-item-third' : '',
              index === 3 ? 'menu-item-fourth' : '',
              index === 0 || index === 3 ? 'u-top' : 'u-bottom'
            ]" :style="{
              backgroundImage: `url(${activeMenuIndex === index ? item.bgActive : item.bg})`
            }" @click.stop="handleMenuClick(index)">
              <div class="menu-title">{{ item.title }}</div>
            </div>
          </div>
          <transition name="fade-slide">
            <div v-if="activeMenuIndex !== null" class="module-area">
              <div class="module-bg"></div>
              <div class="module-list">
                <div v-for="(mod, idx) in subMenus[activeMenuIndex] || []" :key="mod.id" class="module-item"
                  @click="handleWorkBench(mod)">
                  <img class="module-icon" :src="mod.icon" />
                  <div class="module-title">{{ mod.name }}</div>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>

    <!-- 图片弹窗 -->
    <div v-if="showImageDialog" class="image-dialog-overlay" @click="closeImageDialog">
      <div class="image-dialog-content" @click.stop>
        <div class="image-dialog-close" @click="closeImageDialog">
          <el-icon>
            <Close />
          </el-icon>
        </div>
        <el-image :src="dialogImageUrl" fit="contain" class="dialog-image" @click="handleImageClick" />
      </div>
    </div>
    <!-- 直播弹窗 -->
    <LiveBroadcastDetails v-model="detailsShow" :id="detailsId" @callback="callback" />
  </div>
</template>
<script>
export default { name: 'WorkBenchCopy' }
</script>
<script setup>
import api from '@/api'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ref, inject, onMounted, computed, nextTick } from 'vue'
import { user, systemLogo, systemName } from 'common/js/system_var.js'
import config from 'common/config'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { globalReadOpenConfig } from 'common/js/GlobalMethod'
import LiveBroadcastDetails from "../../microApp/interaction/src/views/LiveManagement/LiveBroadcastDetails.vue"
const router = useRouter()
const openPage = inject('openPage')
const leftMenuData = inject('leftMenuData')
const menuListData = inject('WorkBenchList')
const regionId = inject('regionId')
const regionSelect = inject('regionSelect')
const area = inject('area')
// const menuListData = computed(() => filterMenu(store.getters.getMenuFn || []))
const store = useStore()
const menuIcon = `${config.API_URL}/pageImg/open/menuIcon`
const backgroundImage = ref(`${config.API_URL}/pageImg/open/homePage?areaId=${user.value.areaId}`)
const originMenuItems = [
  {
    title: '',
    bg: require('../img/menu_wdgz.png'),
    bgActive: require('../img/menu_wdgz_s.png')
  },
  {
    title: '',
    bg: require('../img/menu_zhyy.png'),
    bgActive: require('../img/menu_zhyy_s.png')
  },
  {
    title: '',
    bg: require('../img/menu_wddb.png'),
    bgActive: require('../img/menu_wddb_s.png')
  },
  {
    title: '',
    bg: require('../img/menu_qtyy.png'),
    bgActive: require('../img/menu_qtyy_s.png')
  }
]
const activeMenuIndex = ref(null)
const isSys = ref(false)

// 弹窗相关变量
const showImageDialog = ref(false)
const dialogImageUrl = ref('')
const dialogData = ref(null)
const detailsShow = ref(false)
const detailsId = ref('')
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      const roleList = JSON.parse(sessionStorage.getItem('role'))
      console.log('当前角色===>', roleList)
      if (roleList) { isSys.value = roleList?.includes('管理员') }
      getLiveDialog()
    }, 1000);
  })
})
// 检测是否存在直播弹窗
const getLiveDialog = async () => {
  const res = await api.globalJson('/popUp/list', {
    pageNo: 1,
    pageSize: 10,
    from: 'pc',
    // query: { type: 1 },
    keyword: ''
  })
  if (res.code === 200 && res.data && res.data.length > 0) {
    dialogData.value = res.data[0]
    dialogImageUrl.value = api.fileURL(res.data[0].pcImg)
    showImageDialog.value = true
  }
}
// 关闭图片弹窗
const closeImageDialog = () => {
  showImageDialog.value = false
  dialogImageUrl.value = ''
  dialogData.value = null
}
// 处理图片点击事件（如果需要跳转）
const handleImageClick = () => {
  if (dialogData.value && dialogData.value.pcUrl) {
    detailsId.value = dialogData.value.pcUrl
    detailsShow.value = true
    // window.open(dialogData.value.pcUrl, '_blank')
  }
}
const menuItems = computed(() => {
  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')
  return filtered.map((item, idx) => ({
    ...originMenuItems[idx],
    title: item.name,
    id: item.id,
    has: item.permissions
  }))
})
const subMenus = computed(() => {
  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')
  var arr = filtered.map(item => (item.children || []).map(child => ({
    id: child.id,
    name: child.name,
    icon: child.icon,
    children: child.children,
    routePath: child.routePath,
    menuFunction: child.menuFunction,
    menuRouteType: child.menuRouteType,
    has: child.has || child.permissions
  }))
  )
  const result = arr.map(subArray =>
    subArray.filter(item =>
      item.name !== '系统运维' && item.name !== '我的'
    )
  ).filter(subArray => subArray.length > 0);
  return result
})
const handleWorkBench = (item) => { leftMenuData(item) }
const filterMenu = menuList => {
  let newMenuList = []
  for (let i = 0, len = menuList.length; i < len; i++) {
    newMenuList.push({
      id: menuList[i].menuId, name: menuList[i].name,
      routePath: menuList[i].routePath,
      menuFunction: menuList[i].menuFunction,
      menuRouteType: menuList[i].menuRouteType,
      icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,
      has: menuList[i].permissions,
      children: filterMenu(menuList[i].children || [])
    })
  }
  return newMenuList
}
// 切换旧版本
const oldVersion = () => {
  console.log('切换到旧版本')
  openPage({ key: 'routePath', value: '/homePage' })
}
// 退出
const handleExit = () => {
  ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => { loginOut('已安全退出！') }).catch(() => { ElMessage({ type: 'info', message: '已取消退出' }) })
}
const loginOut = async (text) => {
  const { code } = await api.loginOut()
  if (code === 200) {
    sessionStorage.clear()
    const goal_login_router_path = localStorage.getItem('goal_login_router_path')
    if (goal_login_router_path) {
      const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''
      router.push({ path: goal_login_router_path, query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {} })
    } else {
      router.push({ path: '/LoginView' })
    }
    store.commit('setState')
    globalReadOpenConfig()
    ElMessage({ message: text, showClose: true, type: 'success' })
  }
}
// 系统管理
const sysManagement = () => {
  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')
  const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '系统运维')
  leftMenuData(systemOperation)
}
// 跳转到我的
const sysUser = () => {
  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')
  const myOperation = filtered.flatMap(item => item.children).find(child => child.name === '我的')
  leftMenuData(myOperation)
}
// 点击主菜单展开子级
const handleMenuClick = (index) => {
  // if (index === 3) {
  //   const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')
  //   const data = filtered.find(item => item.name === '数据中心')
  //   leftMenuData(data)
  //   return
  // }
  if (!subMenus.value[index]?.length) {
    ElMessage.info('该菜单下暂无子菜单');
    return;
  }
  activeMenuIndex.value = activeMenuIndex.value === index ? null : index
}
const handleContentClick = (event) => {
  // 如果点击的是菜单项或其子元素，不处理
  if (event.target.closest('.menu-item') || event.target.closest('.module-area')) {
    return;
  }
  // 点击空白区域时收起菜单
  activeMenuIndex.value = null;
}
</script>
<style lang="scss">
.WorkBenchCopy {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .background-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: no-repeat;
    background-size: 100% 100%;
    transition: filter 0.3s ease;
    z-index: 1;

    &.blur-bg {
      filter: blur(5px);
      -webkit-filter: blur(5px);
    }
  }

  .content-layer {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;
  }

  .WorkBenchHeader {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 40px 0 40px;
    box-sizing: border-box;
    position: relative;
    z-index: 2;
    flex-shrink: 0;

    .WorkBenchHeaderLogoName {
      display: flex;
      align-items: center;

      .WorkBenchHeaderLogo {
        height: 74px;
        width: 74px;
      }

      .WorkBenchHeaderName {
        font-size: 37px;
        color: #fff;
        font-weight: bold;
        margin-left: 15px;
        letter-spacing: 5px;
      }
    }

    .WorkBenchHeaderRight {
      display: flex;
      align-items: center;
      gap: 20px;

      .old_version {
        background: none;
        border: none;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0;
        cursor: pointer;

        img {
          height: 39px;
        }

        span {
          position: absolute;
          left: 0;
          width: 100%;
          text-align: center;
          color: rgb(0, 51, 152);
          font-size: 14px;
          line-height: 39px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .WorkBenchHeaderLink {
        color: #fff;
        font-size: 16px;
        cursor: pointer;
        margin-right: 8px;

        &:hover {
          text-decoration: underline;
        }
      }

      .WorkBenchHeaderUser {
        display: flex;
        align-items: center;

        .WorkBenchHeaderAvatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin: 0 8px;
          object-fit: cover;
          border: 2px solid #fff;
          background: #eee;
        }

        .WorkBenchHeaderName {
          color: #fff;
          font-size: 16px;
          margin-right: 8px;
          margin-left: 4px;
        }
      }


      .WorkBenchHeaderLogout {
        color: #fff;
        font-size: 16px;
        cursor: pointer;
        margin-left: 8px;

        &:hover {
          color: #ff4d4f;
        }
      }
    }
  }

  .WorkBenchContent {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 8vh;
    position: relative;
    z-index: 2;
    height: calc(100% - 98px);
    overflow: hidden;

    .menu-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow: hidden;
      max-height: 100%;
    }

    .menu-list {
      width: calc(100% - 240px);
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      position: relative;
      margin-top: 30vh;
      transition: margin-top 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      &.has-active {
        margin-top: 0;

        .menu-item {
          max-width: 310px;
          position: relative;

          &::before {
            content: '';
            display: block;
            padding-top: 90.3%;
            /* 282/310 ≈ 0.903 */
          }
        }

        .u-top {
          margin-top: 0;
        }

        .u-bottom {
          margin-top: 25px;
        }

        .menu-item-first {
          margin-right: -40px;

          .menu-title {
            margin-top: 8%;
            margin-left: 18px;
          }
        }

        .menu-item-second {
          margin-right: -55px;
          position: relative;

          &::before {
            content: '';
            display: block;
            padding-top: 84.5%;
            /* 262/310 ≈ 0.845 */
          }

          .menu-title {
            margin-top: 5%;
          }
        }

        .menu-item-third {
          margin-right: -40px;
          position: relative;

          &::before {
            content: '';
            display: block;
            padding-top: 84.5%;
            /* 262/310 ≈ 0.845 */
          }

          .menu-title {
            margin-top: 5%;
          }
        }
      }
    }

    .menu-item {
      flex: 1 1 0;
      max-width: 475px;
      min-width: 220px;
      width: 100%;
      position: relative;
      border-radius: 24px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
      transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s, max-width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      margin: 0 8px;
      overflow: hidden;
    }

    .menu-item::before {
      content: '';
      display: block;
      padding-top: 94.7%;
    }

    .menu-item>.menu-title,
    .menu-item>.menu-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;

    }

    .u-top {
      margin-top: 0;
    }

    .u-bottom {
      margin-top: 44px;
    }

    .menu-item-first {
      margin-right: -40px;

      .menu-title {
        color: #fff;
        font-size: 20px;
        font-weight: bold;
        margin-top: 8%;
        margin-left: 18px;
      }
    }

    .menu-item-second {
      margin-right: -55px;
      position: relative;

      &::before {
        content: '';
        display: block;
        padding-top: 84.5%;
        /* 262/310 ≈ 0.845 */
      }

      .menu-title {
        color: #fff;
        font-size: 20px;
        font-weight: bold;
        margin-top: 4%;
      }
    }

    .menu-item-third {
      margin-right: -40px;
      position: relative;

      &::before {
        content: '';
        display: block;
        padding-top: 84.5%;
      }

      .menu-title {
        color: #fff;
        font-size: 20px;
        font-weight: bold;
        margin-top: 4%;
      }
    }

    .menu-item-fourth {
      .menu-title {
        color: #fff;
        font-size: 20px;
        font-weight: bold;
        margin-top: 8%;
        left: 42%;
      }
    }

    .module-area {
      width: 90%;
      max-width: 1248px;
      margin: 0 auto 0;
      position: relative;
      top: -70px;
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow: hidden;
      min-height: 500px;
      opacity: 0;
      animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      height: auto;

      @keyframes fadeIn {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
      }

      .module-bg {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: -1;
        left: 0;
        // top: -20px;
        top: -30px;
        background: url('../img/module_bg.png') no-repeat center top;
        background-size: 100% auto;
        border-radius: 24px;
        pointer-events: none;
      }

      .module-list {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 20px;
        width: 100%;
        // padding: 70px 40px 40px 40px;
        padding: 45px 40px 40px 40px;
        position: relative;
        z-index: 1;

        @media (max-width: 1400px) {
          grid-template-columns: repeat(4, 1fr);
        }

        @media (max-width: 1100px) {
          grid-template-columns: repeat(3, 1fr);
        }
      }

      .module-item {
        min-width: 180px;
        background: url('../img/module_item_bg.png') no-repeat center/100% 100%;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 15px 20px 0;
        height: 82px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        cursor: pointer;

        .module-icon {
          width: 32px;
          height: 32px;
        }

        .module-title {
          color: #fff;
          font-size: 16px;
          font-weight: bold;
          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
          margin-left: 15px;
        }
      }
    }
  }

  .fade-slide-enter-active,
  .fade-slide-leave-active {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .fade-slide-enter-from,
  .fade-slide-leave-to {
    opacity: 0;
    transform: translateY(40px);
  }

  // 图片弹窗样式
  .image-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
  }

  .image-dialog-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-dialog-close {
    position: absolute;
    top: -40px;
    right: -40px;
    width: 32px;
    height: 32px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 10000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    &:hover {
      background-color: #fff;
      transform: scale(1.1);
    }

    .el-icon {
      font-size: 18px;
      color: #666;
    }
  }

  .dialog-image {
    width: 1200px;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }
}
</style>
