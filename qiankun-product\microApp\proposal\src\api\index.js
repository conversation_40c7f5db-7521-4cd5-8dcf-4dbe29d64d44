// 导入封装的方法
import HTTP from 'common/http'
import GlobalApi from 'common/http/GlobalApi'
const api = {
  ...GlobalApi,
  userBatch (params) {
    // 用户批量操作
    return HTTP.json('/user/batch', params)
  },
  cppccMemberInfo (params) {
    // 获取委员信息详情
    return HTTP.json('/cppccMember/info', params)
  },
  teamOfficeSelect (params) {
    // 获取集体提案单位下拉选
    return HTTP.json('/teamOffice/selector', params)
  },
  tableHeadInfo (params) {
    // 自定义表头 列详情
    return HTTP.get(`/customColumn/info/${params}`)
  },
  teamOfficeList (params) {
    // 集体办理单位列表
    return HTTP.json('/teamOffice/list', params)
  },
  teamOfficeInfo (params) {
    // 集体办理单位详情
    return HTTP.json('/teamOffice/info', params)
  },
  teamOfficeDel (params) {
    // 集体办理单位删除
    return HTTP.json('/teamOffice/del', params)
  },
  teamProposalUserList (params) {
    // 集体提案单位用户列表
    return HTTP.json('/teamProposalUser/list', params)
  },
  teamProposalUserInfo (params) {
    // 集体提案单位用户详情
    return HTTP.json('/teamProposalUser/info', params)
  },
  teamProposalUserDel (params) {
    // 集体提案单位用户删除
    return HTTP.json('/teamProposalUser/del', params)
  },
  suggestionOfficeList (params) {
    // 办理单位列表
    return HTTP.json('/proposalOffice/list', params)
  },
  suggestionOfficeInfo (params) {
    // 办理单位详情
    return HTTP.json('/proposalOffice/info', params)
  },
  suggestionOfficeDel (params) {
    // 办理单位删除
    return HTTP.json('/proposalOffice/del', params)
  },
  suggestionOfficeUsingOrStop (params) {
    // 办理单位启用禁用
    return HTTP.json('/proposalOffice/usingOrStop', params)
  },
  suggestionOfficeSelect (params) {
    return HTTP.json('/proposalOffice/select', params)
  },
  suggestUnitUserList (params) {
    // 办理单位用户列表
    return HTTP.json('/proposalOfficeUser/list', params)
  },
  suggestUnitUserInfo (params) {
    // 办理单位用户详情
    return HTTP.json('/proposalOfficeUser/info', params)
  },
  suggestUnitUserDel (params) {
    // 办理单位用户删除
    return HTTP.json('/proposalOfficeUser/dels', params)
  },
  suggestionTermYearList (params) {
    // 届次编号列表
    return HTTP.json('/proposalTermYear/list', params)
  },
  suggestionTermYearInfo (params) {
    // 届次编号详情
    return HTTP.json('/proposalTermYear/info', params)
  },
  suggestionTermYearDel (params) {
    // 届次编号删除
    return HTTP.json('/proposalTermYear/dels', params)
  },
  suggestionThemeSelect (params) {
    // 提案分类列表
    return HTTP.json('/proposalTheme/select', params)
  },
  suggestionThemeList (params) {
    // 提案分类列表
    return HTTP.json('/proposalTheme/tree', params)
  },
  suggestionThemeInfo (params) {
    // 提案分类详情
    return HTTP.json('/proposalTheme/info', params)
  },
  suggestionThemeDel (params) {
    // 提案分类删除
    return HTTP.json('/proposalTheme/dels', params)
  },
  suggestionList (params) {
    // 提案列表
    return HTTP.json('/proposal/list', params)
  },
  suggestionInfo (params) {
    // 提案详情
    return HTTP.json('/proposal/info', params)
  },
  suggestionDetails (params) {
    // 提案流程详情
    return HTTP.json('/proposal/getSuggestionFlow', params)
  },
  suggestionDel (params) {
    // 提案删除
    return HTTP.json('/proposal/dels', params)
  },
  suggestionWord (params) {
    // 提案导出
    return HTTP.json('/proposal/loadDocData', params)
  },
  suggestionStatistics (params) {
    // 提案统计
    return HTTP.json('/proposalStatistics/composite', params)
  },
  changeSerialNumber (params) {
    // 提案编号对调
    return HTTP.json('/proposal/changeSerialNumber', params)
  },
  joinSubmiterList (params) {
    // 提案联名人列表
    return HTTP.json('/cppcc/joinSubmiter/list', params)
  },
  joinSubmiterAdd (params) {
    // 提案联名人新增
    return HTTP.json('/cppcc/joinSubmiter/add', params)
  },
  joinSubmiterDel (params) {
    // 提案联名人删除
    return HTTP.json('/cppcc/joinSubmiter/dels', params)
  },
  suggestBySerialNumber (params) {
    // 根据提案编号查询
    return HTTP.json('/proposal/info/bySerialNumber', params)
  },
  suggestionAgree (params) {
    // 同意联名
    return HTTP.json('/proposal/agree', params)
  },
  suggestionNextNodes (params) {
    // 提案流程操作项
    return HTTP.json('/proposal/nextNodes', params)
  },
  suggestionComplete (params) {
    // 处理任务节点
    return HTTP.json('/proposal/complete', params)
  },
  suggestionBatchComplete (params) {
    // 批量处理任务节点
    return HTTP.json('/proposal/batchComplete', params)
  },
  suggestionBatchCompleteUnit (params) {
    // 批量转给承办单位
    return HTTP.json('/proposal/batchSubmitHandleOffice', params)
  },
  suggestionBatchPreAssignOffice (params) {
    // 批量预交办
    return HTTP.json('/proposal/batchPreAssignOffice', params)
  },
  handingPortionCommunicationList (params) {
    // 沟通情况列表
    return HTTP.json('/cppcc/handingPortionCommunication/list', params)
  },
  handingPortionCommunicationInfo (params) {
    // 沟通情况详情
    return HTTP.json('/cppcc/handingPortionCommunication/info', params)
  },
  handingPortionCommunicationDel (params) {
    // 沟通情况删除
    return HTTP.json('/cppcc/handingPortionCommunication/dels', params)
  },
  prepareMembers (params) {
    // 提案沟通记录待选委员
    return HTTP.json('/cppcc/handingPortionCommunication/prepareMembers', params)
  },
  portionSelect (params) {
    // 提案沟通记录待选单位
    return HTTP.json('/proposal/portionSelect', params)
  },
  handingPortionDelayInfo (params) {
    // 延期记录详情
    return HTTP.json('/cppcc/handingPortionDelay/info', params)
  },
  handingPortionDelayVerifyDelay (params) {
    // 延期记录审查
    return HTTP.json('/cppcc/handingPortionDelay/verify/delay', params)
  },
  handingPortionAnswerList (params) {
    // 答复列表
    return HTTP.json('/cppcc/handingPortionAnswer/list', params)
  },
  handingPortionAnswerInfo (params) {
    // 答复详情
    return HTTP.json('/cppcc/handingPortionAnswer/info', params)
  },
  handingPortionAdjustList (params) {
    // 调整列表
    return HTTP.json('/cppcc/handingPortionAdjust/list', params)
  },
  suggestionUnlock (params) {
    // 解锁
    return HTTP.json('/proposal/unlock', params)
  },
  suggestionOpen (params) {
    // 批量公开
    return HTTP.json('/proposal/batch/open', params)
  },
  suggestionMajor (params) {
    // 批量设置重点提案
    return HTTP.json('/proposal/batch/major', params)
  },
  suggestionExcellent (params) {
    // 批量设置优秀提案
    return HTTP.json('/proposal/signExcellent', params)
  },
  suggestionSatisfactionInfo (params) {
    // 满意度详情
    return HTTP.json('/proposalSatisfaction/info', params)
  },
  suggestionCountSelector (params) {
    // 办理中提案统计
    return HTTP.json('/proposal/countSelector', params)
  },
  handingPortionAdjustRecord (params) {
    // 承办单位调整结果列表
    return HTTP.json('/cppcc/handingPortionAdjust/record', params)
  },
  suggestionPress (params) {
    // 催办提案
    return HTTP.json('/proposal/press', params)
  },
  handlingPortionBatchConfirm (params) {
    // 批量催办签收建议
    return HTTP.json('/cppcc/handlingPortion/batchConfirm', params)
  },
  handlingPortionConfirm (params) {
    // 签收建议
    return HTTP.json('/cppcc/handlingPortion/confirm', params)
  },
  handlingMassingEditAnswerStopDate (params) {
    // 修改答复截止时间
    return HTTP.json('/cppcc/handlingMassing/editAnswerStopDate', params)
  },
  handingPortionTraceList (params) {
    // 承办单位申请跟踪办理记录
    return HTTP.json('/cppcc/handingPortionTrace/list', params)
  },
  handingPortionTraceAdd (params) {
    // 承办单位申请跟踪办理记录
    return HTTP.json('/cppcc/handingPortionTrace/add', params)
  },
  handingPortionTraceInfo (params) {
    // 承办单位申请跟踪详情
    return HTTP.json('/cppcc/handingPortionTrace/info', params)
  },
  handingPortionTraceVerify (params) {
    // 承办单位申请跟踪审查
    return HTTP.json('/cppcc/handingPortionTrace/verify', params)
  },
  proposalClueList (params) {
    // 提案线索列表
    return HTTP.json('/proposalClue/list', params)
  },
  proposalClueInfo (params) {
    // 提案线索详情
    return HTTP.json('/proposalClue/info', params)
  },
  proposalClueDel (params) {
    // 提案线索删除
    return HTTP.json('/proposalClue/dels', params)
  },
  proposalClueUse (params) {
    // 提案线索引用
    return HTTP.json('/proposalClue/use', params)
  },
  mergeProposalList (params) {
    // 并案列表
    return HTTP.json('/mergeProposal/list', params)
  },
  mergeProposalAdd (params) {
    // 并案
    return HTTP.json('/mergeProposal/add', params)
  },
  mergeProposalDel (params) {
    // 取消并案
    return HTTP.json('/mergeProposal/dels', params)
  },
  proposalMergeSuggest (params) {
    // 智能并案
    return HTTP.json('/hadoop_api/datax/proposal/mergeSuggest', params, { noErrorTip: true })
  },
  similarity (params) {
    // 相似度查询
    return HTTP.json('/summoner/pubApi/similarity', params, { noErrorTip: true })
  },
  userJoin (params) {
    // 推荐用户
    return HTTP.json('/common/suggestRecommendation', params, { noErrorTip: true })
  },
  commonUnit (params) {
    // 推荐单位
    return HTTP.json('/summoner/common/recommendHandleUnit', params, { noErrorTip: true })
  },
  commonType (params) {
    // 推荐分类
    return HTTP.json('/common/classify', params, { noErrorTip: true })
  },
  proposalClueTheme (params) {
    // 提案文案线索列表
    return HTTP.json('/proposalClueTheme/list', params)
  },
  proposalClueThemeInfo (params) {
    // 提案线索详情
    return HTTP.json('/proposalClueTheme/info', params)
  },
  clueWord (params) {
    // 提案导出
    return HTTP.json('/proposalClue/loadDocData', params)
  },
  reqProposalEmpty (url, params) {
    // 获取提案分类列表 - empty: 查询未分类提案 notempty：查询已分类提案
    return HTTP.json(`/proposal/findThemeProposal/${url}`, params)
  },
  reqProposalTheme (url, params) {
    //批量操作 提案细分分类接口 - add：批量细分分类  clear：批量清空分类
    return HTTP.json(`/proposal/batch/proposalTheme/${url}`, params)
  },
  reqFindOfficeProposal (url, params) {
    // 获取提案办理单位细分列表 - empty: 查询未分类提案 notempty：查询已分类提案
    return HTTP.json(`/proposal/findOfficeProposal/${url}`, params)
  },
  reqProposalBatchComplete (url, params) {
    //批量操作 提案细分办理单位接口 - add：批量细分分类  clear：批量清空分类
    return HTTP.json(`/proposal/batch/complete/${url}`, params)
  },
  handingPortionAnswerDel (params) {
    // 答复删除
    return HTTP.json('/cppcc/handingPortionAnswer/dels', params)
  },
  handingPortionAnswerEdit (params) {
    // 修改答复
    return HTTP.json('/cppcc/handingPortionAnswer/edit', params)
  },
  handingPortionAdjustOfficeInfo (params) {
    // 办理单位详情
    return HTTP.json('/cppcc/handingPortionAdjust/adjustOfficeInfo', params)
  },
  suggestionSatisfactionList (params) {
    // 获取满意度测评列表
    return HTTP.json('/proposalSatisfaction/list', params)
  },
  suggestionSatisfactionDel (params) {
    // 满意度测评删除
    return HTTP.json('/proposalSatisfaction/dels', params)
  },
  suggestionSatisfactionEdit (params) {
    // 修改满意度测评
    return HTTP.json('/proposalSatisfaction/edit', params)
  },
  handingPortionTraceSelector (params) {
    // 选择下拉选传参承办单位申请跟踪审查
    return HTTP.json('/cppcc/handingPortionTrace/selector', params)
  },
  suggestionSuperDetail (params) {
    // 超级修改详情
    return HTTP.json(`/proposal/superDetail`, params)
  },
  handlingPortionList (params) {
    // 获取建议办理信息列表
    return HTTP.json('/cppcc/handlingPortion/list', params)
  },
  handlingPortionInfo (params) {
    // 建议办理信息详情
    return HTTP.json('/cppcc/handlingPortion/info', params)
  },
  handlingPortionDel (params) {
    // 建议办理信息删除
    return HTTP.json('/cppcc/handlingPortion/dels', params)
  },
  handlingPortionEdit (params) {
    // 修改建议办理信息
    return HTTP.json('/cppcc/handlingPortion/edit', params)
  },
  handlingPortionAdd (params) {
    // 新增建议办理信息
    return HTTP.json('/cppcc/handlingPortion/add', params)
  },
  handleOfficeReportList (params) {
    // 列表
    return HTTP.json('/handleOfficeReport/list', params)
  },
  handleOfficeReportInfo (params) {
    // 详情
    return HTTP.json('/handleOfficeReport/info', params)
  },
  handleOfficeReportDel (params) {
    // 删除
    return HTTP.json('/handleOfficeReport/dels', params)
  },
  handleOfficeReportFlush (params) {
    // 刷新
    return HTTP.json('/handleOfficeReport/flush', params)
  },
  handleOfficeReportPress (params) {
    // 催办
    return HTTP.json('/handleOfficeReport/press', params)
  },
  handleOfficeReportZip (params) {
    // 导出
    return HTTP.fileDownload('/handleOfficeReport/attachmentZip', params)
  },

  getProposalYear (params) { // 配置届次
    return HTTP.json('/customizeData/getProposalYear', params)
  },
  proposalThemeSelect (params) { // 提案分类
    return HTTP.json('/proposalApi/proposalThemeSelect', params)
  },
  getProposalStatus (params) { // 提案状态
    return HTTP.json('/proposalApi/getProposalStatus', params)
  },
  getProposalExportCount (params) { // 导出检索
    return HTTP.json('/proposalApi/proposalExportCount', params)
  },
  getProposalExport (params) { // 导出提案指定文件
    return HTTP.json('/proposalApi/proposalExport', params)
  },
  updateLeaderMark (params) { // 设置领导批示提案
    return HTTP.json('/proposalApi/updateLeaderMark', params)
  },
  proposalHistoryV2List (params) { // 历史提案列表
    return HTTP.json('/proposalHistoryV2/list', params)
  },
  proposalHistoryV2Dels (params) { // 历史提案删除
    return HTTP.json('/proposalHistoryV2/dels', params)
  },
  proposalHistoryV2Info (params) { // 历史提案详情
    return HTTP.json('/proposalHistoryV2/info', params)
  },
  proposalAutomaticEvaluation (params) { // 自动评价
    return HTTP.json('proposalApi/automaticEvaluation', params)
  },
  compareSimilarRates (params) {
    // 文本相似度比对
    return HTTP.json('/common/compareSimilarRates', params)
  },
  suggestionMeetingType (params) {
    // 建议类型
    return HTTP.json('/proposal/proposalMeetingType', params)
  },
  textComparison (params) {
    // 文本对比
    const formData = new FormData()
    Object.keys(params).forEach((key) => {
      formData.append(key, params[key])
    })
    return HTTP.post('/wordApi/textComparison', formData, {
      headers: { 'Content-Type': 'multipart/form-data', 'X-Custom-Header': 'your-header-value' }
    })
  },
  suggestionSortAndRenumberSerialNum (params) {
    // 重新编号
    return HTTP.json('/proposal/sortAndRenumberSerialNum', params)
  }
}
export default api
