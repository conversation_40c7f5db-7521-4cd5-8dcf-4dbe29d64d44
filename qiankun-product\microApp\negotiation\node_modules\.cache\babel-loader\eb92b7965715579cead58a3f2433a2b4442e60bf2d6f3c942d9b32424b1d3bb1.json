{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport store from '@/store';\nimport router from '@/router';\nimport { changeTheme } from './theme';\nimport onWholeWaterMark from './water-market.js';\nimport { setTimeListener } from './GlobalTimeListener.js';\nimport { ElMessage } from 'element-plus';\nexport var configCode = ['systemType', 'systemName', 'platformAreaId', 'systemWatermark', 'appOnlyHeader', 'pageSize', 'pageSizes', 'whetherAiChat', 'file_preview_mode', 'file_preview_open', 'screenWindowDefault', 'queryTypeShow', 'systemMobileEncrypt', 'systemInactivityTimeout', 'whetherUseIntelligentize'];\nexport var openConfigCode = ['systemSign', 'systemPlatform', 'systemName', 'platformAreaName', 'loginSystemName', 'loginNameLineFeedPosition', 'systemNameAreaPrefix', 'systemGrayscale', 'appOnlyHeader', 'appDownloadUrl', 'forbidWeakPassword',\n// 'whetherRegionSelect',\n'systemLoginContact', 'noVerifyAdmin', 'DetectionVersion', 'VersionUpdatePrompt'];\nvar _currentTheme = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n    var _yield$api$currentThe, data;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _context.prev = 0;\n          _context.next = 3;\n          return api.currentTheme({});\n        case 3:\n          _yield$api$currentThe = _context.sent;\n          data = _yield$api$currentThe.data;\n          changeTheme(data);\n          _context.next = 11;\n          break;\n        case 8:\n          _context.prev = 8;\n          _context.t0 = _context[\"catch\"](0);\n          if ((_context.t0 === null || _context.t0 === void 0 ? void 0 : _context.t0.code) === 302) {\n            _currentTheme();\n          } else {\n            changeTheme();\n          }\n        case 11:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee, null, [[0, 8]]);\n  }));\n  return function currentTheme() {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { _currentTheme as currentTheme };\nexport var loginMenu = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n    var _yield$api$loginMenu, menu;\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          _context2.next = 2;\n          return api.loginMenu();\n        case 2:\n          _yield$api$loginMenu = _context2.sent;\n          menu = _yield$api$loginMenu.data;\n          sessionStorage.setItem('menu', JSON.stringify(menu));\n          store.commit('setMenu', menu);\n        case 6:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  }));\n  return function loginMenu() {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport var loginArea = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n    var _yield$api$loginAreas, area;\n    return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          _context3.next = 2;\n          return api.loginAreas();\n        case 2:\n          _yield$api$loginAreas = _context3.sent;\n          area = _yield$api$loginAreas.data;\n          sessionStorage.setItem('area', JSON.stringify(area));\n          store.commit('setArea', area);\n        case 6:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3);\n  }));\n  return function loginArea() {\n    return _ref3.apply(this, arguments);\n  };\n}();\nexport var loginRole = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n    var _yield$api$loginRole, role;\n    return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _context4.next = 2;\n          return api.loginRole();\n        case 2:\n          _yield$api$loginRole = _context4.sent;\n          role = _yield$api$loginRole.data;\n          sessionStorage.setItem('role', JSON.stringify(role));\n          store.commit('setRole', role);\n        case 6:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return function loginRole() {\n    return _ref4.apply(this, arguments);\n  };\n}();\nexport var globalReadConfig = /*#__PURE__*/function () {\n  var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n    var _yield$api$globalRead, data, _store$state;\n    return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n      while (1) switch (_context5.prev = _context5.next) {\n        case 0:\n          _context5.next = 2;\n          return api.globalReadConfig({\n            codes: configCode\n          });\n        case 2:\n          _yield$api$globalRead = _context5.sent;\n          data = _yield$api$globalRead.data;\n          if (data !== null && data !== void 0 && data.systemWatermark && (data === null || data === void 0 ? void 0 : data.systemWatermark) !== 'false') {\n            if ((data === null || data === void 0 ? void 0 : data.systemWatermark) === 'user') {\n              onWholeWaterMark((_store$state = store.state) === null || _store$state === void 0 || (_store$state = _store$state.user) === null || _store$state === void 0 ? void 0 : _store$state.userName);\n            } else {\n              onWholeWaterMark((data === null || data === void 0 ? void 0 : data.systemWatermark) === 'true' ? data === null || data === void 0 ? void 0 : data.systemName : data === null || data === void 0 ? void 0 : data.systemWatermark);\n            }\n          }\n          store.commit('setReadConfig', data);\n        case 6:\n        case \"end\":\n          return _context5.stop();\n      }\n    }, _callee5);\n  }));\n  return function globalReadConfig() {\n    return _ref5.apply(this, arguments);\n  };\n}();\nexport var globalReadOpenConfig = /*#__PURE__*/function () {\n  var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6(callback) {\n    var _yield$api$globalRead2, data;\n    return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n      while (1) switch (_context6.prev = _context6.next) {\n        case 0:\n          _context6.prev = 0;\n          _context6.next = 3;\n          return api.globalReadOpenConfig({\n            codes: openConfigCode\n          });\n        case 3:\n          _yield$api$globalRead2 = _context6.sent;\n          data = _yield$api$globalRead2.data;\n          sessionStorage.setItem('noVerifyAdmin', data.noVerifyAdmin);\n          sessionStorage.setItem('DetectionVersion', data.DetectionVersion);\n          sessionStorage.setItem('VersionUpdatePrompt', data.VersionUpdatePrompt);\n          document.title = data === null || data === void 0 ? void 0 : data.systemName;\n          if ((data === null || data === void 0 ? void 0 : data.systemGrayscale) === 'true') {\n            window.document.documentElement.setAttribute('grey', '1');\n          }\n          store.commit('setReadOpenConfig', data);\n          if (callback) callback(data);\n          _context6.next = 17;\n          break;\n        case 14:\n          _context6.prev = 14;\n          _context6.t0 = _context6[\"catch\"](0);\n          if (callback) callback();\n        case 17:\n        case \"end\":\n          return _context6.stop();\n      }\n    }, _callee6, null, [[0, 14]]);\n  }));\n  return function globalReadOpenConfig(_x) {\n    return _ref6.apply(this, arguments);\n  };\n}();\nvar _loginRefreshToken = /*#__PURE__*/function () {\n  var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n    var refreshToken, _yield$api$login, data, refreshTime, refreshTokenExpiration;\n    return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n      while (1) switch (_context7.prev = _context7.next) {\n        case 0:\n          refreshToken = sessionStorage.getItem('refreshToken');\n          if (refreshToken) {\n            _context7.next = 3;\n            break;\n          }\n          return _context7.abrupt(\"return\");\n        case 3:\n          _context7.prev = 3;\n          _context7.next = 6;\n          return api.login({\n            grant_type: 'refresh_token',\n            refresh_token: refreshToken\n          }, {\n            token: 'basic enlzb2Z0Onp5c29mdCo2MDc5'\n          });\n        case 6:\n          _yield$api$login = _context7.sent;\n          data = _yield$api$login.data;\n          refreshTime = data.expireDate + data.expire * 1000;\n          sessionStorage.setItem('token', data.token);\n          sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`);\n          sessionStorage.setItem('expires', data.expires_in);\n          sessionStorage.setItem('refreshTime', refreshTime);\n          sessionStorage.setItem('refreshToken', data.refreshToken.value);\n          sessionStorage.setItem('expiration', data.refreshToken.expiration);\n          refreshTokenExpiration = Number(refreshTime) - 300000;\n          setTimeListener(refreshTokenExpiration, function () {\n            console.log(refreshTokenExpiration, '刷新令牌时间到了！');\n            _loginRefreshToken();\n          });\n          _context7.next = 23;\n          break;\n        case 19:\n          _context7.prev = 19;\n          _context7.t0 = _context7[\"catch\"](3);\n          console.log(_context7.t0);\n          loginOut('为保障您的账户安全，请重新登录。');\n        case 23:\n        case \"end\":\n          return _context7.stop();\n      }\n    }, _callee7, null, [[3, 19]]);\n  }));\n  return function loginRefreshToken() {\n    return _ref7.apply(this, arguments);\n  };\n}();\nexport { _loginRefreshToken as loginRefreshToken };\nexport var loginOutClear = function loginOutClear() {\n  sessionStorage.clear();\n  var goal_login_router_path = localStorage.getItem('goal_login_router_path');\n  if (goal_login_router_path) {\n    var goal_login_router_query = localStorage.getItem('goal_login_router_query') || '';\n    router.push({\n      path: goal_login_router_path,\n      query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\n    });\n  } else {\n    router.push({\n      path: '/LoginView'\n    });\n  }\n  store.commit('setState');\n  globalReadOpenConfig();\n  // store.state.socket.disconnect()\n  // store.state.socket = null\n};\nexport var loginOut = /*#__PURE__*/function () {\n  var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8(text, type) {\n    var _yield$api$loginOut, code;\n    return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n      while (1) switch (_context8.prev = _context8.next) {\n        case 0:\n          _context8.next = 2;\n          return api.loginOut();\n        case 2:\n          _yield$api$loginOut = _context8.sent;\n          code = _yield$api$loginOut.code;\n          if (code === 200) {\n            loginOutClear();\n            ElMessage({\n              message: text,\n              showClose: true,\n              type: type || 'success'\n            });\n          }\n        case 5:\n        case \"end\":\n          return _context8.stop();\n      }\n    }, _callee8);\n  }));\n  return function loginOut(_x2, _x3) {\n    return _ref8.apply(this, arguments);\n  };\n}();\nexport var rongCloudToken = /*#__PURE__*/function () {\n  var _ref9 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9(type, appkey, url, key) {\n    var res;\n    return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n      while (1) switch (_context9.prev = _context9.next) {\n        case 0:\n          _context9.next = 2;\n          return api.rongCloud(url, {\n            type: 'getToken',\n            userId: key + store.state.user.accountId,\n            userName: store.state.user.userName,\n            userPortrait: store.state.user.image,\n            environment: appkey === 'y745wfm84be5v' ? '1' : '2'\n          }, type);\n        case 2:\n          res = _context9.sent;\n          if (type) {\n            store.commit('setRongCloudToken', res.data);\n          } else {\n            store.commit('setRongCloudToken', res.token);\n          }\n        case 4:\n        case \"end\":\n          return _context9.stop();\n      }\n    }, _callee9);\n  }));\n  return function rongCloudToken(_x4, _x5, _x6, _x7) {\n    return _ref9.apply(this, arguments);\n  };\n}();", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "store", "router", "changeTheme", "onWholeWaterMark", "setTimeListener", "ElMessage", "configCode", "openConfigCode", "currentTheme", "_ref", "_callee", "_yield$api$currentThe", "data", "_callee$", "_context", "t0", "code", "_currentTheme", "loginMenu", "_ref2", "_callee2", "_yield$api$loginMenu", "menu", "_callee2$", "_context2", "sessionStorage", "setItem", "JSON", "stringify", "commit", "loginArea", "_ref3", "_callee3", "_yield$api$loginAreas", "area", "_callee3$", "_context3", "loginAreas", "loginRole", "_ref4", "_callee4", "_yield$api$loginRole", "role", "_callee4$", "_context4", "globalReadConfig", "_ref5", "_callee5", "_yield$api$globalRead", "_store$state", "_callee5$", "_context5", "codes", "systemWatermark", "state", "user", "userName", "systemName", "globalReadOpenConfig", "_ref6", "_callee6", "callback", "_yield$api$globalRead2", "_callee6$", "_context6", "noVerifyAdmin", "DetectionVersion", "VersionUpdatePrompt", "document", "title", "systemGrayscale", "window", "documentElement", "setAttribute", "_x", "loginRefreshToken", "_ref7", "_callee7", "refreshToken", "_yield$api$login", "refreshTime", "refreshTokenExpiration", "_callee7$", "_context7", "getItem", "login", "grant_type", "refresh_token", "token", "expireDate", "expire", "expires_in", "expiration", "Number", "console", "log", "loginOut", "_loginRefreshToken", "loginOutClear", "clear", "goal_login_router_path", "localStorage", "goal_login_router_query", "path", "query", "parse", "_ref8", "_callee8", "text", "_yield$api$loginOut", "_callee8$", "_context8", "message", "showClose", "_x2", "_x3", "rongCloudToken", "_ref9", "_callee9", "appkey", "url", "key", "res", "_callee9$", "_context9", "rongCloud", "userId", "accountId", "userPortrait", "image", "environment", "_x4", "_x5", "_x6", "_x7"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/js/GlobalMethod.js"], "sourcesContent": ["import api from '@/api'\r\nimport store from '@/store'\r\nimport router from '@/router'\r\nimport { changeTheme } from './theme'\r\nimport onWholeWaterMark from './water-market.js'\r\nimport { setTimeListener } from './GlobalTimeListener.js'\r\nimport { ElMessage } from 'element-plus'\r\nexport const configCode = [\r\n  'systemType',\r\n  'systemName',\r\n  'platformAreaId',\r\n  'systemWatermark',\r\n  'appOnlyHeader',\r\n  'pageSize',\r\n  'pageSizes',\r\n  'whetherAiChat',\r\n  'file_preview_mode',\r\n  'file_preview_open',\r\n  'screenWindowDefault',\r\n  'queryTypeShow',\r\n  'systemMobileEncrypt',\r\n  'systemInactivityTimeout',\r\n  'whetherUseIntelligentize'\r\n]\r\nexport const openConfigCode = [\r\n  'systemSign',\r\n  'systemPlatform',\r\n  'systemName',\r\n  'platformAreaName',\r\n  'loginSystemName',\r\n  'loginNameLineFeedPosition',\r\n  'systemNameAreaPrefix',\r\n  'systemGrayscale',\r\n  'appOnlyHeader',\r\n  'appDownloadUrl',\r\n  'forbidWeakPassword',\r\n  // 'whetherRegionSelect',\r\n  'systemLoginContact',\r\n  'noVerifyAdmin',\r\n  'DetectionVersion',\r\n  'VersionUpdatePrompt'\r\n]\r\nexport const currentTheme = async () => {\r\n  try {\r\n    const { data } = await api.currentTheme({})\r\n    changeTheme(data)\r\n  } catch (err) {\r\n    if (err?.code === 302) {\r\n      currentTheme()\r\n    } else {\r\n      changeTheme()\r\n    }\r\n  }\r\n}\r\n\r\nexport const loginMenu = async () => {\r\n  const { data: menu } = await api.loginMenu()\r\n  sessionStorage.setItem('menu', JSON.stringify(menu))\r\n  store.commit('setMenu', menu)\r\n}\r\nexport const loginArea = async () => {\r\n  const { data: area } = await api.loginAreas()\r\n  sessionStorage.setItem('area', JSON.stringify(area))\r\n  store.commit('setArea', area)\r\n}\r\nexport const loginRole = async () => {\r\n  const { data: role } = await api.loginRole()\r\n  sessionStorage.setItem('role', JSON.stringify(role))\r\n  store.commit('setRole', role)\r\n}\r\n\r\nexport const globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: configCode })\r\n  if (data?.systemWatermark && data?.systemWatermark !== 'false') {\r\n    if (data?.systemWatermark === 'user') {\r\n      onWholeWaterMark(store.state?.user?.userName)\r\n    } else {\r\n      onWholeWaterMark(data?.systemWatermark === 'true' ? data?.systemName : data?.systemWatermark)\r\n    }\r\n  }\r\n  store.commit('setReadConfig', data)\r\n}\r\nexport const globalReadOpenConfig = async (callback) => {\r\n  try {\r\n    const { data } = await api.globalReadOpenConfig({ codes: openConfigCode })\r\n    sessionStorage.setItem('noVerifyAdmin', data.noVerifyAdmin)\r\n    sessionStorage.setItem('DetectionVersion', data.DetectionVersion)\r\n    sessionStorage.setItem('VersionUpdatePrompt', data.VersionUpdatePrompt)\r\n    document.title = data?.systemName\r\n    if (data?.systemGrayscale === 'true') {\r\n      window.document.documentElement.setAttribute('grey', '1')\r\n    }\r\n    store.commit('setReadOpenConfig', data)\r\n    if (callback) callback(data)\r\n  } catch (err) {\r\n    if (callback) callback()\r\n  }\r\n}\r\n\r\nexport const loginRefreshToken = async () => {\r\n  const refreshToken = sessionStorage.getItem('refreshToken')\r\n  if (!refreshToken) return\r\n  try {\r\n    const { data } = await api.login({ grant_type: 'refresh_token', refresh_token: refreshToken, }, { token: 'basic enlzb2Z0Onp5c29mdCo2MDc5' })\r\n    const refreshTime = data.expireDate + (data.expire * 1000)\r\n    sessionStorage.setItem('token', data.token)\r\n    sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)\r\n    sessionStorage.setItem('expires', data.expires_in)\r\n    sessionStorage.setItem('refreshTime', refreshTime)\r\n    sessionStorage.setItem('refreshToken', data.refreshToken.value)\r\n    sessionStorage.setItem('expiration', data.refreshToken.expiration)\r\n    const refreshTokenExpiration = Number(refreshTime) - 300000\r\n    setTimeListener(refreshTokenExpiration, () => {\r\n      console.log(refreshTokenExpiration, '刷新令牌时间到了！')\r\n      loginRefreshToken()\r\n    })\r\n  } catch (error) {\r\n    console.log(error)\r\n    loginOut('为保障您的账户安全，请重新登录。')\r\n  }\r\n}\r\nexport const loginOutClear = () => {\r\n  sessionStorage.clear()\r\n  const goal_login_router_path = localStorage.getItem('goal_login_router_path')\r\n  if (goal_login_router_path) {\r\n    const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''\r\n    router.push({\r\n      path: goal_login_router_path,\r\n      query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\r\n    })\r\n  } else {\r\n    router.push({ path: '/LoginView' })\r\n  }\r\n  store.commit('setState')\r\n  globalReadOpenConfig()\r\n  // store.state.socket.disconnect()\r\n  // store.state.socket = null\r\n}\r\nexport const loginOut = async (text, type) => {\r\n  const { code } = await api.loginOut()\r\n  if (code === 200) {\r\n    loginOutClear()\r\n    ElMessage({ message: text, showClose: true, type: type || 'success' })\r\n  }\r\n}\r\n\r\nexport const rongCloudToken = async (type, appkey, url, key) => {\r\n  const res = await api.rongCloud(\r\n    url,\r\n    {\r\n      type: 'getToken',\r\n      userId: key + store.state.user.accountId,\r\n      userName: store.state.user.userName,\r\n      userPortrait: store.state.user.image,\r\n      environment: appkey === 'y745wfm84be5v' ? '1' : '2'\r\n    },\r\n    type\r\n  )\r\n  if (type) {\r\n    store.commit('setRongCloudToken', res.data)\r\n  } else {\r\n    store.commit('setRongCloudToken', res.token)\r\n  }\r\n}\r\n"], "mappings": "+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,WAAW,QAAQ,SAAS;AACrC,OAAOC,gBAAgB,MAAM,mBAAmB;AAChD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAO,IAAMC,UAAU,GAAG,CACxB,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,eAAe,EACf,UAAU,EACV,WAAW,EACX,eAAe,EACf,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACrB,eAAe,EACf,qBAAqB,EACrB,yBAAyB,EACzB,0BAA0B,CAC3B;AACD,OAAO,IAAMC,cAAc,GAAG,CAC5B,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,kBAAkB,EAClB,iBAAiB,EACjB,2BAA2B,EAC3B,sBAAsB,EACtB,iBAAiB,EACjB,eAAe,EACf,gBAAgB,EAChB,oBAAoB;AACpB;AACA,oBAAoB,EACpB,eAAe,EACf,kBAAkB,EAClB,qBAAqB,CACtB;AACM,IAAMC,aAAY;EAAA,IAAAC,IAAA,GAAAf,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqC,QAAA;IAAA,IAAAC,qBAAA,EAAAC,IAAA;IAAA,OAAA3H,mBAAA,GAAAuB,IAAA,UAAAqG,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAhC,IAAA,GAAAgC,QAAA,CAAA3D,IAAA;QAAA;UAAA2D,QAAA,CAAAhC,IAAA;UAAAgC,QAAA,CAAA3D,IAAA;UAAA,OAED4C,GAAG,CAACS,YAAY,CAAC,CAAC,CAAC,CAAC;QAAA;UAAAG,qBAAA,GAAAG,QAAA,CAAAlE,IAAA;UAAnCgE,IAAI,GAAAD,qBAAA,CAAJC,IAAI;UACZV,WAAW,CAACU,IAAI,CAAC;UAAAE,QAAA,CAAA3D,IAAA;UAAA;QAAA;UAAA2D,QAAA,CAAAhC,IAAA;UAAAgC,QAAA,CAAAC,EAAA,GAAAD,QAAA;UAEjB,IAAI,CAAAA,QAAA,CAAAC,EAAA,aAAAD,QAAA,CAAAC,EAAA,uBAAAD,QAAA,CAAAC,EAAA,CAAKC,IAAI,MAAK,GAAG,EAAE;YACrBR,aAAY,CAAC,CAAC;UAChB,CAAC,MAAM;YACLN,WAAW,CAAC,CAAC;UACf;QAAC;QAAA;UAAA,OAAAY,QAAA,CAAA7B,IAAA;MAAA;IAAA,GAAAyB,OAAA;EAAA,CAEJ;EAAA,gBAXYF,YAAYA,CAAA;IAAA,OAAAC,IAAA,CAAAb,KAAA,OAAAD,SAAA;EAAA;AAAA,GAWxB;AAAA,SAAAsB,aAAA,IAAAT,YAAA;AAED,OAAO,IAAMU,SAAS;EAAA,IAAAC,KAAA,GAAAzB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+C,SAAA;IAAA,IAAAC,oBAAA,EAAAC,IAAA;IAAA,OAAArI,mBAAA,GAAAuB,IAAA,UAAA+G,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA1C,IAAA,GAAA0C,SAAA,CAAArE,IAAA;QAAA;UAAAqE,SAAA,CAAArE,IAAA;UAAA,OACM4C,GAAG,CAACmB,SAAS,CAAC,CAAC;QAAA;UAAAG,oBAAA,GAAAG,SAAA,CAAA5E,IAAA;UAA9B0E,IAAI,GAAAD,oBAAA,CAAVT,IAAI;UACZa,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC,CAAC;UACpDtB,KAAK,CAAC6B,MAAM,CAAC,SAAS,EAAEP,IAAI,CAAC;QAAA;QAAA;UAAA,OAAAE,SAAA,CAAAvC,IAAA;MAAA;IAAA,GAAAmC,QAAA;EAAA,CAC9B;EAAA,gBAJYF,SAASA,CAAA;IAAA,OAAAC,KAAA,CAAAvB,KAAA,OAAAD,SAAA;EAAA;AAAA,GAIrB;AACD,OAAO,IAAMmC,SAAS;EAAA,IAAAC,KAAA,GAAArC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2D,SAAA;IAAA,IAAAC,qBAAA,EAAAC,IAAA;IAAA,OAAAjJ,mBAAA,GAAAuB,IAAA,UAAA2H,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAtD,IAAA,GAAAsD,SAAA,CAAAjF,IAAA;QAAA;UAAAiF,SAAA,CAAAjF,IAAA;UAAA,OACM4C,GAAG,CAACsC,UAAU,CAAC,CAAC;QAAA;UAAAJ,qBAAA,GAAAG,SAAA,CAAAxF,IAAA;UAA/BsF,IAAI,GAAAD,qBAAA,CAAVrB,IAAI;UACZa,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACM,IAAI,CAAC,CAAC;UACpDlC,KAAK,CAAC6B,MAAM,CAAC,SAAS,EAAEK,IAAI,CAAC;QAAA;QAAA;UAAA,OAAAE,SAAA,CAAAnD,IAAA;MAAA;IAAA,GAAA+C,QAAA;EAAA,CAC9B;EAAA,gBAJYF,SAASA,CAAA;IAAA,OAAAC,KAAA,CAAAnC,KAAA,OAAAD,SAAA;EAAA;AAAA,GAIrB;AACD,OAAO,IAAM2C,SAAS;EAAA,IAAAC,KAAA,GAAA7C,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAmE,SAAA;IAAA,IAAAC,oBAAA,EAAAC,IAAA;IAAA,OAAAzJ,mBAAA,GAAAuB,IAAA,UAAAmI,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA9D,IAAA,GAAA8D,SAAA,CAAAzF,IAAA;QAAA;UAAAyF,SAAA,CAAAzF,IAAA;UAAA,OACM4C,GAAG,CAACuC,SAAS,CAAC,CAAC;QAAA;UAAAG,oBAAA,GAAAG,SAAA,CAAAhG,IAAA;UAA9B8F,IAAI,GAAAD,oBAAA,CAAV7B,IAAI;UACZa,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACc,IAAI,CAAC,CAAC;UACpD1C,KAAK,CAAC6B,MAAM,CAAC,SAAS,EAAEa,IAAI,CAAC;QAAA;QAAA;UAAA,OAAAE,SAAA,CAAA3D,IAAA;MAAA;IAAA,GAAAuD,QAAA;EAAA,CAC9B;EAAA,gBAJYF,SAASA,CAAA;IAAA,OAAAC,KAAA,CAAA3C,KAAA,OAAAD,SAAA;EAAA;AAAA,GAIrB;AAED,OAAO,IAAMkD,gBAAgB;EAAA,IAAAC,KAAA,GAAApD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0E,SAAA;IAAA,IAAAC,qBAAA,EAAApC,IAAA,EAAAqC,YAAA;IAAA,OAAAhK,mBAAA,GAAAuB,IAAA,UAAA0I,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAArE,IAAA,GAAAqE,SAAA,CAAAhG,IAAA;QAAA;UAAAgG,SAAA,CAAAhG,IAAA;UAAA,OACP4C,GAAG,CAAC8C,gBAAgB,CAAC;YAAEO,KAAK,EAAE9C;UAAW,CAAC,CAAC;QAAA;UAAA0C,qBAAA,GAAAG,SAAA,CAAAvG,IAAA;UAA1DgE,IAAI,GAAAoC,qBAAA,CAAJpC,IAAI;UACZ,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyC,eAAe,IAAI,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,eAAe,MAAK,OAAO,EAAE;YAC9D,IAAI,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,eAAe,MAAK,MAAM,EAAE;cACpClD,gBAAgB,EAAA8C,YAAA,GAACjD,KAAK,CAACsD,KAAK,cAAAL,YAAA,gBAAAA,YAAA,GAAXA,YAAA,CAAaM,IAAI,cAAAN,YAAA,uBAAjBA,YAAA,CAAmBO,QAAQ,CAAC;YAC/C,CAAC,MAAM;cACLrD,gBAAgB,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,eAAe,MAAK,MAAM,GAAGzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,UAAU,GAAG7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,eAAe,CAAC;YAC/F;UACF;UACArD,KAAK,CAAC6B,MAAM,CAAC,eAAe,EAAEjB,IAAI,CAAC;QAAA;QAAA;UAAA,OAAAuC,SAAA,CAAAlE,IAAA;MAAA;IAAA,GAAA8D,QAAA;EAAA,CACpC;EAAA,gBAVYF,gBAAgBA,CAAA;IAAA,OAAAC,KAAA,CAAAlD,KAAA,OAAAD,SAAA;EAAA;AAAA,GAU5B;AACD,OAAO,IAAM+D,oBAAoB;EAAA,IAAAC,KAAA,GAAAjE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAuF,SAAOC,QAAQ;IAAA,IAAAC,sBAAA,EAAAlD,IAAA;IAAA,OAAA3H,mBAAA,GAAAuB,IAAA,UAAAuJ,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAlF,IAAA,GAAAkF,SAAA,CAAA7G,IAAA;QAAA;UAAA6G,SAAA,CAAAlF,IAAA;UAAAkF,SAAA,CAAA7G,IAAA;UAAA,OAExB4C,GAAG,CAAC2D,oBAAoB,CAAC;YAAEN,KAAK,EAAE7C;UAAe,CAAC,CAAC;QAAA;UAAAuD,sBAAA,GAAAE,SAAA,CAAApH,IAAA;UAAlEgE,IAAI,GAAAkD,sBAAA,CAAJlD,IAAI;UACZa,cAAc,CAACC,OAAO,CAAC,eAAe,EAAEd,IAAI,CAACqD,aAAa,CAAC;UAC3DxC,cAAc,CAACC,OAAO,CAAC,kBAAkB,EAAEd,IAAI,CAACsD,gBAAgB,CAAC;UACjEzC,cAAc,CAACC,OAAO,CAAC,qBAAqB,EAAEd,IAAI,CAACuD,mBAAmB,CAAC;UACvEC,QAAQ,CAACC,KAAK,GAAGzD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,UAAU;UACjC,IAAI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,eAAe,MAAK,MAAM,EAAE;YACpCC,MAAM,CAACH,QAAQ,CAACI,eAAe,CAACC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;UAC3D;UACAzE,KAAK,CAAC6B,MAAM,CAAC,mBAAmB,EAAEjB,IAAI,CAAC;UACvC,IAAIiD,QAAQ,EAAEA,QAAQ,CAACjD,IAAI,CAAC;UAAAoD,SAAA,CAAA7G,IAAA;UAAA;QAAA;UAAA6G,SAAA,CAAAlF,IAAA;UAAAkF,SAAA,CAAAjD,EAAA,GAAAiD,SAAA;UAE5B,IAAIH,QAAQ,EAAEA,QAAQ,CAAC,CAAC;QAAA;QAAA;UAAA,OAAAG,SAAA,CAAA/E,IAAA;MAAA;IAAA,GAAA2E,QAAA;EAAA,CAE3B;EAAA,gBAfYF,oBAAoBA,CAAAgB,EAAA;IAAA,OAAAf,KAAA,CAAA/D,KAAA,OAAAD,SAAA;EAAA;AAAA,GAehC;AAEM,IAAMgF,kBAAiB;EAAA,IAAAC,KAAA,GAAAlF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwG,SAAA;IAAA,IAAAC,YAAA,EAAAC,gBAAA,EAAAnE,IAAA,EAAAoE,WAAA,EAAAC,sBAAA;IAAA,OAAAhM,mBAAA,GAAAuB,IAAA,UAAA0K,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAArG,IAAA,GAAAqG,SAAA,CAAAhI,IAAA;QAAA;UACzB2H,YAAY,GAAGrD,cAAc,CAAC2D,OAAO,CAAC,cAAc,CAAC;UAAA,IACtDN,YAAY;YAAAK,SAAA,CAAAhI,IAAA;YAAA;UAAA;UAAA,OAAAgI,SAAA,CAAApI,MAAA;QAAA;UAAAoI,SAAA,CAAArG,IAAA;UAAAqG,SAAA,CAAAhI,IAAA;UAAA,OAEQ4C,GAAG,CAACsF,KAAK,CAAC;YAAEC,UAAU,EAAE,eAAe;YAAEC,aAAa,EAAET;UAAc,CAAC,EAAE;YAAEU,KAAK,EAAE;UAAiC,CAAC,CAAC;QAAA;UAAAT,gBAAA,GAAAI,SAAA,CAAAvI,IAAA;UAApIgE,IAAI,GAAAmE,gBAAA,CAAJnE,IAAI;UACNoE,WAAW,GAAGpE,IAAI,CAAC6E,UAAU,GAAI7E,IAAI,CAAC8E,MAAM,GAAG,IAAK;UAC1DjE,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEd,IAAI,CAAC4E,KAAK,CAAC;UAC3C/D,cAAc,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUd,IAAI,CAACkE,YAAY,CAACnL,KAAK,EAAE,CAAC;UAC5E8H,cAAc,CAACC,OAAO,CAAC,SAAS,EAAEd,IAAI,CAAC+E,UAAU,CAAC;UAClDlE,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEsD,WAAW,CAAC;UAClDvD,cAAc,CAACC,OAAO,CAAC,cAAc,EAAEd,IAAI,CAACkE,YAAY,CAACnL,KAAK,CAAC;UAC/D8H,cAAc,CAACC,OAAO,CAAC,YAAY,EAAEd,IAAI,CAACkE,YAAY,CAACc,UAAU,CAAC;UAC5DX,sBAAsB,GAAGY,MAAM,CAACb,WAAW,CAAC,GAAG,MAAM;UAC3D5E,eAAe,CAAC6E,sBAAsB,EAAE,YAAM;YAC5Ca,OAAO,CAACC,GAAG,CAACd,sBAAsB,EAAE,WAAW,CAAC;YAChDN,kBAAiB,CAAC,CAAC;UACrB,CAAC,CAAC;UAAAQ,SAAA,CAAAhI,IAAA;UAAA;QAAA;UAAAgI,SAAA,CAAArG,IAAA;UAAAqG,SAAA,CAAApE,EAAA,GAAAoE,SAAA;UAEFW,OAAO,CAACC,GAAG,CAAAZ,SAAA,CAAApE,EAAM,CAAC;UAClBiF,QAAQ,CAAC,kBAAkB,CAAC;QAAA;QAAA;UAAA,OAAAb,SAAA,CAAAlG,IAAA;MAAA;IAAA,GAAA4F,QAAA;EAAA,CAE/B;EAAA,gBArBYF,iBAAiBA,CAAA;IAAA,OAAAC,KAAA,CAAAhF,KAAA,OAAAD,SAAA;EAAA;AAAA,GAqB7B;AAAA,SAAAsG,kBAAA,IAAAtB,iBAAA;AACD,OAAO,IAAMuB,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EACjCzE,cAAc,CAAC0E,KAAK,CAAC,CAAC;EACtB,IAAMC,sBAAsB,GAAGC,YAAY,CAACjB,OAAO,CAAC,wBAAwB,CAAC;EAC7E,IAAIgB,sBAAsB,EAAE;IAC1B,IAAME,uBAAuB,GAAGD,YAAY,CAACjB,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE;IACrFnF,MAAM,CAACtC,IAAI,CAAC;MACV4I,IAAI,EAAEH,sBAAsB;MAC5BI,KAAK,EAAEF,uBAAuB,GAAG3E,IAAI,CAAC8E,KAAK,CAACH,uBAAuB,CAAC,GAAG,CAAC;IAC1E,CAAC,CAAC;EACJ,CAAC,MAAM;IACLrG,MAAM,CAACtC,IAAI,CAAC;MAAE4I,IAAI,EAAE;IAAa,CAAC,CAAC;EACrC;EACAvG,KAAK,CAAC6B,MAAM,CAAC,UAAU,CAAC;EACxB6B,oBAAoB,CAAC,CAAC;EACtB;EACA;AACF,CAAC;AACD,OAAO,IAAMsC,QAAQ;EAAA,IAAAU,KAAA,GAAAhH,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsI,SAAOC,IAAI,EAAE9L,IAAI;IAAA,IAAA+L,mBAAA,EAAA7F,IAAA;IAAA,OAAA/H,mBAAA,GAAAuB,IAAA,UAAAsM,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAjI,IAAA,GAAAiI,SAAA,CAAA5J,IAAA;QAAA;UAAA4J,SAAA,CAAA5J,IAAA;UAAA,OAChB4C,GAAG,CAACiG,QAAQ,CAAC,CAAC;QAAA;UAAAa,mBAAA,GAAAE,SAAA,CAAAnK,IAAA;UAA7BoE,IAAI,GAAA6F,mBAAA,CAAJ7F,IAAI;UACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;YAChBkF,aAAa,CAAC,CAAC;YACf7F,SAAS,CAAC;cAAE2G,OAAO,EAAEJ,IAAI;cAAEK,SAAS,EAAE,IAAI;cAAEnM,IAAI,EAAEA,IAAI,IAAI;YAAU,CAAC,CAAC;UACxE;QAAC;QAAA;UAAA,OAAAiM,SAAA,CAAA9H,IAAA;MAAA;IAAA,GAAA0H,QAAA;EAAA,CACF;EAAA,gBANYX,QAAQA,CAAAkB,GAAA,EAAAC,GAAA;IAAA,OAAAT,KAAA,CAAA9G,KAAA,OAAAD,SAAA;EAAA;AAAA,GAMpB;AAED,OAAO,IAAMyH,cAAc;EAAA,IAAAC,KAAA,GAAA3H,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiJ,SAAOxM,IAAI,EAAEyM,MAAM,EAAEC,GAAG,EAAEC,GAAG;IAAA,IAAAC,GAAA;IAAA,OAAAzO,mBAAA,GAAAuB,IAAA,UAAAmN,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA9I,IAAA,GAAA8I,SAAA,CAAAzK,IAAA;QAAA;UAAAyK,SAAA,CAAAzK,IAAA;UAAA,OACvC4C,GAAG,CAAC8H,SAAS,CAC7BL,GAAG,EACH;YACE1M,IAAI,EAAE,UAAU;YAChBgN,MAAM,EAAEL,GAAG,GAAGzH,KAAK,CAACsD,KAAK,CAACC,IAAI,CAACwE,SAAS;YACxCvE,QAAQ,EAAExD,KAAK,CAACsD,KAAK,CAACC,IAAI,CAACC,QAAQ;YACnCwE,YAAY,EAAEhI,KAAK,CAACsD,KAAK,CAACC,IAAI,CAAC0E,KAAK;YACpCC,WAAW,EAAEX,MAAM,KAAK,eAAe,GAAG,GAAG,GAAG;UAClD,CAAC,EACDzM,IACF,CAAC;QAAA;UAVK4M,GAAG,GAAAE,SAAA,CAAAhL,IAAA;UAWT,IAAI9B,IAAI,EAAE;YACRkF,KAAK,CAAC6B,MAAM,CAAC,mBAAmB,EAAE6F,GAAG,CAAC9G,IAAI,CAAC;UAC7C,CAAC,MAAM;YACLZ,KAAK,CAAC6B,MAAM,CAAC,mBAAmB,EAAE6F,GAAG,CAAClC,KAAK,CAAC;UAC9C;QAAC;QAAA;UAAA,OAAAoC,SAAA,CAAA3I,IAAA;MAAA;IAAA,GAAAqI,QAAA;EAAA,CACF;EAAA,gBAjBYF,cAAcA,CAAAe,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAjB,KAAA,CAAAzH,KAAA,OAAAD,SAAA;EAAA;AAAA,GAiB1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}