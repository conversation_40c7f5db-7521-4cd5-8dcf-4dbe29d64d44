{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestClueRegister\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"标题\",\n        \"min-width\": \"280\",\n        \"show-overflow-tooltip\": \"\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_link, {\n            onClick: function onClick($event) {\n              return $setup.handleDetail(scope.row);\n            },\n            type: \"primary\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString(scope.row.title), 1 /* TEXT */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"线索类别\",\n        \"min-width\": \"120\",\n        prop: \"proposalClueType.label\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"线索来源\",\n        width: \"120\",\n        prop: \"terminalName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"提供者\",\n        \"min-width\": \"120\",\n        prop: \"createByName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"提交时间\",\n        width: \"190\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString($setup.format(scope.row.createDate)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"采纳次数\",\n        width: \"120\",\n        prop: \"useTimes\"\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.detailsShow,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.detailsShow = $event;\n    }),\n    name: \"提案线索详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SuggestClueDetails\"], {\n        id: $setup.id\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "ref", "data", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "default", "_component_el_table_column", "type", "width", "fixed", "label", "scope", "_component_el_link", "onClick", "handleDetail", "row", "_createTextVNode", "_toDisplayString", "title", "prop", "format", "createDate", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "detailsShow", "name", "id"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\SuggestClueRegister.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestClueRegister\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column label=\"标题\"\r\n                         min-width=\"280\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            <el-link @click=\"handleDetail(scope.row)\"\r\n                     type=\"primary\">{{ scope.row.title }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"线索类别\"\r\n                         min-width=\"120\"\r\n                         prop=\"proposalClueType.label\" />\r\n        <el-table-column label=\"线索来源\"\r\n                         width=\"120\"\r\n                         prop=\"terminalName\" />\r\n        <el-table-column label=\"提供者\"\r\n                         min-width=\"120\"\r\n                         prop=\"createByName\" />\r\n        <el-table-column label=\"提交时间\"\r\n                         width=\"190\">\r\n          <template #default=\"scope\">{{ format(scope.row.createDate) }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"采纳次数\"\r\n                         width=\"120\"\r\n                         prop=\"useTimes\" />\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"detailsShow\"\r\n                      name=\"提案线索详情\">\r\n      <SuggestClueDetails :id=\"id\"></SuggestClueDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestClueRegister' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SuggestClueDetails from './components/SuggestClueDetails'\r\nimport { clueExportWord } from '@/assets/js/suggestExportWord'\r\nconst detailsShow = ref(false)\r\nconst id = ref('')\r\n\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleGetParams,\r\n} = GlobalTable({ tableApi: 'proposalClueList', tableDataObj: { query: { ifPublish: '1' } } })\r\n\r\nonActivated(() => {\r\n  handleQuery()\r\n})\r\n\r\nconst buttonList = [\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: 'export' },\r\n]\r\n\r\nconst handleDetail = (item) => {\r\n  id.value = item.id\r\n  console.log(id.value, 'id.value')\r\n  detailsShow.value = true\r\n}\r\n\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      clueExportWord(handleGetParams())\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestClueRegister {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAYzBA,KAAK,EAAC;AAAa;;EAoCnBA,KAAK,EAAC;AAAkB;;;;;;;;;uBAhD/BC,mBAAA,CA8DM,OA9DNC,UA8DM,GA7DJC,YAAA,CAUoBC,4BAAA;IAVAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IACvBC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IACvBC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC1BC,UAAU,EAAEN,MAAA,CAAAM;;IACnBC,MAAM,EAAAC,QAAA,CACf;MAAA,OAGsB,CAHtBX,YAAA,CAGsBY,mBAAA;QAV9BC,UAAA,EAO2BV,MAAA,CAAAW,OAAO;QAPlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAO2Bb,MAAA,CAAAW,OAAO,GAAAE,MAAA;QAAA;QAChBC,WAAW,EAAC,QAAQ;QACnBC,OAAK,EATxBC,SAAA,CASgChB,MAAA,CAAAC,WAAW;QACzBgB,SAAS,EAAT;;;IAVlBC,CAAA;uCAaIC,mBAAA,CAmCM,OAnCNC,UAmCM,GAlCJvB,YAAA,CAiCWwB,mBAAA;IAjCDC,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,IAAI;IACXC,IAAI,EAAEvB,MAAA,CAAAwB,SAAS;IACfC,QAAM,EAAEzB,MAAA,CAAA0B,iBAAiB;IACzBC,WAAU,EAAE3B,MAAA,CAAA0B;;IAlB7BE,OAAA,EAAApB,QAAA,CAmBQ;MAAA,OAGyB,CAHzBX,YAAA,CAGyBgC,0BAAA;QAHRC,IAAI,EAAC,WAAW;QAChB,mBAAiB,EAAjB,EAAiB;QACjBC,KAAK,EAAC,IAAI;QACVC,KAAK,EAAL;UACjBnC,YAAA,CAOkBgC,0BAAA;QAPDI,KAAK,EAAC,IAAI;QACV,WAAS,EAAC,KAAK;QACf,uBAAqB,EAArB;;QACJL,OAAO,EAAApB,QAAA,CAChB,UACuD0B,KAFhC;UAAA,QACvBrC,YAAA,CACuDsC,kBAAA;YAD7CC,OAAK,WAALA,OAAKA,CAAAvB,MAAA;cAAA,OAAEb,MAAA,CAAAqC,YAAY,CAACH,KAAK,CAACI,GAAG;YAAA;YAC9BR,IAAI,EAAC;;YA5B1BF,OAAA,EAAApB,QAAA,CA4BoC;cAAA,OAAqB,CA5BzD+B,gBAAA,CAAAC,gBAAA,CA4BuCN,KAAK,CAACI,GAAG,CAACG,KAAK,iB;;YA5BtDvB,CAAA;;;QAAAA,CAAA;UA+BQrB,YAAA,CAEiDgC,0BAAA;QAFhCI,KAAK,EAAC,MAAM;QACZ,WAAS,EAAC,KAAK;QACfS,IAAI,EAAC;UACtB7C,YAAA,CAEuCgC,0BAAA;QAFtBI,KAAK,EAAC,MAAM;QACZF,KAAK,EAAC,KAAK;QACXW,IAAI,EAAC;UACtB7C,YAAA,CAEuCgC,0BAAA;QAFtBI,KAAK,EAAC,KAAK;QACX,WAAS,EAAC,KAAK;QACfS,IAAI,EAAC;UACtB7C,YAAA,CAGkBgC,0BAAA;QAHDI,KAAK,EAAC,MAAM;QACZF,KAAK,EAAC;;QACVH,OAAO,EAAApB,QAAA,CAAS,UAAkC0B,KAApC;UAAA,QA1CnCK,gBAAA,CAAAC,gBAAA,CA0CwCxC,MAAA,CAAA2C,MAAM,CAACT,KAAK,CAACI,GAAG,CAACM,UAAU,kB;;QA1CnE1B,CAAA;UA4CQrB,YAAA,CAEmCgC,0BAAA;QAFlBI,KAAK,EAAC,MAAM;QACZF,KAAK,EAAC,KAAK;QACXW,IAAI,EAAC;;;IA9C9BxB,CAAA;4DAiDIC,mBAAA,CASM,OATN0B,UASM,GARJhD,YAAA,CAO4BiD,wBAAA;IAPLC,WAAW,EAAE/C,MAAA,CAAAgD,MAAM;IAlDhD,wBAAApC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAkD0Cb,MAAA,CAAAgD,MAAM,GAAAnC,MAAA;IAAA;IACnB,WAAS,EAAEb,MAAA,CAAAiD,QAAQ;IAnDhD,qBAAArC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAmDwCb,MAAA,CAAAiD,QAAQ,GAAApC,MAAA;IAAA;IAC1B,YAAU,EAAEb,MAAA,CAAAkD,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAEpD,MAAA,CAAAC,WAAW;IACxBoD,eAAc,EAAErD,MAAA,CAAAC,WAAW;IAC3BqD,KAAK,EAAEtD,MAAA,CAAAuD,MAAM;IACdC,UAAU,EAAV;qHAEjB3D,YAAA,CAGmB4D,2BAAA;IA9DvB/C,UAAA,EA2D+BV,MAAA,CAAA0D,WAAW;IA3D1C,uBAAA9C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2D+Bb,MAAA,CAAA0D,WAAW,GAAA7C,MAAA;IAAA;IACpB8C,IAAI,EAAC;;IA5D3B/B,OAAA,EAAApB,QAAA,CA6DM;MAAA,OAAkD,CAAlDX,YAAA,CAAkDG,MAAA;QAA7B4D,EAAE,EAAE5D,MAAA,CAAA4D;MAAE,gC;;IA7DjC1C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}