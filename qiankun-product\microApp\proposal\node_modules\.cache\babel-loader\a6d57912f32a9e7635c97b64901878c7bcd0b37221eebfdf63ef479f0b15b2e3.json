{"ast": null, "code": "import { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ScoreProportion\"\n};\nvar _hoisted_2 = {\n  class: \"globalPaperFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    border: \"\",\n    data: $setup.tableData\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        label: \"姓名\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\",\n        align: \"center\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString(scope.row.name), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"占比\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\",\n        align: \"center\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_input, {\n            modelValue: scope.row.zhanbi,\n            \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n              return scope.row.zhanbi = $event;\n            },\n            style: {\n              \"width\": \"100px\"\n            }\n          }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _cache[0] || (_cache[0] = _createTextVNode(\"  % \"))];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"]), _createCommentVNode(\" <p style=\\\"color: red;margin-top: 10px;font-size: 14px;\\\">占比小于100%，请调整分配比例</p> \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.confirm\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"确认\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    onClick: $setup.cancel\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_table", "ref", "border", "data", "$setup", "tableData", "default", "_withCtx", "_component_el_table_column", "label", "align", "scope", "_createTextVNode", "_toDisplayString", "row", "name", "_", "_component_el_input", "modelValue", "<PERSON><PERSON><PERSON>", "onUpdateModelValue", "$event", "style", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "confirm", "_cache", "cancel"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\ScoreProportion.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ScoreProportion\">\r\n    <el-table ref=\"tableRef\" row-key=\"id\" border :data=\"tableData\">\r\n      <el-table-column label=\"姓名\" min-width=\"120\" show-overflow-tooltip align=\"center\">\r\n        <template #default=\"scope\">{{ scope.row.name }}</template>\r\n      </el-table-column>\r\n      <el-table-column label=\"占比\" min-width=\"120\" show-overflow-tooltip align=\"center\">\r\n        <template #default=\"scope\">\r\n          <el-input v-model=\"scope.row.zhanbi\" style=\"width: 100px;\" />&nbsp;&nbsp;%\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- <p style=\"color: red;margin-top: 10px;font-size: 14px;\">占比小于100%，请调整分配比例</p> -->\r\n    <div class=\"globalPaperFormButton\">\r\n      <el-button type=\"primary\" @click=\"confirm\">确认</el-button>\r\n      <el-button @click=\"cancel\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ScoreProportion' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\n// import { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  data: {\r\n    type: Array,\r\n    default: () => []\r\n  }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst tableData = ref([])\r\n// const tableData = ref([\r\n//   { id: '1', name: '张三', zhanbi: '' },\r\n//   { id: '2', name: '李四', zhanbi: '' },\r\n//   { id: '3', name: '王五', zhanbi: '' }\r\n// ])\r\nonMounted(() => {\r\n  tableData.value = props.data.map(item => {\r\n    return {\r\n      id: item.id,\r\n      name: item.userName,\r\n      zhanbi: '',\r\n    }\r\n  })\r\n  console.log('tableData.value', tableData.value)\r\n})\r\n\r\n// 取消\r\nconst cancel = () => {\r\n  emit('callback')\r\n}\r\n// 确认\r\nconst confirm = async () => {\r\n  const { code } = await api.globalJson('/proposalAllocationScore/add', {\r\n    form: {\r\n      id: '',\r\n      suggestionId: '',\r\n      userId: '',\r\n      userName: '',\r\n      scoreProportion: ''\r\n    }\r\n  })\r\n  console.log('code===>', code)\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ScoreProportion {\r\n  width: 600px;\r\n  padding: 30px;\r\n\r\n  .globalPaperFormButton {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding-top: 22px;\r\n\r\n    .zy-el-button+.zy-el-button {\r\n      margin-left: var(--zy-distance-two);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAYrBA,KAAK,EAAC;AAAuB;;;;;;uBAZpCC,mBAAA,CAgBM,OAhBNC,UAgBM,GAfJC,YAAA,CASWC,mBAAA;IATDC,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAACC,MAAM,EAAN,EAAM;IAAEC,IAAI,EAAEC,MAAA,CAAAC;;IAFxDC,OAAA,EAAAC,QAAA,CAGM;MAAA,OAEkB,CAFlBR,YAAA,CAEkBS,0BAAA;QAFDC,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB,EAAqB;QAACC,KAAK,EAAC;;QAC3DJ,OAAO,EAAAC,QAAA,CAAS,UAAoBI,KAAtB;UAAA,QAJjCC,gBAAA,CAAAC,gBAAA,CAIsCF,KAAK,CAACG,GAAG,CAACC,IAAI,iB;;QAJpDC,CAAA;UAMMjB,YAAA,CAIkBS,0BAAA;QAJDC,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB,EAAqB;QAACC,KAAK,EAAC;;QAC3DJ,OAAO,EAAAC,QAAA,CAChB,UAA6DI,KADtC;UAAA,QACvBZ,YAAA,CAA6DkB,mBAAA;YARvEC,UAAA,EAQ6BP,KAAK,CAACG,GAAG,CAACK,MAAM;YAR7C,gCAAAC,mBAAAC,MAAA;cAAA,OAQ6BV,KAAK,CAACG,GAAG,CAACK,MAAM,GAAAE,MAAA;YAAA;YAAEC,KAAqB,EAArB;cAAA;YAAA;oGAR/CV,gBAAA,CAQuE,MAC/D,G;;QATRI,CAAA;;;IAAAA,CAAA;+BAYIO,mBAAA,oFAAqF,EACrFC,mBAAA,CAGM,OAHNC,UAGM,GAFJ1B,YAAA,CAAyD2B,oBAAA;IAA9CC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAExB,MAAA,CAAAyB;;IAdxCvB,OAAA,EAAAC,QAAA,CAciD;MAAA,OAAEuB,MAAA,QAAAA,MAAA,OAdnDlB,gBAAA,CAciD,IAAE,E;;IAdnDI,CAAA;MAeMjB,YAAA,CAAyC2B,oBAAA;IAA7BE,OAAK,EAAExB,MAAA,CAAA2B;EAAM;IAf/BzB,OAAA,EAAAC,QAAA,CAeiC;MAAA,OAAEuB,MAAA,QAAAA,MAAA,OAfnClB,gBAAA,CAeiC,IAAE,E;;IAfnCI,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}