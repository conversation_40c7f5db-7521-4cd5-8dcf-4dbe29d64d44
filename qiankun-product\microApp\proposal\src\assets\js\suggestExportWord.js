import api from '@/api'
import { exportWordHtmlList, exportWordHtmlObj, extendDownloadFile } from 'common/config/MicroGlobal'
import { ElMessage, ElMessageBox } from 'element-plus'
import { format } from 'common/js/time.js'
const whetherDataType = (obj) => {
  var toString = Object.prototype.toString
  var map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object'
  }
  return map[toString.call(obj)]
}
const group = (array, subGroupLength) => {
  let index = 0
  let newArray = []
  while (index < array.length) {
    newArray.push(array.slice(index, (index += subGroupLength)))
  }
  return newArray
}
export const filterTableData = (row) => {
  row.content = row.content.replace(/<p>/g, '<p style="font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;">');
  var rowObj = {}
  for (let key in row) {
    const type = whetherDataType(row[key])
    if (type === 'array') {
      rowObj[key] = row[key] || []
    } else if (type === 'object') {
      rowObj[key] = row[key] || {}
      rowObj[key + 'Name'] = row[key].dictName || row[key].name || ''
      rowObj[key + 'View'] = row[key].itemName || ''
    } else {
      rowObj[key] = row[key] || ''
    }
  }
  rowObj.docName = `${row.serialNumber || row.streamNumber}_${row.title}`
  if (row.title.length > 21) {
    rowObj.titleto = true
    rowObj.contentTitle = row.title.slice(0, 21)
    rowObj.contentTitle1 = row.title.slice(21)
  } else {
    rowObj.titleshowto = true
    rowObj.contentTitle = row.title
    rowObj.contentTitle1 = ''
  }
  if (row.title.length > 23) {
    rowObj.titleone = row.title.slice(0, 23)
    rowObj.titletwo = row.title.slice(23)
    rowObj.titletwoArr = group(row.title.slice(23), 28)
    rowObj.titleshow = true
  } else {
    rowObj.titleone = row.title
    rowObj.titleshow = false
  }
  if (row?.joinUsers?.length) {
    rowObj.joinUser = `${row.joinUsers[0].userName}（等${row.joinUsers.length}人）`
  } else {
    rowObj.joinUser = ''
  }
  if (row?.mainHandleOffice) {
    rowObj.direct = true
  } else if (row?.publishHandleOffice) {
    rowObj.jointly = true
  }
  return rowObj
}

const suggestionWord = async (params) => {
  const { data } = await api.suggestionWord({ ...params, isContainMerge: 1 })
  var wordData = []
  data.forEach(v => {
    v.handleType = v.handleType === 'publish' ? '分办' : '主办/协办'
    v.assistHandleOffice = v.handleType === '分办' ? v.publishHandleOffice : v.assistHandleOffice
    v.circlesTypeName = v.circlesType?.name
    v.boutTypeName = v.boutType?.name
    v.partyName = v.party?.name
  })
  for (let index = 0; index < data.length; index++) {
    wordData.push(filterTableData(data[index]))
  }
  const create = { url: '/proposal/loadDocAnswerZip', params: params }
  exportWordHtmlList({
    create: create,
    code: 'proposalDetails',
    name: '提案导出Word',
    key: 'content',
    wordNameKey: 'docName',
    data: wordData
  })
}

const suggestionContent = async (params) => {
  const { data } = await api.suggestionWord(params)
  var wordData = []
  for (let index = 0; index < data.length; index++) {
    wordData.push(filterTableData(data[index]))
  }
  if (wordData.length === 1) {
    exportWordHtmlObj({ code: 'proposalContentDetails', name: wordData[0].docName, key: 'content', data: wordData[0] })
  } else {
    exportWordHtmlList({
      code: 'proposalContentDetails',
      name: '提案导出正文',
      key: 'content',
      wordNameKey: 'docName',
      data: wordData
    })
  }
}

export const suggestExportWord = async (data, isOpen = false) => {
  if (data.selectId.length) {
    ElMessageBox.confirm('此操作将当前选中的提案导出word, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionWord(isOpen ? { ids: data.selectId, tableId: data.params.tableId } : { ids: data.selectId })
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消导出' })
      })
  } else {
    ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出所有数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionWord(data.params)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消导出' })
      })
  }
}
export const suggestExportContent = async (data) => {
  if (data.selectId.length) {
    ElMessageBox.confirm('此操作将当前选中的提案导出正文, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionContent({ ids: data.selectId })
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消导出' })
      })
  } else {
    ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出所有数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionContent(data.params)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消导出' })
      })
  }
}
export const clueExportWord = async (data) => {
  if (data.selectId.length) {
    ElMessageBox.confirm('此操作将当前选中的提案线索导出word, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        clueWord({ ids: data.selectId })
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消导出' })
      })
  } else {
    ElMessageBox.confirm('当前没有选择提案线索，是否根据列表筛选条件导出所有数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        clueWord(data.params)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消导出' })
      })
  }
}

const clueWord = async (params) => {
  const { data } = await api.clueWord(params)
  var wordData = []
  for (let index = 0; index < data.length; index++) {
    let rowData = data[index]
    rowData.proposalClueTypeLabel = ''
    rowData.createDate = format(rowData.createDate)
    rowData.furnishMobile = rowData.furnishMobile ? rowData.furnishMobile : ''
    if (rowData.proposalClueType) {
      rowData.proposalClueTypeLabel = rowData.proposalClueType.label
    }
    wordData.push(rowData)
  }
  exportWordHtmlList({
    code: 'proposalClueDetails',
    name: '提案线索导出Word',
    key: 'content',
    wordNameKey: 'title',
    data: wordData
  })
}

export const suggestExportAnswer = async (data) => {
  if (data.selectId.length) {
    ElMessageBox.confirm('此操作将当前选中的提案导出答复件, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionAnswer({ ids: data.selectId })
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消导出' })
      })
  } else {
    ElMessageBox.confirm('当前没有选择提案，是否根据列表筛选条件导出答复件?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionAnswer(data.params)
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消导出' })
      })
  }
}

const suggestionAnswer = (params) => {
  extendDownloadFile({
    url: '/proposal/loadDocAnswerZip',
    params: params,
    fileSize: 0,
    fileName: '提案答复件.zip',
    fileType: 'zip'
  })
}
