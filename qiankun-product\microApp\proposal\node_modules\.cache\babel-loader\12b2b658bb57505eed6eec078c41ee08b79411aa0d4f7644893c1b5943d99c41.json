{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createBlock as _createBlock, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, KeepAlive as _KeepAlive, resolveDirective as _resolveDirective } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SubmitSuggestBody\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  style: {\n    \"position\": \"absolute\",\n    \"right\": \"-13%\",\n    \"top\": \"0%\"\n  }\n};\nvar _hoisted_3 = {\n  class: \"SubmitSuggestNameBody\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  style: {\n    \"color\": \"red\",\n    \"margin\": \"0 20px\"\n  }\n};\nvar _hoisted_5 = {\n  key: 1,\n  style: {\n    \"color\": \"red\",\n    \"margin\": \"0 20px\"\n  }\n};\nvar _hoisted_6 = [\"innerHTML\"];\nvar _hoisted_7 = {\n  key: 5,\n  style: {\n    \"margin\": \"10px 40px 50px\"\n  }\n};\nvar _hoisted_8 = {\n  key: 0,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_9 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_10 = {\n  key: 1,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_11 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_12 = {\n  key: 2,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_13 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_14 = {\n  key: 3,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_15 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_16 = {\n  key: 4,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_17 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_18 = {\n  key: 5,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_19 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_20 = {\n  class: \"SubmitSuggestContactPersonItem row2\"\n};\nvar _hoisted_21 = {\n  class: \"SubmitSuggestContactPersonItem row2\"\n};\nvar _hoisted_22 = {\n  class: \"SubmitSuggestContactPersonItem row3\"\n};\nvar _hoisted_23 = {\n  class: \"SubmitSuggestContactPersonItem row1\"\n};\nvar _hoisted_24 = {\n  key: 6,\n  class: \"globalPaperFormButton\"\n};\nvar _hoisted_25 = {\n  key: 1,\n  class: \"SuggestSegmentation\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_input_select_person = _resolveComponent(\"input-select-person\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_simple_select_person = _resolveComponent(\"simple-select-person\");\n  var _component_intelligent_assistant = _resolveComponent(\"intelligent-assistant\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _component_CirclePlus = _resolveComponent(\"CirclePlus\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_Remove = _resolveComponent(\"Remove\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createBlock(_component_el_scrollbar, {\n    always: \"\",\n    class: \"SubmitSuggest\",\n    \"lement-loading-text\": $setup.loadingText\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [$setup.queryType == 'review' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createElementVNode(\"div\", {\n        class: \"detailsPrint\",\n        title: \"打印提案\",\n        onClick: $setup.handleSuggestPrint\n      }, \"打印提案\"), _createElementVNode(\"div\", {\n        class: \"detailsExportInfo\",\n        title: \"导出提案word\",\n        onClick: $setup.handleExportWord\n      }, \"导出提案word\")])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_3, [$setup.ProposalYear.description ? (_openBlock(), _createBlock($setup[\"DynamicTitle\"], {\n        key: 0,\n        templateCode: \"proposal_title\",\n        titles: $setup.ProposalYear.description\n      }, null, 8 /* PROPS */, [\"titles\"])) : (_openBlock(), _createBlock($setup[\"DynamicTitle\"], {\n        key: 1,\n        templateCode: \"proposal_title\"\n      }))]), _createVNode(_component_el_form, {\n        ref: \"formRef\",\n        model: $setup.form,\n        rules: $setup.rules,\n        inline: \"\",\n        \"show-message\": false,\n        class: \"globalPaperForm\"\n      }, {\n        default: _withCtx(function () {\n          return [!$setup.typeShow ? (_openBlock(), _createBlock(_component_el_form_item, {\n            key: 0,\n            label: \"提案提交类型\",\n            prop: \"suggestSubmitWay\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.suggestSubmitWay,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n                  return $setup.form.suggestSubmitWay = $event;\n                }),\n                onChange: $setup.submitTypeChange\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_el_radio, {\n                    label: \"cppcc_member\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[25] || (_cache[25] = [_createTextVNode(\"委员提案\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_el_radio, {\n                    label: \"team\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[26] || (_cache[26] = [_createTextVNode(\"集体提案\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  })];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n            label: \"提案标题\",\n            prop: \"title\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.title,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n                  return $setup.form.title = $event;\n                }),\n                placeholder: \"请输入提案标题\",\n                \"show-word-limit\": \"\",\n                maxlength: $setup.suggestTitleNumber,\n                clearable: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\", \"maxlength\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"提案者\",\n            prop: \"suggestUserId\",\n            class: \"SubmitSuggestLeft\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_input_select_person, {\n                modelValue: $setup.form.suggestUserId,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n                  return $setup.form.suggestUserId = $event;\n                }),\n                placeholder: \"请选择提案者\",\n                disabled: $setup.disabled,\n                tabCode: $setup.tabCode,\n                onCallback: $setup.userCallback\n              }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\", \"tabCode\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"委员证号\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.cardNumber,\n                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n                  return $setup.form.cardNumber = $event;\n                }),\n                disabled: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"界别\",\n            class: \"SubmitSuggestLeft\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.sectorType,\n                \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n                  return $setup.form.sectorType = $event;\n                }),\n                disabled: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"联系电话\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.mobile,\n                \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n                  return $setup.form.mobile = $event;\n                }),\n                disabled: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"通讯地址\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.callAddress,\n                \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n                  return $setup.form.callAddress = $event;\n                }),\n                disabled: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"提案者\",\n            prop: \"delegationId\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_select, {\n                modelValue: $setup.form.delegationId,\n                \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n                  return $setup.form.delegationId = $event;\n                }),\n                disabled: $setup.isDisabled,\n                placeholder: \"请选择集体提案单位\",\n                clearable: \"\"\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.delegationData, function (item) {\n                    return _openBlock(), _createBlock(_component_el_option, {\n                      key: item.id,\n                      label: item.name,\n                      value: item.id\n                    }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'team']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"主要撰稿人\",\n            prop: \"writerUserId\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_input_select_person, {\n                modelValue: $setup.form.writerUserId,\n                \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n                  return $setup.form.writerUserId = $event;\n                }),\n                placeholder: \"请选择主要撰稿人\",\n                disabled: $setup.disabled,\n                tabCode: $setup.tabCode,\n                onCallback: $setup.userCallback\n              }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\", \"tabCode\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'team' && ($setup.AreaId == '370500' || $setup.AreaId == '370523')]]), _createVNode(_component_el_form_item, {\n            label: \"是否联名提案\",\n            prop: \"isJoinProposal\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.isJoinProposal,\n                \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n                  return $setup.form.isJoinProposal = $event;\n                }),\n                onChange: $setup.JoinChange\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_el_radio, {\n                    label: 1\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[27] || (_cache[27] = [_createTextVNode(\"是\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_el_radio, {\n                    label: 0\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[28] || (_cache[28] = [_createTextVNode(\"否\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  })];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), $setup.form.isJoinProposal ? (_openBlock(), _createBlock(_component_el_form_item, {\n            key: 1,\n            label: \"提案联名人\",\n            prop: \"joinUsers\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_simple_select_person, {\n                modelValue: $setup.form.joinUsers,\n                \"onUpdate:modelValue\": _cache[10] || (_cache[10] = function ($event) {\n                  return $setup.form.joinUsers = $event;\n                }),\n                placeholder: \"请选择提案联名人\",\n                filterUser: $setup.form.suggestUserId ? [$setup.form.suggestUserId] : [],\n                tabCode: ['cppccMember'],\n                onCallback: $setup.unitedCallback\n              }, null, 8 /* PROPS */, [\"modelValue\", \"filterUser\"]), $setup.whetherUseIntelligentize && $setup.queryType !== 'review' ? (_openBlock(), _createBlock(_component_intelligent_assistant, {\n                key: 0,\n                elIsShow: $setup.elIsShow,\n                \"onUpdate:elIsShow\": _cache[11] || (_cache[11] = function ($event) {\n                  return $setup.elIsShow = $event;\n                }),\n                modelValue: $setup.visibleIsShow,\n                \"onUpdate:modelValue\": _cache[12] || (_cache[12] = function ($event) {\n                  return $setup.visibleIsShow = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode($setup[\"SuggestRecommendUser\"], {\n                    params: $setup.userParams,\n                    onCallback: $setup.userInitCallback,\n                    onSelect: $setup.userSelect\n                  }, null, 8 /* PROPS */, [\"params\"])];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"elIsShow\", \"modelValue\"])) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), $setup.scoreProportionData && $setup.scoreProportionData.length > 0 && $setup.isFlag && ($setup.AreaId == '370500' || $setup.AreaId == '370505' || $setup.AreaId == '370522') ? (_openBlock(), _createBlock(_component_el_form_item, {\n            key: 2,\n            label: \"得分占比分配\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_table, {\n                ref: \"tableRef\",\n                \"row-key\": \"id\",\n                border: \"\",\n                data: $setup.scoreProportionData,\n                style: {\n                  \"margin\": \"5px 20px\",\n                  \"border\": \"1px solid #ccc\"\n                }\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_el_table_column, {\n                    label: \"姓名\",\n                    \"min-width\": \"100\",\n                    \"show-overflow-tooltip\": \"\",\n                    align: \"center\"\n                  }, {\n                    default: _withCtx(function (scope) {\n                      return [_createTextVNode(_toDisplayString(scope.row.userName), 1 /* TEXT */)];\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_el_table_column, {\n                    label: \"占比\",\n                    \"min-width\": \"100\",\n                    \"show-overflow-tooltip\": \"\",\n                    align: \"center\"\n                  }, {\n                    default: _withCtx(function (scope) {\n                      return [_createVNode(_component_el_input, {\n                        modelValue: scope.row.scoreProportion,\n                        \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                          return scope.row.scoreProportion = $event;\n                        },\n                        style: {\n                          \"width\": \"100px\"\n                        },\n                        type: \"number\",\n                        onInput: function onInput($event) {\n                          return $setup.handleInput(scope.row);\n                        },\n                        onBlur: function onBlur($event) {\n                          return $setup.handleBlur(scope.row);\n                        }\n                      }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\", \"onInput\", \"onBlur\"]), _cache[29] || (_cache[29] = _createTextVNode(\"  % \"))];\n                    }),\n                    _: 1 /* STABLE */\n                  })];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"data\"]), $setup.totalExceeds ? (_openBlock(), _createElementBlock(\"p\", _hoisted_4, \"占比总和不能超过100%\")) : _createCommentVNode(\"v-if\", true), $setup.totalInsufficient ? (_openBlock(), _createElementBlock(\"p\", _hoisted_5, \"占比总和不足100%\")) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n            label: \"提案内容\",\n            prop: \"content\",\n            class: \"SubmitSuggestTitle SubmitSuggestButton\"\n          }, {\n            default: _withCtx(function () {\n              return [_createCommentVNode(\" <div class=\\\"SubmitSuggestContentNumber\\\">事实清楚，建议明确，不超过{{ suggestContentNumber }}字</div> \"), _cache[31] || (_cache[31] = _createElementVNode(\"div\", null, null, -1 /* HOISTED */)), $setup.whetherUseIntelligentize && $setup.queryType === 'review' && $setup.reviewShow ? (_openBlock(), _createBlock(_component_el_button, {\n                key: 0,\n                onClick: _cache[13] || (_cache[13] = function ($event) {\n                  return $setup.handleSimilarity(false);\n                }),\n                type: \"primary\"\n              }, {\n                default: _withCtx(function () {\n                  return _cache[30] || (_cache[30] = [_createTextVNode(\" 相似度查询 \")]);\n                }),\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          }), $setup.queryType !== 'review' ? (_openBlock(), _createBlock(_component_TinyMceEditor, {\n            key: 3,\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[14] || (_cache[14] = function ($event) {\n              return $setup.form.content = $event;\n            }),\n            setting: $setup.tinyMceSetting,\n            max_count: $setup.suggestContentNumber,\n            onCount: $setup.handleContentCount,\n            onBlur: $setup.handleContentBlur,\n            textRectify: \"\",\n            placeholder: `事实清楚，建议明确，不超过${$setup.suggestContentNumber}字`\n          }, null, 8 /* PROPS */, [\"modelValue\", \"max_count\", \"placeholder\"])) : (_openBlock(), _createElementBlock(\"div\", {\n            key: 4,\n            innerHTML: $setup.form.content,\n            class: \"content_box\"\n          }, null, 8 /* PROPS */, _hoisted_6)), $setup.queryType == 'review' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"div\", {\n            class: \"detailsPrints\",\n            title: \"打印提案\",\n            onClick: $setup.handleSuggestPrint\n          }, \"打印提案\"), _createElementVNode(\"div\", {\n            class: \"detailsExportInfos\",\n            title: \"导出提案word\",\n            onClick: $setup.handleExportWord\n          }, \"导出提案word\")])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" <el-form-item label=\\\"上传附件\\\" class=\\\"SubmitSuggestFormUpload\\\">\\r\\n          <xyl-upload-file :fileData=\\\"fileData\\\" @fileUpload=\\\"fileUpload\\\" />\\r\\n        </el-form-item> \"), _createVNode(_component_el_form_item, {\n            label: \"提案相关情况\",\n            class: \"SubmitSuggestFormItem\",\n            style: {\n              \"border-top\": \"1px solid var(--zy-el-color-primary)\"\n            }\n          }, {\n            default: _withCtx(function () {\n              return [$setup.suggestOpenTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.suggestOpenTypeName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.suggestOpenType,\n                \"onUpdate:modelValue\": _cache[15] || (_cache[15] = function ($event) {\n                  return $setup.form.suggestOpenType = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.suggestOpenType, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.suggestSurveyTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString($setup.suggestSurveyTypeName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.suggestSurveyType,\n                \"onUpdate:modelValue\": _cache[16] || (_cache[16] = function ($event) {\n                  return $setup.form.suggestSurveyType = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.suggestSurveyType, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.isMakeMineJobName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.isMakeMineJobName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.isMakeMineJob,\n                \"onUpdate:modelValue\": _cache[17] || (_cache[17] = function ($event) {\n                  return $setup.form.isMakeMineJob = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.isMakeMineJob, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.notHandleTimeTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.notHandleTimeTypeName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.notHandleTimeType,\n                \"onUpdate:modelValue\": _cache[18] || (_cache[18] = function ($event) {\n                  return $setup.form.notHandleTimeType = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.notHandleTimeType, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.isHopeEnhanceTalkName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString($setup.isHopeEnhanceTalkName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.isHopeEnhanceTalk,\n                \"onUpdate:modelValue\": _cache[19] || (_cache[19] = function ($event) {\n                  return $setup.form.isHopeEnhanceTalk = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.isHopeEnhanceTalk, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.isNeedPaperAnswerName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, _toDisplayString($setup.isNeedPaperAnswerName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.isNeedPaperAnswer,\n                \"onUpdate:modelValue\": _cache[20] || (_cache[20] = function ($event) {\n                  return $setup.form.isNeedPaperAnswer = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.isNeedPaperAnswer, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" <el-form-item label=\\\"希望送交办单位\\\" class=\\\"SubmitSuggestTitle\\\">\\r\\n          <suggest-simple-select-unit v-model=\\\"form.hopeHandleOfficeIds\\\"></suggest-simple-select-unit>\\r\\n        </el-form-item> \"), _createVNode(_component_el_form_item, {\n            class: \"SubmitSuggestContactPerson\",\n            label: \"提案联系人\"\n          }, {\n            default: _withCtx(function () {\n              return [_cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonHead\"\n              }, [_createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonItem row2\"\n              }, \"提案联系人姓名\"), _createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonItem row2\"\n              }, \"提案联系人电话\"), _createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonItem row3\"\n              }, \"联系人通讯地址\"), _createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonItem row1\"\n              }, \"操作\")], -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.contactPersonList, function (item) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  class: \"SubmitSuggestContactPersonBody\",\n                  key: item.id\n                }, [_createElementVNode(\"div\", _hoisted_20, [_createVNode(_component_el_input, {\n                  placeholder: \"请输入联系人姓名\",\n                  modelValue: item.contactName,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.contactName = $event;\n                  },\n                  clearable: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_input, {\n                  placeholder: \"请输入联系人电话\",\n                  modelValue: item.contactPhone,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.contactPhone = $event;\n                  },\n                  clearable: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_input, {\n                  placeholder: \"请输入联系人通讯地址\",\n                  modelValue: item.contactAddress,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.contactAddress = $event;\n                  },\n                  clearable: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_23, [$setup.contactPersonList.length ? (_openBlock(), _createBlock(_component_el_link, {\n                  key: 0,\n                  onClick: $setup.newContactPerson\n                }, {\n                  default: _withCtx(function () {\n                    return [_createVNode(_component_el_icon, null, {\n                      default: _withCtx(function () {\n                        return [_createVNode(_component_CirclePlus)];\n                      }),\n                      _: 1 /* STABLE */\n                    })];\n                  }),\n                  _: 1 /* STABLE */\n                })) : _createCommentVNode(\"v-if\", true), $setup.contactPersonList.length > 1 ? (_openBlock(), _createBlock(_component_el_link, {\n                  key: 1,\n                  onClick: function onClick($event) {\n                    return $setup.delContactPerson(item.id);\n                  }\n                }, {\n                  default: _withCtx(function () {\n                    return [_createVNode(_component_el_icon, null, {\n                      default: _withCtx(function () {\n                        return [_createVNode(_component_Remove)];\n                      }),\n                      _: 1 /* STABLE */\n                    })];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }), $setup.queryType !== 'review' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: _cache[21] || (_cache[21] = function ($event) {\n              return $setup.submitForm($setup.formRef, 0);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[33] || (_cache[33] = [_createTextVNode(\"提交提案\")]);\n            }),\n            _: 1 /* STABLE */\n          }), !$setup.route.query.anewId && !$setup.route.query.id || $setup.queryType === 'draft' ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 0,\n            onClick: _cache[22] || (_cache[22] = function ($event) {\n              return $setup.submitForm($setup.formRef, 1);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[34] || (_cache[34] = [_createTextVNode(\" 存为草稿 \")]);\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), !$setup.route.query.id ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 1,\n            onClick: $setup.resetForm\n          }, {\n            default: _withCtx(function () {\n              return _cache[35] || (_cache[35] = [_createTextVNode(\"重置\")]);\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), $setup.route.query.id ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 2,\n            onClick: $setup.resetForm\n          }, {\n            default: _withCtx(function () {\n              return _cache[36] || (_cache[36] = [_createTextVNode(\"取消\")]);\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\"]), $setup.queryType === 'review' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25)) : _createCommentVNode(\"v-if\", true), (_openBlock(), _createBlock(_KeepAlive, null, [$setup.queryType === 'review' && $setup.reviewShow ? (_openBlock(), _createBlock($setup[\"SuggestReviewDetail\"], {\n        key: 0,\n        id: $setup.route.query.id,\n        name: $setup.route.query.reviewName,\n        title: $setup.form.title,\n        content: $setup.form.content,\n        SuggestBigType: $setup.form.SuggestBigType,\n        SuggestSmallType: $setup.form.SuggestSmallType,\n        hopeHandleOfficeIds: $setup.form.hopeHandleOfficeIds,\n        onEditCallback: $setup.editCallback,\n        onCallback: $setup.resetForm\n      }, null, 8 /* PROPS */, [\"id\", \"name\", \"title\", \"content\", \"SuggestBigType\", \"SuggestSmallType\", \"hopeHandleOfficeIds\"])) : _createCommentVNode(\"v-if\", true)], 1024 /* DYNAMIC_SLOTS */))]), $setup.elPrintWhetherShow ? (_openBlock(), _createBlock($setup[\"suggestPrint\"], {\n        key: 0,\n        params: $setup.printParams,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"params\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.show,\n        \"onUpdate:modelValue\": _cache[23] || (_cache[23] = function ($event) {\n          return $setup.show = $event;\n        }),\n        name: \"相似度查询\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"SimilarityQuery\"], {\n            type: $setup.isShow,\n            id: $setup.route.query.id,\n            title: $setup.form.title,\n            content: $setup.form.content,\n            onCallback: $setup.handleSimilarityCallback\n          }, null, 8 /* PROPS */, [\"type\", \"id\", \"title\", \"content\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.unitedProportionShow,\n        \"onUpdate:modelValue\": _cache[24] || (_cache[24] = function ($event) {\n          return $setup.unitedProportionShow = $event;\n        }),\n        name: \"得分占比分配\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"ScoreProportion\"], {\n            data: $setup.scoreProportionData,\n            onCallback: _ctx.scoreProportionCallback\n          }, null, 8 /* PROPS */, [\"data\", \"onCallback\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"lement-loading-text\"])), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "key", "style", "_createBlock", "_component_el_scrollbar", "always", "$setup", "loadingText", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "queryType", "_createElementBlock", "_hoisted_2", "title", "onClick", "handleSuggestPrint", "handleExportWord", "_createCommentVNode", "_hoisted_3", "ProposalYear", "description", "templateCode", "titles", "_createVNode", "_component_el_form", "ref", "model", "form", "rules", "inline", "typeShow", "_component_el_form_item", "label", "prop", "_component_el_radio_group", "modelValue", "suggestSubmitWay", "_cache", "$event", "onChange", "submitTypeChange", "_component_el_radio", "_createTextVNode", "_", "_component_el_input", "placeholder", "maxlength", "suggestTitleNumber", "clearable", "_component_input_select_person", "suggestUserId", "disabled", "tabCode", "onCallback", "userCallback", "cardNumber", "sectorType", "mobile", "call<PERSON>dd<PERSON>", "_component_el_select", "delegationId", "isDisabled", "_Fragment", "_renderList", "delegationData", "item", "_component_el_option", "id", "name", "value", "writer<PERSON>serId", "AreaId", "isJoinProposal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_component_simple_select_person", "joinUsers", "filterUser", "unitedCallback", "whetherUseIntelligentize", "_component_intelligent_assistant", "elIsShow", "visibleIsShow", "params", "userParams", "userInitCallback", "onSelect", "userSelect", "scoreProportionData", "length", "isFlag", "_component_el_table", "border", "data", "_component_el_table_column", "align", "scope", "_toDisplayString", "row", "userName", "scoreProportion", "onUpdateModelValue", "type", "onInput", "handleInput", "onBlur", "handleBlur", "totalExceeds", "_hoisted_4", "totalInsufficient", "_hoisted_5", "reviewShow", "_component_el_button", "handleSimilarity", "_component_TinyMceEditor", "content", "setting", "tinyMceSetting", "max_count", "suggestContentNumber", "onCount", "handleContentCount", "handleContentBlur", "textRectify", "innerHTML", "_hoisted_6", "_hoisted_7", "suggestOpenTypeName", "_hoisted_8", "_hoisted_9", "suggestOpenType", "suggestSurveyTypeName", "_hoisted_10", "_hoisted_11", "suggestSurveyType", "isMakeMineJobName", "_hoisted_12", "_hoisted_13", "isMakeMineJob", "notHandleTimeTypeName", "_hoisted_14", "_hoisted_15", "notHandleTimeType", "isHopeEnhanceTalkName", "_hoisted_16", "_hoisted_17", "isHopeEnhanceTalk", "isNeedPaperAnswerName", "_hoisted_18", "_hoisted_19", "isNeedPaperAnswer", "contactPersonList", "_hoisted_20", "contactName", "_hoisted_21", "contactPhone", "_hoisted_22", "contactAddress", "_hoisted_23", "_component_el_link", "newContact<PERSON>erson", "_component_el_icon", "_component_CirclePlus", "delContact<PERSON>erson", "_component_Remove", "_hoisted_24", "submitForm", "formRef", "route", "query", "anewId", "resetForm", "_hoisted_25", "_KeepAlive", "reviewName", "SuggestBigType", "SuggestSmallType", "hopeHandleOfficeIds", "onEditCallback", "edit<PERSON>allback", "elPrintWhetherShow", "printParams", "callback", "_component_xyl_popup_window", "show", "isShow", "handleSimilarityCallback", "unitedProportionShow", "_ctx", "scoreProportionCallback", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\SubmitSuggest.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"SubmitSuggest\" v-loading=\"loading\" :lement-loading-text=\"loadingText\">\r\n    <div class=\"SubmitSuggestBody\">\r\n      <div style=\"position:absolute;right: -13%;top: 0%;\" v-if=\"queryType == 'review'\">\r\n        <div class=\"detailsPrint\" title=\"打印提案\" @click=\"handleSuggestPrint\">打印提案</div>\r\n        <div class=\"detailsExportInfo\" title=\"导出提案word\" @click=\"handleExportWord\">导出提案word</div>\r\n      </div>\r\n      <div class=\"SubmitSuggestNameBody\">\r\n        <dynamic-title templateCode=\"proposal_title\" v-if=\"ProposalYear.description\"\r\n          :titles=\"ProposalYear.description\"></dynamic-title>\r\n        <dynamic-title templateCode=\"proposal_title\" v-else></dynamic-title>\r\n      </div>\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline :show-message=\"false\" class=\"globalPaperForm\">\r\n        <el-form-item label=\"提案提交类型\" v-if=\"!typeShow\" prop=\"suggestSubmitWay\" class=\"SubmitSuggestTitle\">\r\n          <el-radio-group v-model=\"form.suggestSubmitWay\" @change=\"submitTypeChange\">\r\n            <el-radio label=\"cppcc_member\">委员提案</el-radio>\r\n            <el-radio label=\"team\">集体提案</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案标题\" prop=\"title\" class=\"SubmitSuggestTitle\">\r\n          <el-input v-model=\"form.title\" placeholder=\"请输入提案标题\" show-word-limit :maxlength=\"suggestTitleNumber\"\r\n            clearable />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"提案者\" prop=\"suggestUserId\"\r\n          class=\"SubmitSuggestLeft\">\r\n          <input-select-person v-model=\"form.suggestUserId\" placeholder=\"请选择提案者\" :disabled=\"disabled\" :tabCode=\"tabCode\"\r\n            @callback=\"userCallback\" />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"委员证号\">\r\n          <el-input v-model=\"form.cardNumber\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"界别\" class=\"SubmitSuggestLeft\">\r\n          <el-input v-model=\"form.sectorType\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"联系电话\">\r\n          <el-input v-model=\"form.mobile\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"通讯地址\" class=\"SubmitSuggestTitle\">\r\n          <el-input v-model=\"form.callAddress\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'team'\" label=\"提案者\" prop=\"delegationId\"\r\n          class=\"SubmitSuggestTitle\">\r\n          <el-select v-model=\"form.delegationId\" :disabled=\"isDisabled\" placeholder=\"请选择集体提案单位\" clearable>\r\n            <el-option v-for=\"item in delegationData\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'team' && (AreaId == '370500' || AreaId == '370523')\"\r\n          label=\"主要撰稿人\" prop=\"writerUserId\" class=\"SubmitSuggestTitle\">\r\n          <input-select-person v-model=\"form.writerUserId\" placeholder=\"请选择主要撰稿人\" :disabled=\"disabled\"\r\n            :tabCode=\"tabCode\" @callback=\"userCallback\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否联名提案\" prop=\"isJoinProposal\" class=\"SubmitSuggestTitle\">\r\n          <el-radio-group v-model=\"form.isJoinProposal\" @change=\"JoinChange\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案联名人\" prop=\"joinUsers\" v-if=\"form.isJoinProposal\" class=\"SubmitSuggestTitle\">\r\n          <simple-select-person v-model=\"form.joinUsers\" placeholder=\"请选择提案联名人\"\r\n            :filterUser=\"form.suggestUserId ? [form.suggestUserId] : []\" :tabCode=\"['cppccMember']\"\r\n            @callback=\"unitedCallback\"></simple-select-person>\r\n          <template v-if=\"whetherUseIntelligentize && queryType !== 'review'\">\r\n            <intelligent-assistant v-model:elIsShow=\"elIsShow\" v-model=\"visibleIsShow\">\r\n              <SuggestRecommendUser :params=\"userParams\" @callback=\"userInitCallback\" @select=\"userSelect\">\r\n              </SuggestRecommendUser>\r\n            </intelligent-assistant>\r\n          </template>\r\n        </el-form-item>\r\n        <el-form-item label=\"得分占比分配\"\r\n          v-if=\"scoreProportionData && scoreProportionData.length > 0 && isFlag && (AreaId == '370500' || AreaId == '370505' || AreaId == '370522')\"\r\n          class=\"SubmitSuggestTitle\">\r\n          <el-table ref=\"tableRef\" row-key=\"id\" border :data=\"scoreProportionData\"\r\n            style=\"margin: 5px 20px;border: 1px solid #ccc;\">\r\n            <el-table-column label=\"姓名\" min-width=\"100\" show-overflow-tooltip align=\"center\">\r\n              <template #default=\"scope\">{{ scope.row.userName }}</template>\r\n            </el-table-column>\r\n            <el-table-column label=\"占比\" min-width=\"100\" show-overflow-tooltip align=\"center\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.scoreProportion\" style=\"width: 100px;\" type=\"number\"\r\n                  @input=\"handleInput(scope.row)\" @blur=\"handleBlur(scope.row)\" />&nbsp;&nbsp;%\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <p v-if=\"totalExceeds\" style=\"color: red;margin:0 20px;\">占比总和不能超过100%</p>\r\n          <p v-if=\"totalInsufficient\" style=\"color: red;margin:0 20px;\">占比总和不足100%</p>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案内容\" prop=\"content\" class=\"SubmitSuggestTitle SubmitSuggestButton\">\r\n          <!-- <div class=\"SubmitSuggestContentNumber\">事实清楚，建议明确，不超过{{ suggestContentNumber }}字</div> -->\r\n          <div></div>\r\n          <el-button @click=\"handleSimilarity(false)\"\r\n            v-if=\"whetherUseIntelligentize && queryType === 'review' && reviewShow\" type=\"primary\">\r\n            相似度查询\r\n          </el-button>\r\n        </el-form-item>\r\n        <TinyMceEditor v-model=\"form.content\" :setting=\"tinyMceSetting\" :max_count=\"suggestContentNumber\"\r\n          @count=\"handleContentCount\" @blur=\"handleContentBlur\" v-if=\"queryType !== 'review'\" textRectify\r\n          :placeholder=\"`事实清楚，建议明确，不超过${suggestContentNumber}字`\" />\r\n        <div v-html=\"form.content\" v-else class=\"content_box\"></div>\r\n        <div style=\"margin:10px 40px 50px;\" v-if=\"queryType == 'review'\">\r\n          <div class=\"detailsPrints\" title=\"打印提案\" @click=\"handleSuggestPrint\">打印提案</div>\r\n          <div class=\"detailsExportInfos\" title=\"导出提案word\" @click=\"handleExportWord\">导出提案word</div>\r\n        </div>\r\n        <!-- <el-form-item label=\"上传附件\" class=\"SubmitSuggestFormUpload\">\r\n          <xyl-upload-file :fileData=\"fileData\" @fileUpload=\"fileUpload\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"提案相关情况\" class=\"SubmitSuggestFormItem\"\r\n          style=\"border-top: 1px solid var(--zy-el-color-primary);\">\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"suggestOpenTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ suggestOpenTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.suggestOpenType\">\r\n              <el-radio v-for=\"item in suggestOpenType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"suggestSurveyTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ suggestSurveyTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.suggestSurveyType\">\r\n              <el-radio v-for=\"item in suggestSurveyType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isMakeMineJobName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isMakeMineJobName }}：</div>\r\n            <el-radio-group v-model=\"form.isMakeMineJob\">\r\n              <el-radio v-for=\"item in isMakeMineJob\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"notHandleTimeTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ notHandleTimeTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.notHandleTimeType\">\r\n              <el-radio v-for=\"item in notHandleTimeType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isHopeEnhanceTalkName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isHopeEnhanceTalkName }}：</div>\r\n            <el-radio-group v-model=\"form.isHopeEnhanceTalk\">\r\n              <el-radio v-for=\"item in isHopeEnhanceTalk\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isNeedPaperAnswerName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isNeedPaperAnswerName }}：</div>\r\n            <el-radio-group v-model=\"form.isNeedPaperAnswer\">\r\n              <el-radio v-for=\"item in isNeedPaperAnswer\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"希望送交办单位\" class=\"SubmitSuggestTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.hopeHandleOfficeIds\"></suggest-simple-select-unit>\r\n        </el-form-item> -->\r\n        <el-form-item class=\"SubmitSuggestContactPerson\" label=\"提案联系人\">\r\n          <div class=\"SubmitSuggestContactPersonHead\">\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">提案联系人姓名</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">提案联系人电话</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row3\">联系人通讯地址</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row1\">操作</div>\r\n          </div>\r\n          <div class=\"SubmitSuggestContactPersonBody\" v-for=\"item in contactPersonList\" :key=\"item.id\">\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">\r\n              <el-input placeholder=\"请输入联系人姓名\" v-model=\"item.contactName\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">\r\n              <el-input placeholder=\"请输入联系人电话\" v-model=\"item.contactPhone\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row3\">\r\n              <el-input placeholder=\"请输入联系人通讯地址\" v-model=\"item.contactAddress\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row1\">\r\n              <el-link @click=\"newContactPerson\" v-if=\"contactPersonList.length\">\r\n                <el-icon>\r\n                  <CirclePlus />\r\n                </el-icon>\r\n              </el-link>\r\n              <el-link v-if=\"contactPersonList.length > 1\" @click=\"delContactPerson(item.id)\">\r\n                <el-icon>\r\n                  <Remove />\r\n                </el-icon>\r\n              </el-link>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <div class=\"globalPaperFormButton\" v-if=\"queryType !== 'review'\">\r\n          <el-button type=\"primary\" @click=\"submitForm(formRef, 0)\">提交提案</el-button>\r\n          <el-button @click=\"submitForm(formRef, 1)\"\r\n            v-if=\"(!route.query.anewId && !route.query.id) || queryType === 'draft'\">\r\n            存为草稿\r\n          </el-button>\r\n          <el-button @click=\"resetForm\" v-if=\"!route.query.id\">重置</el-button>\r\n          <el-button @click=\"resetForm\" v-if=\"route.query.id\">取消</el-button>\r\n        </div>\r\n      </el-form>\r\n      <div v-if=\"queryType === 'review'\" class=\"SuggestSegmentation\"></div>\r\n      <keep-alive>\r\n        <SuggestReviewDetail :id=\"route.query.id\" :name=\"route.query.reviewName\" :title=\"form.title\"\r\n          :content=\"form.content\" :SuggestBigType=\"form.SuggestBigType\" :SuggestSmallType=\"form.SuggestSmallType\"\r\n          :hopeHandleOfficeIds=\"form.hopeHandleOfficeIds\" v-if=\"queryType === 'review' && reviewShow\"\r\n          @editCallback=\"editCallback\" @callback=\"resetForm\"></SuggestReviewDetail>\r\n      </keep-alive>\r\n    </div>\r\n    <suggestPrint v-if=\"elPrintWhetherShow\" :params=\"printParams\" @callback=\"callback\"></suggestPrint>\r\n    <xyl-popup-window v-model=\"show\" name=\"相似度查询\">\r\n      <SimilarityQuery :type=\"isShow\" :id=\"route.query.id\" :title=\"form.title\" :content=\"form.content\"\r\n        @callback=\"handleSimilarityCallback\"></SimilarityQuery>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"unitedProportionShow\" name=\"得分占比分配\">\r\n      <ScoreProportion :data=\"scoreProportionData\" @callback=\"scoreProportionCallback\"></ScoreProportion>\r\n    </xyl-popup-window>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitSuggest' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, nextTick, watch } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { user, whetherUseIntelligentize } from 'common/js/system_var.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport SimilarityQuery from '@/components/SimilarityQuery/SimilarityQuery.vue'\r\nimport SuggestRecommendUser from '@/components/SuggestRecommendUser/SuggestRecommendUser.vue'\r\nimport DynamicTitle from '@/components/global-dynamic-title/global-dynamic-title.vue'\r\nimport SuggestReviewDetail from '@/views/SuggestReview/component/SuggestReviewDetail.vue'\r\nimport suggestPrint from '@/components/suggestPrint/suggestPrint'\r\nimport { filterTableData } from '@/assets/js/suggestExportWord'\r\nimport { exportWordHtmlObj } from 'common/config/MicroGlobal'\r\nimport ScoreProportion from './ScoreProportion.vue'\r\n\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst AreaId = ref(sessionStorage.getItem('AreaId'))\r\nconst formRef = ref()\r\nconst cachedScoreData = ref(null)\r\nconst isRequesting = ref(false)\r\nconst initialScoreData = ref(null)\r\nconst form = reactive({\r\n  suggestSubmitWay: 'cppcc_member',\r\n  title: '', // 提案标题\r\n  suggestUserId: '',\r\n  writerUserId: '',\r\n  cardNumber: '',\r\n  sectorType: '',\r\n  mobile: '',\r\n  callAddress: '',\r\n  delegationId: '',\r\n  isJoinProposal: 0,\r\n  joinUsers: [],\r\n  content: '',\r\n  suggestOpenType: 'open_all',\r\n  suggestSurveyType: '3',\r\n  isMakeMineJob: '1',\r\n  notHandleTimeType: '1',\r\n  isHopeEnhanceTalk: '1',\r\n  isNeedPaperAnswer: '1',\r\n  hopeHandleOfficeIds: []\r\n})\r\nconst rules = reactive({\r\n  suggestSubmitWay: [{ required: true, message: '请选择提案提交类型', trigger: ['blur', 'change'] }],\r\n  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }],\r\n  suggestUserId: [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }],\r\n  delegationId: [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }],\r\n  isJoinProposal: [{ required: true, message: '请选择是否联名提案', trigger: ['blur', 'change'] }],\r\n  joinUsers: [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst tinyMceSetting = {\r\n  tp_layout_options: {\r\n    style: {\r\n      'text-align': 'justify',\r\n      'text-indent': '2em',\r\n      'line-height': '28pt',\r\n      'font-size': '16pt',\r\n      'font-family': '仿宋_GB2312'\r\n    },\r\n    tagsStyle: {\r\n      span: {\r\n        'text-align': 'justify',\r\n        'text-indent': '2em',\r\n        'line-height': '28pt',\r\n        'font-size': '16pt',\r\n        'font-family': '仿宋_GB2312'\r\n      }\r\n    }\r\n  },\r\n  contextmenu: false,\r\n  paste_postprocess: (plugin, args) => {\r\n    nextTick(() => {\r\n      args.target.execCommand('mceTpLayout')\r\n      nextTick(() => {\r\n        args.target.selection.collapse()\r\n      })\r\n    })\r\n  },\r\n  import_word_callback: (editor) => {\r\n    nextTick(() => {\r\n      editor.execCommand('mceTpLayout')\r\n      nextTick(() => {\r\n        editor.selection.collapse()\r\n      })\r\n    })\r\n  }\r\n}\r\nconst suggestTitleNumber = ref(30)\r\nconst suggestContentNumber = ref(2000)\r\nconst suggestMinSimilar = ref(0)\r\nconst termYearId = ref('')\r\nconst contentCount = ref(0)\r\nconst fileData = ref([])\r\nconst delegationData = ref([])\r\nconst suggestOpenTypeName = ref('')\r\nconst suggestSurveyTypeName = ref('')\r\nconst notHandleTimeTypeName = ref('')\r\nconst isHopeEnhanceTalkName = ref('')\r\nconst isMakeMineJobName = ref('')\r\nconst isNeedPaperAnswerName = ref('')\r\nconst suggestOpenType = ref([])\r\nconst suggestSurveyType = ref([])\r\nconst notHandleTimeType = ref([])\r\nconst isHopeEnhanceTalk = ref([])\r\nconst isMakeMineJob = ref([])\r\nconst isNeedPaperAnswer = ref([])\r\nconst contactPersonList = ref([{ id: guid(), contactName: '', contactPhone: '', contactAddress: '' }])\r\nconst typeShow = ref(false)\r\nconst disabled = ref(false)\r\nconst isDisabled = ref(false)\r\nconst tabCode = ref(['cppccMember'])\r\nconst reviewShow = ref(false)\r\nconst queryType = ref('')\r\n\r\nconst show = ref(false)\r\nconst isShow = ref(false)\r\nconst elIsShow = ref(false)\r\nconst visibleIsShow = ref(false)\r\nconst userParams = ref({})\r\nconst ProposalYear = ref({})\r\n\r\nconst printParams = ref({})\r\nconst elPrintWhetherShow = ref(false)\r\n\r\nconst unitedProportionShow = ref(false)\r\nconst scoreProportionData = ref([])\r\nconst totalExceeds = ref(false)\r\nconst totalInsufficient = ref(false)\r\nconst isFlag = ref(false)\r\nconst hasLoadedScoreData = ref(false)\r\nlet timers = null\r\nconst debounce = (fn, delay) => {\r\n  let timer = null\r\n  return function (...args) {\r\n    if (timer) clearTimeout(timer)\r\n    timer = setTimeout(() => {\r\n      fn(...args)\r\n    }, delay)\r\n  }\r\n}\r\n\r\nconst handleInput = debounce((row) => {\r\n  if (row.scoreProportion !== undefined && !/^[0-9]*$/.test(row.scoreProportion)) {\r\n    row.scoreProportion = row.scoreProportion.replace(/[^0-9]/g, '')\r\n  }\r\n}, 300)\r\n\r\nconst handleBlur = debounce((row) => {\r\n  let filledTotal = 0\r\n  let unfilledCount = 0\r\n  scoreProportionData.value.forEach(item => {\r\n    if (item.scoreProportion && !isNaN(item.scoreProportion)) {\r\n      filledTotal += parseInt(item.scoreProportion, 10)\r\n    } else {\r\n      unfilledCount++\r\n    }\r\n  })\r\n\r\n  if (filledTotal > 100) {\r\n    totalExceeds.value = true\r\n    totalInsufficient.value = false\r\n    row.scoreProportion = ''\r\n    return\r\n  }\r\n\r\n  if (unfilledCount > 0) {\r\n    const remaining = 100 - filledTotal\r\n    if (remaining < 0) {\r\n      totalExceeds.value = true\r\n      totalInsufficient.value = false\r\n      row.scoreProportion = ''\r\n      return\r\n    }\r\n    if (unfilledCount === 1) {\r\n      scoreProportionData.value.forEach(item => {\r\n        if (!item.scoreProportion || isNaN(item.scoreProportion)) {\r\n          item.scoreProportion = remaining.toString()\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  if (filledTotal < 100 && unfilledCount === 0) {\r\n    totalInsufficient.value = true\r\n    totalExceeds.value = false\r\n  } else {\r\n    totalInsufficient.value = false\r\n  }\r\n\r\n  if (filledTotal === 100 && unfilledCount === 0) {\r\n    totalExceeds.value = false\r\n    totalInsufficient.value = false\r\n  }\r\n}, 300)\r\n\r\n// const debouncedUpdateScoreProportion = debounce(() => {\r\n//   updateScoreProportionList()\r\n// }, 300)\r\n\r\nonActivated(() => {\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'ai-intelligent-write-chat' })\r\n  const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''\r\n  if (openAiParams) {\r\n    qiankunMicro.setGlobalState({\r\n      AiChatConfig: {\r\n        AiChatWindow: true,\r\n        AiChatFile: openAiParams.fileData,\r\n        AiChatParams: { tool: openAiParams.toolId, param: { isPage: '1' } },\r\n        AiChatSendMessage: openAiParams.toolContent\r\n      }\r\n    })\r\n    sessionStorage.setItem('openAiParams', JSON.stringify(''))\r\n    timers = setTimeout(() => {\r\n      qiankunMicro.setGlobalState({\r\n        AiChatConfig: {\r\n          AiChatWindow: true,\r\n          AiChatFile: openAiParams.fileData,\r\n          AiChatParams: {}\r\n        }\r\n      })\r\n    }, 2000)\r\n  }\r\n  queryType.value = route.query.type\r\n  if (route.query.clueListId) {\r\n    proposalClueInfo()\r\n  }\r\n  globalReadConfig()\r\n  termYearCurrent()\r\n  dictionaryData()\r\n  dictionaryNameData()\r\n  getProposalYear()\r\n  if (queryType.value === 'draft' || route.query.anewId) {\r\n    typeShow.value = true\r\n    disabled.value = true\r\n  }\r\n  if (route.query.id || route.query.anewId) {\r\n    typeShow.value = true\r\n    suggestionInfo()\r\n  } else {\r\n    tabCode.value = ['cppccMember']\r\n    if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n      typeShow.value = true\r\n    }\r\n    if (user.value.specialRoleKeys.includes('cppcc_member')) {\r\n      typeShow.value = true\r\n      disabled.value = true\r\n      form.suggestUserId = user.value.id\r\n      cppccMemberInfo(user.value.id)\r\n    } else {\r\n      if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n        form.suggestSubmitWay = 'team'\r\n      }\r\n    }\r\n    if (\r\n      user.value.specialRoleKeys.includes('team_office_user') &&\r\n      user.value.specialRoleKeys.includes('cppcc_member')\r\n    ) {\r\n      typeShow.value = false\r\n    }\r\n    if (user.value.specialRoleKeys.includes('admin')) {\r\n      form.suggestSubmitWay = 'cppcc_member'\r\n      typeShow.value = false\r\n      disabled.value = false\r\n      teamOfficeSelect({})\r\n    } else {\r\n      if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n        teamOfficeSelect({ isSelectMine: 1 })\r\n      } else {\r\n        teamOfficeSelect({})\r\n      }\r\n    }\r\n    submitTypeChange()\r\n  }\r\n  if (!route.query.id) {\r\n    cachedScoreData.value = null\r\n    initialScoreData.value = null\r\n    hasLoadedScoreData.value = false\r\n  }\r\n})\r\nonDeactivated(() => {\r\n  if (timers) {\r\n    clearTimeout(timers)\r\n    timers = null\r\n  }\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({\r\n    AiChatConfig: {\r\n      AiChatWindow: false,\r\n      AiChatFile: [],\r\n      AiChatParams: {}\r\n    }\r\n  })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n})\r\nonBeforeUnmount(() => {\r\n  if (timers) {\r\n    clearTimeout(timers)\r\n    timers = null\r\n  }\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({\r\n    AiChatConfig: {\r\n      AiChatWindow: false,\r\n      AiChatFile: [],\r\n      AiChatParams: {}\r\n    }\r\n  })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n})\r\n\r\nconst updateScoreProportionList = async () => {\r\n  if (isRequesting.value) {\r\n    return\r\n  }\r\n\r\n  const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId\r\n  if (!proposerId) {\r\n    isFlag.value = false\r\n    return\r\n  }\r\n\r\n  if (route.query.id && !hasLoadedScoreData.value) {\r\n    try {\r\n      isRequesting.value = true\r\n      const res = await api.globalJson('/proposalAllocationScore/info', {\r\n        detailId: route.query.id\r\n      })\r\n      if (res.data && res.data.length > 0) {\r\n        initialScoreData.value = res.data\r\n        cachedScoreData.value = res.data\r\n        scoreProportionData.value = res.data\r\n        isFlag.value = true\r\n        hasLoadedScoreData.value = true\r\n        return\r\n      }\r\n    } catch (error) {\r\n      if (error.code !== 'ERR_CANCELED') {\r\n        console.error('获取得分占比数据失败:', error)\r\n      }\r\n    } finally {\r\n      isRequesting.value = false\r\n    }\r\n  }\r\n\r\n  if (scoreProportionData.value.length > 0 && hasLoadedScoreData.value) {\r\n    return\r\n  }\r\n\r\n  let newList = []\r\n\r\n  const existingProposer = scoreProportionData.value.find(person => person.id === proposerId)\r\n  if (existingProposer) {\r\n    newList.push({\r\n      ...existingProposer,\r\n      scoreProportion: existingProposer.scoreProportion || ''\r\n    })\r\n  } else {\r\n    try {\r\n      isRequesting.value = true\r\n      const { data } = await api.cppccMemberInfo({ detailId: proposerId })\r\n      newList.push({\r\n        id: proposerId,\r\n        userId: proposerId,\r\n        userName: data.userName,\r\n        scoreProportion: ''\r\n      })\r\n    } catch (error) {\r\n      console.error('获取提案者信息失败:', error)\r\n    } finally {\r\n      isRequesting.value = false\r\n    }\r\n  }\r\n\r\n  if (form.joinUsers && form.joinUsers.length > 0) {\r\n    const existingJoinUsers = scoreProportionData.value.filter(person =>\r\n      form.joinUsers.includes(person.id)\r\n    )\r\n\r\n    const newJoinUserIds = form.joinUsers.filter(id =>\r\n      !existingJoinUsers.some(user => user.id === id)\r\n    )\r\n\r\n    if (newJoinUserIds.length > 0) {\r\n      try {\r\n        isRequesting.value = true\r\n        const newJoinUsersInfo = await Promise.all(\r\n          newJoinUserIds.map(async (userId) => {\r\n            const { data } = await api.cppccMemberInfo({ detailId: userId })\r\n            return {\r\n              id: userId,\r\n              userId: userId,\r\n              userName: data.userName,\r\n              scoreProportion: ''\r\n            }\r\n          })\r\n        )\r\n        newList = [...newList, ...existingJoinUsers, ...newJoinUsersInfo]\r\n      } catch (error) {\r\n        console.error('获取联名人信息失败:', error)\r\n      } finally {\r\n        isRequesting.value = false\r\n      }\r\n    } else {\r\n      newList = [...newList, ...existingJoinUsers]\r\n    }\r\n  }\r\n\r\n  const updatedList = newList.map(newItem => {\r\n    const initialItem = initialScoreData.value?.find(item => item.id === newItem.id)\r\n    const existingItem = scoreProportionData.value.find(item => item.id === newItem.id)\r\n    return {\r\n      ...newItem,\r\n      scoreProportion: initialItem?.scoreProportion || existingItem?.scoreProportion || newItem.scoreProportion\r\n    }\r\n  })\r\n\r\n  scoreProportionData.value = updatedList\r\n  isFlag.value = true\r\n  hasLoadedScoreData.value = true\r\n}\r\n\r\nconst handleExportWord = () => {\r\n  if (!form.content) return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行导出！' })\r\n  suggestionWord({ ids: [route.query.id] })\r\n}\r\nconst handleSuggestPrint = async () => {\r\n  if (!form.content) return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行打印！' })\r\n  printParams.value = { ids: [route.query.id] }\r\n  elPrintWhetherShow.value = true\r\n}\r\nconst callback = (type) => {\r\n  elPrintWhetherShow.value = false\r\n  if (type) {\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  }\r\n}\r\nconst suggestionWord = async (params) => {\r\n  const { data } = await api.suggestionWord(params)\r\n  if (data.length) {\r\n    var wordData = {}\r\n    for (let index = 0; index < data.length; index++) {\r\n      wordData = filterTableData(data[index])\r\n    }\r\n    exportWordHtmlObj({ code: 'proposalDetails', name: wordData.docName, key: 'content', data: wordData })\r\n  }\r\n}\r\nconst handleSimilarity = (isType) => {\r\n  if (!form.content) return ElMessage({ type: 'warning', message: '请输入提案内容进行相似度查询！' })\r\n  sessionStorage.setItem('TextQueryToolTitle', form.title)\r\n  sessionStorage.setItem('TextQueryToolContent', form.content)\r\n  isShow.value = isType\r\n  show.value = true\r\n}\r\nconst handleSimilarityCallback = (type) => {\r\n  if (type) globalJson(0)\r\n  show.value = false\r\n}\r\nconst handleContentBlur = () => {\r\n  userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n}\r\nconst userInitCallback = (isElIsShow, isVisibleIsShow) => {\r\n  elIsShow.value = isElIsShow\r\n  visibleIsShow.value = isVisibleIsShow\r\n}\r\nconst userSelect = (item) => {\r\n  if (!form.joinUsers.includes(item.id)) {\r\n    form.joinUsers = [...form.joinUsers, item.id]\r\n  }\r\n}\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({\r\n    codes: ['suggestTitleNumber', 'suggestContentNumber', 'suggestMinSimilar']\r\n  })\r\n  if (data.suggestTitleNumber) {\r\n    suggestTitleNumber.value = Number(data.suggestTitleNumber)\r\n  }\r\n  if (data.suggestContentNumber) {\r\n    suggestContentNumber.value = Number(data.suggestContentNumber)\r\n  }\r\n  if (data.suggestMinSimilar) {\r\n    suggestMinSimilar.value = Number(data.suggestMinSimilar)\r\n  }\r\n}\r\nconst getProposalYear = async () => {\r\n  const { data } = await api.getProposalYear()\r\n  ProposalYear.value = data.length ? data[0] : {}\r\n}\r\nconst termYearCurrent = async () => {\r\n  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })\r\n  termYearId.value = data.id\r\n}\r\nconst dictionaryData = async () => {\r\n  const { data } = await api.dictionaryData({\r\n    dictCodes: [\r\n      'suggest_open_type',\r\n      'suggest_survey_type',\r\n      'not_handle_time_type',\r\n      'is_hope_enhance_talk',\r\n      'is_make_mine_job',\r\n      'is_need_paper_answer'\r\n    ]\r\n  })\r\n  suggestOpenType.value = data.suggest_open_type\r\n  suggestSurveyType.value = data.suggest_survey_type\r\n  notHandleTimeType.value = data.not_handle_time_type\r\n  isHopeEnhanceTalk.value = data.is_hope_enhance_talk\r\n  isMakeMineJob.value = data.is_make_mine_job\r\n  isNeedPaperAnswer.value = data.is_need_paper_answer\r\n}\r\nconst dictionaryNameData = async () => {\r\n  const { data } = await api.dictionaryNameData({\r\n    dictCodes: [\r\n      'suggest_open_type',\r\n      'suggest_survey_type',\r\n      'not_handle_time_type',\r\n      'is_hope_enhance_talk',\r\n      'is_make_mine_job',\r\n      'is_need_paper_answer'\r\n    ]\r\n  })\r\n  suggestOpenTypeName.value = data.suggest_open_type\r\n  suggestSurveyTypeName.value = data.suggest_survey_type\r\n  notHandleTimeTypeName.value = data.not_handle_time_type\r\n  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk\r\n  isMakeMineJobName.value = data.is_make_mine_job\r\n  isNeedPaperAnswerName.value = data.is_need_paper_answer\r\n}\r\nconst proposalClueInfo = async () => {\r\n  const { data } = await api.proposalClueInfo({ detailId: route.query.clueListId })\r\n  form.title = data.title\r\n  form.content = data.content\r\n}\r\nconst isLock = ref(false)\r\nconst lockVo = ref({})\r\nconst suggestionInfo = async () => {\r\n  try {\r\n    const res = await api.suggestionInfo({\r\n      detailId: route.query.id || route.query.anewId,\r\n      isOpenWithLock: queryType.value === 'review' ? 1 : null\r\n    })\r\n    var { data } = res\r\n    reviewShow.value = true\r\n    lockVo.value = data.lockVo\r\n    isLock.value = route.query.type == 'review' && data.lockVo.isLock == 1 && data.lockVo.lockUserId != user.value.id\r\n    form.suggestSubmitWay = data.suggestSubmitWay\r\n    if (form.suggestSubmitWay === 'cppcc_member') {\r\n      tabCode.value = ['cppccMember']\r\n      form.suggestUserId = data.suggestUserId\r\n      if (data.suggestUserId) {\r\n        cppccMemberInfo(data.suggestUserId)\r\n      }\r\n    }\r\n    if (form.suggestSubmitWay === 'team') {\r\n      isDisabled.value = true\r\n      form.delegationId = data.delegationId\r\n      delegationData.value = data.delegationId ? [{ id: data.delegationId, name: data.delegationName }] : []\r\n    }\r\n    submitTypeChange()\r\n    form.title = data.title\r\n    form.SuggestBigType = data.bigThemeId\r\n    form.SuggestSmallType = data.smallThemeId\r\n    // SuggestBigTypeChange()\r\n    form.termYearId = data.termYearId\r\n    form.content = data.content.replace(/<p>/g, '<p style=\"font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;\">');\r\n    handleContentBlur()\r\n    form.isJoinProposal = data.isJoinProposal\r\n    JoinChange()\r\n    form.suggestOpenType = data.suggestOpenType?.value\r\n    form.suggestSurveyType = data.suggestSurveyType?.value\r\n    form.isMakeMineJob = data.isMakeMineJob?.value\r\n    form.notHandleTimeType = data.notHandleTimeType?.value\r\n    form.isHopeEnhanceTalk = data.isHopeEnhanceTalk?.value\r\n    form.isNeedPaperAnswer = data.isNeedPaperAnswer?.value\r\n    form.writerUserId = data.jordan\r\n    fileData.value = data.attachments || []\r\n    form.hopeHandleOfficeIds = data.hopeHandleOfficeIds?.map((v) => v.officeId) || []\r\n    form.joinUsers = data.joinUsers?.map((v) => v.userId) || []\r\n    if (data.contacters?.length) {\r\n      contactPersonList.value = data.contacters.map((v) => ({\r\n        id: v.id,\r\n        contactName: v.contacterName,\r\n        contactPhone: v.contacterMobile,\r\n        contactAddress: v.contacterAddress\r\n      }))\r\n    }\r\n    userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n\r\n    if (form.isJoinProposal && (form.suggestUserId || form.writerUserId)) {\r\n      isFlag.value = true\r\n      hasLoadedScoreData.value = false\r\n      await updateScoreProportionList()\r\n    }\r\n  } catch (err) {\r\n    if (err.code === 500) {\r\n      if (route.query.id && queryType.value === 'review') {\r\n        reviewShow.value = false\r\n        qiankunMicro.setGlobalState({\r\n          closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\nconst submitTypeChange = () => {\r\n  if (form.suggestSubmitWay === 'cppcc_member') {\r\n    rules.suggestUserId = [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }]\r\n    rules.delegationId = [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.suggestSubmitWay === 'team') {\r\n    rules.suggestUserId = [{ required: false, message: '请选择提案者', trigger: ['blur', 'change'] }]\r\n    rules.delegationId = [{ required: true, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]\r\n  }\r\n  form.joinUsers = []\r\n  scoreProportionData.value = []\r\n  isFlag.value = false\r\n}\r\nconst JoinChange = () => {\r\n  if (form.isJoinProposal) {\r\n    rules.joinUsers = [{ type: 'array', required: true, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.joinUsers = [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n    scoreProportionData.value = []\r\n    isFlag.value = false\r\n  }\r\n}\r\nconst handleContentCount = (count) => {\r\n  contentCount.value = count\r\n}\r\nwatch(() => form.suggestUserId, (newValue, oldValue) => {\r\n  if (newValue !== oldValue) {\r\n    const newData = scoreProportionData.value.filter(\r\n      person => String(person.id) !== String(oldValue)\r\n    )\r\n    scoreProportionData.value = newData\r\n  }\r\n}\r\n)\r\nwatch(() => form.writerUserId, (newValue, oldValue) => {\r\n  if (newValue !== oldValue) {\r\n    const newData = scoreProportionData.value.filter(\r\n      person => String(person.id) !== String(oldValue)\r\n    )\r\n    scoreProportionData.value = newData\r\n  }\r\n}\r\n)\r\nconst userCallback = async (data) => {\r\n  if (data) {\r\n    cppccMemberInfo(data.id)\r\n    form.joinUsers = form.joinUsers.filter((v) => v !== data.id)\r\n    if (form.isJoinProposal && form.joinUsers.length > 0) {\r\n      await updateScoreProportionList()\r\n    }\r\n  } else {\r\n    form.cardNumber = ''\r\n    form.sectorType = ''\r\n    form.mobile = ''\r\n    form.callAddress = ''\r\n    scoreProportionData.value = []\r\n    isFlag.value = false\r\n  }\r\n  userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n}\r\nconst cppccMemberInfo = async (userId) => {\r\n  const { data } = await api.cppccMemberInfo({ detailId: userId })\r\n  form.cardNumber = data.cardNumberCppcc\r\n  form.sectorType = data.sectorType?.label\r\n  form.mobile = data.mobile\r\n  form.callAddress = data.callAddress\r\n}\r\nconst teamOfficeSelect = async (params) => {\r\n  const { data } = await api.teamOfficeSelect(params)\r\n  if (data.length) {\r\n    if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n      isDisabled.value = true\r\n      form.delegationId = data[0].id\r\n    }\r\n  }\r\n  delegationData.value = data\r\n}\r\nconst newContactPerson = () => {\r\n  contactPersonList.value.push({ id: guid(), contactName: '', contactPhone: '', contactAddress: '' })\r\n}\r\nconst delContactPerson = (id) => {\r\n  contactPersonList.value = contactPersonList.value.filter((v) => v.id !== id)\r\n}\r\nconst submitForm = async (formEl, type, cb) => {\r\n  if (!formEl) return\r\n  if (contentCount.value > suggestContentNumber.value) {\r\n    ElMessage({ type: 'warning', message: `当前输入的提案内容超过了${suggestContentNumber.value}字，不允许提交！` })\r\n    return\r\n  }\r\n  if (contentCount.value < 200 && !queryType.value) {\r\n    ElMessage({ type: 'warning', message: '提案字数不得少于200字！' })\r\n    return\r\n  }\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      if (whetherUseIntelligentize.value && !cb) {\r\n        if (type) {\r\n          globalJson(type, cb)\r\n        } else {\r\n          ElMessageBox.confirm('系统将为您进行相似度查询，是否同意执行操作？', '提示', {\r\n            closeOnClickModal: false,\r\n            confirmButtonText: '同意',\r\n            cancelButtonText: '跳过'\r\n          })\r\n            .then(() => {\r\n              handleSimilarity(true)\r\n            })\r\n            .catch(() => {\r\n              globalJson(type, cb)\r\n            })\r\n        }\r\n      } else {\r\n        globalJson(type, cb)\r\n      }\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\nconst editCallback = (cb) => {\r\n  submitForm(formRef.value, 0, cb)\r\n}\r\nconst globalJson = async (type, cb) => {\r\n  const resultScoreProportion = scoreProportionData.value.map(item => ({\r\n    userId: item.userId || item.id,\r\n    scoreProportion: item.scoreProportion\r\n  }))\r\n  try {\r\n    const { code } = await api.globalJson(route.query.id ? '/proposal/edit' : '/proposal/add', {\r\n      form: {\r\n        id: route.query.id,\r\n        suggestSubmitWay: form.suggestSubmitWay,\r\n        title: form.title, // 提案标题\r\n        suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,\r\n        delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,\r\n        content: form.content,\r\n        isJoinProposal: form.isJoinProposal,\r\n        suggestOpenType: form.suggestOpenType,\r\n        suggestSurveyType: form.suggestSurveyType,\r\n        isMakeMineJob: form.isMakeMineJob,\r\n        notHandleTimeType: form.notHandleTimeType,\r\n        isHopeEnhanceTalk: form.isHopeEnhanceTalk,\r\n        isNeedPaperAnswer: form.isNeedPaperAnswer,\r\n        termYearId: route.query.id ? form.termYearId : ProposalYear.value.value,\r\n        attachmentIds: fileData.value.map((v) => v.id)\r\n      },\r\n      objectParam: {\r\n        teamMainAuthor: form.writerUserId ? form.writerUserId : ''\r\n      },\r\n      isSaveDraft: type,\r\n      arrayParam: resultScoreProportion,\r\n      joinUsers: form.isJoinProposal ? form.joinUsers : [],\r\n      hopeHandleOfficeIds: form.hopeHandleOfficeIds,\r\n      contacters: contactPersonList.value\r\n        .filter(\r\n          (v) =>\r\n            v.contactName.replace(/(^\\s*)|(\\s*$)/g, '') ||\r\n            v.contactPhone.replace(/(^\\s*)|(\\s*$)/g, '') ||\r\n            v.contactAddress.replace(/(^\\s*)|(\\s*$)/g, '')\r\n        )\r\n        .map((v) => ({\r\n          contacterName: v.contactName,\r\n          contacterMobile: v.contactPhone,\r\n          contacterAddress: v.contactAddress\r\n        }))\r\n    })\r\n    if (code === 200) {\r\n      cachedScoreData.value = null\r\n      initialScoreData.value = null\r\n      hasLoadedScoreData.value = false\r\n      if (route.query.clueListId) {\r\n        proposalClueUse()\r\n      } else {\r\n        if (cb) {\r\n          return cb()\r\n        }\r\n        ElMessage({ type: 'success', message: route.query.id ? '编辑成功' : '提交成功' })\r\n        if (route.query.id || route.query.anewId || route.query.clueListId) {\r\n          qiankunMicro.setGlobalState({\r\n            closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }\r\n          })\r\n        } else {\r\n          store.commit('setRefreshRoute', 'SubmitSuggest')\r\n          setTimeout(() => {\r\n            store.commit('setRefreshRoute', '')\r\n          }, 222)\r\n        }\r\n      }\r\n    }\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nconst proposalClueUse = async () => {\r\n  const { code } = await api.proposalClueUse({ detailId: route.query.clueListId })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '提交成功' })\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  if (route.query.id || route.query.anewId || route.query.clueListId) {\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  } else {\r\n    store.commit('setRefreshRoute', 'SubmitSuggest')\r\n    setTimeout(() => {\r\n      store.commit('setRefreshRoute', '')\r\n    }, 222)\r\n  }\r\n}\r\n\r\nwatch(\r\n  () => form.joinUsers,\r\n  async (newVal, oldVal) => {\r\n    if (form.isJoinProposal) {\r\n      const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId\r\n      if (proposerId) {\r\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\r\n          await updateScoreProportionList()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  { deep: true }\r\n)\r\n\r\nwatch(\r\n  [() => form.suggestUserId, () => form.writerUserId],\r\n  async (newValues, oldValues) => {\r\n    if (newValues[0] !== oldValues[0] || newValues[1] !== oldValues[1]) {\r\n      if (form.isJoinProposal && form.joinUsers.length > 0) {\r\n        await updateScoreProportionList()\r\n      }\r\n    }\r\n  }\r\n)\r\n\r\nconst unitedCallback = async (_data) => {\r\n  if (_data && _data.length > 0) {\r\n    await updateScoreProportionList()\r\n  } else {\r\n    const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId\r\n    if (proposerId) {\r\n      const proposer = scoreProportionData.value.find(person => person.id === proposerId)\r\n      scoreProportionData.value = proposer ? [proposer] : []\r\n      isFlag.value = true\r\n    } else {\r\n      scoreProportionData.value = []\r\n      isFlag.value = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitSuggest {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SubmitSuggestBody {\r\n    width: 990px;\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    position: relative;\r\n\r\n    .detailsPrints,\r\n    .detailsExportInfos {\r\n      color: #3657c0;\r\n      font-size: 18px;\r\n      line-height: var(--zy-line-height);\r\n      padding-left: 35px;\r\n      margin-bottom: 20px;\r\n      position: relative;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .detailsPrints {\r\n      background: url(\"../../../assets/img/suggest_details_print.png\") no-repeat;\r\n      background-size: 30px 30px;\r\n      background-position: left center;\r\n    }\r\n\r\n    .detailsExportInfos {\r\n      background: url(\"../../../assets/img/suggest_details_export_info.png\") no-repeat;\r\n      background-size: 30px 30px;\r\n      background-position: left center;\r\n    }\r\n\r\n    .detailsPrint,\r\n    .detailsExportInfo {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-left: 30px;\r\n      margin-bottom: 20px;\r\n      position: relative;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .detailsPrint {\r\n      background: url(\"../../../assets/img/suggest_details_print.png\") no-repeat;\r\n      background-size: 20px 20px;\r\n      background-position: left center;\r\n    }\r\n\r\n    .detailsExportInfo {\r\n      background: url(\"../../../assets/img/suggest_details_export_info.png\") no-repeat;\r\n      background-size: 20px 20px;\r\n      background-position: left center;\r\n    }\r\n\r\n    .SubmitSuggestNameBody {\r\n      padding: var(--zy-distance-one);\r\n      padding-bottom: 0;\r\n\r\n      .global-dynamic-title {\r\n        border-bottom: 3px solid var(--zy-el-color-primary);\r\n      }\r\n    }\r\n\r\n    .globalPaperForm {\r\n      width: 100%;\r\n      padding: var(--zy-distance-one);\r\n      padding-top: 0;\r\n\r\n      .zy-el-form-item {\r\n        width: 50%;\r\n        margin: 0;\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n\r\n        .zy-el-form-item__label {\r\n          width: 138px;\r\n          justify-content: center;\r\n        }\r\n\r\n        .zy-el-form-item__content {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n          border-right: 1px solid transparent;\r\n\r\n          &>.simple-select-person {\r\n            box-shadow: 0 0 0 0 !important;\r\n          }\r\n\r\n          &>.zy-el-input,\r\n          .zy-el-input-number {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-select,\r\n          .zy-el-select-v2 {\r\n            .zy-el-select__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-radio-group {\r\n            padding-left: 15px;\r\n          }\r\n\r\n          &>.zy-el-date-editor {\r\n            width: 100%;\r\n\r\n            &>.zy-el-input__wrapper {\r\n              width: 100%;\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestLeft {\r\n        .zy-el-form-item__content {\r\n          border-right-color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestTitle {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          border-right-color: transparent;\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestButton {\r\n        .zy-el-form-item__content {\r\n          flex-wrap: nowrap;\r\n          justify-content: space-between;\r\n\r\n          .SubmitSuggestContentNumber {\r\n            padding: 0 10px;\r\n            color: var(--zy-el-color-error);\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n          }\r\n\r\n          .SubmitSuggestUpload {\r\n            margin-left: 12px;\r\n            margin-right: 12px;\r\n          }\r\n\r\n          .zy-el-button {\r\n            --zy-el-button-size: var(--zy-height-routine);\r\n          }\r\n        }\r\n      }\r\n\r\n      .TinyMceEditor {\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n      }\r\n\r\n      .content_box p span {\r\n        line-height: 1.5 !important;\r\n        font-size: 21px !important;\r\n      }\r\n\r\n      .SubmitSuggestFormUpload {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          padding: 15px;\r\n          border-right-color: transparent;\r\n\r\n          .SubmitSuggestFormInfo {\r\n            width: 100%;\r\n            display: flex;\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestFormItem {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          padding: 0 15px;\r\n          border-right-color: transparent;\r\n\r\n          .SubmitSuggestFormInfo {\r\n            width: 100%;\r\n            display: flex;\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestContactPerson {\r\n        width: 100%;\r\n\r\n        .SubmitSuggestContactPersonHead,\r\n        .SubmitSuggestContactPersonBody {\r\n          width: 100%;\r\n          display: flex;\r\n        }\r\n\r\n        .SubmitSuggestContactPersonBody {\r\n          border-top: 1px solid var(--zy-el-color-primary);\r\n        }\r\n\r\n        .row1 {\r\n          flex: 1;\r\n        }\r\n\r\n        .row2 {\r\n          flex: 2;\r\n        }\r\n\r\n        .row3 {\r\n          flex: 3;\r\n        }\r\n\r\n        .SubmitSuggestContactPersonItem {\r\n          height: 40px;\r\n          line-height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &>.zy-el-input {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          .zy-el-link {\r\n            font-size: 18px;\r\n            line-height: 24px;\r\n          }\r\n\r\n          .zy-el-link+.zy-el-link {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n\r\n        .SubmitSuggestContactPersonItem+.SubmitSuggestContactPersonItem {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .globalPaperFormButton {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding-top: 22px;\r\n\r\n        .zy-el-button+.zy-el-button {\r\n          margin-left: var(--zy-distance-two);\r\n        }\r\n      }\r\n    }\r\n\r\n    .SuggestSegmentation {\r\n      width: 100%;\r\n      height: 10px;\r\n      background-color: var(--zy-el-color-info-light-9);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAmB;;EAFlCC,GAAA;EAGWC,KAA8C,EAA9C;IAAA;IAAA;IAAA;EAAA;;;EAIAF,KAAK,EAAC;AAAuB;;EAPxCC,GAAA;EAmFiCC,KAAiC,EAAjC;IAAA;IAAA;EAAA;;;EAnFjCD,GAAA;EAoFsCC,KAAiC,EAAjC;IAAA;IAAA;EAAA;;iBApFtC;;EAAAD,GAAA;EAkGaC,KAA8B,EAA9B;IAAA;EAAA;;;EAlGbD,GAAA;EA2GeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EA5GlDC,GAAA;EAiHeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EAlHlDC,GAAA;EAuHeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EAxHlDC,GAAA;EA6HeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EA9HlDC,GAAA;EAmIeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EApIlDC,GAAA;EAyIeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EAiBjCA,KAAK,EAAC;AAAqC;;EAG3CA,KAAK,EAAC;AAAqC;;EAG3CA,KAAK,EAAC;AAAqC;;EAG3CA,KAAK,EAAC;AAAqC;;EApK5DC,GAAA;EAkLaD,KAAK,EAAC;;;EAlLnBC,GAAA;EA4LyCD,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;;;;wCA3L7CG,YAAA,CA2MeC,uBAAA;IA3MDC,MAAM,EAAN,EAAM;IAACL,KAAK,EAAC,eAAe;IAAsB,qBAAmB,EAAEM,MAAA,CAAAC;;IADvFC,OAAA,EAAAC,QAAA,CAEI;MAAA,OAiMM,CAjMNC,mBAAA,CAiMM,OAjMNC,UAiMM,GAhMsDL,MAAA,CAAAM,SAAS,gB,cAAnEC,mBAAA,CAGM,OAHNC,UAGM,GAFJJ,mBAAA,CAA6E;QAAxEV,KAAK,EAAC,cAAc;QAACe,KAAK,EAAC,MAAM;QAAEC,OAAK,EAAEV,MAAA,CAAAW;SAAoB,MAAI,GACvEP,mBAAA,CAAwF;QAAnFV,KAAK,EAAC,mBAAmB;QAACe,KAAK,EAAC,UAAU;QAAEC,OAAK,EAAEV,MAAA,CAAAY;SAAkB,UAAQ,E,KAL1FC,mBAAA,gBAOMT,mBAAA,CAIM,OAJNU,UAIM,GAH+Cd,MAAA,CAAAe,YAAY,CAACC,WAAW,I,cAA3EnB,YAAA,CACqDG,MAAA;QAT7DL,GAAA;QAQuBsB,YAAY,EAAC,gBAAgB;QACzCC,MAAM,EAAElB,MAAA,CAAAe,YAAY,CAACC;4DACxBnB,YAAA,CAAoEG,MAAA;QAV5EL,GAAA;QAUuBsB,YAAY,EAAC;aAE9BE,YAAA,CA+KUC,kBAAA;QA/KDC,GAAG,EAAC,SAAS;QAAEC,KAAK,EAAEtB,MAAA,CAAAuB,IAAI;QAAGC,KAAK,EAAExB,MAAA,CAAAwB,KAAK;QAAEC,MAAM,EAAN,EAAM;QAAE,cAAY,EAAE,KAAK;QAAE/B,KAAK,EAAC;;QAZ7FQ,OAAA,EAAAC,QAAA,CAaqD;UAAA,OAOzC,C,CAPgCH,MAAA,CAAA0B,QAAQ,I,cAA5C7B,YAAA,CAKe8B,uBAAA;YAlBvBhC,GAAA;YAasBiC,KAAK,EAAC,QAAQ;YAAkBC,IAAI,EAAC,kBAAkB;YAACnC,KAAK,EAAC;;YAbpFQ,OAAA,EAAAC,QAAA,CAcU;cAAA,OAGiB,CAHjBgB,YAAA,CAGiBW,yBAAA;gBAjB3BC,UAAA,EAcmC/B,MAAA,CAAAuB,IAAI,CAACS,gBAAgB;gBAdxD,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAcmClC,MAAA,CAAAuB,IAAI,CAACS,gBAAgB,GAAAE,MAAA;gBAAA;gBAAGC,QAAM,EAAEnC,MAAA,CAAAoC;;gBAdnElC,OAAA,EAAAC,QAAA,CAeY;kBAAA,OAA8C,CAA9CgB,YAAA,CAA8CkB,mBAAA;oBAApCT,KAAK,EAAC;kBAAc;oBAf1C1B,OAAA,EAAAC,QAAA,CAe2C;sBAAA,OAAI8B,MAAA,SAAAA,MAAA,QAf/CK,gBAAA,CAe2C,MAAI,E;;oBAf/CC,CAAA;sBAgBYpB,YAAA,CAAsCkB,mBAAA;oBAA5BT,KAAK,EAAC;kBAAM;oBAhBlC1B,OAAA,EAAAC,QAAA,CAgBmC;sBAAA,OAAI8B,MAAA,SAAAA,MAAA,QAhBvCK,gBAAA,CAgBmC,MAAI,E;;oBAhBvCC,CAAA;;;gBAAAA,CAAA;;;YAAAA,CAAA;gBAAA1B,mBAAA,gBAmBQM,YAAA,CAGeQ,uBAAA;YAHDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,OAAO;YAACnC,KAAK,EAAC;;YAnBtDQ,OAAA,EAAAC,QAAA,CAoBU;cAAA,OACc,CADdgB,YAAA,CACcqB,mBAAA;gBArBxBT,UAAA,EAoB6B/B,MAAA,CAAAuB,IAAI,CAACd,KAAK;gBApBvC,uBAAAwB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAoB6BlC,MAAA,CAAAuB,IAAI,CAACd,KAAK,GAAAyB,MAAA;gBAAA;gBAAEO,WAAW,EAAC,SAAS;gBAAC,iBAAe,EAAf,EAAe;gBAAEC,SAAS,EAAE1C,MAAA,CAAA2C,kBAAkB;gBACjGC,SAAS,EAAT;;;YArBZL,CAAA;8BAuBQpB,YAAA,CAIeQ,uBAAA;YAJiDC,KAAK,EAAC,KAAK;YAACC,IAAI,EAAC,eAAe;YAC9FnC,KAAK,EAAC;;YAxBhBQ,OAAA,EAAAC,QAAA,CAyBU;cAAA,OAC6B,CAD7BgB,YAAA,CAC6B0B,8BAAA;gBA1BvCd,UAAA,EAyBwC/B,MAAA,CAAAuB,IAAI,CAACuB,aAAa;gBAzB1D,uBAAAb,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAyBwClC,MAAA,CAAAuB,IAAI,CAACuB,aAAa,GAAAZ,MAAA;gBAAA;gBAAEO,WAAW,EAAC,QAAQ;gBAAEM,QAAQ,EAAE/C,MAAA,CAAA+C,QAAQ;gBAAGC,OAAO,EAAEhD,MAAA,CAAAgD,OAAO;gBAC1GC,UAAQ,EAAEjD,MAAA,CAAAkD;;;YA1BvBX,CAAA;8CAuB8BvC,MAAA,CAAAuB,IAAI,CAACS,gBAAgB,qB,mBAK3Cb,YAAA,CAEeQ,uBAAA;YAFiDC,KAAK,EAAC;UAAM;YA5BpF1B,OAAA,EAAAC,QAAA,CA6BU;cAAA,OAA+C,CAA/CgB,YAAA,CAA+CqB,mBAAA;gBA7BzDT,UAAA,EA6B6B/B,MAAA,CAAAuB,IAAI,CAAC4B,UAAU;gBA7B5C,uBAAAlB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OA6B6BlC,MAAA,CAAAuB,IAAI,CAAC4B,UAAU,GAAAjB,MAAA;gBAAA;gBAAEa,QAAQ,EAAR;;;YA7B9CR,CAAA;8CA4B8BvC,MAAA,CAAAuB,IAAI,CAACS,gBAAgB,qB,mBAG3Cb,YAAA,CAEeQ,uBAAA;YAFiDC,KAAK,EAAC,IAAI;YAAClC,KAAK,EAAC;;YA/BzFQ,OAAA,EAAAC,QAAA,CAgCU;cAAA,OAA+C,CAA/CgB,YAAA,CAA+CqB,mBAAA;gBAhCzDT,UAAA,EAgC6B/B,MAAA,CAAAuB,IAAI,CAAC6B,UAAU;gBAhC5C,uBAAAnB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAgC6BlC,MAAA,CAAAuB,IAAI,CAAC6B,UAAU,GAAAlB,MAAA;gBAAA;gBAAEa,QAAQ,EAAR;;;YAhC9CR,CAAA;8CA+B8BvC,MAAA,CAAAuB,IAAI,CAACS,gBAAgB,qB,mBAG3Cb,YAAA,CAEeQ,uBAAA;YAFiDC,KAAK,EAAC;UAAM;YAlCpF1B,OAAA,EAAAC,QAAA,CAmCU;cAAA,OAA2C,CAA3CgB,YAAA,CAA2CqB,mBAAA;gBAnCrDT,UAAA,EAmC6B/B,MAAA,CAAAuB,IAAI,CAAC8B,MAAM;gBAnCxC,uBAAApB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAmC6BlC,MAAA,CAAAuB,IAAI,CAAC8B,MAAM,GAAAnB,MAAA;gBAAA;gBAAEa,QAAQ,EAAR;;;YAnC1CR,CAAA;8CAkC8BvC,MAAA,CAAAuB,IAAI,CAACS,gBAAgB,qB,mBAG3Cb,YAAA,CAEeQ,uBAAA;YAFiDC,KAAK,EAAC,MAAM;YAAClC,KAAK,EAAC;;YArC3FQ,OAAA,EAAAC,QAAA,CAsCU;cAAA,OAAgD,CAAhDgB,YAAA,CAAgDqB,mBAAA;gBAtC1DT,UAAA,EAsC6B/B,MAAA,CAAAuB,IAAI,CAAC+B,WAAW;gBAtC7C,uBAAArB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAsC6BlC,MAAA,CAAAuB,IAAI,CAAC+B,WAAW,GAAApB,MAAA;gBAAA;gBAAEa,QAAQ,EAAR;;;YAtC/CR,CAAA;8CAqC8BvC,MAAA,CAAAuB,IAAI,CAACS,gBAAgB,qB,mBAG3Cb,YAAA,CAKeQ,uBAAA;YALyCC,KAAK,EAAC,KAAK;YAACC,IAAI,EAAC,cAAc;YACrFnC,KAAK,EAAC;;YAzChBQ,OAAA,EAAAC,QAAA,CA0CU;cAAA,OAEY,CAFZgB,YAAA,CAEYoC,oBAAA;gBA5CtBxB,UAAA,EA0C8B/B,MAAA,CAAAuB,IAAI,CAACiC,YAAY;gBA1C/C,uBAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OA0C8BlC,MAAA,CAAAuB,IAAI,CAACiC,YAAY,GAAAtB,MAAA;gBAAA;gBAAGa,QAAQ,EAAE/C,MAAA,CAAAyD,UAAU;gBAAEhB,WAAW,EAAC,WAAW;gBAACG,SAAS,EAAT;;gBA1ChG1C,OAAA,EAAAC,QAAA,CA2CuB;kBAAA,OAA8B,E,kBAAzCI,mBAAA,CAA+FmD,SAAA,QA3C3GC,WAAA,CA2CsC3D,MAAA,CAAA4D,cAAc,EA3CpD,UA2C8BC,IAAI;yCAAtBhE,YAAA,CAA+FiE,oBAAA;sBAApDnE,GAAG,EAAEkE,IAAI,CAACE,EAAE;sBAAGnC,KAAK,EAAEiC,IAAI,CAACG,IAAI;sBAAGC,KAAK,EAAEJ,IAAI,CAACE;;;;gBA3CrGxB,CAAA;;;YAAAA,CAAA;8CAwC8BvC,MAAA,CAAAuB,IAAI,CAACS,gBAAgB,a,mBAM3Cb,YAAA,CAIeQ,uBAAA;YAHbC,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,cAAc;YAACnC,KAAK,EAAC;;YA/ClDQ,OAAA,EAAAC,QAAA,CAgDU;cAAA,OACgD,CADhDgB,YAAA,CACgD0B,8BAAA;gBAjD1Dd,UAAA,EAgDwC/B,MAAA,CAAAuB,IAAI,CAAC2C,YAAY;gBAhDzD,uBAAAjC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAgDwClC,MAAA,CAAAuB,IAAI,CAAC2C,YAAY,GAAAhC,MAAA;gBAAA;gBAAEO,WAAW,EAAC,UAAU;gBAAEM,QAAQ,EAAE/C,MAAA,CAAA+C,QAAQ;gBACxFC,OAAO,EAAEhD,MAAA,CAAAgD,OAAO;gBAAGC,UAAQ,EAAEjD,MAAA,CAAAkD;;;YAjD1CX,CAAA;8CA8C8BvC,MAAA,CAAAuB,IAAI,CAACS,gBAAgB,gBAAgBhC,MAAA,CAAAmE,MAAM,gBAAgBnE,MAAA,CAAAmE,MAAM,e,GAKvFhD,YAAA,CAKeQ,uBAAA;YALDC,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,gBAAgB;YAACnC,KAAK,EAAC;;YAnDjEQ,OAAA,EAAAC,QAAA,CAoDU;cAAA,OAGiB,CAHjBgB,YAAA,CAGiBW,yBAAA;gBAvD3BC,UAAA,EAoDmC/B,MAAA,CAAAuB,IAAI,CAAC6C,cAAc;gBApDtD,uBAAAnC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAoDmClC,MAAA,CAAAuB,IAAI,CAAC6C,cAAc,GAAAlC,MAAA;gBAAA;gBAAGC,QAAM,EAAEnC,MAAA,CAAAqE;;gBApDjEnE,OAAA,EAAAC,QAAA,CAqDY;kBAAA,OAAiC,CAAjCgB,YAAA,CAAiCkB,mBAAA;oBAAtBT,KAAK,EAAE;kBAAC;oBArD/B1B,OAAA,EAAAC,QAAA,CAqDiC;sBAAA,OAAC8B,MAAA,SAAAA,MAAA,QArDlCK,gBAAA,CAqDiC,GAAC,E;;oBArDlCC,CAAA;sBAsDYpB,YAAA,CAAiCkB,mBAAA;oBAAtBT,KAAK,EAAE;kBAAC;oBAtD/B1B,OAAA,EAAAC,QAAA,CAsDiC;sBAAA,OAAC8B,MAAA,SAAAA,MAAA,QAtDlCK,gBAAA,CAsDiC,GAAC,E;;oBAtDlCC,CAAA;;;gBAAAA,CAAA;;;YAAAA,CAAA;cAyD2DvC,MAAA,CAAAuB,IAAI,CAAC6C,cAAc,I,cAAtEvE,YAAA,CAUe8B,uBAAA;YAnEvBhC,GAAA;YAyDsBiC,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,WAAW;YAA4BnC,KAAK,EAAC;;YAzDtFQ,OAAA,EAAAC,QAAA,CA0DU;cAAA,OAEoD,CAFpDgB,YAAA,CAEoDmD,+BAAA;gBA5D9DvC,UAAA,EA0DyC/B,MAAA,CAAAuB,IAAI,CAACgD,SAAS;gBA1DvD,uBAAAtC,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA0DyClC,MAAA,CAAAuB,IAAI,CAACgD,SAAS,GAAArC,MAAA;gBAAA;gBAAEO,WAAW,EAAC,UAAU;gBAClE+B,UAAU,EAAExE,MAAA,CAAAuB,IAAI,CAACuB,aAAa,IAAI9C,MAAA,CAAAuB,IAAI,CAACuB,aAAa;gBAASE,OAAO,EAAE,eAAe;gBACrFC,UAAQ,EAAEjD,MAAA,CAAAyE;qEACGzE,MAAA,CAAA0E,wBAAwB,IAAI1E,MAAA,CAAAM,SAAS,iB,cACnDT,YAAA,CAGwB8E,gCAAA;gBAjEpChF,GAAA;gBA8D2CiF,QAAQ,EAAE5E,MAAA,CAAA4E,QAAQ;gBA9D7D,qBAAA3C,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA8DqDlC,MAAA,CAAA4E,QAAQ,GAAA1C,MAAA;gBAAA;gBA9D7DH,UAAA,EA8DwE/B,MAAA,CAAA6E,aAAa;gBA9DrF,uBAAA5C,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA8DwElC,MAAA,CAAA6E,aAAa,GAAA3C,MAAA;gBAAA;;gBA9DrFhC,OAAA,EAAAC,QAAA,CA+Dc;kBAAA,OACuB,CADvBgB,YAAA,CACuBnB,MAAA;oBADA8E,MAAM,EAAE9E,MAAA,CAAA+E,UAAU;oBAAG9B,UAAQ,EAAEjD,MAAA,CAAAgF,gBAAgB;oBAAGC,QAAM,EAAEjF,MAAA,CAAAkF;;;gBA/D/F3C,CAAA;+DAAA1B,mBAAA,e;;YAAA0B,CAAA;gBAAA1B,mBAAA,gBAqEgBb,MAAA,CAAAmF,mBAAmB,IAAInF,MAAA,CAAAmF,mBAAmB,CAACC,MAAM,QAAQpF,MAAA,CAAAqF,MAAM,KAAKrF,MAAA,CAAAmE,MAAM,gBAAgBnE,MAAA,CAAAmE,MAAM,gBAAgBnE,MAAA,CAAAmE,MAAM,iB,cAD9HtE,YAAA,CAiBe8B,uBAAA;YArFvBhC,GAAA;YAoEsBiC,KAAK,EAAC,QAAQ;YAE1BlC,KAAK,EAAC;;YAtEhBQ,OAAA,EAAAC,QAAA,CAuEU;cAAA,OAWW,CAXXgB,YAAA,CAWWmE,mBAAA;gBAXDjE,GAAG,EAAC,UAAU;gBAAC,SAAO,EAAC,IAAI;gBAACkE,MAAM,EAAN,EAAM;gBAAEC,IAAI,EAAExF,MAAA,CAAAmF,mBAAmB;gBACrEvF,KAAgD,EAAhD;kBAAA;kBAAA;gBAAA;;gBAxEZM,OAAA,EAAAC,QAAA,CAyEY;kBAAA,OAEkB,CAFlBgB,YAAA,CAEkBsE,0BAAA;oBAFD7D,KAAK,EAAC,IAAI;oBAAC,WAAS,EAAC,KAAK;oBAAC,uBAAqB,EAArB,EAAqB;oBAAC8D,KAAK,EAAC;;oBAC3DxF,OAAO,EAAAC,QAAA,CAAS,UAAwBwF,KAA1B;sBAAA,QA1EvCrD,gBAAA,CAAAsD,gBAAA,CA0E4CD,KAAK,CAACE,GAAG,CAACC,QAAQ,iB;;oBA1E9DvD,CAAA;sBA4EYpB,YAAA,CAKkBsE,0BAAA;oBALD7D,KAAK,EAAC,IAAI;oBAAC,WAAS,EAAC,KAAK;oBAAC,uBAAqB,EAArB,EAAqB;oBAAC8D,KAAK,EAAC;;oBAC3DxF,OAAO,EAAAC,QAAA,CAChB,UACkEwF,KAF3C;sBAAA,QACvBxE,YAAA,CACkEqB,mBAAA;wBA/ElFT,UAAA,EA8EmC4D,KAAK,CAACE,GAAG,CAACE,eAAe;wBA9E5D,gCAAAC,mBAAA9D,MAAA;0BAAA,OA8EmCyD,KAAK,CAACE,GAAG,CAACE,eAAe,GAAA7D,MAAA;wBAAA;wBAAEtC,KAAqB,EAArB;0BAAA;wBAAA,CAAqB;wBAACqG,IAAI,EAAC,QAAQ;wBAC9EC,OAAK,WAALA,OAAKA,CAAAhE,MAAA;0BAAA,OAAElC,MAAA,CAAAmG,WAAW,CAACR,KAAK,CAACE,GAAG;wBAAA;wBAAIO,MAAI,WAAJA,MAAIA,CAAAlE,MAAA;0BAAA,OAAElC,MAAA,CAAAqG,UAAU,CAACV,KAAK,CAACE,GAAG;wBAAA;uIA/E7EvD,gBAAA,CA+EkF,MACpE,G;;oBAhFdC,CAAA;;;gBAAAA,CAAA;2CAmFmBvC,MAAA,CAAAsG,YAAY,I,cAArB/F,mBAAA,CAAyE,KAAzEgG,UAAyE,EAAhB,cAAY,KAnF/E1F,mBAAA,gBAoFmBb,MAAA,CAAAwG,iBAAiB,I,cAA1BjG,mBAAA,CAA4E,KAA5EkG,UAA4E,EAAd,YAAU,KApFlF5F,mBAAA,e;;YAAA0B,CAAA;gBAAA1B,mBAAA,gBAsFQM,YAAA,CAOeQ,uBAAA;YAPDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,SAAS;YAACnC,KAAK,EAAC;;YAtFxDQ,OAAA,EAAAC,QAAA,CAuFU;cAAA,OAA+F,CAA/FU,mBAAA,8FAA+F,E,4BAC/FT,mBAAA,CAAW,uCAEHJ,MAAA,CAAA0E,wBAAwB,IAAI1E,MAAA,CAAAM,SAAS,iBAAiBN,MAAA,CAAA0G,UAAU,I,cADxE7G,YAAA,CAGY8G,oBAAA;gBA5FtBhH,GAAA;gBAyFsBe,OAAK,EAAAuB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAAElC,MAAA,CAAA4G,gBAAgB;gBAAA;gBACuCX,IAAI,EAAC;;gBA1FzF/F,OAAA,EAAAC,QAAA,CA0FmG;kBAAA,OAEzF8B,MAAA,SAAAA,MAAA,QA5FVK,gBAAA,CA0FmG,SAEzF,E;;gBA5FVC,CAAA;oBAAA1B,mBAAA,e;;YAAA0B,CAAA;cA+FsEvC,MAAA,CAAAM,SAAS,iB,cADvET,YAAA,CAE2DgH,wBAAA;YAhGnElH,GAAA;YAAAoC,UAAA,EA8FgC/B,MAAA,CAAAuB,IAAI,CAACuF,OAAO;YA9F5C,uBAAA7E,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OA8FgClC,MAAA,CAAAuB,IAAI,CAACuF,OAAO,GAAA5E,MAAA;YAAA;YAAG6E,OAAO,EAAE/G,MAAA,CAAAgH,cAAc;YAAGC,SAAS,EAAEjH,MAAA,CAAAkH,oBAAoB;YAC7FC,OAAK,EAAEnH,MAAA,CAAAoH,kBAAkB;YAAGhB,MAAI,EAAEpG,MAAA,CAAAqH,iBAAiB;YAAgCC,WAAW,EAAX,EAAW;YAC9F7E,WAAW,kBAAkBzC,MAAA,CAAAkH,oBAAoB;gGACpD3G,mBAAA,CAA4D;YAjGpEZ,GAAA;YAiGa4H,SAAqB,EAAbvH,MAAA,CAAAuB,IAAI,CAACuF,OAAO;YAASpH,KAAK,EAAC;kCAjGhD8H,UAAA,IAkGkDxH,MAAA,CAAAM,SAAS,gB,cAAnDC,mBAAA,CAGM,OAHNkH,UAGM,GAFJrH,mBAAA,CAA8E;YAAzEV,KAAK,EAAC,eAAe;YAACe,KAAK,EAAC,MAAM;YAAEC,OAAK,EAAEV,MAAA,CAAAW;aAAoB,MAAI,GACxEP,mBAAA,CAAyF;YAApFV,KAAK,EAAC,oBAAoB;YAACe,KAAK,EAAC,UAAU;YAAEC,OAAK,EAAEV,MAAA,CAAAY;aAAkB,UAAQ,E,KApG7FC,mBAAA,gBAsGQA,mBAAA,mLAEmB,EACnBM,YAAA,CAsCeQ,uBAAA;YAtCDC,KAAK,EAAC,QAAQ;YAAClC,KAAK,EAAC,uBAAuB;YACxDE,KAAyD,EAAzD;cAAA;YAAA;;YA1GVM,OAAA,EAAAC,QAAA,CA+KkB;cAAA,OAeT,CAnF0CH,MAAA,CAAA0H,mBAAmB,I,cAA5DnH,mBAAA,CAKM,OALNoH,UAKM,GAJJvH,mBAAA,CAAuE,OAAvEwH,UAAuE,EAAAhC,gBAAA,CAA7B5F,MAAA,CAAA0H,mBAAmB,IAAG,GAAC,iBACjEvG,YAAA,CAEiBW,yBAAA;gBA/G7BC,UAAA,EA6GqC/B,MAAA,CAAAuB,IAAI,CAACsG,eAAe;gBA7GzD,uBAAA5F,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA6GqClC,MAAA,CAAAuB,IAAI,CAACsG,eAAe,GAAA3F,MAAA;gBAAA;;gBA7GzDhC,OAAA,EAAAC,QAAA,CA8GwB;kBAAA,OAA+B,E,kBAAzCI,mBAAA,CAAsGmD,SAAA,QA9GpHC,WAAA,CA8GuC3D,MAAA,CAAA6H,eAAe,EA9GtD,UA8G+BhE,IAAI;yCAArBhE,YAAA,CAAsGwC,mBAAA;sBAA3D1C,GAAG,EAAEkE,IAAI,CAAClE,GAAG;sBAAGiC,KAAK,EAAEiC,IAAI,CAAClE;;sBA9GrFO,OAAA,EAAAC,QAAA,CA8G0F;wBAAA,OAAe,CA9GzGmC,gBAAA,CAAAsD,gBAAA,CA8G6F/B,IAAI,CAACG,IAAI,iB;;sBA9GtGzB,CAAA;;;;gBAAAA,CAAA;qDAAA1B,mBAAA,gBAiHmDb,MAAA,CAAA8H,qBAAqB,I,cAA9DvH,mBAAA,CAKM,OALNwH,WAKM,GAJJ3H,mBAAA,CAAyE,OAAzE4H,WAAyE,EAAApC,gBAAA,CAA/B5F,MAAA,CAAA8H,qBAAqB,IAAG,GAAC,iBACnE3G,YAAA,CAEiBW,yBAAA;gBArH7BC,UAAA,EAmHqC/B,MAAA,CAAAuB,IAAI,CAAC0G,iBAAiB;gBAnH3D,uBAAAhG,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAmHqClC,MAAA,CAAAuB,IAAI,CAAC0G,iBAAiB,GAAA/F,MAAA;gBAAA;;gBAnH3DhC,OAAA,EAAAC,QAAA,CAoHwB;kBAAA,OAAiC,E,kBAA3CI,mBAAA,CAAwGmD,SAAA,QApHtHC,WAAA,CAoHuC3D,MAAA,CAAAiI,iBAAiB,EApHxD,UAoH+BpE,IAAI;yCAArBhE,YAAA,CAAwGwC,mBAAA;sBAA3D1C,GAAG,EAAEkE,IAAI,CAAClE,GAAG;sBAAGiC,KAAK,EAAEiC,IAAI,CAAClE;;sBApHvFO,OAAA,EAAAC,QAAA,CAoH4F;wBAAA,OAAe,CApH3GmC,gBAAA,CAAAsD,gBAAA,CAoH+F/B,IAAI,CAACG,IAAI,iB;;sBApHxGzB,CAAA;;;;gBAAAA,CAAA;qDAAA1B,mBAAA,gBAuHmDb,MAAA,CAAAkI,iBAAiB,I,cAA1D3H,mBAAA,CAKM,OALN4H,WAKM,GAJJ/H,mBAAA,CAAqE,OAArEgI,WAAqE,EAAAxC,gBAAA,CAA3B5F,MAAA,CAAAkI,iBAAiB,IAAG,GAAC,iBAC/D/G,YAAA,CAEiBW,yBAAA;gBA3H7BC,UAAA,EAyHqC/B,MAAA,CAAAuB,IAAI,CAAC8G,aAAa;gBAzHvD,uBAAApG,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAyHqClC,MAAA,CAAAuB,IAAI,CAAC8G,aAAa,GAAAnG,MAAA;gBAAA;;gBAzHvDhC,OAAA,EAAAC,QAAA,CA0HwB;kBAAA,OAA6B,E,kBAAvCI,mBAAA,CAAoGmD,SAAA,QA1HlHC,WAAA,CA0HuC3D,MAAA,CAAAqI,aAAa,EA1HpD,UA0H+BxE,IAAI;yCAArBhE,YAAA,CAAoGwC,mBAAA;sBAA3D1C,GAAG,EAAEkE,IAAI,CAAClE,GAAG;sBAAGiC,KAAK,EAAEiC,IAAI,CAAClE;;sBA1HnFO,OAAA,EAAAC,QAAA,CA0HwF;wBAAA,OAAe,CA1HvGmC,gBAAA,CAAAsD,gBAAA,CA0H2F/B,IAAI,CAACG,IAAI,iB;;sBA1HpGzB,CAAA;;;;gBAAAA,CAAA;qDAAA1B,mBAAA,gBA6HmDb,MAAA,CAAAsI,qBAAqB,I,cAA9D/H,mBAAA,CAKM,OALNgI,WAKM,GAJJnI,mBAAA,CAAyE,OAAzEoI,WAAyE,EAAA5C,gBAAA,CAA/B5F,MAAA,CAAAsI,qBAAqB,IAAG,GAAC,iBACnEnH,YAAA,CAEiBW,yBAAA;gBAjI7BC,UAAA,EA+HqC/B,MAAA,CAAAuB,IAAI,CAACkH,iBAAiB;gBA/H3D,uBAAAxG,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA+HqClC,MAAA,CAAAuB,IAAI,CAACkH,iBAAiB,GAAAvG,MAAA;gBAAA;;gBA/H3DhC,OAAA,EAAAC,QAAA,CAgIwB;kBAAA,OAAiC,E,kBAA3CI,mBAAA,CAAwGmD,SAAA,QAhItHC,WAAA,CAgIuC3D,MAAA,CAAAyI,iBAAiB,EAhIxD,UAgI+B5E,IAAI;yCAArBhE,YAAA,CAAwGwC,mBAAA;sBAA3D1C,GAAG,EAAEkE,IAAI,CAAClE,GAAG;sBAAGiC,KAAK,EAAEiC,IAAI,CAAClE;;sBAhIvFO,OAAA,EAAAC,QAAA,CAgI4F;wBAAA,OAAe,CAhI3GmC,gBAAA,CAAAsD,gBAAA,CAgI+F/B,IAAI,CAACG,IAAI,iB;;sBAhIxGzB,CAAA;;;;gBAAAA,CAAA;qDAAA1B,mBAAA,gBAmImDb,MAAA,CAAA0I,qBAAqB,I,cAA9DnI,mBAAA,CAKM,OALNoI,WAKM,GAJJvI,mBAAA,CAAyE,OAAzEwI,WAAyE,EAAAhD,gBAAA,CAA/B5F,MAAA,CAAA0I,qBAAqB,IAAG,GAAC,iBACnEvH,YAAA,CAEiBW,yBAAA;gBAvI7BC,UAAA,EAqIqC/B,MAAA,CAAAuB,IAAI,CAACsH,iBAAiB;gBArI3D,uBAAA5G,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAqIqClC,MAAA,CAAAuB,IAAI,CAACsH,iBAAiB,GAAA3G,MAAA;gBAAA;;gBArI3DhC,OAAA,EAAAC,QAAA,CAsIwB;kBAAA,OAAiC,E,kBAA3CI,mBAAA,CAAwGmD,SAAA,QAtItHC,WAAA,CAsIuC3D,MAAA,CAAA6I,iBAAiB,EAtIxD,UAsI+BhF,IAAI;yCAArBhE,YAAA,CAAwGwC,mBAAA;sBAA3D1C,GAAG,EAAEkE,IAAI,CAAClE,GAAG;sBAAGiC,KAAK,EAAEiC,IAAI,CAAClE;;sBAtIvFO,OAAA,EAAAC,QAAA,CAsI4F;wBAAA,OAAe,CAtI3GmC,gBAAA,CAAAsD,gBAAA,CAsI+F/B,IAAI,CAACG,IAAI,iB;;sBAtIxGzB,CAAA;;;;gBAAAA,CAAA;qDAAA1B,mBAAA,gBAyImDb,MAAA,CAAA8I,qBAAqB,I,cAA9DvI,mBAAA,CAKM,OALNwI,WAKM,GAJJ3I,mBAAA,CAAyE,OAAzE4I,WAAyE,EAAApD,gBAAA,CAA/B5F,MAAA,CAAA8I,qBAAqB,IAAG,GAAC,iBACnE3H,YAAA,CAEiBW,yBAAA;gBA7I7BC,UAAA,EA2IqC/B,MAAA,CAAAuB,IAAI,CAAC0H,iBAAiB;gBA3I3D,uBAAAhH,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA2IqClC,MAAA,CAAAuB,IAAI,CAAC0H,iBAAiB,GAAA/G,MAAA;gBAAA;;gBA3I3DhC,OAAA,EAAAC,QAAA,CA4IwB;kBAAA,OAAiC,E,kBAA3CI,mBAAA,CAAwGmD,SAAA,QA5ItHC,WAAA,CA4IuC3D,MAAA,CAAAiJ,iBAAiB,EA5IxD,UA4I+BpF,IAAI;yCAArBhE,YAAA,CAAwGwC,mBAAA;sBAA3D1C,GAAG,EAAEkE,IAAI,CAAClE,GAAG;sBAAGiC,KAAK,EAAEiC,IAAI,CAAClE;;sBA5IvFO,OAAA,EAAAC,QAAA,CA4I4F;wBAAA,OAAe,CA5I3GmC,gBAAA,CAAAsD,gBAAA,CA4I+F/B,IAAI,CAACG,IAAI,iB;;sBA5IxGzB,CAAA;;;;gBAAAA,CAAA;qDAAA1B,mBAAA,e;;YAAA0B,CAAA;cAgJQ1B,mBAAA,0MAEmB,EACnBM,YAAA,CA8BeQ,uBAAA;YA9BDjC,KAAK,EAAC,4BAA4B;YAACkC,KAAK,EAAC;;YAnJ/D1B,OAAA,EAAAC,QAAA,CAoJU;cAAA,OAKM,C,4BALNC,mBAAA,CAKM;gBALDV,KAAK,EAAC;cAAgC,IACzCU,mBAAA,CAA8D;gBAAzDV,KAAK,EAAC;cAAqC,GAAC,SAAO,GACxDU,mBAAA,CAA8D;gBAAzDV,KAAK,EAAC;cAAqC,GAAC,SAAO,GACxDU,mBAAA,CAA8D;gBAAzDV,KAAK,EAAC;cAAqC,GAAC,SAAO,GACxDU,mBAAA,CAAyD;gBAApDV,KAAK,EAAC;cAAqC,GAAC,IAAE,E,yCAErDa,mBAAA,CAsBMmD,SAAA,QAhLhBC,WAAA,CA0JqE3D,MAAA,CAAAkJ,iBAAiB,EA1JtF,UA0J6DrF,IAAI;qCAAvDtD,mBAAA,CAsBM;kBAtBDb,KAAK,EAAC,gCAAgC;kBAAoCC,GAAG,EAAEkE,IAAI,CAACE;oBACvF3D,mBAAA,CAEM,OAFN+I,WAEM,GADJhI,YAAA,CAAiFqB,mBAAA;kBAAvEC,WAAW,EAAC,UAAU;kBA5J9CV,UAAA,EA4JwD8B,IAAI,CAACuF,WAAW;kBA5JxE,gCAAApD,mBAAA9D,MAAA;oBAAA,OA4JwD2B,IAAI,CAACuF,WAAW,GAAAlH,MAAA;kBAAA;kBAAEU,SAAS,EAAT;kFAE9DxC,mBAAA,CAEM,OAFNiJ,WAEM,GADJlI,YAAA,CAAkFqB,mBAAA;kBAAxEC,WAAW,EAAC,UAAU;kBA/J9CV,UAAA,EA+JwD8B,IAAI,CAACyF,YAAY;kBA/JzE,gCAAAtD,mBAAA9D,MAAA;oBAAA,OA+JwD2B,IAAI,CAACyF,YAAY,GAAApH,MAAA;kBAAA;kBAAEU,SAAS,EAAT;kFAE/DxC,mBAAA,CAEM,OAFNmJ,WAEM,GADJpI,YAAA,CAAsFqB,mBAAA;kBAA5EC,WAAW,EAAC,YAAY;kBAlKhDV,UAAA,EAkK0D8B,IAAI,CAAC2F,cAAc;kBAlK7E,gCAAAxD,mBAAA9D,MAAA;oBAAA,OAkK0D2B,IAAI,CAAC2F,cAAc,GAAAtH,MAAA;kBAAA;kBAAEU,SAAS,EAAT;kFAEnExC,mBAAA,CAWM,OAXNqJ,WAWM,GAVqCzJ,MAAA,CAAAkJ,iBAAiB,CAAC9D,MAAM,I,cAAjEvF,YAAA,CAIU6J,kBAAA;kBAzKxB/J,GAAA;kBAqKwBe,OAAK,EAAEV,MAAA,CAAA2J;;kBArK/BzJ,OAAA,EAAAC,QAAA,CAsKgB;oBAAA,OAEU,CAFVgB,YAAA,CAEUyI,kBAAA;sBAxK1B1J,OAAA,EAAAC,QAAA,CAuKkB;wBAAA,OAAc,CAAdgB,YAAA,CAAc0I,qBAAA,E;;sBAvKhCtH,CAAA;;;kBAAAA,CAAA;sBAAA1B,mBAAA,gBA0K6Bb,MAAA,CAAAkJ,iBAAiB,CAAC9D,MAAM,Q,cAAvCvF,YAAA,CAIU6J,kBAAA;kBA9KxB/J,GAAA;kBA0K4De,OAAK,WAALA,OAAKA,CAAAwB,MAAA;oBAAA,OAAElC,MAAA,CAAA8J,gBAAgB,CAACjG,IAAI,CAACE,EAAE;kBAAA;;kBA1K3F7D,OAAA,EAAAC,QAAA,CA2KgB;oBAAA,OAEU,CAFVgB,YAAA,CAEUyI,kBAAA;sBA7K1B1J,OAAA,EAAAC,QAAA,CA4KkB;wBAAA,OAAU,CAAVgB,YAAA,CAAU4I,iBAAA,E;;sBA5K5BxH,CAAA;;;kBAAAA,CAAA;oEAAA1B,mBAAA,e;;;YAAA0B,CAAA;cAkLiDvC,MAAA,CAAAM,SAAS,iB,cAAlDC,mBAAA,CAQM,OARNyJ,WAQM,GAPJ7I,YAAA,CAA0EwF,oBAAA;YAA/DV,IAAI,EAAC,SAAS;YAAEvF,OAAK,EAAAuB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAAElC,MAAA,CAAAiK,UAAU,CAACjK,MAAA,CAAAkK,OAAO;YAAA;;YAnL9DhK,OAAA,EAAAC,QAAA,CAmLoE;cAAA,OAAI8B,MAAA,SAAAA,MAAA,QAnLxEK,gBAAA,CAmLoE,MAAI,E;;YAnLxEC,CAAA;eAqLoBvC,MAAA,CAAAmK,KAAK,CAACC,KAAK,CAACC,MAAM,KAAKrK,MAAA,CAAAmK,KAAK,CAACC,KAAK,CAACrG,EAAE,IAAK/D,MAAA,CAAAM,SAAS,gB,cAD7DT,YAAA,CAGY8G,oBAAA;YAvLtBhH,GAAA;YAoLsBe,OAAK,EAAAuB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAAElC,MAAA,CAAAiK,UAAU,CAACjK,MAAA,CAAAkK,OAAO;YAAA;;YApL/ChK,OAAA,EAAAC,QAAA,CAqLqF;cAAA,OAE3E8B,MAAA,SAAAA,MAAA,QAvLVK,gBAAA,CAqLqF,QAE3E,E;;YAvLVC,CAAA;gBAAA1B,mBAAA,gB,CAwL+Cb,MAAA,CAAAmK,KAAK,CAACC,KAAK,CAACrG,EAAE,I,cAAnDlE,YAAA,CAAmE8G,oBAAA;YAxL7EhH,GAAA;YAwLsBe,OAAK,EAAEV,MAAA,CAAAsK;;YAxL7BpK,OAAA,EAAAC,QAAA,CAwL+D;cAAA,OAAE8B,MAAA,SAAAA,MAAA,QAxLjEK,gBAAA,CAwL+D,IAAE,E;;YAxLjEC,CAAA;gBAAA1B,mBAAA,gBAyL8Cb,MAAA,CAAAmK,KAAK,CAACC,KAAK,CAACrG,EAAE,I,cAAlDlE,YAAA,CAAkE8G,oBAAA;YAzL5EhH,GAAA;YAyLsBe,OAAK,EAAEV,MAAA,CAAAsK;;YAzL7BpK,OAAA,EAAAC,QAAA,CAyL8D;cAAA,OAAE8B,MAAA,SAAAA,MAAA,QAzLhEK,gBAAA,CAyL8D,IAAE,E;;YAzLhEC,CAAA;gBAAA1B,mBAAA,e,KAAAA,mBAAA,e;;QAAA0B,CAAA;6CA4LiBvC,MAAA,CAAAM,SAAS,iB,cAApBC,mBAAA,CAAqE,OAArEgK,WAAqE,KA5L3E1J,mBAAA,iB,cA6LMhB,YAAA,CAKa2K,UAAA,SAF6CxK,MAAA,CAAAM,SAAS,iBAAiBN,MAAA,CAAA0G,UAAU,I,cAF5F7G,YAAA,CAG2EG,MAAA;QAjMnFL,GAAA;QA8L8BoE,EAAE,EAAE/D,MAAA,CAAAmK,KAAK,CAACC,KAAK,CAACrG,EAAE;QAAGC,IAAI,EAAEhE,MAAA,CAAAmK,KAAK,CAACC,KAAK,CAACK,UAAU;QAAGhK,KAAK,EAAET,MAAA,CAAAuB,IAAI,CAACd,KAAK;QACxFqG,OAAO,EAAE9G,MAAA,CAAAuB,IAAI,CAACuF,OAAO;QAAG4D,cAAc,EAAE1K,MAAA,CAAAuB,IAAI,CAACmJ,cAAc;QAAGC,gBAAgB,EAAE3K,MAAA,CAAAuB,IAAI,CAACoJ,gBAAgB;QACrGC,mBAAmB,EAAE5K,MAAA,CAAAuB,IAAI,CAACqJ,mBAAmB;QAC7CC,cAAY,EAAE7K,MAAA,CAAA8K,YAAY;QAAG7H,UAAQ,EAAEjD,MAAA,CAAAsK;kIAjMlDzJ,mBAAA,e,gCAoMwBb,MAAA,CAAA+K,kBAAkB,I,cAAtClL,YAAA,CAAkGG,MAAA;QApMtGL,GAAA;QAoM6CmF,MAAM,EAAE9E,MAAA,CAAAgL,WAAW;QAAG/H,UAAQ,EAAEjD,MAAA,CAAAiL;6CApM7EpK,mBAAA,gBAqMIM,YAAA,CAGmB+J,2BAAA;QAxMvBnJ,UAAA,EAqM+B/B,MAAA,CAAAmL,IAAI;QArMnC,uBAAAlJ,MAAA,SAAAA,MAAA,iBAAAC,MAAA;UAAA,OAqM+BlC,MAAA,CAAAmL,IAAI,GAAAjJ,MAAA;QAAA;QAAE8B,IAAI,EAAC;;QArM1C9D,OAAA,EAAAC,QAAA,CAsMM;UAAA,OACyD,CADzDgB,YAAA,CACyDnB,MAAA;YADvCiG,IAAI,EAAEjG,MAAA,CAAAoL,MAAM;YAAGrH,EAAE,EAAE/D,MAAA,CAAAmK,KAAK,CAACC,KAAK,CAACrG,EAAE;YAAGtD,KAAK,EAAET,MAAA,CAAAuB,IAAI,CAACd,KAAK;YAAGqG,OAAO,EAAE9G,MAAA,CAAAuB,IAAI,CAACuF,OAAO;YAC5F7D,UAAQ,EAAEjD,MAAA,CAAAqL;;;QAvMnB9I,CAAA;yCAyMIpB,YAAA,CAEmB+J,2BAAA;QA3MvBnJ,UAAA,EAyM+B/B,MAAA,CAAAsL,oBAAoB;QAzMnD,uBAAArJ,MAAA,SAAAA,MAAA,iBAAAC,MAAA;UAAA,OAyM+BlC,MAAA,CAAAsL,oBAAoB,GAAApJ,MAAA;QAAA;QAAE8B,IAAI,EAAC;;QAzM1D9D,OAAA,EAAAC,QAAA,CA0MM;UAAA,OAAmG,CAAnGgB,YAAA,CAAmGnB,MAAA;YAAjFwF,IAAI,EAAExF,MAAA,CAAAmF,mBAAmB;YAAGlC,UAAQ,EAAEsI,IAAA,CAAAC;;;QA1M9DjJ,CAAA;;;IAAAA,CAAA;qEACwDvC,MAAA,CAAAyL,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}