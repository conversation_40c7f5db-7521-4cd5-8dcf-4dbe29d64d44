{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestReplyDetail\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_global_info_item = _resolveComponent(\"global-info-item\");\n  var _component_global_info_line = _resolveComponent(\"global-info-line\");\n  var _component_xyl_global_file = _resolveComponent(\"xyl-global-file\");\n  var _component_global_info = _resolveComponent(\"global-info\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"SuggestReplyDetailName\"\n  }, \"答复文件详情\", -1 /* HOISTED */)), _createVNode(_component_global_info, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"办理单位\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.handleOfficeName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"答复类型\"\n          }, {\n            default: _withCtx(function () {\n              var _$setup$details$sugge;\n              return [_createTextVNode(_toDisplayString((_$setup$details$sugge = $setup.details.suggestionAnswerType) === null || _$setup$details$sugge === void 0 ? void 0 : _$setup$details$sugge.label), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"提案评价\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.evaluate[$setup.extDatas.proposalEvaluation]), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"征询意见表满意度\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.satisfaction[$setup.extDatas.satisfactionEvaluation]), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"答复件红头文件\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_global_file, {\n            fileData: $setup.extDatas.attachmentsRed\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"答复件Word版\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_global_file, {\n            fileData: $setup.details.attachments\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"征询意见表扫描件\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_global_file, {\n            fileData: $setup.extDatas.attachmentsOpinion\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" <global-info-item label=\\\"答复内容\\\">\\r\\n        <div v-html=\\\"details.content\\\"></div>\\r\\n      </global-info-item> \")];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createVNode", "_component_global_info", "default", "_withCtx", "_component_global_info_line", "_component_global_info_item", "label", "_createTextVNode", "_toDisplayString", "$setup", "details", "handleOfficeName", "_", "_$setup$details$sugge", "suggestionAnswerType", "evaluate", "extDatas", "proposalEvaluation", "satisfaction", "satisfactionEvaluation", "_component_xyl_global_file", "fileData", "attachmentsRed", "attachments", "attachmentsOpinion", "_createCommentVNode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestReplyDetail\\SuggestReplyDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestReplyDetail\">\r\n    <div class=\"SuggestReplyDetailName\">答复文件详情</div>\r\n    <global-info>\r\n      <global-info-line>\r\n        <global-info-item label=\"办理单位\">{{ details.handleOfficeName }}</global-info-item>\r\n        <global-info-item label=\"答复类型\">{{ details.suggestionAnswerType?.label }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-line>\r\n        <global-info-item label=\"提案评价\">{{ evaluate[extDatas.proposalEvaluation] }}</global-info-item>\r\n        <global-info-item label=\"征询意见表满意度\">{{ satisfaction[extDatas.satisfactionEvaluation] }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-item label=\"答复件红头文件\">\r\n        <xyl-global-file :fileData=\"extDatas.attachmentsRed\"></xyl-global-file>\r\n      </global-info-item>\r\n      <global-info-item label=\"答复件Word版\">\r\n        <xyl-global-file :fileData=\"details.attachments\"></xyl-global-file>\r\n      </global-info-item>\r\n      <global-info-item label=\"征询意见表扫描件\">\r\n        <xyl-global-file :fileData=\"extDatas.attachmentsOpinion\"></xyl-global-file>\r\n      </global-info-item>\r\n      <!-- <global-info-item label=\"答复内容\">\r\n        <div v-html=\"details.content\"></div>\r\n      </global-info-item> -->\r\n    </global-info>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestReplyDetail' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\n\r\nconst details = ref({})\r\nconst extDatas = ref({})\r\n// 提案评价\r\nconst evaluate = {\r\n  1: '优秀',\r\n  2: '良好',\r\n  3: '一般'\r\n}\r\n// 满意度\r\nconst satisfaction = {\r\n  1: '满意',\r\n  2: '基本满意',\r\n  3: '不满意'\r\n}\r\nonMounted(() => { handingPortionAnswerInfo() })\r\n\r\nconst handingPortionAnswerInfo = async () => {\r\n  const { data, extData } = await api.handingPortionAnswerInfo({ detailId: props.id })\r\n  details.value = data\r\n  extDatas.value = extData.answersFileExt\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestReplyDetail {\r\n  width: 990px;\r\n  padding: 0 var(--zy-distance-one);\r\n  padding-top: var(--zy-distance-one);\r\n\r\n  .SuggestReplyDetailName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    color: var(--zy-el-color-primary);\r\n    border-bottom: 1px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .global-info {\r\n    padding-bottom: 12px;\r\n\r\n    .global-info-item {\r\n      .global-info-label {\r\n        width: 160px;\r\n      }\r\n\r\n      .global-info-content {\r\n        width: calc(100% - 160px);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;;;;;uBAA/BC,mBAAA,CAwBM,OAxBNC,UAwBM,G,0BAvBJC,mBAAA,CAAgD;IAA3CH,KAAK,EAAC;EAAwB,GAAC,QAAM,sBAC1CI,YAAA,CAqBcC,sBAAA;IAxBlBC,OAAA,EAAAC,QAAA,CAIM;MAAA,OAGmB,CAHnBH,YAAA,CAGmBI,2BAAA;QAPzBF,OAAA,EAAAC,QAAA,CAKQ;UAAA,OAAgF,CAAhFH,YAAA,CAAgFK,2BAAA;YAA9DC,KAAK,EAAC;UAAM;YALtCJ,OAAA,EAAAC,QAAA,CAKuC;cAAA,OAA8B,CALrEI,gBAAA,CAAAC,gBAAA,CAK0CC,MAAA,CAAAC,OAAO,CAACC,gBAAgB,iB;;YALlEC,CAAA;cAMQZ,YAAA,CAA2FK,2BAAA;YAAzEC,KAAK,EAAC;UAAM;YANtCJ,OAAA,EAAAC,QAAA,CAMuC;cAAA,IAAAU,qBAAA;cAAA,OAAyC,CANhFN,gBAAA,CAAAC,gBAAA,EAAAK,qBAAA,GAM0CJ,MAAA,CAAAC,OAAO,CAACI,oBAAoB,cAAAD,qBAAA,uBAA5BA,qBAAA,CAA8BP,KAAK,iB;;YAN7EM,CAAA;;;QAAAA,CAAA;UAQMZ,YAAA,CAGmBI,2BAAA;QAXzBF,OAAA,EAAAC,QAAA,CASQ;UAAA,OAA6F,CAA7FH,YAAA,CAA6FK,2BAAA;YAA3EC,KAAK,EAAC;UAAM;YATtCJ,OAAA,EAAAC,QAAA,CASuC;cAAA,OAA2C,CATlFI,gBAAA,CAAAC,gBAAA,CAS0CC,MAAA,CAAAM,QAAQ,CAACN,MAAA,CAAAO,QAAQ,CAACC,kBAAkB,kB;;YAT9EL,CAAA;cAUQZ,YAAA,CAAyGK,2BAAA;YAAvFC,KAAK,EAAC;UAAU;YAV1CJ,OAAA,EAAAC,QAAA,CAU2C;cAAA,OAAmD,CAV9FI,gBAAA,CAAAC,gBAAA,CAU8CC,MAAA,CAAAS,YAAY,CAACT,MAAA,CAAAO,QAAQ,CAACG,sBAAsB,kB;;YAV1FP,CAAA;;;QAAAA,CAAA;UAYMZ,YAAA,CAEmBK,2BAAA;QAFDC,KAAK,EAAC;MAAS;QAZvCJ,OAAA,EAAAC,QAAA,CAaQ;UAAA,OAAuE,CAAvEH,YAAA,CAAuEoB,0BAAA;YAArDC,QAAQ,EAAEZ,MAAA,CAAAO,QAAQ,CAACM;;;QAb7CV,CAAA;UAeMZ,YAAA,CAEmBK,2BAAA;QAFDC,KAAK,EAAC;MAAU;QAfxCJ,OAAA,EAAAC,QAAA,CAgBQ;UAAA,OAAmE,CAAnEH,YAAA,CAAmEoB,0BAAA;YAAjDC,QAAQ,EAAEZ,MAAA,CAAAC,OAAO,CAACa;;;QAhB5CX,CAAA;UAkBMZ,YAAA,CAEmBK,2BAAA;QAFDC,KAAK,EAAC;MAAU;QAlBxCJ,OAAA,EAAAC,QAAA,CAmBQ;UAAA,OAA2E,CAA3EH,YAAA,CAA2EoB,0BAAA;YAAzDC,QAAQ,EAAEZ,MAAA,CAAAO,QAAQ,CAACQ;;;QAnB7CZ,CAAA;UAqBMa,mBAAA,sHAEuB,C;;IAvB7Bb,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}