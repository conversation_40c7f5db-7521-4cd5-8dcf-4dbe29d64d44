<template>
  <div class="ScoreProportion">
    <el-table ref="tableRef" row-key="id" border :data="tableData">
      <el-table-column label="姓名" min-width="120" show-overflow-tooltip align="center">
        <template #default="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <el-table-column label="占比" min-width="120" show-overflow-tooltip align="center">
        <template #default="scope">
          <el-input v-model="scope.row.zhanbi" style="width: 100px;" />&nbsp;&nbsp;%
        </template>
      </el-table-column>
    </el-table>
    <!-- <p style="color: red;margin-top: 10px;font-size: 14px;">占比小于100%，请调整分配比例</p> -->
    <div class="globalPaperFormButton">
      <el-button type="primary" @click="confirm">确认</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'ScoreProportion' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
// import { ElMessage } from 'element-plus'
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['callback'])
const tableData = ref([])
// const tableData = ref([
//   { id: '1', name: '张三', zhanbi: '' },
//   { id: '2', name: '李四', zhanbi: '' },
//   { id: '3', name: '王五', zhanbi: '' }
// ])
onMounted(() => {
  tableData.value = props.data.map(item => {
    return {
      id: item.id,
      name: item.userName,
      zhanbi: '',
    }
  })
  console.log('tableData.value', tableData.value)
})

// 取消
const cancel = () => {
  emit('callback')
}
// 确认
const confirm = async () => {
  const { code } = await api.globalJson('/proposalAllocationScore/add', {
    form: {
      id: '',
      suggestionId: '',
      userId: '',
      userName: '',
      scoreProportion: ''
    }
  })
  console.log('code===>', code)
}
</script>
<style lang="scss">
.ScoreProportion {
  width: 600px;
  padding: 30px;

  .globalPaperFormButton {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 22px;

    .zy-el-button+.zy-el-button {
      margin-left: var(--zy-distance-two);
    }
  }
}
</style>
