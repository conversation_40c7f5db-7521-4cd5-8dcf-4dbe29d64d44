{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted, nextTick } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { format } from 'common/js/time.js';\nimport { ElMessage } from 'element-plus';\nimport SubmitExamine from \"./component/SubmitExamine\";\nimport SubmitReply from \"./component/SubmitReply\";\nimport SubmitHandle from \"./component/SubmitHandle\";\nimport publicOpinionPrint from '@/components/publicOpinionPrint/publicOpinionPrint';\nimport { qiankunMicro } from \"common/config/MicroGlobal\";\n// import {qiankunMicro} from \"common/config/MicroGlobal\";\n// import {Print} from \"common/js/print\";\n\nvar __default__ = {\n  name: 'OutcomeManageDetails'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    // const props = defineProps({ id: { type: String, default: '' } })\n    var details = ref({});\n    var printRef = ref();\n    var changeShow = ref(false);\n    var tableData = ref([]);\n    var scrollbarTop = ref(0);\n    var isActive = ref(1);\n    var auditRef = ref();\n    var transactRef = ref();\n    var dataRef = ref();\n    var scrollbarRef = ref();\n    var timer = ref();\n    var printParams = ref({});\n    var elPrintWhetherShow = ref(false);\n    var isScrollTop = ref(0);\n    onMounted(function () {\n      microAdviceInfo();\n    });\n    var scroll = function scroll(_ref2) {\n      var _auditRef$value, _transactRef$value;\n      var scrollTop = _ref2.scrollTop;\n      scrollbarTop.value = scrollTop;\n      if (scrollbarTop.value < ((_auditRef$value = auditRef.value) === null || _auditRef$value === void 0 ? void 0 : _auditRef$value.offsetTop)) {\n        isActive.value = 1;\n      } else if (scrollbarTop.value < (transactRef === null || transactRef === void 0 || (_transactRef$value = transactRef.value) === null || _transactRef$value === void 0 ? void 0 : _transactRef$value.offsetTop)) {\n        if (!auditRef.value) {\n          isActive.value = 1;\n        } else {\n          isActive.value = 2;\n        }\n      } else if (scrollbarTop.value < dataRef.value.offsetTop) {\n        isActive.value = 3;\n      }\n      if (scrollbarTop.value > isScrollTop.value) {\n        isActive.value = 3;\n      }\n    };\n    // const callback = (type) => {\n    //   elPrintWhetherShow.value = false\n    //   if (type) {\n    //     qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\n    //   }\n    // }\n    var printCallback = function printCallback() {\n      elPrintWhetherShow.value = false;\n    };\n    var AnchorLinkTo = function AnchorLinkTo(navEl) {\n      clearInterval(timer.value);\n      if (scrollbarTop.value >= navEl.offsetTop) {\n        timer.value = setInterval(function () {\n          if (scrollbarTop.value <= navEl.offsetTop) {\n            clearInterval(timer.value);\n          } else {\n            if (scrollbarTop.value - 52 <= navEl.offsetTop) {\n              var _scrollbarRef$value;\n              (_scrollbarRef$value = scrollbarRef.value) === null || _scrollbarRef$value === void 0 || _scrollbarRef$value.setScrollTop(navEl.offsetTop);\n            } else {\n              var _scrollbarRef$value2;\n              (_scrollbarRef$value2 = scrollbarRef.value) === null || _scrollbarRef$value2 === void 0 || _scrollbarRef$value2.setScrollTop(scrollbarTop.value - 52);\n            }\n          }\n        }, 2);\n      } else {\n        timer.value = setInterval(function () {\n          if (scrollbarTop.value >= navEl.offsetTop || scrollbarTop.value > isScrollTop.value) {\n            if (scrollbarTop.value > isScrollTop.value) {\n              isActive.value = 3;\n            }\n            clearInterval(timer.value);\n          } else {\n            if (scrollbarTop.value + 52 >= navEl.offsetTop) {\n              var _scrollbarRef$value3;\n              (_scrollbarRef$value3 = scrollbarRef.value) === null || _scrollbarRef$value3 === void 0 || _scrollbarRef$value3.setScrollTop(navEl.offsetTop);\n            } else {\n              var _scrollbarRef$value4;\n              (_scrollbarRef$value4 = scrollbarRef.value) === null || _scrollbarRef$value4 === void 0 || _scrollbarRef$value4.setScrollTop(scrollbarTop.value + 52);\n            }\n          }\n        }, 6);\n      }\n    };\n    var handlePrint = function handlePrint() {\n      // Print.init(printRef.value)\n      if (JSON.stringify(details.value) === '{}') return ElMessage({\n        type: 'warning',\n        message: '请等待详情加载完成再进行导出！'\n      });\n      printParams.value = {\n        ids: [route.query.id]\n      };\n      elPrintWhetherShow.value = true;\n    };\n    var onChange = function onChange(e) {\n      changeShow.value = true;\n      tableData.value = e;\n    };\n    var examineCallback = function examineCallback() {\n      qiankunMicro.setGlobalState({\n        closeOpenRoute: {\n          openId: route.query.oldRouteId,\n          closeId: route.query.routeId\n        }\n      });\n    };\n    var submitCallback = function submitCallback() {\n      qiankunMicro.setGlobalState({\n        closeOpenRoute: {\n          openId: route.query.oldRouteId,\n          closeId: route.query.routeId\n        }\n      });\n    };\n    var microAdviceInfo = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.microAdviceInfo({\n                detailId: route.query.id\n              });\n            case 2:\n              res = _context.sent;\n              data = res.data;\n              details.value = data;\n              details.value.isShow = true;\n              nextTick(function () {\n                var MinSuggestManageDetailsContent = scrollbarRef.value.$el.querySelector('.MinSuggestManageDetailsContent');\n                isScrollTop.value = MinSuggestManageDetailsContent.offsetHeight - scrollbarRef.value.$el.offsetHeight + 38;\n              });\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function microAdviceInfo() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      route,\n      details,\n      printRef,\n      changeShow,\n      tableData,\n      scrollbarTop,\n      isActive,\n      auditRef,\n      transactRef,\n      dataRef,\n      scrollbarRef,\n      timer,\n      printParams,\n      elPrintWhetherShow,\n      isScrollTop,\n      scroll,\n      printCallback,\n      AnchorLinkTo,\n      handlePrint,\n      onChange,\n      examineCallback,\n      submitCallback,\n      microAdviceInfo,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      nextTick,\n      get useRoute() {\n        return useRoute;\n      },\n      get format() {\n        return format;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get SubmitExamine() {\n        return SubmitExamine;\n      },\n      get SubmitReply() {\n        return SubmitReply;\n      },\n      get SubmitHandle() {\n        return SubmitHandle;\n      },\n      get publicOpinionPrint() {\n        return publicOpinionPrint;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "nextTick", "useRoute", "format", "ElMessage", "SubmitExamine", "SubmitReply", "SubmitHandle", "publicOpinionPrint", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__default__", "route", "details", "printRef", "changeShow", "tableData", "scrollbarTop", "isActive", "auditRef", "transactRef", "dataRef", "scrollbarRef", "timer", "printParams", "elPrintWhetherShow", "isScrollTop", "microAdviceInfo", "scroll", "_ref2", "_auditRef$value", "_transactRef$value", "scrollTop", "offsetTop", "printCallback", "AnchorLinkTo", "navEl", "clearInterval", "setInterval", "_scrollbarRef$value", "setScrollTop", "_scrollbarRef$value2", "_scrollbarRef$value3", "_scrollbarRef$value4", "handlePrint", "JSON", "stringify", "message", "ids", "query", "id", "onChange", "examine<PERSON><PERSON><PERSON>", "setGlobalState", "closeOpenRoute", "openId", "oldRouteId", "closeId", "routeId", "submitCallback", "_ref3", "_callee", "res", "data", "_callee$", "_context", "detailId", "isShow", "MinSuggestManageDetailsContent", "$el", "querySelector", "offsetHeight"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/negotiation/src/views/OutcomeManagement/OutcomeManageDetails.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar @scroll=\"scroll\" ref=\"scrollbarRef\" class=\"OutcomeManageDetails\">\r\n    <div class=\"MinSuggestManageDetailsLine\" v-if=\"details.auditInfo && details.auditInfo.auditUser\">\r\n      <div class=\"detailsNavigation\">\r\n        <div :class=\"['detailsNavigationItem', isActive === 1 ? 'ActivityIsActive' : '']\"\r\n          @click=\"AnchorLinkTo({ offsetTop: 0 })\">协商成果审核信息</div>\r\n        <div :class=\"['detailsNavigationItem', isActive === 2 ? 'ActivityIsActive' : '']\"\r\n          @click=\"AnchorLinkTo(auditRef)\">协商成果交办信息</div>\r\n        <div :class=\"['detailsNavigationItem', isActive === 3 ? 'ActivityIsActive' : '']\"\r\n          @click=\"AnchorLinkTo(dataRef)\">协商成果内容</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"MinSuggestManageDetailsContent\" ref=\"printRef\">\r\n      <div class=\"MinSuggestManageDetailsContentTitle\" ref=\"auditRef\"\r\n        v-if=\"details.auditInfo && details.auditInfo.auditUser\">\r\n        <div>\r\n          <img src=\"../../assets/img/column.png\" />\r\n          <span>协商成果审核信息</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"MinSuggestManageDetailsContentGlobal\" v-if=\"details.auditInfo && details.auditInfo.auditUser\">\r\n        <global-info>\r\n          <global-info-line>\r\n            <global-info-item label=\"审核者\">{{ details.auditInfo.auditUser }}</global-info-item>\r\n            <global-info-item label=\"审核时间\">{{ format(details.auditInfo.createDate, 'YYYY-MM-DD hh:mm')\r\n            }}</global-info-item>\r\n          </global-info-line>\r\n          <global-info-line>\r\n            <global-info-item label=\"审核结果\">{{ details.auditInfo.auditResult === 1 ? '通过' : '不通过' }}</global-info-item>\r\n            <global-info-item label=\"审核意见\">{{ details.auditInfo.auditOpinion }}</global-info-item>\r\n          </global-info-line>\r\n        </global-info>\r\n      </div>\r\n      <div class=\"MinSuggestManageDetailsContentTitle\" ref=\"transactRef\"\r\n        v-if=\"details.transactListVo && details.transactListVo.length > 0\">\r\n        <div>\r\n          <img src=\"../../assets/img/column.png\" />\r\n          <span>协商成果交办信息</span>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"onChange(details.changeList)\"\r\n          v-if=\"details.changeList && details.changeList.length > 0\">查看调整记录</el-button>\r\n      </div>\r\n      <div v-if=\"details.transactListVo && details.transactListVo.length > 0\">\r\n        <div class=\"MinSuggestManageDetailsContentGlobal\" v-for=\"item in details.transactListVo\"\r\n          :key=\"item.transactDate\">\r\n          <global-info>\r\n            <global-info-line>\r\n              <global-info-item label=\"交办人\">{{ item.transactUserName }}</global-info-item>\r\n              <global-info-item label=\"交办时间\">{{ format(item.transactDate, 'YYYY-MM-DD hh:mm') }}</global-info-item>\r\n            </global-info-line>\r\n            <global-info-line>\r\n              <global-info-item label=\"交办\">{{ item.transactTo }}</global-info-item>\r\n              <global-info-item label=\"交办意见\">{{ item.transactOpinion }}</global-info-item>\r\n            </global-info-line>\r\n          </global-info>\r\n        </div>\r\n      </div>\r\n      <div class=\"MinSuggestManageDetailsContentTitle\" ref=\"transactRef\"\r\n        v-if=\"details.negotiateGroupListVo && details.negotiateGroupListVo.length > 0\">\r\n        <div>\r\n          <img src=\"../../assets/img/column.png\" />\r\n          <span>协商成果交办信息</span>\r\n        </div>\r\n        <!-- <el-button type=\"primary\" @click=\"onChange(details.changeList)\"\r\n          v-if=\"details.changeList && details.changeList.length > 0\">查看调整记录</el-button> -->\r\n      </div>\r\n      <div v-if=\"details.negotiateGroupListVo && details.negotiateGroupListVo.length > 0\">\r\n        <div style=\"padding: 10px 40px;\" v-for=\"item in details.negotiateGroupListVo\" :key=\"item.transactDate\">\r\n          <global-info>\r\n            <global-info-line>\r\n              <global-info-item label=\"交办人\">{{ item.transactUserName }}</global-info-item>\r\n              <global-info-item label=\"交办时间\">{{ format(item.transactDate, 'YYYY-MM-DD hh:mm') }}</global-info-item>\r\n            </global-info-line>\r\n            <global-info-line>\r\n              <global-info-item :label=\"item.type\">{{ item.transactTo }}</global-info-item>\r\n              <global-info-item label=\"状态\">{{ item.statusName }}</global-info-item>\r\n            </global-info-line>\r\n            <global-info-item label=\"交办意见\">{{ item.transactOpinion }}</global-info-item>\r\n          </global-info>\r\n        </div>\r\n      </div>\r\n      <div class=\"MinSuggestManageDetailsContentTitle\" v-if=\"details.groupName || details.rejectInfo\">\r\n        <div>\r\n          <img src=\"../../assets/img/column.png\" />\r\n          <span>协商成果办理信息</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"MinSuggestManageDetailsContentGlobal\" v-if=\"details.rejectInfo && details.rejectInfo.groupName\">\r\n        <global-info>\r\n          <global-info-item label=\"办理单位\">{{ details.rejectInfo.groupName }}</global-info-item>\r\n          <global-info-item label=\"不予受理\">{{ details.rejectInfo.opinion }}</global-info-item>\r\n        </global-info>\r\n      </div>\r\n      <div class=\"MinSuggestManageDetailsContentGlobal\" v-if=\"details.groupName\">\r\n        <global-info>\r\n          <global-info-item label=\"办理单位\">{{ details.groupName }}</global-info-item>\r\n          <global-info-item label=\"回复内容\" v-for=\"item in details.replyList\" :key=\"item.id\">\r\n            <div>{{ item.opinion }}</div>\r\n            <div>{{ format(item.createDate, 'YYYY-MM-DD hh:mm') }}</div>\r\n            <xyl-global-file :fileData=\"item.attachments\"></xyl-global-file>\r\n          </global-info-item>\r\n          <global-info-item v-if=\"details.evaluationInfo\" label=\"满意度测评\">\r\n            {{ details.evaluationInfo?.appText }}{{ details.evaluationInfo?.opinion }}\r\n          </global-info-item>\r\n        </global-info>\r\n      </div>\r\n      <div>\r\n        <!-- 审核 -->\r\n        <SubmitExamine v-if=\"details.showAudit\" :id=\"route.query.id\" name=\"审核\" width=\"100%\" @callback=\"examineCallback\"\r\n          nextNodeId=\"\"></SubmitExamine>\r\n        <!-- 回复 -->\r\n        <SubmitReply v-if=\"route.query.userType && details.isShow\" :id=\"route.query.id\" name=\"回复\" width=\"100%\"\r\n          :userType=\"route.query.userType\" @callback=\"submitCallback\"></SubmitReply>\r\n        <!-- 交办 -->\r\n        <SubmitHandle v-if=\"details.showHandle && !route.query.userType\" :id=\"route.query.id\" name=\"交办\" width=\"100%\"\r\n          @callback=\"examineCallback\"></SubmitHandle>\r\n      </div>\r\n      <div class=\"MinSuggestManageDetailsContentLine\" v-if=\"details.auditInfo && details.auditInfo.auditUser\"></div>\r\n      <div class=\"MinSuggestManageDetailsContentRecommendation\" ref=\"dataRef\">\r\n        <div class=\"MinSuggestManageDetailsContentRecommendationTitle\">协商成果</div>\r\n        <div class=\"MinSuggestManageDetailsContentRecommendationTime\">提交时间：\r\n          {{ format(details.submitDate, 'YYYY-MM-DD hh: mm') }}\r\n        </div>\r\n        <div class=\"MinSuggestManageDetailsContentRecommendationName\">{{ details.title }}</div>\r\n        <div class=\"info\">\r\n          <div class=\"info_item\">\r\n            <div class=\"name\">提交人:</div>\r\n            <div class=\"value\">{{ details.submitUserName }}</div>\r\n          </div>\r\n          <div class=\"info_item\">\r\n            <div class=\"name\">委员证号:</div>\r\n            <div class=\"value\">{{ details.submitUserInfo?.cardNumber }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"info\">\r\n          <div class=\"info_item\">\r\n            <div class=\"name\">界别:</div>\r\n            <div class=\"value\">{{ details.submitUserInfo?.sector }}</div>\r\n          </div>\r\n          <div class=\"info_item\">\r\n            <div class=\"name\">联系电话:</div>\r\n            <div class=\"value\">{{ details.submitUserInfo?.mobile }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"info\">\r\n          <div class=\"info_item\">\r\n            <div class=\"name\">办公电话:</div>\r\n            <div class=\"value\">{{ details.submitUserInfo?.officePhone }}</div>\r\n          </div>\r\n          <div class=\"info_item\">\r\n            <div class=\"name\">邮政编码:</div>\r\n            <div class=\"value\">{{ details.submitUserInfo?.postcode }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"info\">\r\n          <div class=\"name\">单位及职务:</div>\r\n          <div class=\"value\">{{ details.submitUserInfo?.position }}</div>\r\n        </div>\r\n        <div class=\"info\">\r\n          <div class=\"name\">通讯地址:</div>\r\n          <div class=\"value\">{{ details.submitUserInfo?.callAddress }}</div>\r\n        </div>\r\n        <div class=\"info_content\" v-html=\"details.content\"></div>\r\n        <xyl-global-file :fileData=\"details.attachments\"></xyl-global-file>\r\n      </div>\r\n      <div class=\"SuggestDetailInfoName\" v-if=\"details.colarUserInfo\">领办人</div>\r\n      <div class=\"SuggestDetailTable\" v-if=\"details.colarUserInfo\">\r\n        <div class=\"SuggestDetailTableHead\">\r\n          <div class=\"SuggestDetailTableItem row1\">姓名</div>\r\n          <div class=\"SuggestDetailTableItem row1\">委员证号</div>\r\n          <div class=\"SuggestDetailTableItem row1\">联系电话</div>\r\n          <div class=\"SuggestDetailTableItem row3\">通讯地址</div>\r\n        </div>\r\n        <div class=\"SuggestDetailTableBody\">\r\n          <div class=\"SuggestDetailTableItem row1\">{{ details.colarUserInfo?.userName }}</div>\r\n          <div class=\"SuggestDetailTableItem row1\">{{ details.colarUserInfo?.cardNumber }}</div>\r\n          <div class=\"SuggestDetailTableItem row1\">{{ details.colarUserInfo?.mobile }}</div>\r\n          <div class=\"SuggestDetailTableItem row3\">{{ details.colarUserInfo?.callAddress }}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"SuggestDetailInfoName\" v-if=\"details.inviteList?.length\">协办人</div>\r\n      <div class=\"SuggestDetailTable\" v-if=\"details.inviteList?.length\">\r\n        <div class=\"SuggestDetailTableHead\">\r\n          <div class=\"SuggestDetailTableItem row1\">姓名</div>\r\n          <div class=\"SuggestDetailTableItem row1\">委员证号</div>\r\n          <div class=\"SuggestDetailTableItem row1\">联系电话</div>\r\n          <div class=\"SuggestDetailTableItem row3\">通讯地址</div>\r\n        </div>\r\n        <div class=\"SuggestDetailTableBody\" v-for=\"item in details.inviteList\" :key=\"item.userId\">\r\n          <div class=\"SuggestDetailTableItem row1\">{{ item.userName }}</div>\r\n          <div class=\"SuggestDetailTableItem row1\">{{ item.cardNumber }}</div>\r\n          <div class=\"SuggestDetailTableItem row1\">{{ item.mobile }}</div>\r\n          <div class=\"SuggestDetailTableItem row3\">{{ item.callAddress }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"MinSuggestManageDetailsPrint\">\r\n      <div @click=\"handlePrint\" class=\"detailsFunction\">\r\n        <img src=\"../../assets/img/print.png\" />\r\n        <span>打印</span>\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"changeShow\" name=\"查看调整记录\">\r\n      <div class=\"globalTable\">\r\n        <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\">\r\n          <el-table-column label=\"调整单位\" min-width=\"120\" prop=\"groupName\" />\r\n          <el-table-column label=\"调整类型\" min-width=\"100\" prop=\"typeName\" />\r\n          <el-table-column label=\"提交时间\" min-width=\"120\" prop=\"submitDate\">\r\n            <template #default=\"scope\">\r\n              {{ format(scope.row.submitDate, 'YYYY-MM-DD') }}\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"原因说明\" min-width=\"120\" prop=\"opinion\" />\r\n          <el-table-column label=\"审核结果\" min-width=\"180\" prop=\"auditResult\" />\r\n        </el-table>\r\n      </div>\r\n    </xyl-popup-window>\r\n    <publicOpinionPrint v-if=\"elPrintWhetherShow\" :params=\"printParams\" @callback=\"printCallback\"></publicOpinionPrint>\r\n\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'OutcomeManageDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport SubmitExamine from \"./component/SubmitExamine\"\r\nimport SubmitReply from \"./component/SubmitReply\"\r\nimport SubmitHandle from \"./component/SubmitHandle\"\r\nimport publicOpinionPrint from '@/components/publicOpinionPrint/publicOpinionPrint'\r\nimport { qiankunMicro } from \"common/config/MicroGlobal\";\r\n// import {qiankunMicro} from \"common/config/MicroGlobal\";\r\n// import {Print} from \"common/js/print\";\r\nconst route = useRoute()\r\n// const props = defineProps({ id: { type: String, default: '' } })\r\nconst details = ref({})\r\nconst printRef = ref()\r\nconst changeShow = ref(false)\r\nconst tableData = ref([])\r\nconst scrollbarTop = ref(0)\r\nconst isActive = ref(1)\r\nconst auditRef = ref()\r\nconst transactRef = ref()\r\nconst dataRef = ref()\r\nconst scrollbarRef = ref()\r\nconst timer = ref()\r\nconst printParams = ref({})\r\nconst elPrintWhetherShow = ref(false)\r\nconst isScrollTop = ref(0)\r\nonMounted(() => { microAdviceInfo() })\r\nconst scroll = ({ scrollTop }) => {\r\n  scrollbarTop.value = scrollTop\r\n  if (scrollbarTop.value < auditRef.value?.offsetTop) {\r\n    isActive.value = 1\r\n  } else if (scrollbarTop.value < transactRef?.value?.offsetTop) {\r\n    if (!auditRef.value) {\r\n      isActive.value = 1\r\n    } else {\r\n      isActive.value = 2\r\n    }\r\n  } else if (scrollbarTop.value < dataRef.value.offsetTop) {\r\n    isActive.value = 3\r\n  }\r\n  if (scrollbarTop.value > isScrollTop.value) {\r\n    isActive.value = 3\r\n  }\r\n}\r\n// const callback = (type) => {\r\n//   elPrintWhetherShow.value = false\r\n//   if (type) {\r\n//     qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n//   }\r\n// }\r\nconst printCallback = () => {\r\n  elPrintWhetherShow.value = false\r\n}\r\n\r\nconst AnchorLinkTo = (navEl) => {\r\n  clearInterval(timer.value)\r\n  if (scrollbarTop.value >= navEl.offsetTop) {\r\n    timer.value = setInterval(function () {\r\n      if (scrollbarTop.value <= navEl.offsetTop) {\r\n        clearInterval(timer.value)\r\n      } else {\r\n        if ((scrollbarTop.value - 52) <= navEl.offsetTop) {\r\n          scrollbarRef.value?.setScrollTop(navEl.offsetTop)\r\n        } else {\r\n          scrollbarRef.value?.setScrollTop(scrollbarTop.value - 52)\r\n        }\r\n      }\r\n    }, 2)\r\n  } else {\r\n    timer.value = setInterval(function () {\r\n      if (scrollbarTop.value >= navEl.offsetTop || scrollbarTop.value > isScrollTop.value) {\r\n        if (scrollbarTop.value > isScrollTop.value) { isActive.value = 3 }\r\n        clearInterval(timer.value)\r\n      } else {\r\n        if ((scrollbarTop.value + 52) >= navEl.offsetTop) {\r\n          scrollbarRef.value?.setScrollTop(navEl.offsetTop)\r\n        } else {\r\n          scrollbarRef.value?.setScrollTop(scrollbarTop.value + 52)\r\n        }\r\n      }\r\n    }, 6)\r\n  }\r\n}\r\nconst handlePrint = () => {\r\n  // Print.init(printRef.value)\r\n  if (JSON.stringify(details.value) === '{}') return ElMessage({ type: 'warning', message: '请等待详情加载完成再进行导出！' })\r\n  printParams.value = { ids: [route.query.id] }\r\n  elPrintWhetherShow.value = true\r\n\r\n}\r\nconst onChange = (e) => {\r\n  changeShow.value = true\r\n  tableData.value = e\r\n}\r\nconst examineCallback = () => {\r\n  qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n}\r\nconst submitCallback = () => {\r\n  qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n}\r\nconst microAdviceInfo = async () => {\r\n  const res = await api.microAdviceInfo({ detailId: route.query.id })\r\n  var { data } = res\r\n  details.value = data\r\n  details.value.isShow = true\r\n  nextTick(() => {\r\n    const MinSuggestManageDetailsContent = scrollbarRef.value.$el.querySelector('.MinSuggestManageDetailsContent')\r\n    isScrollTop.value = (MinSuggestManageDetailsContent.offsetHeight - scrollbarRef.value.$el.offsetHeight) + 38\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.OutcomeManageDetails {\r\n  width: 100%;\r\n  padding: 20px 20px 0 20px;\r\n  height: 100%;\r\n\r\n  .MinSuggestManageDetailsLine {\r\n    width: 990px;\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n\r\n    .detailsNavigation {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      transform: translate(-120%, -50%);\r\n\r\n      .detailsNavigationItem {\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-right: 20px;\r\n        margin-bottom: 40px;\r\n        position: relative;\r\n        cursor: pointer;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 50%;\r\n          right: 0;\r\n          width: 6px;\r\n          height: 6px;\r\n          border-radius: 50%;\r\n          border: 2px solid var(--zy-el-border-color-lighter);\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: calc(50% + 5px);\r\n          right: 4px;\r\n          width: 2px;\r\n          height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + 30px);\r\n          background-color: var(--zy-el-border-color-lighter);\r\n        }\r\n\r\n        &:last-child {\r\n          &::before {\r\n            background-color: transparent;\r\n          }\r\n        }\r\n      }\r\n\r\n      .ActivityIsActive {\r\n        font-weight: bold;\r\n\r\n        &::after {\r\n          border: 2px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .MinSuggestManageDetailsContent {\r\n    max-width: 990px;\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    position: relative;\r\n    padding: 20px 40px;\r\n\r\n    .SuggestDetailInfoName {\r\n      padding-top: 15px;\r\n      padding-bottom: 5px;\r\n      font-weight: bold;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .MinSuggestManageDetailsContentTitle {\r\n      div {\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n\r\n      margin-top: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding:0 40px;\r\n      font-size: var(--zy-name-font-size);\r\n      font-weight: bold;\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      img {\r\n        width: 20px;\r\n        height: 20px;\r\n        margin-right: 6px;\r\n      }\r\n    }\r\n\r\n    .MinSuggestManageDetailsContentGlobal {\r\n      padding: 20px 40px;\r\n    }\r\n\r\n    .MinSuggestManageDetailsContentLine {\r\n      background-color: #F8F8F8;\r\n      height: 10px;\r\n      width: 100%;\r\n    }\r\n\r\n    .MinSuggestManageDetailsContentRecommendation {\r\n      padding: 40px;\r\n\r\n      .MinSuggestManageDetailsContentRecommendationTitle {\r\n        padding-bottom: 20px;\r\n        text-align: center;\r\n        font-weight: bold;\r\n        font-size: var(--zy-title-font-size);\r\n        border-bottom: 2px solid #3657C0;\r\n      }\r\n\r\n      .MinSuggestManageDetailsContentRecommendationTime {\r\n        font-size: var(--zy-text-font-size);\r\n        color: #999999;\r\n        margin-top: 20px;\r\n      }\r\n\r\n      .MinSuggestManageDetailsContentRecommendationName {\r\n        font-size: 20px;\r\n        margin-top: 20px;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .info {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        display: flex;\r\n        font-size: 14px;\r\n\r\n        .info_item {\r\n          width: 50%;\r\n          display: flex;\r\n        }\r\n\r\n        .name {\r\n          width: 80px;\r\n        }\r\n      }\r\n\r\n      .info_content {\r\n        margin-top: 40px;\r\n        line-height: 32px;\r\n        font-size: var(zy-name-font-size);\r\n        font-weight: 400;\r\n        text-indent: 30px;\r\n        padding-bottom: 10px;\r\n\r\n      }\r\n    }\r\n\r\n    .SuggestDetailTable {\r\n      width: 100%;\r\n      margin-bottom: 20px;\r\n      border-top: 1px solid var(--zy-el-border-color-lighter);\r\n      border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n      .SuggestDetailTableHead,\r\n      .SuggestDetailTableBody {\r\n        width: 100%;\r\n        display: flex;\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      .SuggestDetailTableHead {\r\n        background-color: var(--zy-el-color-info-light-9);\r\n      }\r\n\r\n      .SuggestDetailTableBody {\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      .SuggestDetailTableItem {\r\n        text-align: center;\r\n        border-left: 1px solid var(--zy-el-border-color-lighter);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 10px;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .row1 {\r\n        flex: 1;\r\n      }\r\n\r\n      .row2 {\r\n        flex: 2;\r\n      }\r\n\r\n      .row3 {\r\n        flex: 3;\r\n      }\r\n\r\n      .row5 {\r\n        flex: 5;\r\n      }\r\n    }\r\n  }\r\n\r\n  .MinSuggestManageDetailsPrint {\r\n    width: 990px;\r\n    position: absolute;\r\n    top: 50px;\r\n    left: 51%;\r\n    transform: translateX(-50%);\r\n\r\n    .detailsFunction {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      transform: translateX(112%);\r\n      font-size: var(zy-text-font-size);\r\n    }\r\n\r\n    img {\r\n      width: 20px;\r\n      height: 20px;\r\n      margin-right: 6px;\r\n    }\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1580px) {\r\n  .OutcomeManageDetails {\r\n    .MinSuggestManageDetailsLine {\r\n      .detailsNavigation {\r\n        .detailsNavigationItem {\r\n          color: transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .MinSuggestManageDetailsPrint {\r\n      .detailsFunction {\r\n        color: transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAmOA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,kBAAkB,MAAM,oDAAoD;AACnF,SAASC,YAAY,QAAQ,2BAA2B;AACxD;AACA;;AAdA,IAAAC,WAAA,GAAe;EAAEvC,IAAI,EAAE;AAAuB,CAAC;;;;;IAe/C,IAAMwC,KAAK,GAAGT,QAAQ,CAAC,CAAC;IACxB;IACA,IAAMU,OAAO,GAAGb,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMc,QAAQ,GAAGd,GAAG,CAAC,CAAC;IACtB,IAAMe,UAAU,GAAGf,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMgB,SAAS,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMiB,YAAY,GAAGjB,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAMkB,QAAQ,GAAGlB,GAAG,CAAC,CAAC,CAAC;IACvB,IAAMmB,QAAQ,GAAGnB,GAAG,CAAC,CAAC;IACtB,IAAMoB,WAAW,GAAGpB,GAAG,CAAC,CAAC;IACzB,IAAMqB,OAAO,GAAGrB,GAAG,CAAC,CAAC;IACrB,IAAMsB,YAAY,GAAGtB,GAAG,CAAC,CAAC;IAC1B,IAAMuB,KAAK,GAAGvB,GAAG,CAAC,CAAC;IACnB,IAAMwB,WAAW,GAAGxB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAMyB,kBAAkB,GAAGzB,GAAG,CAAC,KAAK,CAAC;IACrC,IAAM0B,WAAW,GAAG1B,GAAG,CAAC,CAAC,CAAC;IAC1BC,SAAS,CAAC,YAAM;MAAE0B,eAAe,CAAC,CAAC;IAAC,CAAC,CAAC;IACtC,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAAC,KAAA,EAAsB;MAAA,IAAAC,eAAA,EAAAC,kBAAA;MAAA,IAAhBC,SAAS,GAAAH,KAAA,CAATG,SAAS;MACzBf,YAAY,CAACtH,KAAK,GAAGqI,SAAS;MAC9B,IAAIf,YAAY,CAACtH,KAAK,KAAAmI,eAAA,GAAGX,QAAQ,CAACxH,KAAK,cAAAmI,eAAA,uBAAdA,eAAA,CAAgBG,SAAS,GAAE;QAClDf,QAAQ,CAACvH,KAAK,GAAG,CAAC;MACpB,CAAC,MAAM,IAAIsH,YAAY,CAACtH,KAAK,IAAGyH,WAAW,aAAXA,WAAW,gBAAAW,kBAAA,GAAXX,WAAW,CAAEzH,KAAK,cAAAoI,kBAAA,uBAAlBA,kBAAA,CAAoBE,SAAS,GAAE;QAC7D,IAAI,CAACd,QAAQ,CAACxH,KAAK,EAAE;UACnBuH,QAAQ,CAACvH,KAAK,GAAG,CAAC;QACpB,CAAC,MAAM;UACLuH,QAAQ,CAACvH,KAAK,GAAG,CAAC;QACpB;MACF,CAAC,MAAM,IAAIsH,YAAY,CAACtH,KAAK,GAAG0H,OAAO,CAAC1H,KAAK,CAACsI,SAAS,EAAE;QACvDf,QAAQ,CAACvH,KAAK,GAAG,CAAC;MACpB;MACA,IAAIsH,YAAY,CAACtH,KAAK,GAAG+H,WAAW,CAAC/H,KAAK,EAAE;QAC1CuH,QAAQ,CAACvH,KAAK,GAAG,CAAC;MACpB;IACF,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA,IAAMuI,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1BT,kBAAkB,CAAC9H,KAAK,GAAG,KAAK;IAClC,CAAC;IAED,IAAMwI,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAK;MAC9BC,aAAa,CAACd,KAAK,CAAC5H,KAAK,CAAC;MAC1B,IAAIsH,YAAY,CAACtH,KAAK,IAAIyI,KAAK,CAACH,SAAS,EAAE;QACzCV,KAAK,CAAC5H,KAAK,GAAG2I,WAAW,CAAC,YAAY;UACpC,IAAIrB,YAAY,CAACtH,KAAK,IAAIyI,KAAK,CAACH,SAAS,EAAE;YACzCI,aAAa,CAACd,KAAK,CAAC5H,KAAK,CAAC;UAC5B,CAAC,MAAM;YACL,IAAKsH,YAAY,CAACtH,KAAK,GAAG,EAAE,IAAKyI,KAAK,CAACH,SAAS,EAAE;cAAA,IAAAM,mBAAA;cAChD,CAAAA,mBAAA,GAAAjB,YAAY,CAAC3H,KAAK,cAAA4I,mBAAA,eAAlBA,mBAAA,CAAoBC,YAAY,CAACJ,KAAK,CAACH,SAAS,CAAC;YACnD,CAAC,MAAM;cAAA,IAAAQ,oBAAA;cACL,CAAAA,oBAAA,GAAAnB,YAAY,CAAC3H,KAAK,cAAA8I,oBAAA,eAAlBA,oBAAA,CAAoBD,YAAY,CAACvB,YAAY,CAACtH,KAAK,GAAG,EAAE,CAAC;YAC3D;UACF;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,MAAM;QACL4H,KAAK,CAAC5H,KAAK,GAAG2I,WAAW,CAAC,YAAY;UACpC,IAAIrB,YAAY,CAACtH,KAAK,IAAIyI,KAAK,CAACH,SAAS,IAAIhB,YAAY,CAACtH,KAAK,GAAG+H,WAAW,CAAC/H,KAAK,EAAE;YACnF,IAAIsH,YAAY,CAACtH,KAAK,GAAG+H,WAAW,CAAC/H,KAAK,EAAE;cAAEuH,QAAQ,CAACvH,KAAK,GAAG,CAAC;YAAC;YACjE0I,aAAa,CAACd,KAAK,CAAC5H,KAAK,CAAC;UAC5B,CAAC,MAAM;YACL,IAAKsH,YAAY,CAACtH,KAAK,GAAG,EAAE,IAAKyI,KAAK,CAACH,SAAS,EAAE;cAAA,IAAAS,oBAAA;cAChD,CAAAA,oBAAA,GAAApB,YAAY,CAAC3H,KAAK,cAAA+I,oBAAA,eAAlBA,oBAAA,CAAoBF,YAAY,CAACJ,KAAK,CAACH,SAAS,CAAC;YACnD,CAAC,MAAM;cAAA,IAAAU,oBAAA;cACL,CAAAA,oBAAA,GAAArB,YAAY,CAAC3H,KAAK,cAAAgJ,oBAAA,eAAlBA,oBAAA,CAAoBH,YAAY,CAACvB,YAAY,CAACtH,KAAK,GAAG,EAAE,CAAC;YAC3D;UACF;QACF,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC;IACD,IAAMiJ,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB;MACA,IAAIC,IAAI,CAACC,SAAS,CAACjC,OAAO,CAAClH,KAAK,CAAC,KAAK,IAAI,EAAE,OAAO0G,SAAS,CAAC;QAAEvF,IAAI,EAAE,SAAS;QAAEiI,OAAO,EAAE;MAAkB,CAAC,CAAC;MAC7GvB,WAAW,CAAC7H,KAAK,GAAG;QAAEqJ,GAAG,EAAE,CAACpC,KAAK,CAACqC,KAAK,CAACC,EAAE;MAAE,CAAC;MAC7CzB,kBAAkB,CAAC9H,KAAK,GAAG,IAAI;IAEjC,CAAC;IACD,IAAMwJ,QAAQ,GAAG,SAAXA,QAAQA,CAAIjK,CAAC,EAAK;MACtB6H,UAAU,CAACpH,KAAK,GAAG,IAAI;MACvBqH,SAAS,CAACrH,KAAK,GAAGT,CAAC;IACrB,CAAC;IACD,IAAMkK,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B1C,YAAY,CAAC2C,cAAc,CAAC;QAAEC,cAAc,EAAE;UAAEC,MAAM,EAAE3C,KAAK,CAACqC,KAAK,CAACO,UAAU;UAAEC,OAAO,EAAE7C,KAAK,CAACqC,KAAK,CAACS;QAAQ;MAAE,CAAC,CAAC;IACnH,CAAC;IACD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3BjD,YAAY,CAAC2C,cAAc,CAAC;QAAEC,cAAc,EAAE;UAAEC,MAAM,EAAE3C,KAAK,CAACqC,KAAK,CAACO,UAAU;UAAEC,OAAO,EAAE7C,KAAK,CAACqC,KAAK,CAACS;QAAQ;MAAE,CAAC,CAAC;IACnH,CAAC;IACD,IAAM/B,eAAe;MAAA,IAAAiC,KAAA,GAAAlE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwF,QAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAA9K,mBAAA,GAAAuB,IAAA,UAAAwJ,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAnF,IAAA,GAAAmF,QAAA,CAAA9G,IAAA;YAAA;cAAA8G,QAAA,CAAA9G,IAAA;cAAA,OACJ4C,GAAG,CAAC4B,eAAe,CAAC;gBAAEuC,QAAQ,EAAEtD,KAAK,CAACqC,KAAK,CAACC;cAAG,CAAC,CAAC;YAAA;cAA7DY,GAAG,GAAAG,QAAA,CAAArH,IAAA;cACHmH,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVlD,OAAO,CAAClH,KAAK,GAAGoK,IAAI;cACpBlD,OAAO,CAAClH,KAAK,CAACwK,MAAM,GAAG,IAAI;cAC3BjE,QAAQ,CAAC,YAAM;gBACb,IAAMkE,8BAA8B,GAAG9C,YAAY,CAAC3H,KAAK,CAAC0K,GAAG,CAACC,aAAa,CAAC,iCAAiC,CAAC;gBAC9G5C,WAAW,CAAC/H,KAAK,GAAIyK,8BAA8B,CAACG,YAAY,GAAGjD,YAAY,CAAC3H,KAAK,CAAC0K,GAAG,CAACE,YAAY,GAAI,EAAE;cAC9G,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAN,QAAA,CAAAhF,IAAA;UAAA;QAAA,GAAA4E,OAAA;MAAA,CACH;MAAA,gBATKlC,eAAeA,CAAA;QAAA,OAAAiC,KAAA,CAAAhE,KAAA,OAAAD,SAAA;MAAA;IAAA,GASpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}