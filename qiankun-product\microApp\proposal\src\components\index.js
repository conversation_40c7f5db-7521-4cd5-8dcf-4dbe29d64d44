import TinyMceEditor from 'common/components/TinyMceEditor/TinyMceEditor.vue'
import GlobalMarkdown from 'common/components/global-markdown/global-markdown.vue'
import SuggestSelectUnit from './suggest-select-unit/suggest-select-unit.vue'
import SuggestSimpleSelectUnit from './suggest-simple-select-unit/suggest-simple-select-unit.vue'
const components = [TinyMceEditor, GlobalMarkdown, SuggestSelectUnit, SuggestSimpleSelectUnit]
export default { install (app) { components.forEach(v => app.component(v.name, v)) } }
