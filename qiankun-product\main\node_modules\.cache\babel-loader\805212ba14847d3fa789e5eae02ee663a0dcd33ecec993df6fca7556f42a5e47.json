{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, vShow as _vShow, createElementVNode as _createElementVNode, withDirectives as _withDirectives, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createCommentVNode as _createCommentVNode, withModifiers as _withModifiers, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective } from \"vue\";\nvar _hoisted_1 = [\"element-loading-spinner\", \"lement-loading-text\"];\nvar _hoisted_2 = {\n  class: \"ContentExtractionHead\"\n};\nvar _hoisted_3 = {\n  class: \"ContentExtractionButton\"\n};\nvar _hoisted_4 = {\n  class: \"ContentExtractionButtonItem\"\n};\nvar _hoisted_5 = {\n  class: \"ContentExtractionButton\"\n};\nvar _hoisted_6 = {\n  class: \"ContentExtractionButtonItem\"\n};\nvar _hoisted_7 = {\n  class: \"ContentExtractionBody\"\n};\nvar _hoisted_8 = {\n  class: \"ContentExtractionBodyLeft\"\n};\nvar _hoisted_9 = {\n  class: \"ContentExtractionUploadBody\"\n};\nvar _hoisted_10 = {\n  class: \"ContentExtractionUpload\"\n};\nvar _hoisted_11 = {\n  class: \"zy-el-upload__tip\"\n};\nvar _hoisted_12 = {\n  class: \"ContentExtractionUploadProgressInfo\"\n};\nvar _hoisted_13 = {\n  class: \"ContentExtractionUploadProgress\"\n};\nvar _hoisted_14 = {\n  class: \"ContentExtractionUploadProgressBox\"\n};\nvar _hoisted_15 = {\n  class: \"ContentExtractionUploadProgressName ellipsis\"\n};\nvar _hoisted_16 = {\n  class: \"ContentExtractionUploadHistoryList\"\n};\nvar _hoisted_17 = [\"onClick\"];\nvar _hoisted_18 = {\n  class: \"ContentExtractionUploadHistoryItemName ellipsis\"\n};\nvar _hoisted_19 = {\n  class: \"ContentExtractionUploadHistoryItemInfo\"\n};\nvar _hoisted_20 = {\n  class: \"ContentExtractionUploadHistoryItemFileSize\"\n};\nvar _hoisted_21 = {\n  class: \"ContentExtractionUploadHistoryItemTime\"\n};\nvar _hoisted_22 = {\n  key: 0,\n  class: \"ContentExtractionUploadHistoryLoadingText\"\n};\nvar _hoisted_23 = {\n  key: 1,\n  class: \"ContentExtractionUploadHistoryLoadingText\"\n};\nvar _hoisted_24 = {\n  key: 0,\n  class: \"ContentExtractionWord\"\n};\nvar _hoisted_25 = {\n  class: \"ContentExtractionBodyRight\"\n};\nvar _hoisted_26 = {\n  class: \"ContentExtractionChatBody\"\n};\nvar _hoisted_27 = {\n  class: \"ContentExtractionChatBodyEditor\"\n};\nvar _hoisted_28 = {\n  class: \"ContentExtractionChatBodyEditorBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_upload_filled = _resolveComponent(\"upload-filled\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_upload = _resolveComponent(\"el-upload\");\n  var _component_el_progress = _resolveComponent(\"el-progress\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"ContentExtraction\",\n    \"element-loading-spinner\": $setup.svg,\n    \"lement-loading-text\": $setup.loadingText,\n    \"element-loading-svg-view-box\": \"-10, -10, 50, 50\"\n  }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_withDirectives(_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"重新上传\")]);\n    }),\n    _: 1 /* STABLE */\n  })], 512 /* NEED_PATCH */), [[_vShow, $setup.file.id]]), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"ContentExtractionButtonItem\"\n  }, null, -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_5, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"ContentExtractionButtonItem\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n    type: \"primary\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[4] || (_cache[4] = [_createTextVNode(\"导出\")]);\n    }),\n    _: 1 /* STABLE */\n  })])])]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_upload, {\n    drag: \"\",\n    action: \"/\",\n    \"before-upload\": $setup.handleFile,\n    \"http-request\": $setup.fileUpload,\n    \"show-file-list\": false,\n    multiple: \"\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_icon, {\n        class: \"zy-el-icon--upload\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_upload_filled)];\n        }),\n        _: 1 /* STABLE */\n      }), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"zy-el-upload__text\"\n      }, [_createTextVNode(\" 将附件拖拽至此区域，或 \"), _createElementVNode(\"em\", null, \"点击上传\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, \"仅支持\" + _toDisplayString(['doc', 'docx', 'pdf'].join('、')) + \"格式\", 1 /* TEXT */)];\n    }),\n    _: 1 /* STABLE */\n  }), _withDirectives(_createElementVNode(\"div\", {\n    class: \"ContentExtractionUploadProgressBody\",\n    onClick: _cache[0] || (_cache[0] = _withModifiers(function () {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon($setup.progressType)])\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.progressText), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"ContentExtractionUploadProgressText\"\n  }, \"正在解析\", -1 /* HOISTED */))]), _createCommentVNode(\" <div class=\\\"ContentExtractionUploadProgressClose\\\">\\r\\n                    <el-icon><Close /></el-icon>\\r\\n                  </div> \")]), _createVNode(_component_el_progress, {\n    percentage: $setup.fileProgress,\n    \"show-text\": false,\n    \"stroke-width\": 12\n  }, null, 8 /* PROPS */, [\"percentage\"])])], 512 /* NEED_PATCH */), [[_vShow, $setup.isShowProgress]])]), _cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"ContentExtractionUploadHistoryTitle\"\n  }, \"最近阅读\", -1 /* HOISTED */)), _createVNode(_component_el_scrollbar, {\n    class: \"ContentExtractionUploadHistoryScroll\",\n    ref: \"scrollRef\",\n    onScroll: $setup.handleScroll\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"ContentExtractionUploadHistoryItem\",\n          key: item.id,\n          onClick: function onClick($event) {\n            return $setup.handleHistory(item);\n          }\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon(item.fileName)])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_18, _toDisplayString(item.fileName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, _toDisplayString(item !== null && item !== void 0 && item.fileSize ? $setup.size2Str(item === null || item === void 0 ? void 0 : item.fileSize) : '0KB'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_21, _toDisplayString($setup.format(item.createDate)), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_17);\n      }), 128 /* KEYED_FRAGMENT */))]), $setup.isLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, \"加载中...\")) : _createCommentVNode(\"v-if\", true), $setup.isShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, \"没有更多了\")) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */), [[_vShow, !$setup.file.id]]), $setup.file.id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_createVNode($setup[\"PreviewPdf\"], {\n    id: $setup.file.id,\n    type: $setup.file.extName,\n    name: $setup.file.newFileName\n  }, null, 8 /* PROPS */, [\"id\", \"type\", \"name\"])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createVNode($setup[\"GlobalAiChatScroll\"], {\n    ref: \"chatScrollRef\",\n    AiChatCode: \"content_extraction\",\n    chatId: $setup.chatId,\n    fileData: $setup.fileData,\n    onHandlePromptWord: $setup.handlePromptWord,\n    onHandleGuideWord: $setup.handleGuideWord,\n    onHandleRetryMessage: $setup.handleRetryMessage,\n    onHandleStreamingCallback: $setup.handleStreamingCallback,\n    onHandleSendMessageCallback: $setup.handleSendMessageCallback\n  }, null, 8 /* PROPS */, [\"chatId\", \"fileData\"]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_withDirectives(_createVNode($setup[\"GlobalAiChatFile\"], {\n    fileList: $setup.fileList,\n    fileData: $setup.fileData,\n    onClose: $setup.handleClose\n  }, null, 8 /* PROPS */, [\"fileList\", \"fileData\"]), [[_vShow, $setup.fileList.length || $setup.fileData.length]]), _createVNode($setup[\"GlobalAiChatEditor\"], {\n    ref: \"editorRef\",\n    modelValue: $setup.sendContent,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.sendContent = $event;\n    }),\n    disabled: $setup.disabled,\n    onSend: $setup.handleSendMessage,\n    onStop: $setup.handleStopMessage,\n    onUploadCallback: $setup.handleFileUpload,\n    onFileCallback: $setup.handleFileCallback\n  }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])])])])])])], 8 /* PROPS */, _hoisted_1)), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "$setup", "svg", "loadingText", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_button", "type", "onClick", "handleReset", "default", "_withCtx", "_cache", "_createTextVNode", "_", "file", "id", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_component_el_upload", "drag", "action", "handleFile", "fileUpload", "multiple", "_component_el_icon", "_component_upload_filled", "_hoisted_11", "_toDisplayString", "join", "_withModifiers", "_hoisted_12", "_hoisted_13", "_normalizeClass", "fileIcon", "progressType", "_hoisted_14", "_hoisted_15", "progressText", "_createCommentVNode", "_component_el_progress", "percentage", "fileProgress", "isShowProgress", "_component_el_scrollbar", "ref", "onScroll", "handleScroll", "_hoisted_16", "_Fragment", "_renderList", "tableData", "item", "$event", "handleHistory", "fileName", "_hoisted_18", "_hoisted_19", "_hoisted_20", "fileSize", "size2Str", "_hoisted_21", "format", "createDate", "_hoisted_17", "isLoading", "_hoisted_22", "isShow", "_hoisted_23", "_hoisted_24", "extName", "name", "newFileName", "_hoisted_25", "_hoisted_26", "AiChatCode", "chatId", "fileData", "onHandlePromptWord", "handlePromptWord", "onHandleGuideWord", "handleGuideWord", "onHandleRetryMessage", "handleRetryMessage", "onHandleStreamingCallback", "handleStreamingCallback", "onHandleSendMessageCallback", "handleSendMessageCallback", "_hoisted_27", "_hoisted_28", "fileList", "onClose", "handleClose", "length", "modelValue", "send<PERSON><PERSON><PERSON>", "disabled", "onSend", "handleSendMessage", "onStop", "handleStopMessage", "onUploadCallback", "handleFileUpload", "onFileCallback", "handleFileCallback", "_hoisted_1", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBoxFunction\\ContentExtraction\\ContentExtraction.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"ContentExtraction\"\r\n    v-loading=\"loading\"\r\n    :element-loading-spinner=\"svg\"\r\n    :lement-loading-text=\"loadingText\"\r\n    element-loading-svg-view-box=\"-10, -10, 50, 50\">\r\n    <div class=\"ContentExtractionHead\">\r\n      <div class=\"ContentExtractionButton\">\r\n        <div class=\"ContentExtractionButtonItem\" v-show=\"file.id\">\r\n          <el-button type=\"primary\" @click=\"handleReset\">重新上传</el-button>\r\n        </div>\r\n        <div class=\"ContentExtractionButtonItem\"></div>\r\n      </div>\r\n      <div class=\"ContentExtractionButton\">\r\n        <div class=\"ContentExtractionButtonItem\"></div>\r\n        <div class=\"ContentExtractionButtonItem\">\r\n          <el-button type=\"primary\">导出</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"ContentExtractionBody\">\r\n      <div class=\"ContentExtractionBodyLeft\">\r\n        <div class=\"ContentExtractionUploadBody\" v-show=\"!file.id\">\r\n          <div class=\"ContentExtractionUpload\">\r\n            <el-upload\r\n              drag\r\n              action=\"/\"\r\n              :before-upload=\"handleFile\"\r\n              :http-request=\"fileUpload\"\r\n              :show-file-list=\"false\"\r\n              multiple>\r\n              <el-icon class=\"zy-el-icon--upload\">\r\n                <upload-filled />\r\n              </el-icon>\r\n              <div class=\"zy-el-upload__text\">\r\n                将附件拖拽至此区域，或\r\n                <em>点击上传</em>\r\n              </div>\r\n              <div class=\"zy-el-upload__tip\">仅支持{{ ['doc', 'docx', 'pdf'].join('、') }}格式</div>\r\n            </el-upload>\r\n            <div class=\"ContentExtractionUploadProgressBody\" @click.stop v-show=\"isShowProgress\">\r\n              <div class=\"ContentExtractionUploadProgressInfo\">\r\n                <div class=\"ContentExtractionUploadProgress\">\r\n                  <div class=\"globalFileIcon\" :class=\"fileIcon(progressType)\"></div>\r\n                  <div class=\"ContentExtractionUploadProgressBox\">\r\n                    <div class=\"ContentExtractionUploadProgressName ellipsis\">{{ progressText }}</div>\r\n                    <div class=\"ContentExtractionUploadProgressText\">正在解析</div>\r\n                  </div>\r\n                  <!-- <div class=\"ContentExtractionUploadProgressClose\">\r\n                    <el-icon><Close /></el-icon>\r\n                  </div> -->\r\n                </div>\r\n                <el-progress :percentage=\"fileProgress\" :show-text=\"false\" :stroke-width=\"12\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"ContentExtractionUploadHistoryTitle\">最近阅读</div>\r\n          <el-scrollbar class=\"ContentExtractionUploadHistoryScroll\" ref=\"scrollRef\" @scroll=\"handleScroll\">\r\n            <div class=\"ContentExtractionUploadHistoryList\">\r\n              <div\r\n                class=\"ContentExtractionUploadHistoryItem\"\r\n                v-for=\"item in tableData\"\r\n                :key=\"item.id\"\r\n                @click=\"handleHistory(item)\">\r\n                <div class=\"globalFileIcon\" :class=\"fileIcon(item.fileName)\"></div>\r\n                <div class=\"ContentExtractionUploadHistoryItemName ellipsis\">{{ item.fileName }}</div>\r\n                <div class=\"ContentExtractionUploadHistoryItemInfo\">\r\n                  <div class=\"ContentExtractionUploadHistoryItemFileSize\">\r\n                    {{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}\r\n                  </div>\r\n                  <div class=\"ContentExtractionUploadHistoryItemTime\">{{ format(item.createDate) }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"ContentExtractionUploadHistoryLoadingText\" v-if=\"isLoading\">加载中...</div>\r\n            <div class=\"ContentExtractionUploadHistoryLoadingText\" v-if=\"isShow\">没有更多了</div>\r\n          </el-scrollbar>\r\n        </div>\r\n        <div class=\"ContentExtractionWord\" v-if=\"file.id\">\r\n          <preview-pdf :id=\"file.id\" :type=\"file.extName\" :name=\"file.newFileName\"></preview-pdf>\r\n        </div>\r\n      </div>\r\n      <div class=\"ContentExtractionBodyRight\">\r\n        <div class=\"ContentExtractionChatBody\">\r\n          <GlobalAiChatScroll\r\n            ref=\"chatScrollRef\"\r\n            AiChatCode=\"content_extraction\"\r\n            :chatId=\"chatId\"\r\n            :fileData=\"fileData\"\r\n            @handlePromptWord=\"handlePromptWord\"\r\n            @handleGuideWord=\"handleGuideWord\"\r\n            @handleRetryMessage=\"handleRetryMessage\"\r\n            @handleStreamingCallback=\"handleStreamingCallback\"\r\n            @handleSendMessageCallback=\"handleSendMessageCallback\"></GlobalAiChatScroll>\r\n          <div class=\"ContentExtractionChatBodyEditor\">\r\n            <div class=\"ContentExtractionChatBodyEditorBody\">\r\n              <GlobalAiChatFile\r\n                :fileList=\"fileList\"\r\n                :fileData=\"fileData\"\r\n                @close=\"handleClose\"\r\n                v-show=\"fileList.length || fileData.length\" />\r\n              <GlobalAiChatEditor\r\n                ref=\"editorRef\"\r\n                v-model=\"sendContent\"\r\n                :disabled=\"disabled\"\r\n                @send=\"handleSendMessage\"\r\n                @stop=\"handleStopMessage\"\r\n                @uploadCallback=\"handleFileUpload\"\r\n                @fileCallback=\"handleFileCallback\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ContentExtraction' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick, defineAsyncComponent } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { size2Str } from 'common/js/utils.js'\r\nimport { guid, svg } from '../../AiToolBox/AiToolBox.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst GlobalAiChatScroll = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatScroll.vue'))\r\nconst GlobalAiChatFile = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatFile.vue'))\r\nconst GlobalAiChatEditor = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatEditor.vue'))\r\nconst PreviewPdf = defineAsyncComponent(() => import('@/components/global-file-preview/components/preview-pdf.vue'))\r\n\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n\r\nconst file = ref({})\r\nconst fileProgress = ref(0)\r\nconst isShowProgress = ref(false)\r\nconst progressText = ref('审查意见的补充报告.docx')\r\nconst progressType = ref('docx')\r\n\r\nconst scrollRef = ref()\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst tableData = ref([])\r\nconst totals = ref(0)\r\nconst isShow = ref(false)\r\nconst isLoading = ref(true)\r\nconst loadingScroll = ref(false)\r\n\r\nconst chatScrollRef = ref()\r\nconst editorRef = ref()\r\nconst chatId = ref('')\r\nconst fileData = ref([])\r\nconst fileList = ref([])\r\nconst sendContent = ref('')\r\nconst disabled = ref(false)\r\nconst sendMessageIndex = ref(0)\r\n\r\nconst fileIcon = (name) => {\r\n  const type = name.substring(name.lastIndexOf('.') + 1) || ''\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    pdf: 'globalFilePDF',\r\n    txt: 'globalFileTXT'\r\n  }\r\n  return IconClass[type] || 'globalFileUnknown'\r\n}\r\n\r\nonMounted(() => {\r\n  chatId.value = guid()\r\n  aigptContentExtractionLogList()\r\n})\r\n\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (!scrollRef.value) return\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {\r\n    load()\r\n  }\r\n}\r\nconst load = () => {\r\n  if (pageNo.value * pageSize.value >= totals.value) return\r\n  loadingScroll.value = true\r\n  pageNo.value += 1\r\n  aigptContentExtractionLogList()\r\n}\r\nconst aigptContentExtractionLogList = async () => {\r\n  const { data, total } = await api.aigptContentExtractionLogList({ pageNo: pageNo.value, pageSize: pageSize.value })\r\n  tableData.value = [...tableData.value, ...data]\r\n  totals.value = total\r\n  isLoading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  loadingScroll.value = false\r\n}\r\n\r\nconst handleHistory = (item) => {\r\n  const type = item.fileName.substring(item.fileName.lastIndexOf('.') + 1) || ''\r\n  const data = {\r\n    extName: type,\r\n    fileSize: item.fileSize,\r\n    id: item.fileId,\r\n    newFileName: item.fileId + '.' + type,\r\n    originalFileName: item.fileName\r\n  }\r\n  chatId.value = item.chatId\r\n  sendMessageIndex.value = 1\r\n  // file.value = data\r\n  // editorRef.value?.handleSetFile([data])\r\n  nextTick(() => {\r\n    chatScrollRef.value?.handleOldChat()\r\n  })\r\n}\r\nconst handleReset = () => {\r\n  chatId.value = guid()\r\n  file.value = {}\r\n  editorRef.value?.handleSetFile([])\r\n  pageNo.value = 1\r\n  pageSize.value = 10\r\n  tableData.value = []\r\n  totals.value = 0\r\n  isShow.value = false\r\n  isLoading.value = true\r\n  loadingScroll.value = false\r\n  sendMessageIndex.value = 0\r\n  aigptContentExtractionLogList()\r\n  nextTick(() => {\r\n    chatScrollRef.value?.handleNewChat()\r\n  })\r\n}\r\n\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!fileData.value.length && !sendMessageIndex.value)\r\n    return ElMessage({ type: 'warning', message: `请先上传相关资料在进行内容提炼!` })\r\n  const fileId = fileData.value.map((v) => v.id).join(',')\r\n  const params = { question: value, attachmentIds: fileId }\r\n  chatScrollRef.value?.handleSendMessage(value, params)\r\n}\r\nconst handlePromptWord = (data) => {\r\n  handleSendMessage(data.promptWord)\r\n}\r\nconst handleGuideWord = (data) => {\r\n  chatScrollRef.value?.handleSendMessage(data.question, data)\r\n}\r\nconst handleRetryMessage = (data) => {\r\n  fileData.value = data.fileData\r\n  handleSendMessage(data.content)\r\n}\r\nconst handleStopMessage = () => {\r\n  chatScrollRef.value?.handleStopMessage()\r\n}\r\nconst handleStreamingCallback = (data) => {\r\n  disabled.value = data\r\n}\r\nconst handleSendMessageCallback = () => {\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n}\r\n\r\n/**\r\n * 限制上传附件的文件类型\r\n */\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  const isShow = ['doc', 'docx', 'pdf'].includes(fileType)\r\n  if (!isShow) ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!` })\r\n  isShowProgress.value = true\r\n  fileProgress.value = 0\r\n  progressText.value = file.name\r\n  progressType.value = fileType\r\n  return isShow\r\n}\r\n\r\nconst onUploadProgress = (progressEvent) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    fileProgress.value = parseInt(progress)\r\n  }\r\n}\r\n/**\r\n * 上传附件请求方法\r\n */\r\nconst fileUpload = (file) => {\r\n  const param = new FormData()\r\n  param.append('file', file.file)\r\n  handleGlobalUpload(param)\r\n}\r\n\r\nconst handleGlobalUpload = async (params) => {\r\n  try {\r\n    const { data } = await api.globalUpload(params, onUploadProgress, guid())\r\n    file.value = data\r\n    editorRef.value?.handleSetFile([data])\r\n    loading.value = false\r\n    isShowProgress.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n    isShowProgress.value = false\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ContentExtraction {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .ContentExtractionHead {\r\n    width: 100%;\r\n    padding: var(--zy-distance-four) 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .ContentExtractionButton {\r\n      width: 796px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      & + .ContentExtractionButton {\r\n        width: calc(100% - 828px);\r\n      }\r\n      .ContentExtractionButtonItem {\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n  .ContentExtractionBody {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2)));\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: var(--zy-distance-four);\r\n    .ContentExtractionBodyLeft {\r\n      width: 812px;\r\n      height: 100%;\r\n      .ContentExtractionUploadBody {\r\n        width: 800px;\r\n        height: 100%;\r\n        background: #fff;\r\n        .globalFileIcon {\r\n          width: 32px;\r\n          height: 32px;\r\n          vertical-align: middle;\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 0;\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        .globalFileUnknown {\r\n          background: url('../../img/file_type/unknown.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePDF {\r\n          background: url('../../img/file_type/PDF.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWord {\r\n          background: url('../../img/file_type/Word.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileTXT {\r\n          background: url('../../img/file_type/TXT.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWPS {\r\n          background: url('../../img/file_type/WPS.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .ContentExtractionUpload {\r\n          width: 100%;\r\n          padding: var(--zy-distance-two);\r\n          position: relative;\r\n\r\n          .zy-el-upload {\r\n            --zy-el-upload-dragger-padding-horizontal: 20px;\r\n            --zy-el-upload-dragger-padding-vertical: 10px;\r\n            .zy-el-upload-dragger {\r\n              height: 220px;\r\n              display: flex;\r\n              align-items: center;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n            }\r\n            .zy-el-icon {\r\n              font-size: 99px;\r\n            }\r\n            .zy-el-upload__text {\r\n              line-height: var(--zy-line-height);\r\n            }\r\n\r\n            .zy-el-upload__tip {\r\n              padding: 0 var(--zy-distance-one);\r\n              line-height: var(--zy-line-height);\r\n            }\r\n          }\r\n\r\n          .ContentExtractionUploadProgressBody {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: var(--zy-distance-two) 0;\r\n            position: absolute;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            background: rgba(255, 255, 255, 0.8);\r\n\r\n            .ContentExtractionUploadProgressInfo {\r\n              width: 460px;\r\n              .ContentExtractionUploadProgress {\r\n                width: 100%;\r\n                padding: var(--zy-distance-five) 40px;\r\n                position: relative;\r\n                .ContentExtractionUploadProgressBox {\r\n                  width: 100%;\r\n                  .ContentExtractionUploadProgressName {\r\n                    font-size: var(--zy-name-font-size);\r\n                    line-height: var(--zy-line-height);\r\n                  }\r\n                  .ContentExtractionUploadProgressText {\r\n                    color: var(--zy-el-color-primary);\r\n                    font-size: var(--zy-text-font-size);\r\n                    line-height: var(--zy-line-height);\r\n                  }\r\n                }\r\n                .ContentExtractionUploadProgressClose {\r\n                  width: 40px;\r\n                  height: 40px;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  position: absolute;\r\n                  right: 0;\r\n                  bottom: calc(var(--zy-distance-five) / 2);\r\n                  cursor: pointer;\r\n                  .zy-el-icon {\r\n                    font-size: 26px;\r\n                    &:hover {\r\n                      color: var(--zy-el-color-danger);\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .ContentExtractionUploadHistoryTitle {\r\n          width: 100%;\r\n          height: var(--zy-height);\r\n          line-height: var(--zy-height);\r\n          font-size: var(--zy-name-font-size);\r\n          color: var(--zy-el-text-color-secondary);\r\n          padding: 0 var(--zy-distance-two);\r\n        }\r\n        .ContentExtractionUploadHistoryScroll {\r\n          width: 100%;\r\n          height: calc(100% - (260px + var(--zy-height)));\r\n        }\r\n        .ContentExtractionUploadHistoryList {\r\n          width: 100%;\r\n          padding: 0 var(--zy-distance-two);\r\n          .ContentExtractionUploadHistoryItem {\r\n            width: 100%;\r\n            position: relative;\r\n            padding-left: 40px;\r\n            padding-top: var(--zy-distance-five);\r\n            padding-bottom: var(--zy-distance-five);\r\n            border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n            cursor: pointer;\r\n\r\n            .ContentExtractionUploadHistoryItemName {\r\n              width: 100%;\r\n              font-size: var(--zy-name-font-size);\r\n              line-height: var(--zy-line-height);\r\n              padding-bottom: var(--zy-font-text-distance-five);\r\n            }\r\n            .ContentExtractionUploadHistoryItemInfo {\r\n              width: 100%;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: space-between;\r\n              .ContentExtractionUploadHistoryItemFileSize {\r\n                font-size: var(--zy-text-font-size);\r\n                line-height: var(--zy-line-height);\r\n                color: var(--zy-el-text-color-secondary);\r\n              }\r\n              .ContentExtractionUploadHistoryItemTime {\r\n                font-size: var(--zy-text-font-size);\r\n                line-height: var(--zy-line-height);\r\n                color: var(--zy-el-text-color-secondary);\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .ContentExtractionUploadHistoryLoadingText {\r\n          text-align: center;\r\n          color: var(--zy-el-text-color-regular);\r\n          padding: var(--zy-distance-three) 0;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n      }\r\n      .ContentExtractionWord {\r\n        width: 100%;\r\n        height: 100%;\r\n        .vue-office-pdf-wrapper {\r\n          padding: 0 !important;\r\n          canvas {\r\n            left: 0 !important;\r\n            transform: none !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .ContentExtractionBodyRight {\r\n      width: calc(100% - 820px);\r\n      height: 100%;\r\n      .ContentExtractionChatBody {\r\n        width: 100%;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        background: #fff;\r\n        .GlobalAiChatBody {\r\n          padding-top: 12px;\r\n        }\r\n        .ContentExtractionChatBodyEditor {\r\n          width: 100%;\r\n          padding: 12px;\r\n          .GlobalAiChatFileItemClose {\r\n            display: none !important;\r\n          }\r\n          .ContentExtractionChatBodyEditorBody {\r\n            width: 100%;\r\n            background: #fff;\r\n            border-radius: 8px;\r\n            box-shadow: var(--zy-el-box-shadow);\r\n            border: 1px solid var(--zy-el-border-color-lighter);\r\n            .GlobalAiChatEditorUpload {\r\n              & > div {\r\n                display: none;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";iBAAA;;EAOSA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAA6B;;EAKrCA,KAAK,EAAC;AAAyB;;EAE7BA,KAAK,EAAC;AAA6B;;EAKvCA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAyB;;EAe3BA,KAAK,EAAC;AAAmB;;EAGzBA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAiC;;EAErCA,KAAK,EAAC;AAAoC;;EACxCA,KAAK,EAAC;AAA8C;;EAa5DA,KAAK,EAAC;AAAoC;kBA3D3D;;EAkEqBA,KAAK,EAAC;AAAiD;;EACvDA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAA4C;;EAGlDA,KAAK,EAAC;AAAwC;;EAvErEC,GAAA;EA2EiBD,KAAK,EAAC;;;EA3EvBC,GAAA;EA4EiBD,KAAK,EAAC;;;EA5EvBC,GAAA;EA+EaD,KAAK,EAAC;;;EAIRA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAA2B;;EAW/BA,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC;AAAqC;;;;;;;;;wCA/F1DE,mBAAA,CAkHM;IAjHJF,KAAK,EAAC,mBAAmB;IAExB,yBAAuB,EAAEG,MAAA,CAAAC,GAAG;IAC5B,qBAAmB,EAAED,MAAA,CAAAE,WAAW;IACjC,8BAA4B,EAAC;MAC7BC,mBAAA,CAaM,OAbNC,UAaM,GAZJD,mBAAA,CAKM,OALNE,UAKM,G,gBAJJF,mBAAA,CAEM,OAFNG,UAEM,GADJC,YAAA,CAA+DC,oBAAA;IAApDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEV,MAAA,CAAAW;;IAV5CC,OAAA,EAAAC,QAAA,CAUyD;MAAA,OAAIC,MAAA,QAAAA,MAAA,OAV7DC,gBAAA,CAUyD,MAAI,E;;IAV7DC,CAAA;wCASyDhB,MAAA,CAAAiB,IAAI,CAACC,EAAE,E,6BAGxDf,mBAAA,CAA+C;IAA1CN,KAAK,EAAC;EAA6B,4B,GAE1CM,mBAAA,CAKM,OALNgB,UAKM,G,0BAJJhB,mBAAA,CAA+C;IAA1CN,KAAK,EAAC;EAA6B,6BACxCM,mBAAA,CAEM,OAFNiB,UAEM,GADJb,YAAA,CAAwCC,oBAAA;IAA7BC,IAAI,EAAC;EAAS;IAjBnCG,OAAA,EAAAC,QAAA,CAiBoC;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAjBtCC,gBAAA,CAiBoC,IAAE,E;;IAjBtCC,CAAA;YAqBIb,mBAAA,CA6FM,OA7FNkB,UA6FM,GA5FJlB,mBAAA,CA4DM,OA5DNmB,UA4DM,G,gBA3DJnB,mBAAA,CAuDM,OAvDNoB,UAuDM,GAtDJpB,mBAAA,CAgCM,OAhCNqB,WAgCM,GA/BJjB,YAAA,CAeYkB,oBAAA;IAdVC,IAAI,EAAJ,EAAI;IACJC,MAAM,EAAC,GAAG;IACT,eAAa,EAAE3B,MAAA,CAAA4B,UAAU;IACzB,cAAY,EAAE5B,MAAA,CAAA6B,UAAU;IACxB,gBAAc,EAAE,KAAK;IACtBC,QAAQ,EAAR;;IA/BdlB,OAAA,EAAAC,QAAA,CAgCc;MAAA,OAEU,CAFVN,YAAA,CAEUwB,kBAAA;QAFDlC,KAAK,EAAC;MAAoB;QAhCjDe,OAAA,EAAAC,QAAA,CAiCgB;UAAA,OAAiB,CAAjBN,YAAA,CAAiByB,wBAAA,E;;QAjCjChB,CAAA;oCAmCcb,mBAAA,CAGM;QAHDN,KAAK,EAAC;MAAoB,IAnC7CkB,gBAAA,CAmC8C,eAE9B,GAAAZ,mBAAA,CAAa,YAAT,MAAI,E,sBAEVA,mBAAA,CAAgF,OAAhF8B,WAAgF,EAAjD,KAAG,GAAAC,gBAAA,wBAA0BC,IAAI,SAAQ,IAAE,gB;;IAvCxFnB,CAAA;sBAyCYb,mBAAA,CAcM;IAdDN,KAAK,EAAC,qCAAqC;IAAEa,OAAK,EAAAI,MAAA,QAAAA,MAAA,MAzCnEsB,cAAA,CAyC6D,cAAW;MAC1DjC,mBAAA,CAYM,OAZNkC,WAYM,GAXJlC,mBAAA,CASM,OATNmC,WASM,GARJnC,mBAAA,CAAkE;IAA7DN,KAAK,EA5C5B0C,eAAA,EA4C6B,gBAAgB,EAASvC,MAAA,CAAAwC,QAAQ,CAACxC,MAAA,CAAAyC,YAAY;2BACzDtC,mBAAA,CAGM,OAHNuC,WAGM,GAFJvC,mBAAA,CAAkF,OAAlFwC,WAAkF,EAAAT,gBAAA,CAArBlC,MAAA,CAAA4C,YAAY,kB,0BACzEzC,mBAAA,CAA2D;IAAtDN,KAAK,EAAC;EAAqC,GAAC,MAAI,qB,GAEvDgD,mBAAA,0IAEU,C,GAEZtC,YAAA,CAAgFuC,sBAAA;IAAlEC,UAAU,EAAE/C,MAAA,CAAAgD,YAAY;IAAG,WAAS,EAAE,KAAK;IAAG,cAAY,EAAE;+EAZThD,MAAA,CAAAiD,cAAc,E,+BAgBrF9C,mBAAA,CAA2D;IAAtDN,KAAK,EAAC;EAAqC,GAAC,MAAI,sBACrDU,YAAA,CAmBe2C,uBAAA;IAnBDrD,KAAK,EAAC,sCAAsC;IAACsD,GAAG,EAAC,WAAW;IAAEC,QAAM,EAAEpD,MAAA,CAAAqD;;IA1D9FzC,OAAA,EAAAC,QAAA,CA2DY;MAAA,OAeM,CAfNV,mBAAA,CAeM,OAfNmD,WAeM,I,kBAdJvD,mBAAA,CAaMwD,SAAA,QAzEpBC,WAAA,CA8D+BxD,MAAA,CAAAyD,SAAS,EA9DxC,UA8DuBC,IAAI;6BAFb3D,mBAAA,CAaM;UAZJF,KAAK,EAAC,oCAAoC;UAEzCC,GAAG,EAAE4D,IAAI,CAACxC,EAAE;UACZR,OAAK,WAALA,OAAKA,CAAAiD,MAAA;YAAA,OAAE3D,MAAA,CAAA4D,aAAa,CAACF,IAAI;UAAA;YAC1BvD,mBAAA,CAAmE;UAA9DN,KAAK,EAjE1B0C,eAAA,EAiE2B,gBAAgB,EAASvC,MAAA,CAAAwC,QAAQ,CAACkB,IAAI,CAACG,QAAQ;iCAC1D1D,mBAAA,CAAsF,OAAtF2D,WAAsF,EAAA5B,gBAAA,CAAtBwB,IAAI,CAACG,QAAQ,kBAC7E1D,mBAAA,CAKM,OALN4D,WAKM,GAJJ5D,mBAAA,CAEM,OAFN6D,WAEM,EAAA9B,gBAAA,CADDwB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEO,QAAQ,GAAGjE,MAAA,CAAAkE,QAAQ,CAACR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,QAAQ,2BAE7C9D,mBAAA,CAAuF,OAAvFgE,WAAuF,EAAAjC,gBAAA,CAAhClC,MAAA,CAAAoE,MAAM,CAACV,IAAI,CAACW,UAAU,kB,mBAvE/FC,WAAA;wCA2EyEtE,MAAA,CAAAuE,SAAS,I,cAAtExE,mBAAA,CAAoF,OAApFyE,WAAoF,EAAZ,QAAM,KA3E1F3B,mBAAA,gBA4EyE7C,MAAA,CAAAyE,MAAM,I,cAAnE1E,mBAAA,CAAgF,OAAhF2E,WAAgF,EAAX,OAAK,KA5EtF7B,mBAAA,e;;IAAA7B,CAAA;+DAuB0DhB,MAAA,CAAAiB,IAAI,CAACC,EAAE,E,GAwDhBlB,MAAA,CAAAiB,IAAI,CAACC,EAAE,I,cAAhDnB,mBAAA,CAEM,OAFN4E,WAEM,GADJpE,YAAA,CAAuFP,MAAA;IAAzEkB,EAAE,EAAElB,MAAA,CAAAiB,IAAI,CAACC,EAAE;IAAGT,IAAI,EAAET,MAAA,CAAAiB,IAAI,CAAC2D,OAAO;IAAGC,IAAI,EAAE7E,MAAA,CAAAiB,IAAI,CAAC6D;uDAhFtEjC,mBAAA,e,GAmFM1C,mBAAA,CA8BM,OA9BN4E,WA8BM,GA7BJ5E,mBAAA,CA4BM,OA5BN6E,WA4BM,GA3BJzE,YAAA,CAS8EP,MAAA;IAR5EmD,GAAG,EAAC,eAAe;IACnB8B,UAAU,EAAC,oBAAoB;IAC9BC,MAAM,EAAElF,MAAA,CAAAkF,MAAM;IACdC,QAAQ,EAAEnF,MAAA,CAAAmF,QAAQ;IAClBC,kBAAgB,EAAEpF,MAAA,CAAAqF,gBAAgB;IAClCC,iBAAe,EAAEtF,MAAA,CAAAuF,eAAe;IAChCC,oBAAkB,EAAExF,MAAA,CAAAyF,kBAAkB;IACtCC,yBAAuB,EAAE1F,MAAA,CAAA2F,uBAAuB;IAChDC,2BAAyB,EAAE5F,MAAA,CAAA6F;mDAC9B1F,mBAAA,CAgBM,OAhBN2F,WAgBM,GAfJ3F,mBAAA,CAcM,OAdN4F,WAcM,G,gBAbJxF,YAAA,CAIgDP,MAAA;IAH7CgG,QAAQ,EAAEhG,MAAA,CAAAgG,QAAQ;IAClBb,QAAQ,EAAEnF,MAAA,CAAAmF,QAAQ;IAClBc,OAAK,EAAEjG,MAAA,CAAAkG;+DACAlG,MAAA,CAAAgG,QAAQ,CAACG,MAAM,IAAInG,MAAA,CAAAmF,QAAQ,CAACgB,MAAM,E,GAC5C5F,YAAA,CAOuCP,MAAA;IANrCmD,GAAG,EAAC,WAAW;IAvG/BiD,UAAA,EAwGyBpG,MAAA,CAAAqG,WAAW;IAxGpC,uBAAAvF,MAAA,QAAAA,MAAA,gBAAA6C,MAAA;MAAA,OAwGyB3D,MAAA,CAAAqG,WAAW,GAAA1C,MAAA;IAAA;IACnB2C,QAAQ,EAAEtG,MAAA,CAAAsG,QAAQ;IAClBC,MAAI,EAAEvG,MAAA,CAAAwG,iBAAiB;IACvBC,MAAI,EAAEzG,MAAA,CAAA0G,iBAAiB;IACvBC,gBAAc,EAAE3G,MAAA,CAAA4G,gBAAgB;IAChCC,cAAY,EAAE7G,MAAA,CAAA8G;iFA7G/BC,UAAA,K,qBAGe/G,MAAA,CAAAgH,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}