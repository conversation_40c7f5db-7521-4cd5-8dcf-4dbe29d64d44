{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, nextTick, watch } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { useStore } from 'vuex';\nimport { user, whetherUseIntelligentize } from 'common/js/system_var.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport SimilarityQuery from '@/components/SimilarityQuery/SimilarityQuery.vue';\nimport SuggestRecommendUser from '@/components/SuggestRecommendUser/SuggestRecommendUser.vue';\nimport DynamicTitle from '@/components/global-dynamic-title/global-dynamic-title.vue';\nimport SuggestReviewDetail from '@/views/SuggestReview/component/SuggestReviewDetail.vue';\nimport suggestPrint from '@/components/suggestPrint/suggestPrint';\nimport { filterTableData } from '@/assets/js/suggestExportWord';\nimport { exportWordHtmlObj } from 'common/config/MicroGlobal';\nimport ScoreProportion from './ScoreProportion.vue';\nvar __default__ = {\n  name: 'SubmitSuggest'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var store = useStore();\n    var loading = ref(false);\n    var loadingText = ref('');\n    var AreaId = ref(sessionStorage.getItem('AreaId'));\n    var formRef = ref();\n    var cachedScoreData = ref(null);\n    var isRequesting = ref(false);\n    var initialScoreData = ref(null);\n    var form = reactive({\n      suggestSubmitWay: 'cppcc_member',\n      title: '',\n      // 提案标题\n      suggestUserId: '',\n      writerUserId: '',\n      cardNumber: '',\n      sectorType: '',\n      mobile: '',\n      callAddress: '',\n      delegationId: '',\n      isJoinProposal: 0,\n      joinUsers: [],\n      content: '',\n      suggestOpenType: 'open_all',\n      suggestSurveyType: '3',\n      isMakeMineJob: '1',\n      notHandleTimeType: '1',\n      isHopeEnhanceTalk: '1',\n      isNeedPaperAnswer: '1',\n      hopeHandleOfficeIds: []\n    });\n    var rules = reactive({\n      suggestSubmitWay: [{\n        required: true,\n        message: '请选择提案提交类型',\n        trigger: ['blur', 'change']\n      }],\n      title: [{\n        required: true,\n        message: '请输入提案标题',\n        trigger: ['blur', 'change']\n      }],\n      content: [{\n        required: true,\n        message: '请输入提案内容',\n        trigger: ['blur', 'change']\n      }],\n      suggestUserId: [{\n        required: true,\n        message: '请选择提案者',\n        trigger: ['blur', 'change']\n      }],\n      delegationId: [{\n        required: false,\n        message: '请选择集体提案单位',\n        trigger: ['blur', 'change']\n      }],\n      isJoinProposal: [{\n        required: true,\n        message: '请选择是否联名提案',\n        trigger: ['blur', 'change']\n      }],\n      joinUsers: [{\n        type: 'array',\n        required: false,\n        message: '请选择提案联名人',\n        trigger: ['blur', 'change']\n      }]\n    });\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var tinyMceSetting = {\n      tp_layout_options: {\n        style: {\n          'text-align': 'justify',\n          'text-indent': '2em',\n          'line-height': '28pt',\n          'font-size': '16pt',\n          'font-family': '仿宋_GB2312'\n        },\n        tagsStyle: {\n          span: {\n            'text-align': 'justify',\n            'text-indent': '2em',\n            'line-height': '28pt',\n            'font-size': '16pt',\n            'font-family': '仿宋_GB2312'\n          }\n        }\n      },\n      contextmenu: false,\n      paste_postprocess: function paste_postprocess(plugin, args) {\n        nextTick(function () {\n          args.target.execCommand('mceTpLayout');\n          nextTick(function () {\n            args.target.selection.collapse();\n          });\n        });\n      },\n      import_word_callback: function import_word_callback(editor) {\n        nextTick(function () {\n          editor.execCommand('mceTpLayout');\n          nextTick(function () {\n            editor.selection.collapse();\n          });\n        });\n      }\n    };\n    var suggestTitleNumber = ref(30);\n    var suggestContentNumber = ref(2000);\n    var suggestMinSimilar = ref(0);\n    var termYearId = ref('');\n    var contentCount = ref(0);\n    var fileData = ref([]);\n    var delegationData = ref([]);\n    var suggestOpenTypeName = ref('');\n    var suggestSurveyTypeName = ref('');\n    var notHandleTimeTypeName = ref('');\n    var isHopeEnhanceTalkName = ref('');\n    var isMakeMineJobName = ref('');\n    var isNeedPaperAnswerName = ref('');\n    var suggestOpenType = ref([]);\n    var suggestSurveyType = ref([]);\n    var notHandleTimeType = ref([]);\n    var isHopeEnhanceTalk = ref([]);\n    var isMakeMineJob = ref([]);\n    var isNeedPaperAnswer = ref([]);\n    var contactPersonList = ref([{\n      id: guid(),\n      contactName: '',\n      contactPhone: '',\n      contactAddress: ''\n    }]);\n    var typeShow = ref(false);\n    var disabled = ref(false);\n    var isDisabled = ref(false);\n    var tabCode = ref(['cppccMember']);\n    var reviewShow = ref(false);\n    var queryType = ref('');\n    var show = ref(false);\n    var isShow = ref(false);\n    var elIsShow = ref(false);\n    var visibleIsShow = ref(false);\n    var userParams = ref({});\n    var ProposalYear = ref({});\n    var printParams = ref({});\n    var elPrintWhetherShow = ref(false);\n    var unitedProportionShow = ref(false);\n    var scoreProportionData = ref([]);\n    var totalExceeds = ref(false);\n    var totalInsufficient = ref(false);\n    var isFlag = ref(false);\n    var hasLoadedScoreData = ref(false);\n    var timers = null;\n    var debounce = function debounce(fn, delay) {\n      var timer = null;\n      return function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (timer) clearTimeout(timer);\n        timer = setTimeout(function () {\n          fn.apply(void 0, args);\n        }, delay);\n      };\n    };\n    var handleInput = debounce(function (row) {\n      if (row.scoreProportion !== undefined && !/^[0-9]*$/.test(row.scoreProportion)) {\n        row.scoreProportion = row.scoreProportion.replace(/[^0-9]/g, '');\n      }\n    }, 300);\n    var handleBlur = debounce(function (row) {\n      var filledTotal = 0;\n      var unfilledCount = 0;\n      scoreProportionData.value.forEach(function (item) {\n        if (item.scoreProportion && !isNaN(item.scoreProportion)) {\n          filledTotal += parseInt(item.scoreProportion, 10);\n        } else {\n          unfilledCount++;\n        }\n      });\n      if (filledTotal > 100) {\n        totalExceeds.value = true;\n        totalInsufficient.value = false;\n        row.scoreProportion = '';\n        return;\n      }\n      if (unfilledCount > 0) {\n        var remaining = 100 - filledTotal;\n        if (remaining < 0) {\n          totalExceeds.value = true;\n          totalInsufficient.value = false;\n          row.scoreProportion = '';\n          return;\n        }\n        if (unfilledCount === 1) {\n          scoreProportionData.value.forEach(function (item) {\n            if (!item.scoreProportion || isNaN(item.scoreProportion)) {\n              item.scoreProportion = remaining.toString();\n            }\n          });\n        }\n      }\n      if (filledTotal < 100 && unfilledCount === 0) {\n        totalInsufficient.value = true;\n        totalExceeds.value = false;\n      } else {\n        totalInsufficient.value = false;\n      }\n      if (filledTotal === 100 && unfilledCount === 0) {\n        totalExceeds.value = false;\n        totalInsufficient.value = false;\n      }\n    }, 300);\n\n    // const debouncedUpdateScoreProportion = debounce(() => {\n    //   updateScoreProportionList()\n    // }, 300)\n\n    onActivated(function () {\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'ai-intelligent-write-chat'\n      });\n      var openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || '';\n      if (openAiParams) {\n        qiankunMicro.setGlobalState({\n          AiChatConfig: {\n            AiChatWindow: true,\n            AiChatFile: openAiParams.fileData,\n            AiChatParams: {\n              tool: openAiParams.toolId,\n              param: {\n                isPage: '1'\n              }\n            },\n            AiChatSendMessage: openAiParams.toolContent\n          }\n        });\n        sessionStorage.setItem('openAiParams', JSON.stringify(''));\n        timers = setTimeout(function () {\n          qiankunMicro.setGlobalState({\n            AiChatConfig: {\n              AiChatWindow: true,\n              AiChatFile: openAiParams.fileData,\n              AiChatParams: {}\n            }\n          });\n        }, 2000);\n      }\n      queryType.value = route.query.type;\n      if (route.query.clueListId) {\n        proposalClueInfo();\n      }\n      globalReadConfig();\n      termYearCurrent();\n      dictionaryData();\n      dictionaryNameData();\n      getProposalYear();\n      if (queryType.value === 'draft' || route.query.anewId) {\n        typeShow.value = true;\n        disabled.value = true;\n      }\n      if (route.query.id || route.query.anewId) {\n        typeShow.value = true;\n        suggestionInfo();\n      } else {\n        tabCode.value = ['cppccMember'];\n        if (user.value.specialRoleKeys.includes('team_office_user')) {\n          typeShow.value = true;\n        }\n        if (user.value.specialRoleKeys.includes('cppcc_member')) {\n          typeShow.value = true;\n          disabled.value = true;\n          form.suggestUserId = user.value.id;\n          cppccMemberInfo(user.value.id);\n        } else {\n          if (user.value.specialRoleKeys.includes('team_office_user')) {\n            form.suggestSubmitWay = 'team';\n          }\n        }\n        if (user.value.specialRoleKeys.includes('team_office_user') && user.value.specialRoleKeys.includes('cppcc_member')) {\n          typeShow.value = false;\n        }\n        if (user.value.specialRoleKeys.includes('admin')) {\n          form.suggestSubmitWay = 'cppcc_member';\n          typeShow.value = false;\n          disabled.value = false;\n          teamOfficeSelect({});\n        } else {\n          if (user.value.specialRoleKeys.includes('team_office_user')) {\n            teamOfficeSelect({\n              isSelectMine: 1\n            });\n          } else {\n            teamOfficeSelect({});\n          }\n        }\n        submitTypeChange();\n      }\n      if (!route.query.id) {\n        cachedScoreData.value = null;\n        initialScoreData.value = null;\n        hasLoadedScoreData.value = false;\n      }\n    });\n    onDeactivated(function () {\n      if (timers) {\n        clearTimeout(timers);\n        timers = null;\n      }\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'test_chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatConfig: {\n          AiChatWindow: false,\n          AiChatFile: [],\n          AiChatParams: {}\n        }\n      });\n      // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\n      // elAiChatClass.AiChatHistory()\n    });\n    onBeforeUnmount(function () {\n      if (timers) {\n        clearTimeout(timers);\n        timers = null;\n      }\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'test_chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatConfig: {\n          AiChatWindow: false,\n          AiChatFile: [],\n          AiChatParams: {}\n        }\n      });\n      // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\n      // elAiChatClass.AiChatHistory()\n    });\n    var updateScoreProportionList = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var proposerId, res, newList, existingProposer, _yield$api$cppccMembe, data, existingJoinUsers, newJoinUserIds, newJoinUsersInfo, updatedList;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              if (!isRequesting.value) {\n                _context2.next = 2;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 2:\n              proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId;\n              if (proposerId) {\n                _context2.next = 6;\n                break;\n              }\n              isFlag.value = false;\n              return _context2.abrupt(\"return\");\n            case 6:\n              if (!(route.query.id && !hasLoadedScoreData.value)) {\n                _context2.next = 27;\n                break;\n              }\n              _context2.prev = 7;\n              isRequesting.value = true;\n              _context2.next = 11;\n              return api.globalJson('/proposalAllocationScore/info', {\n                detailId: route.query.id\n              });\n            case 11:\n              res = _context2.sent;\n              if (!(res.data && res.data.length > 0)) {\n                _context2.next = 19;\n                break;\n              }\n              initialScoreData.value = res.data;\n              cachedScoreData.value = res.data;\n              scoreProportionData.value = res.data;\n              isFlag.value = true;\n              hasLoadedScoreData.value = true;\n              return _context2.abrupt(\"return\");\n            case 19:\n              _context2.next = 24;\n              break;\n            case 21:\n              _context2.prev = 21;\n              _context2.t0 = _context2[\"catch\"](7);\n              if (_context2.t0.code !== 'ERR_CANCELED') {\n                console.error('获取得分占比数据失败:', _context2.t0);\n              }\n            case 24:\n              _context2.prev = 24;\n              isRequesting.value = false;\n              return _context2.finish(24);\n            case 27:\n              if (!(scoreProportionData.value.length > 0 && hasLoadedScoreData.value)) {\n                _context2.next = 29;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 29:\n              newList = [];\n              existingProposer = scoreProportionData.value.find(function (person) {\n                return person.id === proposerId;\n              });\n              if (!existingProposer) {\n                _context2.next = 35;\n                break;\n              }\n              newList.push(_objectSpread(_objectSpread({}, existingProposer), {}, {\n                scoreProportion: existingProposer.scoreProportion || ''\n              }));\n              _context2.next = 50;\n              break;\n            case 35:\n              _context2.prev = 35;\n              isRequesting.value = true;\n              _context2.next = 39;\n              return api.cppccMemberInfo({\n                detailId: proposerId\n              });\n            case 39:\n              _yield$api$cppccMembe = _context2.sent;\n              data = _yield$api$cppccMembe.data;\n              newList.push({\n                id: proposerId,\n                userId: proposerId,\n                userName: data.userName,\n                scoreProportion: ''\n              });\n              _context2.next = 47;\n              break;\n            case 44:\n              _context2.prev = 44;\n              _context2.t1 = _context2[\"catch\"](35);\n              console.error('获取提案者信息失败:', _context2.t1);\n            case 47:\n              _context2.prev = 47;\n              isRequesting.value = false;\n              return _context2.finish(47);\n            case 50:\n              if (!(form.joinUsers && form.joinUsers.length > 0)) {\n                _context2.next = 71;\n                break;\n              }\n              existingJoinUsers = scoreProportionData.value.filter(function (person) {\n                return form.joinUsers.includes(person.id);\n              });\n              newJoinUserIds = form.joinUsers.filter(function (id) {\n                return !existingJoinUsers.some(function (user) {\n                  return user.id === id;\n                });\n              });\n              if (!(newJoinUserIds.length > 0)) {\n                _context2.next = 70;\n                break;\n              }\n              _context2.prev = 54;\n              isRequesting.value = true;\n              _context2.next = 58;\n              return Promise.all(newJoinUserIds.map(/*#__PURE__*/function () {\n                var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(userId) {\n                  var _yield$api$cppccMembe2, data;\n                  return _regeneratorRuntime().wrap(function _callee$(_context) {\n                    while (1) switch (_context.prev = _context.next) {\n                      case 0:\n                        _context.next = 2;\n                        return api.cppccMemberInfo({\n                          detailId: userId\n                        });\n                      case 2:\n                        _yield$api$cppccMembe2 = _context.sent;\n                        data = _yield$api$cppccMembe2.data;\n                        return _context.abrupt(\"return\", {\n                          id: userId,\n                          userId: userId,\n                          userName: data.userName,\n                          scoreProportion: ''\n                        });\n                      case 5:\n                      case \"end\":\n                        return _context.stop();\n                    }\n                  }, _callee);\n                }));\n                return function (_x) {\n                  return _ref3.apply(this, arguments);\n                };\n              }()));\n            case 58:\n              newJoinUsersInfo = _context2.sent;\n              newList = [].concat(_toConsumableArray(newList), _toConsumableArray(existingJoinUsers), _toConsumableArray(newJoinUsersInfo));\n              _context2.next = 65;\n              break;\n            case 62:\n              _context2.prev = 62;\n              _context2.t2 = _context2[\"catch\"](54);\n              console.error('获取联名人信息失败:', _context2.t2);\n            case 65:\n              _context2.prev = 65;\n              isRequesting.value = false;\n              return _context2.finish(65);\n            case 68:\n              _context2.next = 71;\n              break;\n            case 70:\n              newList = [].concat(_toConsumableArray(newList), _toConsumableArray(existingJoinUsers));\n            case 71:\n              updatedList = newList.map(function (newItem) {\n                var _initialScoreData$val;\n                var initialItem = (_initialScoreData$val = initialScoreData.value) === null || _initialScoreData$val === void 0 ? void 0 : _initialScoreData$val.find(function (item) {\n                  return item.id === newItem.id;\n                });\n                var existingItem = scoreProportionData.value.find(function (item) {\n                  return item.id === newItem.id;\n                });\n                return _objectSpread(_objectSpread({}, newItem), {}, {\n                  scoreProportion: (initialItem === null || initialItem === void 0 ? void 0 : initialItem.scoreProportion) || (existingItem === null || existingItem === void 0 ? void 0 : existingItem.scoreProportion) || newItem.scoreProportion\n                });\n              });\n              scoreProportionData.value = updatedList;\n              isFlag.value = true;\n              hasLoadedScoreData.value = true;\n            case 75:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[7, 21, 24, 27], [35, 44, 47, 50], [54, 62, 65, 68]]);\n      }));\n      return function updateScoreProportionList() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleExportWord = function handleExportWord() {\n      if (!form.content) return ElMessage({\n        type: 'warning',\n        message: '请等待提案详情加载完成再进行导出！'\n      });\n      suggestionWord({\n        ids: [route.query.id]\n      });\n    };\n    var handleSuggestPrint = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              if (form.content) {\n                _context3.next = 2;\n                break;\n              }\n              return _context3.abrupt(\"return\", ElMessage({\n                type: 'warning',\n                message: '请等待提案详情加载完成再进行打印！'\n              }));\n            case 2:\n              printParams.value = {\n                ids: [route.query.id]\n              };\n              elPrintWhetherShow.value = true;\n            case 4:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function handleSuggestPrint() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var callback = function callback(type) {\n      elPrintWhetherShow.value = false;\n      if (type) {\n        qiankunMicro.setGlobalState({\n          closeOpenRoute: {\n            openId: route.query.oldRouteId,\n            closeId: route.query.routeId\n          }\n        });\n      }\n    };\n    var suggestionWord = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(params) {\n        var _yield$api$suggestion, data, wordData, index;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.suggestionWord(params);\n            case 2:\n              _yield$api$suggestion = _context4.sent;\n              data = _yield$api$suggestion.data;\n              if (data.length) {\n                wordData = {};\n                for (index = 0; index < data.length; index++) {\n                  wordData = filterTableData(data[index]);\n                }\n                exportWordHtmlObj({\n                  code: 'proposalDetails',\n                  name: wordData.docName,\n                  key: 'content',\n                  data: wordData\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function suggestionWord(_x2) {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handleSimilarity = function handleSimilarity(isType) {\n      if (!form.content) return ElMessage({\n        type: 'warning',\n        message: '请输入提案内容进行相似度查询！'\n      });\n      sessionStorage.setItem('TextQueryToolTitle', form.title);\n      sessionStorage.setItem('TextQueryToolContent', form.content);\n      isShow.value = isType;\n      show.value = true;\n    };\n    var handleSimilarityCallback = function handleSimilarityCallback(type) {\n      if (type) globalJson(0);\n      show.value = false;\n    };\n    var handleContentBlur = function handleContentBlur() {\n      userParams.value = {\n        authorId: form.suggestUserId,\n        content: form.content\n      };\n    };\n    var userInitCallback = function userInitCallback(isElIsShow, isVisibleIsShow) {\n      elIsShow.value = isElIsShow;\n      visibleIsShow.value = isVisibleIsShow;\n    };\n    var userSelect = function userSelect(item) {\n      if (!form.joinUsers.includes(item.id)) {\n        form.joinUsers = [].concat(_toConsumableArray(form.joinUsers), [item.id]);\n      }\n    };\n    var globalReadConfig = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var _yield$api$globalRead, data;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.next = 2;\n              return api.globalReadConfig({\n                codes: ['suggestTitleNumber', 'suggestContentNumber', 'suggestMinSimilar']\n              });\n            case 2:\n              _yield$api$globalRead = _context5.sent;\n              data = _yield$api$globalRead.data;\n              if (data.suggestTitleNumber) {\n                suggestTitleNumber.value = Number(data.suggestTitleNumber);\n              }\n              if (data.suggestContentNumber) {\n                suggestContentNumber.value = Number(data.suggestContentNumber);\n              }\n              if (data.suggestMinSimilar) {\n                suggestMinSimilar.value = Number(data.suggestMinSimilar);\n              }\n            case 7:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function globalReadConfig() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var getProposalYear = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var _yield$api$getProposa, data;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.next = 2;\n              return api.getProposalYear();\n            case 2:\n              _yield$api$getProposa = _context6.sent;\n              data = _yield$api$getProposa.data;\n              ProposalYear.value = data.length ? data[0] : {};\n            case 5:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function getProposalYear() {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var termYearCurrent = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var _yield$api$termYearCu, data;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _context7.next = 2;\n              return api.termYearCurrent({\n                termYearType: 'cppcc_member'\n              });\n            case 2:\n              _yield$api$termYearCu = _context7.sent;\n              data = _yield$api$termYearCu.data;\n              termYearId.value = data.id;\n            case 5:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7);\n      }));\n      return function termYearCurrent() {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    var dictionaryData = /*#__PURE__*/function () {\n      var _ref9 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8() {\n        var _yield$api$dictionary, data;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              _context8.next = 2;\n              return api.dictionaryData({\n                dictCodes: ['suggest_open_type', 'suggest_survey_type', 'not_handle_time_type', 'is_hope_enhance_talk', 'is_make_mine_job', 'is_need_paper_answer']\n              });\n            case 2:\n              _yield$api$dictionary = _context8.sent;\n              data = _yield$api$dictionary.data;\n              suggestOpenType.value = data.suggest_open_type;\n              suggestSurveyType.value = data.suggest_survey_type;\n              notHandleTimeType.value = data.not_handle_time_type;\n              isHopeEnhanceTalk.value = data.is_hope_enhance_talk;\n              isMakeMineJob.value = data.is_make_mine_job;\n              isNeedPaperAnswer.value = data.is_need_paper_answer;\n            case 10:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8);\n      }));\n      return function dictionaryData() {\n        return _ref9.apply(this, arguments);\n      };\n    }();\n    var dictionaryNameData = /*#__PURE__*/function () {\n      var _ref10 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9() {\n        var _yield$api$dictionary2, data;\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1) switch (_context9.prev = _context9.next) {\n            case 0:\n              _context9.next = 2;\n              return api.dictionaryNameData({\n                dictCodes: ['suggest_open_type', 'suggest_survey_type', 'not_handle_time_type', 'is_hope_enhance_talk', 'is_make_mine_job', 'is_need_paper_answer']\n              });\n            case 2:\n              _yield$api$dictionary2 = _context9.sent;\n              data = _yield$api$dictionary2.data;\n              suggestOpenTypeName.value = data.suggest_open_type;\n              suggestSurveyTypeName.value = data.suggest_survey_type;\n              notHandleTimeTypeName.value = data.not_handle_time_type;\n              isHopeEnhanceTalkName.value = data.is_hope_enhance_talk;\n              isMakeMineJobName.value = data.is_make_mine_job;\n              isNeedPaperAnswerName.value = data.is_need_paper_answer;\n            case 10:\n            case \"end\":\n              return _context9.stop();\n          }\n        }, _callee9);\n      }));\n      return function dictionaryNameData() {\n        return _ref10.apply(this, arguments);\n      };\n    }();\n    var proposalClueInfo = /*#__PURE__*/function () {\n      var _ref11 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee10() {\n        var _yield$api$proposalCl, data;\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1) switch (_context10.prev = _context10.next) {\n            case 0:\n              _context10.next = 2;\n              return api.proposalClueInfo({\n                detailId: route.query.clueListId\n              });\n            case 2:\n              _yield$api$proposalCl = _context10.sent;\n              data = _yield$api$proposalCl.data;\n              form.title = data.title;\n              form.content = data.content;\n            case 6:\n            case \"end\":\n              return _context10.stop();\n          }\n        }, _callee10);\n      }));\n      return function proposalClueInfo() {\n        return _ref11.apply(this, arguments);\n      };\n    }();\n    var isLock = ref(false);\n    var lockVo = ref({});\n    var suggestionInfo = /*#__PURE__*/function () {\n      var _ref12 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee11() {\n        var _data$suggestOpenType, _data$suggestSurveyTy, _data$isMakeMineJob, _data$notHandleTimeTy, _data$isHopeEnhanceTa, _data$isNeedPaperAnsw, _data$hopeHandleOffic, _data$joinUsers, _data$contacters, res, data;\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              _context11.prev = 0;\n              _context11.next = 3;\n              return api.suggestionInfo({\n                detailId: route.query.id || route.query.anewId,\n                isOpenWithLock: queryType.value === 'review' ? 1 : null\n              });\n            case 3:\n              res = _context11.sent;\n              data = res.data;\n              reviewShow.value = true;\n              lockVo.value = data.lockVo;\n              isLock.value = route.query.type == 'review' && data.lockVo.isLock == 1 && data.lockVo.lockUserId != user.value.id;\n              form.suggestSubmitWay = data.suggestSubmitWay;\n              if (form.suggestSubmitWay === 'cppcc_member') {\n                tabCode.value = ['cppccMember'];\n                form.suggestUserId = data.suggestUserId;\n                if (data.suggestUserId) {\n                  cppccMemberInfo(data.suggestUserId);\n                }\n              }\n              if (form.suggestSubmitWay === 'team') {\n                isDisabled.value = true;\n                form.delegationId = data.delegationId;\n                delegationData.value = data.delegationId ? [{\n                  id: data.delegationId,\n                  name: data.delegationName\n                }] : [];\n              }\n              submitTypeChange();\n              form.title = data.title;\n              form.SuggestBigType = data.bigThemeId;\n              form.SuggestSmallType = data.smallThemeId;\n              // SuggestBigTypeChange()\n              form.termYearId = data.termYearId;\n              form.content = data.content.replace(/<p>/g, '<p style=\"font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;\">');\n              handleContentBlur();\n              form.isJoinProposal = data.isJoinProposal;\n              JoinChange();\n              form.suggestOpenType = (_data$suggestOpenType = data.suggestOpenType) === null || _data$suggestOpenType === void 0 ? void 0 : _data$suggestOpenType.value;\n              form.suggestSurveyType = (_data$suggestSurveyTy = data.suggestSurveyType) === null || _data$suggestSurveyTy === void 0 ? void 0 : _data$suggestSurveyTy.value;\n              form.isMakeMineJob = (_data$isMakeMineJob = data.isMakeMineJob) === null || _data$isMakeMineJob === void 0 ? void 0 : _data$isMakeMineJob.value;\n              form.notHandleTimeType = (_data$notHandleTimeTy = data.notHandleTimeType) === null || _data$notHandleTimeTy === void 0 ? void 0 : _data$notHandleTimeTy.value;\n              form.isHopeEnhanceTalk = (_data$isHopeEnhanceTa = data.isHopeEnhanceTalk) === null || _data$isHopeEnhanceTa === void 0 ? void 0 : _data$isHopeEnhanceTa.value;\n              form.isNeedPaperAnswer = (_data$isNeedPaperAnsw = data.isNeedPaperAnswer) === null || _data$isNeedPaperAnsw === void 0 ? void 0 : _data$isNeedPaperAnsw.value;\n              form.writerUserId = data.jordan;\n              fileData.value = data.attachments || [];\n              form.hopeHandleOfficeIds = ((_data$hopeHandleOffic = data.hopeHandleOfficeIds) === null || _data$hopeHandleOffic === void 0 ? void 0 : _data$hopeHandleOffic.map(function (v) {\n                return v.officeId;\n              })) || [];\n              form.joinUsers = ((_data$joinUsers = data.joinUsers) === null || _data$joinUsers === void 0 ? void 0 : _data$joinUsers.map(function (v) {\n                return v.userId;\n              })) || [];\n              if ((_data$contacters = data.contacters) !== null && _data$contacters !== void 0 && _data$contacters.length) {\n                contactPersonList.value = data.contacters.map(function (v) {\n                  return {\n                    id: v.id,\n                    contactName: v.contacterName,\n                    contactPhone: v.contacterMobile,\n                    contactAddress: v.contacterAddress\n                  };\n                });\n              }\n              userParams.value = {\n                authorId: form.suggestUserId,\n                content: form.content\n              };\n              if (!(form.isJoinProposal && (form.suggestUserId || form.writerUserId))) {\n                _context11.next = 37;\n                break;\n              }\n              isFlag.value = true;\n              hasLoadedScoreData.value = false;\n              _context11.next = 37;\n              return updateScoreProportionList();\n            case 37:\n              _context11.next = 42;\n              break;\n            case 39:\n              _context11.prev = 39;\n              _context11.t0 = _context11[\"catch\"](0);\n              if (_context11.t0.code === 500) {\n                if (route.query.id && queryType.value === 'review') {\n                  reviewShow.value = false;\n                  qiankunMicro.setGlobalState({\n                    closeOpenRoute: {\n                      openId: route.query.oldRouteId,\n                      closeId: route.query.routeId\n                    }\n                  });\n                }\n              }\n            case 42:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee11, null, [[0, 39]]);\n      }));\n      return function suggestionInfo() {\n        return _ref12.apply(this, arguments);\n      };\n    }();\n    var submitTypeChange = function submitTypeChange() {\n      if (form.suggestSubmitWay === 'cppcc_member') {\n        rules.suggestUserId = [{\n          required: true,\n          message: '请选择提案者',\n          trigger: ['blur', 'change']\n        }];\n        rules.delegationId = [{\n          required: false,\n          message: '请选择集体提案单位',\n          trigger: ['blur', 'change']\n        }];\n      } else if (form.suggestSubmitWay === 'team') {\n        rules.suggestUserId = [{\n          required: false,\n          message: '请选择提案者',\n          trigger: ['blur', 'change']\n        }];\n        rules.delegationId = [{\n          required: true,\n          message: '请选择集体提案单位',\n          trigger: ['blur', 'change']\n        }];\n      }\n      form.joinUsers = [];\n      scoreProportionData.value = [];\n      isFlag.value = false;\n    };\n    var JoinChange = function JoinChange() {\n      if (form.isJoinProposal) {\n        rules.joinUsers = [{\n          type: 'array',\n          required: true,\n          message: '请选择提案联名人',\n          trigger: ['blur', 'change']\n        }];\n      } else {\n        rules.joinUsers = [{\n          type: 'array',\n          required: false,\n          message: '请选择提案联名人',\n          trigger: ['blur', 'change']\n        }];\n        scoreProportionData.value = [];\n        isFlag.value = false;\n      }\n    };\n    var handleContentCount = function handleContentCount(count) {\n      contentCount.value = count;\n    };\n    watch(function () {\n      return form.suggestUserId;\n    }, function (newValue, oldValue) {\n      if (newValue !== oldValue) {\n        var newData = scoreProportionData.value.filter(function (person) {\n          return String(person.id) !== String(oldValue);\n        });\n        scoreProportionData.value = newData;\n      }\n    });\n    watch(function () {\n      return form.writerUserId;\n    }, function (newValue, oldValue) {\n      if (newValue !== oldValue) {\n        var newData = scoreProportionData.value.filter(function (person) {\n          return String(person.id) !== String(oldValue);\n        });\n        scoreProportionData.value = newData;\n      }\n    });\n    var userCallback = /*#__PURE__*/function () {\n      var _ref13 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee12(data) {\n        return _regeneratorRuntime().wrap(function _callee12$(_context12) {\n          while (1) switch (_context12.prev = _context12.next) {\n            case 0:\n              if (!data) {\n                _context12.next = 8;\n                break;\n              }\n              cppccMemberInfo(data.id);\n              form.joinUsers = form.joinUsers.filter(function (v) {\n                return v !== data.id;\n              });\n              if (!(form.isJoinProposal && form.joinUsers.length > 0)) {\n                _context12.next = 6;\n                break;\n              }\n              _context12.next = 6;\n              return updateScoreProportionList();\n            case 6:\n              _context12.next = 14;\n              break;\n            case 8:\n              form.cardNumber = '';\n              form.sectorType = '';\n              form.mobile = '';\n              form.callAddress = '';\n              scoreProportionData.value = [];\n              isFlag.value = false;\n            case 14:\n              userParams.value = {\n                authorId: form.suggestUserId,\n                content: form.content\n              };\n            case 15:\n            case \"end\":\n              return _context12.stop();\n          }\n        }, _callee12);\n      }));\n      return function userCallback(_x3) {\n        return _ref13.apply(this, arguments);\n      };\n    }();\n    var cppccMemberInfo = /*#__PURE__*/function () {\n      var _ref14 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee13(userId) {\n        var _data$sectorType;\n        var _yield$api$cppccMembe3, data;\n        return _regeneratorRuntime().wrap(function _callee13$(_context13) {\n          while (1) switch (_context13.prev = _context13.next) {\n            case 0:\n              _context13.next = 2;\n              return api.cppccMemberInfo({\n                detailId: userId\n              });\n            case 2:\n              _yield$api$cppccMembe3 = _context13.sent;\n              data = _yield$api$cppccMembe3.data;\n              form.cardNumber = data.cardNumberCppcc;\n              form.sectorType = (_data$sectorType = data.sectorType) === null || _data$sectorType === void 0 ? void 0 : _data$sectorType.label;\n              form.mobile = data.mobile;\n              form.callAddress = data.callAddress;\n            case 8:\n            case \"end\":\n              return _context13.stop();\n          }\n        }, _callee13);\n      }));\n      return function cppccMemberInfo(_x4) {\n        return _ref14.apply(this, arguments);\n      };\n    }();\n    var teamOfficeSelect = /*#__PURE__*/function () {\n      var _ref15 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee14(params) {\n        var _yield$api$teamOffice, data;\n        return _regeneratorRuntime().wrap(function _callee14$(_context14) {\n          while (1) switch (_context14.prev = _context14.next) {\n            case 0:\n              _context14.next = 2;\n              return api.teamOfficeSelect(params);\n            case 2:\n              _yield$api$teamOffice = _context14.sent;\n              data = _yield$api$teamOffice.data;\n              if (data.length) {\n                if (user.value.specialRoleKeys.includes('team_office_user')) {\n                  isDisabled.value = true;\n                  form.delegationId = data[0].id;\n                }\n              }\n              delegationData.value = data;\n            case 6:\n            case \"end\":\n              return _context14.stop();\n          }\n        }, _callee14);\n      }));\n      return function teamOfficeSelect(_x5) {\n        return _ref15.apply(this, arguments);\n      };\n    }();\n    var newContactPerson = function newContactPerson() {\n      contactPersonList.value.push({\n        id: guid(),\n        contactName: '',\n        contactPhone: '',\n        contactAddress: ''\n      });\n    };\n    var delContactPerson = function delContactPerson(id) {\n      contactPersonList.value = contactPersonList.value.filter(function (v) {\n        return v.id !== id;\n      });\n    };\n    var submitForm = /*#__PURE__*/function () {\n      var _ref16 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee15(formEl, type, cb) {\n        return _regeneratorRuntime().wrap(function _callee15$(_context15) {\n          while (1) switch (_context15.prev = _context15.next) {\n            case 0:\n              if (formEl) {\n                _context15.next = 2;\n                break;\n              }\n              return _context15.abrupt(\"return\");\n            case 2:\n              if (!(contentCount.value > suggestContentNumber.value)) {\n                _context15.next = 5;\n                break;\n              }\n              ElMessage({\n                type: 'warning',\n                message: `当前输入的提案内容超过了${suggestContentNumber.value}字，不允许提交！`\n              });\n              return _context15.abrupt(\"return\");\n            case 5:\n              if (!(contentCount.value < 200 && !queryType.value)) {\n                _context15.next = 8;\n                break;\n              }\n              ElMessage({\n                type: 'warning',\n                message: '提案字数不得少于200字！'\n              });\n              return _context15.abrupt(\"return\");\n            case 8:\n              _context15.next = 10;\n              return formEl.validate(function (valid, fields) {\n                if (valid) {\n                  if (whetherUseIntelligentize.value && !cb) {\n                    if (type) {\n                      globalJson(type, cb);\n                    } else {\n                      ElMessageBox.confirm('系统将为您进行相似度查询，是否同意执行操作？', '提示', {\n                        closeOnClickModal: false,\n                        confirmButtonText: '同意',\n                        cancelButtonText: '跳过'\n                      }).then(function () {\n                        handleSimilarity(true);\n                      }).catch(function () {\n                        globalJson(type, cb);\n                      });\n                    }\n                  } else {\n                    globalJson(type, cb);\n                  }\n                } else {\n                  ElMessage({\n                    type: 'warning',\n                    message: '请根据提示信息完善字段内容！'\n                  });\n                }\n              });\n            case 10:\n            case \"end\":\n              return _context15.stop();\n          }\n        }, _callee15);\n      }));\n      return function submitForm(_x6, _x7, _x8) {\n        return _ref16.apply(this, arguments);\n      };\n    }();\n    var editCallback = function editCallback(cb) {\n      submitForm(formRef.value, 0, cb);\n    };\n    var globalJson = /*#__PURE__*/function () {\n      var _ref17 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee16(type, cb) {\n        var resultScoreProportion, _yield$api$globalJson, code;\n        return _regeneratorRuntime().wrap(function _callee16$(_context16) {\n          while (1) switch (_context16.prev = _context16.next) {\n            case 0:\n              resultScoreProportion = scoreProportionData.value.map(function (item) {\n                return {\n                  userId: item.userId || item.id,\n                  scoreProportion: item.scoreProportion\n                };\n              });\n              _context16.prev = 1;\n              _context16.next = 4;\n              return api.globalJson(route.query.id ? '/proposal/edit' : '/proposal/add', {\n                form: {\n                  id: route.query.id,\n                  suggestSubmitWay: form.suggestSubmitWay,\n                  title: form.title,\n                  // 提案标题\n                  suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,\n                  delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,\n                  content: form.content,\n                  isJoinProposal: form.isJoinProposal,\n                  suggestOpenType: form.suggestOpenType,\n                  suggestSurveyType: form.suggestSurveyType,\n                  isMakeMineJob: form.isMakeMineJob,\n                  notHandleTimeType: form.notHandleTimeType,\n                  isHopeEnhanceTalk: form.isHopeEnhanceTalk,\n                  isNeedPaperAnswer: form.isNeedPaperAnswer,\n                  termYearId: route.query.id ? form.termYearId : ProposalYear.value.value,\n                  attachmentIds: fileData.value.map(function (v) {\n                    return v.id;\n                  })\n                },\n                objectParam: {\n                  teamMainAuthor: form.writerUserId ? form.writerUserId : ''\n                },\n                isSaveDraft: type,\n                arrayParam: resultScoreProportion,\n                joinUsers: form.isJoinProposal ? form.joinUsers : [],\n                hopeHandleOfficeIds: form.hopeHandleOfficeIds,\n                contacters: contactPersonList.value.filter(function (v) {\n                  return v.contactName.replace(/(^\\s*)|(\\s*$)/g, '') || v.contactPhone.replace(/(^\\s*)|(\\s*$)/g, '') || v.contactAddress.replace(/(^\\s*)|(\\s*$)/g, '');\n                }).map(function (v) {\n                  return {\n                    contacterName: v.contactName,\n                    contacterMobile: v.contactPhone,\n                    contacterAddress: v.contactAddress\n                  };\n                })\n              });\n            case 4:\n              _yield$api$globalJson = _context16.sent;\n              code = _yield$api$globalJson.code;\n              if (!(code === 200)) {\n                _context16.next = 18;\n                break;\n              }\n              cachedScoreData.value = null;\n              initialScoreData.value = null;\n              hasLoadedScoreData.value = false;\n              if (!route.query.clueListId) {\n                _context16.next = 14;\n                break;\n              }\n              proposalClueUse();\n              _context16.next = 18;\n              break;\n            case 14:\n              if (!cb) {\n                _context16.next = 16;\n                break;\n              }\n              return _context16.abrupt(\"return\", cb());\n            case 16:\n              ElMessage({\n                type: 'success',\n                message: route.query.id ? '编辑成功' : '提交成功'\n              });\n              if (route.query.id || route.query.anewId || route.query.clueListId) {\n                qiankunMicro.setGlobalState({\n                  closeOpenRoute: {\n                    openId: route.query.oldRouteId,\n                    closeId: route.query.routeId\n                  }\n                });\n              } else {\n                store.commit('setRefreshRoute', 'SubmitSuggest');\n                setTimeout(function () {\n                  store.commit('setRefreshRoute', '');\n                }, 222);\n              }\n            case 18:\n              _context16.next = 23;\n              break;\n            case 20:\n              _context16.prev = 20;\n              _context16.t0 = _context16[\"catch\"](1);\n              loading.value = false;\n            case 23:\n            case \"end\":\n              return _context16.stop();\n          }\n        }, _callee16, null, [[1, 20]]);\n      }));\n      return function globalJson(_x9, _x10) {\n        return _ref17.apply(this, arguments);\n      };\n    }();\n    var proposalClueUse = /*#__PURE__*/function () {\n      var _ref18 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee17() {\n        var _yield$api$proposalCl2, code;\n        return _regeneratorRuntime().wrap(function _callee17$(_context17) {\n          while (1) switch (_context17.prev = _context17.next) {\n            case 0:\n              _context17.next = 2;\n              return api.proposalClueUse({\n                detailId: route.query.clueListId\n              });\n            case 2:\n              _yield$api$proposalCl2 = _context17.sent;\n              code = _yield$api$proposalCl2.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '提交成功'\n                });\n                qiankunMicro.setGlobalState({\n                  closeOpenRoute: {\n                    openId: route.query.oldRouteId,\n                    closeId: route.query.routeId\n                  }\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context17.stop();\n          }\n        }, _callee17);\n      }));\n      return function proposalClueUse() {\n        return _ref18.apply(this, arguments);\n      };\n    }();\n    var resetForm = function resetForm() {\n      if (route.query.id || route.query.anewId || route.query.clueListId) {\n        qiankunMicro.setGlobalState({\n          closeOpenRoute: {\n            openId: route.query.oldRouteId,\n            closeId: route.query.routeId\n          }\n        });\n      } else {\n        store.commit('setRefreshRoute', 'SubmitSuggest');\n        setTimeout(function () {\n          store.commit('setRefreshRoute', '');\n        }, 222);\n      }\n    };\n    watch(function () {\n      return form.joinUsers;\n    }, /*#__PURE__*/function () {\n      var _ref19 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee18(newVal, oldVal) {\n        var proposerId;\n        return _regeneratorRuntime().wrap(function _callee18$(_context18) {\n          while (1) switch (_context18.prev = _context18.next) {\n            case 0:\n              if (!form.isJoinProposal) {\n                _context18.next = 6;\n                break;\n              }\n              proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId;\n              if (!proposerId) {\n                _context18.next = 6;\n                break;\n              }\n              if (!(JSON.stringify(newVal) !== JSON.stringify(oldVal))) {\n                _context18.next = 6;\n                break;\n              }\n              _context18.next = 6;\n              return updateScoreProportionList();\n            case 6:\n            case \"end\":\n              return _context18.stop();\n          }\n        }, _callee18);\n      }));\n      return function (_x11, _x12) {\n        return _ref19.apply(this, arguments);\n      };\n    }(), {\n      deep: true\n    });\n    watch([function () {\n      return form.suggestUserId;\n    }, function () {\n      return form.writerUserId;\n    }], /*#__PURE__*/function () {\n      var _ref20 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee19(newValues, oldValues) {\n        return _regeneratorRuntime().wrap(function _callee19$(_context19) {\n          while (1) switch (_context19.prev = _context19.next) {\n            case 0:\n              if (!(newValues[0] !== oldValues[0] || newValues[1] !== oldValues[1])) {\n                _context19.next = 4;\n                break;\n              }\n              if (!(form.isJoinProposal && form.joinUsers.length > 0)) {\n                _context19.next = 4;\n                break;\n              }\n              _context19.next = 4;\n              return updateScoreProportionList();\n            case 4:\n            case \"end\":\n              return _context19.stop();\n          }\n        }, _callee19);\n      }));\n      return function (_x13, _x14) {\n        return _ref20.apply(this, arguments);\n      };\n    }());\n    var unitedCallback = /*#__PURE__*/function () {\n      var _ref21 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee20(_data) {\n        var proposerId, proposer;\n        return _regeneratorRuntime().wrap(function _callee20$(_context20) {\n          while (1) switch (_context20.prev = _context20.next) {\n            case 0:\n              if (!(_data && _data.length > 0)) {\n                _context20.next = 5;\n                break;\n              }\n              _context20.next = 3;\n              return updateScoreProportionList();\n            case 3:\n              _context20.next = 7;\n              break;\n            case 5:\n              proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId;\n              if (proposerId) {\n                proposer = scoreProportionData.value.find(function (person) {\n                  return person.id === proposerId;\n                });\n                scoreProportionData.value = proposer ? [proposer] : [];\n                isFlag.value = true;\n              } else {\n                scoreProportionData.value = [];\n                isFlag.value = false;\n              }\n            case 7:\n            case \"end\":\n              return _context20.stop();\n          }\n        }, _callee20);\n      }));\n      return function unitedCallback(_x15) {\n        return _ref21.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      route,\n      store,\n      loading,\n      loadingText,\n      AreaId,\n      formRef,\n      cachedScoreData,\n      isRequesting,\n      initialScoreData,\n      form,\n      rules,\n      guid,\n      tinyMceSetting,\n      suggestTitleNumber,\n      suggestContentNumber,\n      suggestMinSimilar,\n      termYearId,\n      contentCount,\n      fileData,\n      delegationData,\n      suggestOpenTypeName,\n      suggestSurveyTypeName,\n      notHandleTimeTypeName,\n      isHopeEnhanceTalkName,\n      isMakeMineJobName,\n      isNeedPaperAnswerName,\n      suggestOpenType,\n      suggestSurveyType,\n      notHandleTimeType,\n      isHopeEnhanceTalk,\n      isMakeMineJob,\n      isNeedPaperAnswer,\n      contactPersonList,\n      typeShow,\n      disabled,\n      isDisabled,\n      tabCode,\n      reviewShow,\n      queryType,\n      show,\n      isShow,\n      elIsShow,\n      visibleIsShow,\n      userParams,\n      ProposalYear,\n      printParams,\n      elPrintWhetherShow,\n      unitedProportionShow,\n      scoreProportionData,\n      totalExceeds,\n      totalInsufficient,\n      isFlag,\n      hasLoadedScoreData,\n      get timers() {\n        return timers;\n      },\n      set timers(v) {\n        timers = v;\n      },\n      debounce,\n      handleInput,\n      handleBlur,\n      updateScoreProportionList,\n      handleExportWord,\n      handleSuggestPrint,\n      callback,\n      suggestionWord,\n      handleSimilarity,\n      handleSimilarityCallback,\n      handleContentBlur,\n      userInitCallback,\n      userSelect,\n      globalReadConfig,\n      getProposalYear,\n      termYearCurrent,\n      dictionaryData,\n      dictionaryNameData,\n      proposalClueInfo,\n      isLock,\n      lockVo,\n      suggestionInfo,\n      submitTypeChange,\n      JoinChange,\n      handleContentCount,\n      userCallback,\n      cppccMemberInfo,\n      teamOfficeSelect,\n      newContactPerson,\n      delContactPerson,\n      submitForm,\n      editCallback,\n      globalJson,\n      proposalClueUse,\n      resetForm,\n      unitedCallback,\n      get api() {\n        return api;\n      },\n      reactive,\n      ref,\n      onActivated,\n      onDeactivated,\n      onBeforeUnmount,\n      nextTick,\n      watch,\n      get useRoute() {\n        return useRoute;\n      },\n      get useStore() {\n        return useStore;\n      },\n      get user() {\n        return user;\n      },\n      get whetherUseIntelligentize() {\n        return whetherUseIntelligentize;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      SimilarityQuery,\n      SuggestRecommendUser,\n      DynamicTitle,\n      SuggestReviewDetail,\n      get suggestPrint() {\n        return suggestPrint;\n      },\n      get filterTableData() {\n        return filterTableData;\n      },\n      get exportWordHtmlObj() {\n        return exportWordHtmlObj;\n      },\n      ScoreProportion\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread", "arguments", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "asyncGeneratorStep", "_asyncToGenerator", "_next", "_throw", "api", "reactive", "ref", "onActivated", "onDeactivated", "onBeforeUnmount", "nextTick", "watch", "useRoute", "useStore", "user", "whetherUseIntelligentize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ElMessage", "ElMessageBox", "Similarity<PERSON><PERSON>y", "SuggestRecommendUser", "DynamicTitle", "SuggestReviewDetail", "suggest<PERSON><PERSON>t", "filterTableData", "exportWordHtmlObj", "ScoreProportion", "__default__", "route", "store", "loading", "loadingText", "AreaId", "sessionStorage", "getItem", "formRef", "cachedScoreData", "isRequesting", "initialScoreData", "form", "suggestSubmitWay", "title", "suggestUserId", "writer<PERSON>serId", "cardNumber", "sectorType", "mobile", "call<PERSON>dd<PERSON>", "delegationId", "isJoinProposal", "joinUsers", "content", "suggestOpenType", "suggestSurveyType", "isMakeMineJob", "notHandleTimeType", "isHopeEnhanceTalk", "isNeedPaperAnswer", "hopeHandleOfficeIds", "rules", "required", "message", "trigger", "guid", "replace", "Math", "random", "toString", "tinyMceSetting", "tp_layout_options", "style", "tagsStyle", "span", "contextmenu", "paste_postprocess", "plugin", "args", "target", "execCommand", "selection", "collapse", "import_word_callback", "editor", "suggestTitleNumber", "suggestContentNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "termYearId", "contentCount", "fileData", "delegationData", "suggestOpenTypeName", "suggestSurveyTypeName", "notHandleTimeTypeName", "isHopeEnhanceTalkName", "isMakeMineJobName", "isNeedPaperAnswerName", "contactPersonList", "id", "contactName", "contactPhone", "contactAddress", "typeShow", "disabled", "isDisabled", "tabCode", "reviewShow", "queryType", "show", "isShow", "elIsShow", "visibleIsShow", "userParams", "ProposalYear", "printParams", "elPrintWhetherShow", "unitedProportionShow", "scoreProportionData", "totalExceeds", "totalInsufficient", "isFlag", "hasLoadedScoreData", "timers", "debounce", "fn", "delay", "timer", "_len", "Array", "_key", "clearTimeout", "setTimeout", "handleInput", "row", "scoreProportion", "undefined", "test", "handleBlur", "filledTotal", "unfilledCount", "item", "parseInt", "remaining", "setGlobalState", "AiChatCode", "openAiParams", "JSON", "parse", "AiChatConfig", "AiChatWindow", "AiChatFile", "AiChatParams", "tool", "toolId", "param", "isPage", "AiChatSendMessage", "toolContent", "setItem", "stringify", "query", "clueListId", "proposalClueInfo", "globalReadConfig", "termYearCurrent", "dictionaryData", "dictionaryNameData", "getProposalYear", "anewId", "suggestionInfo", "special<PERSON><PERSON><PERSON><PERSON>s", "includes", "cppccMemberInfo", "teamOfficeSelect", "isSelectMine", "submitTypeChange", "updateScoreProportionList", "_ref2", "_callee2", "proposerId", "res", "newList", "existingProposer", "_yield$api$cppccMembe", "data", "existingJoinUsers", "newJoinUserIds", "newJoinUsersInfo", "updatedList", "_callee2$", "_context2", "globalJson", "detailId", "t0", "code", "console", "error", "find", "person", "userId", "userName", "t1", "some", "all", "map", "_ref3", "_callee", "_yield$api$cppccMembe2", "_callee$", "_context", "_x", "concat", "_toConsumableArray", "t2", "newItem", "_initialScoreData$val", "initialItem", "existingItem", "handleExportWord", "<PERSON><PERSON><PERSON>", "ids", "handleSuggestPrint", "_ref4", "_callee3", "_callee3$", "_context3", "callback", "closeOpenRoute", "openId", "oldRouteId", "closeId", "routeId", "_ref5", "_callee4", "params", "_yield$api$suggestion", "wordData", "index", "_callee4$", "_context4", "doc<PERSON>ame", "key", "_x2", "handleSimilarity", "isType", "handleSimilarityCallback", "handleContentBlur", "authorId", "userInitCallback", "isElIsShow", "isVisibleIsShow", "userSelect", "_ref6", "_callee5", "_yield$api$globalRead", "_callee5$", "_context5", "codes", "_ref7", "_callee6", "_yield$api$getProposa", "_callee6$", "_context6", "_ref8", "_callee7", "_yield$api$termYearCu", "_callee7$", "_context7", "termYearType", "_ref9", "_callee8", "_yield$api$dictionary", "_callee8$", "_context8", "dictCodes", "suggest_open_type", "suggest_survey_type", "not_handle_time_type", "is_hope_enhance_talk", "is_make_mine_job", "is_need_paper_answer", "_ref10", "_callee9", "_yield$api$dictionary2", "_callee9$", "_context9", "_ref11", "_callee10", "_yield$api$proposalCl", "_callee10$", "_context10", "isLock", "lockVo", "_ref12", "_callee11", "_data$suggestOpenType", "_data$suggestSurveyTy", "_data$isMakeMineJob", "_data$notHandleTimeTy", "_data$isHopeEnhanceTa", "_data$isNeedPaperAnsw", "_data$hopeHandleOffic", "_data$joinUsers", "_data$contacters", "_callee11$", "_context11", "isOpenWithLock", "lockUserId", "delegation<PERSON>ame", "SuggestBigType", "bigThemeId", "SuggestSmallType", "smallThemeId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jordan", "attachments", "officeId", "contacters", "contacterName", "contacterMobile", "contacter<PERSON><PERSON><PERSON>", "handleContentCount", "count", "newValue", "oldValue", "newData", "userCallback", "_ref13", "_callee12", "_callee12$", "_context12", "_x3", "_ref14", "_callee13", "_data$sectorType", "_yield$api$cppccMembe3", "_callee13$", "_context13", "cardNumberCppcc", "label", "_x4", "_ref15", "_callee14", "_yield$api$teamOffice", "_callee14$", "_context14", "_x5", "newContact<PERSON>erson", "delContact<PERSON>erson", "submitForm", "_ref16", "_callee15", "formEl", "cb", "_callee15$", "_context15", "validate", "valid", "fields", "confirm", "closeOnClickModal", "confirmButtonText", "cancelButtonText", "_x6", "_x7", "_x8", "edit<PERSON>allback", "_ref17", "_callee16", "resultScoreProportion", "_yield$api$globalJson", "_callee16$", "_context16", "attachmentIds", "objectParam", "teamMainAuthor", "isSaveDraft", "arrayParam", "proposalClueUse", "commit", "_x9", "_x10", "_ref18", "_callee17", "_yield$api$proposalCl2", "_callee17$", "_context17", "resetForm", "_ref19", "_callee18", "newVal", "oldVal", "_callee18$", "_context18", "_x11", "_x12", "deep", "_ref20", "_callee19", "newValues", "oldValues", "_callee19$", "_context19", "_x13", "_x14", "unitedCallback", "_ref21", "_callee20", "_data", "proposer", "_callee20$", "_context20", "_x15"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/BehalfSuggest/SubmitSuggest/SubmitSuggest.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"SubmitSuggest\" v-loading=\"loading\" :lement-loading-text=\"loadingText\">\r\n    <div class=\"SubmitSuggestBody\">\r\n      <div style=\"position:absolute;right: -13%;top: 0%;\" v-if=\"queryType == 'review'\">\r\n        <div class=\"detailsPrint\" title=\"打印提案\" @click=\"handleSuggestPrint\">打印提案</div>\r\n        <div class=\"detailsExportInfo\" title=\"导出提案word\" @click=\"handleExportWord\">导出提案word</div>\r\n      </div>\r\n      <div class=\"SubmitSuggestNameBody\">\r\n        <dynamic-title templateCode=\"proposal_title\" v-if=\"ProposalYear.description\"\r\n          :titles=\"ProposalYear.description\"></dynamic-title>\r\n        <dynamic-title templateCode=\"proposal_title\" v-else></dynamic-title>\r\n      </div>\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline :show-message=\"false\" class=\"globalPaperForm\">\r\n        <el-form-item label=\"提案提交类型\" v-if=\"!typeShow\" prop=\"suggestSubmitWay\" class=\"SubmitSuggestTitle\">\r\n          <el-radio-group v-model=\"form.suggestSubmitWay\" @change=\"submitTypeChange\">\r\n            <el-radio label=\"cppcc_member\">委员提案</el-radio>\r\n            <el-radio label=\"team\">集体提案</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案标题\" prop=\"title\" class=\"SubmitSuggestTitle\">\r\n          <el-input v-model=\"form.title\" placeholder=\"请输入提案标题\" show-word-limit :maxlength=\"suggestTitleNumber\"\r\n            clearable />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"提案者\" prop=\"suggestUserId\"\r\n          class=\"SubmitSuggestLeft\">\r\n          <input-select-person v-model=\"form.suggestUserId\" placeholder=\"请选择提案者\" :disabled=\"disabled\" :tabCode=\"tabCode\"\r\n            @callback=\"userCallback\" />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"委员证号\">\r\n          <el-input v-model=\"form.cardNumber\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"界别\" class=\"SubmitSuggestLeft\">\r\n          <el-input v-model=\"form.sectorType\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"联系电话\">\r\n          <el-input v-model=\"form.mobile\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"通讯地址\" class=\"SubmitSuggestTitle\">\r\n          <el-input v-model=\"form.callAddress\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'team'\" label=\"提案者\" prop=\"delegationId\"\r\n          class=\"SubmitSuggestTitle\">\r\n          <el-select v-model=\"form.delegationId\" :disabled=\"isDisabled\" placeholder=\"请选择集体提案单位\" clearable>\r\n            <el-option v-for=\"item in delegationData\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'team' && (AreaId == '370500' || AreaId == '370523')\"\r\n          label=\"主要撰稿人\" prop=\"writerUserId\" class=\"SubmitSuggestTitle\">\r\n          <input-select-person v-model=\"form.writerUserId\" placeholder=\"请选择主要撰稿人\" :disabled=\"disabled\"\r\n            :tabCode=\"tabCode\" @callback=\"userCallback\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否联名提案\" prop=\"isJoinProposal\" class=\"SubmitSuggestTitle\">\r\n          <el-radio-group v-model=\"form.isJoinProposal\" @change=\"JoinChange\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案联名人\" prop=\"joinUsers\" v-if=\"form.isJoinProposal\" class=\"SubmitSuggestTitle\">\r\n          <simple-select-person v-model=\"form.joinUsers\" placeholder=\"请选择提案联名人\"\r\n            :filterUser=\"form.suggestUserId ? [form.suggestUserId] : []\" :tabCode=\"['cppccMember']\"\r\n            @callback=\"unitedCallback\"></simple-select-person>\r\n          <template v-if=\"whetherUseIntelligentize && queryType !== 'review'\">\r\n            <intelligent-assistant v-model:elIsShow=\"elIsShow\" v-model=\"visibleIsShow\">\r\n              <SuggestRecommendUser :params=\"userParams\" @callback=\"userInitCallback\" @select=\"userSelect\">\r\n              </SuggestRecommendUser>\r\n            </intelligent-assistant>\r\n          </template>\r\n        </el-form-item>\r\n        <el-form-item label=\"得分占比分配\"\r\n          v-if=\"scoreProportionData && scoreProportionData.length > 0 && isFlag && (AreaId == '370500' || AreaId == '370505' || AreaId == '370522')\"\r\n          class=\"SubmitSuggestTitle\">\r\n          <el-table ref=\"tableRef\" row-key=\"id\" border :data=\"scoreProportionData\"\r\n            style=\"margin: 5px 20px;border: 1px solid #ccc;\">\r\n            <el-table-column label=\"姓名\" min-width=\"100\" show-overflow-tooltip align=\"center\">\r\n              <template #default=\"scope\">{{ scope.row.userName }}</template>\r\n            </el-table-column>\r\n            <el-table-column label=\"占比\" min-width=\"100\" show-overflow-tooltip align=\"center\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.scoreProportion\" style=\"width: 100px;\" type=\"number\"\r\n                  @input=\"handleInput(scope.row)\" @blur=\"handleBlur(scope.row)\" />&nbsp;&nbsp;%\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <p v-if=\"totalExceeds\" style=\"color: red;margin:0 20px;\">占比总和不能超过100%</p>\r\n          <p v-if=\"totalInsufficient\" style=\"color: red;margin:0 20px;\">占比总和不足100%</p>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案内容\" prop=\"content\" class=\"SubmitSuggestTitle SubmitSuggestButton\">\r\n          <!-- <div class=\"SubmitSuggestContentNumber\">事实清楚，建议明确，不超过{{ suggestContentNumber }}字</div> -->\r\n          <div></div>\r\n          <el-button @click=\"handleSimilarity(false)\"\r\n            v-if=\"whetherUseIntelligentize && queryType === 'review' && reviewShow\" type=\"primary\">\r\n            相似度查询\r\n          </el-button>\r\n        </el-form-item>\r\n        <TinyMceEditor v-model=\"form.content\" :setting=\"tinyMceSetting\" :max_count=\"suggestContentNumber\"\r\n          @count=\"handleContentCount\" @blur=\"handleContentBlur\" v-if=\"queryType !== 'review'\" textRectify\r\n          :placeholder=\"`事实清楚，建议明确，不超过${suggestContentNumber}字`\" />\r\n        <div v-html=\"form.content\" v-else class=\"content_box\"></div>\r\n        <div style=\"margin:10px 40px 50px;\" v-if=\"queryType == 'review'\">\r\n          <div class=\"detailsPrints\" title=\"打印提案\" @click=\"handleSuggestPrint\">打印提案</div>\r\n          <div class=\"detailsExportInfos\" title=\"导出提案word\" @click=\"handleExportWord\">导出提案word</div>\r\n        </div>\r\n        <!-- <el-form-item label=\"上传附件\" class=\"SubmitSuggestFormUpload\">\r\n          <xyl-upload-file :fileData=\"fileData\" @fileUpload=\"fileUpload\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"提案相关情况\" class=\"SubmitSuggestFormItem\"\r\n          style=\"border-top: 1px solid var(--zy-el-color-primary);\">\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"suggestOpenTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ suggestOpenTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.suggestOpenType\">\r\n              <el-radio v-for=\"item in suggestOpenType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"suggestSurveyTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ suggestSurveyTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.suggestSurveyType\">\r\n              <el-radio v-for=\"item in suggestSurveyType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isMakeMineJobName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isMakeMineJobName }}：</div>\r\n            <el-radio-group v-model=\"form.isMakeMineJob\">\r\n              <el-radio v-for=\"item in isMakeMineJob\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"notHandleTimeTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ notHandleTimeTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.notHandleTimeType\">\r\n              <el-radio v-for=\"item in notHandleTimeType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isHopeEnhanceTalkName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isHopeEnhanceTalkName }}：</div>\r\n            <el-radio-group v-model=\"form.isHopeEnhanceTalk\">\r\n              <el-radio v-for=\"item in isHopeEnhanceTalk\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isNeedPaperAnswerName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isNeedPaperAnswerName }}：</div>\r\n            <el-radio-group v-model=\"form.isNeedPaperAnswer\">\r\n              <el-radio v-for=\"item in isNeedPaperAnswer\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"希望送交办单位\" class=\"SubmitSuggestTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.hopeHandleOfficeIds\"></suggest-simple-select-unit>\r\n        </el-form-item> -->\r\n        <el-form-item class=\"SubmitSuggestContactPerson\" label=\"提案联系人\">\r\n          <div class=\"SubmitSuggestContactPersonHead\">\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">提案联系人姓名</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">提案联系人电话</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row3\">联系人通讯地址</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row1\">操作</div>\r\n          </div>\r\n          <div class=\"SubmitSuggestContactPersonBody\" v-for=\"item in contactPersonList\" :key=\"item.id\">\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">\r\n              <el-input placeholder=\"请输入联系人姓名\" v-model=\"item.contactName\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">\r\n              <el-input placeholder=\"请输入联系人电话\" v-model=\"item.contactPhone\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row3\">\r\n              <el-input placeholder=\"请输入联系人通讯地址\" v-model=\"item.contactAddress\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row1\">\r\n              <el-link @click=\"newContactPerson\" v-if=\"contactPersonList.length\">\r\n                <el-icon>\r\n                  <CirclePlus />\r\n                </el-icon>\r\n              </el-link>\r\n              <el-link v-if=\"contactPersonList.length > 1\" @click=\"delContactPerson(item.id)\">\r\n                <el-icon>\r\n                  <Remove />\r\n                </el-icon>\r\n              </el-link>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <div class=\"globalPaperFormButton\" v-if=\"queryType !== 'review'\">\r\n          <el-button type=\"primary\" @click=\"submitForm(formRef, 0)\">提交提案</el-button>\r\n          <el-button @click=\"submitForm(formRef, 1)\"\r\n            v-if=\"(!route.query.anewId && !route.query.id) || queryType === 'draft'\">\r\n            存为草稿\r\n          </el-button>\r\n          <el-button @click=\"resetForm\" v-if=\"!route.query.id\">重置</el-button>\r\n          <el-button @click=\"resetForm\" v-if=\"route.query.id\">取消</el-button>\r\n        </div>\r\n      </el-form>\r\n      <div v-if=\"queryType === 'review'\" class=\"SuggestSegmentation\"></div>\r\n      <keep-alive>\r\n        <SuggestReviewDetail :id=\"route.query.id\" :name=\"route.query.reviewName\" :title=\"form.title\"\r\n          :content=\"form.content\" :SuggestBigType=\"form.SuggestBigType\" :SuggestSmallType=\"form.SuggestSmallType\"\r\n          :hopeHandleOfficeIds=\"form.hopeHandleOfficeIds\" v-if=\"queryType === 'review' && reviewShow\"\r\n          @editCallback=\"editCallback\" @callback=\"resetForm\"></SuggestReviewDetail>\r\n      </keep-alive>\r\n    </div>\r\n    <suggestPrint v-if=\"elPrintWhetherShow\" :params=\"printParams\" @callback=\"callback\"></suggestPrint>\r\n    <xyl-popup-window v-model=\"show\" name=\"相似度查询\">\r\n      <SimilarityQuery :type=\"isShow\" :id=\"route.query.id\" :title=\"form.title\" :content=\"form.content\"\r\n        @callback=\"handleSimilarityCallback\"></SimilarityQuery>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"unitedProportionShow\" name=\"得分占比分配\">\r\n      <ScoreProportion :data=\"scoreProportionData\" @callback=\"scoreProportionCallback\"></ScoreProportion>\r\n    </xyl-popup-window>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitSuggest' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, nextTick, watch } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { user, whetherUseIntelligentize } from 'common/js/system_var.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport SimilarityQuery from '@/components/SimilarityQuery/SimilarityQuery.vue'\r\nimport SuggestRecommendUser from '@/components/SuggestRecommendUser/SuggestRecommendUser.vue'\r\nimport DynamicTitle from '@/components/global-dynamic-title/global-dynamic-title.vue'\r\nimport SuggestReviewDetail from '@/views/SuggestReview/component/SuggestReviewDetail.vue'\r\nimport suggestPrint from '@/components/suggestPrint/suggestPrint'\r\nimport { filterTableData } from '@/assets/js/suggestExportWord'\r\nimport { exportWordHtmlObj } from 'common/config/MicroGlobal'\r\nimport ScoreProportion from './ScoreProportion.vue'\r\n\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst AreaId = ref(sessionStorage.getItem('AreaId'))\r\nconst formRef = ref()\r\nconst cachedScoreData = ref(null)\r\nconst isRequesting = ref(false)\r\nconst initialScoreData = ref(null)\r\nconst form = reactive({\r\n  suggestSubmitWay: 'cppcc_member',\r\n  title: '', // 提案标题\r\n  suggestUserId: '',\r\n  writerUserId: '',\r\n  cardNumber: '',\r\n  sectorType: '',\r\n  mobile: '',\r\n  callAddress: '',\r\n  delegationId: '',\r\n  isJoinProposal: 0,\r\n  joinUsers: [],\r\n  content: '',\r\n  suggestOpenType: 'open_all',\r\n  suggestSurveyType: '3',\r\n  isMakeMineJob: '1',\r\n  notHandleTimeType: '1',\r\n  isHopeEnhanceTalk: '1',\r\n  isNeedPaperAnswer: '1',\r\n  hopeHandleOfficeIds: []\r\n})\r\nconst rules = reactive({\r\n  suggestSubmitWay: [{ required: true, message: '请选择提案提交类型', trigger: ['blur', 'change'] }],\r\n  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }],\r\n  suggestUserId: [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }],\r\n  delegationId: [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }],\r\n  isJoinProposal: [{ required: true, message: '请选择是否联名提案', trigger: ['blur', 'change'] }],\r\n  joinUsers: [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst tinyMceSetting = {\r\n  tp_layout_options: {\r\n    style: {\r\n      'text-align': 'justify',\r\n      'text-indent': '2em',\r\n      'line-height': '28pt',\r\n      'font-size': '16pt',\r\n      'font-family': '仿宋_GB2312'\r\n    },\r\n    tagsStyle: {\r\n      span: {\r\n        'text-align': 'justify',\r\n        'text-indent': '2em',\r\n        'line-height': '28pt',\r\n        'font-size': '16pt',\r\n        'font-family': '仿宋_GB2312'\r\n      }\r\n    }\r\n  },\r\n  contextmenu: false,\r\n  paste_postprocess: (plugin, args) => {\r\n    nextTick(() => {\r\n      args.target.execCommand('mceTpLayout')\r\n      nextTick(() => {\r\n        args.target.selection.collapse()\r\n      })\r\n    })\r\n  },\r\n  import_word_callback: (editor) => {\r\n    nextTick(() => {\r\n      editor.execCommand('mceTpLayout')\r\n      nextTick(() => {\r\n        editor.selection.collapse()\r\n      })\r\n    })\r\n  }\r\n}\r\nconst suggestTitleNumber = ref(30)\r\nconst suggestContentNumber = ref(2000)\r\nconst suggestMinSimilar = ref(0)\r\nconst termYearId = ref('')\r\nconst contentCount = ref(0)\r\nconst fileData = ref([])\r\nconst delegationData = ref([])\r\nconst suggestOpenTypeName = ref('')\r\nconst suggestSurveyTypeName = ref('')\r\nconst notHandleTimeTypeName = ref('')\r\nconst isHopeEnhanceTalkName = ref('')\r\nconst isMakeMineJobName = ref('')\r\nconst isNeedPaperAnswerName = ref('')\r\nconst suggestOpenType = ref([])\r\nconst suggestSurveyType = ref([])\r\nconst notHandleTimeType = ref([])\r\nconst isHopeEnhanceTalk = ref([])\r\nconst isMakeMineJob = ref([])\r\nconst isNeedPaperAnswer = ref([])\r\nconst contactPersonList = ref([{ id: guid(), contactName: '', contactPhone: '', contactAddress: '' }])\r\nconst typeShow = ref(false)\r\nconst disabled = ref(false)\r\nconst isDisabled = ref(false)\r\nconst tabCode = ref(['cppccMember'])\r\nconst reviewShow = ref(false)\r\nconst queryType = ref('')\r\n\r\nconst show = ref(false)\r\nconst isShow = ref(false)\r\nconst elIsShow = ref(false)\r\nconst visibleIsShow = ref(false)\r\nconst userParams = ref({})\r\nconst ProposalYear = ref({})\r\n\r\nconst printParams = ref({})\r\nconst elPrintWhetherShow = ref(false)\r\n\r\nconst unitedProportionShow = ref(false)\r\nconst scoreProportionData = ref([])\r\nconst totalExceeds = ref(false)\r\nconst totalInsufficient = ref(false)\r\nconst isFlag = ref(false)\r\nconst hasLoadedScoreData = ref(false)\r\nlet timers = null\r\nconst debounce = (fn, delay) => {\r\n  let timer = null\r\n  return function (...args) {\r\n    if (timer) clearTimeout(timer)\r\n    timer = setTimeout(() => {\r\n      fn(...args)\r\n    }, delay)\r\n  }\r\n}\r\n\r\nconst handleInput = debounce((row) => {\r\n  if (row.scoreProportion !== undefined && !/^[0-9]*$/.test(row.scoreProportion)) {\r\n    row.scoreProportion = row.scoreProportion.replace(/[^0-9]/g, '')\r\n  }\r\n}, 300)\r\n\r\nconst handleBlur = debounce((row) => {\r\n  let filledTotal = 0\r\n  let unfilledCount = 0\r\n  scoreProportionData.value.forEach(item => {\r\n    if (item.scoreProportion && !isNaN(item.scoreProportion)) {\r\n      filledTotal += parseInt(item.scoreProportion, 10)\r\n    } else {\r\n      unfilledCount++\r\n    }\r\n  })\r\n\r\n  if (filledTotal > 100) {\r\n    totalExceeds.value = true\r\n    totalInsufficient.value = false\r\n    row.scoreProportion = ''\r\n    return\r\n  }\r\n\r\n  if (unfilledCount > 0) {\r\n    const remaining = 100 - filledTotal\r\n    if (remaining < 0) {\r\n      totalExceeds.value = true\r\n      totalInsufficient.value = false\r\n      row.scoreProportion = ''\r\n      return\r\n    }\r\n    if (unfilledCount === 1) {\r\n      scoreProportionData.value.forEach(item => {\r\n        if (!item.scoreProportion || isNaN(item.scoreProportion)) {\r\n          item.scoreProportion = remaining.toString()\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  if (filledTotal < 100 && unfilledCount === 0) {\r\n    totalInsufficient.value = true\r\n    totalExceeds.value = false\r\n  } else {\r\n    totalInsufficient.value = false\r\n  }\r\n\r\n  if (filledTotal === 100 && unfilledCount === 0) {\r\n    totalExceeds.value = false\r\n    totalInsufficient.value = false\r\n  }\r\n}, 300)\r\n\r\n// const debouncedUpdateScoreProportion = debounce(() => {\r\n//   updateScoreProportionList()\r\n// }, 300)\r\n\r\nonActivated(() => {\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'ai-intelligent-write-chat' })\r\n  const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''\r\n  if (openAiParams) {\r\n    qiankunMicro.setGlobalState({\r\n      AiChatConfig: {\r\n        AiChatWindow: true,\r\n        AiChatFile: openAiParams.fileData,\r\n        AiChatParams: { tool: openAiParams.toolId, param: { isPage: '1' } },\r\n        AiChatSendMessage: openAiParams.toolContent\r\n      }\r\n    })\r\n    sessionStorage.setItem('openAiParams', JSON.stringify(''))\r\n    timers = setTimeout(() => {\r\n      qiankunMicro.setGlobalState({\r\n        AiChatConfig: {\r\n          AiChatWindow: true,\r\n          AiChatFile: openAiParams.fileData,\r\n          AiChatParams: {}\r\n        }\r\n      })\r\n    }, 2000)\r\n  }\r\n  queryType.value = route.query.type\r\n  if (route.query.clueListId) {\r\n    proposalClueInfo()\r\n  }\r\n  globalReadConfig()\r\n  termYearCurrent()\r\n  dictionaryData()\r\n  dictionaryNameData()\r\n  getProposalYear()\r\n  if (queryType.value === 'draft' || route.query.anewId) {\r\n    typeShow.value = true\r\n    disabled.value = true\r\n  }\r\n  if (route.query.id || route.query.anewId) {\r\n    typeShow.value = true\r\n    suggestionInfo()\r\n  } else {\r\n    tabCode.value = ['cppccMember']\r\n    if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n      typeShow.value = true\r\n    }\r\n    if (user.value.specialRoleKeys.includes('cppcc_member')) {\r\n      typeShow.value = true\r\n      disabled.value = true\r\n      form.suggestUserId = user.value.id\r\n      cppccMemberInfo(user.value.id)\r\n    } else {\r\n      if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n        form.suggestSubmitWay = 'team'\r\n      }\r\n    }\r\n    if (\r\n      user.value.specialRoleKeys.includes('team_office_user') &&\r\n      user.value.specialRoleKeys.includes('cppcc_member')\r\n    ) {\r\n      typeShow.value = false\r\n    }\r\n    if (user.value.specialRoleKeys.includes('admin')) {\r\n      form.suggestSubmitWay = 'cppcc_member'\r\n      typeShow.value = false\r\n      disabled.value = false\r\n      teamOfficeSelect({})\r\n    } else {\r\n      if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n        teamOfficeSelect({ isSelectMine: 1 })\r\n      } else {\r\n        teamOfficeSelect({})\r\n      }\r\n    }\r\n    submitTypeChange()\r\n  }\r\n  if (!route.query.id) {\r\n    cachedScoreData.value = null\r\n    initialScoreData.value = null\r\n    hasLoadedScoreData.value = false\r\n  }\r\n})\r\nonDeactivated(() => {\r\n  if (timers) {\r\n    clearTimeout(timers)\r\n    timers = null\r\n  }\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({\r\n    AiChatConfig: {\r\n      AiChatWindow: false,\r\n      AiChatFile: [],\r\n      AiChatParams: {}\r\n    }\r\n  })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n})\r\nonBeforeUnmount(() => {\r\n  if (timers) {\r\n    clearTimeout(timers)\r\n    timers = null\r\n  }\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({\r\n    AiChatConfig: {\r\n      AiChatWindow: false,\r\n      AiChatFile: [],\r\n      AiChatParams: {}\r\n    }\r\n  })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n})\r\n\r\nconst updateScoreProportionList = async () => {\r\n  if (isRequesting.value) {\r\n    return\r\n  }\r\n\r\n  const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId\r\n  if (!proposerId) {\r\n    isFlag.value = false\r\n    return\r\n  }\r\n\r\n  if (route.query.id && !hasLoadedScoreData.value) {\r\n    try {\r\n      isRequesting.value = true\r\n      const res = await api.globalJson('/proposalAllocationScore/info', {\r\n        detailId: route.query.id\r\n      })\r\n      if (res.data && res.data.length > 0) {\r\n        initialScoreData.value = res.data\r\n        cachedScoreData.value = res.data\r\n        scoreProportionData.value = res.data\r\n        isFlag.value = true\r\n        hasLoadedScoreData.value = true\r\n        return\r\n      }\r\n    } catch (error) {\r\n      if (error.code !== 'ERR_CANCELED') {\r\n        console.error('获取得分占比数据失败:', error)\r\n      }\r\n    } finally {\r\n      isRequesting.value = false\r\n    }\r\n  }\r\n\r\n  if (scoreProportionData.value.length > 0 && hasLoadedScoreData.value) {\r\n    return\r\n  }\r\n\r\n  let newList = []\r\n\r\n  const existingProposer = scoreProportionData.value.find(person => person.id === proposerId)\r\n  if (existingProposer) {\r\n    newList.push({\r\n      ...existingProposer,\r\n      scoreProportion: existingProposer.scoreProportion || ''\r\n    })\r\n  } else {\r\n    try {\r\n      isRequesting.value = true\r\n      const { data } = await api.cppccMemberInfo({ detailId: proposerId })\r\n      newList.push({\r\n        id: proposerId,\r\n        userId: proposerId,\r\n        userName: data.userName,\r\n        scoreProportion: ''\r\n      })\r\n    } catch (error) {\r\n      console.error('获取提案者信息失败:', error)\r\n    } finally {\r\n      isRequesting.value = false\r\n    }\r\n  }\r\n\r\n  if (form.joinUsers && form.joinUsers.length > 0) {\r\n    const existingJoinUsers = scoreProportionData.value.filter(person =>\r\n      form.joinUsers.includes(person.id)\r\n    )\r\n\r\n    const newJoinUserIds = form.joinUsers.filter(id =>\r\n      !existingJoinUsers.some(user => user.id === id)\r\n    )\r\n\r\n    if (newJoinUserIds.length > 0) {\r\n      try {\r\n        isRequesting.value = true\r\n        const newJoinUsersInfo = await Promise.all(\r\n          newJoinUserIds.map(async (userId) => {\r\n            const { data } = await api.cppccMemberInfo({ detailId: userId })\r\n            return {\r\n              id: userId,\r\n              userId: userId,\r\n              userName: data.userName,\r\n              scoreProportion: ''\r\n            }\r\n          })\r\n        )\r\n        newList = [...newList, ...existingJoinUsers, ...newJoinUsersInfo]\r\n      } catch (error) {\r\n        console.error('获取联名人信息失败:', error)\r\n      } finally {\r\n        isRequesting.value = false\r\n      }\r\n    } else {\r\n      newList = [...newList, ...existingJoinUsers]\r\n    }\r\n  }\r\n\r\n  const updatedList = newList.map(newItem => {\r\n    const initialItem = initialScoreData.value?.find(item => item.id === newItem.id)\r\n    const existingItem = scoreProportionData.value.find(item => item.id === newItem.id)\r\n    return {\r\n      ...newItem,\r\n      scoreProportion: initialItem?.scoreProportion || existingItem?.scoreProportion || newItem.scoreProportion\r\n    }\r\n  })\r\n\r\n  scoreProportionData.value = updatedList\r\n  isFlag.value = true\r\n  hasLoadedScoreData.value = true\r\n}\r\n\r\nconst handleExportWord = () => {\r\n  if (!form.content) return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行导出！' })\r\n  suggestionWord({ ids: [route.query.id] })\r\n}\r\nconst handleSuggestPrint = async () => {\r\n  if (!form.content) return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行打印！' })\r\n  printParams.value = { ids: [route.query.id] }\r\n  elPrintWhetherShow.value = true\r\n}\r\nconst callback = (type) => {\r\n  elPrintWhetherShow.value = false\r\n  if (type) {\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  }\r\n}\r\nconst suggestionWord = async (params) => {\r\n  const { data } = await api.suggestionWord(params)\r\n  if (data.length) {\r\n    var wordData = {}\r\n    for (let index = 0; index < data.length; index++) {\r\n      wordData = filterTableData(data[index])\r\n    }\r\n    exportWordHtmlObj({ code: 'proposalDetails', name: wordData.docName, key: 'content', data: wordData })\r\n  }\r\n}\r\nconst handleSimilarity = (isType) => {\r\n  if (!form.content) return ElMessage({ type: 'warning', message: '请输入提案内容进行相似度查询！' })\r\n  sessionStorage.setItem('TextQueryToolTitle', form.title)\r\n  sessionStorage.setItem('TextQueryToolContent', form.content)\r\n  isShow.value = isType\r\n  show.value = true\r\n}\r\nconst handleSimilarityCallback = (type) => {\r\n  if (type) globalJson(0)\r\n  show.value = false\r\n}\r\nconst handleContentBlur = () => {\r\n  userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n}\r\nconst userInitCallback = (isElIsShow, isVisibleIsShow) => {\r\n  elIsShow.value = isElIsShow\r\n  visibleIsShow.value = isVisibleIsShow\r\n}\r\nconst userSelect = (item) => {\r\n  if (!form.joinUsers.includes(item.id)) {\r\n    form.joinUsers = [...form.joinUsers, item.id]\r\n  }\r\n}\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({\r\n    codes: ['suggestTitleNumber', 'suggestContentNumber', 'suggestMinSimilar']\r\n  })\r\n  if (data.suggestTitleNumber) {\r\n    suggestTitleNumber.value = Number(data.suggestTitleNumber)\r\n  }\r\n  if (data.suggestContentNumber) {\r\n    suggestContentNumber.value = Number(data.suggestContentNumber)\r\n  }\r\n  if (data.suggestMinSimilar) {\r\n    suggestMinSimilar.value = Number(data.suggestMinSimilar)\r\n  }\r\n}\r\nconst getProposalYear = async () => {\r\n  const { data } = await api.getProposalYear()\r\n  ProposalYear.value = data.length ? data[0] : {}\r\n}\r\nconst termYearCurrent = async () => {\r\n  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })\r\n  termYearId.value = data.id\r\n}\r\nconst dictionaryData = async () => {\r\n  const { data } = await api.dictionaryData({\r\n    dictCodes: [\r\n      'suggest_open_type',\r\n      'suggest_survey_type',\r\n      'not_handle_time_type',\r\n      'is_hope_enhance_talk',\r\n      'is_make_mine_job',\r\n      'is_need_paper_answer'\r\n    ]\r\n  })\r\n  suggestOpenType.value = data.suggest_open_type\r\n  suggestSurveyType.value = data.suggest_survey_type\r\n  notHandleTimeType.value = data.not_handle_time_type\r\n  isHopeEnhanceTalk.value = data.is_hope_enhance_talk\r\n  isMakeMineJob.value = data.is_make_mine_job\r\n  isNeedPaperAnswer.value = data.is_need_paper_answer\r\n}\r\nconst dictionaryNameData = async () => {\r\n  const { data } = await api.dictionaryNameData({\r\n    dictCodes: [\r\n      'suggest_open_type',\r\n      'suggest_survey_type',\r\n      'not_handle_time_type',\r\n      'is_hope_enhance_talk',\r\n      'is_make_mine_job',\r\n      'is_need_paper_answer'\r\n    ]\r\n  })\r\n  suggestOpenTypeName.value = data.suggest_open_type\r\n  suggestSurveyTypeName.value = data.suggest_survey_type\r\n  notHandleTimeTypeName.value = data.not_handle_time_type\r\n  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk\r\n  isMakeMineJobName.value = data.is_make_mine_job\r\n  isNeedPaperAnswerName.value = data.is_need_paper_answer\r\n}\r\nconst proposalClueInfo = async () => {\r\n  const { data } = await api.proposalClueInfo({ detailId: route.query.clueListId })\r\n  form.title = data.title\r\n  form.content = data.content\r\n}\r\nconst isLock = ref(false)\r\nconst lockVo = ref({})\r\nconst suggestionInfo = async () => {\r\n  try {\r\n    const res = await api.suggestionInfo({\r\n      detailId: route.query.id || route.query.anewId,\r\n      isOpenWithLock: queryType.value === 'review' ? 1 : null\r\n    })\r\n    var { data } = res\r\n    reviewShow.value = true\r\n    lockVo.value = data.lockVo\r\n    isLock.value = route.query.type == 'review' && data.lockVo.isLock == 1 && data.lockVo.lockUserId != user.value.id\r\n    form.suggestSubmitWay = data.suggestSubmitWay\r\n    if (form.suggestSubmitWay === 'cppcc_member') {\r\n      tabCode.value = ['cppccMember']\r\n      form.suggestUserId = data.suggestUserId\r\n      if (data.suggestUserId) {\r\n        cppccMemberInfo(data.suggestUserId)\r\n      }\r\n    }\r\n    if (form.suggestSubmitWay === 'team') {\r\n      isDisabled.value = true\r\n      form.delegationId = data.delegationId\r\n      delegationData.value = data.delegationId ? [{ id: data.delegationId, name: data.delegationName }] : []\r\n    }\r\n    submitTypeChange()\r\n    form.title = data.title\r\n    form.SuggestBigType = data.bigThemeId\r\n    form.SuggestSmallType = data.smallThemeId\r\n    // SuggestBigTypeChange()\r\n    form.termYearId = data.termYearId\r\n    form.content = data.content.replace(/<p>/g, '<p style=\"font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;\">');\r\n    handleContentBlur()\r\n    form.isJoinProposal = data.isJoinProposal\r\n    JoinChange()\r\n    form.suggestOpenType = data.suggestOpenType?.value\r\n    form.suggestSurveyType = data.suggestSurveyType?.value\r\n    form.isMakeMineJob = data.isMakeMineJob?.value\r\n    form.notHandleTimeType = data.notHandleTimeType?.value\r\n    form.isHopeEnhanceTalk = data.isHopeEnhanceTalk?.value\r\n    form.isNeedPaperAnswer = data.isNeedPaperAnswer?.value\r\n    form.writerUserId = data.jordan\r\n    fileData.value = data.attachments || []\r\n    form.hopeHandleOfficeIds = data.hopeHandleOfficeIds?.map((v) => v.officeId) || []\r\n    form.joinUsers = data.joinUsers?.map((v) => v.userId) || []\r\n    if (data.contacters?.length) {\r\n      contactPersonList.value = data.contacters.map((v) => ({\r\n        id: v.id,\r\n        contactName: v.contacterName,\r\n        contactPhone: v.contacterMobile,\r\n        contactAddress: v.contacterAddress\r\n      }))\r\n    }\r\n    userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n\r\n    if (form.isJoinProposal && (form.suggestUserId || form.writerUserId)) {\r\n      isFlag.value = true\r\n      hasLoadedScoreData.value = false\r\n      await updateScoreProportionList()\r\n    }\r\n  } catch (err) {\r\n    if (err.code === 500) {\r\n      if (route.query.id && queryType.value === 'review') {\r\n        reviewShow.value = false\r\n        qiankunMicro.setGlobalState({\r\n          closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\nconst submitTypeChange = () => {\r\n  if (form.suggestSubmitWay === 'cppcc_member') {\r\n    rules.suggestUserId = [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }]\r\n    rules.delegationId = [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.suggestSubmitWay === 'team') {\r\n    rules.suggestUserId = [{ required: false, message: '请选择提案者', trigger: ['blur', 'change'] }]\r\n    rules.delegationId = [{ required: true, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]\r\n  }\r\n  form.joinUsers = []\r\n  scoreProportionData.value = []\r\n  isFlag.value = false\r\n}\r\nconst JoinChange = () => {\r\n  if (form.isJoinProposal) {\r\n    rules.joinUsers = [{ type: 'array', required: true, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.joinUsers = [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n    scoreProportionData.value = []\r\n    isFlag.value = false\r\n  }\r\n}\r\nconst handleContentCount = (count) => {\r\n  contentCount.value = count\r\n}\r\nwatch(() => form.suggestUserId, (newValue, oldValue) => {\r\n  if (newValue !== oldValue) {\r\n    const newData = scoreProportionData.value.filter(\r\n      person => String(person.id) !== String(oldValue)\r\n    )\r\n    scoreProportionData.value = newData\r\n  }\r\n}\r\n)\r\nwatch(() => form.writerUserId, (newValue, oldValue) => {\r\n  if (newValue !== oldValue) {\r\n    const newData = scoreProportionData.value.filter(\r\n      person => String(person.id) !== String(oldValue)\r\n    )\r\n    scoreProportionData.value = newData\r\n  }\r\n}\r\n)\r\nconst userCallback = async (data) => {\r\n  if (data) {\r\n    cppccMemberInfo(data.id)\r\n    form.joinUsers = form.joinUsers.filter((v) => v !== data.id)\r\n    if (form.isJoinProposal && form.joinUsers.length > 0) {\r\n      await updateScoreProportionList()\r\n    }\r\n  } else {\r\n    form.cardNumber = ''\r\n    form.sectorType = ''\r\n    form.mobile = ''\r\n    form.callAddress = ''\r\n    scoreProportionData.value = []\r\n    isFlag.value = false\r\n  }\r\n  userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n}\r\nconst cppccMemberInfo = async (userId) => {\r\n  const { data } = await api.cppccMemberInfo({ detailId: userId })\r\n  form.cardNumber = data.cardNumberCppcc\r\n  form.sectorType = data.sectorType?.label\r\n  form.mobile = data.mobile\r\n  form.callAddress = data.callAddress\r\n}\r\nconst teamOfficeSelect = async (params) => {\r\n  const { data } = await api.teamOfficeSelect(params)\r\n  if (data.length) {\r\n    if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n      isDisabled.value = true\r\n      form.delegationId = data[0].id\r\n    }\r\n  }\r\n  delegationData.value = data\r\n}\r\nconst newContactPerson = () => {\r\n  contactPersonList.value.push({ id: guid(), contactName: '', contactPhone: '', contactAddress: '' })\r\n}\r\nconst delContactPerson = (id) => {\r\n  contactPersonList.value = contactPersonList.value.filter((v) => v.id !== id)\r\n}\r\nconst submitForm = async (formEl, type, cb) => {\r\n  if (!formEl) return\r\n  if (contentCount.value > suggestContentNumber.value) {\r\n    ElMessage({ type: 'warning', message: `当前输入的提案内容超过了${suggestContentNumber.value}字，不允许提交！` })\r\n    return\r\n  }\r\n  if (contentCount.value < 200 && !queryType.value) {\r\n    ElMessage({ type: 'warning', message: '提案字数不得少于200字！' })\r\n    return\r\n  }\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      if (whetherUseIntelligentize.value && !cb) {\r\n        if (type) {\r\n          globalJson(type, cb)\r\n        } else {\r\n          ElMessageBox.confirm('系统将为您进行相似度查询，是否同意执行操作？', '提示', {\r\n            closeOnClickModal: false,\r\n            confirmButtonText: '同意',\r\n            cancelButtonText: '跳过'\r\n          })\r\n            .then(() => {\r\n              handleSimilarity(true)\r\n            })\r\n            .catch(() => {\r\n              globalJson(type, cb)\r\n            })\r\n        }\r\n      } else {\r\n        globalJson(type, cb)\r\n      }\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\nconst editCallback = (cb) => {\r\n  submitForm(formRef.value, 0, cb)\r\n}\r\nconst globalJson = async (type, cb) => {\r\n  const resultScoreProportion = scoreProportionData.value.map(item => ({\r\n    userId: item.userId || item.id,\r\n    scoreProportion: item.scoreProportion\r\n  }))\r\n  try {\r\n    const { code } = await api.globalJson(route.query.id ? '/proposal/edit' : '/proposal/add', {\r\n      form: {\r\n        id: route.query.id,\r\n        suggestSubmitWay: form.suggestSubmitWay,\r\n        title: form.title, // 提案标题\r\n        suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,\r\n        delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,\r\n        content: form.content,\r\n        isJoinProposal: form.isJoinProposal,\r\n        suggestOpenType: form.suggestOpenType,\r\n        suggestSurveyType: form.suggestSurveyType,\r\n        isMakeMineJob: form.isMakeMineJob,\r\n        notHandleTimeType: form.notHandleTimeType,\r\n        isHopeEnhanceTalk: form.isHopeEnhanceTalk,\r\n        isNeedPaperAnswer: form.isNeedPaperAnswer,\r\n        termYearId: route.query.id ? form.termYearId : ProposalYear.value.value,\r\n        attachmentIds: fileData.value.map((v) => v.id)\r\n      },\r\n      objectParam: {\r\n        teamMainAuthor: form.writerUserId ? form.writerUserId : ''\r\n      },\r\n      isSaveDraft: type,\r\n      arrayParam: resultScoreProportion,\r\n      joinUsers: form.isJoinProposal ? form.joinUsers : [],\r\n      hopeHandleOfficeIds: form.hopeHandleOfficeIds,\r\n      contacters: contactPersonList.value\r\n        .filter(\r\n          (v) =>\r\n            v.contactName.replace(/(^\\s*)|(\\s*$)/g, '') ||\r\n            v.contactPhone.replace(/(^\\s*)|(\\s*$)/g, '') ||\r\n            v.contactAddress.replace(/(^\\s*)|(\\s*$)/g, '')\r\n        )\r\n        .map((v) => ({\r\n          contacterName: v.contactName,\r\n          contacterMobile: v.contactPhone,\r\n          contacterAddress: v.contactAddress\r\n        }))\r\n    })\r\n    if (code === 200) {\r\n      cachedScoreData.value = null\r\n      initialScoreData.value = null\r\n      hasLoadedScoreData.value = false\r\n      if (route.query.clueListId) {\r\n        proposalClueUse()\r\n      } else {\r\n        if (cb) {\r\n          return cb()\r\n        }\r\n        ElMessage({ type: 'success', message: route.query.id ? '编辑成功' : '提交成功' })\r\n        if (route.query.id || route.query.anewId || route.query.clueListId) {\r\n          qiankunMicro.setGlobalState({\r\n            closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }\r\n          })\r\n        } else {\r\n          store.commit('setRefreshRoute', 'SubmitSuggest')\r\n          setTimeout(() => {\r\n            store.commit('setRefreshRoute', '')\r\n          }, 222)\r\n        }\r\n      }\r\n    }\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nconst proposalClueUse = async () => {\r\n  const { code } = await api.proposalClueUse({ detailId: route.query.clueListId })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '提交成功' })\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  if (route.query.id || route.query.anewId || route.query.clueListId) {\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  } else {\r\n    store.commit('setRefreshRoute', 'SubmitSuggest')\r\n    setTimeout(() => {\r\n      store.commit('setRefreshRoute', '')\r\n    }, 222)\r\n  }\r\n}\r\n\r\nwatch(\r\n  () => form.joinUsers,\r\n  async (newVal, oldVal) => {\r\n    if (form.isJoinProposal) {\r\n      const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId\r\n      if (proposerId) {\r\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\r\n          await updateScoreProportionList()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  { deep: true }\r\n)\r\n\r\nwatch(\r\n  [() => form.suggestUserId, () => form.writerUserId],\r\n  async (newValues, oldValues) => {\r\n    if (newValues[0] !== oldValues[0] || newValues[1] !== oldValues[1]) {\r\n      if (form.isJoinProposal && form.joinUsers.length > 0) {\r\n        await updateScoreProportionList()\r\n      }\r\n    }\r\n  }\r\n)\r\n\r\nconst unitedCallback = async (_data) => {\r\n  if (_data && _data.length > 0) {\r\n    await updateScoreProportionList()\r\n  } else {\r\n    const proposerId = form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : form.writerUserId\r\n    if (proposerId) {\r\n      const proposer = scoreProportionData.value.find(person => person.id === proposerId)\r\n      scoreProportionData.value = proposer ? [proposer] : []\r\n      isFlag.value = true\r\n    } else {\r\n      scoreProportionData.value = []\r\n      isFlag.value = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitSuggest {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SubmitSuggestBody {\r\n    width: 990px;\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    position: relative;\r\n\r\n    .detailsPrints,\r\n    .detailsExportInfos {\r\n      color: #3657c0;\r\n      font-size: 18px;\r\n      line-height: var(--zy-line-height);\r\n      padding-left: 35px;\r\n      margin-bottom: 20px;\r\n      position: relative;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .detailsPrints {\r\n      background: url(\"../../../assets/img/suggest_details_print.png\") no-repeat;\r\n      background-size: 30px 30px;\r\n      background-position: left center;\r\n    }\r\n\r\n    .detailsExportInfos {\r\n      background: url(\"../../../assets/img/suggest_details_export_info.png\") no-repeat;\r\n      background-size: 30px 30px;\r\n      background-position: left center;\r\n    }\r\n\r\n    .detailsPrint,\r\n    .detailsExportInfo {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-left: 30px;\r\n      margin-bottom: 20px;\r\n      position: relative;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .detailsPrint {\r\n      background: url(\"../../../assets/img/suggest_details_print.png\") no-repeat;\r\n      background-size: 20px 20px;\r\n      background-position: left center;\r\n    }\r\n\r\n    .detailsExportInfo {\r\n      background: url(\"../../../assets/img/suggest_details_export_info.png\") no-repeat;\r\n      background-size: 20px 20px;\r\n      background-position: left center;\r\n    }\r\n\r\n    .SubmitSuggestNameBody {\r\n      padding: var(--zy-distance-one);\r\n      padding-bottom: 0;\r\n\r\n      .global-dynamic-title {\r\n        border-bottom: 3px solid var(--zy-el-color-primary);\r\n      }\r\n    }\r\n\r\n    .globalPaperForm {\r\n      width: 100%;\r\n      padding: var(--zy-distance-one);\r\n      padding-top: 0;\r\n\r\n      .zy-el-form-item {\r\n        width: 50%;\r\n        margin: 0;\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n\r\n        .zy-el-form-item__label {\r\n          width: 138px;\r\n          justify-content: center;\r\n        }\r\n\r\n        .zy-el-form-item__content {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n          border-right: 1px solid transparent;\r\n\r\n          &>.simple-select-person {\r\n            box-shadow: 0 0 0 0 !important;\r\n          }\r\n\r\n          &>.zy-el-input,\r\n          .zy-el-input-number {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-select,\r\n          .zy-el-select-v2 {\r\n            .zy-el-select__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-radio-group {\r\n            padding-left: 15px;\r\n          }\r\n\r\n          &>.zy-el-date-editor {\r\n            width: 100%;\r\n\r\n            &>.zy-el-input__wrapper {\r\n              width: 100%;\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestLeft {\r\n        .zy-el-form-item__content {\r\n          border-right-color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestTitle {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          border-right-color: transparent;\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestButton {\r\n        .zy-el-form-item__content {\r\n          flex-wrap: nowrap;\r\n          justify-content: space-between;\r\n\r\n          .SubmitSuggestContentNumber {\r\n            padding: 0 10px;\r\n            color: var(--zy-el-color-error);\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n          }\r\n\r\n          .SubmitSuggestUpload {\r\n            margin-left: 12px;\r\n            margin-right: 12px;\r\n          }\r\n\r\n          .zy-el-button {\r\n            --zy-el-button-size: var(--zy-height-routine);\r\n          }\r\n        }\r\n      }\r\n\r\n      .TinyMceEditor {\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n      }\r\n\r\n      .content_box p span {\r\n        line-height: 1.5 !important;\r\n        font-size: 21px !important;\r\n      }\r\n\r\n      .SubmitSuggestFormUpload {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          padding: 15px;\r\n          border-right-color: transparent;\r\n\r\n          .SubmitSuggestFormInfo {\r\n            width: 100%;\r\n            display: flex;\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestFormItem {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          padding: 0 15px;\r\n          border-right-color: transparent;\r\n\r\n          .SubmitSuggestFormInfo {\r\n            width: 100%;\r\n            display: flex;\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestContactPerson {\r\n        width: 100%;\r\n\r\n        .SubmitSuggestContactPersonHead,\r\n        .SubmitSuggestContactPersonBody {\r\n          width: 100%;\r\n          display: flex;\r\n        }\r\n\r\n        .SubmitSuggestContactPersonBody {\r\n          border-top: 1px solid var(--zy-el-color-primary);\r\n        }\r\n\r\n        .row1 {\r\n          flex: 1;\r\n        }\r\n\r\n        .row2 {\r\n          flex: 2;\r\n        }\r\n\r\n        .row3 {\r\n          flex: 3;\r\n        }\r\n\r\n        .SubmitSuggestContactPersonItem {\r\n          height: 40px;\r\n          line-height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &>.zy-el-input {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          .zy-el-link {\r\n            font-size: 18px;\r\n            line-height: 24px;\r\n          }\r\n\r\n          .zy-el-link+.zy-el-link {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n\r\n        .SubmitSuggestContactPersonItem+.SubmitSuggestContactPersonItem {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .globalPaperFormButton {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding-top: 22px;\r\n\r\n        .zy-el-button+.zy-el-button {\r\n          margin-left: var(--zy-distance-two);\r\n        }\r\n      }\r\n    }\r\n\r\n    .SuggestSegmentation {\r\n      width: 100%;\r\n      height: 10px;\r\n      background-color: var(--zy-el-color-info-light-9);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;+CAmNA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,QAAAvG,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAqG,qBAAA,QAAAjG,CAAA,GAAAJ,MAAA,CAAAqG,qBAAA,CAAAxG,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAkG,MAAA,WAAAvG,CAAA,WAAAC,MAAA,CAAAuG,wBAAA,CAAA1G,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAkC,KAAA,CAAA1G,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2G,cAAA5G,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAA2G,SAAA,CAAA/B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAA4G,SAAA,CAAA3G,CAAA,IAAA2G,SAAA,CAAA3G,CAAA,QAAAA,CAAA,OAAAqG,OAAA,CAAApG,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA4G,eAAA,CAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA4G,yBAAA,GAAA5G,MAAA,CAAA6G,gBAAA,CAAAhH,CAAA,EAAAG,MAAA,CAAA4G,yBAAA,CAAA9G,CAAA,KAAAsG,OAAA,CAAApG,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAuG,wBAAA,CAAAzG,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA8G,gBAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA+G,cAAA,CAAA/G,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAiH,eAAAhH,CAAA,QAAAS,CAAA,GAAAwG,YAAA,CAAAjH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAwG,aAAAjH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAwG,WAAA,kBAAAnH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAkH,MAAA,GAAAC,MAAA,EAAApH,CAAA;AAAA,SAAAqH,mBAAAjH,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAgH,kBAAAlH,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA6G,SAAA,aAAArB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAAwH,MAAAnH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,UAAApH,CAAA,cAAAoH,OAAApH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,WAAApH,CAAA,KAAAmH,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,KAAK;AACjG,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,IAAI,EAAEC,wBAAwB,QAAQ,yBAAyB;AACxE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,OAAOC,eAAe,MAAM,kDAAkD;AAC9E,OAAOC,oBAAoB,MAAM,4DAA4D;AAC7F,OAAOC,YAAY,MAAM,4DAA4D;AACrF,OAAOC,mBAAmB,MAAM,yDAAyD;AACzF,OAAOC,YAAY,MAAM,wCAAwC;AACjE,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAOC,eAAe,MAAM,uBAAuB;AAjBnD,IAAAC,WAAA,GAAe;EAAE/D,IAAI,EAAE;AAAgB,CAAC;;;;;IAmBxC,IAAMgE,KAAK,GAAGhB,QAAQ,CAAC,CAAC;IACxB,IAAMiB,KAAK,GAAGhB,QAAQ,CAAC,CAAC;IACxB,IAAMiB,OAAO,GAAGxB,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMyB,WAAW,GAAGzB,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAM0B,MAAM,GAAG1B,GAAG,CAAC2B,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACpD,IAAMC,OAAO,GAAG7B,GAAG,CAAC,CAAC;IACrB,IAAM8B,eAAe,GAAG9B,GAAG,CAAC,IAAI,CAAC;IACjC,IAAM+B,YAAY,GAAG/B,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMgC,gBAAgB,GAAGhC,GAAG,CAAC,IAAI,CAAC;IAClC,IAAMiC,IAAI,GAAGlC,QAAQ,CAAC;MACpBmC,gBAAgB,EAAE,cAAc;MAChCC,KAAK,EAAE,EAAE;MAAE;MACXC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,CAAC;MACjBC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE,UAAU;MAC3BC,iBAAiB,EAAE,GAAG;MACtBC,aAAa,EAAE,GAAG;MAClBC,iBAAiB,EAAE,GAAG;MACtBC,iBAAiB,EAAE,GAAG;MACtBC,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACF,IAAMC,KAAK,GAAGtD,QAAQ,CAAC;MACrBmC,gBAAgB,EAAE,CAAC;QAAEoB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACzFrB,KAAK,EAAE,CAAC;QAAEmB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC5EX,OAAO,EAAE,CAAC;QAAES,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC9EpB,aAAa,EAAE,CAAC;QAAEkB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACnFd,YAAY,EAAE,CAAC;QAAEY,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACtFb,cAAc,EAAE,CAAC;QAAEW,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACvFZ,SAAS,EAAE,CAAC;QAAE5I,IAAI,EAAE,OAAO;QAAEsJ,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,UAAU;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC;IAClG,CAAC,CAAC;IAEF,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAACxK,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAIqL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9B/I,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAACgJ,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,cAAc,GAAG;MACrBC,iBAAiB,EAAE;QACjBC,KAAK,EAAE;UACL,YAAY,EAAE,SAAS;UACvB,aAAa,EAAE,KAAK;UACpB,aAAa,EAAE,MAAM;UACrB,WAAW,EAAE,MAAM;UACnB,aAAa,EAAE;QACjB,CAAC;QACDC,SAAS,EAAE;UACTC,IAAI,EAAE;YACJ,YAAY,EAAE,SAAS;YACvB,aAAa,EAAE,KAAK;YACpB,aAAa,EAAE,MAAM;YACrB,WAAW,EAAE,MAAM;YACnB,aAAa,EAAE;UACjB;QACF;MACF,CAAC;MACDC,WAAW,EAAE,KAAK;MAClBC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGC,MAAM,EAAEC,IAAI,EAAK;QACnClE,QAAQ,CAAC,YAAM;UACbkE,IAAI,CAACC,MAAM,CAACC,WAAW,CAAC,aAAa,CAAC;UACtCpE,QAAQ,CAAC,YAAM;YACbkE,IAAI,CAACC,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,CAAC;UAClC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACDC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAGC,MAAM,EAAK;QAChCxE,QAAQ,CAAC,YAAM;UACbwE,MAAM,CAACJ,WAAW,CAAC,aAAa,CAAC;UACjCpE,QAAQ,CAAC,YAAM;YACbwE,MAAM,CAACH,SAAS,CAACC,QAAQ,CAAC,CAAC;UAC7B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAMG,kBAAkB,GAAG7E,GAAG,CAAC,EAAE,CAAC;IAClC,IAAM8E,oBAAoB,GAAG9E,GAAG,CAAC,IAAI,CAAC;IACtC,IAAM+E,iBAAiB,GAAG/E,GAAG,CAAC,CAAC,CAAC;IAChC,IAAMgF,UAAU,GAAGhF,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMiF,YAAY,GAAGjF,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAMkF,QAAQ,GAAGlF,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMmF,cAAc,GAAGnF,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMoF,mBAAmB,GAAGpF,GAAG,CAAC,EAAE,CAAC;IACnC,IAAMqF,qBAAqB,GAAGrF,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMsF,qBAAqB,GAAGtF,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMuF,qBAAqB,GAAGvF,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMwF,iBAAiB,GAAGxF,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMyF,qBAAqB,GAAGzF,GAAG,CAAC,EAAE,CAAC;IACrC,IAAM8C,eAAe,GAAG9C,GAAG,CAAC,EAAE,CAAC;IAC/B,IAAM+C,iBAAiB,GAAG/C,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMiD,iBAAiB,GAAGjD,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMkD,iBAAiB,GAAGlD,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMgD,aAAa,GAAGhD,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMmD,iBAAiB,GAAGnD,GAAG,CAAC,EAAE,CAAC;IACjC,IAAM0F,iBAAiB,GAAG1F,GAAG,CAAC,CAAC;MAAE2F,EAAE,EAAElC,IAAI,CAAC,CAAC;MAAEmC,WAAW,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,cAAc,EAAE;IAAG,CAAC,CAAC,CAAC;IACtG,IAAMC,QAAQ,GAAG/F,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMgG,QAAQ,GAAGhG,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMiG,UAAU,GAAGjG,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMkG,OAAO,GAAGlG,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC;IACpC,IAAMmG,UAAU,GAAGnG,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMoG,SAAS,GAAGpG,GAAG,CAAC,EAAE,CAAC;IAEzB,IAAMqG,IAAI,GAAGrG,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMsG,MAAM,GAAGtG,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMuG,QAAQ,GAAGvG,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMwG,aAAa,GAAGxG,GAAG,CAAC,KAAK,CAAC;IAChC,IAAMyG,UAAU,GAAGzG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAM0G,YAAY,GAAG1G,GAAG,CAAC,CAAC,CAAC,CAAC;IAE5B,IAAM2G,WAAW,GAAG3G,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAM4G,kBAAkB,GAAG5G,GAAG,CAAC,KAAK,CAAC;IAErC,IAAM6G,oBAAoB,GAAG7G,GAAG,CAAC,KAAK,CAAC;IACvC,IAAM8G,mBAAmB,GAAG9G,GAAG,CAAC,EAAE,CAAC;IACnC,IAAM+G,YAAY,GAAG/G,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMgH,iBAAiB,GAAGhH,GAAG,CAAC,KAAK,CAAC;IACpC,IAAMiH,MAAM,GAAGjH,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMkH,kBAAkB,GAAGlH,GAAG,CAAC,KAAK,CAAC;IACrC,IAAImH,MAAM,GAAG,IAAI;IACjB,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,EAAE,EAAEC,KAAK,EAAK;MAC9B,IAAIC,KAAK,GAAG,IAAI;MAChB,OAAO,YAAmB;QAAA,SAAAC,IAAA,GAAAvI,SAAA,CAAA/B,MAAA,EAANoH,IAAI,OAAAmD,KAAA,CAAAD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;UAAJpD,IAAI,CAAAoD,IAAA,IAAAzI,SAAA,CAAAyI,IAAA;QAAA;QACtB,IAAIH,KAAK,EAAEI,YAAY,CAACJ,KAAK,CAAC;QAC9BA,KAAK,GAAGK,UAAU,CAAC,YAAM;UACvBP,EAAE,CAAAtI,KAAA,SAAIuF,IAAI,CAAC;QACb,CAAC,EAAEgD,KAAK,CAAC;MACX,CAAC;IACH,CAAC;IAED,IAAMO,WAAW,GAAGT,QAAQ,CAAC,UAACU,GAAG,EAAK;MACpC,IAAIA,GAAG,CAACC,eAAe,KAAKC,SAAS,IAAI,CAAC,UAAU,CAACC,IAAI,CAACH,GAAG,CAACC,eAAe,CAAC,EAAE;QAC9ED,GAAG,CAACC,eAAe,GAAGD,GAAG,CAACC,eAAe,CAACrE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MAClE;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,IAAMwE,UAAU,GAAGd,QAAQ,CAAC,UAACU,GAAG,EAAK;MACnC,IAAIK,WAAW,GAAG,CAAC;MACnB,IAAIC,aAAa,GAAG,CAAC;MACrBtB,mBAAmB,CAACjO,KAAK,CAACoC,OAAO,CAAC,UAAAoN,IAAI,EAAI;QACxC,IAAIA,IAAI,CAACN,eAAe,IAAI,CAAC9K,KAAK,CAACoL,IAAI,CAACN,eAAe,CAAC,EAAE;UACxDI,WAAW,IAAIG,QAAQ,CAACD,IAAI,CAACN,eAAe,EAAE,EAAE,CAAC;QACnD,CAAC,MAAM;UACLK,aAAa,EAAE;QACjB;MACF,CAAC,CAAC;MAEF,IAAID,WAAW,GAAG,GAAG,EAAE;QACrBpB,YAAY,CAAClO,KAAK,GAAG,IAAI;QACzBmO,iBAAiB,CAACnO,KAAK,GAAG,KAAK;QAC/BiP,GAAG,CAACC,eAAe,GAAG,EAAE;QACxB;MACF;MAEA,IAAIK,aAAa,GAAG,CAAC,EAAE;QACrB,IAAMG,SAAS,GAAG,GAAG,GAAGJ,WAAW;QACnC,IAAII,SAAS,GAAG,CAAC,EAAE;UACjBxB,YAAY,CAAClO,KAAK,GAAG,IAAI;UACzBmO,iBAAiB,CAACnO,KAAK,GAAG,KAAK;UAC/BiP,GAAG,CAACC,eAAe,GAAG,EAAE;UACxB;QACF;QACA,IAAIK,aAAa,KAAK,CAAC,EAAE;UACvBtB,mBAAmB,CAACjO,KAAK,CAACoC,OAAO,CAAC,UAAAoN,IAAI,EAAI;YACxC,IAAI,CAACA,IAAI,CAACN,eAAe,IAAI9K,KAAK,CAACoL,IAAI,CAACN,eAAe,CAAC,EAAE;cACxDM,IAAI,CAACN,eAAe,GAAGQ,SAAS,CAAC1E,QAAQ,CAAC,CAAC;YAC7C;UACF,CAAC,CAAC;QACJ;MACF;MAEA,IAAIsE,WAAW,GAAG,GAAG,IAAIC,aAAa,KAAK,CAAC,EAAE;QAC5CpB,iBAAiB,CAACnO,KAAK,GAAG,IAAI;QAC9BkO,YAAY,CAAClO,KAAK,GAAG,KAAK;MAC5B,CAAC,MAAM;QACLmO,iBAAiB,CAACnO,KAAK,GAAG,KAAK;MACjC;MAEA,IAAIsP,WAAW,KAAK,GAAG,IAAIC,aAAa,KAAK,CAAC,EAAE;QAC9CrB,YAAY,CAAClO,KAAK,GAAG,KAAK;QAC1BmO,iBAAiB,CAACnO,KAAK,GAAG,KAAK;MACjC;IACF,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA;IACA;;IAEAoH,WAAW,CAAC,YAAM;MAChBS,YAAY,CAAC8H,cAAc,CAAC;QAAEC,UAAU,EAAE;MAA4B,CAAC,CAAC;MACxE,IAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACjH,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE;MAC7E,IAAI8G,YAAY,EAAE;QAChBhI,YAAY,CAAC8H,cAAc,CAAC;UAC1BK,YAAY,EAAE;YACZC,YAAY,EAAE,IAAI;YAClBC,UAAU,EAAEL,YAAY,CAACxD,QAAQ;YACjC8D,YAAY,EAAE;cAAEC,IAAI,EAAEP,YAAY,CAACQ,MAAM;cAAEC,KAAK,EAAE;gBAAEC,MAAM,EAAE;cAAI;YAAE,CAAC;YACnEC,iBAAiB,EAAEX,YAAY,CAACY;UAClC;QACF,CAAC,CAAC;QACF3H,cAAc,CAAC4H,OAAO,CAAC,cAAc,EAAEZ,IAAI,CAACa,SAAS,CAAC,EAAE,CAAC,CAAC;QAC1DrC,MAAM,GAAGS,UAAU,CAAC,YAAM;UACxBlH,YAAY,CAAC8H,cAAc,CAAC;YAC1BK,YAAY,EAAE;cACZC,YAAY,EAAE,IAAI;cAClBC,UAAU,EAAEL,YAAY,CAACxD,QAAQ;cACjC8D,YAAY,EAAE,CAAC;YACjB;UACF,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV;MACA5C,SAAS,CAACvN,KAAK,GAAGyI,KAAK,CAACmI,KAAK,CAACzP,IAAI;MAClC,IAAIsH,KAAK,CAACmI,KAAK,CAACC,UAAU,EAAE;QAC1BC,gBAAgB,CAAC,CAAC;MACpB;MACAC,gBAAgB,CAAC,CAAC;MAClBC,eAAe,CAAC,CAAC;MACjBC,cAAc,CAAC,CAAC;MAChBC,kBAAkB,CAAC,CAAC;MACpBC,eAAe,CAAC,CAAC;MACjB,IAAI5D,SAAS,CAACvN,KAAK,KAAK,OAAO,IAAIyI,KAAK,CAACmI,KAAK,CAACQ,MAAM,EAAE;QACrDlE,QAAQ,CAAClN,KAAK,GAAG,IAAI;QACrBmN,QAAQ,CAACnN,KAAK,GAAG,IAAI;MACvB;MACA,IAAIyI,KAAK,CAACmI,KAAK,CAAC9D,EAAE,IAAIrE,KAAK,CAACmI,KAAK,CAACQ,MAAM,EAAE;QACxClE,QAAQ,CAAClN,KAAK,GAAG,IAAI;QACrBqR,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLhE,OAAO,CAACrN,KAAK,GAAG,CAAC,aAAa,CAAC;QAC/B,IAAI2H,IAAI,CAAC3H,KAAK,CAACsR,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UAC3DrE,QAAQ,CAAClN,KAAK,GAAG,IAAI;QACvB;QACA,IAAI2H,IAAI,CAAC3H,KAAK,CAACsR,eAAe,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;UACvDrE,QAAQ,CAAClN,KAAK,GAAG,IAAI;UACrBmN,QAAQ,CAACnN,KAAK,GAAG,IAAI;UACrBoJ,IAAI,CAACG,aAAa,GAAG5B,IAAI,CAAC3H,KAAK,CAAC8M,EAAE;UAClC0E,eAAe,CAAC7J,IAAI,CAAC3H,KAAK,CAAC8M,EAAE,CAAC;QAChC,CAAC,MAAM;UACL,IAAInF,IAAI,CAAC3H,KAAK,CAACsR,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC3DnI,IAAI,CAACC,gBAAgB,GAAG,MAAM;UAChC;QACF;QACA,IACE1B,IAAI,CAAC3H,KAAK,CAACsR,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,IACvD5J,IAAI,CAAC3H,KAAK,CAACsR,eAAe,CAACC,QAAQ,CAAC,cAAc,CAAC,EACnD;UACArE,QAAQ,CAAClN,KAAK,GAAG,KAAK;QACxB;QACA,IAAI2H,IAAI,CAAC3H,KAAK,CAACsR,eAAe,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;UAChDnI,IAAI,CAACC,gBAAgB,GAAG,cAAc;UACtC6D,QAAQ,CAAClN,KAAK,GAAG,KAAK;UACtBmN,QAAQ,CAACnN,KAAK,GAAG,KAAK;UACtByR,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,MAAM;UACL,IAAI9J,IAAI,CAAC3H,KAAK,CAACsR,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC3DE,gBAAgB,CAAC;cAAEC,YAAY,EAAE;YAAE,CAAC,CAAC;UACvC,CAAC,MAAM;YACLD,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACtB;QACF;QACAE,gBAAgB,CAAC,CAAC;MACpB;MACA,IAAI,CAAClJ,KAAK,CAACmI,KAAK,CAAC9D,EAAE,EAAE;QACnB7D,eAAe,CAACjJ,KAAK,GAAG,IAAI;QAC5BmJ,gBAAgB,CAACnJ,KAAK,GAAG,IAAI;QAC7BqO,kBAAkB,CAACrO,KAAK,GAAG,KAAK;MAClC;IACF,CAAC,CAAC;IACFqH,aAAa,CAAC,YAAM;MAClB,IAAIiH,MAAM,EAAE;QACVQ,YAAY,CAACR,MAAM,CAAC;QACpBA,MAAM,GAAG,IAAI;MACf;MACAzG,YAAY,CAAC8H,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC;MACxD/H,YAAY,CAAC8H,cAAc,CAAC;QAC1BK,YAAY,EAAE;UACZC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,EAAE;UACdC,YAAY,EAAE,CAAC;QACjB;MACF,CAAC,CAAC;MACF;MACA;IACF,CAAC,CAAC;IACF7I,eAAe,CAAC,YAAM;MACpB,IAAIgH,MAAM,EAAE;QACVQ,YAAY,CAACR,MAAM,CAAC;QACpBA,MAAM,GAAG,IAAI;MACf;MACAzG,YAAY,CAAC8H,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC;MACxD/H,YAAY,CAAC8H,cAAc,CAAC;QAC1BK,YAAY,EAAE;UACZC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,EAAE;UACdC,YAAY,EAAE,CAAC;QACjB;MACF,CAAC,CAAC;MACF;MACA;IACF,CAAC,CAAC;IAEF,IAAMyB,yBAAyB;MAAA,IAAAC,KAAA,GAAA/K,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAoN,SAAA;QAAA,IAAAC,UAAA,EAAAC,GAAA,EAAAC,OAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,IAAA,EAAAC,iBAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,WAAA;QAAA,OAAAlT,mBAAA,GAAAuB,IAAA,UAAA4R,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvN,IAAA,GAAAuN,SAAA,CAAAlP,IAAA;YAAA;cAAA,KAC5B0F,YAAY,CAAClJ,KAAK;gBAAA0S,SAAA,CAAAlP,IAAA;gBAAA;cAAA;cAAA,OAAAkP,SAAA,CAAAtP,MAAA;YAAA;cAIhB2O,UAAU,GAAG3I,IAAI,CAACC,gBAAgB,KAAK,cAAc,GAAGD,IAAI,CAACG,aAAa,GAAGH,IAAI,CAACI,YAAY;cAAA,IAC/FuI,UAAU;gBAAAW,SAAA,CAAAlP,IAAA;gBAAA;cAAA;cACb4K,MAAM,CAACpO,KAAK,GAAG,KAAK;cAAA,OAAA0S,SAAA,CAAAtP,MAAA;YAAA;cAAA,MAIlBqF,KAAK,CAACmI,KAAK,CAAC9D,EAAE,IAAI,CAACuB,kBAAkB,CAACrO,KAAK;gBAAA0S,SAAA,CAAAlP,IAAA;gBAAA;cAAA;cAAAkP,SAAA,CAAAvN,IAAA;cAE3C+D,YAAY,CAAClJ,KAAK,GAAG,IAAI;cAAA0S,SAAA,CAAAlP,IAAA;cAAA,OACPyD,GAAG,CAAC0L,UAAU,CAAC,+BAA+B,EAAE;gBAChEC,QAAQ,EAAEnK,KAAK,CAACmI,KAAK,CAAC9D;cACxB,CAAC,CAAC;YAAA;cAFIkF,GAAG,GAAAU,SAAA,CAAAzP,IAAA;cAAA,MAGL+O,GAAG,CAACI,IAAI,IAAIJ,GAAG,CAACI,IAAI,CAAC/N,MAAM,GAAG,CAAC;gBAAAqO,SAAA,CAAAlP,IAAA;gBAAA;cAAA;cACjC2F,gBAAgB,CAACnJ,KAAK,GAAGgS,GAAG,CAACI,IAAI;cACjCnJ,eAAe,CAACjJ,KAAK,GAAGgS,GAAG,CAACI,IAAI;cAChCnE,mBAAmB,CAACjO,KAAK,GAAGgS,GAAG,CAACI,IAAI;cACpChE,MAAM,CAACpO,KAAK,GAAG,IAAI;cACnBqO,kBAAkB,CAACrO,KAAK,GAAG,IAAI;cAAA,OAAA0S,SAAA,CAAAtP,MAAA;YAAA;cAAAsP,SAAA,CAAAlP,IAAA;cAAA;YAAA;cAAAkP,SAAA,CAAAvN,IAAA;cAAAuN,SAAA,CAAAG,EAAA,GAAAH,SAAA;cAIjC,IAAIA,SAAA,CAAAG,EAAA,CAAMC,IAAI,KAAK,cAAc,EAAE;gBACjCC,OAAO,CAACC,KAAK,CAAC,aAAa,EAAAN,SAAA,CAAAG,EAAO,CAAC;cACrC;YAAC;cAAAH,SAAA,CAAAvN,IAAA;cAED+D,YAAY,CAAClJ,KAAK,GAAG,KAAK;cAAA,OAAA0S,SAAA,CAAAhN,MAAA;YAAA;cAAA,MAI1BuI,mBAAmB,CAACjO,KAAK,CAACqE,MAAM,GAAG,CAAC,IAAIgK,kBAAkB,CAACrO,KAAK;gBAAA0S,SAAA,CAAAlP,IAAA;gBAAA;cAAA;cAAA,OAAAkP,SAAA,CAAAtP,MAAA;YAAA;cAIhE6O,OAAO,GAAG,EAAE;cAEVC,gBAAgB,GAAGjE,mBAAmB,CAACjO,KAAK,CAACiT,IAAI,CAAC,UAAAC,MAAM;gBAAA,OAAIA,MAAM,CAACpG,EAAE,KAAKiF,UAAU;cAAA,EAAC;cAAA,KACvFG,gBAAgB;gBAAAQ,SAAA,CAAAlP,IAAA;gBAAA;cAAA;cAClByO,OAAO,CAACjO,IAAI,CAAAmC,aAAA,CAAAA,aAAA,KACP+L,gBAAgB;gBACnBhD,eAAe,EAAEgD,gBAAgB,CAAChD,eAAe,IAAI;cAAE,EACxD,CAAC;cAAAwD,SAAA,CAAAlP,IAAA;cAAA;YAAA;cAAAkP,SAAA,CAAAvN,IAAA;cAGA+D,YAAY,CAAClJ,KAAK,GAAG,IAAI;cAAA0S,SAAA,CAAAlP,IAAA;cAAA,OACFyD,GAAG,CAACuK,eAAe,CAAC;gBAAEoB,QAAQ,EAAEb;cAAW,CAAC,CAAC;YAAA;cAAAI,qBAAA,GAAAO,SAAA,CAAAzP,IAAA;cAA5DmP,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZH,OAAO,CAACjO,IAAI,CAAC;gBACX8I,EAAE,EAAEiF,UAAU;gBACdoB,MAAM,EAAEpB,UAAU;gBAClBqB,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ;gBACvBlE,eAAe,EAAE;cACnB,CAAC,CAAC;cAAAwD,SAAA,CAAAlP,IAAA;cAAA;YAAA;cAAAkP,SAAA,CAAAvN,IAAA;cAAAuN,SAAA,CAAAW,EAAA,GAAAX,SAAA;cAEFK,OAAO,CAACC,KAAK,CAAC,YAAY,EAAAN,SAAA,CAAAW,EAAO,CAAC;YAAA;cAAAX,SAAA,CAAAvN,IAAA;cAElC+D,YAAY,CAAClJ,KAAK,GAAG,KAAK;cAAA,OAAA0S,SAAA,CAAAhN,MAAA;YAAA;cAAA,MAI1B0D,IAAI,CAACW,SAAS,IAAIX,IAAI,CAACW,SAAS,CAAC1F,MAAM,GAAG,CAAC;gBAAAqO,SAAA,CAAAlP,IAAA;gBAAA;cAAA;cACvC6O,iBAAiB,GAAGpE,mBAAmB,CAACjO,KAAK,CAACgG,MAAM,CAAC,UAAAkN,MAAM;gBAAA,OAC/D9J,IAAI,CAACW,SAAS,CAACwH,QAAQ,CAAC2B,MAAM,CAACpG,EAAE,CAAC;cAAA,CACpC,CAAC;cAEKwF,cAAc,GAAGlJ,IAAI,CAACW,SAAS,CAAC/D,MAAM,CAAC,UAAA8G,EAAE;gBAAA,OAC7C,CAACuF,iBAAiB,CAACiB,IAAI,CAAC,UAAA3L,IAAI;kBAAA,OAAIA,IAAI,CAACmF,EAAE,KAAKA,EAAE;gBAAA,EAAC;cAAA,CACjD,CAAC;cAAA,MAEGwF,cAAc,CAACjO,MAAM,GAAG,CAAC;gBAAAqO,SAAA,CAAAlP,IAAA;gBAAA;cAAA;cAAAkP,SAAA,CAAAvN,IAAA;cAEzB+D,YAAY,CAAClJ,KAAK,GAAG,IAAI;cAAA0S,SAAA,CAAAlP,IAAA;cAAA,OACMuB,OAAO,CAACwO,GAAG,CACxCjB,cAAc,CAACkB,GAAG;gBAAA,IAAAC,KAAA,GAAA3M,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAC,SAAAgP,QAAOP,MAAM;kBAAA,IAAAQ,sBAAA,EAAAvB,IAAA;kBAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAA+S,SAAAC,QAAA;oBAAA,kBAAAA,QAAA,CAAA1O,IAAA,GAAA0O,QAAA,CAAArQ,IAAA;sBAAA;wBAAAqQ,QAAA,CAAArQ,IAAA;wBAAA,OACPyD,GAAG,CAACuK,eAAe,CAAC;0BAAEoB,QAAQ,EAAEO;wBAAO,CAAC,CAAC;sBAAA;wBAAAQ,sBAAA,GAAAE,QAAA,CAAA5Q,IAAA;wBAAxDmP,IAAI,GAAAuB,sBAAA,CAAJvB,IAAI;wBAAA,OAAAyB,QAAA,CAAAzQ,MAAA,WACL;0BACL0J,EAAE,EAAEqG,MAAM;0BACVA,MAAM,EAAEA,MAAM;0BACdC,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ;0BACvBlE,eAAe,EAAE;wBACnB,CAAC;sBAAA;sBAAA;wBAAA,OAAA2E,QAAA,CAAAvO,IAAA;oBAAA;kBAAA,GAAAoO,OAAA;gBAAA,CACF;gBAAA,iBAAAI,EAAA;kBAAA,OAAAL,KAAA,CAAAvN,KAAA,OAAAE,SAAA;gBAAA;cAAA,IACH,CAAC;YAAA;cAVKmM,gBAAgB,GAAAG,SAAA,CAAAzP,IAAA;cAWtBgP,OAAO,MAAA8B,MAAA,CAAAC,kBAAA,CAAO/B,OAAO,GAAA+B,kBAAA,CAAK3B,iBAAiB,GAAA2B,kBAAA,CAAKzB,gBAAgB,EAAC;cAAAG,SAAA,CAAAlP,IAAA;cAAA;YAAA;cAAAkP,SAAA,CAAAvN,IAAA;cAAAuN,SAAA,CAAAuB,EAAA,GAAAvB,SAAA;cAEjEK,OAAO,CAACC,KAAK,CAAC,YAAY,EAAAN,SAAA,CAAAuB,EAAO,CAAC;YAAA;cAAAvB,SAAA,CAAAvN,IAAA;cAElC+D,YAAY,CAAClJ,KAAK,GAAG,KAAK;cAAA,OAAA0S,SAAA,CAAAhN,MAAA;YAAA;cAAAgN,SAAA,CAAAlP,IAAA;cAAA;YAAA;cAG5ByO,OAAO,MAAA8B,MAAA,CAAAC,kBAAA,CAAO/B,OAAO,GAAA+B,kBAAA,CAAK3B,iBAAiB,EAAC;YAAA;cAI1CG,WAAW,GAAGP,OAAO,CAACuB,GAAG,CAAC,UAAAU,OAAO,EAAI;gBAAA,IAAAC,qBAAA;gBACzC,IAAMC,WAAW,IAAAD,qBAAA,GAAGhL,gBAAgB,CAACnJ,KAAK,cAAAmU,qBAAA,uBAAtBA,qBAAA,CAAwBlB,IAAI,CAAC,UAAAzD,IAAI;kBAAA,OAAIA,IAAI,CAAC1C,EAAE,KAAKoH,OAAO,CAACpH,EAAE;gBAAA,EAAC;gBAChF,IAAMuH,YAAY,GAAGpG,mBAAmB,CAACjO,KAAK,CAACiT,IAAI,CAAC,UAAAzD,IAAI;kBAAA,OAAIA,IAAI,CAAC1C,EAAE,KAAKoH,OAAO,CAACpH,EAAE;gBAAA,EAAC;gBACnF,OAAA3G,aAAA,CAAAA,aAAA,KACK+N,OAAO;kBACVhF,eAAe,EAAE,CAAAkF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAElF,eAAe,MAAImF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEnF,eAAe,KAAIgF,OAAO,CAAChF;gBAAe;cAE7G,CAAC,CAAC;cAEFjB,mBAAmB,CAACjO,KAAK,GAAGwS,WAAW;cACvCpE,MAAM,CAACpO,KAAK,GAAG,IAAI;cACnBqO,kBAAkB,CAACrO,KAAK,GAAG,IAAI;YAAA;YAAA;cAAA,OAAA0S,SAAA,CAAApN,IAAA;UAAA;QAAA,GAAAwM,QAAA;MAAA,CAChC;MAAA,gBA7GKF,yBAAyBA,CAAA;QAAA,OAAAC,KAAA,CAAA3L,KAAA,OAAAE,SAAA;MAAA;IAAA,GA6G9B;IAED,IAAMkO,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B,IAAI,CAAClL,IAAI,CAACY,OAAO,EAAE,OAAOlC,SAAS,CAAC;QAAE3G,IAAI,EAAE,SAAS;QAAEuJ,OAAO,EAAE;MAAoB,CAAC,CAAC;MACtF6J,cAAc,CAAC;QAAEC,GAAG,EAAE,CAAC/L,KAAK,CAACmI,KAAK,CAAC9D,EAAE;MAAE,CAAC,CAAC;IAC3C,CAAC;IACD,IAAM2H,kBAAkB;MAAA,IAAAC,KAAA,GAAA5N,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAiQ,SAAA;QAAA,OAAArV,mBAAA,GAAAuB,IAAA,UAAA+T,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1P,IAAA,GAAA0P,SAAA,CAAArR,IAAA;YAAA;cAAA,IACpB4F,IAAI,CAACY,OAAO;gBAAA6K,SAAA,CAAArR,IAAA;gBAAA;cAAA;cAAA,OAAAqR,SAAA,CAAAzR,MAAA,WAAS0E,SAAS,CAAC;gBAAE3G,IAAI,EAAE,SAAS;gBAAEuJ,OAAO,EAAE;cAAoB,CAAC,CAAC;YAAA;cACtFoD,WAAW,CAAC9N,KAAK,GAAG;gBAAEwU,GAAG,EAAE,CAAC/L,KAAK,CAACmI,KAAK,CAAC9D,EAAE;cAAE,CAAC;cAC7CiB,kBAAkB,CAAC/N,KAAK,GAAG,IAAI;YAAA;YAAA;cAAA,OAAA6U,SAAA,CAAAvP,IAAA;UAAA;QAAA,GAAAqP,QAAA;MAAA,CAChC;MAAA,gBAJKF,kBAAkBA,CAAA;QAAA,OAAAC,KAAA,CAAAxO,KAAA,OAAAE,SAAA;MAAA;IAAA,GAIvB;IACD,IAAM0O,QAAQ,GAAG,SAAXA,QAAQA,CAAI3T,IAAI,EAAK;MACzB4M,kBAAkB,CAAC/N,KAAK,GAAG,KAAK;MAChC,IAAImB,IAAI,EAAE;QACR0G,YAAY,CAAC8H,cAAc,CAAC;UAAEoF,cAAc,EAAE;YAAEC,MAAM,EAAEvM,KAAK,CAACmI,KAAK,CAACqE,UAAU;YAAEC,OAAO,EAAEzM,KAAK,CAACmI,KAAK,CAACuE;UAAQ;QAAE,CAAC,CAAC;MACnH;IACF,CAAC;IACD,IAAMZ,cAAc;MAAA,IAAAa,KAAA,GAAAtO,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA2Q,SAAOC,MAAM;QAAA,IAAAC,qBAAA,EAAAnD,IAAA,EAAAoD,QAAA,EAAAC,KAAA;QAAA,OAAAnW,mBAAA,GAAAuB,IAAA,UAAA6U,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxQ,IAAA,GAAAwQ,SAAA,CAAAnS,IAAA;YAAA;cAAAmS,SAAA,CAAAnS,IAAA;cAAA,OACXyD,GAAG,CAACsN,cAAc,CAACe,MAAM,CAAC;YAAA;cAAAC,qBAAA,GAAAI,SAAA,CAAA1S,IAAA;cAAzCmP,IAAI,GAAAmD,qBAAA,CAAJnD,IAAI;cACZ,IAAIA,IAAI,CAAC/N,MAAM,EAAE;gBACXmR,QAAQ,GAAG,CAAC,CAAC;gBACjB,KAASC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGrD,IAAI,CAAC/N,MAAM,EAAEoR,KAAK,EAAE,EAAE;kBAChDD,QAAQ,GAAGnN,eAAe,CAAC+J,IAAI,CAACqD,KAAK,CAAC,CAAC;gBACzC;gBACAnN,iBAAiB,CAAC;kBAAEwK,IAAI,EAAE,iBAAiB;kBAAErO,IAAI,EAAE+Q,QAAQ,CAACI,OAAO;kBAAEC,GAAG,EAAE,SAAS;kBAAEzD,IAAI,EAAEoD;gBAAS,CAAC,CAAC;cACxG;YAAC;YAAA;cAAA,OAAAG,SAAA,CAAArQ,IAAA;UAAA;QAAA,GAAA+P,QAAA;MAAA,CACF;MAAA,gBATKd,cAAcA,CAAAuB,GAAA;QAAA,OAAAV,KAAA,CAAAlP,KAAA,OAAAE,SAAA;MAAA;IAAA,GASnB;IACD,IAAM2P,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,MAAM,EAAK;MACnC,IAAI,CAAC5M,IAAI,CAACY,OAAO,EAAE,OAAOlC,SAAS,CAAC;QAAE3G,IAAI,EAAE,SAAS;QAAEuJ,OAAO,EAAE;MAAkB,CAAC,CAAC;MACpF5B,cAAc,CAAC4H,OAAO,CAAC,oBAAoB,EAAEtH,IAAI,CAACE,KAAK,CAAC;MACxDR,cAAc,CAAC4H,OAAO,CAAC,sBAAsB,EAAEtH,IAAI,CAACY,OAAO,CAAC;MAC5DyD,MAAM,CAACzN,KAAK,GAAGgW,MAAM;MACrBxI,IAAI,CAACxN,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMiW,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAI9U,IAAI,EAAK;MACzC,IAAIA,IAAI,EAAEwR,UAAU,CAAC,CAAC,CAAC;MACvBnF,IAAI,CAACxN,KAAK,GAAG,KAAK;IACpB,CAAC;IACD,IAAMkW,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9BtI,UAAU,CAAC5N,KAAK,GAAG;QAAEmW,QAAQ,EAAE/M,IAAI,CAACG,aAAa;QAAES,OAAO,EAAEZ,IAAI,CAACY;MAAQ,CAAC;IAC5E,CAAC;IACD,IAAMoM,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,UAAU,EAAEC,eAAe,EAAK;MACxD5I,QAAQ,CAAC1N,KAAK,GAAGqW,UAAU;MAC3B1I,aAAa,CAAC3N,KAAK,GAAGsW,eAAe;IACvC,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAI/G,IAAI,EAAK;MAC3B,IAAI,CAACpG,IAAI,CAACW,SAAS,CAACwH,QAAQ,CAAC/B,IAAI,CAAC1C,EAAE,CAAC,EAAE;QACrC1D,IAAI,CAACW,SAAS,MAAAgK,MAAA,CAAAC,kBAAA,CAAO5K,IAAI,CAACW,SAAS,IAAEyF,IAAI,CAAC1C,EAAE,EAAC;MAC/C;IACF,CAAC;IACD,IAAMiE,gBAAgB;MAAA,IAAAyF,KAAA,GAAA1P,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA+R,SAAA;QAAA,IAAAC,qBAAA,EAAAtE,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAA8V,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzR,IAAA,GAAAyR,SAAA,CAAApT,IAAA;YAAA;cAAAoT,SAAA,CAAApT,IAAA;cAAA,OACAyD,GAAG,CAAC8J,gBAAgB,CAAC;gBAC1C8F,KAAK,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,mBAAmB;cAC3E,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAA3T,IAAA;cAFMmP,IAAI,GAAAsE,qBAAA,CAAJtE,IAAI;cAGZ,IAAIA,IAAI,CAACpG,kBAAkB,EAAE;gBAC3BA,kBAAkB,CAAChM,KAAK,GAAG4G,MAAM,CAACwL,IAAI,CAACpG,kBAAkB,CAAC;cAC5D;cACA,IAAIoG,IAAI,CAACnG,oBAAoB,EAAE;gBAC7BA,oBAAoB,CAACjM,KAAK,GAAG4G,MAAM,CAACwL,IAAI,CAACnG,oBAAoB,CAAC;cAChE;cACA,IAAImG,IAAI,CAAClG,iBAAiB,EAAE;gBAC1BA,iBAAiB,CAAClM,KAAK,GAAG4G,MAAM,CAACwL,IAAI,CAAClG,iBAAiB,CAAC;cAC1D;YAAC;YAAA;cAAA,OAAA0K,SAAA,CAAAtR,IAAA;UAAA;QAAA,GAAAmR,QAAA;MAAA,CACF;MAAA,gBAbK1F,gBAAgBA,CAAA;QAAA,OAAAyF,KAAA,CAAAtQ,KAAA,OAAAE,SAAA;MAAA;IAAA,GAarB;IACD,IAAM+K,eAAe;MAAA,IAAA2F,KAAA,GAAAhQ,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAqS,SAAA;QAAA,IAAAC,qBAAA,EAAA5E,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAAoW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/R,IAAA,GAAA+R,SAAA,CAAA1T,IAAA;YAAA;cAAA0T,SAAA,CAAA1T,IAAA;cAAA,OACCyD,GAAG,CAACkK,eAAe,CAAC,CAAC;YAAA;cAAA6F,qBAAA,GAAAE,SAAA,CAAAjU,IAAA;cAApCmP,IAAI,GAAA4E,qBAAA,CAAJ5E,IAAI;cACZvE,YAAY,CAAC7N,KAAK,GAAGoS,IAAI,CAAC/N,MAAM,GAAG+N,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAAA;YAAA;cAAA,OAAA8E,SAAA,CAAA5R,IAAA;UAAA;QAAA,GAAAyR,QAAA;MAAA,CAChD;MAAA,gBAHK5F,eAAeA,CAAA;QAAA,OAAA2F,KAAA,CAAA5Q,KAAA,OAAAE,SAAA;MAAA;IAAA,GAGpB;IACD,IAAM4K,eAAe;MAAA,IAAAmG,KAAA,GAAArQ,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA0S,SAAA;QAAA,IAAAC,qBAAA,EAAAjF,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAAyW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApS,IAAA,GAAAoS,SAAA,CAAA/T,IAAA;YAAA;cAAA+T,SAAA,CAAA/T,IAAA;cAAA,OACCyD,GAAG,CAAC+J,eAAe,CAAC;gBAAEwG,YAAY,EAAE;cAAe,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAAtU,IAAA;cAApEmP,IAAI,GAAAiF,qBAAA,CAAJjF,IAAI;cACZjG,UAAU,CAACnM,KAAK,GAAGoS,IAAI,CAACtF,EAAE;YAAA;YAAA;cAAA,OAAAyK,SAAA,CAAAjS,IAAA;UAAA;QAAA,GAAA8R,QAAA;MAAA,CAC3B;MAAA,gBAHKpG,eAAeA,CAAA;QAAA,OAAAmG,KAAA,CAAAjR,KAAA,OAAAE,SAAA;MAAA;IAAA,GAGpB;IACD,IAAM6K,cAAc;MAAA,IAAAwG,KAAA,GAAA3Q,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAgT,SAAA;QAAA,IAAAC,qBAAA,EAAAvF,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAA+W,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1S,IAAA,GAAA0S,SAAA,CAAArU,IAAA;YAAA;cAAAqU,SAAA,CAAArU,IAAA;cAAA,OACEyD,GAAG,CAACgK,cAAc,CAAC;gBACxC6G,SAAS,EAAE,CACT,mBAAmB,EACnB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,kBAAkB,EAClB,sBAAsB;cAE1B,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAA5U,IAAA;cATMmP,IAAI,GAAAuF,qBAAA,CAAJvF,IAAI;cAUZnI,eAAe,CAACjK,KAAK,GAAGoS,IAAI,CAAC2F,iBAAiB;cAC9C7N,iBAAiB,CAAClK,KAAK,GAAGoS,IAAI,CAAC4F,mBAAmB;cAClD5N,iBAAiB,CAACpK,KAAK,GAAGoS,IAAI,CAAC6F,oBAAoB;cACnD5N,iBAAiB,CAACrK,KAAK,GAAGoS,IAAI,CAAC8F,oBAAoB;cACnD/N,aAAa,CAACnK,KAAK,GAAGoS,IAAI,CAAC+F,gBAAgB;cAC3C7N,iBAAiB,CAACtK,KAAK,GAAGoS,IAAI,CAACgG,oBAAoB;YAAA;YAAA;cAAA,OAAAP,SAAA,CAAAvS,IAAA;UAAA;QAAA,GAAAoS,QAAA;MAAA,CACpD;MAAA,gBAjBKzG,cAAcA,CAAA;QAAA,OAAAwG,KAAA,CAAAvR,KAAA,OAAAE,SAAA;MAAA;IAAA,GAiBnB;IACD,IAAM8K,kBAAkB;MAAA,IAAAmH,MAAA,GAAAvR,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA4T,SAAA;QAAA,IAAAC,sBAAA,EAAAnG,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAA2X,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtT,IAAA,GAAAsT,SAAA,CAAAjV,IAAA;YAAA;cAAAiV,SAAA,CAAAjV,IAAA;cAAA,OACFyD,GAAG,CAACiK,kBAAkB,CAAC;gBAC5C4G,SAAS,EAAE,CACT,mBAAmB,EACnB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,kBAAkB,EAClB,sBAAsB;cAE1B,CAAC,CAAC;YAAA;cAAAS,sBAAA,GAAAE,SAAA,CAAAxV,IAAA;cATMmP,IAAI,GAAAmG,sBAAA,CAAJnG,IAAI;cAUZ7F,mBAAmB,CAACvM,KAAK,GAAGoS,IAAI,CAAC2F,iBAAiB;cAClDvL,qBAAqB,CAACxM,KAAK,GAAGoS,IAAI,CAAC4F,mBAAmB;cACtDvL,qBAAqB,CAACzM,KAAK,GAAGoS,IAAI,CAAC6F,oBAAoB;cACvDvL,qBAAqB,CAAC1M,KAAK,GAAGoS,IAAI,CAAC8F,oBAAoB;cACvDvL,iBAAiB,CAAC3M,KAAK,GAAGoS,IAAI,CAAC+F,gBAAgB;cAC/CvL,qBAAqB,CAAC5M,KAAK,GAAGoS,IAAI,CAACgG,oBAAoB;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAnT,IAAA;UAAA;QAAA,GAAAgT,QAAA;MAAA,CACxD;MAAA,gBAjBKpH,kBAAkBA,CAAA;QAAA,OAAAmH,MAAA,CAAAnS,KAAA,OAAAE,SAAA;MAAA;IAAA,GAiBvB;IACD,IAAM0K,gBAAgB;MAAA,IAAA4H,MAAA,GAAA5R,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAiU,UAAA;QAAA,IAAAC,qBAAA,EAAAxG,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAAgY,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3T,IAAA,GAAA2T,UAAA,CAAAtV,IAAA;YAAA;cAAAsV,UAAA,CAAAtV,IAAA;cAAA,OACAyD,GAAG,CAAC6J,gBAAgB,CAAC;gBAAE8B,QAAQ,EAAEnK,KAAK,CAACmI,KAAK,CAACC;cAAW,CAAC,CAAC;YAAA;cAAA+H,qBAAA,GAAAE,UAAA,CAAA7V,IAAA;cAAzEmP,IAAI,GAAAwG,qBAAA,CAAJxG,IAAI;cACZhJ,IAAI,CAACE,KAAK,GAAG8I,IAAI,CAAC9I,KAAK;cACvBF,IAAI,CAACY,OAAO,GAAGoI,IAAI,CAACpI,OAAO;YAAA;YAAA;cAAA,OAAA8O,UAAA,CAAAxT,IAAA;UAAA;QAAA,GAAAqT,SAAA;MAAA,CAC5B;MAAA,gBAJK7H,gBAAgBA,CAAA;QAAA,OAAA4H,MAAA,CAAAxS,KAAA,OAAAE,SAAA;MAAA;IAAA,GAIrB;IACD,IAAM2S,MAAM,GAAG5R,GAAG,CAAC,KAAK,CAAC;IACzB,IAAM6R,MAAM,GAAG7R,GAAG,CAAC,CAAC,CAAC,CAAC;IACtB,IAAMkK,cAAc;MAAA,IAAA4H,MAAA,GAAAnS,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAwU,UAAA;QAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAA3H,GAAA,EAAAI,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAA+Y,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1U,IAAA,GAAA0U,UAAA,CAAArW,IAAA;YAAA;cAAAqW,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAArW,IAAA;cAAA,OAEDyD,GAAG,CAACoK,cAAc,CAAC;gBACnCuB,QAAQ,EAAEnK,KAAK,CAACmI,KAAK,CAAC9D,EAAE,IAAIrE,KAAK,CAACmI,KAAK,CAACQ,MAAM;gBAC9C0I,cAAc,EAAEvM,SAAS,CAACvN,KAAK,KAAK,QAAQ,GAAG,CAAC,GAAG;cACrD,CAAC,CAAC;YAAA;cAHIgS,GAAG,GAAA6H,UAAA,CAAA5W,IAAA;cAIHmP,IAAI,GAAKJ,GAAG,CAAZI,IAAI;cACV9E,UAAU,CAACtN,KAAK,GAAG,IAAI;cACvBgZ,MAAM,CAAChZ,KAAK,GAAGoS,IAAI,CAAC4G,MAAM;cAC1BD,MAAM,CAAC/Y,KAAK,GAAGyI,KAAK,CAACmI,KAAK,CAACzP,IAAI,IAAI,QAAQ,IAAIiR,IAAI,CAAC4G,MAAM,CAACD,MAAM,IAAI,CAAC,IAAI3G,IAAI,CAAC4G,MAAM,CAACe,UAAU,IAAIpS,IAAI,CAAC3H,KAAK,CAAC8M,EAAE;cACjH1D,IAAI,CAACC,gBAAgB,GAAG+I,IAAI,CAAC/I,gBAAgB;cAC7C,IAAID,IAAI,CAACC,gBAAgB,KAAK,cAAc,EAAE;gBAC5CgE,OAAO,CAACrN,KAAK,GAAG,CAAC,aAAa,CAAC;gBAC/BoJ,IAAI,CAACG,aAAa,GAAG6I,IAAI,CAAC7I,aAAa;gBACvC,IAAI6I,IAAI,CAAC7I,aAAa,EAAE;kBACtBiI,eAAe,CAACY,IAAI,CAAC7I,aAAa,CAAC;gBACrC;cACF;cACA,IAAIH,IAAI,CAACC,gBAAgB,KAAK,MAAM,EAAE;gBACpC+D,UAAU,CAACpN,KAAK,GAAG,IAAI;gBACvBoJ,IAAI,CAACS,YAAY,GAAGuI,IAAI,CAACvI,YAAY;gBACrCyC,cAAc,CAACtM,KAAK,GAAGoS,IAAI,CAACvI,YAAY,GAAG,CAAC;kBAAEiD,EAAE,EAAEsF,IAAI,CAACvI,YAAY;kBAAEpF,IAAI,EAAE2N,IAAI,CAAC4H;gBAAe,CAAC,CAAC,GAAG,EAAE;cACxG;cACArI,gBAAgB,CAAC,CAAC;cAClBvI,IAAI,CAACE,KAAK,GAAG8I,IAAI,CAAC9I,KAAK;cACvBF,IAAI,CAAC6Q,cAAc,GAAG7H,IAAI,CAAC8H,UAAU;cACrC9Q,IAAI,CAAC+Q,gBAAgB,GAAG/H,IAAI,CAACgI,YAAY;cACzC;cACAhR,IAAI,CAAC+C,UAAU,GAAGiG,IAAI,CAACjG,UAAU;cACjC/C,IAAI,CAACY,OAAO,GAAGoI,IAAI,CAACpI,OAAO,CAACa,OAAO,CAAC,MAAM,EAAE,4FAA4F,CAAC;cACzIqL,iBAAiB,CAAC,CAAC;cACnB9M,IAAI,CAACU,cAAc,GAAGsI,IAAI,CAACtI,cAAc;cACzCuQ,UAAU,CAAC,CAAC;cACZjR,IAAI,CAACa,eAAe,IAAAkP,qBAAA,GAAG/G,IAAI,CAACnI,eAAe,cAAAkP,qBAAA,uBAApBA,qBAAA,CAAsBnZ,KAAK;cAClDoJ,IAAI,CAACc,iBAAiB,IAAAkP,qBAAA,GAAGhH,IAAI,CAAClI,iBAAiB,cAAAkP,qBAAA,uBAAtBA,qBAAA,CAAwBpZ,KAAK;cACtDoJ,IAAI,CAACe,aAAa,IAAAkP,mBAAA,GAAGjH,IAAI,CAACjI,aAAa,cAAAkP,mBAAA,uBAAlBA,mBAAA,CAAoBrZ,KAAK;cAC9CoJ,IAAI,CAACgB,iBAAiB,IAAAkP,qBAAA,GAAGlH,IAAI,CAAChI,iBAAiB,cAAAkP,qBAAA,uBAAtBA,qBAAA,CAAwBtZ,KAAK;cACtDoJ,IAAI,CAACiB,iBAAiB,IAAAkP,qBAAA,GAAGnH,IAAI,CAAC/H,iBAAiB,cAAAkP,qBAAA,uBAAtBA,qBAAA,CAAwBvZ,KAAK;cACtDoJ,IAAI,CAACkB,iBAAiB,IAAAkP,qBAAA,GAAGpH,IAAI,CAAC9H,iBAAiB,cAAAkP,qBAAA,uBAAtBA,qBAAA,CAAwBxZ,KAAK;cACtDoJ,IAAI,CAACI,YAAY,GAAG4I,IAAI,CAACkI,MAAM;cAC/BjO,QAAQ,CAACrM,KAAK,GAAGoS,IAAI,CAACmI,WAAW,IAAI,EAAE;cACvCnR,IAAI,CAACmB,mBAAmB,GAAG,EAAAkP,qBAAA,GAAArH,IAAI,CAAC7H,mBAAmB,cAAAkP,qBAAA,uBAAxBA,qBAAA,CAA0BjG,GAAG,CAAC,UAACxR,CAAC;gBAAA,OAAKA,CAAC,CAACwY,QAAQ;cAAA,EAAC,KAAI,EAAE;cACjFpR,IAAI,CAACW,SAAS,GAAG,EAAA2P,eAAA,GAAAtH,IAAI,CAACrI,SAAS,cAAA2P,eAAA,uBAAdA,eAAA,CAAgBlG,GAAG,CAAC,UAACxR,CAAC;gBAAA,OAAKA,CAAC,CAACmR,MAAM;cAAA,EAAC,KAAI,EAAE;cAC3D,KAAAwG,gBAAA,GAAIvH,IAAI,CAACqI,UAAU,cAAAd,gBAAA,eAAfA,gBAAA,CAAiBtV,MAAM,EAAE;gBAC3BwI,iBAAiB,CAAC7M,KAAK,GAAGoS,IAAI,CAACqI,UAAU,CAACjH,GAAG,CAAC,UAACxR,CAAC;kBAAA,OAAM;oBACpD8K,EAAE,EAAE9K,CAAC,CAAC8K,EAAE;oBACRC,WAAW,EAAE/K,CAAC,CAAC0Y,aAAa;oBAC5B1N,YAAY,EAAEhL,CAAC,CAAC2Y,eAAe;oBAC/B1N,cAAc,EAAEjL,CAAC,CAAC4Y;kBACpB,CAAC;gBAAA,CAAC,CAAC;cACL;cACAhN,UAAU,CAAC5N,KAAK,GAAG;gBAAEmW,QAAQ,EAAE/M,IAAI,CAACG,aAAa;gBAAES,OAAO,EAAEZ,IAAI,CAACY;cAAQ,CAAC;cAAA,MAEtEZ,IAAI,CAACU,cAAc,KAAKV,IAAI,CAACG,aAAa,IAAIH,IAAI,CAACI,YAAY,CAAC;gBAAAqQ,UAAA,CAAArW,IAAA;gBAAA;cAAA;cAClE4K,MAAM,CAACpO,KAAK,GAAG,IAAI;cACnBqO,kBAAkB,CAACrO,KAAK,GAAG,KAAK;cAAA6Z,UAAA,CAAArW,IAAA;cAAA,OAC1BoO,yBAAyB,CAAC,CAAC;YAAA;cAAAiI,UAAA,CAAArW,IAAA;cAAA;YAAA;cAAAqW,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAAhH,EAAA,GAAAgH,UAAA;cAGnC,IAAIA,UAAA,CAAAhH,EAAA,CAAIC,IAAI,KAAK,GAAG,EAAE;gBACpB,IAAIrK,KAAK,CAACmI,KAAK,CAAC9D,EAAE,IAAIS,SAAS,CAACvN,KAAK,KAAK,QAAQ,EAAE;kBAClDsN,UAAU,CAACtN,KAAK,GAAG,KAAK;kBACxB6H,YAAY,CAAC8H,cAAc,CAAC;oBAC1BoF,cAAc,EAAE;sBAAEC,MAAM,EAAEvM,KAAK,CAACmI,KAAK,CAACqE,UAAU;sBAAEC,OAAO,EAAEzM,KAAK,CAACmI,KAAK,CAACuE;oBAAQ;kBACjF,CAAC,CAAC;gBACJ;cACF;YAAC;YAAA;cAAA,OAAA0E,UAAA,CAAAvU,IAAA;UAAA;QAAA,GAAA4T,SAAA;MAAA,CAEJ;MAAA,gBApEK7H,cAAcA,CAAA;QAAA,OAAA4H,MAAA,CAAA/S,KAAA,OAAAE,SAAA;MAAA;IAAA,GAoEnB;IACD,IAAMuL,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B,IAAIvI,IAAI,CAACC,gBAAgB,KAAK,cAAc,EAAE;QAC5CmB,KAAK,CAACjB,aAAa,GAAG,CAAC;UAAEkB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;QAC1FH,KAAK,CAACX,YAAY,GAAG,CAAC;UAAEY,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC/F,CAAC,MAAM,IAAIvB,IAAI,CAACC,gBAAgB,KAAK,MAAM,EAAE;QAC3CmB,KAAK,CAACjB,aAAa,GAAG,CAAC;UAAEkB,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;QAC3FH,KAAK,CAACX,YAAY,GAAG,CAAC;UAAEY,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC9F;MACAvB,IAAI,CAACW,SAAS,GAAG,EAAE;MACnBkE,mBAAmB,CAACjO,KAAK,GAAG,EAAE;MAC9BoO,MAAM,CAACpO,KAAK,GAAG,KAAK;IACtB,CAAC;IACD,IAAMqa,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIjR,IAAI,CAACU,cAAc,EAAE;QACvBU,KAAK,CAACT,SAAS,GAAG,CAAC;UAAE5I,IAAI,EAAE,OAAO;UAAEsJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MACzG,CAAC,MAAM;QACLH,KAAK,CAACT,SAAS,GAAG,CAAC;UAAE5I,IAAI,EAAE,OAAO;UAAEsJ,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;QACxGsD,mBAAmB,CAACjO,KAAK,GAAG,EAAE;QAC9BoO,MAAM,CAACpO,KAAK,GAAG,KAAK;MACtB;IACF,CAAC;IACD,IAAM6a,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,KAAK,EAAK;MACpC1O,YAAY,CAACpM,KAAK,GAAG8a,KAAK;IAC5B,CAAC;IACDtT,KAAK,CAAC;MAAA,OAAM4B,IAAI,CAACG,aAAa;IAAA,GAAE,UAACwR,QAAQ,EAAEC,QAAQ,EAAK;MACtD,IAAID,QAAQ,KAAKC,QAAQ,EAAE;QACzB,IAAMC,OAAO,GAAGhN,mBAAmB,CAACjO,KAAK,CAACgG,MAAM,CAC9C,UAAAkN,MAAM;UAAA,OAAIvM,MAAM,CAACuM,MAAM,CAACpG,EAAE,CAAC,KAAKnG,MAAM,CAACqU,QAAQ,CAAC;QAAA,CAClD,CAAC;QACD/M,mBAAmB,CAACjO,KAAK,GAAGib,OAAO;MACrC;IACF,CACA,CAAC;IACDzT,KAAK,CAAC;MAAA,OAAM4B,IAAI,CAACI,YAAY;IAAA,GAAE,UAACuR,QAAQ,EAAEC,QAAQ,EAAK;MACrD,IAAID,QAAQ,KAAKC,QAAQ,EAAE;QACzB,IAAMC,OAAO,GAAGhN,mBAAmB,CAACjO,KAAK,CAACgG,MAAM,CAC9C,UAAAkN,MAAM;UAAA,OAAIvM,MAAM,CAACuM,MAAM,CAACpG,EAAE,CAAC,KAAKnG,MAAM,CAACqU,QAAQ,CAAC;QAAA,CAClD,CAAC;QACD/M,mBAAmB,CAACjO,KAAK,GAAGib,OAAO;MACrC;IACF,CACA,CAAC;IACD,IAAMC,YAAY;MAAA,IAAAC,MAAA,GAAArU,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA0W,UAAOhJ,IAAI;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAAwa,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnW,IAAA,GAAAmW,UAAA,CAAA9X,IAAA;YAAA;cAAA,KAC1B4O,IAAI;gBAAAkJ,UAAA,CAAA9X,IAAA;gBAAA;cAAA;cACNgO,eAAe,CAACY,IAAI,CAACtF,EAAE,CAAC;cACxB1D,IAAI,CAACW,SAAS,GAAGX,IAAI,CAACW,SAAS,CAAC/D,MAAM,CAAC,UAAChE,CAAC;gBAAA,OAAKA,CAAC,KAAKoQ,IAAI,CAACtF,EAAE;cAAA,EAAC;cAAA,MACxD1D,IAAI,CAACU,cAAc,IAAIV,IAAI,CAACW,SAAS,CAAC1F,MAAM,GAAG,CAAC;gBAAAiX,UAAA,CAAA9X,IAAA;gBAAA;cAAA;cAAA8X,UAAA,CAAA9X,IAAA;cAAA,OAC5CoO,yBAAyB,CAAC,CAAC;YAAA;cAAA0J,UAAA,CAAA9X,IAAA;cAAA;YAAA;cAGnC4F,IAAI,CAACK,UAAU,GAAG,EAAE;cACpBL,IAAI,CAACM,UAAU,GAAG,EAAE;cACpBN,IAAI,CAACO,MAAM,GAAG,EAAE;cAChBP,IAAI,CAACQ,WAAW,GAAG,EAAE;cACrBqE,mBAAmB,CAACjO,KAAK,GAAG,EAAE;cAC9BoO,MAAM,CAACpO,KAAK,GAAG,KAAK;YAAA;cAEtB4N,UAAU,CAAC5N,KAAK,GAAG;gBAAEmW,QAAQ,EAAE/M,IAAI,CAACG,aAAa;gBAAES,OAAO,EAAEZ,IAAI,CAACY;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAsR,UAAA,CAAAhW,IAAA;UAAA;QAAA,GAAA8V,SAAA;MAAA,CAC3E;MAAA,gBAhBKF,YAAYA,CAAAK,GAAA;QAAA,OAAAJ,MAAA,CAAAjV,KAAA,OAAAE,SAAA;MAAA;IAAA,GAgBjB;IACD,IAAMoL,eAAe;MAAA,IAAAgK,MAAA,GAAA1U,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAA+W,UAAOtI,MAAM;QAAA,IAAAuI,gBAAA;QAAA,IAAAC,sBAAA,EAAAvJ,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAA+a,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1W,IAAA,GAAA0W,UAAA,CAAArY,IAAA;YAAA;cAAAqY,UAAA,CAAArY,IAAA;cAAA,OACZyD,GAAG,CAACuK,eAAe,CAAC;gBAAEoB,QAAQ,EAAEO;cAAO,CAAC,CAAC;YAAA;cAAAwI,sBAAA,GAAAE,UAAA,CAAA5Y,IAAA;cAAxDmP,IAAI,GAAAuJ,sBAAA,CAAJvJ,IAAI;cACZhJ,IAAI,CAACK,UAAU,GAAG2I,IAAI,CAAC0J,eAAe;cACtC1S,IAAI,CAACM,UAAU,IAAAgS,gBAAA,GAAGtJ,IAAI,CAAC1I,UAAU,cAAAgS,gBAAA,uBAAfA,gBAAA,CAAiBK,KAAK;cACxC3S,IAAI,CAACO,MAAM,GAAGyI,IAAI,CAACzI,MAAM;cACzBP,IAAI,CAACQ,WAAW,GAAGwI,IAAI,CAACxI,WAAW;YAAA;YAAA;cAAA,OAAAiS,UAAA,CAAAvW,IAAA;UAAA;QAAA,GAAAmW,SAAA;MAAA,CACpC;MAAA,gBANKjK,eAAeA,CAAAwK,GAAA;QAAA,OAAAR,MAAA,CAAAtV,KAAA,OAAAE,SAAA;MAAA;IAAA,GAMpB;IACD,IAAMqL,gBAAgB;MAAA,IAAAwK,MAAA,GAAAnV,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAwX,UAAO5G,MAAM;QAAA,IAAA6G,qBAAA,EAAA/J,IAAA;QAAA,OAAA9S,mBAAA,GAAAuB,IAAA,UAAAub,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlX,IAAA,GAAAkX,UAAA,CAAA7Y,IAAA;YAAA;cAAA6Y,UAAA,CAAA7Y,IAAA;cAAA,OACbyD,GAAG,CAACwK,gBAAgB,CAAC6D,MAAM,CAAC;YAAA;cAAA6G,qBAAA,GAAAE,UAAA,CAAApZ,IAAA;cAA3CmP,IAAI,GAAA+J,qBAAA,CAAJ/J,IAAI;cACZ,IAAIA,IAAI,CAAC/N,MAAM,EAAE;gBACf,IAAIsD,IAAI,CAAC3H,KAAK,CAACsR,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;kBAC3DnE,UAAU,CAACpN,KAAK,GAAG,IAAI;kBACvBoJ,IAAI,CAACS,YAAY,GAAGuI,IAAI,CAAC,CAAC,CAAC,CAACtF,EAAE;gBAChC;cACF;cACAR,cAAc,CAACtM,KAAK,GAAGoS,IAAI;YAAA;YAAA;cAAA,OAAAiK,UAAA,CAAA/W,IAAA;UAAA;QAAA,GAAA4W,SAAA;MAAA,CAC5B;MAAA,gBATKzK,gBAAgBA,CAAA6K,GAAA;QAAA,OAAAL,MAAA,CAAA/V,KAAA,OAAAE,SAAA;MAAA;IAAA,GASrB;IACD,IAAMmW,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B1P,iBAAiB,CAAC7M,KAAK,CAACgE,IAAI,CAAC;QAAE8I,EAAE,EAAElC,IAAI,CAAC,CAAC;QAAEmC,WAAW,EAAE,EAAE;QAAEC,YAAY,EAAE,EAAE;QAAEC,cAAc,EAAE;MAAG,CAAC,CAAC;IACrG,CAAC;IACD,IAAMuP,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI1P,EAAE,EAAK;MAC/BD,iBAAiB,CAAC7M,KAAK,GAAG6M,iBAAiB,CAAC7M,KAAK,CAACgG,MAAM,CAAC,UAAChE,CAAC;QAAA,OAAKA,CAAC,CAAC8K,EAAE,KAAKA,EAAE;MAAA,EAAC;IAC9E,CAAC;IACD,IAAM2P,UAAU;MAAA,IAAAC,MAAA,GAAA5V,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAiY,UAAOC,MAAM,EAAEzb,IAAI,EAAE0b,EAAE;QAAA,OAAAvd,mBAAA,GAAAuB,IAAA,UAAAic,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5X,IAAA,GAAA4X,UAAA,CAAAvZ,IAAA;YAAA;cAAA,IACnCoZ,MAAM;gBAAAG,UAAA,CAAAvZ,IAAA;gBAAA;cAAA;cAAA,OAAAuZ,UAAA,CAAA3Z,MAAA;YAAA;cAAA,MACPgJ,YAAY,CAACpM,KAAK,GAAGiM,oBAAoB,CAACjM,KAAK;gBAAA+c,UAAA,CAAAvZ,IAAA;gBAAA;cAAA;cACjDsE,SAAS,CAAC;gBAAE3G,IAAI,EAAE,SAAS;gBAAEuJ,OAAO,EAAE,eAAeuB,oBAAoB,CAACjM,KAAK;cAAW,CAAC,CAAC;cAAA,OAAA+c,UAAA,CAAA3Z,MAAA;YAAA;cAAA,MAG1FgJ,YAAY,CAACpM,KAAK,GAAG,GAAG,IAAI,CAACuN,SAAS,CAACvN,KAAK;gBAAA+c,UAAA,CAAAvZ,IAAA;gBAAA;cAAA;cAC9CsE,SAAS,CAAC;gBAAE3G,IAAI,EAAE,SAAS;gBAAEuJ,OAAO,EAAE;cAAgB,CAAC,CAAC;cAAA,OAAAqS,UAAA,CAAA3Z,MAAA;YAAA;cAAA2Z,UAAA,CAAAvZ,IAAA;cAAA,OAGpDoZ,MAAM,CAACI,QAAQ,CAAC,UAACC,KAAK,EAAEC,MAAM,EAAK;gBACvC,IAAID,KAAK,EAAE;kBACT,IAAIrV,wBAAwB,CAAC5H,KAAK,IAAI,CAAC6c,EAAE,EAAE;oBACzC,IAAI1b,IAAI,EAAE;sBACRwR,UAAU,CAACxR,IAAI,EAAE0b,EAAE,CAAC;oBACtB,CAAC,MAAM;sBACL9U,YAAY,CAACoV,OAAO,CAAC,wBAAwB,EAAE,IAAI,EAAE;wBACnDC,iBAAiB,EAAE,KAAK;wBACxBC,iBAAiB,EAAE,IAAI;wBACvBC,gBAAgB,EAAE;sBACpB,CAAC,CAAC,CACC5a,IAAI,CAAC,YAAM;wBACVqT,gBAAgB,CAAC,IAAI,CAAC;sBACxB,CAAC,CAAC,CACDpQ,KAAK,CAAC,YAAM;wBACXgN,UAAU,CAACxR,IAAI,EAAE0b,EAAE,CAAC;sBACtB,CAAC,CAAC;oBACN;kBACF,CAAC,MAAM;oBACLlK,UAAU,CAACxR,IAAI,EAAE0b,EAAE,CAAC;kBACtB;gBACF,CAAC,MAAM;kBACL/U,SAAS,CAAC;oBAAE3G,IAAI,EAAE,SAAS;oBAAEuJ,OAAO,EAAE;kBAAiB,CAAC,CAAC;gBAC3D;cACF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAqS,UAAA,CAAAzX,IAAA;UAAA;QAAA,GAAAqX,SAAA;MAAA,CACH;MAAA,gBAnCKF,UAAUA,CAAAc,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAf,MAAA,CAAAxW,KAAA,OAAAE,SAAA;MAAA;IAAA,GAmCf;IACD,IAAMsX,YAAY,GAAG,SAAfA,YAAYA,CAAIb,EAAE,EAAK;MAC3BJ,UAAU,CAACzT,OAAO,CAAChJ,KAAK,EAAE,CAAC,EAAE6c,EAAE,CAAC;IAClC,CAAC;IACD,IAAMlK,UAAU;MAAA,IAAAgL,MAAA,GAAA7W,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAkZ,UAAOzc,IAAI,EAAE0b,EAAE;QAAA,IAAAgB,qBAAA,EAAAC,qBAAA,EAAAhL,IAAA;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAAkd,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7Y,IAAA,GAAA6Y,UAAA,CAAAxa,IAAA;YAAA;cAC1Bqa,qBAAqB,GAAG5P,mBAAmB,CAACjO,KAAK,CAACwT,GAAG,CAAC,UAAAhE,IAAI;gBAAA,OAAK;kBACnE2D,MAAM,EAAE3D,IAAI,CAAC2D,MAAM,IAAI3D,IAAI,CAAC1C,EAAE;kBAC9BoC,eAAe,EAAEM,IAAI,CAACN;gBACxB,CAAC;cAAA,CAAC,CAAC;cAAA8O,UAAA,CAAA7Y,IAAA;cAAA6Y,UAAA,CAAAxa,IAAA;cAAA,OAEsByD,GAAG,CAAC0L,UAAU,CAAClK,KAAK,CAACmI,KAAK,CAAC9D,EAAE,GAAG,gBAAgB,GAAG,eAAe,EAAE;gBACzF1D,IAAI,EAAE;kBACJ0D,EAAE,EAAErE,KAAK,CAACmI,KAAK,CAAC9D,EAAE;kBAClBzD,gBAAgB,EAAED,IAAI,CAACC,gBAAgB;kBACvCC,KAAK,EAAEF,IAAI,CAACE,KAAK;kBAAE;kBACnBC,aAAa,EAAEH,IAAI,CAACC,gBAAgB,KAAK,cAAc,GAAGD,IAAI,CAACG,aAAa,GAAG,IAAI;kBACnFM,YAAY,EAAET,IAAI,CAACC,gBAAgB,KAAK,MAAM,GAAGD,IAAI,CAACS,YAAY,GAAG,IAAI;kBACzEG,OAAO,EAAEZ,IAAI,CAACY,OAAO;kBACrBF,cAAc,EAAEV,IAAI,CAACU,cAAc;kBACnCG,eAAe,EAAEb,IAAI,CAACa,eAAe;kBACrCC,iBAAiB,EAAEd,IAAI,CAACc,iBAAiB;kBACzCC,aAAa,EAAEf,IAAI,CAACe,aAAa;kBACjCC,iBAAiB,EAAEhB,IAAI,CAACgB,iBAAiB;kBACzCC,iBAAiB,EAAEjB,IAAI,CAACiB,iBAAiB;kBACzCC,iBAAiB,EAAElB,IAAI,CAACkB,iBAAiB;kBACzC6B,UAAU,EAAE1D,KAAK,CAACmI,KAAK,CAAC9D,EAAE,GAAG1D,IAAI,CAAC+C,UAAU,GAAG0B,YAAY,CAAC7N,KAAK,CAACA,KAAK;kBACvEie,aAAa,EAAE5R,QAAQ,CAACrM,KAAK,CAACwT,GAAG,CAAC,UAACxR,CAAC;oBAAA,OAAKA,CAAC,CAAC8K,EAAE;kBAAA;gBAC/C,CAAC;gBACDoR,WAAW,EAAE;kBACXC,cAAc,EAAE/U,IAAI,CAACI,YAAY,GAAGJ,IAAI,CAACI,YAAY,GAAG;gBAC1D,CAAC;gBACD4U,WAAW,EAAEjd,IAAI;gBACjBkd,UAAU,EAAER,qBAAqB;gBACjC9T,SAAS,EAAEX,IAAI,CAACU,cAAc,GAAGV,IAAI,CAACW,SAAS,GAAG,EAAE;gBACpDQ,mBAAmB,EAAEnB,IAAI,CAACmB,mBAAmB;gBAC7CkQ,UAAU,EAAE5N,iBAAiB,CAAC7M,KAAK,CAChCgG,MAAM,CACL,UAAChE,CAAC;kBAAA,OACAA,CAAC,CAAC+K,WAAW,CAAClC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,IAC3C7I,CAAC,CAACgL,YAAY,CAACnC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,IAC5C7I,CAAC,CAACiL,cAAc,CAACpC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBAAA,CAClD,CAAC,CACA2I,GAAG,CAAC,UAACxR,CAAC;kBAAA,OAAM;oBACX0Y,aAAa,EAAE1Y,CAAC,CAAC+K,WAAW;oBAC5B4N,eAAe,EAAE3Y,CAAC,CAACgL,YAAY;oBAC/B4N,gBAAgB,EAAE5Y,CAAC,CAACiL;kBACtB,CAAC;gBAAA,CAAC;cACN,CAAC,CAAC;YAAA;cAAA6Q,qBAAA,GAAAE,UAAA,CAAA/a,IAAA;cArCM6P,IAAI,GAAAgL,qBAAA,CAAJhL,IAAI;cAAA,MAsCRA,IAAI,KAAK,GAAG;gBAAAkL,UAAA,CAAAxa,IAAA;gBAAA;cAAA;cACdyF,eAAe,CAACjJ,KAAK,GAAG,IAAI;cAC5BmJ,gBAAgB,CAACnJ,KAAK,GAAG,IAAI;cAC7BqO,kBAAkB,CAACrO,KAAK,GAAG,KAAK;cAAA,KAC5ByI,KAAK,CAACmI,KAAK,CAACC,UAAU;gBAAAmN,UAAA,CAAAxa,IAAA;gBAAA;cAAA;cACxB8a,eAAe,CAAC,CAAC;cAAAN,UAAA,CAAAxa,IAAA;cAAA;YAAA;cAAA,KAEbqZ,EAAE;gBAAAmB,UAAA,CAAAxa,IAAA;gBAAA;cAAA;cAAA,OAAAwa,UAAA,CAAA5a,MAAA,WACGyZ,EAAE,CAAC,CAAC;YAAA;cAEb/U,SAAS,CAAC;gBAAE3G,IAAI,EAAE,SAAS;gBAAEuJ,OAAO,EAAEjC,KAAK,CAACmI,KAAK,CAAC9D,EAAE,GAAG,MAAM,GAAG;cAAO,CAAC,CAAC;cACzE,IAAIrE,KAAK,CAACmI,KAAK,CAAC9D,EAAE,IAAIrE,KAAK,CAACmI,KAAK,CAACQ,MAAM,IAAI3I,KAAK,CAACmI,KAAK,CAACC,UAAU,EAAE;gBAClEhJ,YAAY,CAAC8H,cAAc,CAAC;kBAC1BoF,cAAc,EAAE;oBAAEC,MAAM,EAAEvM,KAAK,CAACmI,KAAK,CAACqE,UAAU;oBAAEC,OAAO,EAAEzM,KAAK,CAACmI,KAAK,CAACuE;kBAAQ;gBACjF,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLzM,KAAK,CAAC6V,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC;gBAChDxP,UAAU,CAAC,YAAM;kBACfrG,KAAK,CAAC6V,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBACrC,CAAC,EAAE,GAAG,CAAC;cACT;YAAC;cAAAP,UAAA,CAAAxa,IAAA;cAAA;YAAA;cAAAwa,UAAA,CAAA7Y,IAAA;cAAA6Y,UAAA,CAAAnL,EAAA,GAAAmL,UAAA;cAILrV,OAAO,CAAC3I,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAge,UAAA,CAAA1Y,IAAA;UAAA;QAAA,GAAAsY,SAAA;MAAA,CAExB;MAAA,gBAtEKjL,UAAUA,CAAA6L,GAAA,EAAAC,IAAA;QAAA,OAAAd,MAAA,CAAAzX,KAAA,OAAAE,SAAA;MAAA;IAAA,GAsEf;IACD,IAAMkY,eAAe;MAAA,IAAAI,MAAA,GAAA5X,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAia,UAAA;QAAA,IAAAC,sBAAA,EAAA9L,IAAA;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAAge,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3Z,IAAA,GAAA2Z,UAAA,CAAAtb,IAAA;YAAA;cAAAsb,UAAA,CAAAtb,IAAA;cAAA,OACCyD,GAAG,CAACqX,eAAe,CAAC;gBAAE1L,QAAQ,EAAEnK,KAAK,CAACmI,KAAK,CAACC;cAAW,CAAC,CAAC;YAAA;cAAA+N,sBAAA,GAAAE,UAAA,CAAA7b,IAAA;cAAxE6P,IAAI,GAAA8L,sBAAA,CAAJ9L,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBhL,SAAS,CAAC;kBAAE3G,IAAI,EAAE,SAAS;kBAAEuJ,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/C7C,YAAY,CAAC8H,cAAc,CAAC;kBAAEoF,cAAc,EAAE;oBAAEC,MAAM,EAAEvM,KAAK,CAACmI,KAAK,CAACqE,UAAU;oBAAEC,OAAO,EAAEzM,KAAK,CAACmI,KAAK,CAACuE;kBAAQ;gBAAE,CAAC,CAAC;cACnH;YAAC;YAAA;cAAA,OAAA2J,UAAA,CAAAxZ,IAAA;UAAA;QAAA,GAAAqZ,SAAA;MAAA,CACF;MAAA,gBANKL,eAAeA,CAAA;QAAA,OAAAI,MAAA,CAAAxY,KAAA,OAAAE,SAAA;MAAA;IAAA,GAMpB;IACD,IAAM2Y,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAItW,KAAK,CAACmI,KAAK,CAAC9D,EAAE,IAAIrE,KAAK,CAACmI,KAAK,CAACQ,MAAM,IAAI3I,KAAK,CAACmI,KAAK,CAACC,UAAU,EAAE;QAClEhJ,YAAY,CAAC8H,cAAc,CAAC;UAAEoF,cAAc,EAAE;YAAEC,MAAM,EAAEvM,KAAK,CAACmI,KAAK,CAACqE,UAAU;YAAEC,OAAO,EAAEzM,KAAK,CAACmI,KAAK,CAACuE;UAAQ;QAAE,CAAC,CAAC;MACnH,CAAC,MAAM;QACLzM,KAAK,CAAC6V,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC;QAChDxP,UAAU,CAAC,YAAM;UACfrG,KAAK,CAAC6V,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;QACrC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC;IAED/W,KAAK,CACH;MAAA,OAAM4B,IAAI,CAACW,SAAS;IAAA;MAAA,IAAAiV,MAAA,GAAAlY,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CACpB,SAAAua,UAAOC,MAAM,EAAEC,MAAM;QAAA,IAAApN,UAAA;QAAA,OAAAzS,mBAAA,GAAAuB,IAAA,UAAAue,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAla,IAAA,GAAAka,UAAA,CAAA7b,IAAA;YAAA;cAAA,KACf4F,IAAI,CAACU,cAAc;gBAAAuV,UAAA,CAAA7b,IAAA;gBAAA;cAAA;cACfuO,UAAU,GAAG3I,IAAI,CAACC,gBAAgB,KAAK,cAAc,GAAGD,IAAI,CAACG,aAAa,GAAGH,IAAI,CAACI,YAAY;cAAA,KAChGuI,UAAU;gBAAAsN,UAAA,CAAA7b,IAAA;gBAAA;cAAA;cAAA,MACRsM,IAAI,CAACa,SAAS,CAACuO,MAAM,CAAC,KAAKpP,IAAI,CAACa,SAAS,CAACwO,MAAM,CAAC;gBAAAE,UAAA,CAAA7b,IAAA;gBAAA;cAAA;cAAA6b,UAAA,CAAA7b,IAAA;cAAA,OAC7CoO,yBAAyB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAyN,UAAA,CAAA/Z,IAAA;UAAA;QAAA,GAAA2Z,SAAA;MAAA,CAIxC;MAAA,iBAAAK,IAAA,EAAAC,IAAA;QAAA,OAAAP,MAAA,CAAA9Y,KAAA,OAAAE,SAAA;MAAA;IAAA,KACD;MAAEoZ,IAAI,EAAE;IAAK,CACf,CAAC;IAEDhY,KAAK,CACH,CAAC;MAAA,OAAM4B,IAAI,CAACG,aAAa;IAAA,GAAE;MAAA,OAAMH,IAAI,CAACI,YAAY;IAAA,EAAC;MAAA,IAAAiW,MAAA,GAAA3Y,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CACnD,SAAAgb,UAAOC,SAAS,EAAEC,SAAS;QAAA,OAAAtgB,mBAAA,GAAAuB,IAAA,UAAAgf,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3a,IAAA,GAAA2a,UAAA,CAAAtc,IAAA;YAAA;cAAA,MACrBmc,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,CAAC,CAAC,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,CAAC,CAAC,CAAC;gBAAAE,UAAA,CAAAtc,IAAA;gBAAA;cAAA;cAAA,MAC5D4F,IAAI,CAACU,cAAc,IAAIV,IAAI,CAACW,SAAS,CAAC1F,MAAM,GAAG,CAAC;gBAAAyb,UAAA,CAAAtc,IAAA;gBAAA;cAAA;cAAAsc,UAAA,CAAAtc,IAAA;cAAA,OAC5CoO,yBAAyB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAkO,UAAA,CAAAxa,IAAA;UAAA;QAAA,GAAAoa,SAAA;MAAA,CAGtC;MAAA,iBAAAK,IAAA,EAAAC,IAAA;QAAA,OAAAP,MAAA,CAAAvZ,KAAA,OAAAE,SAAA;MAAA;IAAA,GACH,CAAC;IAED,IAAM6Z,cAAc;MAAA,IAAAC,MAAA,GAAApZ,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAyb,UAAOC,KAAK;QAAA,IAAArO,UAAA,EAAAsO,QAAA;QAAA,OAAA/gB,mBAAA,GAAAuB,IAAA,UAAAyf,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApb,IAAA,GAAAob,UAAA,CAAA/c,IAAA;YAAA;cAAA,MAC7B4c,KAAK,IAAIA,KAAK,CAAC/b,MAAM,GAAG,CAAC;gBAAAkc,UAAA,CAAA/c,IAAA;gBAAA;cAAA;cAAA+c,UAAA,CAAA/c,IAAA;cAAA,OACrBoO,yBAAyB,CAAC,CAAC;YAAA;cAAA2O,UAAA,CAAA/c,IAAA;cAAA;YAAA;cAE3BuO,UAAU,GAAG3I,IAAI,CAACC,gBAAgB,KAAK,cAAc,GAAGD,IAAI,CAACG,aAAa,GAAGH,IAAI,CAACI,YAAY;cACpG,IAAIuI,UAAU,EAAE;gBACRsO,QAAQ,GAAGpS,mBAAmB,CAACjO,KAAK,CAACiT,IAAI,CAAC,UAAAC,MAAM;kBAAA,OAAIA,MAAM,CAACpG,EAAE,KAAKiF,UAAU;gBAAA,EAAC;gBACnF9D,mBAAmB,CAACjO,KAAK,GAAGqgB,QAAQ,GAAG,CAACA,QAAQ,CAAC,GAAG,EAAE;gBACtDjS,MAAM,CAACpO,KAAK,GAAG,IAAI;cACrB,CAAC,MAAM;gBACLiO,mBAAmB,CAACjO,KAAK,GAAG,EAAE;gBAC9BoO,MAAM,CAACpO,KAAK,GAAG,KAAK;cACtB;YAAC;YAAA;cAAA,OAAAugB,UAAA,CAAAjb,IAAA;UAAA;QAAA,GAAA6a,SAAA;MAAA,CAEJ;MAAA,gBAdKF,cAAcA,CAAAO,IAAA;QAAA,OAAAN,MAAA,CAAAha,KAAA,OAAAE,SAAA;MAAA;IAAA,GAcnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}