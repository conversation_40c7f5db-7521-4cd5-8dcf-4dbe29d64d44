{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON>s as _withKeys, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"DoNotReceiveSuggest\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table = _resolveComponent(\"xyl-global-table\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_export_excel = _resolveComponent(\"xyl-export-excel\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList,\n    data: $setup.tableHead,\n    ref: \"queryRef\"\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\", \"data\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect,\n    onSortChange: $setup.handleSortChange,\n    \"header-cell-class-name\": $setup.handleHeaderClass\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_xyl_global_table, {\n        tableHead: $setup.tableHead,\n        onTableClick: $setup.handleTableClick\n      }, null, 8 /* PROPS */, [\"tableHead\"]), _createVNode(_component_xyl_global_table_button, {\n        editCustomTableHead: $setup.handleEditorCustom\n      }, null, 8 /* PROPS */, [\"editCustomTableHead\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\", \"onSortChange\", \"header-cell-class-name\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.exportShow,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.exportShow = $event;\n    }),\n    name: \"导出Excel\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_xyl_export_excel, {\n        name: $setup.route.query.moduleName,\n        exportId: $setup.exportId,\n        params: $setup.exportParams,\n        module: \"proposalExportExcel\",\n        tableId: $setup.route.query.tableId,\n        onExcelCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"name\", \"exportId\", \"params\", \"tableId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "data", "tableHead", "ref", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "onSortChange", "handleSortChange", "handleHeaderClass", "default", "_component_el_table_column", "type", "width", "fixed", "_component_xyl_global_table", "onTableClick", "handleTableClick", "_component_xyl_global_table_button", "editCustomTableHead", "handleEditorCustom", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "exportShow", "name", "_component_xyl_export_excel", "route", "query", "moduleName", "exportId", "params", "exportParams", "module", "tableId", "onExcelCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\DoNotReceiveSuggest\\DoNotReceiveSuggest.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DoNotReceiveSuggest\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\"\r\n                       :data=\"tableHead\"\r\n                       ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\"\r\n                @sort-change=\"handleSortChange\"\r\n                :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\"\r\n                          @tableClick=\"handleTableClick\"></xyl-global-table>\r\n        <xyl-global-table-button :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\"\r\n                      name=\"导出Excel\">\r\n      <xyl-export-excel :name=\"route.query.moduleName\"\r\n                        :exportId=\"exportId\"\r\n                        :params=\"exportParams\"\r\n                        module=\"proposalExportExcel\"\r\n                        :tableId=\"route.query.tableId\"\r\n                        @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DoNotReceiveSuggest' }\r\n</script>\r\n<script setup>\r\nimport { onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nconst route = useRoute()\r\nconst buttonList = [\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n]\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel\r\n} = GlobalTable({ tableId: route.query.tableId, tableApi: 'suggestionList' })\r\n\r\nonActivated(() => { handleQuery() })\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams())\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } } })\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  exportShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.DoNotReceiveSuggest {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAczBA,KAAK,EAAC;AAAa;;EAiBnBA,KAAK,EAAC;AAAkB;;;;;;;;;;;uBA/B/BC,mBAAA,CAkDM,OAlDNC,UAkDM,GAjDJC,YAAA,CAYoBC,4BAAA;IAZAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IACvBC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IACvBC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC1BC,UAAU,EAAEN,MAAA,CAAAM,UAAU;IACtBC,IAAI,EAAEP,MAAA,CAAAQ,SAAS;IAChBC,GAAG,EAAC;;IACVC,MAAM,EAAAC,QAAA,CACf;MAAA,OAGsB,CAHtBd,YAAA,CAGsBe,mBAAA;QAZ9BC,UAAA,EAS2Bb,MAAA,CAAAc,OAAO;QATlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAS2BhB,MAAA,CAAAc,OAAO,GAAAE,MAAA;QAAA;QAChBC,WAAW,EAAC,QAAQ;QACnBC,OAAK,EAXxBC,SAAA,CAWgCnB,MAAA,CAAAC,WAAW;QACzBmB,SAAS,EAAT;;;IAZlBC,CAAA;+CAeIC,mBAAA,CAgBM,OAhBNC,UAgBM,GAfJ1B,YAAA,CAcW2B,mBAAA;IAdDf,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,IAAI;IACXF,IAAI,EAAEP,MAAA,CAAAyB,SAAS;IACfC,QAAM,EAAE1B,MAAA,CAAA2B,iBAAiB;IACzBC,WAAU,EAAE5B,MAAA,CAAA2B,iBAAiB;IAC7BE,YAAW,EAAE7B,MAAA,CAAA8B,gBAAgB;IAC7B,wBAAsB,EAAE9B,MAAA,CAAA+B;;IAtBzCC,OAAA,EAAArB,QAAA,CAuBQ;MAAA,OAGyB,CAHzBd,YAAA,CAGyBoC,0BAAA;QAHRC,IAAI,EAAC,WAAW;QAChB,mBAAiB,EAAjB,EAAiB;QACjBC,KAAK,EAAC,IAAI;QACVC,KAAK,EAAL;UACjBvC,YAAA,CACoEwC,2BAAA;QADjD7B,SAAS,EAAER,MAAA,CAAAQ,SAAS;QACpB8B,YAAU,EAAEtC,MAAA,CAAAuC;8CAC/B1C,YAAA,CAA6F2C,kCAAA;QAAnEC,mBAAmB,EAAEzC,MAAA,CAAA0C;MAAkB,iD;;IA7BzErB,CAAA;sGAgCIC,mBAAA,CASM,OATNqB,UASM,GARJ9C,YAAA,CAO4B+C,wBAAA;IAPLC,WAAW,EAAE7C,MAAA,CAAA8C,MAAM;IAjChD,wBAAA/B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAiC0ChB,MAAA,CAAA8C,MAAM,GAAA9B,MAAA;IAAA;IACnB,WAAS,EAAEhB,MAAA,CAAA+C,QAAQ;IAlChD,qBAAAhC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAkCwChB,MAAA,CAAA+C,QAAQ,GAAA/B,MAAA;IAAA;IAC1B,YAAU,EAAEhB,MAAA,CAAAgD,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAElD,MAAA,CAAAC,WAAW;IACxBkD,eAAc,EAAEnD,MAAA,CAAAC,WAAW;IAC3BmD,KAAK,EAAEpD,MAAA,CAAAqD,MAAM;IACdC,UAAU,EAAV;qHAEjBzD,YAAA,CAQmB0D,2BAAA;IAlDvB1C,UAAA,EA0C+Bb,MAAA,CAAAwD,UAAU;IA1CzC,uBAAAzC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA0C+BhB,MAAA,CAAAwD,UAAU,GAAAxC,MAAA;IAAA;IACnByC,IAAI,EAAC;;IA3C3BzB,OAAA,EAAArB,QAAA,CA4CM;MAAA,OAK+D,CAL/Dd,YAAA,CAK+D6D,2BAAA;QAL5CD,IAAI,EAAEzD,MAAA,CAAA2D,KAAK,CAACC,KAAK,CAACC,UAAU;QAC5BC,QAAQ,EAAE9D,MAAA,CAAA8D,QAAQ;QAClBC,MAAM,EAAE/D,MAAA,CAAAgE,YAAY;QACrBC,MAAM,EAAC,qBAAqB;QAC3BC,OAAO,EAAElE,MAAA,CAAA2D,KAAK,CAACC,KAAK,CAACM,OAAO;QAC5BC,eAAa,EAAEnE,MAAA,CAAAoE;;;IAjDxC/C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}