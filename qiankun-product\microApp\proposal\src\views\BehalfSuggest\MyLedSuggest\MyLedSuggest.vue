<template>
  <div class="MyLedSuggest">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #mainHandleOffices="scope">
            {{scope.row.mainHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}
          </template>
          <template #assistHandleOffices="scope">
            {{scope.row.assistHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}
          </template>
          <template #publishHandleOffices="scope">
            {{scope.row.publishHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}
          </template>
        </xyl-global-table>
        <el-table-column width="160" fixed="right" class-name="globalTableCustom">
          <template #header>
            操作
            <div class="TableCustomIcon" v-if="hasPermission('table_custom')" @click="handleEditorCustom"></div>
          </template>
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)" v-if="scope.row.currentNodeId === 'returnSubmit'" type="primary"
              plain>
              重新提交
            </el-button>
            <el-button @click="handleSubmit(scope.row)" v-if="scope.row.currentNodeId === 'hasAnswerSuggestion'"
              type="primary" plain>
              {{ scope.row.satisfactionHandleResult || '满意度测评' }}
            </el-button>
            <el-button @click="handleSatisfaction(scope.row)" v-if="scope.row.currentNodeId === 'handleOver'"
              type="primary" plain>
              {{ scope.row.satisfactionHandleResult }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="我领衔的提案" :exportId="exportId" :params="exportParams" module="proposalExportExcel"
        tableId="id_prop_proposal_mySubmit" @excelCallback="callback"
        :handleExcelData="handleExcelData"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="show" name="满意度测评">
      <SubmitSegreeSatisfaction :id="id" :type="type" @callback="callback"></SubmitSegreeSatisfaction>
    </xyl-popup-window>
    <xyl-popup-window v-model="isShow" name="满意度测评">
      <SegreeSatisfactionDetail :suggestId="id" @callback="callback" type></SegreeSatisfactionDetail>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'MyLedSuggest' }
</script>
<script setup>
import { ref, onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { hasPermission } from 'common/js/permissions'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import SubmitSegreeSatisfaction from './component/SubmitSegreeSatisfaction.vue'
import SegreeSatisfactionDetail from '@/views/SuggestDetail/SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue'
const buttonList = [
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
]
const id = ref('')
const type = ref('')
const show = ref(false)
const isShow = ref(false)
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({
  tableId: 'id_prop_proposal_mySubmit',
  tableApi: 'suggestionList',
  tableDataObj: { isContainMerge: 1 }
})

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    handleQuery()
  }
})
const handleExcelData = (_item) => {
  _item.forEach(v => {
    if (!v.mainHandleOffices) {
      v.mainHandleOffices = v.publishHandleOffices
    }
  })
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } }
  })
}
const handleEdit = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '编辑提案', path: '/proposal/SubmitSuggest', query: { anewId: item.id } }
  })
}
const handleSubmit = (row) => {
  id.value = row.id
  type.value = row.satisfactionHandleResult
  show.value = true
}
const handleSatisfaction = (row) => {
  id.value = row.id
  isShow.value = true
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
  show.value = false
}
</script>
<style lang="scss">
.MyLedSuggest {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
