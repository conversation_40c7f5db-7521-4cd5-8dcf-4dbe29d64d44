<template>
  <div class="SuggestClueSubmit">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             inline
             label-position="top"
             class="globalForm">
      <el-form-item label="线索标题"
                    prop="title"
                    class="globalFormTitle">
        <el-input v-model="form.title"
                  placeholder="请输入线索标题"
                  clearable />
      </el-form-item>

      <el-form-item label="提供者"
                    prop="furnishName"
                    v-if="form.terminalName == '第三方'">
        <el-input v-model="form.furnishName"
                  show-word-limit
                  placeholder="请输入提供者"
                  disabled
                  clearable />
      </el-form-item>

      <el-form-item label="提供者"
                    prop="furnish"
                    v-else>
        <input-select-person v-model="form.furnish"
                             placeholder="请选择提供者"
                             :tabCode="tabCode"
                             @callback="userCallback" />
      </el-form-item>

      <el-form-item label="线索类别"
                    prop="proposalClueType">
        <el-select v-model="form.proposalClueType"
                   placeholder="请选择线索类别">
          <el-option v-for="item in proposalClueType"
                     :key="item.id"
                     :label="item.label"
                     :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="线索内容"
                    prop="content"
                    class="globalFormTitle">
        <el-input v-model="form.content"
                  show-word-limit
                  type="textarea"
                  placeholder="请输入线索内容"
                  rows="6"
                  clearable />
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary"
                   @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SuggestClueSubmit' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, computed, onMounted } from 'vue'
import { selectUser } from 'common/js/system_var.js'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const tabCode = computed(() => selectUser.value.suggestClue)
const formRef = ref()
const form = reactive({
  id: '',
  title: '',
  proposalClueType: '',
  content: '',
  furnish: '',
  furnishName: '',
  furnish_mobile: '',
  terminalName: ''
})
const rules = reactive({
  title: [{ required: true, message: '请输入线索标题', trigger: ['blur', 'change'] }],
  furnish: [{ required: true, message: '请输入选择提供者', trigger: ['blur', 'change'] }],
  proposalClueType: [{ required: true, message: '请选择线索类别', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '请输入线索内容', trigger: ['blur', 'change'] }]
})
const proposalClueType = ref([])

console.log(form.terminalName, 'terminalName')

onMounted(() => {
  dictionaryData()
  if (props.id) { proposalClueInfo() }
})

const dictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: ['proposal_clue_type'] })
  proposalClueType.value = data.proposal_clue_type
}
const proposalClueInfo = async () => {
  const res = await api.proposalClueInfo({ detailId: props.id })
  var { data } = res
  form.id = props.id
  form.title = data.title
  form.content = data.content
  form.furnish = data.furnish
  form.furnishName = data.furnishName
  form.furnish_mobile = data.furnish_mobile
  form.proposalClueType = data.proposalClueType.value
  form.terminalName = data.terminalName
}

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const userCallback = (data) => {
  if (data) {
    form.furnish = data.id
    form.furnishName = data.userName
    form.furnish_mobile = data.mobile
  }
}

const globalJson = async () => {
  const { code } = await api.globalJson(props.id ? '/proposalClue/edit' : '/proposalClue/add', {
    form: {
      id: props.id || null,
      title: form.title,
      content: form.content,
      proposalClueType: form.proposalClueType,
      furnish: form.furnish,
      furnishName: form.furnishName,
      furnishMobile: form.furnish_mobile,
      terminalName: form.terminalName || "PC",
    },
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}
const resetForm = () => { emit('callback') }
</script>
<style lang="scss">
.SuggestClueSubmit {
  width: 680px;
}
</style>
