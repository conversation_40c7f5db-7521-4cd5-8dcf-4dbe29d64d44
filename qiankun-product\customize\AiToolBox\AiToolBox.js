import { defineAsyncComponent } from 'vue'
const IntelligentErrorCorrection = defineAsyncComponent(() =>
  import('../AiToolBoxFunction/IntelligentErrorCorrection/IntelligentErrorCorrection.vue')
)
const OneClickLayout = defineAsyncComponent(() => import('../AiToolBoxFunction/OneClickLayout/OneClickLayout.vue'))
const ContentExtraction = defineAsyncComponent(() =>
  import('../AiToolBoxFunction/ContentExtraction/ContentExtraction.vue')
)
const IntelligentManuscriptMerging = defineAsyncComponent(() =>
  import('../AiToolBoxFunction/IntelligentManuscriptMerging/IntelligentManuscriptMerging.vue')
)
const TextComparison = defineAsyncComponent(() => import('../AiToolBoxFunction/TextComparison/TextComparison.vue'))
const TextPolishing = defineAsyncComponent(() => import('../AiToolBoxFunction/TextPolishing/TextPolishing.vue'))
const TextExpansion = defineAsyncComponent(() => import('../AiToolBoxFunction/TextExpansion/TextExpansion.vue'))
const TextContinuation = defineAsyncComponent(() =>
  import('../AiToolBoxFunction/TextContinuation/TextContinuation.vue')
)
const TextRewrite = defineAsyncComponent(() => import('../AiToolBoxFunction/TextRewrite/TextRewrite.vue'))
const TextRecognition = defineAsyncComponent(() => import('../AiToolBoxFunction/TextRecognition/TextRecognition.vue'))
const ProposalAuxiliaryWriting = defineAsyncComponent(() =>
  import('../AiToolBoxFunction/ProposalAuxiliaryWriting/ProposalAuxiliaryWriting.vue')
)
export const AiToolBoxElement = {
  IntelligentErrorCorrection,
  OneClickLayout,
  ContentExtraction,
  IntelligentManuscriptMerging,
  TextComparison,
  TextPolishing,
  TextExpansion,
  TextContinuation,
  TextRewrite,
  TextRecognition,
  ProposalAuxiliaryWriting
}

export const setting = {
  height: '100%',
  menubar: false,
  statusbar: false,
  elementpath: false,
  toolbar:
    'undo redo | formatselect | fontselect | fontsizeselect | alignleft aligncenter alignright alignjustify | removeformat searchreplace wordcount | bold italic underline strikethrough | forecolor backcolor lineheight | indent2em indent outdent | numlist bullist | link unlink | table',
  toolbar_mode: 'sliding' // floating / sliding / scrolling / wrap
}
export const content_style = `
html {
  background: #f7f7f7;
  padding: 16px 0;
}
body {
  width: 793.733px;
  min-height: 1122.53px; 
  padding: 90.7333px 94.5px;
  background: #fff;
  box-sizing: border-box;
  box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 10px 0px, rgba(0, 0, 0, 0.04) 0px 0px 0px 1px;
  margin: 0 0 0 1px;
}
body::before {
  left: 94.5px !important;
}`

export const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
export const trigerUpload = () => {
  return new Promise((resolve) => {
    let input = document.createElement('input')
    input.setAttribute('type', 'file')
    input.setAttribute('multiple', 'multiple')
    input.addEventListener('change', (e) => {
      resolve(e.target.files[0])
    })
    input.click()
  })
}
