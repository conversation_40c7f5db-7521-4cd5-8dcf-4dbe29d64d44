<template>
  <div class="SuggestTrackTransactDetail">
    <div class="SuggestTrackTransactDetailNameBody">
      <div class="SuggestTrackTransactDetailName">
        <div>跟踪办理审查</div>
      </div>
    </div>
    <div class="SuggestTrackTransactDetailBody">
      <global-info v-for="item in dataList"
                   :key="item.id">
        <global-info-line>
          <global-info-item label="申请单位">
            <div class="SuggestTrackTransactUnit">
              <span>{{ item.handleOfficeName }}</span>
              <el-link v-if="!item.verifyStatus"
                       @click="handleTrackTransact(item)"
                       type="primary">审查</el-link>
            </div>
          </global-info-item>
          <global-info-item label="申请时间">{{ format(item.createDate) }}</global-info-item>
        </global-info-line>
        <global-info-item label="申请理由">
          <pre>{{ item.applyReason }}</pre>
        </global-info-item>
        <global-info-item v-if="item.verifyStatus"
                          label="是否同意申请">{{ item.verifyStatus === 1 ? '同意申请' : '驳回' }}</global-info-item>
        <global-info-item v-if="item.verifyStatus"
                          :label="item.verifyStatus === 1 ? '备注' : '驳回理由'">
          <pre>{{ item.verifySuggestion }}</pre>
        </global-info-item>
        <global-info-item label="跟踪办理答复文件"
                          v-if="item.verifyStatus === 1">
          <el-link @click="isTrackTransactReply = !isTrackTransactReply"
                   type="primary">填写跟踪办理答复文件</el-link>
        </global-info-item>
      </global-info>
    </div>
    <xyl-popup-window v-model="show"
                      name="跟踪办理审查">
      <SuggestTrackTransactReview :id="id"
                                  @callback="callback"></SuggestTrackTransactReview>
    </xyl-popup-window>

    <xyl-popup-window v-model="isTrackTransactReply"
                      name="填写跟踪办理答复文件">
      <SubmitSuggestTrackTransactReply :suggestId="props.id"
                                       :unitId="transactId"
                                       :traceId="tracesInfo.id"
                                       @callback="handleCallback"></SubmitSuggestTrackTransactReply>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestTrackTransactDetail' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated, computed } from 'vue'
import { format } from 'common/js/time.js'
import SuggestTrackTransactReview from './SuggestTrackTransactReview.vue'
import SubmitSuggestTrackTransactReply from '@/views/UnitSuggestDetail/component/SubmitSuggestTrackTransactReply.vue'
const props = defineProps({
  id: { type: String, default: '' },
  transactUnitObj: { type: Object, default: () => ({}) },
})
const emit = defineEmits(['refresh'])

const tracesInfo = computed(() => props.transactUnitObj?.traces?.filter(v => !v.hasAnswer)[0] || {})
const transactId = computed(() => props.transactUnitObj?.handlerOffice?.id)
const id = ref('')
const show = ref(false)
const dataList = ref([])
const isTrackTransactReply = ref(false)


onActivated(() => { handingPortionTraceList() })

const handingPortionTraceList = async () => {
  const res = await api.handingPortionTraceList({ query: { suggestionId: props.id } })
  var { data } = res
  dataList.value = data.filter(v => !v.hasAnswer)
}
const handleTrackTransact = (item) => {
  id.value = item.id
  show.value = true
}
const callback = (type) => {
  handingPortionTraceList()
  show.value = false
  isTrackTransactReply.value = false
  if (type) { emit('refresh') }
}
const handleCallback = () => {
  emit('callback')
}
</script>
<style lang="scss">
.SuggestTrackTransactDetail {
  width: 100%;
  height: 100%;

  .SuggestTrackTransactDetailNameBody {
    padding: 0 var(--zy-distance-one);
    padding-top: var(--zy-distance-one);

    .SuggestTrackTransactDetailName {
      width: 100%;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      position: relative;
      text-align: center;

      div {
        display: inline-block;
        background-color: #fff;
        position: relative;
        z-index: 2;
        padding: 0 20px;
      }

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 1px;
        background-color: var(--zy-el-color-primary);
      }
    }
  }

  .SuggestTrackTransactDetailBody {
    padding: var(--zy-distance-one);
    padding-bottom: 0;

    .global-info {
      padding-bottom: 12px;

      .global-info-item {
        .global-info-label {
          width: 160px;
        }

        .global-info-content {
          width: calc(100% - 160px);
        }
      }
    }

    .SuggestTrackTransactUnit {
      display: flex;
      align-items: center;

      .zy-el-link {
        margin-left: 10px;
      }
    }
  }
}
</style>
