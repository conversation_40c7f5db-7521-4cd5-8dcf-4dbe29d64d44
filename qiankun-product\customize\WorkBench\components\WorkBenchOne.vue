<template>
  <el-scrollbar always class="WorkBenchOne"
    :style="`background: url('${WorkBenchBgImg}') no-repeat;background-size: 100% 100%;`">
    <div class="WorkBenchOneBox" v-for="item in WorkBench" :key="item.id">
      <div class="WorkBenchOneColumn" :class="{ WorkBenchOneColumnOther: item.id === 'other' && !item.name }">
        {{ item.name }}
      </div>
      <div class="WorkBenchOneList">
        <div class="WorkBenchOneItem" v-for="row in item.data" :key="row.id" @click="handleWorkBench(row)">
          <el-image :src="row.icon" fit="cover" />
          <div class="WorkBenchOneItemBox">
            <div class="WorkBenchOneItemName" v-html="row.name"></div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script>
export default { name: 'WorkBench' }
</script>
<script setup>
import api from '@/api'
import { ref, inject, watch, onMounted } from 'vue'
import { WorkBenchBgImg } from 'common/js/system_var.js'

const menuFunction = ref([])
const WorkBench = ref([])
const WorkBenchList = inject('WorkBenchList')
const leftMenuData = inject('leftMenuData')

onMounted(() => {
  dictionaryData()
})

const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['menu_function'] })
  var { data } = res
  menuFunction.value = data.menu_function || []
  if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {
    handleWorkBenchData(menuFunction.value.map((v) => ({ id: v.key, name: v.name, data: [] })))
  }
}
const handleWorkBenchData = (arr) => {
  if (!WorkBenchList || !WorkBenchList.value) return

  var other = ''
  var arrData = arr
  var arrIndex = arr.map((v) => v.id)
  for (let i = 0, len = WorkBenchList.value.length; i < len; i++) {
    const item = WorkBenchList.value[i]
    if (item.menuFunction?.value) {
      if (arrIndex.includes(item.menuFunction.value)) {
        arrData.forEach((row) => {
          if (item.menuFunction.value === row.id) {
            row.data.push(item)
          }
        })
      } else {
        arrIndex.push(item.menuFunction.value)
        arrData.push({ id: item.menuFunction.value, name: item.menuFunction.label, data: [item] })
      }
    } else {
      if (other) {
        other.data.push(item)
      } else {
        other = { id: 'other', data: [item] }
      }
    }
  }
  WorkBench.value = arrData.filter((v) => v.data.length)
  if (other) {
    WorkBench.value.push({ ...other, name: WorkBench.value.length ? '其他' : '' })
  }
}
const handleWorkBench = (item) => {
  leftMenuData(item)
}

watch(
  () => WorkBenchList.value,
  () => {
    if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {
      handleWorkBenchData(menuFunction.value.map((v) => ({ id: v.key, name: v.name, data: [] })))
    }
  },
  { deep: true }
)
</script>
<style lang="scss">
.WorkBenchOne {
  width: 100%;
  height: 100%;

  .WorkBenchOneBox {
    width: 1140px;
    margin: auto;
    padding-left: 40px;

    .WorkBenchOneColumn {
      padding: 30px 0;
      padding-left: 24px;
      font-size: 20px;
      font-weight: bold;
      color: #333333;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid var(--zy-el-color-primary);
        transform: translateY(-50%);
      }

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 10px;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid var(--zy-el-color-primary);
        transform: translateY(-50%);
      }
    }

    .WorkBenchOneColumnOther {
      &::after {
        border-left: 6px solid transparent;
      }

      &::before {
        border-left: 6px solid transparent;
      }
    }

    .WorkBenchOneList {
      display: flex;
      flex-wrap: wrap;

      .WorkBenchOneItem {
        width: 248px;
        background: #ffffff;
        box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        margin: 0 26px 26px 0;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        padding: 20px 22px;
        cursor: pointer;

        &:hover {
          box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.2);
        }

        .zy-el-image {
          width: 48px;
          height: 48px;
        }

        .WorkBenchOneItemBox {
          width: calc(100% - 48px);
          height: calc((var(--zy-name-font-size) * var(--zy-line-height)) * 2);
          display: flex;
          flex-wrap: wrap;
          align-items: center;
        }

        .WorkBenchOneItemName {
          width: 100%;
          line-height: var(--zy-line-height);
          font-size: var(--zy-name-font-size);
          padding-left: 20px;
          font-weight: bold;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
