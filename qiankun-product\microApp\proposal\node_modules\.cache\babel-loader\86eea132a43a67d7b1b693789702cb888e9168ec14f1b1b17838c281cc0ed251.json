{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n// 导入封装的方法\nimport HTTP from 'common/http';\nimport GlobalApi from 'common/http/GlobalApi';\nvar api = _objectSpread(_objectSpread({}, GlobalApi), {}, {\n  userBatch(params) {\n    // 用户批量操作\n    return HTTP.json('/user/batch', params);\n  },\n  cppccMemberInfo(params) {\n    // 获取委员信息详情\n    return HTTP.json('/cppccMember/info', params);\n  },\n  teamOfficeSelect(params) {\n    // 获取集体提案单位下拉选\n    return HTTP.json('/teamOffice/selector', params);\n  },\n  tableHeadInfo(params) {\n    // 自定义表头 列详情\n    return HTTP.get(`/customColumn/info/${params}`);\n  },\n  teamOfficeList(params) {\n    // 集体办理单位列表\n    return HTTP.json('/teamOffice/list', params);\n  },\n  teamOfficeInfo(params) {\n    // 集体办理单位详情\n    return HTTP.json('/teamOffice/info', params);\n  },\n  teamOfficeDel(params) {\n    // 集体办理单位删除\n    return HTTP.json('/teamOffice/del', params);\n  },\n  teamProposalUserList(params) {\n    // 集体提案单位用户列表\n    return HTTP.json('/teamProposalUser/list', params);\n  },\n  teamProposalUserInfo(params) {\n    // 集体提案单位用户详情\n    return HTTP.json('/teamProposalUser/info', params);\n  },\n  teamProposalUserDel(params) {\n    // 集体提案单位用户删除\n    return HTTP.json('/teamProposalUser/del', params);\n  },\n  suggestionOfficeList(params) {\n    // 办理单位列表\n    return HTTP.json('/proposalOffice/list', params);\n  },\n  suggestionOfficeInfo(params) {\n    // 办理单位详情\n    return HTTP.json('/proposalOffice/info', params);\n  },\n  suggestionOfficeDel(params) {\n    // 办理单位删除\n    return HTTP.json('/proposalOffice/del', params);\n  },\n  suggestionOfficeUsingOrStop(params) {\n    // 办理单位启用禁用\n    return HTTP.json('/proposalOffice/usingOrStop', params);\n  },\n  suggestionOfficeSelect(params) {\n    return HTTP.json('/proposalOffice/select', params);\n  },\n  suggestUnitUserList(params) {\n    // 办理单位用户列表\n    return HTTP.json('/proposalOfficeUser/list', params);\n  },\n  suggestUnitUserInfo(params) {\n    // 办理单位用户详情\n    return HTTP.json('/proposalOfficeUser/info', params);\n  },\n  suggestUnitUserDel(params) {\n    // 办理单位用户删除\n    return HTTP.json('/proposalOfficeUser/dels', params);\n  },\n  suggestionTermYearList(params) {\n    // 届次编号列表\n    return HTTP.json('/proposalTermYear/list', params);\n  },\n  suggestionTermYearInfo(params) {\n    // 届次编号详情\n    return HTTP.json('/proposalTermYear/info', params);\n  },\n  suggestionTermYearDel(params) {\n    // 届次编号删除\n    return HTTP.json('/proposalTermYear/dels', params);\n  },\n  suggestionThemeSelect(params) {\n    // 提案分类列表\n    return HTTP.json('/proposalTheme/select', params);\n  },\n  suggestionThemeList(params) {\n    // 提案分类列表\n    return HTTP.json('/proposalTheme/tree', params);\n  },\n  suggestionThemeInfo(params) {\n    // 提案分类详情\n    return HTTP.json('/proposalTheme/info', params);\n  },\n  suggestionThemeDel(params) {\n    // 提案分类删除\n    return HTTP.json('/proposalTheme/dels', params);\n  },\n  suggestionList(params) {\n    // 提案列表\n    return HTTP.json('/proposal/list', params);\n  },\n  suggestionInfo(params) {\n    // 提案详情\n    return HTTP.json('/proposal/info', params);\n  },\n  suggestionDetails(params) {\n    // 提案流程详情\n    return HTTP.json('/proposal/getSuggestionFlow', params);\n  },\n  suggestionDel(params) {\n    // 提案删除\n    return HTTP.json('/proposal/dels', params);\n  },\n  suggestionWord(params) {\n    // 提案导出\n    return HTTP.json('/proposal/loadDocData', params);\n  },\n  suggestionStatistics(params) {\n    // 提案统计\n    return HTTP.json('/proposalStatistics/composite', params);\n  },\n  changeSerialNumber(params) {\n    // 提案编号对调\n    return HTTP.json('/proposal/changeSerialNumber', params);\n  },\n  joinSubmiterList(params) {\n    // 提案联名人列表\n    return HTTP.json('/cppcc/joinSubmiter/list', params);\n  },\n  joinSubmiterAdd(params) {\n    // 提案联名人新增\n    return HTTP.json('/cppcc/joinSubmiter/add', params);\n  },\n  joinSubmiterDel(params) {\n    // 提案联名人删除\n    return HTTP.json('/cppcc/joinSubmiter/dels', params);\n  },\n  suggestBySerialNumber(params) {\n    // 根据提案编号查询\n    return HTTP.json('/proposal/info/bySerialNumber', params);\n  },\n  suggestionAgree(params) {\n    // 同意联名\n    return HTTP.json('/proposal/agree', params);\n  },\n  suggestionNextNodes(params) {\n    // 提案流程操作项\n    return HTTP.json('/proposal/nextNodes', params);\n  },\n  suggestionComplete(params) {\n    // 处理任务节点\n    return HTTP.json('/proposal/complete', params);\n  },\n  suggestionBatchComplete(params) {\n    // 批量处理任务节点\n    return HTTP.json('/proposal/batchComplete', params);\n  },\n  suggestionBatchCompleteUnit(params) {\n    // 批量转给承办单位\n    return HTTP.json('/proposal/batchSubmitHandleOffice', params);\n  },\n  suggestionBatchPreAssignOffice(params) {\n    // 批量预交办\n    return HTTP.json('/proposal/batchPreAssignOffice', params);\n  },\n  handingPortionCommunicationList(params) {\n    // 沟通情况列表\n    return HTTP.json('/cppcc/handingPortionCommunication/list', params);\n  },\n  handingPortionCommunicationInfo(params) {\n    // 沟通情况详情\n    return HTTP.json('/cppcc/handingPortionCommunication/info', params);\n  },\n  handingPortionCommunicationDel(params) {\n    // 沟通情况删除\n    return HTTP.json('/cppcc/handingPortionCommunication/dels', params);\n  },\n  prepareMembers(params) {\n    // 提案沟通记录待选委员\n    return HTTP.json('/cppcc/handingPortionCommunication/prepareMembers', params);\n  },\n  portionSelect(params) {\n    // 提案沟通记录待选单位\n    return HTTP.json('/proposal/portionSelect', params);\n  },\n  handingPortionDelayInfo(params) {\n    // 延期记录详情\n    return HTTP.json('/cppcc/handingPortionDelay/info', params);\n  },\n  handingPortionDelayVerifyDelay(params) {\n    // 延期记录审查\n    return HTTP.json('/cppcc/handingPortionDelay/verify/delay', params);\n  },\n  handingPortionAnswerList(params) {\n    // 答复列表\n    return HTTP.json('/cppcc/handingPortionAnswer/list', params);\n  },\n  handingPortionAnswerInfo(params) {\n    // 答复详情\n    return HTTP.json('/cppcc/handingPortionAnswer/info', params);\n  },\n  handingPortionAdjustList(params) {\n    // 调整列表\n    return HTTP.json('/cppcc/handingPortionAdjust/list', params);\n  },\n  suggestionUnlock(params) {\n    // 解锁\n    return HTTP.json('/proposal/unlock', params);\n  },\n  suggestionOpen(params) {\n    // 批量公开\n    return HTTP.json('/proposal/batch/open', params);\n  },\n  suggestionMajor(params) {\n    // 批量设置重点提案\n    return HTTP.json('/proposal/batch/major', params);\n  },\n  suggestionExcellent(params) {\n    // 批量设置优秀提案\n    return HTTP.json('/proposal/signExcellent', params);\n  },\n  suggestionSatisfactionInfo(params) {\n    // 满意度详情\n    return HTTP.json('/proposalSatisfaction/info', params);\n  },\n  suggestionCountSelector(params) {\n    // 办理中提案统计\n    return HTTP.json('/proposal/countSelector', params);\n  },\n  handingPortionAdjustRecord(params) {\n    // 承办单位调整结果列表\n    return HTTP.json('/cppcc/handingPortionAdjust/record', params);\n  },\n  suggestionPress(params) {\n    // 催办提案\n    return HTTP.json('/proposal/press', params);\n  },\n  handlingPortionBatchConfirm(params) {\n    // 批量催办签收建议\n    return HTTP.json('/cppcc/handlingPortion/batchConfirm', params);\n  },\n  handlingPortionConfirm(params) {\n    // 签收建议\n    return HTTP.json('/cppcc/handlingPortion/confirm', params);\n  },\n  handlingMassingEditAnswerStopDate(params) {\n    // 修改答复截止时间\n    return HTTP.json('/cppcc/handlingMassing/editAnswerStopDate', params);\n  },\n  handingPortionTraceList(params) {\n    // 承办单位申请跟踪办理记录\n    return HTTP.json('/cppcc/handingPortionTrace/list', params);\n  },\n  handingPortionTraceAdd(params) {\n    // 承办单位申请跟踪办理记录\n    return HTTP.json('/cppcc/handingPortionTrace/add', params);\n  },\n  handingPortionTraceInfo(params) {\n    // 承办单位申请跟踪详情\n    return HTTP.json('/cppcc/handingPortionTrace/info', params);\n  },\n  handingPortionTraceVerify(params) {\n    // 承办单位申请跟踪审查\n    return HTTP.json('/cppcc/handingPortionTrace/verify', params);\n  },\n  proposalClueList(params) {\n    // 提案线索列表\n    return HTTP.json('/proposalClue/list', params);\n  },\n  proposalClueInfo(params) {\n    // 提案线索详情\n    return HTTP.json('/proposalClue/info', params);\n  },\n  proposalClueDel(params) {\n    // 提案线索删除\n    return HTTP.json('/proposalClue/dels', params);\n  },\n  proposalClueUse(params) {\n    // 提案线索引用\n    return HTTP.json('/proposalClue/use', params);\n  },\n  mergeProposalList(params) {\n    // 并案列表\n    return HTTP.json('/mergeProposal/list', params);\n  },\n  mergeProposalAdd(params) {\n    // 并案\n    return HTTP.json('/mergeProposal/add', params);\n  },\n  mergeProposalDel(params) {\n    // 取消并案\n    return HTTP.json('/mergeProposal/dels', params);\n  },\n  proposalMergeSuggest(params) {\n    // 智能并案\n    return HTTP.json('/hadoop_api/datax/proposal/mergeSuggest', params, {\n      noErrorTip: true\n    });\n  },\n  similarity(params) {\n    // 相似度查询\n    return HTTP.json('/summoner/pubApi/similarity', params, {\n      noErrorTip: true\n    });\n  },\n  userJoin(params) {\n    // 推荐用户\n    return HTTP.json('/common/suggestRecommendation', params, {\n      noErrorTip: true\n    });\n  },\n  commonUnit(params) {\n    // 推荐单位\n    return HTTP.json('/summoner/common/recommendHandleUnit', params, {\n      noErrorTip: true\n    });\n  },\n  commonType(params) {\n    // 推荐分类\n    return HTTP.json('/common/classify', params, {\n      noErrorTip: true\n    });\n  },\n  proposalClueTheme(params) {\n    // 提案文案线索列表\n    return HTTP.json('/proposalClueTheme/list', params);\n  },\n  proposalClueThemeInfo(params) {\n    // 提案线索详情\n    return HTTP.json('/proposalClueTheme/info', params);\n  },\n  clueWord(params) {\n    // 提案导出\n    return HTTP.json('/proposalClue/loadDocData', params);\n  },\n  reqProposalEmpty(url, params) {\n    // 获取提案分类列表 - empty: 查询未分类提案 notempty：查询已分类提案\n    return HTTP.json(`/proposal/findThemeProposal/${url}`, params);\n  },\n  reqProposalTheme(url, params) {\n    //批量操作 提案细分分类接口 - add：批量细分分类  clear：批量清空分类\n    return HTTP.json(`/proposal/batch/proposalTheme/${url}`, params);\n  },\n  reqFindOfficeProposal(url, params) {\n    // 获取提案办理单位细分列表 - empty: 查询未分类提案 notempty：查询已分类提案\n    return HTTP.json(`/proposal/findOfficeProposal/${url}`, params);\n  },\n  reqProposalBatchComplete(url, params) {\n    //批量操作 提案细分办理单位接口 - add：批量细分分类  clear：批量清空分类\n    return HTTP.json(`/proposal/batch/complete/${url}`, params);\n  },\n  handingPortionAnswerDel(params) {\n    // 答复删除\n    return HTTP.json('/cppcc/handingPortionAnswer/dels', params);\n  },\n  handingPortionAnswerEdit(params) {\n    // 修改答复\n    return HTTP.json('/cppcc/handingPortionAnswer/edit', params);\n  },\n  handingPortionAdjustOfficeInfo(params) {\n    // 办理单位详情\n    return HTTP.json('/cppcc/handingPortionAdjust/adjustOfficeInfo', params);\n  },\n  suggestionSatisfactionList(params) {\n    // 获取满意度测评列表\n    return HTTP.json('/proposalSatisfaction/list', params);\n  },\n  suggestionSatisfactionDel(params) {\n    // 满意度测评删除\n    return HTTP.json('/proposalSatisfaction/dels', params);\n  },\n  suggestionSatisfactionEdit(params) {\n    // 修改满意度测评\n    return HTTP.json('/proposalSatisfaction/edit', params);\n  },\n  handingPortionTraceSelector(params) {\n    // 选择下拉选传参承办单位申请跟踪审查\n    return HTTP.json('/cppcc/handingPortionTrace/selector', params);\n  },\n  suggestionSuperDetail(params) {\n    // 超级修改详情\n    return HTTP.json(`/proposal/superDetail`, params);\n  },\n  handlingPortionList(params) {\n    // 获取建议办理信息列表\n    return HTTP.json('/cppcc/handlingPortion/list', params);\n  },\n  handlingPortionInfo(params) {\n    // 建议办理信息详情\n    return HTTP.json('/cppcc/handlingPortion/info', params);\n  },\n  handlingPortionDel(params) {\n    // 建议办理信息删除\n    return HTTP.json('/cppcc/handlingPortion/dels', params);\n  },\n  handlingPortionEdit(params) {\n    // 修改建议办理信息\n    return HTTP.json('/cppcc/handlingPortion/edit', params);\n  },\n  handlingPortionAdd(params) {\n    // 新增建议办理信息\n    return HTTP.json('/cppcc/handlingPortion/add', params);\n  },\n  handleOfficeReportList(params) {\n    // 列表\n    return HTTP.json('/handleOfficeReport/list', params);\n  },\n  handleOfficeReportInfo(params) {\n    // 详情\n    return HTTP.json('/handleOfficeReport/info', params);\n  },\n  handleOfficeReportDel(params) {\n    // 删除\n    return HTTP.json('/handleOfficeReport/dels', params);\n  },\n  handleOfficeReportFlush(params) {\n    // 刷新\n    return HTTP.json('/handleOfficeReport/flush', params);\n  },\n  handleOfficeReportPress(params) {\n    // 催办\n    return HTTP.json('/handleOfficeReport/press', params);\n  },\n  handleOfficeReportZip(params) {\n    // 导出\n    return HTTP.fileDownload('/handleOfficeReport/attachmentZip', params);\n  },\n  getProposalYear(params) {\n    // 配置届次\n    return HTTP.json('/customizeData/getProposalYear', params);\n  },\n  proposalThemeSelect(params) {\n    // 提案分类\n    return HTTP.json('/proposalApi/proposalThemeSelect', params);\n  },\n  getProposalStatus(params) {\n    // 提案状态\n    return HTTP.json('/proposalApi/getProposalStatus', params);\n  },\n  getProposalExportCount(params) {\n    // 导出检索\n    return HTTP.json('/proposalApi/proposalExportCount', params);\n  },\n  getProposalExport(params) {\n    // 导出提案指定文件\n    return HTTP.json('/proposalApi/proposalExport', params);\n  },\n  updateLeaderMark(params) {\n    // 设置领导批示提案\n    return HTTP.json('/proposalApi/updateLeaderMark', params);\n  },\n  proposalHistoryV2List(params) {\n    // 历史提案列表\n    return HTTP.json('/proposalHistoryV2/list', params);\n  },\n  proposalHistoryV2Dels(params) {\n    // 历史提案删除\n    return HTTP.json('/proposalHistoryV2/dels', params);\n  },\n  proposalHistoryV2Info(params) {\n    // 历史提案详情\n    return HTTP.json('/proposalHistoryV2/info', params);\n  },\n  proposalAutomaticEvaluation(params) {\n    // 自动评价\n    return HTTP.json('proposalApi/automaticEvaluation', params);\n  },\n  compareSimilarRates(params) {\n    // 文本相似度比对\n    return HTTP.json('/common/compareSimilarRates', params);\n  },\n  suggestionMeetingType(params) {\n    // 建议类型\n    return HTTP.json('/proposal/proposalMeetingType', params);\n  },\n  textComparison(params) {\n    // 文本对比\n    var formData = new FormData();\n    Object.keys(params).forEach(function (key) {\n      formData.append(key, params[key]);\n    });\n    return HTTP.post('/wordApi/textComparison', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n        'X-Custom-Header': 'your-header-value'\n      }\n    });\n  },\n  suggestionSortAndRenumberSerialNum(params) {\n    // 重新编号\n    return HTTP.json('/proposal/sortAndRenumberSerialNum', params);\n  }\n});\nexport default api;", "map": {"version": 3, "names": ["HTTP", "GlobalApi", "api", "_objectSpread", "userBatch", "params", "json", "cppccMemberInfo", "teamOfficeSelect", "tableHeadInfo", "get", "teamOfficeList", "teamOfficeInfo", "teamOfficeDel", "teamProposalUserList", "teamProposalUserInfo", "teamProposalUserDel", "suggestionOfficeList", "suggestionOfficeInfo", "suggestionOfficeDel", "suggestionOfficeUsingOrStop", "suggestionOfficeSelect", "suggestUnitUserList", "suggestUnitUserInfo", "suggestUnitUserDel", "suggestionTermYearList", "suggestionTermYearInfo", "suggestionTermYearDel", "suggestionThemeSelect", "suggestionThemeList", "suggestionThemeInfo", "suggestionThemeDel", "suggestionList", "suggestionInfo", "suggestionDetails", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "suggestionStatistics", "changeSerialNumber", "joinSubmiterList", "joinSubmiterAdd", "joinSubmiterDel", "suggestBySerialNumber", "suggestionAgree", "suggestionNextNodes", "suggestionComplete", "suggestionBatchComplete", "suggestionBatchCompleteUnit", "suggestionBatchPreAssignOffice", "handingPortionCommunicationList", "handingPortionCommunicationInfo", "handingPortionCommunicationDel", "prepareMembers", "portionSelect", "handingPortionDelayInfo", "handingPortionDelayVerifyDelay", "handingPortionAnswerList", "handingPortionAnswerInfo", "handingPortionAdjustList", "<PERSON><PERSON><PERSON><PERSON>", "suggestion<PERSON>pen", "<PERSON><PERSON><PERSON><PERSON>", "suggestionExcellent", "suggestionSatisfactionInfo", "suggestionCountSelector", "handingPortionAdjustRecord", "suggestionPress", "handlingPortionBatchConfirm", "handlingPortionConfirm", "handlingMassingEditAnswerStopDate", "handingPortionTraceList", "handingPortionTraceAdd", "handingPortionTraceInfo", "handingPortionTraceVerify", "proposalClueList", "proposalClueInfo", "proposalClueDel", "proposalClueUse", "mergeProposalList", "mergeProposalAdd", "mergeProposalDel", "proposalMergeSuggest", "noErrorTip", "similarity", "userJoin", "commonUnit", "commonType", "proposalClueTheme", "proposalClueThemeInfo", "clueWord", "reqProposalEmpty", "url", "reqProposalTheme", "reqFindOfficeProposal", "reqProposalBatchComplete", "handingPortionAnswerDel", "handingPortionAnswerEdit", "handingPortionAdjustOfficeInfo", "suggestionSatisfactionList", "suggestionSatisfactionDel", "suggestionSatisfactionEdit", "handingPortionTraceSelector", "suggestionSuperDetail", "handlingPortionList", "handlingPortionInfo", "handlingPortionDel", "handlingPortionEdit", "handlingPortionAdd", "handleOfficeReportList", "handleOfficeReportInfo", "handleOfficeReportDel", "handleOfficeReportFlush", "handleOfficeReportPress", "handleOfficeReportZip", "fileDownload", "getProposalYear", "proposalThemeSelect", "getProposalStatus", "getProposalExportCount", "getProposalExport", "updateLeaderMark", "proposalHistoryV2List", "proposalHistoryV2Dels", "proposalHistoryV2Info", "proposalAutomaticEvaluation", "compareSimilarRates", "suggestionMeetingType", "textComparison", "formData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "append", "post", "headers", "suggestionSortAndRenumberSerialNum"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/api/index.js"], "sourcesContent": ["// 导入封装的方法\r\nimport HTTP from 'common/http'\r\nimport GlobalApi from 'common/http/GlobalApi'\r\nconst api = {\r\n  ...GlobalApi,\r\n  userBatch (params) {\r\n    // 用户批量操作\r\n    return HTTP.json('/user/batch', params)\r\n  },\r\n  cppccMemberInfo (params) {\r\n    // 获取委员信息详情\r\n    return HTTP.json('/cppccMember/info', params)\r\n  },\r\n  teamOfficeSelect (params) {\r\n    // 获取集体提案单位下拉选\r\n    return HTTP.json('/teamOffice/selector', params)\r\n  },\r\n  tableHeadInfo (params) {\r\n    // 自定义表头 列详情\r\n    return HTTP.get(`/customColumn/info/${params}`)\r\n  },\r\n  teamOfficeList (params) {\r\n    // 集体办理单位列表\r\n    return HTTP.json('/teamOffice/list', params)\r\n  },\r\n  teamOfficeInfo (params) {\r\n    // 集体办理单位详情\r\n    return HTTP.json('/teamOffice/info', params)\r\n  },\r\n  teamOfficeDel (params) {\r\n    // 集体办理单位删除\r\n    return HTTP.json('/teamOffice/del', params)\r\n  },\r\n  teamProposalUserList (params) {\r\n    // 集体提案单位用户列表\r\n    return HTTP.json('/teamProposalUser/list', params)\r\n  },\r\n  teamProposalUserInfo (params) {\r\n    // 集体提案单位用户详情\r\n    return HTTP.json('/teamProposalUser/info', params)\r\n  },\r\n  teamProposalUserDel (params) {\r\n    // 集体提案单位用户删除\r\n    return HTTP.json('/teamProposalUser/del', params)\r\n  },\r\n  suggestionOfficeList (params) {\r\n    // 办理单位列表\r\n    return HTTP.json('/proposalOffice/list', params)\r\n  },\r\n  suggestionOfficeInfo (params) {\r\n    // 办理单位详情\r\n    return HTTP.json('/proposalOffice/info', params)\r\n  },\r\n  suggestionOfficeDel (params) {\r\n    // 办理单位删除\r\n    return HTTP.json('/proposalOffice/del', params)\r\n  },\r\n  suggestionOfficeUsingOrStop (params) {\r\n    // 办理单位启用禁用\r\n    return HTTP.json('/proposalOffice/usingOrStop', params)\r\n  },\r\n  suggestionOfficeSelect (params) {\r\n    return HTTP.json('/proposalOffice/select', params)\r\n  },\r\n  suggestUnitUserList (params) {\r\n    // 办理单位用户列表\r\n    return HTTP.json('/proposalOfficeUser/list', params)\r\n  },\r\n  suggestUnitUserInfo (params) {\r\n    // 办理单位用户详情\r\n    return HTTP.json('/proposalOfficeUser/info', params)\r\n  },\r\n  suggestUnitUserDel (params) {\r\n    // 办理单位用户删除\r\n    return HTTP.json('/proposalOfficeUser/dels', params)\r\n  },\r\n  suggestionTermYearList (params) {\r\n    // 届次编号列表\r\n    return HTTP.json('/proposalTermYear/list', params)\r\n  },\r\n  suggestionTermYearInfo (params) {\r\n    // 届次编号详情\r\n    return HTTP.json('/proposalTermYear/info', params)\r\n  },\r\n  suggestionTermYearDel (params) {\r\n    // 届次编号删除\r\n    return HTTP.json('/proposalTermYear/dels', params)\r\n  },\r\n  suggestionThemeSelect (params) {\r\n    // 提案分类列表\r\n    return HTTP.json('/proposalTheme/select', params)\r\n  },\r\n  suggestionThemeList (params) {\r\n    // 提案分类列表\r\n    return HTTP.json('/proposalTheme/tree', params)\r\n  },\r\n  suggestionThemeInfo (params) {\r\n    // 提案分类详情\r\n    return HTTP.json('/proposalTheme/info', params)\r\n  },\r\n  suggestionThemeDel (params) {\r\n    // 提案分类删除\r\n    return HTTP.json('/proposalTheme/dels', params)\r\n  },\r\n  suggestionList (params) {\r\n    // 提案列表\r\n    return HTTP.json('/proposal/list', params)\r\n  },\r\n  suggestionInfo (params) {\r\n    // 提案详情\r\n    return HTTP.json('/proposal/info', params)\r\n  },\r\n  suggestionDetails (params) {\r\n    // 提案流程详情\r\n    return HTTP.json('/proposal/getSuggestionFlow', params)\r\n  },\r\n  suggestionDel (params) {\r\n    // 提案删除\r\n    return HTTP.json('/proposal/dels', params)\r\n  },\r\n  suggestionWord (params) {\r\n    // 提案导出\r\n    return HTTP.json('/proposal/loadDocData', params)\r\n  },\r\n  suggestionStatistics (params) {\r\n    // 提案统计\r\n    return HTTP.json('/proposalStatistics/composite', params)\r\n  },\r\n  changeSerialNumber (params) {\r\n    // 提案编号对调\r\n    return HTTP.json('/proposal/changeSerialNumber', params)\r\n  },\r\n  joinSubmiterList (params) {\r\n    // 提案联名人列表\r\n    return HTTP.json('/cppcc/joinSubmiter/list', params)\r\n  },\r\n  joinSubmiterAdd (params) {\r\n    // 提案联名人新增\r\n    return HTTP.json('/cppcc/joinSubmiter/add', params)\r\n  },\r\n  joinSubmiterDel (params) {\r\n    // 提案联名人删除\r\n    return HTTP.json('/cppcc/joinSubmiter/dels', params)\r\n  },\r\n  suggestBySerialNumber (params) {\r\n    // 根据提案编号查询\r\n    return HTTP.json('/proposal/info/bySerialNumber', params)\r\n  },\r\n  suggestionAgree (params) {\r\n    // 同意联名\r\n    return HTTP.json('/proposal/agree', params)\r\n  },\r\n  suggestionNextNodes (params) {\r\n    // 提案流程操作项\r\n    return HTTP.json('/proposal/nextNodes', params)\r\n  },\r\n  suggestionComplete (params) {\r\n    // 处理任务节点\r\n    return HTTP.json('/proposal/complete', params)\r\n  },\r\n  suggestionBatchComplete (params) {\r\n    // 批量处理任务节点\r\n    return HTTP.json('/proposal/batchComplete', params)\r\n  },\r\n  suggestionBatchCompleteUnit (params) {\r\n    // 批量转给承办单位\r\n    return HTTP.json('/proposal/batchSubmitHandleOffice', params)\r\n  },\r\n  suggestionBatchPreAssignOffice (params) {\r\n    // 批量预交办\r\n    return HTTP.json('/proposal/batchPreAssignOffice', params)\r\n  },\r\n  handingPortionCommunicationList (params) {\r\n    // 沟通情况列表\r\n    return HTTP.json('/cppcc/handingPortionCommunication/list', params)\r\n  },\r\n  handingPortionCommunicationInfo (params) {\r\n    // 沟通情况详情\r\n    return HTTP.json('/cppcc/handingPortionCommunication/info', params)\r\n  },\r\n  handingPortionCommunicationDel (params) {\r\n    // 沟通情况删除\r\n    return HTTP.json('/cppcc/handingPortionCommunication/dels', params)\r\n  },\r\n  prepareMembers (params) {\r\n    // 提案沟通记录待选委员\r\n    return HTTP.json('/cppcc/handingPortionCommunication/prepareMembers', params)\r\n  },\r\n  portionSelect (params) {\r\n    // 提案沟通记录待选单位\r\n    return HTTP.json('/proposal/portionSelect', params)\r\n  },\r\n  handingPortionDelayInfo (params) {\r\n    // 延期记录详情\r\n    return HTTP.json('/cppcc/handingPortionDelay/info', params)\r\n  },\r\n  handingPortionDelayVerifyDelay (params) {\r\n    // 延期记录审查\r\n    return HTTP.json('/cppcc/handingPortionDelay/verify/delay', params)\r\n  },\r\n  handingPortionAnswerList (params) {\r\n    // 答复列表\r\n    return HTTP.json('/cppcc/handingPortionAnswer/list', params)\r\n  },\r\n  handingPortionAnswerInfo (params) {\r\n    // 答复详情\r\n    return HTTP.json('/cppcc/handingPortionAnswer/info', params)\r\n  },\r\n  handingPortionAdjustList (params) {\r\n    // 调整列表\r\n    return HTTP.json('/cppcc/handingPortionAdjust/list', params)\r\n  },\r\n  suggestionUnlock (params) {\r\n    // 解锁\r\n    return HTTP.json('/proposal/unlock', params)\r\n  },\r\n  suggestionOpen (params) {\r\n    // 批量公开\r\n    return HTTP.json('/proposal/batch/open', params)\r\n  },\r\n  suggestionMajor (params) {\r\n    // 批量设置重点提案\r\n    return HTTP.json('/proposal/batch/major', params)\r\n  },\r\n  suggestionExcellent (params) {\r\n    // 批量设置优秀提案\r\n    return HTTP.json('/proposal/signExcellent', params)\r\n  },\r\n  suggestionSatisfactionInfo (params) {\r\n    // 满意度详情\r\n    return HTTP.json('/proposalSatisfaction/info', params)\r\n  },\r\n  suggestionCountSelector (params) {\r\n    // 办理中提案统计\r\n    return HTTP.json('/proposal/countSelector', params)\r\n  },\r\n  handingPortionAdjustRecord (params) {\r\n    // 承办单位调整结果列表\r\n    return HTTP.json('/cppcc/handingPortionAdjust/record', params)\r\n  },\r\n  suggestionPress (params) {\r\n    // 催办提案\r\n    return HTTP.json('/proposal/press', params)\r\n  },\r\n  handlingPortionBatchConfirm (params) {\r\n    // 批量催办签收建议\r\n    return HTTP.json('/cppcc/handlingPortion/batchConfirm', params)\r\n  },\r\n  handlingPortionConfirm (params) {\r\n    // 签收建议\r\n    return HTTP.json('/cppcc/handlingPortion/confirm', params)\r\n  },\r\n  handlingMassingEditAnswerStopDate (params) {\r\n    // 修改答复截止时间\r\n    return HTTP.json('/cppcc/handlingMassing/editAnswerStopDate', params)\r\n  },\r\n  handingPortionTraceList (params) {\r\n    // 承办单位申请跟踪办理记录\r\n    return HTTP.json('/cppcc/handingPortionTrace/list', params)\r\n  },\r\n  handingPortionTraceAdd (params) {\r\n    // 承办单位申请跟踪办理记录\r\n    return HTTP.json('/cppcc/handingPortionTrace/add', params)\r\n  },\r\n  handingPortionTraceInfo (params) {\r\n    // 承办单位申请跟踪详情\r\n    return HTTP.json('/cppcc/handingPortionTrace/info', params)\r\n  },\r\n  handingPortionTraceVerify (params) {\r\n    // 承办单位申请跟踪审查\r\n    return HTTP.json('/cppcc/handingPortionTrace/verify', params)\r\n  },\r\n  proposalClueList (params) {\r\n    // 提案线索列表\r\n    return HTTP.json('/proposalClue/list', params)\r\n  },\r\n  proposalClueInfo (params) {\r\n    // 提案线索详情\r\n    return HTTP.json('/proposalClue/info', params)\r\n  },\r\n  proposalClueDel (params) {\r\n    // 提案线索删除\r\n    return HTTP.json('/proposalClue/dels', params)\r\n  },\r\n  proposalClueUse (params) {\r\n    // 提案线索引用\r\n    return HTTP.json('/proposalClue/use', params)\r\n  },\r\n  mergeProposalList (params) {\r\n    // 并案列表\r\n    return HTTP.json('/mergeProposal/list', params)\r\n  },\r\n  mergeProposalAdd (params) {\r\n    // 并案\r\n    return HTTP.json('/mergeProposal/add', params)\r\n  },\r\n  mergeProposalDel (params) {\r\n    // 取消并案\r\n    return HTTP.json('/mergeProposal/dels', params)\r\n  },\r\n  proposalMergeSuggest (params) {\r\n    // 智能并案\r\n    return HTTP.json('/hadoop_api/datax/proposal/mergeSuggest', params, { noErrorTip: true })\r\n  },\r\n  similarity (params) {\r\n    // 相似度查询\r\n    return HTTP.json('/summoner/pubApi/similarity', params, { noErrorTip: true })\r\n  },\r\n  userJoin (params) {\r\n    // 推荐用户\r\n    return HTTP.json('/common/suggestRecommendation', params, { noErrorTip: true })\r\n  },\r\n  commonUnit (params) {\r\n    // 推荐单位\r\n    return HTTP.json('/summoner/common/recommendHandleUnit', params, { noErrorTip: true })\r\n  },\r\n  commonType (params) {\r\n    // 推荐分类\r\n    return HTTP.json('/common/classify', params, { noErrorTip: true })\r\n  },\r\n  proposalClueTheme (params) {\r\n    // 提案文案线索列表\r\n    return HTTP.json('/proposalClueTheme/list', params)\r\n  },\r\n  proposalClueThemeInfo (params) {\r\n    // 提案线索详情\r\n    return HTTP.json('/proposalClueTheme/info', params)\r\n  },\r\n  clueWord (params) {\r\n    // 提案导出\r\n    return HTTP.json('/proposalClue/loadDocData', params)\r\n  },\r\n  reqProposalEmpty (url, params) {\r\n    // 获取提案分类列表 - empty: 查询未分类提案 notempty：查询已分类提案\r\n    return HTTP.json(`/proposal/findThemeProposal/${url}`, params)\r\n  },\r\n  reqProposalTheme (url, params) {\r\n    //批量操作 提案细分分类接口 - add：批量细分分类  clear：批量清空分类\r\n    return HTTP.json(`/proposal/batch/proposalTheme/${url}`, params)\r\n  },\r\n  reqFindOfficeProposal (url, params) {\r\n    // 获取提案办理单位细分列表 - empty: 查询未分类提案 notempty：查询已分类提案\r\n    return HTTP.json(`/proposal/findOfficeProposal/${url}`, params)\r\n  },\r\n  reqProposalBatchComplete (url, params) {\r\n    //批量操作 提案细分办理单位接口 - add：批量细分分类  clear：批量清空分类\r\n    return HTTP.json(`/proposal/batch/complete/${url}`, params)\r\n  },\r\n  handingPortionAnswerDel (params) {\r\n    // 答复删除\r\n    return HTTP.json('/cppcc/handingPortionAnswer/dels', params)\r\n  },\r\n  handingPortionAnswerEdit (params) {\r\n    // 修改答复\r\n    return HTTP.json('/cppcc/handingPortionAnswer/edit', params)\r\n  },\r\n  handingPortionAdjustOfficeInfo (params) {\r\n    // 办理单位详情\r\n    return HTTP.json('/cppcc/handingPortionAdjust/adjustOfficeInfo', params)\r\n  },\r\n  suggestionSatisfactionList (params) {\r\n    // 获取满意度测评列表\r\n    return HTTP.json('/proposalSatisfaction/list', params)\r\n  },\r\n  suggestionSatisfactionDel (params) {\r\n    // 满意度测评删除\r\n    return HTTP.json('/proposalSatisfaction/dels', params)\r\n  },\r\n  suggestionSatisfactionEdit (params) {\r\n    // 修改满意度测评\r\n    return HTTP.json('/proposalSatisfaction/edit', params)\r\n  },\r\n  handingPortionTraceSelector (params) {\r\n    // 选择下拉选传参承办单位申请跟踪审查\r\n    return HTTP.json('/cppcc/handingPortionTrace/selector', params)\r\n  },\r\n  suggestionSuperDetail (params) {\r\n    // 超级修改详情\r\n    return HTTP.json(`/proposal/superDetail`, params)\r\n  },\r\n  handlingPortionList (params) {\r\n    // 获取建议办理信息列表\r\n    return HTTP.json('/cppcc/handlingPortion/list', params)\r\n  },\r\n  handlingPortionInfo (params) {\r\n    // 建议办理信息详情\r\n    return HTTP.json('/cppcc/handlingPortion/info', params)\r\n  },\r\n  handlingPortionDel (params) {\r\n    // 建议办理信息删除\r\n    return HTTP.json('/cppcc/handlingPortion/dels', params)\r\n  },\r\n  handlingPortionEdit (params) {\r\n    // 修改建议办理信息\r\n    return HTTP.json('/cppcc/handlingPortion/edit', params)\r\n  },\r\n  handlingPortionAdd (params) {\r\n    // 新增建议办理信息\r\n    return HTTP.json('/cppcc/handlingPortion/add', params)\r\n  },\r\n  handleOfficeReportList (params) {\r\n    // 列表\r\n    return HTTP.json('/handleOfficeReport/list', params)\r\n  },\r\n  handleOfficeReportInfo (params) {\r\n    // 详情\r\n    return HTTP.json('/handleOfficeReport/info', params)\r\n  },\r\n  handleOfficeReportDel (params) {\r\n    // 删除\r\n    return HTTP.json('/handleOfficeReport/dels', params)\r\n  },\r\n  handleOfficeReportFlush (params) {\r\n    // 刷新\r\n    return HTTP.json('/handleOfficeReport/flush', params)\r\n  },\r\n  handleOfficeReportPress (params) {\r\n    // 催办\r\n    return HTTP.json('/handleOfficeReport/press', params)\r\n  },\r\n  handleOfficeReportZip (params) {\r\n    // 导出\r\n    return HTTP.fileDownload('/handleOfficeReport/attachmentZip', params)\r\n  },\r\n\r\n  getProposalYear (params) { // 配置届次\r\n    return HTTP.json('/customizeData/getProposalYear', params)\r\n  },\r\n  proposalThemeSelect (params) { // 提案分类\r\n    return HTTP.json('/proposalApi/proposalThemeSelect', params)\r\n  },\r\n  getProposalStatus (params) { // 提案状态\r\n    return HTTP.json('/proposalApi/getProposalStatus', params)\r\n  },\r\n  getProposalExportCount (params) { // 导出检索\r\n    return HTTP.json('/proposalApi/proposalExportCount', params)\r\n  },\r\n  getProposalExport (params) { // 导出提案指定文件\r\n    return HTTP.json('/proposalApi/proposalExport', params)\r\n  },\r\n  updateLeaderMark (params) { // 设置领导批示提案\r\n    return HTTP.json('/proposalApi/updateLeaderMark', params)\r\n  },\r\n  proposalHistoryV2List (params) { // 历史提案列表\r\n    return HTTP.json('/proposalHistoryV2/list', params)\r\n  },\r\n  proposalHistoryV2Dels (params) { // 历史提案删除\r\n    return HTTP.json('/proposalHistoryV2/dels', params)\r\n  },\r\n  proposalHistoryV2Info (params) { // 历史提案详情\r\n    return HTTP.json('/proposalHistoryV2/info', params)\r\n  },\r\n  proposalAutomaticEvaluation (params) { // 自动评价\r\n    return HTTP.json('proposalApi/automaticEvaluation', params)\r\n  },\r\n  compareSimilarRates (params) {\r\n    // 文本相似度比对\r\n    return HTTP.json('/common/compareSimilarRates', params)\r\n  },\r\n  suggestionMeetingType (params) {\r\n    // 建议类型\r\n    return HTTP.json('/proposal/proposalMeetingType', params)\r\n  },\r\n  textComparison (params) {\r\n    // 文本对比\r\n    const formData = new FormData()\r\n    Object.keys(params).forEach((key) => {\r\n      formData.append(key, params[key])\r\n    })\r\n    return HTTP.post('/wordApi/textComparison', formData, {\r\n      headers: { 'Content-Type': 'multipart/form-data', 'X-Custom-Header': 'your-header-value' }\r\n    })\r\n  },\r\n  suggestionSortAndRenumberSerialNum (params) {\r\n    // 重新编号\r\n    return HTTP.json('/proposal/sortAndRenumberSerialNum', params)\r\n  }\r\n}\r\nexport default api\r\n"], "mappings": ";;;;;AAAA;AACA,OAAOA,IAAI,MAAM,aAAa;AAC9B,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,IAAMC,GAAG,GAAAC,aAAA,CAAAA,aAAA,KACJF,SAAS;EACZG,SAASA,CAAEC,MAAM,EAAE;IACjB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,aAAa,EAAED,MAAM,CAAC;EACzC,CAAC;EACDE,eAAeA,CAAEF,MAAM,EAAE;IACvB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDG,gBAAgBA,CAAEH,MAAM,EAAE;IACxB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDI,aAAaA,CAAEJ,MAAM,EAAE;IACrB;IACA,OAAOL,IAAI,CAACU,GAAG,CAAC,sBAAsBL,MAAM,EAAE,CAAC;EACjD,CAAC;EACDM,cAAcA,CAAEN,MAAM,EAAE;IACtB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDO,cAAcA,CAAEP,MAAM,EAAE;IACtB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDQ,aAAaA,CAAER,MAAM,EAAE;IACrB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACDS,oBAAoBA,CAAET,MAAM,EAAE;IAC5B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDU,oBAAoBA,CAAEV,MAAM,EAAE;IAC5B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDW,mBAAmBA,CAAEX,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDY,oBAAoBA,CAAEZ,MAAM,EAAE;IAC5B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDa,oBAAoBA,CAAEb,MAAM,EAAE;IAC5B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDc,mBAAmBA,CAAEd,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDe,2BAA2BA,CAAEf,MAAM,EAAE;IACnC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACDgB,sBAAsBA,CAAEhB,MAAM,EAAE;IAC9B,OAAOL,IAAI,CAACM,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDiB,mBAAmBA,CAAEjB,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDkB,mBAAmBA,CAAElB,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDmB,kBAAkBA,CAAEnB,MAAM,EAAE;IAC1B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDoB,sBAAsBA,CAAEpB,MAAM,EAAE;IAC9B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDqB,sBAAsBA,CAAErB,MAAM,EAAE;IAC9B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDsB,qBAAqBA,CAAEtB,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,wBAAwB,EAAED,MAAM,CAAC;EACpD,CAAC;EACDuB,qBAAqBA,CAAEvB,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDwB,mBAAmBA,CAAExB,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDyB,mBAAmBA,CAAEzB,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACD0B,kBAAkBA,CAAE1B,MAAM,EAAE;IAC1B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACD2B,cAAcA,CAAE3B,MAAM,EAAE;IACtB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,gBAAgB,EAAED,MAAM,CAAC;EAC5C,CAAC;EACD4B,cAAcA,CAAE5B,MAAM,EAAE;IACtB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,gBAAgB,EAAED,MAAM,CAAC;EAC5C,CAAC;EACD6B,iBAAiBA,CAAE7B,MAAM,EAAE;IACzB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACD8B,aAAaA,CAAE9B,MAAM,EAAE;IACrB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,gBAAgB,EAAED,MAAM,CAAC;EAC5C,CAAC;EACD+B,cAAcA,CAAE/B,MAAM,EAAE;IACtB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDgC,oBAAoBA,CAAEhC,MAAM,EAAE;IAC5B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACDiC,kBAAkBA,CAAEjC,MAAM,EAAE;IAC1B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,8BAA8B,EAAED,MAAM,CAAC;EAC1D,CAAC;EACDkC,gBAAgBA,CAAElC,MAAM,EAAE;IACxB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDmC,eAAeA,CAAEnC,MAAM,EAAE;IACvB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDoC,eAAeA,CAAEpC,MAAM,EAAE;IACvB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDqC,qBAAqBA,CAAErC,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACDsC,eAAeA,CAAEtC,MAAM,EAAE;IACvB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACDuC,mBAAmBA,CAAEvC,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACDwC,kBAAkBA,CAAExC,MAAM,EAAE;IAC1B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDyC,uBAAuBA,CAAEzC,MAAM,EAAE;IAC/B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACD0C,2BAA2BA,CAAE1C,MAAM,EAAE;IACnC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,mCAAmC,EAAED,MAAM,CAAC;EAC/D,CAAC;EACD2C,8BAA8BA,CAAE3C,MAAM,EAAE;IACtC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACD4C,+BAA+BA,CAAE5C,MAAM,EAAE;IACvC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yCAAyC,EAAED,MAAM,CAAC;EACrE,CAAC;EACD6C,+BAA+BA,CAAE7C,MAAM,EAAE;IACvC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yCAAyC,EAAED,MAAM,CAAC;EACrE,CAAC;EACD8C,8BAA8BA,CAAE9C,MAAM,EAAE;IACtC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yCAAyC,EAAED,MAAM,CAAC;EACrE,CAAC;EACD+C,cAAcA,CAAE/C,MAAM,EAAE;IACtB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,mDAAmD,EAAED,MAAM,CAAC;EAC/E,CAAC;EACDgD,aAAaA,CAAEhD,MAAM,EAAE;IACrB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDiD,uBAAuBA,CAAEjD,MAAM,EAAE;IAC/B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiC,EAAED,MAAM,CAAC;EAC7D,CAAC;EACDkD,8BAA8BA,CAAElD,MAAM,EAAE;IACtC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yCAAyC,EAAED,MAAM,CAAC;EACrE,CAAC;EACDmD,wBAAwBA,CAAEnD,MAAM,EAAE;IAChC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kCAAkC,EAAED,MAAM,CAAC;EAC9D,CAAC;EACDoD,wBAAwBA,CAAEpD,MAAM,EAAE;IAChC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kCAAkC,EAAED,MAAM,CAAC;EAC9D,CAAC;EACDqD,wBAAwBA,CAAErD,MAAM,EAAE;IAChC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kCAAkC,EAAED,MAAM,CAAC;EAC9D,CAAC;EACDsD,gBAAgBA,CAAEtD,MAAM,EAAE;IACxB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,CAAC;EAC9C,CAAC;EACDuD,cAAcA,CAAEvD,MAAM,EAAE;IACtB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,sBAAsB,EAAED,MAAM,CAAC;EAClD,CAAC;EACDwD,eAAeA,CAAExD,MAAM,EAAE;IACvB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDyD,mBAAmBA,CAAEzD,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACD0D,0BAA0BA,CAAE1D,MAAM,EAAE;IAClC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACD2D,uBAAuBA,CAAE3D,MAAM,EAAE;IAC/B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACD4D,0BAA0BA,CAAE5D,MAAM,EAAE;IAClC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,oCAAoC,EAAED,MAAM,CAAC;EAChE,CAAC;EACD6D,eAAeA,CAAE7D,MAAM,EAAE;IACvB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,iBAAiB,EAAED,MAAM,CAAC;EAC7C,CAAC;EACD8D,2BAA2BA,CAAE9D,MAAM,EAAE;IACnC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qCAAqC,EAAED,MAAM,CAAC;EACjE,CAAC;EACD+D,sBAAsBA,CAAE/D,MAAM,EAAE;IAC9B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACDgE,iCAAiCA,CAAEhE,MAAM,EAAE;IACzC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,2CAA2C,EAAED,MAAM,CAAC;EACvE,CAAC;EACDiE,uBAAuBA,CAAEjE,MAAM,EAAE;IAC/B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiC,EAAED,MAAM,CAAC;EAC7D,CAAC;EACDkE,sBAAsBA,CAAElE,MAAM,EAAE;IAC9B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACDmE,uBAAuBA,CAAEnE,MAAM,EAAE;IAC/B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiC,EAAED,MAAM,CAAC;EAC7D,CAAC;EACDoE,yBAAyBA,CAAEpE,MAAM,EAAE;IACjC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,mCAAmC,EAAED,MAAM,CAAC;EAC/D,CAAC;EACDqE,gBAAgBA,CAAErE,MAAM,EAAE;IACxB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDsE,gBAAgBA,CAAEtE,MAAM,EAAE;IACxB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDuE,eAAeA,CAAEvE,MAAM,EAAE;IACvB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACDwE,eAAeA,CAAExE,MAAM,EAAE;IACvB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC/C,CAAC;EACDyE,iBAAiBA,CAAEzE,MAAM,EAAE;IACzB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACD0E,gBAAgBA,CAAE1E,MAAM,EAAE;IACxB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,oBAAoB,EAAED,MAAM,CAAC;EAChD,CAAC;EACD2E,gBAAgBA,CAAE3E,MAAM,EAAE;IACxB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qBAAqB,EAAED,MAAM,CAAC;EACjD,CAAC;EACD4E,oBAAoBA,CAAE5E,MAAM,EAAE;IAC5B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yCAAyC,EAAED,MAAM,EAAE;MAAE6E,UAAU,EAAE;IAAK,CAAC,CAAC;EAC3F,CAAC;EACDC,UAAUA,CAAE9E,MAAM,EAAE;IAClB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,EAAE;MAAE6E,UAAU,EAAE;IAAK,CAAC,CAAC;EAC/E,CAAC;EACDE,QAAQA,CAAE/E,MAAM,EAAE;IAChB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,EAAE;MAAE6E,UAAU,EAAE;IAAK,CAAC,CAAC;EACjF,CAAC;EACDG,UAAUA,CAAEhF,MAAM,EAAE;IAClB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,sCAAsC,EAAED,MAAM,EAAE;MAAE6E,UAAU,EAAE;IAAK,CAAC,CAAC;EACxF,CAAC;EACDI,UAAUA,CAAEjF,MAAM,EAAE;IAClB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kBAAkB,EAAED,MAAM,EAAE;MAAE6E,UAAU,EAAE;IAAK,CAAC,CAAC;EACpE,CAAC;EACDK,iBAAiBA,CAAElF,MAAM,EAAE;IACzB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDmF,qBAAqBA,CAAEnF,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDoF,QAAQA,CAAEpF,MAAM,EAAE;IAChB;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACDqF,gBAAgBA,CAAEC,GAAG,EAAEtF,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+BqF,GAAG,EAAE,EAAEtF,MAAM,CAAC;EAChE,CAAC;EACDuF,gBAAgBA,CAAED,GAAG,EAAEtF,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiCqF,GAAG,EAAE,EAAEtF,MAAM,CAAC;EAClE,CAAC;EACDwF,qBAAqBA,CAAEF,GAAG,EAAEtF,MAAM,EAAE;IAClC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgCqF,GAAG,EAAE,EAAEtF,MAAM,CAAC;EACjE,CAAC;EACDyF,wBAAwBA,CAAEH,GAAG,EAAEtF,MAAM,EAAE;IACrC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4BqF,GAAG,EAAE,EAAEtF,MAAM,CAAC;EAC7D,CAAC;EACD0F,uBAAuBA,CAAE1F,MAAM,EAAE;IAC/B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kCAAkC,EAAED,MAAM,CAAC;EAC9D,CAAC;EACD2F,wBAAwBA,CAAE3F,MAAM,EAAE;IAChC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,kCAAkC,EAAED,MAAM,CAAC;EAC9D,CAAC;EACD4F,8BAA8BA,CAAE5F,MAAM,EAAE;IACtC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,8CAA8C,EAAED,MAAM,CAAC;EAC1E,CAAC;EACD6F,0BAA0BA,CAAE7F,MAAM,EAAE;IAClC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACD8F,yBAAyBA,CAAE9F,MAAM,EAAE;IACjC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACD+F,0BAA0BA,CAAE/F,MAAM,EAAE;IAClC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACDgG,2BAA2BA,CAAEhG,MAAM,EAAE;IACnC;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,qCAAqC,EAAED,MAAM,CAAC;EACjE,CAAC;EACDiG,qBAAqBA,CAAEjG,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACnD,CAAC;EACDkG,mBAAmBA,CAAElG,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACDmG,mBAAmBA,CAAEnG,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACDoG,kBAAkBA,CAAEpG,MAAM,EAAE;IAC1B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACDqG,mBAAmBA,CAAErG,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACDsG,kBAAkBA,CAAEtG,MAAM,EAAE;IAC1B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,4BAA4B,EAAED,MAAM,CAAC;EACxD,CAAC;EACDuG,sBAAsBA,CAAEvG,MAAM,EAAE;IAC9B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDwG,sBAAsBA,CAAExG,MAAM,EAAE;IAC9B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACDyG,qBAAqBA,CAAEzG,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,0BAA0B,EAAED,MAAM,CAAC;EACtD,CAAC;EACD0G,uBAAuBA,CAAE1G,MAAM,EAAE;IAC/B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACD2G,uBAAuBA,CAAE3G,MAAM,EAAE;IAC/B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,2BAA2B,EAAED,MAAM,CAAC;EACvD,CAAC;EACD4G,qBAAqBA,CAAE5G,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACkH,YAAY,CAAC,mCAAmC,EAAE7G,MAAM,CAAC;EACvE,CAAC;EAED8G,eAAeA,CAAE9G,MAAM,EAAE;IAAE;IACzB,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACD+G,mBAAmBA,CAAE/G,MAAM,EAAE;IAAE;IAC7B,OAAOL,IAAI,CAACM,IAAI,CAAC,kCAAkC,EAAED,MAAM,CAAC;EAC9D,CAAC;EACDgH,iBAAiBA,CAAEhH,MAAM,EAAE;IAAE;IAC3B,OAAOL,IAAI,CAACM,IAAI,CAAC,gCAAgC,EAAED,MAAM,CAAC;EAC5D,CAAC;EACDiH,sBAAsBA,CAAEjH,MAAM,EAAE;IAAE;IAChC,OAAOL,IAAI,CAACM,IAAI,CAAC,kCAAkC,EAAED,MAAM,CAAC;EAC9D,CAAC;EACDkH,iBAAiBA,CAAElH,MAAM,EAAE;IAAE;IAC3B,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACDmH,gBAAgBA,CAAEnH,MAAM,EAAE;IAAE;IAC1B,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACDoH,qBAAqBA,CAAEpH,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDqH,qBAAqBA,CAAErH,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDsH,qBAAqBA,CAAEtH,MAAM,EAAE;IAAE;IAC/B,OAAOL,IAAI,CAACM,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;EACrD,CAAC;EACDuH,2BAA2BA,CAAEvH,MAAM,EAAE;IAAE;IACrC,OAAOL,IAAI,CAACM,IAAI,CAAC,iCAAiC,EAAED,MAAM,CAAC;EAC7D,CAAC;EACDwH,mBAAmBA,CAAExH,MAAM,EAAE;IAC3B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,6BAA6B,EAAED,MAAM,CAAC;EACzD,CAAC;EACDyH,qBAAqBA,CAAEzH,MAAM,EAAE;IAC7B;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,+BAA+B,EAAED,MAAM,CAAC;EAC3D,CAAC;EACD0H,cAAcA,CAAE1H,MAAM,EAAE;IACtB;IACA,IAAM2H,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BC,MAAM,CAACC,IAAI,CAAC9H,MAAM,CAAC,CAAC+H,OAAO,CAAC,UAACC,GAAG,EAAK;MACnCL,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAEhI,MAAM,CAACgI,GAAG,CAAC,CAAC;IACnC,CAAC,CAAC;IACF,OAAOrI,IAAI,CAACuI,IAAI,CAAC,yBAAyB,EAAEP,QAAQ,EAAE;MACpDQ,OAAO,EAAE;QAAE,cAAc,EAAE,qBAAqB;QAAE,iBAAiB,EAAE;MAAoB;IAC3F,CAAC,CAAC;EACJ,CAAC;EACDC,kCAAkCA,CAAEpI,MAAM,EAAE;IAC1C;IACA,OAAOL,IAAI,CAACM,IAAI,CAAC,oCAAoC,EAAED,MAAM,CAAC;EAChE;AAAC,EACF;AACD,eAAeH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}