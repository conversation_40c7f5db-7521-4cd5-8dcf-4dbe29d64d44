<template>
  <div class="CollectiveProposalUnit">
    <xyl-search-button @queryClick="handleQuery"
                       @resetClick="handleReset"
                       @handleButton="handleButton"
                       :buttonList="buttonList">
      <template #search>
        <el-input v-model="keyword"
                  placeholder="请输入关键词"
                  @keyup.enter="handleQuery"
                  clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef"
                row-key="id"
                :data="tableData"
                @select="handleTableSelect"
                @select-all="handleTableSelect">
        <el-table-column type="selection"
                         reserve-selection
                         width="60"
                         fixed />
        <el-table-column label="序号"
                         width="80"
                         prop="sort" />
        <el-table-column label="单位名称"
                         min-width="220"
                         prop="name"
                         show-overflow-tooltip />
        <el-table-column label="类型"
                         min-width="220"
                         prop="teamOfficeTheme"
                         show-overflow-tooltip>
          <template #default="scope">
            <span>
              {{ scope.row.teamOfficeTheme.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="联系人"
                         min-width="160"
                         prop="contactUser"
                         show-overflow-tooltip />
        <el-table-column label="联系电话"
                         min-width="160"
                         prop="contactPhone"
                         show-overflow-tooltip />
        <xyl-global-table-button :data="tableButtonList"
                                 @buttonClick="handleCommand"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo"
                     v-model:page-size="pageSize"
                     :page-sizes="pageSizes"
                     layout="total, sizes, prev, pager, next, jumper"
                     @size-change="handleQuery"
                     @current-change="handleQuery"
                     :total="totals"
                     background />
    </div>
    <xyl-popup-window v-model="exportShow"
                      name="导出Excel">
      <xyl-export-excel name="办理单位管理"
                        :exportId="exportId"
                        :params="exportParams"
                        module="suggestionOfficeExportExcel"
                        @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="importShow"
                      name="Excel导入">
      <xyl-import-excel name="办理单位管理"
                        type="proposalOfficeImportExcel"
                        @callback="callback"></xyl-import-excel>
    </xyl-popup-window>
    <xyl-popup-window v-model="show"
                      :name="id ? '编辑单位' : '新增单位'">
      <SubmitCollectiveProposalUnit :id="id"
                                    @callback="callback"></SubmitCollectiveProposalUnit>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'CollectiveProposalUnit' }
</script>
<script setup>
import { ref, onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import SubmitCollectiveProposalUnit from './component/SubmitCollectiveProposalUnit'
const buttonList = [
  { id: 'new', name: '新增', type: 'primary', has: 'new' },
  // { id: 'import', name: 'Excel导入', type: 'primary', has: 'import' },
  // { id: 'export', name: '导出Excel', type: 'primary', has: 'export' },
  { id: 'del', name: '删除', type: '', has: 'del' }
]
const tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: 'edit' }]
const id = ref('')
const show = ref(false)
const importShow = ref(false)
const {
  keyword,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  handleTableSelect,
  handleDel,
  tableRefReset,
  handleExportExcel
} = GlobalTable({ tableApi: 'teamOfficeList', delApi: 'teamOfficeDel' })

onActivated(() => { handleQuery() })

const handleButton = (id) => {
  switch (id) {
    case 'new':
      handleNew()
      break
    case 'import':
      importShow.value = true
      break
    case 'export':
      handleExportExcel()
      break
    case 'del':
      handleDel('单位')
      break
    default:
      break
  }
}
const handleCommand = (row, isType) => {
  switch (isType) {
    case 'edit':
      handleEdit(row)
      break
    default:
      break
  }
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleNew = () => {
  id.value = ''
  show.value = true
}
const handleEdit = (item) => {
  id.value = item.id
  show.value = true
}
const callback = () => {
  tableRefReset()
  handleQuery()
  show.value = false
  exportShow.value = false
  importShow.value = false
}
</script>
<style lang="scss">
.CollectiveProposalUnit {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(
      100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px)
    );
  }
}
</style>
