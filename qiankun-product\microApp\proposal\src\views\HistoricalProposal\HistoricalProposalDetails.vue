<template>
  <div class="HistoricalProposalDetails">
    <div class="HistoricalProposalDetailsName">详情</div>
    <global-info>
      <global-info-line>
        <global-info-item label="届次">{{ details.termYearId }}</global-info-item>
        <global-info-item label="案号">{{ details.serialNumber }}</global-info-item>
      </global-info-line>
      <global-info-item label="标题">{{ details.title }}</global-info-item>
      <global-info-line>
        <global-info-item label="分类">{{ details.type }}</global-info-item>
        <global-info-item label="提案者">{{ details.suggestUserId }}</global-info-item>
      </global-info-line>
      <global-info-line>
        <global-info-item label="党派">{{ details.party }}</global-info-item>
        <global-info-item label="界别">{{ details.circles }}</global-info-item>
      </global-info-line>
      <global-info-line>
        <global-info-item label="联名人">{{ details.joinProposal }}</global-info-item>
        <global-info-item label="提交时间">{{ format(details.submitDate, 'YYYY-MM-DD') }}</global-info-item>
      </global-info-line>
      <global-info-item label="内容">
        <div class="content" v-html="details.content"></div>
      </global-info-item>
      <global-info-item label="附件">
        <xyl-global-file :fileData="details.fileInfoList"></xyl-global-file>
      </global-info-item>
      <global-info-line>
        <global-info-item label="状态">{{ details.processStatus }}</global-info-item>
        <global-info-item label="不予立案理由">{{ details.notRegisteredReasonName }}</global-info-item>
      </global-info-line>
      <global-info-line>
        <global-info-item label="办理方式">{{ details.handlingMethod }}</global-info-item>
        <global-info-item label="办理单位">{{ details.handlingUnit }}</global-info-item>
      </global-info-line>
      <global-info-item label="答复文件">
        <xyl-global-file :fileData="details.replyFileInfoList"></xyl-global-file>
      </global-info-item>
      <global-info-item label="办理情况" v-if="false">{{ details.handlingContent }}</global-info-item>
      <global-info-line>
        <global-info-item label="提案评价" v-if="false">{{ details.unitEvaluationName }}</global-info-item>
        <global-info-item label="征询意见表满意度" v-if="false">{{ details.memberEvaluationName }}</global-info-item>
      </global-info-line>
    </global-info>
  </div>
</template>
<script>
export default { name: 'HistoricalProposalDetails' }
</script>
<script setup>
import api from '@/api';
import { ref, onMounted } from 'vue'
import { format } from 'common/js/time.js'
const props = defineProps({ id: { type: String, default: '' } })

const details = ref({})

onMounted(() => {
  getInfo()
})

const getInfo = async () => {
  const res = await api.proposalHistoryV2Info({ detailId: props.id })
  details.value = res.data
}
</script>
<style lang="scss">
.HistoricalProposalDetails {
  width: 820px;
  padding: 40px;
  padding-top: 0;

  .HistoricalProposalDetailsName {
    font-size: var(--zy-title-font-size);
    font-weight: bold;
    color: var(--zy-el-color-primary);
    border-bottom: 1px solid var(--zy-el-color-primary);
    text-align: center;
    padding: 20px 0;
    margin-bottom: 20px;
  }

  .content {
    padding: 20px 0;
    overflow: hidden;
    line-height: var(--zy-line-height);

    img,
    video {
      max-width: 100%;
    }
  }
}
</style>
