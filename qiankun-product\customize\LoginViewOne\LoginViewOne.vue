<template>
  <div class="LoginViewOne">
    <div class="LoginViewOneBox">
      <div class="LoginViewOneLogo">
        <el-image :src="systemLogo" fit="cover" />
      </div>
      <div class="LoginViewOneName" v-html="loginSystemName"></div>
      <el-form ref="LoginForm" :model="form" :rules="rules" class="LoginViewOneForm">
        <el-form-item prop="account">
          <el-input v-model="form.account" placeholder="账号/手机号" @blur="handleBlur" clearable />
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" v-model="form.password" placeholder="密码" show-password clearable />
        </el-form-item>
        <el-form-item class="smsValidation" v-if="loginVerifyShow && whetherVerifyCode" prop="verifyCode">
          <el-input v-model="form.verifyCode" placeholder="短信验证码" clearable></el-input>
          <el-button type="primary" @click="handleGetVerifyCode" :disabled="countDownText != '获取验证码'">
            {{ countDownText }}
          </el-button>
        </el-form-item>
        <div class="LoginViewOneSlideVerify" v-if="loginVerifyShow && !whetherVerifyCode">
          <xyl-slide-verify ref="slideVerify" @again="onAgain" @success="onSuccess" :disabled="disabled" />
        </div>
        <div class="LoginViewOneFormOperation">
          <el-checkbox v-model="checked">记住用户名和密码</el-checkbox>
          <div class="LoginViewOneFormOperationText" @click="show = !show">忘记密码？</div>
        </div>
        <el-button type="primary" @click="submitForm(LoginForm)" class="LoginViewOneFormButton" :loading="loading"
          :disabled="loginDisabled">
          {{ loading ? '登录中' : '登录' }}
        </el-button>
      </el-form>
      <div class="LoginViewOneOperation" v-if="appDownloadUrl">
        <div class="LoginViewOneOperationBox">
          <el-popover placement="top" width="auto" @show="refresh" @hide="hideQrcode">
            <div class="LoginViewOneQrCodeBox">
              <div class="LoginViewOneQrCodeNameBody">
                <div class="LoginViewOneQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="LoginViewOneQrCodeName">APP扫码登录</div>
              </div>
              <div class="LoginViewOneQrCodeRefreshBody">
                <qrcode-vue :value="loginQrcode" :size="120" />
                <div class="LoginViewOneQrCodeRefresh" v-show="loginQrcodeShow">
                  <el-button type="primary" @click="refresh">刷新</el-button>
                </div>
              </div>
              <div class="LoginViewOneQrCodeText">请使用{{ systemName }}APP扫码登录</div>
            </div>
            <template #reference>
              <div class="LoginViewOneQrCode"></div>
            </template>
          </el-popover>
          <div class="LoginViewOneOperationText">APP扫码登录</div>
        </div>
        <div class="LoginViewOneOperationBox">
          <el-popover placement="top" width="auto">
            <div class="LoginViewOneQrCodeBox">
              <div class="LoginViewOneQrCodeNameBody">
                <div class="LoginViewOneQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="LoginViewOneQrCodeName">手机APP下载</div>
              </div>
              <qrcode-vue :value="appDownloadUrl" :size="120" />
              <div class="LoginViewOneQrCodeText">使用其他软件扫码下载{{ systemName }}APP</div>
            </div>
            <template #reference>
              <div class="LoginViewOneApp"></div>
            </template>
          </el-popover>
          <div class="LoginViewOneOperationText">手机APP下载</div>
        </div>
      </div>
      <div class="LoginViewOneSystemTips" v-if="systemLoginContact">{{ systemLoginContact }}</div>
    </div>
    <xyl-popup-window v-model="show" name="重置密码">
      <ResetPassword @callback="show = !show"></ResetPassword>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'LoginViewOne' }
</script>
<script setup>
import { ref, onMounted, computed, defineAsyncComponent } from 'vue'
import {
  systemLogo,
  systemName,
  platformAreaName,
  loginNameLineFeedPosition,
  appDownloadUrl,
  systemLoginContact
} from 'common/js/system_var.js'
import { LoginView } from '../LoginView/LoginView.js'
const QrcodeVue = defineAsyncComponent(() => import('qrcode.vue'))
const ResetPassword = defineAsyncComponent(() => import('../LoginView/component/ResetPassword.vue'))
const show = ref(false)
const loginSystemName = computed(() => {
  const name = (platformAreaName.value || '') + systemName.value
  const num = Number(loginNameLineFeedPosition.value || '0') || 0
  return num ? name.substring(0, num) + '\n' + name.substring(num) : name
})
const {
  loginVerifyShow,
  whetherVerifyCode,
  loginDisabled,
  loading,
  checked,
  LoginForm,
  form,
  rules,
  countDownText,
  slideVerify,
  disabled,
  loginQrcode,
  loginQrcodeShow,
  handleBlur,
  handleGetVerifyCode,
  onAgain,
  onSuccess,
  submitForm,
  loginInfo,
  refresh,
  hideQrcode
} = LoginView('/LoginViewOne')
onMounted(() => {
  loginInfo()
})
</script>
<style lang="scss">
.LoginViewOne {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: var(--zy-el-color-primary);
  }

  .LoginViewOneBox {
    padding: var(--zy-distance-one);
    box-shadow: var(--zy-el-box-shadow);
    padding-bottom: var(--zy-distance-two);
    border-radius: var(--el-border-radius-base);
    background: #fff;
    position: relative;
    z-index: 2;

    .LoginViewOneLogo {
      width: 60px;
      margin: auto;
      margin-bottom: var(--zy-distance-two);

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .LoginViewOneName {
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: var(--zy-system-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      letter-spacing: 2px;
      padding-bottom: var(--zy-distance-one);
      white-space: pre-wrap;
      margin: auto;
    }

    .LoginViewOneForm {
      width: 320px;
      margin: auto;
      padding-bottom: var(--zy-distance-one);

      input:-webkit-autofill {
        transition: background-color 5000s ease-in-out 0s;
      }

      .zy-el-form-item {
        margin-bottom: var(--zy-form-distance-bottom);
      }

      .LoginViewOneFormButton {
        width: 100%;
      }

      .smsValidation {
        .zy-el-form-item__content {
          display: flex;
          justify-content: space-between;
        }

        .zy-el-input {
          width: 56%;
        }
      }

      .LoginViewOneSlideVerify {
        margin-bottom: var(--zy-distance-five);
      }

      .LoginViewOneFormOperation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--zy-distance-three);

        .zy-el-checkbox {
          height: var(--zy-height-secondary);
        }

        .LoginViewOneFormOperationText {
          cursor: pointer;
          color: var(--zy-el-color-primary);
          font-size: var(--zy-text-font-size);
        }
      }
    }

    .LoginViewOneOperation {
      width: 100%;
      padding-bottom: var(--zy-distance-two);
      display: flex;
      justify-content: space-between;

      .LoginViewOneOperationBox {
        margin: 0 var(--zy-distance-two);
        cursor: pointer;

        .LoginViewOneQrCode {
          width: 50px;
          height: 50px;
          background: url('../img/login_qr_code.png');
          background-size: 100% 100%;
          margin: auto;
        }

        .LoginViewOneApp {
          width: 50px;
          height: 50px;
          background: url('../img/login_app.png') no-repeat;
          background-size: auto 100%;
          background-position: center;
          margin: auto;
        }

        .LoginViewOneOperationText {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          padding: var(--el-border-radius-small) 0;
          text-align: center;
        }
      }
    }

    .LoginViewOneForm+.LoginViewOneSystemTips {
      padding-top: var(--zy-distance-one);
    }

    .LoginViewOneSystemTips {
      color: var(--zy-el-text-color-secondary);
      font-size: var(--zy-text-font-size);
      text-align: center;
    }
  }
}

.LoginViewOneQrCodeBox {
  width: 320px;
  background-color: #fff;

  canvas {
    display: block;
    margin: auto;
  }

  .LoginViewOneQrCodeNameBody {
    padding: var(--zy-distance-three);
    display: flex;
    align-items: center;
    justify-content: center;

    .LoginViewOneQrCodeLogo {
      width: 26px;
      margin-right: 6px;

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .LoginViewOneQrCodeName {
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
    }
  }

  .LoginViewOneQrCodeRefreshBody {
    position: relative;

    .LoginViewOneQrCodeRefresh {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 120px;
      background-color: rgba(000, 000, 000, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;

      .zy-el-button {
        --zy-el-button-size: var(--zy-height-secondary);
      }
    }
  }

  .LoginViewOneQrCodeText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-three);
    color: var(--zy-el-color-primary);
  }
}
</style>
