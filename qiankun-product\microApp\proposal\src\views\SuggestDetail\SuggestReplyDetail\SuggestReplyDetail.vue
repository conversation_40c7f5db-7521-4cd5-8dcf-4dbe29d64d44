<template>
  <div class="SuggestReplyDetail">
    <div class="SuggestReplyDetailName">答复文件详情</div>
    <global-info>
      <global-info-line>
        <global-info-item label="办理单位">{{ details.handleOfficeName }}</global-info-item>
        <global-info-item label="答复类型">{{ details.suggestionAnswerType?.label }}</global-info-item>
      </global-info-line>
      <global-info-line>
        <global-info-item label="提案评价">{{ evaluate[extDatas.proposalEvaluation] }}</global-info-item>
        <global-info-item label="征询意见表满意度">{{ satisfaction[extDatas.satisfactionEvaluation] }}</global-info-item>
      </global-info-line>
      <global-info-item label="答复件红头文件">
        <xyl-global-file :fileData="extDatas.attachmentsRed"></xyl-global-file>
      </global-info-item>
      <global-info-item label="答复件Word版">
        <xyl-global-file :fileData="details.attachments"></xyl-global-file>
      </global-info-item>
      <global-info-item label="征询意见表扫描件">
        <xyl-global-file :fileData="extDatas.attachmentsOpinion"></xyl-global-file>
      </global-info-item>
      <!-- <global-info-item label="答复内容">
        <div v-html="details.content"></div>
      </global-info-item> -->
    </global-info>
  </div>
</template>
<script>
export default { name: 'SuggestReplyDetail' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
const props = defineProps({ id: { type: String, default: '' } })

const details = ref({})
const extDatas = ref({})
// 提案评价
const evaluate = {
  1: '优秀',
  2: '良好',
  3: '一般'
}
// 满意度
const satisfaction = {
  1: '满意',
  2: '基本满意',
  3: '不满意'
}
onMounted(() => { handingPortionAnswerInfo() })

const handingPortionAnswerInfo = async () => {
  const { data, extData } = await api.handingPortionAnswerInfo({ detailId: props.id })
  details.value = data
  extDatas.value = extData.answersFileExt
}
</script>
<style lang="scss">
.SuggestReplyDetail {
  width: 990px;
  padding: 0 var(--zy-distance-one);
  padding-top: var(--zy-distance-one);

  .SuggestReplyDetailName {
    font-size: var(--zy-title-font-size);
    font-weight: bold;
    color: var(--zy-el-color-primary);
    border-bottom: 1px solid var(--zy-el-color-primary);
    text-align: center;
    padding: 20px 0;
    margin-bottom: 20px;
  }

  .global-info {
    padding-bottom: 12px;

    .global-info-item {
      .global-info-label {
        width: 160px;
      }

      .global-info-content {
        width: calc(100% - 160px);
      }
    }
  }
}
</style>
