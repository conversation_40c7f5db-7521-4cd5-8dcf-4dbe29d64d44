<template>
  <el-popover v-model:visible="show" trigger="click" placement="bottom-start" popper-class="GlobalAiChatHistoryPopover"
    transition="zy-el-zoom-in-top">
    <template #reference>
      <div class="GlobalAiChatDialogueHistory"><span v-html="historyIcon"></span></div>
    </template>
    <el-scrollbar ref="scrollRef" class="GlobalAiChatHistoryScrollbar" @scroll="handleScroll">
      <div class="GlobalAiChatHistoryScroll">
        <el-tree ref="treeRef" node-key="id" highlight-current :data="tableData"
          :props="{ children: 'children', label: 'userQuestion' }" @node-click="handleNodeClick"
          v-if="tableData.length" />
        <div class="GlobalAiChatHistoryLoadingText" v-if="loading">加载中...</div>
        <div class="GlobalAiChatHistoryLoadingText" v-if="isShow">没有更多了</div>
      </div>
    </el-scrollbar>
  </el-popover>
</template>
<script>
export default { name: 'GlobalAiChatHistory' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, nextTick, watch } from 'vue'
const props = defineProps({
  modelValue: [String, Number]
})
const emit = defineEmits(['update:modelValue', 'select'])
const historyIcon =
  '<svg t="1741231838961" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6487" width="26" height="26"><path d="M896 234.666667H128V149.333333h768v85.333334zM341.333333 661.333333H128v-85.333333h213.333333v85.333333zM426.666667 448H128v-85.333333h298.666667v85.333333zM426.666667 874.666667H128v-85.333334h298.666667v85.333334zM682.666667 448a170.666667 170.666667 0 1 0 0 341.333333 170.666667 170.666667 0 0 0 0-341.333333z m-256 170.666667c0-141.376 114.624-256 256-256s256 114.624 256 256-114.624 256-256 256-256-114.624-256-256z" fill="#000000" p-id="6488"></path><path d="M725.333333 490.666667v128h-85.333333v-128h85.333333z" fill="#000000" p-id="6489"></path><path d="M810.666667 682.666667h-170.666667v-85.333334h170.666667v85.333334z" fill="#000000" p-id="6490"></path></svg>'
const scrollRef = ref()
const loadingScroll = ref(false)
const treeRef = ref()
const show = ref(false)
const pageNo = ref(1)
const pageSize = ref(10)
const totals = ref(0)
const isShow = ref(false)
const loading = ref(true)
const tableId = ref('')
const tableInfo = ref({})
const tableData = ref([])
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

onMounted(() => {
  aigptChatClusterList()
})
const handleScroll = ({ scrollTop }) => {
  if (!scrollRef.value) return
  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef
  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {
    load()
  }
}
const load = () => {
  if (pageNo.value * pageSize.value >= totals.value) return
  loadingScroll.value = true
  pageNo.value += 1
  aigptChatClusterList()
}
const aigptChatClusterList = async () => {
  const { data, total } = await api.aigptChatClusterList({
    key: guid(),
    pageNo: pageNo.value,
    pageSize: pageSize.value
  })
  tableData.value = [...tableData.value, ...data]
  totals.value = total
  loading.value = pageNo.value * pageSize.value < totals.value
  isShow.value = pageNo.value * pageSize.value >= totals.value
  selectedMethods(tableData.value, tableId.value)
  loadingScroll.value = false
}
const handleNodeClick = (data) => {
  show.value = false
  emit('update:modelValue', data.id)
}
// 首次进来默认选中
const selectedMethods = (data, id, type) => {
  data.forEach((item) => {
    if (item.id === id) {
      if (JSON.stringify(tableInfo.value) !== JSON.stringify(item)) {
        tableInfo.value = item
        emit('select', tableInfo.value, type)
        nextTick(() => {
          treeRef.value.setCurrentKey(id)
        })
      } else {
        nextTick(() => {
          treeRef.value.setCurrentKey(id)
        })
      }
    }
    if (item.children && item.children.length) selectedMethods(item.children, id, type)
  })
}
const handleCurrentChat = async (code, id) => {
  const { data } = await api.aigptChatClusterList({
    key: guid(),
    pageNo: 1,
    pageSize: 1,
    query: { chatBusinessScene: code, businessId: id || null }
  })
  if (data.length) {
    emit('update:modelValue', data[0].id)
    tableInfo.value = data[0]
    emit('select', tableInfo.value)
    nextTick(() => {
      treeRef.value.setCurrentKey(data[0].id)
    })
  }
}
const handleRefresh = async () => {
  const { data, total } = await api.aigptChatClusterList({ pageNo: 1, pageSize: tableData.value.length })
  tableData.value = data
  totals.value = total
  loading.value = pageNo.value * pageSize.value < totals.value
  isShow.value = pageNo.value * pageSize.value >= totals.value
  selectedMethods(tableData.value, tableId.value, true)
}
watch(
  () => props.modelValue,
  () => {
    tableId.value = props.modelValue
    if (tableId.value !== tableInfo.value.id) tableInfo.value = {}
    nextTick(() => {
      treeRef.value?.setCurrentKey(null)
    })
    selectedMethods(tableData.value, tableId.value, false)
  },
  { immediate: true }
)
defineExpose({ refresh: handleRefresh, handleCurrentChat })
</script>
<style lang="scss">
.GlobalAiChatHistoryPopover {
  width: 320px !important;
  padding: 0 !important;

  .GlobalAiChatHistoryScrollbar {
    width: 100%;
    height: 320px;

    .zy-el-tree-node {
      width: 100%;

      .zy-el-tree-node__content {
        width: 100%;
        height: var(--zy-height);

        .zy-el-tree-node__label {
          width: calc(100% - 24px);
          padding-right: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      &.is-current {
        &>.zy-el-tree-node__content {
          .zy-el-tree-node__label {
            color: var(--zy-el-color-primary);
          }
        }
      }
    }
  }

  .GlobalAiChatHistoryScroll {
    padding: var(--zy-distance-five) 0;

    .GlobalAiChatHistoryItem {
      cursor: pointer;
      padding: var(--zy-distance-five) var(--zy-distance-two);

      .GlobalAiChatHistoryTime {
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
        color: var(--zy-el-text-color-secondary);
      }

      .GlobalAiChatHistoryTitle {
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
        color: var(--zy-el-text-color-primary);
      }
    }

    .GlobalAiChatHistoryLoadingText {
      width: 100%;
      height: var(--zy-height);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--zy-el-text-color-secondary);
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
    }
  }
}
</style>
