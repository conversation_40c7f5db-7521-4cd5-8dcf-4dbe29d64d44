{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ContentExtraction\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, \"内容提炼\");\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBoxFunction\\ContentExtraction\\ContentExtraction.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ContentExtraction\">内容提炼</div>\r\n</template>\r\n<script>\r\nexport default { name: 'ContentExtraction' }\r\n</script>\r\n\r\n<script setup></script>\r\n<style lang=\"scss\">\r\n.ContentExtraction {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;uBAA9BC,mBAAA,CAAyC,OAAzCC,UAAyC,EAAV,MAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}