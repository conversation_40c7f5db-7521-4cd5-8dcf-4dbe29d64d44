{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from '../img/home_toptext_bg.png';\nimport _imports_1 from '../img/search_icon.png';\nimport _imports_2 from '../img/login_btn_bg.png';\nimport _imports_3 from '../img/mine_btn_bg.png';\nimport _imports_4 from '../img/mine_icon.png';\nimport _imports_5 from '../img/menu_icon1.png';\nimport _imports_6 from '../img/menu_icon2.png';\nimport _imports_7 from '../img/menu_icon3.png';\nimport _imports_8 from '../img/menu_icon4.png';\nvar _hoisted_1 = {\n  class: \"HomeLayout\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n    class: \"home-header\"\n  }, [_cache[4] || (_cache[4] = _createElementVNode(\"img\", {\n    class: \"home-logo\",\n    src: _imports_0,\n    alt: \"logo\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", {\n    class: \"home-header-right\"\n  }, [_createElementVNode(\"div\", {\n    class: \"search-box\"\n  }, [_cache[1] || (_cache[1] = _createElementVNode(\"input\", {\n    type: \"text\",\n    placeholder: \"请输入搜索内容\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    class: \"search-btn\",\n    onClick: $setup.search\n  }, _cache[0] || (_cache[0] = [_createElementVNode(\"img\", {\n    class: \"search-icon\",\n    src: _imports_1,\n    alt: \"搜索\"\n  }, null, -1 /* HOISTED */)]))]), _createElementVNode(\"button\", {\n    class: \"login-btn\",\n    onClick: $setup.openLogin\n  }, _cache[2] || (_cache[2] = [_createElementVNode(\"img\", {\n    src: _imports_2,\n    alt: \"登录首页\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", {\n    style: {\n      \"color\": \"#003399\"\n    }\n  }, \"登录首页\", -1 /* HOISTED */)])), _createElementVNode(\"button\", {\n    class: \"mine-btn\",\n    onClick: $setup.openLogin\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"img\", {\n    src: _imports_3,\n    alt: \"我的\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, [_createElementVNode(\"img\", {\n    class: \"mine-icon\",\n    src: _imports_4,\n    alt: \"我的\"\n  }), _createTextVNode(\" 我的 \")], -1 /* HOISTED */)]))])]), _createElementVNode(\"div\", {\n    class: \"home-content\"\n  }, [_createElementVNode(\"div\", {\n    class: \"menu-list\"\n  }, [_createElementVNode(\"div\", {\n    class: \"menu-item menu-bg1 u-top\",\n    onClick: $setup.openLogin\n  }, _cache[5] || (_cache[5] = [_createElementVNode(\"img\", {\n    class: \"menu-icon\",\n    src: _imports_5,\n    alt: \"我的工作\",\n    style: {\n      \"margin-top\": \"-20px\",\n      \"margin-left\": \"35px\"\n    }\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"menu-title\",\n    style: {\n      \"margin-left\": \"35px\"\n    }\n  }, \"我的工作\", -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n    class: \"menu-item menu-bg2 u-bottom\",\n    onClick: $setup.openLogin,\n    style: {\n      \"margin-left\": \"-65px\",\n      \"height\": \"400px\"\n    }\n  }, _cache[6] || (_cache[6] = [_createElementVNode(\"img\", {\n    class: \"menu-icon\",\n    src: _imports_6,\n    alt: \"我的待办\",\n    style: {\n      \"margin-top\": \"-65px\"\n    }\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"menu-title\"\n  }, \"我的待办\", -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n    class: \"menu-item menu-bg3 u-bottom\",\n    onClick: $setup.openLogin,\n    style: {\n      \"margin-left\": \"-90px\",\n      \"height\": \"400px\"\n    }\n  }, _cache[7] || (_cache[7] = [_createElementVNode(\"img\", {\n    class: \"menu-icon\",\n    src: _imports_7,\n    alt: \"综合应用\",\n    style: {\n      \"margin-top\": \"-65px\"\n    }\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"menu-title\"\n  }, \"综合应用\", -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n    class: \"menu-item menu-bg4 u-top\",\n    onClick: $setup.openLogin,\n    style: {\n      \"margin-left\": \"-70px\"\n    }\n  }, _cache[8] || (_cache[8] = [_createElementVNode(\"img\", {\n    class: \"menu-icon\",\n    src: _imports_8,\n    alt: \"其他应用\",\n    style: {\n      \"margin-top\": \"-20px\",\n      \"margin-right\": \"50px\"\n    }\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"menu-title\",\n    style: {\n      \"margin-right\": \"50px\"\n    }\n  }, \"其他应用\", -1 /* HOISTED */)]))])])]);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "_imports_3", "_imports_4", "_imports_5", "_imports_6", "_imports_7", "_imports_8", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "src", "alt", "type", "placeholder", "onClick", "$setup", "search", "openLogin", "style", "_createTextVNode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\HomeLayout\\HomeLayout.vue"], "sourcesContent": ["<template>\r\n  <div class=\"HomeLayout\">\r\n    <div class=\"home-header\">\r\n      <img class=\"home-logo\" src=\"../img/home_toptext_bg.png\" alt=\"logo\" />\r\n      <div class=\"home-header-right\">\r\n        <div class=\"search-box\">\r\n          <input type=\"text\" placeholder=\"请输入搜索内容\" />\r\n          <button class=\"search-btn\" @click=\"search\">\r\n            <img class=\"search-icon\" src=\"../img/search_icon.png\" alt=\"搜索\" />\r\n          </button>\r\n        </div>\r\n        <button class=\"login-btn\" @click=\"openLogin\">\r\n          <img src=\"../img/login_btn_bg.png\" alt=\"登录首页\" />\r\n          <span style=\"color: #003399;\">登录首页</span>\r\n        </button>\r\n        <button class=\"mine-btn\" @click=\"openLogin\">\r\n          <img src=\"../img/mine_btn_bg.png\" alt=\"我的\" />\r\n          <span>\r\n            <img class=\"mine-icon\" src=\"../img/mine_icon.png\" alt=\"我的\" />\r\n            我的\r\n          </span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"home-content\">\r\n      <div class=\"menu-list\">\r\n        <div class=\"menu-item menu-bg1 u-top\" @click=\"openLogin\">\r\n          <img class=\"menu-icon\" src=\"../img/menu_icon1.png\" alt=\"我的工作\" style=\"margin-top: -20px;margin-left: 35px;\" />\r\n          <div class=\"menu-title\" style=\"margin-left: 35px;\">我的工作</div>\r\n        </div>\r\n        <div class=\"menu-item menu-bg2 u-bottom\" @click=\"openLogin\" style=\"margin-left: -65px;height: 400px;\">\r\n          <img class=\"menu-icon\" src=\"../img/menu_icon2.png\" alt=\"我的待办\" style=\"margin-top: -65px;\" />\r\n          <div class=\"menu-title\">我的待办</div>\r\n        </div>\r\n        <div class=\"menu-item menu-bg3 u-bottom\" @click=\"openLogin\" style=\"margin-left: -90px;height: 400px;\">\r\n          <img class=\"menu-icon\" src=\"../img/menu_icon3.png\" alt=\"综合应用\" style=\"margin-top: -65px;\" />\r\n          <div class=\"menu-title\">综合应用</div>\r\n        </div>\r\n        <div class=\"menu-item menu-bg4 u-top\" @click=\"openLogin\" style=\"margin-left: -70px;\">\r\n          <img class=\"menu-icon\" src=\"../img/menu_icon4.png\" alt=\"其他应用\" style=\"margin-top: -20px;margin-right: 50px;\" />\r\n          <div class=\"menu-title\" style=\"margin-right: 50px;\">其他应用</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'HomeLayout' }\r\n</script>\r\n<script setup>\r\nimport { useRouter } from 'vue-router'\r\nconst router = useRouter()\r\nconst search = () => {\r\n  console.log('搜索')\r\n}\r\nconst openLogin = () => {\r\n  router.push({ name: 'LoginView' })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.HomeLayout {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: url(\"../img/home_layout_bg.png\") no-repeat;\r\n  background-size: 100% 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .home-header {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24px 40px 0 40px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    z-index: 2;\r\n    flex-shrink: 0;\r\n\r\n    .home-logo {\r\n      height: 74px;\r\n    }\r\n\r\n    .home-header-right {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n\r\n      .search-box {\r\n        display: flex;\r\n        align-items: center;\r\n        border-radius: 20px;\r\n        padding: 0 0 0 8px;\r\n        height: 36px;\r\n        border: 1px solid #FFFFFF;\r\n        width: 350px;\r\n\r\n        input {\r\n          border: none;\r\n          outline: none;\r\n          height: 100%;\r\n          padding: 0 8px;\r\n          border-radius: 20px 0 0 20px;\r\n          background: rgb(0, 0, 0, 0);\r\n          width: calc(100% - 55px);\r\n          color: #fff;\r\n\r\n          &::placeholder {\r\n            color: #fff;\r\n            opacity: 1;\r\n          }\r\n        }\r\n\r\n        .search-btn {\r\n          background: url(\"../img/search_btn_bg.png\") no-repeat center/cover;\r\n          border: none;\r\n          width: 55px;\r\n          height: 36px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n\r\n          .search-icon {\r\n            width: 18px;\r\n            height: 18px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .login-btn,\r\n      .mine-btn {\r\n        background: none;\r\n        border: none;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          height: 39px;\r\n        }\r\n\r\n        span {\r\n          position: absolute;\r\n          left: 0;\r\n          width: 100%;\r\n          text-align: center;\r\n          color: #fff;\r\n          font-size: 14px;\r\n          line-height: 39px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n\r\n      .mine-btn .mine-icon {\r\n        width: 20px;\r\n        height: 20px;\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .home-content {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: flex-start;\r\n    padding-top: 35vh;\r\n    position: relative;\r\n    z-index: 1;\r\n\r\n    .menu-list {\r\n      width: calc(100% - 240px); // 两边各120px\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: flex-start;\r\n      position: relative;\r\n    }\r\n\r\n    .menu-item {\r\n      flex: 1 1 0;\r\n      max-width: 475px;\r\n      min-width: 220px;\r\n      aspect-ratio: 475/450; // 保持比例\r\n      border-radius: 24px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      position: relative;\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      // box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.15);\r\n      cursor: pointer;\r\n      transition: transform 0.2s;\r\n      margin: 0;\r\n\r\n      &:hover {\r\n        // transform: translateY(-8px) scale(1.03);\r\n        // box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.18);\r\n      }\r\n\r\n      .menu-icon {\r\n        width: 82px;\r\n        height: 82px;\r\n      }\r\n\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 28px;\r\n        font-weight: bold;\r\n        margin-top: 5px;\r\n      }\r\n    }\r\n\r\n    // U形布局\r\n    .u-top {\r\n      margin-top: 0;\r\n    }\r\n\r\n    .u-bottom {\r\n      margin-top: 50px; // 第二、第三个往下\r\n    }\r\n\r\n    .menu-bg1 {\r\n      background-image: url('../img/menu_bg1.png');\r\n    }\r\n\r\n    .menu-bg2 {\r\n      background-image: url('../img/menu_bg2.png');\r\n    }\r\n\r\n    .menu-bg3 {\r\n      background-image: url('../img/menu_bg3.png');\r\n    }\r\n\r\n    .menu-bg4 {\r\n      background-image: url('../img/menu_bg4.png');\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAG6BA,UAAgC;OAKxBC,UAA4B;OAIlDC,UAA6B;OAI7BC,UAA4B;OAERC,UAA0B;OAS5BC,UAA2B;OAI3BC,UAA2B;OAI3BC,UAA2B;OAI3BC,UAA2B;;EAtCrDC,KAAK,EAAC;AAAY;;uBAAvBC,mBAAA,CA2CM,OA3CNC,UA2CM,GA1CJC,mBAAA,CAqBM;IArBDH,KAAK,EAAC;EAAa,I,0BACtBG,mBAAA,CAAqE;IAAhEH,KAAK,EAAC,WAAW;IAACI,GAAgC,EAAhCb,UAAgC;IAACc,GAAG,EAAC;+BAC5DF,mBAAA,CAkBM;IAlBDH,KAAK,EAAC;EAAmB,IAC5BG,mBAAA,CAKM;IALDH,KAAK,EAAC;EAAY,I,0BACrBG,mBAAA,CAA2C;IAApCG,IAAI,EAAC,MAAM;IAACC,WAAW,EAAC;+BA<PERSON>/BJ,mBAAA,CAES;IAFDH,KAAK,EAAC,YAAY;IAAEQ,OAAK,EAAEC,MAAA,CAAAC;gCACjCP,mBAAA,CAAiE;IAA5DH,KAAK,EAAC,aAAa;IAACI,GAA4B,EAA5BZ,UAA4B;IAACa,GAAG,EAAC;mCAG9DF,mBAAA,CAGS;IAHDH,KAAK,EAAC,WAAW;IAAEQ,OAAK,EAAEC,MAAA,CAAAE;gCAChCR,mBAAA,CAAgD;IAA3CC,GAA6B,EAA7BX,UAA6B;IAACY,GAAG,EAAC;8BACvCF,mBAAA,CAAyC;IAAnCS,KAAuB,EAAvB;MAAA;IAAA;EAAuB,GAAC,MAAI,oB,IAEpCT,mBAAA,CAMS;IANDH,KAAK,EAAC,UAAU;IAAEQ,OAAK,EAAEC,MAAA,CAAAE;gCAC/BR,mBAAA,CAA6C;IAAxCC,GAA4B,EAA5BV,UAA4B;IAACW,GAAG,EAAC;8BACtCF,mBAAA,CAGO,eAFLA,mBAAA,CAA6D;IAAxDH,KAAK,EAAC,WAAW;IAACI,GAA0B,EAA1BT,UAA0B;IAACU,GAAG,EAAC;MAlBlEQ,gBAAA,CAkByE,MAE/D,E,4BAINV,mBAAA,CAmBM;IAnBDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAiBM;IAjBDH,KAAK,EAAC;EAAW,IACpBG,mBAAA,CAGM;IAHDH,KAAK,EAAC,0BAA0B;IAAEQ,OAAK,EAAEC,MAAA,CAAAE;gCAC5CR,mBAAA,CAA6G;IAAxGH,KAAK,EAAC,WAAW;IAACI,GAA2B,EAA3BR,UAA2B;IAACS,GAAG,EAAC,MAAM;IAACO,KAA4C,EAA5C;MAAA;MAAA;IAAA;8BAC9DT,mBAAA,CAA6D;IAAxDH,KAAK,EAAC,YAAY;IAACY,KAA0B,EAA1B;MAAA;IAAA;KAA2B,MAAI,oB,IAEzDT,mBAAA,CAGM;IAHDH,KAAK,EAAC,6BAA6B;IAAEQ,OAAK,EAAEC,MAAA,CAAAE,SAAS;IAAEC,KAAyC,EAAzC;MAAA;MAAA;IAAA;gCAC1DT,mBAAA,CAA2F;IAAtFH,KAAK,EAAC,WAAW;IAACI,GAA2B,EAA3BP,UAA2B;IAACQ,GAAG,EAAC,MAAM;IAACO,KAA0B,EAA1B;MAAA;IAAA;8BAC9DT,mBAAA,CAAkC;IAA7BH,KAAK,EAAC;EAAY,GAAC,MAAI,oB,IAE9BG,mBAAA,CAGM;IAHDH,KAAK,EAAC,6BAA6B;IAAEQ,OAAK,EAAEC,MAAA,CAAAE,SAAS;IAAEC,KAAyC,EAAzC;MAAA;MAAA;IAAA;gCAC1DT,mBAAA,CAA2F;IAAtFH,KAAK,EAAC,WAAW;IAACI,GAA2B,EAA3BN,UAA2B;IAACO,GAAG,EAAC,MAAM;IAACO,KAA0B,EAA1B;MAAA;IAAA;8BAC9DT,mBAAA,CAAkC;IAA7BH,KAAK,EAAC;EAAY,GAAC,MAAI,oB,IAE9BG,mBAAA,CAGM;IAHDH,KAAK,EAAC,0BAA0B;IAAEQ,OAAK,EAAEC,MAAA,CAAAE,SAAS;IAAEC,KAA2B,EAA3B;MAAA;IAAA;gCACvDT,mBAAA,CAA8G;IAAzGH,KAAK,EAAC,WAAW;IAACI,GAA2B,EAA3BL,UAA2B;IAACM,GAAG,EAAC,MAAM;IAACO,KAA6C,EAA7C;MAAA;MAAA;IAAA;8BAC9DT,mBAAA,CAA8D;IAAzDH,KAAK,EAAC,YAAY;IAACY,KAA2B,EAA3B;MAAA;IAAA;KAA4B,MAAI,oB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}