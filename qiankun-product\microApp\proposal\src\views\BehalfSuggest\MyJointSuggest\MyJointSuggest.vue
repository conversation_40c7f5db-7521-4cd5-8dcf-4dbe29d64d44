<template>
  <div class="MyJointSuggest">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #mainHandleOffices="scope">
            {{scope.row.mainHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}
          </template>
          <template #assistHandleOffices="scope">
            {{scope.row.assistHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}
          </template>
          <template #publishHandleOffices="scope">
            {{scope.row.publishHandleOffices?.map((v) => v.flowHandleOfficeName).join('、')}}
          </template>
        </xyl-global-table>
        <el-table-column width="180" fixed="right" class-name="globalTableCustom">
          <template #header>
            操作
            <div class="TableCustomIcon" v-if="hasPermission('table_custom')" @click="handleEditorCustom"></div>
          </template>
          <template #default="scope">
            <div v-if="scope.row.agreeStatus === 1">已操作同意</div>
            <div v-if="scope.row.agreeStatus === 2">已操作不同意</div>
            <template v-if="!scope.row.agreeStatus">
              <el-button @click="handleJoin(scope.row, 1)" type="primary" plain>同意</el-button>
              <el-button @click="handleJoin(scope.row, 2)" type="primary" plain>不同意</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="我附议的提案" :exportId="exportId" :params="exportParams" module="proposalExportExcel"
        tableId="id_prop_proposal_myJoin" @excelCallback="callback"
        :handleExcelData="handleExcelData"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'MyJointSuggest' }
</script>
<script setup>
import api from '@/api'
import { onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { hasPermission } from 'common/js/permissions'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
const buttonList = [
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
]
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: 'id_prop_proposal_myJoin', tableApi: 'suggestionList' })

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    handleQuery()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    handleQuery()
  }
})
const handleExcelData = (_item) => {
  _item.forEach(v => {
    if (!v.mainHandleOffices) {
      v.mainHandleOffices = v.publishHandleOffices
    }
  })
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } }
  })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
}
const handleJoin = (item, type) => {
  ElMessageBox.confirm(`此操作将会${type === 1 ? '同意' : '不同意'}当前提案的联名, 是否继续?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      suggestionAgree(item.id, type)
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消操作' })
    })
}
const suggestionAgree = async (id, type) => {
  const { code } = await api.suggestionAgree({ detailId: id, agreeStatus: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: '操作成功' })
    tableRefReset()
    handleQuery()
  }
}
</script>
<style lang="scss">
.MyJointSuggest {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
