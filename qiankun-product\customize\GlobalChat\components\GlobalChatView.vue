<template>
  <div class="GlobalChatView" :class="{ GlobalChatMacView: isMac }" @click.prevent="handleElClick"
    @contextmenu.prevent="handleElContextmenu">
    <div class="GlobalChatViewList forbidSelect">
      <div class="GlobalChatViewListHead">
        <el-autocomplete v-model="keyword" :prefix-icon="Search" :fetch-suggestions="querySearch" placeholder="搜索"
          popper-class="GlobalChatViewAutocomplete" clearable @select="handleClick">
          <template #default="{ item }">
            <div class="GlobalChatViewMessagesItem forbidSelect">
              <el-badge :value="item.count" :hidden="!item.count" :is-dot="item.isNotInform === 1">
                <el-image :src="imgUrl(item.chatObjectInfo?.img)" fit="cover" draggable="false" />
              </el-badge>
              <div class="GlobalChatViewMessagesInfo">
                <div class="GlobalChatViewMessagesName">
                  <div class="ellipsis">{{ item.chatObjectInfo?.name }}</div>
                  <span>{{ handleTimeFormat(item?.sentTime) }}</span>
                </div>
                <div class="GlobalChatViewNotInform" v-html="notificationIcon" v-if="item.isNotInform === 1"></div>
                <div class="GlobalChatViewMessagesText ellipsis" v-if="item.messageType === 'RC:ImgTextMsg'">
                  {{ item?.content?.title }}
                </div>
                <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:ImgMsg'">
                  [图片]
                </div>
                <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:HQVCMsg'">
                  [语音] {{ item?.content?.duration }}"
                </div>
                <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:CmdNtf'">
                  {{ item?.content?.name }}
                </div>
                <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:LBSMsg'">
                  [不支持的消息，请在移动端进行查看]
                </div>
                <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:RcCmd'">
                  {{ item.revocationMessage }}
                </div>
                <div class="GlobalChatViewMessagesText ellipsis" v-else>{{ item?.content?.content }}</div>
              </div>
            </div>
          </template>
        </el-autocomplete>
        <div class="GlobalChatViewListHeadIcon" v-html="initGroupChatIcon" @click="handleCreateGroup('')"></div>
      </div>
      <el-scrollbar class="GlobalChatViewMessagesList">
        <div :class="['GlobalChatViewMessagesItem', { 'is-top': item.isTop }, { 'is-active': item.id === chatId }]"
          v-for="item in chatList" :key="item.id" @click="handleClick(item)"
          @contextmenu.prevent="handleChatMenu($event, item)">
          <el-badge :value="item.count" :hidden="!item.count" :is-dot="item.isNotInform === 1">
            <el-image :src="imgUrl(item.chatObjectInfo?.img)" fit="cover" draggable="false" />
          </el-badge>
          <div class="GlobalChatViewMessagesInfo">
            <div class="GlobalChatViewMessagesName">
              <div class="ellipsis" v-if="!item.chatObjectInfo?.chatGroupType">{{ item.chatObjectInfo?.name }}</div>
              <div class="GlobalChatViewMessagesNameGroup ellipsis"
                v-if="item.type === 3 && item.chatObjectInfo.chatGroupType">
                {{ item.chatObjectInfo?.name }}
                <span>{{ item.chatObjectInfo.chatGroupType }}</span>
              </div>
              <span>{{ handleTimeFormat(item?.sentTime) }}</span>
            </div>
            <div class="GlobalChatViewNotInform" v-html="notificationIcon" v-if="item.isNotInform === 1"></div>
            <div class="GlobalChatViewMessagesText ellipsis" v-if="item.messageType === 'RC:ImgTextMsg'">
              {{ item?.content?.title }}
            </div>
            <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:ImgMsg'">[图片]</div>
            <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:HQVCMsg'">
              [语音] {{ item?.content?.duration }}"
            </div>
            <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:CmdNtf'">
              {{ item?.content?.name }}
            </div>
            <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:LBSMsg'">
              [不支持的消息，请在移动端进行查看]
            </div>
            <div class="GlobalChatViewMessagesText ellipsis" v-else-if="item.messageType === 'RC:RcCmd'">
              {{ item.revocationMessage }}
            </div>
            <div class="GlobalChatViewMessagesText ellipsis" v-else>{{ item?.content?.content }}</div>
          </div>
        </div>
      </el-scrollbar>
      <div class="GlobalChatClearAway" @click="handleClearAway">
        <div v-html="clearAwayIcon"></div>
        清除未读
      </div>
      <div class="GlobalChatViewMenu" :style="{ left: chatMenuLeft + 'px', top: chatMenuTop + 'px' }"
        v-show="chatMenuShow">
        <div class="GlobalChatViewMenuItem" @click="handleIsTopClick(true)" v-if="!chatMenuItem.isTop">置顶</div>
        <div class="GlobalChatViewMenuItem" @click="handleIsTopClick(false)" v-if="chatMenuItem.isTop">取消置顶</div>
        <div class="GlobalChatViewMenuItem" @click="handleNotificationClick(1)" v-if="chatMenuItem.isNotInform === 2">
          消息免打扰
        </div>
        <div class="GlobalChatViewMenuItem" @click="handleNotificationClick(2)" v-if="chatMenuItem.isNotInform === 1">
          允许消息通知
        </div>
        <div class="GlobalChatViewMenuLine"></div>
        <div class="GlobalChatViewMenuItem" @click="handleDelChat">删除</div>
      </div>
    </div>
    <div class="GlobalChatWindow" @dragover.prevent @drop="handleDrop" v-if="chatId">
      <div class="GlobalChatWindowTitle forbidSelect">
        <div class="ellipsis" @click="handleSetting">
          {{ chatInfo.chatObjectInfo?.name }}
          <span v-if="chatInfo.type === 3 && groupUser.length">（{{ groupUser.length }}）</span>
        </div>
        <div class="GlobalChatWindowMore" @click="handleSetting">
          <el-icon>
            <MoreFilled />
          </el-icon>
        </div>
      </div>
      <el-scrollbar ref="scrollRef" always class="GlobalChatWindowScroll" :class="{ GlobalChatWindowNoChat: !isChat }"
        @scroll="handleMessagesScroll">
        <div class="GlobalChatGroupAnnouncement" v-if="isChatGroupAnnouncement">
          <div class="GlobalChatGroupAnnouncementTitle">
            <div>
              <span v-html="announcementIcon"></span>
              群公告
            </div>
            <el-icon @click="handleChatGroupAnnouncement">
              <CircleCloseFilled />
            </el-icon>
          </div>
          <div class="GlobalChatGroupAnnouncementContent">{{ chatGroupAnnouncement }}</div>
        </div>
        <div class="GlobalChatWindowBody">
          <div v-for="item in chatInfoMessagesData" :key="item.id || item.uid" :class="item.className">
            <template v-if="item.type === 'time'">{{ item?.content }}</template>
            <template v-else-if="item.type === 'RC:CmdNtf'">{{ item?.content?.name }}</template>
            <template v-else-if="item.type === 'RC:RcCmd'">
              {{ item.chatObjectInfoType ? '你' : item.userName }}撤回了一条消息
            </template>
            <template v-else>
              <div class="GlobalChatWindowUserImg">
                <el-image :src="imgUrl(item.chatObjectInfo?.img)" fit="cover" draggable="false" />
              </div>
              <div class="GlobalChatMessagesInfo">
                <div class="GlobalChatMessagesName ellipsis" @contextmenu.prevent="handleMessagesMenu($event, item)"
                  v-if="!item.chatObjectInfoType">
                  {{ item.chatObjectInfo?.name }}
                </div>
                <div class="GlobalChatMessagesText" @contextmenu.prevent="handleMessagesMenu($event, item)"
                  v-if="item.type === 'RC:TxtMsg'">
                  <span></span>
                  <div class="GlobalChatEmotion" v-html="item?.content?.htmlContent"></div>
                </div>
                <div class="GlobalChatMessagesText" @contextmenu.prevent="handleMessagesMenu($event, item)"
                  @click="handleAudio(item)" v-if="item.type === 'RC:HQVCMsg'">
                  <span></span>
                  <div :class="['GlobalChatVoice', { 'is-active': chatInfoAudio.id === item.id }]"
                    :style="{ width: `${88 + (item?.content?.duration || 0)}px` }">
                    {{ item?.content?.duration }}"
                  </div>
                  <div class="GlobalChatVoiceContinue" @click.stop="handleGoAudio(item)"
                    v-if="chatInfoAudio.id !== item.id && chatInfoAudioObj[item.id]">
                    继续播放
                  </div>
                </div>
                <el-image :src="item?.content?.imageUri" fit="cover" :preview-src-list="chatInfoImg"
                  :initial-index="item.imgIndex" @load="handleImgLoad"
                  @contextmenu.prevent="handleMessagesMenu($event, item)" v-if="item.type === 'RC:ImgMsg'">
                  <template #error>
                    <div class="GlobalChatMessagesImgSlot" @contextmenu.prevent="handleMessagesMenu($event, item)">
                      <el-icon>
                        <Picture />
                      </el-icon>
                    </div>
                  </template>
                </el-image>
                <div :class="['GlobalChatMessagesCustom', item.customType]"
                  @contextmenu.prevent="handleMessagesMenu($event, item)" @click="handleCustom(item)"
                  v-if="['RC:ImgTextMsg', 'RC:LBSMsg']?.includes(item.type)">
                  <span></span>
                  <template v-if="item.customType === 'file'">
                    <div class="GlobalChatMessagesFileDownload" :style="{ width: isElectronFileObj[item.file.id] }"
                      v-if="isElectronFile?.includes(item.file.id)"></div>
                    <div class="globalFileIcon" :class="fileIcon(item.file?.extName)"></div>
                    <div class="GlobalChatMessagesCustomName">{{ item.file?.originalFileName || '未知文件' }}</div>
                    <div class="GlobalChatMessagesCustomText">
                      {{ item.file?.fileSize ? size2Str(item.file.fileSize) : '0KB' }}
                    </div>
                  </template>
                  <template v-if="item.customType === 'vote'">
                    <div class="GlobalChatMessagesVoteTitleBody">
                      <div class="GlobalChatMessagesVoteTitle">
                        <div class="GlobalChatMessagesVoteIcon" v-html="voteListIcon"></div>
                        投票
                      </div>
                      <div class="GlobalChatMessagesVoteTime">{{ format(item.vote?.createDate) }}</div>
                    </div>
                    <div class="GlobalChatMessagesVoteInfo">
                      <div class="GlobalChatMessagesVoteInfoIcon" v-html="voteBgIcon"></div>
                      <div class="GlobalChatMessagesVoteName">{{ item.vote?.topic }}</div>
                    </div>
                  </template>
                  <template v-if="item.customType === 'unknown'">
                    <div class="GlobalChatMessagesCustomUnknown">[不支持的消息，请在移动端进行查看]</div>
                  </template>
                </div>
                <div class="GlobalChatMessagesFile" @contextmenu.prevent="handleMessagesMenu($event, item)"
                  v-if="item.type === 'RC:FileMsg'">
                  <span></span>
                  <div class="globalFileIcon" :class="fileIcon(item?.content?.type)"></div>
                  <div class="GlobalChatMessagesFileName">{{ item?.content?.name || '未知文件' }}</div>
                  <div class="GlobalChatMessagesFileSize">
                    {{ item?.content?.size ? size2Str(item?.content?.size) : '0KB' }}
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </el-scrollbar>
      <div class="GlobalChatViewMenu" :style="{ left: messagesMenuLeft + 'px', top: messagesMenuTop + 'px' }"
        v-show="messagesMenuShow">
        <!-- <div class="GlobalChatViewMenuItem" v-if="chatType?.includes(messagesMenuItem.type)">转发</div> -->
        <!-- <div class="GlobalChatViewMenuItem">收藏</div> -->
        <div class="GlobalChatViewMenuItem" v-copy="messagesMenuItem?.copyContent"
          v-if="messagesMenuItem.type === 'RC:TxtMsg'">
          复制
        </div>
        <div class="GlobalChatViewMenuItem" @click="handleFolderSelectFile"
          v-if="isElectron && messagesMenuItem?.customType === 'file'">
          {{ isMacText() ? '在 Finder 中显示' : '在资源管理器中显示' }}
        </div>
        <div class="GlobalChatViewMenuLine" v-if="chatType?.includes(messagesMenuItem.type)"></div>
        <div class="GlobalChatViewMenuItem" @click="handleDelMessage"
          v-if="!messagesMenuItem.chatObjectInfoType || !messagesMenuItem.isWithdraw">
          删除
        </div>
        <div class="GlobalChatViewMenuItem" @click="handleWithdrawMessage"
          v-if="messagesMenuItem.chatObjectInfoType && messagesMenuItem.isWithdraw">
          撤回
        </div>
      </div>
      <GlobalChatEditor ref="editorRef" :isVote="chatInfo.type === 3" :userData="groupUser" @handleFile="fileUpload"
        @handleVote="handleVote" @handlePasteImg="handlePasteImg" @handleSendMessage="handleKeyCode" v-show="isChat">
      </GlobalChatEditor>
      <div class="GlobalChatViewNoMessage" v-show="!isChat">
        <el-icon>
          <Warning />
        </el-icon>
        无法在已退出的群聊中接收和发送消息
      </div>
      <setting-popup-window v-model="settingShow">
        <GlobalChatViewWindow :chatInfo="chatInfo" :groupUser="groupUser" @refresh="handleRefresh"
          @callback="handleGroup" />
      </setting-popup-window>
    </div>
    <div class="GlobalChatViewDrag" v-if="!chatId"></div>
    <chat-popup-window v-model="fileShow">
      <ChatSendFile :chatInfo="chatInfo" :fileList="fileList" @callback="fileCallback"></ChatSendFile>
    </chat-popup-window>
    <chat-popup-window v-model="imgShow">
      <ChatSendImg :chatInfo="chatInfo" :fileImg="fileImg" @callback="imgCallback"></ChatSendImg>
    </chat-popup-window>
    <chat-popup-window v-model="createGroupShow">
      <GlobalCreateGroup :userId="userId" @callback="createCallback"></GlobalCreateGroup>
    </chat-popup-window>
    <chat-popup-window v-model="addShow">
      <GlobalGroupAddUser :infoId="infoId" @callback="addCallback"></GlobalGroupAddUser>
    </chat-popup-window>
    <chat-popup-window v-model="delShow">
      <GlobalGroupDelUser :infoId="infoId" @callback="delCallback"></GlobalGroupDelUser>
    </chat-popup-window>
    <chat-popup-window v-model="nameShow" class="GlobalGroupNamePopupWindow">
      <GlobalGroupName :infoId="infoId" @callback="nameCallback"></GlobalGroupName>
    </chat-popup-window>
    <chat-popup-window v-model="qrShow" class="GlobalGroupQrPopupWindow">
      <GlobalGroupQr :infoId="infoId"></GlobalGroupQr>
    </chat-popup-window>
    <chat-popup-window v-model="announcementShow" class="GlobalGroupAnnouncementPopupWindow">
      <GlobalGroupAnnouncement :infoId="infoId" :isOwner="isGroupOwner" @callback="announcementCallback">
      </GlobalGroupAnnouncement>
    </chat-popup-window>
    <chat-popup-window v-model="transferShow">
      <GlobalGroupTransfer :infoId="infoId" @callback="transferCallback"></GlobalGroupTransfer>
    </chat-popup-window>
    <chat-popup-window v-model="voteShow" class="GlobalGroupVotePopupWindow">
      <GlobalGroupVote :id="infoId" :refresh="voteRefresh" @callback="voteCallback"
        @sendMessage="handleSendCustomMessage">
      </GlobalGroupVote>
    </chat-popup-window>
    <chat-popup-window v-model="createVoteShow">
      <GlobalCreateVote :dataId="infoId" @callback="handleVoteCallback"></GlobalCreateVote>
    </chat-popup-window>
    <chat-popup-window v-model="voteDetailsShow" class="GlobalGroupVotePopupWindow">
      <GlobalVoteDetails :id="voteId" @callback="handleVoteCallback" @sendMessage="handleSendCustomMessage">
      </GlobalVoteDetails>
    </chat-popup-window>
  </div>
</template>
<script>
export default { name: 'GlobalChatView' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted, onUnmounted, watch, nextTick, defineAsyncComponent } from 'vue'
import * as RongIMLib from '@rongcloud/imlib-next'
import { format } from 'common/js/time.js'
import utils, { size2Str } from 'common/js/utils.js'
import { globalFileLocation } from 'common/config/location'
import { user, appOnlyHeader } from 'common/js/system_var.js'
// import { emotion } from '../js/emotion.js' emoteIcon, folderIcon, lineFeedIcon, voteIcon,
import { chatGroupMemberList } from '../js/ChatMethod.js'
import { fileIcon, handleTimeFormat, handleHistoryMessages, getUniqueFileName } from '../js/ChatViewMethod.js'
import {
  notificationIcon,
  initGroupChatIcon,
  announcementIcon,
  clearAwayIcon,
  voteListIcon,
  voteBgIcon
} from '../js/icon.js'
import { Search, Picture } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const GlobalChatEditor = defineAsyncComponent(() => import('./GlobalChatEditor.vue'))
const ChatPopupWindow = defineAsyncComponent(() => import('./chat-popup-window/chat-popup-window.vue'))
const SettingPopupWindow = defineAsyncComponent(() => import('./setting-popup-window/setting-popup-window.vue'))
const ChatSendImg = defineAsyncComponent(() => import('./ChatSendImg/ChatSendImg.vue'))
const ChatSendFile = defineAsyncComponent(() => import('./ChatSendFile/ChatSendFile.vue'))
const GlobalCreateGroup = defineAsyncComponent(() => import('./GlobalCreateGroup/GlobalCreateGroup.vue'))
const GlobalGroupAddUser = defineAsyncComponent(() => import('./GlobalGroupAddUser/GlobalGroupAddUser.vue'))
const GlobalGroupDelUser = defineAsyncComponent(() => import('./GlobalGroupDelUser/GlobalGroupDelUser.vue'))
const GlobalGroupName = defineAsyncComponent(() => import('./GlobalGroupName/GlobalGroupName.vue'))
const GlobalGroupQr = defineAsyncComponent(() => import('./GlobalGroupQr/GlobalGroupQr.vue'))
const GlobalGroupAnnouncement = defineAsyncComponent(() =>
  import('./GlobalGroupAnnouncement/GlobalGroupAnnouncement.vue')
)
const GlobalGroupTransfer = defineAsyncComponent(() => import('./GlobalGroupTransfer/GlobalGroupTransfer.vue'))
const GlobalChatViewWindow = defineAsyncComponent(() => import('./GlobalChatViewWindow/GlobalChatViewWindow.vue'))
const GlobalGroupVote = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalGroupVote.vue'))
const GlobalCreateVote = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalCreateVote.vue'))
const GlobalVoteDetails = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalVoteDetails.vue'))
const props = defineProps({ modelValue: [String, Number], chatList: { type: Array, default: () => [] } })
const emit = defineEmits(['update:modelValue', 'time', 'refresh', 'send'])
const isMac = window.electron?.isMac
const isElectron = window.electron ? true : false
const keyword = ref('')
const chatId = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})
const chatType = ['RC:TxtMsg', 'RC:ImgTextMsg']
const chatList = computed(() => props.chatList)
const isChat = ref(true)
const chatInfo = ref({})
const groupUser = ref([])
const chatInfoImg = ref([])
const chatInfoAudio = ref({})
const chatInfoAudioObj = ref({})
const electronFile = ref([])
const isElectronFile = ref([])
const isElectronFileObj = ref({})
const electronRecordObj = ref({})
const electronRecordData = ref([])
const chatInfoUser = ref({})
const chatMenuTop = ref(0)
const chatMenuLeft = ref(0)
const chatMenuItem = ref({})
const chatMenuShow = ref(false)
const isChatMenuShow = ref(false)
const messagesMenuTop = ref(0)
const messagesMenuLeft = ref(0)
const messagesMenuItem = ref({})
const messagesMenuShow = ref(false)
const isMessagesMenuShow = ref(false)
const fileList = ref([])
const fileShow = ref(false)
const fileImg = ref({})
const imgShow = ref(false)
const userId = ref([])
const createGroupShow = ref(false)
const infoId = ref('')
const addShow = ref(false)
const delShow = ref(false)
const nameShow = ref(false)
const qrShow = ref(false)
const isGroupOwner = ref(false)
const announcementShow = ref(false)
const transferShow = ref(false)
const settingShow = ref(false)
const chatGroupAnnouncement = ref('')
const isChatGroupAnnouncement = ref(false)
const voteId = ref('')
const voteRefresh = ref('')
const voteShow = ref(false)
const createVoteShow = ref(false)
const voteDetailsShow = ref(false)
// 图片地址拼接组合
const imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const scrollRef = ref()
const editorRef = ref()
const scrollHeight = ref(0)
const scrollTopNum = ref(0)
const scrollShow = ref(true)
const chatInfoMessages = ref([])
const chatInfoMessagesData = ref([])
onMounted(() => { })
const handleElClick = (e) => {
  chatMenuShow.value = false
  messagesMenuShow.value = false
  e.preventDefault()
}
const handleElContextmenu = (e) => {
  if (isChatMenuShow.value) {
    isChatMenuShow.value = false
  } else {
    chatMenuShow.value = false
  }
  if (isMessagesMenuShow.value) {
    isMessagesMenuShow.value = false
  } else {
    messagesMenuShow.value = false
  }
  e.preventDefault()
}
const handleCustom = (item) => {
  if (item.customType === 'file') isElectron ? handleElectronFile(item.uid, item.file, false) : handlePreview(item.file)
  if (item.customType === 'vote') handleVoteDetails(item.vote)
}
const handleElectronFile = async (uid, file, type = false) => {
  if (isElectronFile.value?.includes(uid)) return
  let fileName = file.originalFileName
  if (electronRecordObj.value[uid]) {
    fileName = electronRecordObj.value[uid]
  } else {
    fileName = getUniqueFileName(fileName, electronRecordData.value)
    electronRecordObj.value[uid] = fileName
    electronRecordData.value.push(fileName)
  }
  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'
  const result = await window.electron.fileExists(fileFolderPath, fileName)
  if (result) type ? handleOpenFolderSelectFile(fileName) : handleElectronOpenFile(fileName)
  if (!result) globalElectronFileDownload(uid, file, fileName, type)
}
const onDownloadProgress = (progressEvent, id) => {
  if (progressEvent?.event?.lengthComputable) {
    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)
    isElectronFileObj.value[id] = 100 - parseInt(progress) + '%'
  }
}
const globalElectronFileDownload = async (uid, file, fileName, type = false) => {
  isElectronFile.value.push(uid)
  isElectronFileObj.value[uid] = '100%'
  const res = await api.globalElectronFileDownload(uid, file.id, {}, onDownloadProgress)
  isElectronFile.value = isElectronFile.value.filter((v) => v !== uid)
  delete isElectronFileObj.value[uid]
  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'
  const saveRes = await window.electron.saveFile(fileFolderPath, fileName, res)
  if (saveRes.type === 'success') type ? handleOpenFolderSelectFile(fileName) : handleElectronOpenFile(fileName)
  if (saveRes.type === 'error') ElMessage({ type: 'error', message: saveRes.message })
}
const handleElectronOpenFile = async (fileName) => {
  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'
  const openRes = await window.electron.openFile(fileFolderPath, fileName)
  if (openRes.type === 'error') ElMessage({ type: 'error', message: openRes.message })
}
const handleOpenFolderSelectFile = async (fileName) => {
  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'
  const openRes = await window.electron.openFolderSelectFile(`chat_files/${fileFolderPath}`, fileName)
  if (openRes.type === 'error') ElMessage({ type: 'error', message: openRes.message })
}
const handleFolderSelectFile = async () => {
  const uid = messagesMenuItem.value?.uid
  const file = messagesMenuItem.value?.file
  handleElectronFile(uid, file, true)
}
const globalElectronSaveRecord = async (recordId, data) => {
  const fileName = recordId + '_' + user.value.accountId + '_record.txt'
  const fileContent = utils.gm_encrypt(data, 'zysoft2017-08-11', 'zysoft2017-08-11')
  const res = await window.electron.saveRecordFile('chat_record', fileName, fileContent)
  console.log(res)
}
const globalElectronReadRecord = async (recordId) => {
  const fileName = recordId + '_' + user.value.accountId + '_record.txt'
  const res = await window.electron.readRecordFile('chat_record', fileName)
  if (res.type === 'success') {
    const recordObj = JSON.parse(utils.gm_decrypt(res.data, 'zysoft2017-08-11', 'zysoft2017-08-11'))
    const recordData = []
    for (const key in recordObj) {
      if (Object.prototype.hasOwnProperty.call(recordObj, key)) {
        const value = recordObj[key]
        recordData.push(value)
      }
    }
    electronRecordObj.value = recordObj
    electronRecordData.value = recordData
  }
}
const handlePreview = (row) => {
  if (!row) return
  globalFileLocation({
    name: process.env.VUE_APP_NAME,
    fileId: row.id,
    fileType: row.extName,
    fileName: row.originalFileName,
    fileSize: row.fileSize
  })
}
const querySearch = (queryString, cb) => {
  const results = queryString
    ? chatList.value.filter((v) => v.chatObjectInfo?.name?.toLowerCase().includes(queryString?.toLowerCase()))
    : []
  cb(results)
}
const scrollDown = () => {
  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight
}
const scrollElHeight = () => {
  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - scrollHeight.value
}
const handleMessagesScroll = ({ scrollTop }) => {
  scrollTopNum.value = scrollTop
  if (scrollTop === 0) {
    scrollHeight.value = scrollRef.value.wrapRef.scrollHeight
    getHistoryMessages(chatInfoMessages.value[0]?.sentTime || 0)
  }
}
const handleClick = async (item) => {
  if (chatId.value === item.id) return
  isChat.value = true
  chatId.value = item.id
}
const handleChatClick = async (item, type) => {
  settingShow.value = false
  chatInfo.value = item
  scrollHeight.value = 0
  chatInfoMessages.value = []
  electronFile.value = []
  isElectronFile.value = []
  isElectronFileObj.value = {}
  if (!type) {
    editorRef.value?.clearMessage()
    chatGroupAnnouncement.value = ''
    isChatGroupAnnouncement.value = false
  }
  getHistoryMessages()
  if (!item.isTemporary) clearMessagesUnreadStatus()
}
const handleClearAway = () => {
  ElMessageBox.confirm('此操作将清除所有消息的未读状态, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      clearAllMessagesUnreadStatus()
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消清除' })
    })
}
const clearAllMessagesUnreadStatus = async () => {
  const { code, msg } = await RongIMLib.clearAllMessagesUnreadStatus()
  if (code === 0) {
    handleRefresh()
  } else {
    console.log(code, msg)
  }
}
const clearMessagesUnreadStatus = async (type) => {
  const { code, msg } = await RongIMLib.clearMessagesUnreadStatus({
    conversationType: chatInfo.value.type,
    targetId: chatInfo.value.targetId
  })
  if (code === 0) {
    if (!type) handleRefresh()
  } else {
    console.log(code, msg)
  }
}
const getHistoryMessages = async (timestamp = 0) => {
  const option = { timestamp, count: 20, order: 0 }
  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
  const { code, data, msg } = await RongIMLib.getHistoryMessages(conversation, option)
  if (code === 0) {
    await handleUser()
    scrollShow.value = timestamp === 0
    handleMessages(data.list, timestamp === 0)
  } else {
    console.log(code, msg)
  }
}
const getNewestMessages = async () => {
  const option = { timestamp: chatInfo.value.sentTime, count: 20, order: 1 }
  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
  const { code, data, msg } = await RongIMLib.getHistoryMessages(conversation, option)
  if (code === 0) {
    scrollShow.value = true
    clearMessagesUnreadStatus(true)
    handleMessages(data.list, true)
  } else {
    console.log(code, msg)
  }
}
const chatGroupInfo = async (id) => {
  const { data } = await api.chatGroupInfo({ detailId: id.slice(appOnlyHeader.value.length) })
  const groupAnnouncement = JSON.parse(localStorage.getItem('isChatGroupAnnouncement')) || {}
  const groupAnnouncementItem = groupAnnouncement.hasOwnProperty(id)
    ? groupAnnouncement[id]
    : { show: true, callBoard: '' }
  if (data.callBoard && groupAnnouncementItem.callBoard !== data.callBoard) {
    chatGroupAnnouncement.value = data.callBoard
    isChatGroupAnnouncement.value = true
  }
  return data
}
const handleChatGroupAnnouncement = () => {
  const groupAnnouncement = JSON.parse(localStorage.getItem('isChatGroupAnnouncement')) || {}
  let newGroupAnnouncement = { ...groupAnnouncement }
  newGroupAnnouncement[chatInfo.value.targetId] = { show: false, callBoard: chatGroupAnnouncement.value }
  localStorage.setItem('isChatGroupAnnouncement', JSON.stringify(newGroupAnnouncement))
  chatGroupAnnouncement.value = ''
  isChatGroupAnnouncement.value = false
}
const handleAreArraysEqual = (arr1, arr2) => {
  // 如果数组长度不相等，直接返回 false
  if (arr1.length !== arr2.length) return false
  // 将两个数组排序后比较
  const sortedArr1 = [...arr1].sort()
  const sortedArr2 = [...arr2].sort()
  // 遍历比较每个元素
  for (let i = 0; i < sortedArr1.length; i++) {
    if (sortedArr1[i] !== sortedArr2[i]) {
      return false
    }
  }
  return true
}

const handleUser = async () => {
  let newUserObj = {}
  if (chatInfo.value.type === 3) {
    const newGroupInfo = await chatGroupInfo(chatInfo.value.targetId)
    if (!newGroupInfo.id) {
      ElMessageBox.alert('当前群组已解散！', '提示', {
        confirmButtonText: '确定',
        callback: () => {
          scrollHeight.value = 0
          chatInfoMessages.value = []
          chatInfoMessagesData.value = []
          chatMenuItem.value = chatInfo.value
          handleDelChat()
        }
      })
    }
    const isGroupUser = handleAreArraysEqual(
      newGroupInfo.memberUserIds,
      groupUser.value.map((v) => v.accountId)
    )
    if (!isGroupUser)
      groupUser.value = await chatGroupMemberList(chatInfo.value.targetId.slice(appOnlyHeader.value.length))
    let isChatGroup = false
    for (let index = 0; index < groupUser.value.length; index++) {
      const item = groupUser.value[index]
      if (item.accountId === user.value.accountId) isChatGroup = true
      newUserObj[appOnlyHeader.value + item.accountId] = {
        uid: appOnlyHeader.value + item.accountId,
        id: item.accountId,
        name: item.userName,
        img: item.photo || item.headImg,
        userInfo: { userId: item.id, userName: item.userName, photo: item.photo, headImg: item.headImg }
      }
    }
    isChat.value = newGroupInfo.id ? isChatGroup : true
  } else {
    isChat.value = true
    groupUser.value = []
    newUserObj[appOnlyHeader.value + user.value.accountId] = {
      uid: appOnlyHeader.value + user.value.accountId,
      id: user.value.accountId,
      name: user.value.userName,
      img: user.value.photo || user.value.headImg,
      userInfo: {
        userId: user.value.id,
        userName: user.value.userName,
        photo: user.value.photo,
        headImg: user.value.headImg
      }
    }
    newUserObj[chatInfo.value?.chatObjectInfo.uid] = chatInfo.value?.chatObjectInfo
  }
  chatInfoUser.value = newUserObj
}
const handleMessages = async (data, type) => {
  const { newMessages, withdrawId } = await handleHistoryMessages(data, chatInfoUser.value)
  console.log(newMessages)
  if (type) {
    chatInfoMessages.value = [...chatInfoMessages.value, ...newMessages].filter((v) => !withdrawId?.includes(v.uid))
    handleRenderMessages(withdrawId)
  } else {
    chatInfoMessages.value = [...newMessages, ...chatInfoMessages.value].filter((v) => !withdrawId?.includes(v.uid))
    handleRenderMessages(withdrawId)
  }
}
const handleRenderMessages = async (withdrawId, type) => {
  let timeData = []
  let newMessages = []
  let newChatInfoImg = []
  let newMessagesId = []
  let isGroupAnnouncement = false
  for (let index = 0; index < chatInfoMessages.value.length; index++) {
    const item = chatInfoMessages.value[index]
    if (item.content?.content?.includes('群公告：\n')) isGroupAnnouncement = true
    if (!newMessagesId?.includes(item.uid) && !withdrawId?.includes(item.uid)) {
      newMessagesId.push(item.uid)
      if (!timeData?.includes(format(item.sentTime))) {
        timeData = [format(item.sentTime), format(item.sentTime + 60000)]
        newMessages.push({
          id: item.sentTime,
          type: 'time',
          className: 'GlobalChatMessagesTime',
          content: format(item.sentTime)
        })
      }
      if (item.type === 'RC:ImgMsg') {
        newMessages.push({ ...item, imgIndex: newChatInfoImg.length })
        newChatInfoImg.push(item.content.imageUri)
      } else if (item.type === 'RC:HQVCMsg') {
        newMessages.push({ ...item, audio: new Audio(item.content.remoteUrl) })
      } else if (item.type === 'RC:ImgTextMsg') {
        newMessages.push(item)
        electronFile.value.push(item?.uid)
      } else {
        newMessages.push(item)
      }
    }
  }
  if (isGroupAnnouncement) chatGroupInfo(chatInfo.value.targetId)
  chatInfoImg.value = newChatInfoImg
  chatInfoMessagesData.value = newMessages
  if (type) {
    nextTick(() => {
      scrollRef.value.wrapRef.scrollTop = scrollTopNum.value
    })
  } else {
    nextTick(() => {
      scrollShow.value ? scrollDown() : scrollElHeight()
    })
  }
}
const handleChatMenu = (e, item) => {
  chatMenuItem.value = item
  chatMenuTop.value = e.pageY
  chatMenuLeft.value = e.pageX
  chatMenuShow.value = true
  isChatMenuShow.value = true
}
const handleMessagesMenu = (e, item) => {
  const selection = window.getSelection()
  const copyContent = selection.toString() || item?.content?.content
  const now = new Date().getTime()
  const timeDifference = now - item.sentTime
  const isWithdraw = timeDifference < 180000
  messagesMenuItem.value = { ...item, copyContent, isWithdraw }
  messagesMenuTop.value = e.pageY
  messagesMenuLeft.value = e.pageX
  messagesMenuShow.value = true
  isMessagesMenuShow.value = true
}
const handleAudio = (data) => {
  if (chatInfoAudio.value.id) {
    if (chatInfoAudio.value.id === data.id) {
      chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime
      handlePauseAudio()
    } else {
      chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime
      handlePauseAudio()
      chatInfoAudio.value = { id: data.id, audio: data.audio }
      chatInfoAudio.value.audio.currentTime = 0
      handlePlayAudio()
    }
  } else {
    chatInfoAudio.value = { id: data.id, audio: data.audio }
    chatInfoAudio.value.audio.currentTime = 0
    handlePlayAudio()
  }
}
const handleGoAudio = (data) => {
  if (chatInfoAudio.value.id) {
    chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime
    handlePauseAudio()
  }
  chatInfoAudio.value = { id: data.id, audio: data.audio }
  chatInfoAudio.value.audio.currentTime = chatInfoAudioObj.value[chatInfoAudio.value.id] || 0
  handlePlayAudio()
}
const handlePlayAudio = () => {
  chatInfoAudio.value.audio.play()
  chatInfoAudio.value.audio.addEventListener('ended', () => {
    chatInfoAudioObj.value[chatInfoAudio.value.id] = 0
    chatInfoAudio.value = {}
  })
}
const handlePauseAudio = () => {
  chatInfoAudio.value.audio.pause()
  chatInfoAudio.value = {}
}
const handleImgLoad = () => {
  if (scrollShow.value) scrollDown()
}
const handleDrop = (event) => {
  event.preventDefault()
  const files = event.dataTransfer.files
  if (files.length) {
    const mewFileList = []
    for (let index = 0; index < files.length; index++) {
      const file = files.item(index)
      const extName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      mewFileList.push({ id: guid(), name: file.name, extName, size: file.size, file: file })
    }
    fileList.value = mewFileList
    fileShow.value = true
  }
}
const fileCallback = (data) => {
  if (data) {
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      fileUpload(item)
    }
  }
  fileList.value = []
  fileShow.value = false
}
const handlePasteImg = (file) => {
  fileImg.value = file
  imgShow.value = true
}
const imgCallback = (type) => {
  if (type) fileUpload(fileImg.value)
  fileImg.value = {}
  imgShow.value = false
}
const fileUpload = async (file) => {
  const params = new FormData()
  params.append('file', file.file)
  params.append('isKeepAlive', true)
  params.append('uid', file.id || file.uid || guid())
  const { data } = await api.globalUpload(params, () => { })
  if (['png', 'jpg', 'jpeg']?.includes(data.extName)) {
    handleSendImgMessage(api.openImgURL(data.newFileName))
  } else {
    localStorage.setItem(data.id, JSON.stringify(data))
    handleSendFileMessage(data.id)
    if (chatInfo.value.type === 3) submitChatGroupFile(data.id)
  }
}
const submitChatGroupFile = async (fileId) => {
  await api.chatGroupFileAdd({
    form: { chatGroupId: chatInfo.value.targetId.slice(appOnlyHeader.value.length), fileId }
  })
}
const isMacText = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent?.includes('macintosh') || userAgent?.includes('mac os x')
}
const handleSetting = () => {
  if (!isChat.value) return
  settingShow.value = !settingShow.value
}
const handleKeyCode = (data) => {
  const userIdData = data?.mentions?.map((v) => appOnlyHeader.value + v.userInfo.accountId)
  const mentionData = userIdData.length
    ? { mentionedContent: '', type: 2, userIdList: Array.from(new Set(userIdData)) }
    : {}
  handleSendTextMessage(data.content, mentionData)
}
const handleSendTextMessage = (contentText, mentionData = {}) => {
  if (!contentText.replace(/^\s+|\s+$/g, '')) return
  const message = new RongIMLib.TextMessage({ content: contentText, mentionedInfo: mentionData })
  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
  const options = {
    onSendBefore: (message) => {
      scrollShow.value = true
      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)
    }
  }
  handleSendMessage(conversation, message, options, (code, msg, data) => {
    if (code === 0) {
      let newChatInfoMessages = []
      for (let index = 0; index < chatInfoMessages.value.length; index++) {
        const item = chatInfoMessages.value[index]
        newChatInfoMessages.push(
          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item
        )
      }
      chatInfoMessages.value = newChatInfoMessages
      handleRenderMessages([], true)
      console.log('消息发送成功：', data)
    } else {
      console.log('消息发送失败：', code, msg)
    }
  })
}
const handleSendImgMessage = (url) => {
  const message = new RongIMLib.ImageMessage({ content: '', imageUri: url })
  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
  const options = {
    onSendBefore: (message) => {
      scrollShow.value = true
      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)
    }
  }
  handleSendMessage(conversation, message, options, (code, msg, data) => {
    if (code === 0) {
      let newChatInfoMessages = []
      for (let index = 0; index < chatInfoMessages.value.length; index++) {
        const item = chatInfoMessages.value[index]
        newChatInfoMessages.push(
          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item
        )
      }
      chatInfoMessages.value = newChatInfoMessages
      handleRenderMessages([], true)
      console.log('消息发送成功：', data)
    } else {
      console.log('消息发送失败：', code, msg)
    }
  })
}
const handleSendFileMessage = (fileId) => {
  const PersonMessage = RongIMLib.registerMessageType('RC:ImgTextMsg', true, true, [], false)
  const message = new PersonMessage({ content: `[文件],${fileId}`, title: '[文件]' })
  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
  const options = {
    onSendBefore: (message) => {
      scrollShow.value = true
      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)
    }
  }
  handleSendMessage(conversation, message, options, (code, msg, data) => {
    if (code === 0) {
      let newChatInfoMessages = []
      for (let index = 0; index < chatInfoMessages.value.length; index++) {
        const item = chatInfoMessages.value[index]
        newChatInfoMessages.push(
          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item
        )
      }
      chatInfoMessages.value = newChatInfoMessages
      handleRenderMessages([], true)
      console.log('消息发送成功：', data)
    } else {
      console.log('消息发送失败：', code, msg)
    }
  })
}
const handleSendCustomMessage = (params) => {
  const PersonMessage = RongIMLib.registerMessageType('RC:CmdNtf', true, true, [], false)
  const message = new PersonMessage(params)
  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
  const options = {
    onSendBefore: (message) => {
      scrollShow.value = true
      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)
    }
  }
  handleSendMessage(conversation, message, options, (code, msg, data) => {
    if (code === 0) {
      let newChatInfoMessages = []
      for (let index = 0; index < chatInfoMessages.value.length; index++) {
        const item = chatInfoMessages.value[index]
        newChatInfoMessages.push(
          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item
        )
      }
      chatInfoMessages.value = newChatInfoMessages
      handleRenderMessages([], true)
      console.log('消息发送成功：', data)
    } else {
      console.log('消息发送失败：', code, msg)
    }
  })
}
const handleSendMessage = async (conversation, message, options, callback) => {
  const { code, msg, data } = await RongIMLib.sendMessage(conversation, message, options)
  if (code === 0) handleRefresh()
  callback(code, msg, data)
}
const handleNotificationClick = async (notification) => {
  const { code } = await RongIMLib.setConversationNotificationStatus(
    { conversationType: chatMenuItem.value.type, targetId: chatMenuItem.value.id },
    notification
  )
  if (!code) handleRefresh()
}
const handleIsTopClick = async (isTop) => {
  const { code } = await RongIMLib.setConversationToTop(
    { conversationType: chatMenuItem.value.type, targetId: chatMenuItem.value.id },
    isTop
  )
  if (!code) handleRefresh()
}
const handleDelChat = async () => {
  const { code, msg } = await RongIMLib.removeConversation({
    conversationType: chatMenuItem.value.type,
    targetId: chatMenuItem.value.id
  })
  if (code === 0) {
    console.log('消息删除成功')
    handleRefresh('del', chatMenuItem.value)
  } else {
    console.log('消息删除失败：', code, msg)
  }
}
const handleDelMessage = async () => {
  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
  const messagesData = [
    {
      messageUId: messagesMenuItem.value.uid,
      sentTime: messagesMenuItem.value.sentTime,
      messageDirection: messagesMenuItem.value.direction
    }
  ]
  const { code, msg } = await RongIMLib.deleteMessages(conversation, messagesData)
  if (code === 0) {
    console.log('消息删除成功')
    chatInfoMessages.value = chatInfoMessages.value.filter((item) => item.uid !== messagesMenuItem.value.uid)
    handleRenderMessages([], true)
  } else {
    console.log('消息删除失败：', code, msg)
  }
}
const handleWithdrawMessage = async () => {
  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
  const messagesData = {
    messageUId: messagesMenuItem.value.uid,
    sentTime: messagesMenuItem.value.sentTime,
    disableNotification: true
  }
  const { code, msg } = await RongIMLib.recallMessage(conversation, messagesData)
  if (code === 0) {
    handleChatClick(chatInfo.value, true)
    console.log('消息撤回成功')
  } else {
    console.log('消息撤回失败：', code, msg)
  }
}
const handleGroup = async (type, data, isOwner) => {
  if (type === 'create') handleCreateGroup(data?.chatObjectInfo?.id)
  if (type === 'add') handleGroupAddUser(data?.chatObjectInfo?.id)
  if (type === 'del') handleGroupDelUser(data?.chatObjectInfo?.id)
  if (type === 'name') handleGroupName(data?.chatObjectInfo?.id)
  if (type === 'qr') handleGroupQr(data?.chatObjectInfo?.id)
  if (type === 'announcement') handleGroupAnnouncement(data?.chatObjectInfo?.id, isOwner)
  if (type === 'transfer') handleGroupTransfer(data?.chatObjectInfo?.id)
  if (type === 'quit') {
    const PersonMessage = RongIMLib.registerMessageType('RC:CmdNtf', true, true, [], false)
    const message = new PersonMessage(data)
    const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }
    const options = { onSendBefore: () => { } }
    await RongIMLib.sendMessage(conversation, message, options)
    handleQuitDelChat()
  }
}
const handleQuitDelChat = async () => {
  const { code, msg } = await RongIMLib.removeConversation({
    conversationType: chatInfo.value.type,
    targetId: chatInfo.value.targetId
  })
  if (code === 0) {
    console.log('消息删除成功')
    handleRefresh()
  } else {
    console.log('消息删除失败：', code, msg)
  }
}
const handleCreateGroup = (id) => {
  userId.value = id ? [user.value?.accountId, id] : [user.value?.accountId]
  createGroupShow.value = true
}
const handleGroupAddUser = (id) => {
  infoId.value = id
  addShow.value = true
}
const handleGroupDelUser = (id) => {
  infoId.value = id
  delShow.value = true
}
const handleGroupName = (id) => {
  infoId.value = id
  nameShow.value = true
}
const handleGroupQr = (id) => {
  infoId.value = id
  qrShow.value = true
}
const handleGroupAnnouncement = (id, isOwner) => {
  infoId.value = id
  isGroupOwner.value = isOwner
  announcementShow.value = true
}
const handleGroupTransfer = (id) => {
  infoId.value = id
  transferShow.value = true
}
const handleVote = () => {
  infoId.value = chatInfo.value.targetId.slice(appOnlyHeader.value.length)
  voteShow.value = true
}
const handleVoteDetails = (row) => {
  voteId.value = row.id
  voteDetailsShow.value = true
}
const createCallback = (data) => {
  settingShow.value = false
  if (data) emit('send', data)
  createGroupShow.value = false
}
const addCallback = async (type, data) => {
  settingShow.value = false
  if (type) {
    await handleUser()
    handleSendCustomMessage(data)
  }
  addShow.value = false
}
const delCallback = async (type, data) => {
  settingShow.value = false
  if (type) {
    await handleUser()
    handleSendCustomMessage(data)
  }
  delShow.value = false
}
const nameCallback = async (type, data) => {
  settingShow.value = false
  if (type) {
    await handleUser()
    handleTime()
    handleSendCustomMessage(data)
  }
  nameShow.value = false
}
const announcementCallback = async (type, data) => {
  settingShow.value = false
  if (type) {
    handleSendTextMessage(data)
  }
  announcementShow.value = false
}
const transferCallback = async (type, data) => {
  settingShow.value = false
  if (type) {
    await handleUser()
    handleSendCustomMessage(data)
  }
  transferShow.value = false
}
const voteCallback = () => {
  createVoteShow.value = true
}
const handleVoteCallback = () => {
  voteRefresh.value = guid()
  createVoteShow.value = false
  voteDetailsShow.value = false
}
const handleTime = () => {
  emit('time')
}
const handleRefresh = (type, data) => {
  emit('refresh', type, data)
}
onUnmounted(() => {
  if (isElectron) {
    const fileContent = JSON.stringify(electronRecordObj.value)
    if (chatId.value) globalElectronSaveRecord(chatId.value, fileContent)
  }
})
watch(
  () => chatId.value,
  (newValue, oldValue) => {
    if (isElectron) {
      const fileContent = JSON.stringify(electronRecordObj.value)
      electronRecordObj.value = {}
      electronRecordData.value = []
      if (oldValue) globalElectronSaveRecord(oldValue, fileContent)
      if (newValue) globalElectronReadRecord(newValue)
    }
    if (chatId.value) {
      for (let index = 0; index < props.chatList.length; index++) {
        const item = props.chatList[index]
        if (chatId.value === item.id) handleChatClick(item)
      }
    }
  },
  { immediate: true }
)
watch(
  () => chatList.value,
  () => {
    if (chatId.value) {
      let isShow = true
      for (let index = 0; index < props.chatList.length; index++) {
        const item = props.chatList[index]
        if (chatId.value === item.id) {
          isShow = false
          chatInfo.value = item
        }
      }
      if (isShow) {
        chatId.value = ''
        chatInfo.value = {}
      }
    }
  },
  { immediate: true }
)
defineExpose({ getNewestMessages })
</script>
<style lang="scss">
@import url('../scss/emotion.scss');

.GlobalChatView {
  width: 100%;
  height: 100%;
  display: flex;

  &.GlobalChatMacView {
    .GlobalChatViewList {
      .GlobalChatViewListHead {
        height: 56px;
      }

      .GlobalChatViewMessagesList {
        height: calc(100% - 92px);
      }
    }

    .GlobalChatViewDrag {
      height: 56px;
    }

    .GlobalChatWindow {
      .GlobalChatWindowTitle {
        height: 56px;

        .GlobalChatWindowMore {
          margin: 0;
        }
      }

      .GlobalChatWindowScroll {
        height: calc(100% - 222px);

        &.GlobalChatWindowNoChat {
          height: calc(100% - (56px + var(--zy-height)));
        }
      }
    }
  }

  .GlobalChatViewList {
    width: 280px;
    height: 100%;
    border-right: 1px solid var(--zy-el-border-color-lighter);

    .GlobalChatViewListHead {
      width: 100%;
      height: 66px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 20px 0 20px;
      -webkit-app-region: drag;

      .zy-el-autocomplete {
        width: 199px;
        height: var(--zy-height-routine);
        -webkit-app-region: no-drag;

        .zy-el-input {
          width: 199px;
          height: var(--zy-height-routine);
        }
      }

      .GlobalChatViewListHeadIcon {
        width: 32px;
        height: 32px;
        cursor: pointer;
        -webkit-app-region: no-drag;
      }
    }

    .GlobalChatViewMessagesList {
      width: 100%;
      height: calc(100% - 102px);

      .GlobalChatViewMessagesItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 20px;
        cursor: pointer;

        &:hover {
          background: #f9f9fa;
        }

        &.is-top {
          background: #f6f6f6;
        }

        &.is-active {
          background: #f2f2f2;
        }

        .zy-el-badge {
          width: 42px;
          height: 42px;
        }

        .zy-el-image {
          width: 42px;
          height: 42px;
          border-radius: 50%;
          overflow: hidden;
        }

        .GlobalChatViewMessagesInfo {
          width: calc(100% - 56px);
          height: 42px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          position: relative;

          .GlobalChatViewNotInform {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            right: 0;
            bottom: 0;

            path {
              fill: var(--zy-el-text-color-secondary);
            }
          }

          .GlobalChatViewMessagesName {
            display: flex;
            align-items: center;

            div {
              flex: 1;
              font-size: 14px;
            }

            .GlobalChatViewMessagesNameGroup {
              position: relative;
              padding-right: 39px;

              span {
                padding: 2px 6px;
                font-size: 12px;
                border-radius: 3px;
                position: absolute;
                top: 50%;
                right: 3px;
                transform: translateY(-50%);
                text-align: center;
                color: var(--zy-el-color-success);
                background: var(--zy-el-color-success-light-9);
              }
            }

            span {
              font-size: 12px;
              color: var(--zy-el-text-color-secondary);
            }
          }

          .GlobalChatViewNotInform+.GlobalChatViewMessagesText {
            padding-right: 18px;
          }

          .GlobalChatViewMessagesText {
            font-size: 12px;
            color: var(--zy-el-text-color-secondary);
          }
        }
      }
    }

    .GlobalChatClearAway {
      width: 100%;
      height: 36px;
      display: flex;
      align-items: center;
      color: var(--zy-el-text-color-secondary);
      border-top: 1px solid var(--zy-el-border-color-lighter);
      font-size: 14px;
      padding-top: 4px;
      padding-left: 46px;
      position: relative;
      cursor: pointer;

      div {
        width: 22px;
        height: 22px;
        position: absolute;
        top: 50%;
        left: 20px;
        transform: translateY(-50%);

        .icon {
          width: 22px;
          height: 22px;

          path {
            fill: var(--zy-el-text-color-secondary);
          }
        }
      }
    }
  }

  .GlobalChatViewDrag {
    width: calc(100% - 280px);
    height: 66px;
    position: relative;
    -webkit-app-region: drag;

    &::before {
      content: '';
      width: 96px;
      height: 28px;
      position: absolute;
      top: 0;
      right: 0;
      background: transparent;
      -webkit-app-region: no-drag;
    }
  }

  .GlobalChatWindow {
    width: calc(100% - 280px);
    height: 100%;
    background: #f9f9fa;
    position: relative;

    .GlobalChatWindowTitle {
      width: 100%;
      height: 66px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 20px 0 20px;
      background: #fff;
      position: relative;
      -webkit-app-region: drag;

      &::after {
        content: '';
        width: 100%;
        height: 1px;
        position: absolute;
        left: 0;
        bottom: 0;
        background: var(--zy-el-border-color-lighter);
      }

      &::before {
        content: '';
        width: 96px;
        height: 28px;
        position: absolute;
        top: 0;
        right: 0;
        background: transparent;
        -webkit-app-region: no-drag;
        z-index: 8;
      }

      .ellipsis {
        max-width: calc(100% - 52px);
        -webkit-app-region: no-drag;
      }

      .GlobalChatWindowMore {
        width: 32px;
        height: 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        -webkit-app-region: no-drag;
        margin-top: 16px;

        .zy-el-icon {
          font-size: 20px;
        }
      }
    }

    .GlobalChatViewNoMessage {
      width: 100%;
      height: var(--zy-height);
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.6);
      color: #fff;
      font-size: 12px;

      .zy-el-icon {
        font-size: 16px;
        margin-right: 2px;
      }
    }

    .GlobalChatWindowScroll {
      width: 100%;
      height: calc(100% - 232px);

      &.GlobalChatWindowNoChat {
        height: calc(100% - (66px + var(--zy-height)));
      }

      .GlobalChatGroupAnnouncement {
        width: calc(100% - 40px);
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        box-shadow: var(--zy-el-box-shadow-light);
        border-radius: 4px;
        padding: 10px 20px;
        background: #fff;
        z-index: 6;

        .GlobalChatGroupAnnouncementTitle {
          width: 100%;
          height: 32px;
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          padding-bottom: 9px;

          div {
            display: flex;
            align-items: flex-end;
            justify-content: center;
            font-size: 14px;
            line-height: 14px;
            font-weight: bold;
            padding-left: 2px;

            span {
              width: 22px;
              height: 22px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 6px;

              .icon {
                width: 22px;
                height: 22px;

                path {
                  fill: var(--zy-el-color-primary);
                }
              }
            }
          }

          .zy-el-icon {
            color: #ccc;
            font-size: 18px;
            cursor: pointer;
          }
        }

        .GlobalChatGroupAnnouncementContent {
          font-size: 14px;
          line-height: 1.6;
        }
      }

      .GlobalChatWindowBody {
        padding: var(--zy-distance-five) 0;

        .GlobalChatMessagesTime {
          width: 100%;
          font-size: 14px;
          text-align: center;
          color: var(--zy-el-text-color-secondary);
          padding: var(--zy-distance-five) var(--zy-distance-two);
        }

        .GlobalChatMessagesInform {
          width: 100%;
          font-size: 12px;
          text-align: center;
          color: var(--zy-el-text-color-secondary);
          padding: var(--zy-distance-two);
        }

        .GlobalChatMessages,
        .GlobalChatSelfMessages {
          display: flex;
          justify-content: space-between;
          padding: var(--zy-distance-five) var(--zy-distance-two);

          .GlobalChatWindowUserImg {
            width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);
            height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);

            .zy-el-image {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
          }

          .GlobalChatMessagesInfo {
            display: flex;
            flex-wrap: wrap;
            width: calc(100% - ((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));
            position: relative;

            .GlobalChatMessagesImgSlot {
              width: 168px;
              height: 68px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 28px;
              background: #fff;
              color: var(--zy-el-text-color-regular);
            }

            .zy-el-image {
              width: 168px;
              height: auto;
              border-radius: 8px;
              overflow: hidden;
            }

            .GlobalChatMessagesName {
              width: 100%;
              font-size: 12px;
              line-height: 12px;
              padding-bottom: 6px;
              color: var(--zy-el-text-color-secondary);
            }

            .GlobalChatMessagesText {
              position: relative;
              display: inline-block;
              max-width: 100%;
              padding: 10px var(--zy-distance-five);
              font-size: var(--zy-text-font-size);
              line-height: var(--zy-line-height);
              background: #fff;
              border-radius: var(--el-border-radius-base);
              border: 1px solid var(--zy-el-border-color-light);
              word-wrap: break-word;
              white-space: pre-wrap;
              z-index: 2;

              span {
                position: absolute;
                width: 10px;
                height: 10px;
                z-index: 2;
                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);

                &::after {
                  content: '';
                  position: absolute;
                  width: 10px;
                  height: 10px;
                  transform: rotate(45deg);
                  background: #fff;
                  border: 1px solid var(--zy-el-border-color-light);
                  box-sizing: border-box;
                }
              }
            }

            .GlobalChatMessagesCustom {
              width: 320px;
              padding: 12px 16px;
              display: flex;
              flex-direction: column;
              background: #fff;
              position: relative;
              border-radius: var(--el-border-radius-base);
              border: 1px solid var(--zy-el-border-color-light);
              word-wrap: break-word;
              white-space: pre-wrap;
              cursor: pointer;

              &.file {
                padding-right: 58px;
              }

              &.unknown {
                width: auto;
              }

              span {
                position: absolute;
                width: 10px;
                height: 10px;
                z-index: 2;
                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);

                &::after {
                  content: '';
                  position: absolute;
                  width: 10px;
                  height: 10px;
                  transform: rotate(45deg);
                  background: #fff;
                  border: 1px solid var(--zy-el-border-color-light);
                  box-sizing: border-box;
                }
              }

              .GlobalChatMessagesCustomName {
                font-size: var(--zy-text-font-size);
                line-height: var(--zy-line-height);
                padding-bottom: var(--zy-font-text-distance-five);
                word-break: break-all;
              }

              .GlobalChatMessagesCustomText {
                color: var(--zy-el-text-color-secondary);
                font-size: calc(var(--zy-text-font-size) - 2px);
              }

              .GlobalChatMessagesFileDownload {
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.2);
                position: absolute;
                top: 0;
                right: 0;
                z-index: 9;
              }

              .globalFileIcon {
                width: 40px;
                height: 40px;
                vertical-align: middle;
                position: absolute;
                top: 12px;
                right: 12px;

                &.globalFileUnknown {
                  background: url('../img/unknown.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFilePDF {
                  background: url('../img/PDF.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileWord {
                  background: url('../img/Word.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileExcel {
                  background: url('../img/Excel.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFilePicture {
                  background: url('../img/picture.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileVideo {
                  background: url('../img/video.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileTXT {
                  background: url('../img/TXT.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileCompress {
                  background: url('../img/compress.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileWPS {
                  background: url('../img/WPS.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFilePPT {
                  background: url('../img/PPT.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }
              }

              .GlobalChatMessagesVoteTitleBody {
                width: 100%;
                display: flex;
                align-items: flex-end;
                justify-content: space-between;
                padding: 0 2px 8px 2px;

                .GlobalChatMessagesVoteTitle {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-weight: bold;
                  font-size: var(--zy-name-font-size);

                  .GlobalChatMessagesVoteIcon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 6px;

                    svg {
                      width: 16px;
                      height: 16px;

                      path {
                        fill: var(--zy-el-color-warning);
                      }
                    }
                  }
                }

                .GlobalChatMessagesVoteTime {
                  font-size: var(--zy-text-font-size);
                  color: var(--zy-el-text-color-secondary);
                }
              }

              .GlobalChatMessagesVoteInfo {
                width: 100%;
                height: 92px;
                display: flex;
                align-items: center;
                position: relative;
                padding-top: 2px;

                .GlobalChatMessagesVoteInfoIcon {
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: absolute;
                  top: 0;
                  left: 0;
                  z-index: 1;
                }

                .GlobalChatMessagesVoteName {
                  width: 100%;
                  height: calc((var(--zy-text-font-size) * var(--zy-line-height)) * 3);
                  color: #fff;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 3;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-size: var(--zy-text-font-size);
                  line-height: var(--zy-line-height);
                  padding: 0 72px 0 16px;
                  overflow: hidden;
                  position: relative;
                  z-index: 2;
                }
              }
            }

            .GlobalChatMessagesFile {
              width: 320px;
              padding: 12px 16px;
              display: flex;
              flex-direction: column;
              background: #fff;
              position: relative;
              padding-right: 58px;
              border-radius: var(--el-border-radius-base);
              border: 1px solid var(--zy-el-border-color-light);
              word-wrap: break-word;
              white-space: pre-wrap;
              cursor: pointer;

              span {
                position: absolute;
                width: 10px;
                height: 10px;
                z-index: 2;
                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);

                &::after {
                  content: '';
                  position: absolute;
                  width: 10px;
                  height: 10px;
                  transform: rotate(45deg);
                  background: #fff;
                  border: 1px solid var(--zy-el-border-color-light);
                  box-sizing: border-box;
                }
              }

              .GlobalChatMessagesFileName {
                font-size: var(--zy-text-font-size);
                line-height: var(--zy-line-height);
                padding-bottom: var(--zy-font-text-distance-five);
                word-break: break-all;
              }

              .GlobalChatMessagesFileSize {
                color: var(--zy-el-text-color-secondary);
                font-size: calc(var(--zy-text-font-size) - 2px);
              }

              .globalFileIcon {
                width: 40px;
                height: 40px;
                vertical-align: middle;
                position: absolute;
                top: 12px;
                right: 12px;

                &.globalFileUnknown {
                  background: url('../img/unknown.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFilePDF {
                  background: url('../img/PDF.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileWord {
                  background: url('../img/Word.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileExcel {
                  background: url('../img/Excel.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFilePicture {
                  background: url('../img/picture.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileVideo {
                  background: url('../img/video.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileTXT {
                  background: url('../img/TXT.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileCompress {
                  background: url('../img/compress.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFileWPS {
                  background: url('../img/WPS.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }

                &.globalFilePPT {
                  background: url('../img/PPT.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                }
              }
            }

            .GlobalChatMessagesCustomUnknown {
              font-size: var(--zy-text-font-size);
              line-height: var(--zy-line-height);
            }
          }
        }

        .GlobalChatMessages {
          .GlobalChatMessagesInfo {
            padding-right: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));

            .GlobalChatMessagesText {
              span {
                left: 0;
                transform: translate(-50%, -50%);

                &::after {
                  border-top-color: transparent !important;
                  border-right-color: transparent !important;
                }
              }

              .GlobalChatVoice {
                padding: 0 26px;
                position: relative;
                cursor: pointer;

                &::after {
                  content: '';
                  width: 26px;
                  height: 19px;
                  position: absolute;
                  top: 50%;
                  left: 0;
                  transform: translateY(-50%);
                  background: url('../img/record_receive.png') no-repeat;
                  background-size: auto 19px;
                  background-position: center left;
                }

                &.is-active {
                  &::after {
                    background: url('../img/record_receive_gif.gif') no-repeat;
                    background-size: auto 19px;
                    background-position: center left;
                  }
                }
              }

              .GlobalChatVoiceContinue {
                width: 68px;
                height: 20px;
                font-size: 10px;
                line-height: 20px;
                position: absolute;
                top: 50%;
                left: 110%;
                transform: translateY(-50%);
                color: var(--zy-el-text-color-regular);
                background: #e6e6e6;
                text-align: center;
                border-radius: 10px;
                cursor: pointer;
              }
            }

            .GlobalChatMessagesCustom {
              span {
                left: 0;
                transform: translate(-50%, -50%);

                &::after {
                  border-top-color: transparent !important;
                  border-right-color: transparent !important;
                }
              }
            }

            .GlobalChatMessagesFile {
              span {
                left: 0;
                transform: translate(-50%, -50%);

                &::after {
                  border-top-color: transparent !important;
                  border-right-color: transparent !important;
                }
              }
            }
          }
        }

        .GlobalChatSelfMessages {
          flex-direction: row-reverse;

          .GlobalChatMessagesInfo {
            justify-content: flex-end;
            padding-left: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));

            .GlobalChatMessagesText {
              span {
                right: 0;
                transform: translate(50%, -50%);

                &::after {
                  border-left-color: transparent !important;
                  border-bottom-color: transparent !important;
                }
              }

              .GlobalChatVoice {
                padding: 0 26px;
                position: relative;
                cursor: pointer;
                text-align: right;

                &::after {
                  content: '';
                  width: 26px;
                  height: 19px;
                  position: absolute;
                  top: 50%;
                  right: 0;
                  transform: translateY(-50%) rotate(180deg);
                  background: url('../img/record_receive.png') no-repeat;
                  background-size: auto 19px;
                  background-position: center left;
                }

                &.is-active {
                  &::after {
                    background: url('../img/record_receive_gif.gif') no-repeat;
                    background-size: auto 19px;
                    background-position: center left;
                  }
                }
              }

              .GlobalChatVoiceContinue {
                width: 68px;
                height: 20px;
                font-size: 10px;
                line-height: 20px;
                position: absolute;
                top: 50%;
                right: 110%;
                transform: translateY(-50%);
                color: var(--zy-el-text-color-regular);
                background: #e6e6e6;
                text-align: center;
                border-radius: 10px;
                cursor: pointer;
              }
            }

            .GlobalChatMessagesCustom {
              span {
                right: 0;
                transform: translate(50%, -50%);

                &::after {
                  border-left-color: transparent !important;
                  border-bottom-color: transparent !important;
                }
              }
            }

            .GlobalChatMessagesFile {
              span {
                right: 0;
                transform: translate(50%, -50%);

                &::after {
                  border-left-color: transparent !important;
                  border-bottom-color: transparent !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .GlobalChatViewMenu {
    position: fixed;
    background: #fff;
    box-shadow: var(--zy-el-box-shadow);
    border-radius: var(--el-border-radius-base);
    border: 1px solid var(--zy-el-border-color-lighter);
    padding: 6px;
    z-index: 9;

    .GlobalChatViewMenuItem {
      min-width: 52px;
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background: #f8f8fa;
      }
    }

    .GlobalChatViewMenuLine {
      width: 100%;
      height: 7px;
      position: relative;

      &::after {
        content: '';
        width: calc(100% - 16px);
        border-top: 1px solid var(--zy-el-border-color-light);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  .GlobalGroupNamePopupWindow {
    .chat-popup-window-body {
      height: 168px;
    }
  }

  .GlobalGroupQrPopupWindow {
    .chat-popup-window-body {
      height: auto;
    }
  }

  .GlobalGroupAnnouncementPopupWindow {
    .chat-popup-window-body {
      height: auto;
    }
  }

  .GlobalGroupVotePopupWindow {
    .chat-popup-window-body {
      height: 82%;
      max-height: 680px;
    }
  }
}

.GlobalChatViewAutocomplete {
  .GlobalChatViewMessagesItem {
    width: 218px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    cursor: pointer;

    &:hover {
      background: #f9f9fa;
    }

    &.is-top {
      background: #f6f6f6;
    }

    &.is-active {
      background: #f2f2f2;
    }

    .zy-el-badge {
      width: 42px;
      height: 42px;
    }

    .zy-el-image {
      width: 42px;
      height: 42px;
      border-radius: 50%;
      overflow: hidden;
    }

    .GlobalChatViewMessagesInfo {
      width: calc(100% - 56px);
      height: 42px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      line-height: normal;
      position: relative;

      .GlobalChatViewNotInform {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        right: 0;
        bottom: 0;

        path {
          fill: var(--zy-el-text-color-secondary);
        }
      }

      .GlobalChatViewMessagesName {
        display: flex;

        div {
          flex: 1;
          font-size: 14px;
        }

        span {
          font-size: 12px;
          color: var(--zy-el-text-color-secondary);
        }
      }

      .GlobalChatViewNotInform+.GlobalChatViewMessagesText {
        padding-right: 18px;
      }

      .GlobalChatViewMessagesText {
        font-size: 12px;
        color: var(--zy-el-text-color-secondary);
      }
    }
  }
}
</style>
