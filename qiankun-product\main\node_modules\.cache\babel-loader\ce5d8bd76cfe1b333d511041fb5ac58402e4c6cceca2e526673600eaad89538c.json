{"ast": null, "code": "import { ref, onMounted } from 'vue';\nimport QrcodeVue from 'qrcode.vue';\nimport { systemLogo, systemName, appDownloadUrl, systemLoginContact, user } from 'common/js/system_var.js';\nimport { LoginView } from './LoginView.js';\nimport config from 'common/config';\nimport ResetPassword from './component/ResetPassword.vue';\nvar __default__ = {\n  name: 'LoginViewCopy'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var backgroundImage = ref(`${config.API_URL}/pageImg/open/homePage?areaId=${user.value.areaId}`);\n    var show = ref(false);\n    var _LoginView = LoginView(),\n      loginVerifyShow = _LoginView.loginVerifyShow,\n      whetherVerifyCode = _LoginView.whetherVerifyCode,\n      loginDisabled = _LoginView.loginDisabled,\n      loading = _LoginView.loading,\n      checked = _LoginView.checked,\n      imgList = _LoginView.imgList,\n      LoginForm = _LoginView.LoginForm,\n      form = _LoginView.form,\n      rules = _LoginView.rules,\n      countDownText = _LoginView.countDownText,\n      slideVerify = _LoginView.slideVerify,\n      disabled = _LoginView.disabled,\n      loginQrcode = _LoginView.loginQrcode,\n      loginQrcodeShow = _LoginView.loginQrcodeShow,\n      handleBlur = _LoginView.handleBlur,\n      handleGetVerifyCode = _LoginView.handleGetVerifyCode,\n      onAgain = _LoginView.onAgain,\n      onSuccess = _LoginView.onSuccess,\n      globalData = _LoginView.globalData,\n      submitForm = _LoginView.submitForm,\n      loginInfo = _LoginView.loginInfo,\n      refresh = _LoginView.refresh,\n      hideQrcode = _LoginView.hideQrcode;\n    onMounted(function () {\n      loginInfo();\n      globalData();\n    });\n    var __returned__ = {\n      backgroundImage,\n      show,\n      loginVerifyShow,\n      whetherVerifyCode,\n      loginDisabled,\n      loading,\n      checked,\n      imgList,\n      LoginForm,\n      form,\n      rules,\n      countDownText,\n      slideVerify,\n      disabled,\n      loginQrcode,\n      loginQrcodeShow,\n      handleBlur,\n      handleGetVerifyCode,\n      onAgain,\n      onSuccess,\n      globalData,\n      submitForm,\n      loginInfo,\n      refresh,\n      hideQrcode,\n      ref,\n      onMounted,\n      QrcodeVue,\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get appDownloadUrl() {\n        return appDownloadUrl;\n      },\n      get systemLoginContact() {\n        return systemLoginContact;\n      },\n      get user() {\n        return user;\n      },\n      get LoginView() {\n        return LoginView;\n      },\n      get config() {\n        return config;\n      },\n      ResetPassword\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "QrcodeVue", "systemLogo", "systemName", "appDownloadUrl", "systemLoginContact", "user", "<PERSON><PERSON><PERSON>ie<PERSON>", "config", "ResetPassword", "__default__", "name", "backgroundImage", "API_URL", "value", "areaId", "show", "_<PERSON><PERSON><PERSON>ie<PERSON>", "loginVerifyShow", "whetherVerifyCode", "loginDisabled", "loading", "checked", "imgList", "LoginForm", "form", "rules", "countDownText", "slideVerify", "disabled", "loginQrcode", "loginQrcodeShow", "handleBlur", "handleGetVerifyCode", "onAgain", "onSuccess", "globalData", "submitForm", "loginInfo", "refresh", "hideQrcode"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LoginView/LoginViewCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LoginViewCopy\" :style=\"{ backgroundImage: `url(${backgroundImage})` }\">\r\n    <div class=\"login-header\">\r\n      <div class=\"login-logo-name\">\r\n        <el-image class=\"login-logo\" :src=\"systemLogo\" fit=\"contain\" />\r\n        <div class=\"login-name\" v-html=\"systemName\"></div>\r\n      </div>\r\n      <div class=\"login-header-right\">\r\n        <div class=\"search-box\">\r\n          <input type=\"text\" placeholder=\"请输入搜索内容\" />\r\n          <button class=\"search-btn\" @click=\"search\">\r\n            <img class=\"search-icon\" src=\"../img/search_icon.png\" alt=\"搜索\" />\r\n          </button>\r\n        </div>\r\n        <button class=\"mine-btn\" @click=\"openLogin\">\r\n          <img src=\"../img/mine_btn_bg.png\" alt=\"我的\" />\r\n          <span>\r\n            <img class=\"mine-icon\" src=\"../img/mine_icon.png\" alt=\"我的\" />\r\n            我的\r\n          </span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"LoginViewBox\">\r\n      <div class=\"LoginViewName\">欢迎登录</div>\r\n      <el-form ref=\"LoginForm\" :model=\"form\" :rules=\"rules\" class=\"LoginViewForm\">\r\n        <el-form-item prop=\"account\">\r\n          <div class=\"input-bg\">\r\n            <el-input v-model=\"form.account\" placeholder=\"账号/手机号\" @blur=\"handleBlur\" clearable />\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item prop=\"password\">\r\n          <div class=\"input-bg\">\r\n            <el-input type=\"password\" v-model=\"form.password\" placeholder=\"密码\" show-password clearable />\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item class=\"smsValidation\" v-if=\"loginVerifyShow && whetherVerifyCode\" prop=\"verifyCode\">\r\n          <el-input v-model=\"form.verifyCode\" placeholder=\"短信验证码\" clearable> </el-input>\r\n          <el-button type=\"primary\" @click=\"handleGetVerifyCode\" :disabled=\"countDownText != '获取验证码'\">\r\n            {{ countDownText }}</el-button>\r\n        </el-form-item>\r\n        <div class=\"LoginViewSlideVerify\" v-if=\"loginVerifyShow && !whetherVerifyCode\">\r\n          <xyl-slide-verify ref=\"slideVerify\" @again=\"onAgain\" @success=\"onSuccess\" :disabled=\"disabled\" />\r\n        </div>\r\n        <div class=\"LoginViewFormOperation\">\r\n          <el-checkbox v-model=\"checked\">记住用户名和密码</el-checkbox>\r\n          <div class=\"LoginViewFormOperationText\" @click=\"show = !show\">忘记密码？</div>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"submitForm(LoginForm)\" class=\"LoginViewFormButton login-btn-bg\"\r\n          :loading=\"loading\" :disabled=\"loginDisabled\">{{ loading ? '登录中' : '登录' }}</el-button>\r\n      </el-form>\r\n      <div class=\"LoginViewOperation\" v-if=\"appDownloadUrl\">\r\n        <div class=\"LoginViewOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\" @show=\"refresh\" @hide=\"hideQrcode\">\r\n            <div class=\"LoginViewQrCodeBox\">\r\n              <div class=\"LoginViewQrCodeNameBody\">\r\n                <div class=\"LoginViewQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewQrCodeName\">APP扫码登录</div>\r\n              </div>\r\n              <div class=\"LoginViewQrCodeRefreshBody\">\r\n                <qrcode-vue :value=\"loginQrcode\" :size=\"120\" />\r\n                <div class=\"LoginViewQrCodeRefresh\" v-show=\"loginQrcodeShow\">\r\n                  <el-button type=\"primary\" @click=\"refresh\">刷新</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"LoginViewQrCodeText\">请使用{{ systemName }}APP扫码登录</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewQrCode\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewOperationText\">APP扫码登录</div>\r\n        </div>\r\n        <div class=\"LoginViewOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\">\r\n            <div class=\"LoginViewQrCodeBox\">\r\n              <div class=\"LoginViewQrCodeNameBody\">\r\n                <div class=\"LoginViewQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewQrCodeName\">手机APP下载</div>\r\n              </div>\r\n              <qrcode-vue :value=\"appDownloadUrl\" :size=\"120\" />\r\n              <div class=\"LoginViewQrCodeText\">使用其他软件扫码下载{{ systemName }}APP</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewApp\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewOperationText\">手机APP下载</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"LoginViewSystemTips\" v-if=\"systemLoginContact\">{{ systemLoginContact }}</div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"重置密码\">\r\n      <ResetPassword @callback=\"show = !show\"></ResetPassword>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LoginViewCopy' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport { systemLogo, systemName, appDownloadUrl, systemLoginContact, user } from 'common/js/system_var.js'\r\nimport { LoginView } from './LoginView.js'\r\nimport config from 'common/config'\r\nimport ResetPassword from './component/ResetPassword.vue'\r\nconst backgroundImage = ref(`${config.API_URL}/pageImg/open/homePage?areaId=${user.value.areaId}`)\r\nconst show = ref(false)\r\nconst { loginVerifyShow, whetherVerifyCode, loginDisabled, loading, checked, imgList, LoginForm, form, rules, countDownText, slideVerify, disabled, loginQrcode, loginQrcodeShow, handleBlur, handleGetVerifyCode, onAgain, onSuccess, globalData, submitForm, loginInfo, refresh, hideQrcode } = LoginView()\r\n\r\n\r\nonMounted(() => {\r\n  loginInfo()\r\n  globalData()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.LoginViewCopy {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: no-repeat;\r\n  background-size: 100% 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n\r\n  .login-header {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24px 40px 0 40px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    z-index: 2;\r\n    flex-shrink: 0;\r\n\r\n    .login-logo-name {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .login-logo {\r\n        height: 75px;\r\n        width: 75px;\r\n      }\r\n\r\n      .login-name {\r\n        font-size: 37px;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        margin-left: 15px;\r\n        letter-spacing: 5px;\r\n      }\r\n    }\r\n\r\n\r\n    .login-header-right {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n\r\n      .search-box {\r\n        display: flex;\r\n        align-items: center;\r\n        border-radius: 20px;\r\n        padding: 0 0 0 8px;\r\n        height: 36px;\r\n        border: 1px solid #FFFFFF;\r\n        // width: 350px;\r\n\r\n        input {\r\n          border: none;\r\n          outline: none;\r\n          height: 100%;\r\n          padding: 0 8px;\r\n          border-radius: 20px 0 0 20px;\r\n          background: rgb(0, 0, 0, 0);\r\n          width: calc(100% - 55px);\r\n          color: #fff;\r\n\r\n          &::placeholder {\r\n            color: #fff;\r\n            opacity: 1;\r\n          }\r\n        }\r\n\r\n        .search-btn {\r\n          background: url(\"../img/search_btn_bg.png\") no-repeat center/cover;\r\n          border: none;\r\n          width: 55px;\r\n          height: 36px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n\r\n          .search-icon {\r\n            width: 18px;\r\n            height: 18px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .mine-btn {\r\n        background: none;\r\n        border: none;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          height: 39px;\r\n        }\r\n\r\n        span {\r\n          position: absolute;\r\n          left: 0;\r\n          width: 100%;\r\n          text-align: center;\r\n          color: #fff;\r\n          font-size: 14px;\r\n          line-height: 39px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n\r\n      .mine-btn .mine-icon {\r\n        width: 20px;\r\n        height: 20px;\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewBox {\r\n    position: absolute;\r\n    left: 100px; // 距离左侧 100px\r\n    top: 55%;\r\n    transform: translateY(-50%);\r\n    width: 460px;\r\n    height: 640px;\r\n    background: url(\"../img/login_form_bg.png\") no-repeat center/cover;\r\n    background-size: 100% 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center; // 水平居中\r\n    justify-content: flex-start;\r\n    padding: 0 40px;\r\n\r\n    .LoginViewName {\r\n      width: 100%;\r\n      text-align: center;\r\n      font-size: 28px;\r\n      font-weight: bold;\r\n      color: #fff;\r\n      margin-top: 82px; // 距离顶部\r\n      margin-bottom: 26px; // 与表单间距\r\n      margin-left: 45px;\r\n      letter-spacing: 2px;\r\n      line-height: 1.2;\r\n    }\r\n\r\n    .LoginViewForm {\r\n      width: 360px;\r\n      margin: 0px 20px 0 58px;\r\n      padding-bottom: 20px;\r\n\r\n      input:-webkit-autofill {\r\n        transition: background-color 5000s ease-in-out 0s;\r\n      }\r\n\r\n      .input-bg {\r\n        width: 100%;\r\n        height: 44px;\r\n        background: url(\"../img/input_bg.png\") no-repeat center/cover; // 你的输入框背景图\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 16px;\r\n        box-sizing: border-box;\r\n\r\n        .zy-el-input {\r\n          background: transparent !important;\r\n\r\n          .zy-el-input__wrapper {\r\n            background: transparent !important;\r\n            box-shadow: none !important;\r\n            border: none !important;\r\n          }\r\n\r\n          .zy-el-input__inner {\r\n            background: transparent !important;\r\n            color: #fff;\r\n            border: none;\r\n            font-size: 16px;\r\n\r\n            &::placeholder {\r\n              color: #fff;\r\n              opacity: 0.7;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-form-item {\r\n        margin-bottom: 20px;\r\n      }\r\n\r\n      .LoginViewFormButton.login-btn-bg {\r\n        background: url(\"../img/login_btn.png\") no-repeat center/cover !important; // 按钮背景图\r\n        border: none !important;\r\n        color: #fff !important;\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        width: 100%;\r\n        height: 44px;\r\n        box-shadow: none;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        transition: filter 0.2s;\r\n\r\n        // 鼠标悬浮时可加亮\r\n        &:hover,\r\n        &:focus {\r\n          filter: brightness(1.08);\r\n        }\r\n\r\n        // 禁用时灰度\r\n        &.is-disabled,\r\n        &[disabled] {\r\n          filter: grayscale(0.6);\r\n          opacity: 0.7;\r\n          cursor: not-allowed;\r\n        }\r\n      }\r\n\r\n      .is-loading {\r\n        border-radius: 60px;\r\n      }\r\n\r\n      .smsValidation {\r\n        .zy-el-form-item__content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n        }\r\n\r\n        .zy-el-input {\r\n          width: 56%;\r\n        }\r\n      }\r\n\r\n      .LoginViewSlideVerify {\r\n        margin-bottom: var(--zy-distance-five);\r\n        width: 100%;\r\n\r\n        .xyl-slide-verify-slider {\r\n          width: 100% !important;\r\n          border: 1px solid #7ea4ff !important;\r\n\r\n          .xyl-slide-verify-slider-mask .xyl-slide-verify-slider-mask-item {\r\n            background: #8ecbff;\r\n          }\r\n\r\n          .container-success .xyl-slide-verify-slider-mask .xyl-slide-verify-slider-mask-item {\r\n            background-color: var(--zy-el-color-success);\r\n          }\r\n\r\n          .xyl-slide-verify-slider-text {\r\n            color: #cee4ff !important;\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-checkbox,\r\n      .zy-el-checkbox__label {\r\n        color: #fff !important;\r\n      }\r\n\r\n      .LoginViewFormOperation {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: var(--zy-distance-three);\r\n\r\n        .zy-el-checkbox {\r\n          height: var(--zy-height-secondary);\r\n        }\r\n\r\n        .LoginViewFormOperationText {\r\n          cursor: pointer;\r\n          color: #51DCFF;\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewOperation {\r\n      width: 100%;\r\n      padding-bottom: var(--zy-distance-two);\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin: 0px 45px 0 80px;\r\n\r\n      .LoginViewOperationBox {\r\n        margin: 0 var(--zy-distance-two);\r\n        cursor: pointer;\r\n\r\n        .LoginViewQrCode {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url(\"../img/login_qr_code.png\");\r\n          background-size: 100% 100%;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewApp {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url(\"../img/login_app.png\") no-repeat;\r\n          background-size: auto 100%;\r\n          background-position: center;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewOperationText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: var(--el-border-radius-small) 0;\r\n          text-align: center;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewForm+.LoginViewSystemTips {\r\n      padding-top: var(--zy-distance-one);\r\n    }\r\n\r\n    .LoginViewSystemTips {\r\n      color: #fff;\r\n      font-size: var(--zy-text-font-size);\r\n      text-align: center;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n\r\n.LoginViewQrCodeBox {\r\n  width: 320px;\r\n  background-color: #fff;\r\n\r\n  canvas {\r\n    display: block;\r\n    margin: auto;\r\n  }\r\n\r\n  .LoginViewQrCodeNameBody {\r\n    padding: var(--zy-distance-three);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LoginViewQrCodeLogo {\r\n      width: 26px;\r\n      margin-right: 6px;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .LoginViewQrCodeName {\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .LoginViewQrCodeRefreshBody {\r\n    position: relative;\r\n\r\n    .LoginViewQrCodeRefresh {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 120px;\r\n      height: 120px;\r\n      background-color: rgba(000, 000, 000, 0.6);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .zy-el-button {\r\n        --zy-el-button-size: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewQrCodeText {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding: var(--zy-distance-three);\r\n    color: var(--zy-el-color-primary);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAyGA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAK;AACpC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,EAAEC,UAAU,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,IAAI,QAAQ,yBAAyB;AAC1G,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,MAAM,MAAM,eAAe;AAClC,OAAOC,aAAa,MAAM,+BAA+B;AARzD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAgB,CAAC;;;;;IASxC,IAAMC,eAAe,GAAGb,GAAG,CAAC,GAAGS,MAAM,CAACK,OAAO,iCAAiCP,IAAI,CAACQ,KAAK,CAACC,MAAM,EAAE,CAAC;IAClG,IAAMC,IAAI,GAAGjB,GAAG,CAAC,KAAK,CAAC;IACvB,IAAAkB,UAAA,GAAkSV,SAAS,CAAC,CAAC;MAArSW,eAAe,GAAAD,UAAA,CAAfC,eAAe;MAAEC,iBAAiB,GAAAF,UAAA,CAAjBE,iBAAiB;MAAEC,aAAa,GAAAH,UAAA,CAAbG,aAAa;MAAEC,OAAO,GAAAJ,UAAA,CAAPI,OAAO;MAAEC,OAAO,GAAAL,UAAA,CAAPK,OAAO;MAAEC,OAAO,GAAAN,UAAA,CAAPM,OAAO;MAAEC,SAAS,GAAAP,UAAA,CAATO,SAAS;MAAEC,IAAI,GAAAR,UAAA,CAAJQ,IAAI;MAAEC,KAAK,GAAAT,UAAA,CAALS,KAAK;MAAEC,aAAa,GAAAV,UAAA,CAAbU,aAAa;MAAEC,WAAW,GAAAX,UAAA,CAAXW,WAAW;MAAEC,QAAQ,GAAAZ,UAAA,CAARY,QAAQ;MAAEC,WAAW,GAAAb,UAAA,CAAXa,WAAW;MAAEC,eAAe,GAAAd,UAAA,CAAfc,eAAe;MAAEC,UAAU,GAAAf,UAAA,CAAVe,UAAU;MAAEC,mBAAmB,GAAAhB,UAAA,CAAnBgB,mBAAmB;MAAEC,OAAO,GAAAjB,UAAA,CAAPiB,OAAO;MAAEC,SAAS,GAAAlB,UAAA,CAATkB,SAAS;MAAEC,UAAU,GAAAnB,UAAA,CAAVmB,UAAU;MAAEC,UAAU,GAAApB,UAAA,CAAVoB,UAAU;MAAEC,SAAS,GAAArB,UAAA,CAATqB,SAAS;MAAEC,OAAO,GAAAtB,UAAA,CAAPsB,OAAO;MAAEC,UAAU,GAAAvB,UAAA,CAAVuB,UAAU;IAG7RxC,SAAS,CAAC,YAAM;MACdsC,SAAS,CAAC,CAAC;MACXF,UAAU,CAAC,CAAC;IACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}