<template>
  <div class="LoginView" v-if="isShow">
    <el-carousel class="LoginViewCarousel" v-if="imgList.length" height="100%">
      <el-carousel-item class="LoginViewCarouselBox" v-for="item in imgList" :key="item.id">
        <el-image :src="item.imgPath" loading="lazy" fit="cover" />
      </el-carousel-item>
    </el-carousel>
    <div class="LoginViewBox">
      <div class="LoginViewLogo">
        <el-image :src="systemLogo" fit="cover" />
      </div>
      <div class="LoginViewName" v-html="loginSystemName"></div>
      <el-form ref="LoginForm" :model="form" :rules="rules" class="LoginViewForm">
        <el-form-item prop="account">
          <el-input v-model="form.account" placeholder="账号/手机号" @blur="handleBlur" clearable />
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" v-model="form.password" placeholder="密码" show-password clearable />
        </el-form-item>
        <el-form-item class="smsValidation" v-if="loginVerifyShow && whetherVerifyCode" prop="verifyCode">
          <el-input v-model="form.verifyCode" placeholder="短信验证码" clearable></el-input>
          <el-button type="primary" @click="handleGetVerifyCode" :disabled="countDownText != '获取验证码'">
            {{ countDownText }}
          </el-button>
        </el-form-item>
        <div class="LoginViewSlideVerify" v-if="loginVerifyShow && !whetherVerifyCode">
          <xyl-slide-verify ref="slideVerify" @again="onAgain" @success="onSuccess" :disabled="disabled" />
        </div>
        <div class="LoginViewFormOperation">
          <el-checkbox v-model="checked">记住用户名和密码</el-checkbox>
          <div class="LoginViewFormOperationText" @click="show = !show">忘记密码？</div>
        </div>
        <el-button type="primary" @click="submitForm(LoginForm)" class="LoginViewFormButton" :loading="loading"
          :disabled="loginDisabled">
          {{ loading ? '登录中' : '登录' }}
        </el-button>
      </el-form>
      <div class="LoginViewOperation" v-if="appDownloadUrl">
        <div class="LoginViewOperationBox">
          <el-popover placement="top" width="auto" @show="refresh" @hide="hideQrcode">
            <div class="LoginViewQrCodeBox">
              <div class="LoginViewQrCodeNameBody">
                <div class="LoginViewQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="LoginViewQrCodeName">APP扫码登录</div>
              </div>
              <div class="LoginViewQrCodeRefreshBody">
                <qrcode-vue :value="loginQrcode" :size="120" />
                <div class="LoginViewQrCodeRefresh" v-show="loginQrcodeShow">
                  <el-button type="primary" @click="refresh">刷新</el-button>
                </div>
              </div>
              <div class="LoginViewQrCodeText">请使用{{ systemName }}APP扫码登录</div>
            </div>
            <template #reference>
              <div class="LoginViewQrCode"></div>
            </template>
          </el-popover>
          <div class="LoginViewOperationText">APP扫码登录</div>
        </div>
        <div class="LoginViewOperationBox">
          <el-popover placement="top" width="auto">
            <div class="LoginViewQrCodeBox">
              <div class="LoginViewQrCodeNameBody">
                <div class="LoginViewQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="LoginViewQrCodeName">手机APP下载</div>
              </div>
              <qrcode-vue :value="appDownloadUrl" :size="120" />
              <div class="LoginViewQrCodeText">使用其他软件扫码下载{{ systemName }}APP</div>
            </div>
            <template #reference>
              <div class="LoginViewApp"></div>
            </template>
          </el-popover>
          <div class="LoginViewOperationText">手机APP下载</div>
        </div>
      </div>
      <div class="LoginViewSystemTips" v-if="systemLoginContact">{{ systemLoginContact }}</div>
    </div>
    <xyl-popup-window v-model="show" name="重置密码">
      <ResetPassword @callback="show = !show"></ResetPassword>
    </xyl-popup-window>
  </div>
  <div class="LoginViewElectron" v-if="!isShow">
    <div class="LoginViewBox">
      <div class="LoginViewMinimize" @click="handleMinimize" v-if="!isMac">
        <el-icon>
          <Minus />
        </el-icon>
      </div>
      <div class="LoginViewClose" @click="handleClose" v-if="!isMac">
        <el-icon>
          <Close />
        </el-icon>
      </div>
      <div class="LoginViewLogo">
        <el-image :src="systemLogo" fit="cover" />
      </div>
      <div class="LoginViewName" v-html="loginSystemName"></div>
      <el-form ref="LoginForm" :model="form" :rules="rules" class="LoginViewForm">
        <el-form-item prop="account">
          <el-input v-model="form.account" placeholder="账号/手机号" @blur="handleBlur" clearable />
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" v-model="form.password" placeholder="密码" show-password clearable />
        </el-form-item>
        <el-form-item class="smsValidation" v-if="loginVerifyShow && whetherVerifyCode" prop="verifyCode">
          <el-input v-model="form.verifyCode" placeholder="短信验证码" clearable></el-input>
          <el-button type="primary" @click="handleGetVerifyCode" :disabled="countDownText != '获取验证码'">
            {{ countDownText }}
          </el-button>
        </el-form-item>
        <div class="LoginViewSlideVerify" v-if="loginVerifyShow && !whetherVerifyCode">
          <xyl-slide-verify ref="slideVerify" @again="onAgain" @success="onSuccess" :disabled="disabled" />
        </div>
        <div class="LoginViewFormOperation">
          <el-checkbox v-model="checked">记住用户名和密码</el-checkbox>
        </div>
        <el-button type="primary" @click="submitForm(LoginForm, true)" class="LoginViewFormButton" :loading="loading"
          :disabled="loginDisabled">
          {{ loading ? '登录中' : '登录' }}
        </el-button>
      </el-form>
    </div>
  </div>
</template>
<script>
export default { name: 'LoginView' }
</script>
<script setup>
import { ref, onMounted, computed, defineAsyncComponent } from 'vue'
import {
  systemLogo,
  systemName,
  platformAreaName,
  loginNameLineFeedPosition,
  appDownloadUrl,
  systemLoginContact
} from 'common/js/system_var.js'
import { LoginView } from './LoginView.js'
const QrcodeVue = defineAsyncComponent(() => import('qrcode.vue'))
const ResetPassword = defineAsyncComponent(() => import('./component/ResetPassword.vue'))
const show = ref(false)
const isShow = !window.electron
const isMac = window.electron?.isMac
const loginSystemName = computed(() => {
  const name = (platformAreaName.value || '') + systemName.value
  const num = Number(loginNameLineFeedPosition.value || '0') || 0
  return num ? name.substring(0, num) + '\n' + name.substring(num) : name
})
const {
  loginVerifyShow,
  whetherVerifyCode,
  loginDisabled,
  loading,
  checked,
  imgList,
  LoginForm,
  form,
  rules,
  countDownText,
  slideVerify,
  disabled,
  loginQrcode,
  loginQrcodeShow,
  handleBlur,
  handleGetVerifyCode,
  onAgain,
  onSuccess,
  globalData,
  submitForm,
  loginInfo,
  refresh,
  hideQrcode
} = LoginView()
onMounted(() => {
  if (!isShow) {
    if (window.electron)
      window.electron.setConfig({
        resizable: false,
        maximizable: false,
        minimumSize: [480, 528],
        size: [480, 528],
        center: true
      })
    loginInfo()
  } else {
    loginInfo()
    globalData()
  }
})
const handleMinimize = () => {
  if (window.electron) window.electron.minimize()
}
const handleClose = () => {
  if (window.electron) window.electron.close()
}
</script>
<style lang="scss">
.LoginView {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .LoginViewCarousel {
    width: 100%;
    height: 100%;
    position: absolute;

    .LoginViewCarouselBox {
      width: 100%;
      height: 100%;

      .zy-el-image {
        width: 100%;
        height: 100%;
      }
    }

    .zy-el-carousel__indicators--horizontal {
      .zy-el-carousel__button {
        width: 16px;
        height: 16px;
        border-radius: 50%;
      }
    }
  }

  .LoginViewBox {
    padding: var(--zy-distance-one);
    box-shadow: var(--zy-el-box-shadow);
    padding-bottom: var(--zy-distance-two);
    border-radius: var(--el-border-radius-base);
    margin-right: 80px;
    background: #fff;
    position: relative;
    z-index: 2;

    .LoginViewLogo {
      width: 60px;
      margin: auto;
      margin-bottom: var(--zy-distance-two);

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .LoginViewName {
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: var(--zy-system-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      letter-spacing: 2px;
      padding-bottom: var(--zy-distance-one);
      white-space: pre-wrap;
      margin: auto;
    }

    .LoginViewForm {
      width: 320px;
      margin: auto;
      padding-bottom: var(--zy-distance-one);

      input:-webkit-autofill {
        transition: background-color 5000s ease-in-out 0s;
      }

      .zy-el-form-item {
        margin-bottom: var(--zy-form-distance-bottom);
      }

      .LoginViewFormButton {
        width: 100%;
      }

      .smsValidation {
        .zy-el-form-item__content {
          display: flex;
          justify-content: space-between;
        }

        .zy-el-input {
          width: 56%;
        }
      }

      .LoginViewSlideVerify {
        margin-bottom: var(--zy-distance-five);
      }

      .LoginViewFormOperation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--zy-distance-three);

        .zy-el-checkbox {
          height: var(--zy-height-secondary);
        }

        .LoginViewFormOperationText {
          cursor: pointer;
          color: var(--zy-el-color-primary);
          font-size: var(--zy-text-font-size);
        }
      }
    }

    .LoginViewOperation {
      width: 100%;
      padding-bottom: var(--zy-distance-two);
      display: flex;
      justify-content: space-between;

      .LoginViewOperationBox {
        margin: 0 var(--zy-distance-two);
        cursor: pointer;

        .LoginViewQrCode {
          width: 50px;
          height: 50px;
          background: url('../img/login_qr_code.png');
          background-size: 100% 100%;
          margin: auto;
        }

        .LoginViewApp {
          width: 50px;
          height: 50px;
          background: url('../img/login_app.png') no-repeat;
          background-size: auto 100%;
          background-position: center;
          margin: auto;
        }

        .LoginViewOperationText {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          padding: var(--el-border-radius-small) 0;
          text-align: center;
        }
      }
    }

    .LoginViewForm+.LoginViewSystemTips {
      padding-top: var(--zy-distance-one);
    }

    .LoginViewSystemTips {
      color: var(--zy-el-text-color-secondary);
      font-size: var(--zy-text-font-size);
      text-align: center;
    }
  }
}

.LoginViewQrCodeBox {
  width: 320px;
  background-color: #fff;

  canvas {
    display: block;
    margin: auto;
  }

  .LoginViewQrCodeNameBody {
    padding: var(--zy-distance-three);
    display: flex;
    align-items: center;
    justify-content: center;

    .LoginViewQrCodeLogo {
      width: 26px;
      margin-right: 6px;

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .LoginViewQrCodeName {
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
    }
  }

  .LoginViewQrCodeRefreshBody {
    position: relative;

    .LoginViewQrCodeRefresh {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 120px;
      background-color: rgba(000, 000, 000, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;

      .zy-el-button {
        --zy-el-button-size: var(--zy-height-secondary);
      }
    }
  }

  .LoginViewQrCodeText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-three);
    color: var(--zy-el-color-primary);
  }
}

.LoginViewElectron {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .LoginViewBox {
    width: 480px;
    height: 528px;
    box-shadow: 0px 2px 25px 1px rgba(0, 0, 0, 0.1);
    padding: 40px 0 0 0;
    border-radius: 10px;
    position: relative;
    -webkit-app-region: drag;
    overflow: hidden;

    .LoginViewMinimize {
      width: 38px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      position: absolute;
      top: 0;
      right: 38px;
      border-radius: 2px;
      cursor: pointer;
      -webkit-app-region: no-drag;

      &:hover {
        background-color: rgba($color: #999, $alpha: 0.6);
      }
    }

    .LoginViewClose {
      width: 38px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      position: absolute;
      top: 0;
      right: 0;
      border-radius: 2px;
      cursor: pointer;
      -webkit-app-region: no-drag;

      &:hover {
        background-color: rgba($color: red, $alpha: 0.6);
      }
    }

    .LoginViewLogo {
      width: 68px;
      margin: auto;
      margin-bottom: var(--zy-distance-two);

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .LoginViewName {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: calc(var(--zy-system-font-size) + 4px);
      line-height: var(--zy-line-height);
      font-weight: bold;
      letter-spacing: 2px;
      padding-bottom: 40px;
      white-space: pre-wrap;
      margin: auto;
    }

    .LoginViewForm {
      width: 320px;
      margin: auto;
      -webkit-app-region: no-drag;

      input:-webkit-autofill {
        transition: background-color 5000s ease-in-out 0s;
      }

      .zy-el-form-item {
        margin-bottom: var(--zy-form-distance-bottom);

        .zy-el-input {
          --zy-el-input-height: 44px;
        }
      }

      .LoginViewFormButton {
        width: 100%;
        --zy-height: 44px;
      }

      .smsValidation {
        .zy-el-form-item__content {
          display: flex;
          justify-content: space-between;
        }

        .zy-el-input {
          width: 56%;
        }

        .zy-el-button {
          --zy-height: 44px;
        }
      }

      .LoginViewSlideVerify {
        --zy-height: 44px;
        margin-bottom: var(--zy-distance-five);
      }

      .LoginViewFormOperation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--zy-distance-three);

        .zy-el-checkbox {
          height: var(--zy-height-secondary);
        }
      }
    }
  }
}
</style>
