<template>
  <div class="AssistedWriting">
    <div class="AssistedWritingBody">
      <div class="AssistedWritingUserBody">
        <div class="AssistedWritingUser">
          <el-image :src="user.image" fit="cover" />
          <div class="AssistedWritingUserInfo">
            <div class="AssistedWritingUserName">{{ user.userName }}</div>
            <div class="AssistedWritingUserText">{{ user.position }}</div>
          </div>
        </div>
      </div>
      <div class="AssistedWritingTitle">文档撰写类型</div>
      <div class="AssistedWritingList">
        <div
          class="AssistedWritingItem"
          :class="{ 'is-active': toolId === item.chatToolCode }"
          v-for="item in toolData"
          :key="item.chatToolCode"
          @click="handleTool(item)">
          <div class="AssistedWritingIcon" v-html="toolIconData[item.chatToolCode]"></div>
          <div class="AssistedWritingName">{{ item.chatToolName }}</div>
        </div>
        <div class="AssistedWritingPlaceholder" v-for="item in 2" :key="item + '_placeholder'"></div>
      </div>
      <div class="AssistedWritingEditorBody">
        <GlobalAiChatFile
          :fileList="fileList"
          :fileData="fileData"
          @close="handleClose"
          v-show="fileList.length || fileData.length" />
        <GlobalAiChatEditor
          ref="editorRef"
          v-model="sendContent"
          @send="handleSendMessage"
          @uploadCallback="handleFileUpload"
          @fileCallback="handleFileCallback" />
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'AssistedWriting' }
</script>
<script setup>
import api from '@/api'
import { ref, inject, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { user } from 'common/js/system_var.js'
import { ElMessage } from 'element-plus'
import GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue'
import GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue'
const store = useStore()
const proposalIcon =
  '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="&#230;&#148;&#191;&#229;&#141;&#143;&#230;&#143;&#144;&#230;&#161;&#136;" clip-path="url(#clip0_40_267)"><g id="&#230;&#142;&#140;&#228;&#184;&#138;&#230;&#143;&#144;&#230;&#161;&#136;"><path id="&#231;&#159;&#169;&#229;&#189;&#162; 6276" d="M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z" fill="url(#paint0_linear_40_267)"/><g id="&#231;&#187;&#132; 2742"><path id="&#231;&#159;&#169;&#229;&#189;&#162; 6277" d="M35.4666 8.2666H12.5332C11.355 8.2666 10.3999 9.22173 10.3999 10.3999V37.5999C10.3999 38.7781 11.355 39.7333 12.5332 39.7333H35.4666C36.6448 39.7333 37.5999 38.7781 37.5999 37.5999V10.3999C37.5999 9.22173 36.6448 8.2666 35.4666 8.2666Z" fill="white"/><path id="&#231;&#155;&#180;&#231;&#186;&#191; 342 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M14.1333 13.8664C14.1333 13.4246 14.4915 13.0664 14.9333 13.0664H29.8666C30.3085 13.0664 30.6666 13.4246 30.6666 13.8664C30.6666 14.3082 30.3085 14.6664 29.8666 14.6664H14.9333C14.4915 14.6664 14.1333 14.3082 14.1333 13.8664Z" fill="#0964E3"/><path id="&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M14.1333 19.8664C14.1333 19.4246 14.4915 19.0664 14.9333 19.0664H22.9333C23.3751 19.0664 23.7333 19.4246 23.7333 19.8664C23.7333 20.3082 23.3751 20.6664 22.9333 20.6664H14.9333C14.4915 20.6664 14.1333 20.3082 14.1333 19.8664Z" fill="#0964E3"/><path id="&#231;&#155;&#180;&#231;&#186;&#191; 344 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M14.1333 25.8664C14.1333 25.4246 14.4915 25.0664 14.9333 25.0664H25.6C26.0418 25.0664 26.4 25.4246 26.4 25.8664C26.4 26.3082 26.0418 26.6664 25.6 26.6664H14.9333C14.4915 26.6664 14.1333 26.3082 14.1333 25.8664Z" fill="#0964E3"/><path id="&#232;&#183;&#175;&#229;&#190;&#132; 797" d="M28.9067 36.2952L36.2805 25.764C36.3438 25.674 36.3887 25.5724 36.4127 25.4649C36.4367 25.3575 36.4392 25.2464 36.4203 25.138C36.4013 25.0296 36.3612 24.9259 36.3021 24.833C36.2431 24.7401 36.1664 24.6598 36.0763 24.5966L33.9179 23.0851C33.8277 23.022 33.726 22.9773 33.6186 22.9535C33.5112 22.9297 33.4001 22.9272 33.2918 22.9463C33.1834 22.9654 33.0799 23.0057 32.9871 23.0649C32.8943 23.124 32.814 23.2008 32.7509 23.291L25.376 33.8222C25.3014 33.9287 25.2528 34.0513 25.2341 34.18L28.6213 36.5523C28.7352 36.4901 28.8329 36.402 28.9067 36.2952Z" fill="url(#paint1_linear_40_267)"/><path id="&#232;&#183;&#175;&#229;&#190;&#132; 798" d="M28.6218 36.5514L25.5604 37.7685C25.5189 37.7849 25.4746 37.793 25.43 37.7923C25.3854 37.7916 25.3413 37.7821 25.3003 37.7644C25.2594 37.7467 25.2223 37.7211 25.1913 37.689C25.1602 37.6569 25.1358 37.619 25.1194 37.5775C25.1023 37.5334 25.0943 37.4862 25.0959 37.4389L25.2378 34.1855L28.6218 36.5514Z" fill="url(#paint2_linear_40_267)"/></g></g></g><defs><linearGradient id="paint0_linear_40_267" x1="37.296" y1="48.624" x2="11.52" y2="0.863998" gradientUnits="userSpaceOnUse"><stop stop-color="#599FFA"/><stop offset="0.995" stop-color="#0964E3"/></linearGradient><linearGradient id="paint1_linear_40_267" x1="30.8335" y1="22.9336" x2="30.8335" y2="36.5523" gradientUnits="userSpaceOnUse"><stop stop-color="#EAF2FE"/><stop offset="1" stop-color="#A3C9FE"/></linearGradient><linearGradient id="paint2_linear_40_267" x1="26.8587" y1="34.1855" x2="26.8587" y2="37.7924" gradientUnits="userSpaceOnUse"><stop stop-color="#F1F7FE"/><stop offset="1" stop-color="#A3C9FE"/></linearGradient><clipPath id="clip0_40_267"><rect width="48" height="48" fill="white"/></clipPath></defs></svg>'
const socialIcon =
  '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="&#231;&#164;&#190;&#230;&#131;&#133;&#230;&#176;&#145;&#230;&#132;&#143;" clip-path="url(#clip0_40_278)"><g id="&#231;&#164;&#190;&#230;&#131;&#133;&#230;&#176;&#145;&#230;&#132;&#143;_2"><path id="&#231;&#159;&#169;&#229;&#189;&#162; 6278" d="M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z" fill="url(#paint0_linear_40_278)"/><g id="&#231;&#187;&#132; 2744"><path id="&#232;&#183;&#175;&#229;&#190;&#132; 1794" d="M36.9018 40.0001H11.0981C10.3701 40.0001 9.67185 39.7109 9.15705 39.1961C8.64225 38.6813 8.35303 37.9831 8.35303 37.2551V27.3351L24 20.7041L39.6469 27.3324V37.2524C39.6473 37.6131 39.5765 37.9704 39.4387 38.3037C39.3009 38.6371 39.0988 38.94 38.8438 39.1952C38.5889 39.4504 38.2862 39.6528 37.9529 39.7909C37.6197 39.9291 37.2625 40.0001 36.9018 40.0001Z" fill="#BBD7FE"/><path id="&#231;&#159;&#169;&#229;&#189;&#162; 6279" d="M34.241 8H13.7588C12.5806 8 11.6255 8.95513 11.6255 10.1333V34.4976C11.6255 35.6758 12.5806 36.6309 13.7588 36.6309H34.241C35.4192 36.6309 36.3743 35.6758 36.3743 34.4976V10.1333C36.3743 8.95513 35.4192 8 34.241 8Z" fill="white"/><path id="&#231;&#155;&#180;&#231;&#186;&#191; 345 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M15.5129 19.2219C15.5129 18.78 15.8711 18.4219 16.3129 18.4219H29.8121C30.254 18.4219 30.6121 18.78 30.6121 19.2219C30.6121 19.6637 30.254 20.0219 29.8121 20.0219H16.3129C15.8711 20.0219 15.5129 19.6637 15.5129 19.2219Z" fill="#0964E3"/><path id="&#231;&#155;&#180;&#231;&#186;&#191; 346 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M15.5129 24.5651C15.5129 24.1233 15.8711 23.7651 16.3129 23.7651H25.3124C25.7542 23.7651 26.1124 24.1233 26.1124 24.5651C26.1124 25.007 25.7542 25.3651 25.3124 25.3651H16.3129C15.8711 25.3651 15.5129 25.007 15.5129 24.5651Z" fill="#0964E3"/><path id="&#231;&#155;&#180;&#231;&#186;&#191; 347 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M15.5129 13.8786C15.5129 13.4368 15.8711 13.0786 16.3129 13.0786H29.2495C29.6913 13.0786 30.0495 13.4368 30.0495 13.8786C30.0495 14.3204 29.6913 14.6786 29.2495 14.6786H16.3129C15.8711 14.6786 15.5129 14.3204 15.5129 13.8786Z" fill="#0964E3"/><path id="&#232;&#183;&#175;&#229;&#190;&#132; 1795" d="M36.9018 40.0002H11.0981C10.3701 40.0002 9.67185 39.711 9.15705 39.1962C8.64225 38.6814 8.35303 37.9832 8.35303 37.2552V27.3352L24 30.764L39.6469 27.3325V37.2525C39.6473 37.6132 39.5765 37.9705 39.4387 38.3038C39.3009 38.6372 39.0988 38.9401 38.8438 39.1953C38.5889 39.4505 38.2862 39.6529 37.9529 39.791C37.6197 39.9292 37.2625 40.0002 36.9018 40.0002Z" fill="url(#paint1_linear_40_278)"/><path id="&#231;&#155;&#180;&#231;&#186;&#191; 348 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M20.3877 35.5334C20.3877 35.0916 20.7459 34.7334 21.1877 34.7334H26.8122C27.2541 34.7334 27.6122 35.0916 27.6122 35.5334C27.6122 35.9752 27.2541 36.3334 26.8122 36.3334H21.1877C20.7459 36.3334 20.3877 35.9752 20.3877 35.5334Z" fill="white"/></g></g></g><defs><linearGradient id="paint0_linear_40_278" x1="37.296" y1="48.624" x2="11.52" y2="0.863998" gradientUnits="userSpaceOnUse"><stop stop-color="#599FFA"/><stop offset="0.995" stop-color="#0964E3"/></linearGradient><linearGradient id="paint1_linear_40_278" x1="24" y1="27.3325" x2="24" y2="40.0002" gradientUnits="userSpaceOnUse"><stop stop-color="#EAF2FE"/><stop offset="1" stop-color="#A3C9FE"/></linearGradient><clipPath id="clip0_40_278"><rect width="48" height="48" fill="white"/></clipPath></defs></svg>'

const openPage = inject('openPage')
const toolId = ref('')
const toolInfo = ref({})
const toolData = ref([])
const toolIconData = { proposal: proposalIcon, social: socialIcon }

const editorRef = ref()
const fileList = ref([])
const fileData = ref([])
const sendContent = ref('')
const handleFileUpload = (data) => {
  fileList.value = data
}
const handleFileCallback = (data) => {
  fileData.value = data
}
const handleClose = (item) => {
  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))
}
const handleTips = (text) => {
  const parts = text.split(/(\{[^}]+\})/)
  const result = parts
    .map((part) => {
      if (part.startsWith('{') && part.endsWith('}')) {
        return { value: part.slice(1, -1), type: true }
      } else if (part.trim() !== '') {
        return { value: part, type: false }
      }
    })
    .filter((item) => item !== undefined)
  return result
}
const handleTool = (item) => {
  toolInfo.value = item
  toolId.value = item.chatToolCode
  editorRef.value?.handleSetFile([])
  editorRef.value?.handleSetContent('')
  nextTick(() => {
    editorRef.value?.handleInsertPlaceholder(handleTips(item.userPromptTip))
  })
}
const handleSendMessage = (value) => {
  if (!toolId.value) return ElMessage({ type: 'warning', message: '请先选择文档撰写类型！' })
  const openAiParams = {
    toolId: toolInfo.value.id,
    toolCode: toolId.value,
    toolContent: value,
    fileData: fileData.value
  }
  sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams))
  if (toolId.value === 'proposal') openPage({ key: 'routePath', value: '/proposal/SubmitSuggest?utype=1' })
  if (toolId.value === 'social') openPage({ key: 'routePath', value: '/publicOpinion/PublicOpinionNew' })
  editorRef.value?.handleSetFile([])
}
const aigptChatSceneDetail = async () => {
  const { data } = await api.aigptChatSceneDetail({ query: { chatSceneCode: 'ai-assisted-writing-chat' } })
  toolData.value = data?.tools?.filter((v) => v.isUsing) || []
}
onActivated(() => {
  aigptChatSceneDetail()
  store.commit('setAiChatElShow', false)
})
onDeactivated(() => {
  store.commit('setAiChatElShow', true)
})
onUnmounted(() => {
  store.commit('setAiChatElShow', true)
})
</script>
<style lang="scss">
.AssistedWriting {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  position: relative;

  .AssistedWritingBody {
    padding: var(--zy-distance-two);
  }

  .AssistedWritingUserBody {
    width: 800px;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid var(--zy-el-border-color-lighter);

    .AssistedWritingUser {
      width: 100%;
      display: flex;
      align-items: center;
      padding: var(--zy-distance-two);

      .zy-el-image {
        width: 62px;
        height: 62px;
        border-radius: 50%;
      }

      .AssistedWritingUserInfo {
        width: calc(100% - 62px);
        height: 58px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-left: var(--zy-distance-two);

        .AssistedWritingUserName {
          font-weight: bold;
          line-height: var(--zy-line-height);
          font-size: calc(var(--zy-name-font-size) + 2px);
        }

        .AssistedWritingUserText {
          line-height: var(--zy-line-height);
          font-size: var(--zy-name-font-size);
          color: var(--zy-el-text-color-regular);
        }
      }
    }
  }

  .AssistedWritingTitle {
    width: 100%;
    font-weight: bold;
    line-height: var(--zy-line-height);
    font-size: var(--zy-name-font-size);
    padding: 12px 0;
  }

  .AssistedWritingList {
    width: 800px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;

    .AssistedWritingPlaceholder {
      width: 185px;
      height: 126px;
      margin-bottom: 20px;
    }

    .AssistedWritingItem {
      width: 185px;
      height: 126px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: space-between;
      border-radius: 6px 6px 6px 6px;
      border: 1px solid var(--zy-el-border-color-lighter);
      padding: 20px;
      margin-bottom: 20px;
      cursor: pointer;

      &.is-active {
        box-shadow: var(--zy-el-box-shadow);
        border-color: var(--zy-el-color-primary);
        background: var(--zy-el-color-primary-light-9);

        .AssistedWritingName {
          color: var(--zy-el-color-primary);
        }
      }

      &:hover {
        box-shadow: var(--zy-el-box-shadow);
        border-color: var(--zy-el-color-primary);
        background: var(--zy-el-color-primary-light-9);

        .AssistedWritingName {
          color: var(--zy-el-color-primary);
        }
      }

      .AssistedWritingIcon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .AssistedWritingName {
        line-height: var(--zy-line-height);
        font-size: var(--zy-name-font-size);
      }
    }
  }

  .AssistedWritingEditorBody {
    width: 800px;
    position: absolute;
    left: 50%;
    bottom: 20px;
    transform: translateX(-50%);
    background: #fff;
    border-radius: 8px;
    box-shadow: var(--zy-el-box-shadow);
    border: 1px solid var(--zy-el-border-color-lighter);

    .GlobalAiChatEditorContainer {
      border-radius: 8px;
    }
  }
}
</style>
