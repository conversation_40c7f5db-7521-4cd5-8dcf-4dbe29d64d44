{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode, Fragment as _Fragment, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, renderList as _renderList } from \"vue\";\nvar _hoisted_1 = {\n  class: \"global-dynamic-title\"\n};\nvar _hoisted_2 = {\n  class: \"global-dynamic-name\"\n};\nvar _hoisted_3 = {\n  key: 0,\n  class: \"global-dynamic-input-list\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  var _component_Finished = _resolveComponent(\"Finished\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createTextVNode(_toDisplayString($setup.title) + \" \", 1 /* TEXT */), $setup.hasPermission('dynamic_title') && !$setup.props.disabled ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [!$setup.show ? (_openBlock(), _createBlock(_component_el_tooltip, {\n    key: 0,\n    effect: \"dark\",\n    placement: \"top\",\n    content: \"点击修改表头\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"span\", {\n        class: \"global-dynamic-icon\",\n        onClick: $setup.handleEdit\n      })];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), $setup.show ? (_openBlock(), _createBlock(_component_el_tooltip, {\n    key: 1,\n    effect: \"dark\",\n    placement: \"top\",\n    content: \"点击确定修改\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"span\", {\n        class: \"global-dynamic-determine\",\n        onClick: $setup.dynamicTextEdit\n      }, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_Finished)];\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]), $setup.show ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.formArr, function (item) {\n    return _openBlock(), _createBlock($setup[\"GlobalDynamicInput\"], {\n      key: item.id,\n      modelValue: item.text,\n      \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n        return item.text = $event;\n      },\n      disabled: item.disabled\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\", \"disabled\"]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createTextVNode", "_toDisplayString", "$setup", "title", "hasPermission", "props", "disabled", "_Fragment", "show", "_createBlock", "_component_el_tooltip", "effect", "placement", "content", "default", "_withCtx", "onClick", "handleEdit", "_", "_createCommentVNode", "dynamicTextEdit", "_createVNode", "_component_el_icon", "_component_Finished", "_hoisted_3", "_renderList", "formArr", "item", "id", "modelValue", "text", "onUpdateModelValue", "$event"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\global-dynamic-title\\global-dynamic-title.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-dynamic-title\">\r\n    <div class=\"global-dynamic-name\">{{ title }}\r\n      <template v-if=\"hasPermission('dynamic_title') && !props.disabled\">\r\n        <el-tooltip effect=\"dark\"\r\n                    placement=\"top\"\r\n                    v-if=\"!show\"\r\n                    content=\"点击修改表头\">\r\n          <span class=\"global-dynamic-icon\"\r\n                @click=\"handleEdit\"></span>\r\n        </el-tooltip>\r\n        <el-tooltip effect=\"dark\"\r\n                    placement=\"top\"\r\n                    v-if=\"show\"\r\n                    content=\"点击确定修改\">\r\n          <span class=\"global-dynamic-determine\"\r\n                @click=\"dynamicTextEdit\">\r\n            <el-icon>\r\n              <Finished />\r\n            </el-icon>\r\n          </span>\r\n        </el-tooltip>\r\n      </template>\r\n    </div>\r\n    <div class=\"global-dynamic-input-list\"\r\n         v-if=\"show\">\r\n      <GlobalDynamicInput v-for=\"item in formArr\"\r\n                          :key=\"item.id\"\r\n                          v-model=\"item.text\"\r\n                          :disabled=\"item.disabled\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DynamicTitle' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, watch } from 'vue'\r\nimport { hasPermission } from 'common/js/permissions'\r\nimport GlobalDynamicInput from './global-dynamic-input.vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  templateCode: { type: String, default: '' },\r\n  params: { type: Object, default: () => ({}) },\r\n  disabled: { type: Boolean, default: false },\r\n  titles: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst id = ref('')\r\nconst title = ref('')\r\nconst show = ref(false)\r\nconst formArr = ref([])\r\n\r\nconst dynamicTextByCode = async () => {\r\n  const { data } = await api.dynamicTextByCode({ templateCode: props.templateCode, ...props.params })\r\n  if (props.titles) {\r\n    title.value = props.titles\r\n  } else {\r\n    title.value = data\r\n  }\r\n  emit('callback', data)\r\n}\r\nconst handleEdit = () => {\r\n  show.value = true\r\n  dynamicTextInfo()\r\n}\r\nconst splitFunc = (array, label) => {\r\n  const outArray = []\r\n  for (let i = 0; i < array.length; i++) {\r\n    let newArr = []\r\n    const splitArr = array[i].split(label)\r\n    for (let j = 0; j < splitArr.length; j++) {\r\n      newArr.push(splitArr[j])\r\n      if (j < splitArr.length - 1) {\r\n        newArr.push(label)\r\n      }\r\n    }\r\n    outArray.push(newArr)\r\n  }\r\n  return outArray\r\n}\r\nconst dynamicTextInfo = async () => {\r\n  const { data } = await api.dynamicTextInfo({ templateCode: props.templateCode, ...props.params })\r\n  formArr.value = []\r\n  id.value = data.id\r\n  let array = [data.templateContent]\r\n  const labels = data.templateContent.match(/\\{[^\\}]+\\}/g) // eslint-disable-line\r\n  for (let i = 0; i < labels.length; i++) {\r\n    const twoArray = splitFunc(array, labels[i])\r\n    const oneArray = twoArray.reduce(function (a, b) { return a.concat(b) })\r\n    array = oneArray.filter(item => item !== '')\r\n  }\r\n  var is = true\r\n  for (let i = 0; i < array.length; i++) {\r\n    if (array[i].match(/\\{[^\\}]+\\}/g)) { // eslint-disable-line\r\n      if (is) {\r\n        formArr.value.push({ id: formArr.value.length + '', text: '', disabled: false })\r\n      }\r\n      is = true\r\n      formArr.value.push({ id: formArr.value.length + '', text: array[i], disabled: true })\r\n      if (i === array.length - 1) {\r\n        formArr.value.push({ id: formArr.value.length + '', text: '', disabled: false })\r\n      }\r\n    } else {\r\n      is = false\r\n      formArr.value.push({ id: formArr.value.length + '', text: array[i], disabled: false })\r\n    }\r\n  }\r\n}\r\nconst dynamicTextEdit = async () => {\r\n  var templateContent = ''\r\n  for (let index = 0; index < formArr.value.length; index++) {\r\n    const item = formArr.value[index]\r\n    templateContent += item.text\r\n  }\r\n  const { code } = await api.dynamicTextEdit({\r\n    form: {\r\n      id: id.value,\r\n      templateCode: props.templateCode,\r\n      templateContent: templateContent,\r\n      ...props.params\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '编辑成功' })\r\n    dynamicTextByCode()\r\n    show.value = false\r\n  }\r\n}\r\nwatch(() => props.params, () => {\r\n  dynamicTextByCode()\r\n  dynamicTextInfo()\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.global-dynamic-title {\r\n  width: 100%;\r\n  border-bottom: 2px solid var(--zy-el-color-primary);\r\n\r\n  .global-dynamic-name {\r\n    position: relative;\r\n    padding: 20px 40px;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    color: var(--zy-el-color-primary);\r\n    font-size: var(--zy-title-font-size);\r\n    line-height: var(--zy-line-height);\r\n\r\n    .global-dynamic-icon {\r\n      position: absolute;\r\n      top: 50%;\r\n      right: 0;\r\n      transform: translateY(-50%);\r\n      width: 24px;\r\n      height: 24px;\r\n      background: url(\"./img/global_dynamic_title_edit.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .global-dynamic-determine {\r\n      position: absolute;\r\n      top: 50%;\r\n      right: 0;\r\n      transform: translateY(-50%);\r\n      font-size: 26px;\r\n      display: flex;\r\n      align-items: center;\r\n      color: var(--zy-el-text-color-regular);\r\n    }\r\n  }\r\n\r\n  .global-dynamic-input-list {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding-bottom: 20px;\r\n\r\n    .zy-el-input__wrapper {\r\n      border-radius: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAqB;;EAFpCC,GAAA;EAwBSD,KAAK,EAAC;;;;;;uBAvBbE,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJC,mBAAA,CAqBM,OArBNC,UAqBM,GAvBVC,gBAAA,CAAAC,gBAAA,CAEwCC,MAAA,CAAAC,KAAK,IAAG,GAC1C,iBAAgBD,MAAA,CAAAE,aAAa,sBAAsBF,MAAA,CAAAG,KAAK,CAACC,QAAQ,I,cAAjEV,mBAAA,CAmBWW,SAAA;IAtBjBZ,GAAA;EAAA,I,CAM2BO,MAAA,CAAAM,IAAI,I,cAFvBC,YAAA,CAMaC,qBAAA;IAVrBf,GAAA;IAIoBgB,MAAM,EAAC,MAAM;IACbC,SAAS,EAAC,KAAK;IAEfC,OAAO,EAAC;;IAP5BC,OAAA,EAAAC,QAAA,CAQU;MAAA,OACiC,CADjCjB,mBAAA,CACiC;QAD3BJ,KAAK,EAAC,qBAAqB;QAC1BsB,OAAK,EAAEd,MAAA,CAAAe;;;IATxBC,CAAA;QAAAC,mBAAA,gBAa0BjB,MAAA,CAAAM,IAAI,I,cAFtBC,YAAA,CAUaC,qBAAA;IArBrBf,GAAA;IAWoBgB,MAAM,EAAC,MAAM;IACbC,SAAS,EAAC,KAAK;IAEfC,OAAO,EAAC;;IAd5BC,OAAA,EAAAC,QAAA,CAeU;MAAA,OAKO,CALPjB,mBAAA,CAKO;QALDJ,KAAK,EAAC,0BAA0B;QAC/BsB,OAAK,EAAEd,MAAA,CAAAkB;UACZC,YAAA,CAEUC,kBAAA;QAnBtBR,OAAA,EAAAC,QAAA,CAkBc;UAAA,OAAY,CAAZM,YAAA,CAAYE,mBAAA,E;;QAlB1BL,CAAA;;;IAAAA,CAAA;QAAAC,mBAAA,e,+BAAAA,mBAAA,e,GAyBejB,MAAA,CAAAM,IAAI,I,cADfZ,mBAAA,CAMM,OANN4B,UAMM,I,kBAJJ5B,mBAAA,CAGgDW,SAAA,QA7BtDkB,WAAA,CA0ByCvB,MAAA,CAAAwB,OAAO,EA1BhD,UA0BiCC,IAAI;yBAA/BlB,YAAA,CAGgDP,MAAA;MAF3BP,GAAG,EAAEgC,IAAI,CAACC,EAAE;MA3BvCC,UAAA,EA4BmCF,IAAI,CAACG,IAAI;MA5B5C,gCAAAC,mBAAAC,MAAA;QAAA,OA4BmCL,IAAI,CAACG,IAAI,GAAAE,MAAA;MAAA;MACjB1B,QAAQ,EAAEqB,IAAI,CAACrB;;sCA7B1Ca,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}