{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\n/**\r\n * 提案配置\r\n */\n// 集体提案单位\nvar CollectiveProposalUnit = function CollectiveProposalUnit() {\n  return import('@/views/SuggestConfig/CollectiveProposalUnit/CollectiveProposalUnit');\n};\n// 集体提案单位用户\nvar CollectiveProposalUnitUser = function CollectiveProposalUnitUser() {\n  return import('@/views/SuggestConfig/CollectiveProposalUnitUser/CollectiveProposalUnitUser');\n};\n// 集体提案单位用户提交\nvar SubmitCollectiveProposalUnitUser = function SubmitCollectiveProposalUnitUser() {\n  return import('@/views/SuggestConfig/CollectiveProposalUnitUser/SubmitCollectiveProposalUnitUser');\n};\n// 提案单位\nvar SuggestUnit = function SuggestUnit() {\n  return import('@/views/SuggestConfig/SuggestUnit/SuggestUnit');\n};\n// 提案单位用户\nvar SuggestUnitUser = function SuggestUnitUser() {\n  return import('@/views/SuggestConfig/SuggestUnitUser/SuggestUnitUser');\n};\n// 提案单位用户提交\nvar SuggestUnitUserSubmit = function SuggestUnitUserSubmit() {\n  return import('@/views/SuggestConfig/SuggestUnitUser/SuggestUnitUserSubmit');\n};\n// 提案届次编号管理\nvar SuggestNumbering = function SuggestNumbering() {\n  return import('@/views/SuggestConfig/SuggestNumbering/SuggestNumbering');\n};\n// 提案分类\nvar SuggestType = function SuggestType() {\n  return import('@/views/SuggestConfig/SuggestType/SuggestType');\n};\n// 相似度详情\n// const SimilarityDetails = () => import('@/views/SimilarityDetails/SimilarityDetails')\n/**\r\n * 提案者\r\n */\n// 提交提案\nvar SubmitSuggest = function SubmitSuggest() {\n  return import('@/views/BehalfSuggest/SubmitSuggest/SubmitSuggest');\n};\n// 我领衔的提案\nvar MyLedSuggest = function MyLedSuggest() {\n  return import('@/views/BehalfSuggest/MyLedSuggest/MyLedSuggest');\n};\n// 我附议的提案\nvar MyJointSuggest = function MyJointSuggest() {\n  return import('@/views/BehalfSuggest/MyJointSuggest/MyJointSuggest');\n};\n// 草稿箱\nvar SuggestDraftBox = function SuggestDraftBox() {\n  return import('@/views/BehalfSuggest/SuggestDraftBox/SuggestDraftBox');\n};\n// 所有提案\nvar PersonalAllSuggest = function PersonalAllSuggest() {\n  return import('@/views/PersonalAllSuggest/PersonalAllSuggest');\n};\n// 相似度对比页面\nvar TextQueryTool = function TextQueryTool() {\n  return import('@/views/BehalfSuggest/ProposalManagementTool/TextQueryTool');\n};\n\n// 提案线索\nvar ProposalClueList = function ProposalClueList() {\n  return import('@/views/ProposalClue/ProposalClueList');\n};\nvar SubmitProposalClue = function SubmitProposalClue() {\n  return import('@/views/ProposalClue/SubmitProposalClue');\n};\nvar ProposalClueDetail = function ProposalClueDetail() {\n  return import('@/views/ProposalClue/ProposalClueDetail');\n};\n/**\r\n * 提案管理\r\n */\n// 所有提案\nvar AllSuggest = function AllSuggest() {\n  return import('@/views/AllSuggest/AllSuggest');\n};\nvar SuggestControls = function SuggestControls() {\n  return import('@/views/SuggestControls/SuggestControls');\n};\n// 提案统计\nvar SuggestStatistics = function SuggestStatistics() {\n  return import('@/views/SuggestStatistics/SuggestStatistics');\n};\n// 提案统计列表\nvar SuggestStatisticsList = function SuggestStatisticsList() {\n  return import('@/views/SuggestStatistics/SuggestStatisticsList');\n};\n// 提案对比\nvar SuggestCompare = function SuggestCompare() {\n  return import('@/views/SuggestCompare/SuggestCompare');\n};\n// 提案详情\nvar SuggestDetail = function SuggestDetail() {\n  return import('@/views/SuggestDetail/SuggestDetail');\n};\n// 提案分类\nvar SuggestedClassification = function SuggestedClassification() {\n  return import('@/views/SuggestedClassification/SuggestedClassification');\n};\n// 提案细分\nvar SuggestedSubdivide = function SuggestedSubdivide() {\n  return import('@/views/SuggestedClassification/SuggestedSubdivide');\n};\n/**\r\n * 提案审查\r\n */\n// 通用提案审查\nvar SuggestReview = function SuggestReview() {\n  return import('@/views/SuggestReview/SuggestReview');\n};\n/**\r\n * 不接收提案\r\n */\nvar DoNotReceiveSuggest = function DoNotReceiveSuggest() {\n  return import('@/views/DoNotReceiveSuggest/DoNotReceiveSuggest');\n};\n/**\r\n * 提案交办\r\n */\n// 通用提案交办\nvar SuggestAssign = function SuggestAssign() {\n  return import('@/views/SuggestAssign/SuggestAssign');\n};\n// 建议预交办\nvar SuggestAdvanceAssign = function SuggestAdvanceAssign() {\n  return import('@/views/SuggestAdvanceAssign/SuggestAdvanceAssign');\n};\n/**\r\n * 提案并案\r\n */\n// 并案提案\nvar MergerProposalContainer = function MergerProposalContainer() {\n  return import('@/views/MergerProposal/MergerProposalContainer');\n};\n// 已并案提案\nvar HaveMergerProposal = function HaveMergerProposal() {\n  return import('@/views/MergerProposal/HaveMergerProposal');\n};\n\n// 办理中提案\nvar SuggestTransact = function SuggestTransact() {\n  return import('@/views/SuggestTransact/SuggestTransact');\n};\n// 申请延期提案\nvar SuggestApplyForPostpone = function SuggestApplyForPostpone() {\n  return import('@/views/SuggestApplyForPostpone/SuggestApplyForPostpone');\n};\n// 申请调整提案\nvar SuggestApplyForAdjust = function SuggestApplyForAdjust() {\n  return import('@/views/SuggestApplyForAdjust/SuggestApplyForAdjust');\n};\n// 预交办申请调整建议\nvar SuggestPreApplyForAdjust = function SuggestPreApplyForAdjust() {\n  return import('@/views/SuggestPreApplyForAdjust/SuggestPreApplyForAdjust');\n};\n// 已答复提案\nvar SuggestReply = function SuggestReply() {\n  return import('@/views/SuggestReply/SuggestReply');\n};\n// 跟踪办理提案\nvar SuggestTrackTransact = function SuggestTrackTransact() {\n  return import('@/views/SuggestTrackTransact/SuggestTrackTransact');\n};\n// 已办结提案\nvar SuggestConclude = function SuggestConclude() {\n  return import('@/views/SuggestConclude/SuggestConclude');\n};\n/**\r\n * 承办单位\r\n */\n// 所有提案\nvar UnitAllSuggest = function UnitAllSuggest() {\n  return import('@/views/UnitAllSuggest/UnitAllSuggest');\n};\n// 预交办建议\nvar UnitSuggestAdvanceAssign = function UnitSuggestAdvanceAssign() {\n  return import('@/views/UnitSuggestAdvanceAssign/UnitSuggestAdvanceAssign');\n};\n// 办理中提案\nvar UnitSuggestTransact = function UnitSuggestTransact() {\n  return import('@/views/UnitSuggestTransact/UnitSuggestTransact');\n};\n// 已答复提案\nvar UnitSuggestReply = function UnitSuggestReply() {\n  return import('@/views/UnitSuggestReply/UnitSuggestReply');\n};\n// 跟踪办理提案\nvar UnitSuggestTrackTransact = function UnitSuggestTrackTransact() {\n  return import('@/views/UnitSuggestTrackTransact/UnitSuggestTrackTransact');\n};\n// 已办结提案\nvar UnitSuggestConclude = function UnitSuggestConclude() {\n  return import('@/views/UnitSuggestConclude/UnitSuggestConclude');\n};\n// 已办结提案\nvar UnitSummaryReport = function UnitSummaryReport() {\n  return import('@/views/UnitSummaryReport/UnitSummaryReport');\n};\n\n/**\r\n * 提案线索征集\r\n */\n// 征集文案管理\nvar SuggestDocument = function SuggestDocument() {\n  return import('@/views/SuggestClue/SuggestDocument/SuggestDocument');\n};\n// 提案线索征集管理\nvar SuggestClueControl = function SuggestClueControl() {\n  return import('@/views/SuggestClue/SuggestClueControl/SuggestClueControl');\n};\n// 提案线索征集添加\nvar SuggestClueAdd = function SuggestClueAdd() {\n  return import('@/views/SuggestClue/SuggestClueAdd/SuggestClueAdd');\n};\n// 提案线索征集选登\nvar SuggestClueRegister = function SuggestClueRegister() {\n  return import('@/views/SuggestClue/SuggestClueRegister/SuggestClueRegister');\n};\n// 我的线索\nvar SuggestClueMine = function SuggestClueMine() {\n  return import('@/views/SuggestClue/SuggestClueMine/SuggestClueMine');\n};\n// 超级修改\nvar SuperEdit = function SuperEdit() {\n  return import('@/views/SuperEdit/SuperEdit.vue');\n};\n// 批量导出\nvar batchExport = function batchExport() {\n  return import('@/views/batchExport/batchExport.vue');\n};\n// 历史提案\nvar HistoricalProposal = function HistoricalProposal() {\n  return import('@/views/HistoricalProposal/HistoricalProposal.vue');\n};\nvar routes = [{\n  path: '/CollectiveProposalUnit',\n  name: 'CollectiveProposalUnit',\n  component: CollectiveProposalUnit,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/CollectiveProposalUnitUser',\n  name: 'CollectiveProposalUnitUser',\n  component: CollectiveProposalUnitUser,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SubmitCollectiveProposalUnitUser',\n  name: 'SubmitCollectiveProposalUnitUser',\n  component: SubmitCollectiveProposalUnitUser,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestUnit',\n  name: 'SuggestUnit',\n  component: SuggestUnit,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestUnitUser',\n  name: 'SuggestUnitUser',\n  component: SuggestUnitUser,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestUnitUserSubmit',\n  name: 'SuggestUnitUserSubmit',\n  component: SuggestUnitUserSubmit,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestNumbering',\n  name: 'SuggestNumbering',\n  component: SuggestNumbering,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestType',\n  name: 'SuggestType',\n  component: SuggestType,\n  meta: {\n    moduleName: 'main'\n  }\n},\n// { path: '/SimilarityDetails', name: 'SimilarityDetails', component: SimilarityDetails, meta: { moduleName: 'main' } },\n{\n  path: '/SubmitSuggest',\n  name: 'SubmitSuggest',\n  component: SubmitSuggest,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/MyLedSuggest',\n  name: 'MyLedSuggest',\n  component: MyLedSuggest,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/MyJointSuggest',\n  name: 'MyJointSuggest',\n  component: MyJointSuggest,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestDraftBox',\n  name: 'SuggestDraftBox',\n  component: SuggestDraftBox,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/PersonalAllSuggest',\n  name: 'PersonalAllSuggest',\n  component: PersonalAllSuggest,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/TextQueryTool',\n  name: 'TextQueryTool',\n  component: TextQueryTool,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/ProposalClueList',\n  name: 'ProposalClueList',\n  component: ProposalClueList,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SubmitProposalClue',\n  name: 'SubmitProposalClue',\n  component: SubmitProposalClue,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/ProposalClueDetail',\n  name: 'ProposalClueDetail',\n  component: ProposalClueDetail,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AllSuggest',\n  name: 'AllSuggest',\n  component: AllSuggest,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestControls',\n  name: 'SuggestControls',\n  component: SuggestControls,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestStatistics',\n  name: 'SuggestStatistics',\n  component: SuggestStatistics,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestStatisticsList',\n  name: 'SuggestStatisticsList',\n  component: SuggestStatisticsList,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestCompare',\n  name: 'SuggestCompare',\n  component: SuggestCompare,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestDetail',\n  name: 'SuggestDetail',\n  component: SuggestDetail,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestReview',\n  name: 'SuggestReview',\n  component: SuggestReview,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/DoNotReceiveSuggest',\n  name: 'DoNotReceiveSuggest',\n  component: DoNotReceiveSuggest,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestAssign',\n  name: 'SuggestAssign',\n  component: SuggestAssign,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestAdvanceAssign',\n  name: 'SuggestAdvanceAssign',\n  component: SuggestAdvanceAssign,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/MergerProposalContainer',\n  name: 'MergerProposalContainer',\n  component: MergerProposalContainer,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/HaveMergerProposal',\n  name: 'HaveMergerProposal',\n  component: HaveMergerProposal,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestTransact',\n  name: 'SuggestTransact',\n  component: SuggestTransact,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestApplyForPostpone',\n  name: 'SuggestApplyForPostpone',\n  component: SuggestApplyForPostpone,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestPreApplyForAdjust',\n  name: 'SuggestPreApplyForAdjust',\n  component: SuggestPreApplyForAdjust,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestApplyForAdjust',\n  name: 'SuggestApplyForAdjust',\n  component: SuggestApplyForAdjust,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestReply',\n  name: 'SuggestReply',\n  component: SuggestReply,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestTrackTransact',\n  name: 'SuggestTrackTransact',\n  component: SuggestTrackTransact,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestConclude',\n  name: 'SuggestConclude',\n  component: SuggestConclude,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/UnitAllSuggest',\n  name: 'UnitAllSuggest',\n  component: UnitAllSuggest,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/UnitSuggestAdvanceAssign',\n  name: 'UnitSuggestAdvanceAssign',\n  component: UnitSuggestAdvanceAssign,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/UnitSuggestTransact',\n  name: 'UnitSuggestTransact',\n  component: UnitSuggestTransact,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/UnitSuggestReply',\n  name: 'UnitSuggestReply',\n  component: UnitSuggestReply,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/UnitSuggestTrackTransact',\n  name: 'UnitSuggestTrackTransact',\n  component: UnitSuggestTrackTransact,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/UnitSuggestConclude',\n  name: 'UnitSuggestConclude',\n  component: UnitSuggestConclude,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/UnitSummaryReport',\n  name: 'UnitSummaryReport',\n  component: UnitSummaryReport,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestDocument',\n  name: 'SuggestDocument',\n  component: SuggestDocument,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestClueControl',\n  name: 'SuggestClueControl',\n  component: SuggestClueControl,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestClueAdd',\n  name: 'SuggestClueAdd',\n  component: SuggestClueAdd,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestClueRegister',\n  name: 'SuggestClueRegister',\n  component: SuggestClueRegister,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestClueMine',\n  name: 'SuggestClueMine',\n  component: SuggestClueMine,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestedClassification',\n  name: 'SuggestedClassification',\n  component: SuggestedClassification,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuggestedSubdivide',\n  name: 'SuggestedSubdivide',\n  component: SuggestedSubdivide,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/SuperEdit',\n  name: 'SuperEdit',\n  component: SuperEdit,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/batchExport',\n  name: 'batchExport',\n  component: batchExport,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/HistoricalProposal',\n  name: 'HistoricalProposal',\n  component: HistoricalProposal,\n  meta: {\n    moduleName: 'main'\n  }\n}];\nvar router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\nexport { routes };\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "CollectiveProposalUnit", "CollectiveProposalUnitUser", "SubmitCollectiveProposalUnitUser", "SuggestUnit", "SuggestUnitUser", "SuggestUnitUserSubmit", "SuggestNumbering", "SuggestType", "SubmitSuggest", "MyLedSuggest", "MyJointSuggest", "SuggestDraftBox", "PersonalAllSuggest", "TextQueryTool", "ProposalClueList", "SubmitProposalClue", "ProposalClueDetail", "AllSuggest", "SuggestControls", "SuggestStatistics", "SuggestStatisticsList", "SuggestCompare", "SuggestDetail", "SuggestedClassification", "SuggestedSubdivide", "SuggestReview", "DoNotReceiveSuggest", "SuggestAssign", "SuggestAdvanceAssign", "MergerProposalContainer", "HaveMergerProposal", "SuggestTransact", "SuggestApplyForPostpone", "SuggestApplyForAdjust", "SuggestPreApplyForAdjust", "SuggestReply", "SuggestTrackTransact", "SuggestConclude", "UnitAllSuggest", "UnitSuggestAdvanceAssign", "UnitSuggestTransact", "UnitSuggestReply", "UnitSuggestTrackTransact", "UnitSuggestConclude", "UnitSummaryReport", "SuggestDocument", "SuggestClueControl", "SuggestClueAdd", "SuggestClueRegister", "SuggestClueMine", "SuperEdit", "batchExport", "HistoricalProposal", "routes", "path", "name", "component", "meta", "moduleName", "router", "history", "process", "env", "BASE_URL"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\n/**\r\n * 提案配置\r\n */\r\n// 集体提案单位\r\nconst CollectiveProposalUnit = () => import('@/views/SuggestConfig/CollectiveProposalUnit/CollectiveProposalUnit')\r\n// 集体提案单位用户\r\nconst CollectiveProposalUnitUser = () =>\r\n  import('@/views/SuggestConfig/CollectiveProposalUnitUser/CollectiveProposalUnitUser')\r\n// 集体提案单位用户提交\r\nconst SubmitCollectiveProposalUnitUser = () =>\r\n  import('@/views/SuggestConfig/CollectiveProposalUnitUser/SubmitCollectiveProposalUnitUser')\r\n// 提案单位\r\nconst SuggestUnit = () => import('@/views/SuggestConfig/SuggestUnit/SuggestUnit')\r\n// 提案单位用户\r\nconst SuggestUnitUser = () => import('@/views/SuggestConfig/SuggestUnitUser/SuggestUnitUser')\r\n// 提案单位用户提交\r\nconst SuggestUnitUserSubmit = () => import('@/views/SuggestConfig/SuggestUnitUser/SuggestUnitUserSubmit')\r\n// 提案届次编号管理\r\nconst SuggestNumbering = () => import('@/views/SuggestConfig/SuggestNumbering/SuggestNumbering')\r\n// 提案分类\r\nconst SuggestType = () => import('@/views/SuggestConfig/SuggestType/SuggestType')\r\n// 相似度详情\r\n// const SimilarityDetails = () => import('@/views/SimilarityDetails/SimilarityDetails')\r\n/**\r\n * 提案者\r\n */\r\n// 提交提案\r\nconst SubmitSuggest = () => import('@/views/BehalfSuggest/SubmitSuggest/SubmitSuggest')\r\n// 我领衔的提案\r\nconst MyLedSuggest = () => import('@/views/BehalfSuggest/MyLedSuggest/MyLedSuggest')\r\n// 我附议的提案\r\nconst MyJointSuggest = () => import('@/views/BehalfSuggest/MyJointSuggest/MyJointSuggest')\r\n// 草稿箱\r\nconst SuggestDraftBox = () => import('@/views/BehalfSuggest/SuggestDraftBox/SuggestDraftBox')\r\n// 所有提案\r\nconst PersonalAllSuggest = () => import('@/views/PersonalAllSuggest/PersonalAllSuggest')\r\n// 相似度对比页面\r\nconst TextQueryTool = () => import('@/views/BehalfSuggest/ProposalManagementTool/TextQueryTool')\r\n\r\n// 提案线索\r\nconst ProposalClueList = () => import('@/views/ProposalClue/ProposalClueList')\r\nconst SubmitProposalClue = () => import('@/views/ProposalClue/SubmitProposalClue')\r\nconst ProposalClueDetail = () => import('@/views/ProposalClue/ProposalClueDetail')\r\n/**\r\n * 提案管理\r\n */\r\n// 所有提案\r\nconst AllSuggest = () => import('@/views/AllSuggest/AllSuggest')\r\nconst SuggestControls = () => import('@/views/SuggestControls/SuggestControls')\r\n// 提案统计\r\nconst SuggestStatistics = () => import('@/views/SuggestStatistics/SuggestStatistics')\r\n// 提案统计列表\r\nconst SuggestStatisticsList = () => import('@/views/SuggestStatistics/SuggestStatisticsList')\r\n// 提案对比\r\nconst SuggestCompare = () => import('@/views/SuggestCompare/SuggestCompare')\r\n// 提案详情\r\nconst SuggestDetail = () => import('@/views/SuggestDetail/SuggestDetail')\r\n// 提案分类\r\nconst SuggestedClassification = () => import('@/views/SuggestedClassification/SuggestedClassification')\r\n// 提案细分\r\nconst SuggestedSubdivide = () => import('@/views/SuggestedClassification/SuggestedSubdivide')\r\n/**\r\n * 提案审查\r\n */\r\n// 通用提案审查\r\nconst SuggestReview = () => import('@/views/SuggestReview/SuggestReview')\r\n/**\r\n * 不接收提案\r\n */\r\nconst DoNotReceiveSuggest = () => import('@/views/DoNotReceiveSuggest/DoNotReceiveSuggest')\r\n/**\r\n * 提案交办\r\n */\r\n// 通用提案交办\r\nconst SuggestAssign = () => import('@/views/SuggestAssign/SuggestAssign')\r\n// 建议预交办\r\nconst SuggestAdvanceAssign = () => import('@/views/SuggestAdvanceAssign/SuggestAdvanceAssign')\r\n/**\r\n * 提案并案\r\n */\r\n// 并案提案\r\nconst MergerProposalContainer = () => import('@/views/MergerProposal/MergerProposalContainer')\r\n// 已并案提案\r\nconst HaveMergerProposal = () => import('@/views/MergerProposal/HaveMergerProposal')\r\n\r\n// 办理中提案\r\nconst SuggestTransact = () => import('@/views/SuggestTransact/SuggestTransact')\r\n// 申请延期提案\r\nconst SuggestApplyForPostpone = () => import('@/views/SuggestApplyForPostpone/SuggestApplyForPostpone')\r\n// 申请调整提案\r\nconst SuggestApplyForAdjust = () => import('@/views/SuggestApplyForAdjust/SuggestApplyForAdjust')\r\n// 预交办申请调整建议\r\nconst SuggestPreApplyForAdjust = () => import('@/views/SuggestPreApplyForAdjust/SuggestPreApplyForAdjust')\r\n// 已答复提案\r\nconst SuggestReply = () => import('@/views/SuggestReply/SuggestReply')\r\n// 跟踪办理提案\r\nconst SuggestTrackTransact = () => import('@/views/SuggestTrackTransact/SuggestTrackTransact')\r\n// 已办结提案\r\nconst SuggestConclude = () => import('@/views/SuggestConclude/SuggestConclude')\r\n/**\r\n * 承办单位\r\n */\r\n// 所有提案\r\nconst UnitAllSuggest = () => import('@/views/UnitAllSuggest/UnitAllSuggest')\r\n// 预交办建议\r\nconst UnitSuggestAdvanceAssign = () => import('@/views/UnitSuggestAdvanceAssign/UnitSuggestAdvanceAssign')\r\n// 办理中提案\r\nconst UnitSuggestTransact = () => import('@/views/UnitSuggestTransact/UnitSuggestTransact')\r\n// 已答复提案\r\nconst UnitSuggestReply = () => import('@/views/UnitSuggestReply/UnitSuggestReply')\r\n// 跟踪办理提案\r\nconst UnitSuggestTrackTransact = () => import('@/views/UnitSuggestTrackTransact/UnitSuggestTrackTransact')\r\n// 已办结提案\r\nconst UnitSuggestConclude = () => import('@/views/UnitSuggestConclude/UnitSuggestConclude')\r\n// 已办结提案\r\nconst UnitSummaryReport = () => import('@/views/UnitSummaryReport/UnitSummaryReport')\r\n\r\n/**\r\n * 提案线索征集\r\n */\r\n// 征集文案管理\r\nconst SuggestDocument = () => import('@/views/SuggestClue/SuggestDocument/SuggestDocument')\r\n// 提案线索征集管理\r\nconst SuggestClueControl = () => import('@/views/SuggestClue/SuggestClueControl/SuggestClueControl')\r\n// 提案线索征集添加\r\nconst SuggestClueAdd = () => import('@/views/SuggestClue/SuggestClueAdd/SuggestClueAdd')\r\n// 提案线索征集选登\r\nconst SuggestClueRegister = () => import('@/views/SuggestClue/SuggestClueRegister/SuggestClueRegister')\r\n// 我的线索\r\nconst SuggestClueMine = () => import('@/views/SuggestClue/SuggestClueMine/SuggestClueMine')\r\n// 超级修改\r\nconst SuperEdit = () => import('@/views/SuperEdit/SuperEdit.vue')\r\n// 批量导出\r\nconst batchExport = () => import('@/views/batchExport/batchExport.vue')\r\n// 历史提案\r\nconst HistoricalProposal = () => import('@/views/HistoricalProposal/HistoricalProposal.vue')\r\n\r\nconst routes = [\r\n  {\r\n    path: '/CollectiveProposalUnit',\r\n    name: 'CollectiveProposalUnit',\r\n    component: CollectiveProposalUnit,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/CollectiveProposalUnitUser',\r\n    name: 'CollectiveProposalUnitUser',\r\n    component: CollectiveProposalUnitUser,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/SubmitCollectiveProposalUnitUser',\r\n    name: 'SubmitCollectiveProposalUnitUser',\r\n    component: SubmitCollectiveProposalUnitUser,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestUnit', name: 'SuggestUnit', component: SuggestUnit, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestUnitUser', name: 'SuggestUnitUser', component: SuggestUnitUser, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/SuggestUnitUserSubmit',\r\n    name: 'SuggestUnitUserSubmit',\r\n    component: SuggestUnitUserSubmit,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestNumbering', name: 'SuggestNumbering', component: SuggestNumbering, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestType', name: 'SuggestType', component: SuggestType, meta: { moduleName: 'main' } },\r\n  // { path: '/SimilarityDetails', name: 'SimilarityDetails', component: SimilarityDetails, meta: { moduleName: 'main' } },\r\n  { path: '/SubmitSuggest', name: 'SubmitSuggest', component: SubmitSuggest, meta: { moduleName: 'main' } },\r\n  { path: '/MyLedSuggest', name: 'MyLedSuggest', component: MyLedSuggest, meta: { moduleName: 'main' } },\r\n  { path: '/MyJointSuggest', name: 'MyJointSuggest', component: MyJointSuggest, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestDraftBox', name: 'SuggestDraftBox', component: SuggestDraftBox, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/PersonalAllSuggest',\r\n    name: 'PersonalAllSuggest',\r\n    component: PersonalAllSuggest,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/TextQueryTool', name: 'TextQueryTool', component: TextQueryTool, meta: { moduleName: 'main' } },\r\n  { path: '/ProposalClueList', name: 'ProposalClueList', component: ProposalClueList, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/SubmitProposalClue',\r\n    name: 'SubmitProposalClue',\r\n    component: SubmitProposalClue,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/ProposalClueDetail',\r\n    name: 'ProposalClueDetail',\r\n    component: ProposalClueDetail,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/AllSuggest', name: 'AllSuggest', component: AllSuggest, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestControls', name: 'SuggestControls', component: SuggestControls, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestStatistics', name: 'SuggestStatistics', component: SuggestStatistics, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/SuggestStatisticsList',\r\n    name: 'SuggestStatisticsList',\r\n    component: SuggestStatisticsList,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestCompare', name: 'SuggestCompare', component: SuggestCompare, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestDetail', name: 'SuggestDetail', component: SuggestDetail, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestReview', name: 'SuggestReview', component: SuggestReview, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/DoNotReceiveSuggest',\r\n    name: 'DoNotReceiveSuggest',\r\n    component: DoNotReceiveSuggest,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestAssign', name: 'SuggestAssign', component: SuggestAssign, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/SuggestAdvanceAssign',\r\n    name: 'SuggestAdvanceAssign',\r\n    component: SuggestAdvanceAssign,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/MergerProposalContainer',\r\n    name: 'MergerProposalContainer',\r\n    component: MergerProposalContainer,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/HaveMergerProposal',\r\n    name: 'HaveMergerProposal',\r\n    component: HaveMergerProposal,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestTransact', name: 'SuggestTransact', component: SuggestTransact, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/SuggestApplyForPostpone',\r\n    name: 'SuggestApplyForPostpone',\r\n    component: SuggestApplyForPostpone,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/SuggestPreApplyForAdjust',\r\n    name: 'SuggestPreApplyForAdjust',\r\n    component: SuggestPreApplyForAdjust,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/SuggestApplyForAdjust',\r\n    name: 'SuggestApplyForAdjust',\r\n    component: SuggestApplyForAdjust,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestReply', name: 'SuggestReply', component: SuggestReply, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/SuggestTrackTransact',\r\n    name: 'SuggestTrackTransact',\r\n    component: SuggestTrackTransact,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestConclude', name: 'SuggestConclude', component: SuggestConclude, meta: { moduleName: 'main' } },\r\n  { path: '/UnitAllSuggest', name: 'UnitAllSuggest', component: UnitAllSuggest, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/UnitSuggestAdvanceAssign',\r\n    name: 'UnitSuggestAdvanceAssign',\r\n    component: UnitSuggestAdvanceAssign,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/UnitSuggestTransact',\r\n    name: 'UnitSuggestTransact',\r\n    component: UnitSuggestTransact,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/UnitSuggestReply', name: 'UnitSuggestReply', component: UnitSuggestReply, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/UnitSuggestTrackTransact',\r\n    name: 'UnitSuggestTrackTransact',\r\n    component: UnitSuggestTrackTransact,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/UnitSuggestConclude',\r\n    name: 'UnitSuggestConclude',\r\n    component: UnitSuggestConclude,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/UnitSummaryReport', name: 'UnitSummaryReport', component: UnitSummaryReport, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestDocument', name: 'SuggestDocument', component: SuggestDocument, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/SuggestClueControl',\r\n    name: 'SuggestClueControl',\r\n    component: SuggestClueControl,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestClueAdd', name: 'SuggestClueAdd', component: SuggestClueAdd, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/SuggestClueRegister',\r\n    name: 'SuggestClueRegister',\r\n    component: SuggestClueRegister,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/SuggestClueMine', name: 'SuggestClueMine', component: SuggestClueMine, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestedClassification', name: 'SuggestedClassification', component: SuggestedClassification, meta: { moduleName: 'main' } },\r\n  { path: '/SuggestedSubdivide', name: 'SuggestedSubdivide', component: SuggestedSubdivide, meta: { moduleName: 'main' } },\r\n  { path: '/SuperEdit', name: 'SuperEdit', component: SuperEdit, meta: { moduleName: 'main' } },\r\n  { path: '/batchExport', name: 'batchExport', component: batchExport, meta: { moduleName: 'main' } },\r\n  { path: '/HistoricalProposal', name: 'HistoricalProposal', component: HistoricalProposal, meta: { moduleName: 'main' } }\r\n]\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes\r\n})\r\n\r\nexport { routes }\r\nexport default router\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D;AACA;AACA;AACA;AACA,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA;EAAA,OAAS,MAAM,CAAC,qEAAqE,CAAC;AAAA;AAClH;AACA,IAAMC,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA;EAAA,OAC9B,MAAM,CAAC,6EAA6E,CAAC;AAAA;AACvF;AACA,IAAMC,gCAAgC,GAAG,SAAnCA,gCAAgCA,CAAA;EAAA,OACpC,MAAM,CAAC,mFAAmF,CAAC;AAAA;AAC7F;AACA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAAS,MAAM,CAAC,+CAA+C,CAAC;AAAA;AACjF;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,uDAAuD,CAAC;AAAA;AAC7F;AACA,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA;EAAA,OAAS,MAAM,CAAC,6DAA6D,CAAC;AAAA;AACzG;AACA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OAAS,MAAM,CAAC,yDAAyD,CAAC;AAAA;AAChG;AACA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAAS,MAAM,CAAC,+CAA+C,CAAC;AAAA;AACjF;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,mDAAmD,CAAC;AAAA;AACvF;AACA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA;AACpF;AACA,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAAS,MAAM,CAAC,qDAAqD,CAAC;AAAA;AAC1F;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,uDAAuD,CAAC;AAAA;AAC7F;AACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,+CAA+C,CAAC;AAAA;AACxF;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,4DAA4D,CAAC;AAAA;;AAEhG;AACA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OAAS,MAAM,CAAC,uCAAuC,CAAC;AAAA;AAC9E,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAClF,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAClF;AACA;AACA;AACA;AACA,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS,MAAM,CAAC,+BAA+B,CAAC;AAAA;AAChE,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAC/E;AACA,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;EAAA,OAAS,MAAM,CAAC,6CAA6C,CAAC;AAAA;AACrF;AACA,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA;AAC7F;AACA,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAAS,MAAM,CAAC,uCAAuC,CAAC;AAAA;AAC5E;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,qCAAqC,CAAC;AAAA;AACzE;AACA,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA;EAAA,OAAS,MAAM,CAAC,yDAAyD,CAAC;AAAA;AACvG;AACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,oDAAoD,CAAC;AAAA;AAC7F;AACA;AACA;AACA;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,qCAAqC,CAAC;AAAA;AACzE;AACA;AACA;AACA,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA;AAC3F;AACA;AACA;AACA;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,qCAAqC,CAAC;AAAA;AACzE;AACA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA;EAAA,OAAS,MAAM,CAAC,mDAAmD,CAAC;AAAA;AAC9F;AACA;AACA;AACA;AACA,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA;EAAA,OAAS,MAAM,CAAC,gDAAgD,CAAC;AAAA;AAC9F;AACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,2CAA2C,CAAC;AAAA;;AAEpF;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAC/E;AACA,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA;EAAA,OAAS,MAAM,CAAC,yDAAyD,CAAC;AAAA;AACvG;AACA,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA;EAAA,OAAS,MAAM,CAAC,qDAAqD,CAAC;AAAA;AACjG;AACA,IAAMC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA;EAAA,OAAS,MAAM,CAAC,2DAA2D,CAAC;AAAA;AAC1G;AACA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAAS,MAAM,CAAC,mCAAmC,CAAC;AAAA;AACtE;AACA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA;EAAA,OAAS,MAAM,CAAC,mDAAmD,CAAC;AAAA;AAC9F;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAC/E;AACA;AACA;AACA;AACA,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAAS,MAAM,CAAC,uCAAuC,CAAC;AAAA;AAC5E;AACA,IAAMC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA;EAAA,OAAS,MAAM,CAAC,2DAA2D,CAAC;AAAA;AAC1G;AACA,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA;AAC3F;AACA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OAAS,MAAM,CAAC,2CAA2C,CAAC;AAAA;AAClF;AACA,IAAMC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA;EAAA,OAAS,MAAM,CAAC,2DAA2D,CAAC;AAAA;AAC1G;AACA,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA;AAC3F;AACA,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;EAAA,OAAS,MAAM,CAAC,6CAA6C,CAAC;AAAA;;AAErF;AACA;AACA;AACA;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,qDAAqD,CAAC;AAAA;AAC3F;AACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,2DAA2D,CAAC;AAAA;AACpG;AACA,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAAS,MAAM,CAAC,mDAAmD,CAAC;AAAA;AACxF;AACA,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,6DAA6D,CAAC;AAAA;AACvG;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,qDAAqD,CAAC;AAAA;AAC3F;AACA,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OAAS,MAAM,CAAC,iCAAiC,CAAC;AAAA;AACjE;AACA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAAS,MAAM,CAAC,qCAAqC,CAAC;AAAA;AACvE;AACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,mDAAmD,CAAC;AAAA;AAE5F,IAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,yBAAyB;EAC/BC,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,EAAExD,sBAAsB;EACjCyD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,6BAA6B;EACnCC,IAAI,EAAE,4BAA4B;EAClCC,SAAS,EAAEvD,0BAA0B;EACrCwD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,mCAAmC;EACzCC,IAAI,EAAE,kCAAkC;EACxCC,SAAS,EAAEtD,gCAAgC;EAC3CuD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAErD,WAAW;EAAEsD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACnG;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEpD,eAAe;EAAEqD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EACEJ,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEnD,qBAAqB;EAChCoD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,mBAAmB;EAAEC,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAElD,gBAAgB;EAAEmD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAClH;EAAEJ,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEjD,WAAW;EAAEkD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC;AACnG;AACA;EAAEJ,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEhD,aAAa;EAAEiD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACzG;EAAEJ,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAE/C,YAAY;EAAEgD,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACtG;EAAEJ,IAAI,EAAE,iBAAiB;EAAEC,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAE9C,cAAc;EAAE+C,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC5G;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAE7C,eAAe;EAAE8C,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EACEJ,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAE5C,kBAAkB;EAC7B6C,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAE3C,aAAa;EAAE4C,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACzG;EAAEJ,IAAI,EAAE,mBAAmB;EAAEC,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAE1C,gBAAgB;EAAE2C,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAClH;EACEJ,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEzC,kBAAkB;EAC7B0C,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAExC,kBAAkB;EAC7ByC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEvC,UAAU;EAAEwC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAChG;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEtC,eAAe;EAAEuC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EAAEJ,IAAI,EAAE,oBAAoB;EAAEC,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAErC,iBAAiB;EAAEsC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACrH;EACEJ,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEpC,qBAAqB;EAChCqC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,iBAAiB;EAAEC,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEnC,cAAc;EAAEoC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC5G;EAAEJ,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAElC,aAAa;EAAEmC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACzG;EAAEJ,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAE/B,aAAa;EAAEgC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACzG;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAE9B,mBAAmB;EAC9B+B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAE7B,aAAa;EAAE8B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACzG;EACEJ,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAE5B,oBAAoB;EAC/B6B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAE3B,uBAAuB;EAClC4B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAE1B,kBAAkB;EAC7B2B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEzB,eAAe;EAAE0B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EACEJ,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAExB,uBAAuB;EAClCyB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAEtB,wBAAwB;EACnCuB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEvB,qBAAqB;EAChCwB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAErB,YAAY;EAAEsB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACtG;EACEJ,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAEpB,oBAAoB;EAC/BqB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEnB,eAAe;EAAEoB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EAAEJ,IAAI,EAAE,iBAAiB;EAAEC,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAElB,cAAc;EAAEmB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC5G;EACEJ,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAEjB,wBAAwB;EACnCkB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEhB,mBAAmB;EAC9BiB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,mBAAmB;EAAEC,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAEf,gBAAgB;EAAEgB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAClH;EACEJ,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAEd,wBAAwB;EACnCe,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEb,mBAAmB;EAC9Bc,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,oBAAoB;EAAEC,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAEZ,iBAAiB;EAAEa,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACrH;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEX,eAAe;EAAEY,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EACEJ,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEV,kBAAkB;EAC7BW,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,iBAAiB;EAAEC,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAET,cAAc;EAAEU,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC5G;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAER,mBAAmB;EAC9BS,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEP,eAAe;EAAEQ,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EAAEJ,IAAI,EAAE,0BAA0B;EAAEC,IAAI,EAAE,yBAAyB;EAAEC,SAAS,EAAEjC,uBAAuB;EAAEkC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACvI;EAAEJ,IAAI,EAAE,qBAAqB;EAAEC,IAAI,EAAE,oBAAoB;EAAEC,SAAS,EAAEhC,kBAAkB;EAAEiC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACxH;EAAEJ,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEN,SAAS;EAAEO,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC7F;EAAEJ,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEL,WAAW;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACnG;EAAEJ,IAAI,EAAE,qBAAqB;EAAEC,IAAI,EAAE,oBAAoB;EAAEC,SAAS,EAAEJ,kBAAkB;EAAEK,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,CACzH;AAED,IAAMC,MAAM,GAAG7D,YAAY,CAAC;EAC1B8D,OAAO,EAAE7D,gBAAgB,CAAC8D,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CV;AACF,CAAC,CAAC;AAEF,SAASA,MAAM;AACf,eAAeM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}