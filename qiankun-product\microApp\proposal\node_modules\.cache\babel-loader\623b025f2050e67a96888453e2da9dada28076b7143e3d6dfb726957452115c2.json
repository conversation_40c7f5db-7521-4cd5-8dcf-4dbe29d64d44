{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createTextVNode as _createTextVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SubmitProposalClueBody\"\n};\nvar _hoisted_2 = {\n  class: \"globalPaperFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    always: \"\",\n    class: \"SubmitProposalClue\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"SubmitProposalClueNameBody\"\n      }, [_createElementVNode(\"div\", {\n        class: \"SubmitProposalClueName\"\n      }, \"提案线索\")], -1 /* HOISTED */)), _createVNode(_component_el_form, {\n        ref: \"formRef\",\n        model: $setup.form,\n        rules: $setup.rules,\n        inline: \"\",\n        \"show-message\": false,\n        class: \"globalPaperForm\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_form_item, {\n            label: \"提案标题\",\n            prop: \"title\",\n            class: \"SubmitProposalClueTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.title,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n                  return $setup.form.title = $event;\n                }),\n                placeholder: \"请输入提案标题\",\n                clearable: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"提案大类\",\n            class: \"SubmitProposalClueTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_select, {\n                modelValue: $setup.form.bigTheme,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n                  return $setup.form.bigTheme = $event;\n                }),\n                placeholder: \"请选择提案大类\",\n                clearable: \"\"\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.bigTheme, function (item) {\n                    return _openBlock(), _createBlock(_component_el_option, {\n                      key: item.id,\n                      label: item.name,\n                      value: item.id\n                    }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"提案内容\",\n            prop: \"content\",\n            class: \"SubmitProposalClueTitle\"\n          }), _createVNode(_component_TinyMceEditor, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.content = $event;\n            })\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: _cache[3] || (_cache[3] = function ($event) {\n              return $setup.submitForm($setup.formRef);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[4] || (_cache[4] = [_createTextVNode(\"提交\")]);\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_button, {\n            onClick: $setup.resetForm\n          }, {\n            default: _withCtx(function () {\n              return _cache[5] || (_cache[5] = [_createTextVNode(\"取消\")]);\n            }),\n            _: 1 /* STABLE */\n          })])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\"])])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_scrollbar", "always", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "title", "_cache", "$event", "placeholder", "clearable", "_", "_component_el_select", "bigTheme", "_createElementBlock", "_Fragment", "_renderList", "item", "_component_el_option", "key", "id", "name", "value", "_component_TinyMceEditor", "content", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\SubmitProposalClue.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"SubmitProposalClue\">\r\n    <div class=\"SubmitProposalClueBody\">\r\n      <div class=\"SubmitProposalClueNameBody\">\r\n        <div class=\"SubmitProposalClueName\">提案线索</div>\r\n      </div>\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline :show-message=\"false\" class=\"globalPaperForm\">\r\n        <el-form-item label=\"提案标题\" prop=\"title\" class=\"SubmitProposalClueTitle\">\r\n          <el-input v-model=\"form.title\" placeholder=\"请输入提案标题\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"提案大类\" class=\"SubmitProposalClueTitle\">\r\n          <el-select v-model=\"form.bigTheme\" placeholder=\"请选择提案大类\" clearable>\r\n            <el-option v-for=\"item in bigTheme\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案内容\" prop=\"content\" class=\"SubmitProposalClueTitle\"></el-form-item>\r\n        <TinyMceEditor v-model=\"form.content\" />\r\n        <div class=\"globalPaperFormButton\">\r\n          <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n          <el-button @click=\"resetForm\">取消</el-button>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitProposalClue' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\n\r\nconst route = useRoute()\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  title: '', // 提案标题\r\n  bigTheme: '',\r\n  content: ''\r\n})\r\nconst rules = reactive({\r\n  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }]\r\n})\r\nconst bigTheme = ref([])\r\n\r\nonActivated(() => {\r\n  suggestionThemeSelect()\r\n  if (route.query.id) { proposalClueInfo() }\r\n})\r\nconst suggestionThemeSelect = async () => {\r\n  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })\r\n  var { data } = res\r\n  bigTheme.value = data\r\n}\r\nconst proposalClueInfo = async () => {\r\n  const { data } = await api.proposalClueInfo({ detailId: route.query.id })\r\n  form.title = data.title\r\n  form.bigTheme = data.bigThemeId\r\n  form.content = data.content\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(route.query.id ? '/proposalClue/edit' : '/proposalClue/add', {\r\n    form: { title: form.title, bigTheme: form.bigTheme, content: form.content }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: route.query.id ? '编辑成功' : '提交成功' })\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitProposalClue {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SubmitProposalClueBody {\r\n    width: 990px;\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n\r\n    .SubmitProposalClueNameBody {\r\n      padding: var(--zy-distance-one);\r\n      padding-bottom: 0;\r\n\r\n      .SubmitProposalClueName {\r\n        padding: 20px 40px;\r\n        font-weight: bold;\r\n        text-align: center;\r\n        color: var(--zy-el-color-primary);\r\n        font-size: var(--zy-title-font-size);\r\n        line-height: var(--zy-line-height);\r\n        border-bottom: 3px solid var(--zy-el-color-primary);\r\n      }\r\n    }\r\n\r\n    .globalPaperForm {\r\n      width: 100%;\r\n      padding: var(--zy-distance-one);\r\n      padding-top: 0;\r\n\r\n      .zy-el-form-item {\r\n        width: 50%;\r\n        margin: 0;\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n\r\n        .zy-el-form-item__label {\r\n          width: 138px;\r\n          justify-content: center;\r\n        }\r\n\r\n        .zy-el-form-item__content {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n          border-right: 1px solid transparent;\r\n\r\n          &>.zy-el-input,\r\n          .zy-el-input-number {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-select,\r\n          .zy-el-select-v2 {\r\n            .zy-el-select__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-radio-group {\r\n            padding-left: 15px;\r\n          }\r\n\r\n          &>.zy-el-date-editor {\r\n            width: 100%;\r\n\r\n            &>.zy-el-input__wrapper {\r\n              width: 100%;\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitProposalClueTitle {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          border-right-color: transparent;\r\n        }\r\n      }\r\n\r\n      .TinyMceEditor {\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n      }\r\n\r\n      .globalPaperFormButton {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding-top: 22px;\r\n\r\n        .zy-el-button+.zy-el-button {\r\n          margin-left: var(--zy-distance-two);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAwB;;EAe1BA,KAAK,EAAC;AAAuB;;;;;;;;;;uBAhBxCC,YAAA,CAsBeC,uBAAA;IAtBDC,MAAM,EAAN,EAAM;IAACH,KAAK,EAAC;;IAD7BI,OAAA,EAAAC,QAAA,CAEI;MAAA,OAoBM,CApBNC,mBAAA,CAoBM,OApBNC,UAoBM,G,0BAnBJD,mBAAA,CAEM;QAFDN,KAAK,EAAC;MAA4B,IACrCM,mBAAA,CAA8C;QAAzCN,KAAK,EAAC;MAAwB,GAAC,MAAI,E,sBAE1CQ,YAAA,CAeUC,kBAAA;QAfDC,GAAG,EAAC,SAAS;QAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;QAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;QAAEC,MAAM,EAAN,EAAM;QAAE,cAAY,EAAE,KAAK;QAAEf,KAAK,EAAC;;QAN7FI,OAAA,EAAAC,QAAA,CAOQ;UAAA,OAEe,CAFfG,YAAA,CAEeQ,uBAAA;YAFDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,OAAO;YAAClB,KAAK,EAAC;;YAPtDI,OAAA,EAAAC,QAAA,CAQU;cAAA,OAAiE,CAAjEG,YAAA,CAAiEW,mBAAA;gBAR3EC,UAAA,EAQ6BR,MAAA,CAAAC,IAAI,CAACQ,KAAK;gBARvC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAQ6BX,MAAA,CAAAC,IAAI,CAACQ,KAAK,GAAAE,MAAA;gBAAA;gBAAEC,WAAW,EAAC,SAAS;gBAACC,SAAS,EAAT;;;YAR/DC,CAAA;cAUQlB,YAAA,CAIeQ,uBAAA;YAJDC,KAAK,EAAC,MAAM;YAACjB,KAAK,EAAC;;YAVzCI,OAAA,EAAAC,QAAA,CAWU;cAAA,OAEY,CAFZG,YAAA,CAEYmB,oBAAA;gBAbtBP,UAAA,EAW8BR,MAAA,CAAAC,IAAI,CAACe,QAAQ;gBAX3C,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAW8BX,MAAA,CAAAC,IAAI,CAACe,QAAQ,GAAAL,MAAA;gBAAA;gBAAEC,WAAW,EAAC,SAAS;gBAACC,SAAS,EAAT;;gBAXnErB,OAAA,EAAAC,QAAA,CAYuB;kBAAA,OAAwB,E,kBAAnCwB,mBAAA,CAAyFC,SAAA,QAZrGC,WAAA,CAYsCnB,MAAA,CAAAgB,QAAQ,EAZ9C,UAY8BI,IAAI;yCAAtB/B,YAAA,CAAyFgC,oBAAA;sBAApDC,GAAG,EAAEF,IAAI,CAACG,EAAE;sBAAGlB,KAAK,EAAEe,IAAI,CAACI,IAAI;sBAAGC,KAAK,EAAEL,IAAI,CAACG;;;;gBAZ/FT,CAAA;;;YAAAA,CAAA;cAeQlB,YAAA,CAAyFQ,uBAAA;YAA3EC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,SAAS;YAAClB,KAAK,EAAC;cAChDQ,YAAA,CAAwC8B,wBAAA;YAhBhDlB,UAAA,EAgBgCR,MAAA,CAAAC,IAAI,CAAC0B,OAAO;YAhB5C,uBAAAjB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAgBgCX,MAAA,CAAAC,IAAI,CAAC0B,OAAO,GAAAhB,MAAA;YAAA;mDACpCjB,mBAAA,CAGM,OAHNkC,UAGM,GAFJhC,YAAA,CAAqEiC,oBAAA;YAA1DC,IAAI,EAAC,SAAS;YAAEC,OAAK,EAAArB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEX,MAAA,CAAAgC,UAAU,CAAChC,MAAA,CAAAiC,OAAO;YAAA;;YAlB9DzC,OAAA,EAAAC,QAAA,CAkBiE;cAAA,OAAEiB,MAAA,QAAAA,MAAA,OAlBnEwB,gBAAA,CAkBiE,IAAE,E;;YAlBnEpB,CAAA;cAmBUlB,YAAA,CAA4CiC,oBAAA;YAAhCE,OAAK,EAAE/B,MAAA,CAAAmC;UAAS;YAnBtC3C,OAAA,EAAAC,QAAA,CAmBwC;cAAA,OAAEiB,MAAA,QAAAA,MAAA,OAnB1CwB,gBAAA,CAmBwC,IAAE,E;;YAnB1CpB,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}