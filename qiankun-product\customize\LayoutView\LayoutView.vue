<template>
  <el-container class="LayoutView">
    <el-header class="LayoutViewHeader" v-if="route.name != 'WorkBenchCopy'">
      <div class="LayoutViewBox">
        <el-image class="LayoutViewLogo" :src="systemLogo" fit="contain" />
        <div class="LayoutViewName" v-html="systemName"></div>
      </div>
      <div class="LayoutViewInfo" ref="LayoutViewInfo">
        <!-- <el-dropdown @command="handleFontSizeChange">
          <span class="LayoutViewFontSize">
            {{ currentFontSizeLabel }} <el-icon><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="default">默认字号</el-dropdown-item>
              <el-dropdown-item command="large">大字号</el-dropdown-item>
              <el-dropdown-item command="xlarge">超大字号</el-dropdown-item>
            </el-dropdown-menu>
          </template>
</el-dropdown> -->
        <xyl-region v-model="regionId" :data="area" @select="regionSelect" v-show="!isChildView"
          :props="{ label: 'name', children: 'children' }"></xyl-region>
        <button v-if="route.name == 'homePage'" class="new_version" @click="newVersion">
          <img src="../img/login_btn_bg.png" alt="" />
          <span>新版本</span>
        </button>
        <div class="LayoutViewMenuItem" @click="returnHome">
          <img src="../img/icon_layout_home.png" alt="首页" />
          <span>首页</span>
        </div>
        <div class="LayoutViewMenuItem" @click="openInformation" v-if="false">
          <img src="../img/icon_layout_news.png" alt="资讯" />
          <span>资讯</span>
        </div>
        <div class="LayoutViewMenuItem" @click="sysManagement" v-if="isSys">
          <img src="../img/icon_layout_system.png" alt="系统" />
          <span>系统</span>
        </div>
        <el-tooltip placement="top" effect="light" :offset="6" :disabled="!role.length">
          <template #content>
            <div class="LayoutViewRoleItem" v-for="(item, index) in role" :key="index">{{ item }}</div>
          </template>
          <div class="LayoutViewMenuItem" @click="sysUser">
            <img :src="user.image" alt="" class="avatar" />
            <span>{{ user.userName }}</span>
          </div>
        </el-tooltip>
        <div class="LayoutViewMenuItem" @click="handleCommand('exit')">
          <span>退出</span>
        </div>
      </div>
    </el-header>
    <el-container class="LayoutViewContainer"
      :style="route.name != 'WorkBenchCopy' ? 'height: calc(100% - 120px);' : 'height: 100%;'">
      <el-aside class="LayoutViewAside" v-show="isView">
        <LayoutMenu v-model="menuId" :menuData="menuData" @select="menuClick"></LayoutMenu>
      </el-aside>
      <el-main class="LayoutViewMain"
        :class="{ LayoutViewMainView: !isView, LayoutViewMainBreadcrumb: (!isView && (tabData.length > 1)) }">
        <xyl-tab v-model="menuId" @tab-click="tabClick" @refresh="handleRefresh" @close="handleClose" feature
          @closeOther="handleCloseOther" v-show="isView">
          <xyl-tab-item v-for="item in tabData" :key="item.id" :value="item.id">{{ item.name }}</xyl-tab-item>
        </xyl-tab>
        <div class="LayoutViewBreadcrumb" v-if="!isView && tabData.length > 1">
          <el-breadcrumb :separator-icon="ArrowRight">
            <el-breadcrumb-item v-for="(item, index) in tabData" :key="`key-${item.id}`"
              @click="handleBreadcrumb(item, index)">
              {{ item.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="LayoutViewBody">
          <router-view v-slot="{ Component }">
            <keep-alive :include="keepAliveRoute">
              <component v-if="isMain && isRefresh" :key="$route.fullPath" :is="Component"></component>
            </keep-alive>
          </router-view>
          <SubAppViewport v-for="item in MicroApp" :key="item" v-show="!isMain && isMicroApp === item" :name="item">
          </SubAppViewport>
        </div>
      </el-main>
      <el-aside class="LayoutViewFloatingWindow" v-if="whetherAiChat">
        <transition name="width-animation">
          <div class="LayoutViewFloatingWindowBody" :style="{ '--ai-chat-target-width': AiChatTargetWidth }"
            v-if="AiChatViewType" v-show="AiChatWindowShow">
            <GlobalAiChat v-model="AiChatWindowShow"></GlobalAiChat>
          </div>
        </transition>
      </el-aside>
    </el-container>
    <xyl-popup-window v-model="helpShow" name="帮助文档">
      <HelpDocument></HelpDocument>
    </xyl-popup-window>
    <xyl-popup-window v-model="editPassWordShow" name="修改密码">
      <EditPassWord :type="verifyEditPassWord" @callback="editPassWordCallback"></EditPassWord>
    </xyl-popup-window>
    <div class="ConstraintEditPassWord" v-if="verifyEditPassWordShow">
      <EditPassWord :type="verifyEditPassWord" @callback="editPassWordCallback"></EditPassWord>
    </div>
    <GlobalRegionSelect v-if="isRegionSelectShow" @callback="regionSelect"></GlobalRegionSelect>
  </el-container>
  <qusetionAnswering></qusetionAnswering>
  <GlobalChatFloating v-if="rongCloudToken"></GlobalChatFloating>
  <GlobalFloatingWindow v-model="AiChatWindowShow" :disabled="AiChatViewType" v-if="whetherAiChat" />
  <GlobalAiControls v-if="whetherAiChat" />
  <suggestPop v-if="isMain && loginHintShow" />
</template>
<script>
export default { name: 'LayoutView' }
</script>
<script setup>
import { defineAsyncComponent, ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { qiankun, LayoutView, ChatMethod, AiChatMethod, refreshIcon, loginHintMethod } from './LayoutView.js'
import {
  systemLogo,
  systemName,
  whetherAiChat,
  systemNameAreaPrefix,
  layoutNameBg,
  layoutChildBg,
  layoutChildNameBg
} from 'common/js/system_var.js'
import { ArrowRight } from '@element-plus/icons-vue'
const HelpDocument = defineAsyncComponent(() => import('../LayoutContainer/components/HelpDocument'))
const EditPassWord = defineAsyncComponent(() => import('../LayoutContainer/components/EditPassWord'))
// const LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))
// const LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))
const GlobalRegionSelect = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalRegionSelect'))
const GlobalChatFloating = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalChatFloating'))
const GlobalFloatingWindow = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalFloatingWindow'))
const GlobalAiControls = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalAiControls'))
const GlobalAiChat = defineAsyncComponent(() => import('../GlobalAiChat/GlobalAiChat'))
const LayoutMenu = defineAsyncComponent(() => import('./component/LayoutMenu/LayoutMenu.vue'))
const suggestPop = defineAsyncComponent(() => import('./component/suggestPop'))
const qusetionAnswering = defineAsyncComponent(() => import('./component/question-answering.vue'))
const SubAppViewport = {
  name: 'SubAppViewport',
  props: ['name'],
  template: `<div :id="name" class="subApp-viewport"></div>`
}
const router = useRouter()
const { isMain } = qiankun(useRoute())
const route = useRoute()
// const currentFontSizeLabel = ref('切换字号')
const isSys = ref(false)
// const loginSystemName = computed(() => {
//   const name = (platformAreaName.value || '') + systemName.value
//   const num = Number(loginNameLineFeedPosition.value || '0') || 0
//   return num ? name.substring(0, num) + '\n' + name.substring(num) : name
// })
const {
  user, area, role, left, width, LayoutViewBox, LayoutViewInfo, helpShow, handleCommand,
  editPassWordShow, verifyEditPassWord, verifyEditPassWordShow, editPassWordCallback,
  regionId, regionName, regionSelect, isRegionSelectShow, isView, isChildView, tabMenu, tabMenuData, handleClick,
  menuId, menuData, menuClick, handleBreadcrumb, WorkBenchObj, childData, WorkBenchReturn,
  isRefresh, keepAliveRoute, tabData, tabClick, handleRefresh, handleClose, handleCloseOther,
  isMicroApp, MicroApp, openPage, leftMenuData, WorkBenchList
} = LayoutView(useRoute(), useRouter())
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      const roleList = JSON.parse(sessionStorage.getItem('role'))
      console.log('当前角色===>', roleList)
      if (roleList) { isSys.value = roleList?.includes('管理员') }
    }, 1000)
  })
})
const { rongCloudToken } = ChatMethod()
const { AiChatTargetWidth, AiChatViewType, AiChatWindowShow } = AiChatMethod()

// const handleFontSizeChange = (command) => {
//   if (command === 'default') currentFontSizeLabel.value = '默认字号'
//   if (command === 'large') currentFontSizeLabel.value = '大字号'
//   if (command === 'xlarge') currentFontSizeLabel.value = '超大字号'
//   // 这里可以加实际字号切换逻辑
// }
const returnHome = () => {
  if (isChildView.value) {
    WorkBenchReturn()
  } else {
    openPage({ key: 'routePath', value: '/homePage' })
  }
}

// 切换新版本
const newVersion = () => {
  console.log('切换到新版本')
  openPage({ key: 'routePath', value: '/WorkBenchCopy' })
}
// 资讯
const openInformation = () => {
  openPage({ key: 'routePath', value: '/information/AllInformation?moduleId=1&moduleName=资讯' })
  // const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')
  // const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '新闻资讯')
  // leftMenuData(systemOperation)
}
// 系统管理
const sysManagement = () => {
  const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')
  const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '系统运维')
  leftMenuData(systemOperation)
}
// 跳转到我的
const sysUser = () => {
  const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')
  const myOperation = filtered.flatMap(item => item.children).find(child => child.name === '我的')
  leftMenuData(myOperation)
}

const { loginHintShow } = loginHintMethod()

const handleAiToolBox = () => {
  router.push('/GlobalAiToolBox')
}
</script>
<style lang="scss">
.LayoutView {
  width: 100%;
  height: 100%;

  .LayoutViewHeader {
    height: 120px;
    background: url("../img/layout_top_bg1.png") no-repeat;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    justify-content: space-between;

    .LayoutViewBox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      position: relative;
      z-index: 2;
      flex-shrink: 0;

      .LayoutViewLogo {
        height: 75px;
        width: 75px;

        .zy-el-image {
          width: 100%;
          display: block;
        }
      }

      .LayoutViewName {
        font-size: 37px;
        color: #fff;
        font-weight: bold;
        margin-left: 15px;
        letter-spacing: 5px;
      }
    }

    .LayoutViewInfo {
      display: flex;
      align-items: center;
      gap: 32px;

      .new_version {
        background: none;
        border: none;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0;
        cursor: pointer;

        img {
          height: 39px;
        }

        span {
          position: absolute;
          left: 0;
          width: 100%;
          text-align: center;
          color: rgb(0, 51, 152);
          font-size: 14px;
          line-height: 39px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .LayoutViewFontSize {
        color: #fff;
        font-size: 18px;
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-right: 24px;

        .el-icon {
          margin-left: 4px;
        }
      }

      .LayoutViewMenuItem {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #fff;
        font-size: 18px;
        cursor: pointer;
        margin-right: 24px;

        img {
          width: 35px;
          height: 35px;
        }

        .avatar {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          margin: 0 8px;
          object-fit: cover;
          border: 2px solid #fff;
          background: #eee;
        }

        span {
          font-size: 16px;
          margin-top: 4px;
        }
      }
    }
  }

  .LayoutViewContainer {
    width: 100%;
    // height: 100%;
    //  height: calc(100% - 62px);
    background: var(--zy-el-color-info-light-9);

    .LayoutViewFloatingWindow {
      width: auto;
      height: 100%;

      .LayoutViewFloatingWindowBody {
        width: var(--ai-chat-target-width);
        height: 100%;
        background: #fff;
        box-sizing: border-box;
        transform-origin: left center;
        border-left: 1px solid var(--zy-el-border-color-lighter);
      }

      /* 进入动画 */
      .width-animation-enter-active {
        animation: widen 0.2s ease-in-out forwards;
      }

      /* 离开动画 */
      .width-animation-leave-active {
        animation: narrow 0.2s ease-in-out forwards;
      }

      /* 定义进入动画 */
      @keyframes widen {
        from {
          width: 0;
        }

        to {
          width: var(--ai-chat-target-width);
        }
      }

      /* 定义离开动画 */
      @keyframes narrow {
        from {
          width: var(--ai-chat-target-width);
        }

        to {
          width: 0;
        }
      }
    }

    .LayoutViewAside {
      width: auto;
    }

    .LayoutViewMain {
      height: 100%;
      padding: var(--zy-distance-three) var(--zy-distance-three) 0 0;

      .LayoutViewBreadcrumb {
        width: 100%;
        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px);
        display: flex;
        align-items: center;
        background-color: #fff;
        padding: 0 var(--zy-distance-two);
        border-bottom: 1px solid var(--zy-el-border-color-lighter);

        .zy-el-breadcrumb {
          font-size: var(--zy-name-font-size);

          .zy-el-breadcrumb__inner {
            cursor: pointer;
            font-weight: bold;
            color: var(--zy-el-color-primary);
          }

          .zy-el-breadcrumb__item {
            &:last-child {
              .zy-el-breadcrumb__inner {
                cursor: text;
                font-weight: normal;
                color: var(--zy-el-text-color-regular);
              }
            }
          }
        }
      }

      .LayoutViewBody {
        width: 100%;
        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));
        background-color: #fff;

        .subApp-viewport {
          width: 100%;
          height: 100%;

          >div {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .LayoutViewMainView {
      width: 100%;
      padding: 0;
      background: #f8f8f8;

      .LayoutViewBody {
        height: 100%;
      }
    }

    .LayoutViewMainBreadcrumb {
      width: 100%;

      .LayoutViewBody {
        position: relative;
        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));
      }
    }
  }

  .ConstraintEditPassWord {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .EditPassWord {
      box-shadow: 0px 2px 40px rgba(0, 0, 0, 0.1);
    }
  }
}

.LayoutViewRoleItem {
  font-size: var(--zy-text-font-size);
  line-height: var(--zy-line-height);
}
</style>
