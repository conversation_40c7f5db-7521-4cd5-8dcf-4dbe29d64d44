{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"HistoricalProposalDetails\"\n};\nvar _hoisted_2 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_global_info_item = _resolveComponent(\"global-info-item\");\n  var _component_global_info_line = _resolveComponent(\"global-info-line\");\n  var _component_xyl_global_file = _resolveComponent(\"xyl-global-file\");\n  var _component_global_info = _resolveComponent(\"global-info\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"HistoricalProposalDetailsName\"\n  }, \"详情\", -1 /* HOISTED */)), _createVNode(_component_global_info, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"届次\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.termYearId), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"案号\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.serialNumber), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"标题\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.details.title), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"分类\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.type), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"提案者\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.suggestUserId), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"党派\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.party), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"界别\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.circles), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"联名人\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.joinProposal), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"提交时间\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.format($setup.details.submitDate, 'YYYY-MM-DD')), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"内容\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", {\n            class: \"content\",\n            innerHTML: $setup.details.content\n          }, null, 8 /* PROPS */, _hoisted_2)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"附件\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_global_file, {\n            fileData: $setup.details.fileInfoList\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"状态\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.processStatus), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"不予立案理由\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.notRegisteredReasonName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_global_info_item, {\n            label: \"办理方式\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.handlingMethod), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_global_info_item, {\n            label: \"办理单位\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.handlingUnit), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_global_info_item, {\n        label: \"答复文件\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_global_file, {\n            fileData: $setup.details.replyFileInfoList\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), false ? (_openBlock(), _createBlock(_component_global_info_item, {\n        key: 0,\n        label: \"办理情况\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.details.handlingContent), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_global_info_line, null, {\n        default: _withCtx(function () {\n          return [false ? (_openBlock(), _createBlock(_component_global_info_item, {\n            key: 0,\n            label: \"提案评价\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.unitEvaluationName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), false ? (_openBlock(), _createBlock(_component_global_info_item, {\n            key: 1,\n            label: \"征询意见表满意度\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.details.memberEvaluationName), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createVNode", "_component_global_info", "default", "_withCtx", "_component_global_info_line", "_component_global_info_item", "label", "_createTextVNode", "_toDisplayString", "$setup", "details", "termYearId", "_", "serialNumber", "title", "type", "suggestUserId", "party", "circles", "joinProposal", "format", "submitDate", "innerHTML", "content", "_hoisted_2", "_component_xyl_global_file", "fileData", "fileInfoList", "processStatus", "notRegisteredReasonName", "handlingMethod", "handlingUnit", "replyFileInfoList", "_createBlock", "key", "handlingContent", "_createCommentVNode", "unitEvaluationName", "memberEvaluationName"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\HistoricalProposal\\HistoricalProposalDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"HistoricalProposalDetails\">\r\n    <div class=\"HistoricalProposalDetailsName\">详情</div>\r\n    <global-info>\r\n      <global-info-line>\r\n        <global-info-item label=\"届次\">{{ details.termYearId }}</global-info-item>\r\n        <global-info-item label=\"案号\">{{ details.serialNumber }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-item label=\"标题\">{{ details.title }}</global-info-item>\r\n      <global-info-line>\r\n        <global-info-item label=\"分类\">{{ details.type }}</global-info-item>\r\n        <global-info-item label=\"提案者\">{{ details.suggestUserId }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-line>\r\n        <global-info-item label=\"党派\">{{ details.party }}</global-info-item>\r\n        <global-info-item label=\"界别\">{{ details.circles }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-line>\r\n        <global-info-item label=\"联名人\">{{ details.joinProposal }}</global-info-item>\r\n        <global-info-item label=\"提交时间\">{{ format(details.submitDate, 'YYYY-MM-DD') }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-item label=\"内容\">\r\n        <div class=\"content\" v-html=\"details.content\"></div>\r\n      </global-info-item>\r\n      <global-info-item label=\"附件\">\r\n        <xyl-global-file :fileData=\"details.fileInfoList\"></xyl-global-file>\r\n      </global-info-item>\r\n      <global-info-line>\r\n        <global-info-item label=\"状态\">{{ details.processStatus }}</global-info-item>\r\n        <global-info-item label=\"不予立案理由\">{{ details.notRegisteredReasonName }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-line>\r\n        <global-info-item label=\"办理方式\">{{ details.handlingMethod }}</global-info-item>\r\n        <global-info-item label=\"办理单位\">{{ details.handlingUnit }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-item label=\"答复文件\">\r\n        <xyl-global-file :fileData=\"details.replyFileInfoList\"></xyl-global-file>\r\n      </global-info-item>\r\n      <global-info-item label=\"办理情况\" v-if=\"false\">{{ details.handlingContent }}</global-info-item>\r\n      <global-info-line>\r\n        <global-info-item label=\"提案评价\" v-if=\"false\">{{ details.unitEvaluationName }}</global-info-item>\r\n        <global-info-item label=\"征询意见表满意度\" v-if=\"false\">{{ details.memberEvaluationName }}</global-info-item>\r\n      </global-info-line>\r\n    </global-info>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'HistoricalProposalDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api';\r\nimport { ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\n\r\nconst details = ref({})\r\n\r\nonMounted(() => {\r\n  getInfo()\r\n})\r\n\r\nconst getInfo = async () => {\r\n  const res = await api.proposalHistoryV2Info({ detailId: props.id })\r\n  details.value = res.data\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.HistoricalProposalDetails {\r\n  width: 820px;\r\n  padding: 40px;\r\n  padding-top: 0;\r\n\r\n  .HistoricalProposalDetailsName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    color: var(--zy-el-color-primary);\r\n    border-bottom: 1px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .content {\r\n    padding: 20px 0;\r\n    overflow: hidden;\r\n    line-height: var(--zy-line-height);\r\n\r\n    img,\r\n    video {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA2B;iBADxC;;;;;;uBACEC,mBAAA,CA2CM,OA3CNC,UA2CM,G,0BA1CJC,mBAAA,CAAmD;IAA9CH,KAAK,EAAC;EAA+B,GAAC,IAAE,sBAC7CI,YAAA,CAwCcC,sBAAA;IA3ClBC,OAAA,EAAAC,QAAA,CAIM;MAAA,OAGmB,CAHnBH,YAAA,CAGmBI,2BAAA;QAPzBF,OAAA,EAAAC,QAAA,CAKQ;UAAA,OAAwE,CAAxEH,YAAA,CAAwEK,2BAAA;YAAtDC,KAAK,EAAC;UAAI;YALpCJ,OAAA,EAAAC,QAAA,CAKqC;cAAA,OAAwB,CAL7DI,gBAAA,CAAAC,gBAAA,CAKwCC,MAAA,CAAAC,OAAO,CAACC,UAAU,iB;;YAL1DC,CAAA;cAMQZ,YAAA,CAA0EK,2BAAA;YAAxDC,KAAK,EAAC;UAAI;YANpCJ,OAAA,EAAAC,QAAA,CAMqC;cAAA,OAA0B,CAN/DI,gBAAA,CAAAC,gBAAA,CAMwCC,MAAA,CAAAC,OAAO,CAACG,YAAY,iB;;YAN5DD,CAAA;;;QAAAA,CAAA;UAQMZ,YAAA,CAAmEK,2BAAA;QAAjDC,KAAK,EAAC;MAAI;QARlCJ,OAAA,EAAAC,QAAA,CAQmC;UAAA,OAAmB,CARtDI,gBAAA,CAAAC,gBAAA,CAQsCC,MAAA,CAAAC,OAAO,CAACI,KAAK,iB;;QARnDF,CAAA;UASMZ,YAAA,CAGmBI,2BAAA;QAZzBF,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAAkE,CAAlEH,YAAA,CAAkEK,2BAAA;YAAhDC,KAAK,EAAC;UAAI;YAVpCJ,OAAA,EAAAC,QAAA,CAUqC;cAAA,OAAkB,CAVvDI,gBAAA,CAAAC,gBAAA,CAUwCC,MAAA,CAAAC,OAAO,CAACK,IAAI,iB;;YAVpDH,CAAA;cAWQZ,YAAA,CAA4EK,2BAAA;YAA1DC,KAAK,EAAC;UAAK;YAXrCJ,OAAA,EAAAC,QAAA,CAWsC;cAAA,OAA2B,CAXjEI,gBAAA,CAAAC,gBAAA,CAWyCC,MAAA,CAAAC,OAAO,CAACM,aAAa,iB;;YAX9DJ,CAAA;;;QAAAA,CAAA;UAaMZ,YAAA,CAGmBI,2BAAA;QAhBzBF,OAAA,EAAAC,QAAA,CAcQ;UAAA,OAAmE,CAAnEH,YAAA,CAAmEK,2BAAA;YAAjDC,KAAK,EAAC;UAAI;YAdpCJ,OAAA,EAAAC,QAAA,CAcqC;cAAA,OAAmB,CAdxDI,gBAAA,CAAAC,gBAAA,CAcwCC,MAAA,CAAAC,OAAO,CAACO,KAAK,iB;;YAdrDL,CAAA;cAeQZ,YAAA,CAAqEK,2BAAA;YAAnDC,KAAK,EAAC;UAAI;YAfpCJ,OAAA,EAAAC,QAAA,CAeqC;cAAA,OAAqB,CAf1DI,gBAAA,CAAAC,gBAAA,CAewCC,MAAA,CAAAC,OAAO,CAACQ,OAAO,iB;;YAfvDN,CAAA;;;QAAAA,CAAA;UAiBMZ,YAAA,CAGmBI,2BAAA;QApBzBF,OAAA,EAAAC,QAAA,CAkBQ;UAAA,OAA2E,CAA3EH,YAAA,CAA2EK,2BAAA;YAAzDC,KAAK,EAAC;UAAK;YAlBrCJ,OAAA,EAAAC,QAAA,CAkBsC;cAAA,OAA0B,CAlBhEI,gBAAA,CAAAC,gBAAA,CAkByCC,MAAA,CAAAC,OAAO,CAACS,YAAY,iB;;YAlB7DP,CAAA;cAmBQZ,YAAA,CAAgGK,2BAAA;YAA9EC,KAAK,EAAC;UAAM;YAnBtCJ,OAAA,EAAAC,QAAA,CAmBuC;cAAA,OAA8C,CAnBrFI,gBAAA,CAAAC,gBAAA,CAmB0CC,MAAA,CAAAW,MAAM,CAACX,MAAA,CAAAC,OAAO,CAACW,UAAU,gC;;YAnBnET,CAAA;;;QAAAA,CAAA;UAqBMZ,YAAA,CAEmBK,2BAAA;QAFDC,KAAK,EAAC;MAAI;QArBlCJ,OAAA,EAAAC,QAAA,CAsBQ;UAAA,OAAoD,CAApDJ,mBAAA,CAAoD;YAA/CH,KAAK,EAAC,SAAS;YAAC0B,SAAwB,EAAhBb,MAAA,CAAAC,OAAO,CAACa;kCAtB7CC,UAAA,E;;QAAAZ,CAAA;UAwBMZ,YAAA,CAEmBK,2BAAA;QAFDC,KAAK,EAAC;MAAI;QAxBlCJ,OAAA,EAAAC,QAAA,CAyBQ;UAAA,OAAoE,CAApEH,YAAA,CAAoEyB,0BAAA;YAAlDC,QAAQ,EAAEjB,MAAA,CAAAC,OAAO,CAACiB;;;QAzB5Cf,CAAA;UA2BMZ,YAAA,CAGmBI,2BAAA;QA9BzBF,OAAA,EAAAC,QAAA,CA4BQ;UAAA,OAA2E,CAA3EH,YAAA,CAA2EK,2BAAA;YAAzDC,KAAK,EAAC;UAAI;YA5BpCJ,OAAA,EAAAC,QAAA,CA4BqC;cAAA,OAA2B,CA5BhEI,gBAAA,CAAAC,gBAAA,CA4BwCC,MAAA,CAAAC,OAAO,CAACkB,aAAa,iB;;YA5B7DhB,CAAA;cA6BQZ,YAAA,CAAyFK,2BAAA;YAAvEC,KAAK,EAAC;UAAQ;YA7BxCJ,OAAA,EAAAC,QAAA,CA6ByC;cAAA,OAAqC,CA7B9EI,gBAAA,CAAAC,gBAAA,CA6B4CC,MAAA,CAAAC,OAAO,CAACmB,uBAAuB,iB;;YA7B3EjB,CAAA;;;QAAAA,CAAA;UA+BMZ,YAAA,CAGmBI,2BAAA;QAlCzBF,OAAA,EAAAC,QAAA,CAgCQ;UAAA,OAA8E,CAA9EH,YAAA,CAA8EK,2BAAA;YAA5DC,KAAK,EAAC;UAAM;YAhCtCJ,OAAA,EAAAC,QAAA,CAgCuC;cAAA,OAA4B,CAhCnEI,gBAAA,CAAAC,gBAAA,CAgC0CC,MAAA,CAAAC,OAAO,CAACoB,cAAc,iB;;YAhChElB,CAAA;cAiCQZ,YAAA,CAA4EK,2BAAA;YAA1DC,KAAK,EAAC;UAAM;YAjCtCJ,OAAA,EAAAC,QAAA,CAiCuC;cAAA,OAA0B,CAjCjEI,gBAAA,CAAAC,gBAAA,CAiC0CC,MAAA,CAAAC,OAAO,CAACqB,YAAY,iB;;YAjC9DnB,CAAA;;;QAAAA,CAAA;UAmCMZ,YAAA,CAEmBK,2BAAA;QAFDC,KAAK,EAAC;MAAM;QAnCpCJ,OAAA,EAAAC,QAAA,CAoCQ;UAAA,OAAyE,CAAzEH,YAAA,CAAyEyB,0BAAA;YAAvDC,QAAQ,EAAEjB,MAAA,CAAAC,OAAO,CAACsB;;;QApC5CpB,CAAA;UAsC2C,KAAK,I,cAA1CqB,YAAA,CAA4F5B,2BAAA;QAtClG6B,GAAA;QAsCwB5B,KAAK,EAAC;;QAtC9BJ,OAAA,EAAAC,QAAA,CAsCkD;UAAA,OAA6B,CAtC/EI,gBAAA,CAAAC,gBAAA,CAsCqDC,MAAA,CAAAC,OAAO,CAACyB,eAAe,iB;;QAtC5EvB,CAAA;YAAAwB,mBAAA,gBAuCMpC,YAAA,CAGmBI,2BAAA;QA1CzBF,OAAA,EAAAC,QAAA,CAwCQ;UAAA,OAA+F,CAA1D,KAAK,I,cAA1C8B,YAAA,CAA+F5B,2BAAA;YAxCvG6B,GAAA;YAwC0B5B,KAAK,EAAC;;YAxChCJ,OAAA,EAAAC,QAAA,CAwCoD;cAAA,OAAgC,CAxCpFI,gBAAA,CAAAC,gBAAA,CAwCuDC,MAAA,CAAAC,OAAO,CAAC2B,kBAAkB,iB;;YAxCjFzB,CAAA;gBAAAwB,mBAAA,gBAyCiD,KAAK,I,cAA9CH,YAAA,CAAqG5B,2BAAA;YAzC7G6B,GAAA;YAyC0B5B,KAAK,EAAC;;YAzChCJ,OAAA,EAAAC,QAAA,CAyCwD;cAAA,OAAkC,CAzC1FI,gBAAA,CAAAC,gBAAA,CAyC2DC,MAAA,CAAAC,OAAO,CAAC4B,oBAAoB,iB;;YAzCvF1B,CAAA;gBAAAwB,mBAAA,e;;QAAAxB,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}