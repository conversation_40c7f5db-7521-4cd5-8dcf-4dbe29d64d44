<template>
  <div
    class="TextRewrite"
    v-loading="loading"
    :element-loading-spinner="svg"
    :lement-loading-text="loadingText"
    element-loading-svg-view-box="-10, -10, 50, 50">
    <div class="TextRewriteHead">
      <div class="TextRewriteButton">
        <div class="TextRewriteButtonItem">
          <el-button type="primary" @click="handleImport">文档导入</el-button>
        </div>
        <div class="TextRewriteButtonItem"></div>
      </div>
      <div class="TextRewriteButton">
        <div class="TextRewriteButtonItem"></div>
        <div class="TextRewriteButtonItem">
          <el-button type="primary" @click="handleExportWord">导出</el-button>
        </div>
      </div>
    </div>
    <div class="TextRewriteBody">
      <div class="TextRewriteBodyLeft">
        <TinyMceEditor ref="wordRef" v-model="content" :setting="setting" :content_style="content_style" />
      </div>
      <div class="TextRewriteBodyRight"></div>
    </div>
  </div>
</template>
<script>
export default { name: 'TextRewrite' }
</script>

<script setup>
import api from '@/api'
import { ref } from 'vue'
import { useStore } from 'vuex'
import { setting, content_style, trigerUpload } from '../../AiToolBox/AiToolBox.js'
import { ElMessage } from 'element-plus'

const store = useStore()

const svg =
  '<path class="path" d="M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15" style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>'

const loading = ref(false)
const loadingText = ref('')
const wordRef = ref()
const content = ref('')

const handleImport = () => {
  trigerUpload().then((file) => {
    const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
    const isShow = ['doc', 'docx', 'wps'].includes(fileType)
    if (!isShow) return ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'wps'].join('、')}格式!` })
    loading.value = true
    fileWordUpload(file)
  })
}
const fileWordUpload = async (file) => {
  try {
    const param = new FormData()
    param.append('file', file)
    const { data } = await api.fileword2html(param)
    content.value = data
      .replace(/<\/?html[^>]*>/g, '')
      .replace(/<head\b[^<]*(?:(?!<\/head>)<[^<]*)*<\/head>/gi, '')
      .replace(/<\/?body[^>]*>/g, '')
      .replace(/<\/?div[^>]*>/g, '')
    loading.value = false
  } catch (err) {
    loading.value = false
  }
}
const handleExportWord = () => {
  store.commit('setExportWordHtmlObj', {
    code: 'exportWord',
    name: '智能纠错 --- 文档导出',
    key: 'content',
    data: { content: content.value }
  })
}
</script>
<style lang="scss">
.TextRewrite {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .TextRewriteHead {
    width: 100%;
    padding: var(--zy-distance-two) 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .TextRewriteButton {
      width: 796px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      & + .TextRewriteButton {
        width: calc(100% - 840px);
      }
      .TextRewriteButtonItem {
        display: flex;
        align-items: center;
      }
    }
  }
  .TextRewriteBody {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-two) * 2)));
    display: flex;
    justify-content: space-between;
    padding-bottom: var(--zy-distance-two);
    .TextRewriteBodyLeft {
      width: 820px;
      height: 100%;
      .TinyMceEditor {
        height: 100%;
        .tox-tinymce {
          border: none;
        }
        .tox-editor-header {
          width: 796px;
          border: 1px solid #ccc;
          border-bottom: none;
          margin: auto;
          margin-right: 30px;
        }
      }
    }
    .TextRewriteBodyRight {
      width: calc(100% - 840px);
      height: 100%;
      .globalTable {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
