{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport config from 'common/config';\nimport http_stream from 'common/http/stream.js';\nimport { ref, computed, nextTick, defineAsyncComponent } from 'vue';\nimport { size2Str } from 'common/js/utils.js';\nimport { globalFileLocation } from 'common/config/location.js';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'GlobalAiChatScroll'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    AiChatCode: {\n      type: String,\n      default: ''\n    },\n    chatId: {\n      type: String,\n      default: ''\n    },\n    chatName: {\n      type: String,\n      default: ''\n    },\n    promptWords: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    fileData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  emits: ['handlePromptWord', 'handleGuideWord', 'handleRetryMessage', 'handleStreamingCallback', 'handleSendMessageCallback', 'handleChatDialogueRefresh'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    var GlobalMarkdown = defineAsyncComponent(function () {\n      return import('common/components/global-markdown/global-markdown.vue');\n    });\n    var GlobalAiChart = defineAsyncComponent(function () {\n      return import('./GlobalAiChart.vue');\n    });\n    var GlobalAiChatData = defineAsyncComponent(function () {\n      return import('./GlobalAiChatData.vue');\n    });\n    var props = __props;\n    var emit = __emit;\n    var tipsIcon = '<svg t=\"1741241762761\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"7848\" width=\"14\" height=\"14\"><path d=\"M115.152 356.453c-1.492-9.942-2.486-18.724-3.314-25.849-0.829-7.125-0.995-14.251-0.995-21.541 0-22.867 3.314-38.112 9.611-45.237 6.463-7.125 15.41-10.77 27.01-10.77h198.343L410.596 5.001c40.266 2.818 67.109 8.285 80.863 16.239 13.753 7.954 20.546 17.564 20.546 28.998v15.079L460.141 252.89h239.438L766.522 4.836c40.266 2.817 67.108 8.285 80.862 16.238 13.753 7.954 20.547 17.565 20.547 28.998 0 5.8-0.829 10.771-2.154 15.079l-49.71 187.739h170.34c2.817 8.617 4.309 16.902 4.309 24.855v22.701c0 21.541-3.314 36.289-9.776 44.242-6.463 7.954-15.41 11.765-27.01 11.765H788.063L710.35 643.281h200.498c1.326 10.108 2.485 19.056 3.314 27.01a217.169 217.169 0 0 1 1.159 22.701c0 37.448-12.262 56.007-36.619 56.007H682.181l-73.24 269.595c-40.265-2.816-66.942-8.285-79.867-16.072-12.925-7.954-19.387-17.564-19.387-29.164 0-5.634 0.662-10.107 2.154-12.925l56.006-211.269H326.421l-71.086 269.597c-40.265-2.817-67.606-8.286-82.022-16.074-14.416-7.953-21.541-17.564-21.541-29.163 0-2.816 0.331-4.971 0.994-6.462 0.663-1.326 0.994-3.646 0.994-6.463l58.327-211.269H39.592c-2.817-10.107-4.308-19.056-4.308-27.009V699.62c0-21.541 3.314-36.289 9.776-44.242 6.463-7.954 15.41-11.765 27.01-11.765h168.186l75.394-286.829H115.152v-0.331z m239.272 286.828H595.85l77.714-286.828H432.138l-77.714 286.828z\" p-id=\"7849\"></path></svg>';\n    var loadingIcon = '<svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\"><path d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\" fill=\"#2c2c2c\" p-id=\"2363\"></path><path d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\" fill=\"#2c2c2c\" p-id=\"2364\"></path><path d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\" fill=\"#2c2c2c\" p-id=\"2365\"></path><path d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\" fill=\"#2c2c2c\" p-id=\"2366\"></path><path d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\" fill=\"#2c2c2c\" p-id=\"2367\"></path><path d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2368\"></path><path d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2369\"></path><path d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\" fill=\"#2c2c2c\" p-id=\"2370\"></path></svg>';\n    var ponderIcon = '<svg t=\"1741658991857\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"11203\" width=\"16\" height=\"16\"><path d=\"M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z\" p-id=\"11204\"></path><path d=\"M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z\" p-id=\"11205\"></path><path d=\"M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z\" p-id=\"11206\"></path></svg>';\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var scrollRef = ref();\n    var isScroll = ref(false);\n    var chatData = ref([]);\n    var chatTotal = ref(0);\n    var chatUpdate = ref(0);\n    var wrapScrollHeight = ref(0);\n    var promptWords = computed(function () {\n      return props.promptWords;\n    });\n    var elRefs = ref([]);\n    var getElRef = function getElRef(el, i) {\n      var index = (i + 1) / 2 - 1;\n      if (el) elRefs.value[index] = el;\n    };\n    var elRef = computed(function () {\n      return elRefs.value[elRefs.value.length - 1];\n    });\n    var elPonderRefs = ref([]);\n    var getElPonderRef = function getElPonderRef(el, i) {\n      var index = (i + 1) / 2 - 1;\n      if (el) elPonderRefs.value[index] = el;\n    };\n    var elPonderRef = computed(function () {\n      return elPonderRefs.value[elPonderRefs.value.length - 1];\n    });\n    var currentRequest = null;\n    var loading = ref(false);\n    var isStreaming = ref(false);\n    var startTime = null;\n    var endTime = null;\n    var dataName = ref('');\n    var dataInfo = ref({});\n    var dataShow = ref(false);\n    var fileIcon = function fileIcon(fileType) {\n      var IconClass = {\n        docx: 'globalFileWord',\n        doc: 'globalFileWord',\n        wps: 'globalFileWPS',\n        xlsx: 'globalFileExcel',\n        xls: 'globalFileExcel',\n        pdf: 'globalFilePDF',\n        pptx: 'globalFilePPT',\n        ppt: 'globalFilePPT',\n        txt: 'globalFileTXT',\n        jpg: 'globalFilePicture',\n        png: 'globalFilePicture',\n        gif: 'globalFilePicture',\n        avi: 'globalFileVideo',\n        mp4: 'globalFileVideo',\n        zip: 'globalFileCompress',\n        rar: 'globalFileCompress'\n      };\n      return IconClass[fileType] || 'globalFileUnknown';\n    };\n    var formatDuring = function formatDuring(mss) {\n      var days = parseInt(mss / (1000 * 60 * 60 * 24));\n      var hours = parseInt(mss % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n      var minutes = parseInt(mss % (1000 * 60 * 60) / (1000 * 60));\n      var seconds = mss % (1000 * 60) / 1000;\n      var time = '';\n      if (days > 0) time += `${days} 天 `;\n      if (hours > 0) time += `${hours} 小时 `;\n      if (minutes > 0) time += `${minutes} 分钟 `;\n      if (seconds > 0) time += `${seconds} 秒 `;\n      return time;\n    };\n    var stringToJson = function stringToJson(str) {\n      // 替换属性名\n      str = str.replace(/(\\w+):/g, '\"$1\":');\n      // 替换单引号为双引号\n      str = str.replace(/'/g, '\"');\n      var obj = JSON.parse(str);\n      return obj;\n    };\n    var handleNewChat = function handleNewChat() {\n      elRefs.value = [];\n      elPonderRefs.value = [];\n      chatData.value = [];\n      chatTotal.value = 0;\n      handleStopMessage();\n    };\n    var handleOldChat = function handleOldChat() {\n      elRefs.value = [];\n      elPonderRefs.value = [];\n      chatData.value = [];\n      chatTotal.value = 0;\n      handleStopMessage();\n      handleChatMessage();\n    };\n    var handleNewChatMessage = function handleNewChatMessage(item) {\n      var oneData = {\n        id: guid(),\n        type: true,\n        ponderShow: true,\n        isControls: true,\n        content: item.question,\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: props.fileData,\n        chartData: []\n      };\n      var twoData = {\n        id: guid(),\n        type: false,\n        ponderShow: true,\n        isControls: false,\n        content: '',\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: [],\n        chartData: [],\n        guideWord: []\n      };\n      chatData.value.push(oneData);\n      chatData.value.push(twoData);\n    };\n    var handleOldChatMessage = function handleOldChatMessage(item, type) {\n      var oneData = {\n        id: guid(),\n        type: true,\n        ponderShow: true,\n        isControls: true,\n        content: item.userQuestion,\n        contentOld: '',\n        ponderContent: '',\n        ponderContentOld: '',\n        time: '',\n        dataList: [],\n        fileData: item.attachments,\n        chartData: [],\n        guideWord: []\n      };\n      var twoData = {\n        id: guid(),\n        type: false,\n        ponderShow: true,\n        isControls: true,\n        content: '',\n        contentOld: item.answer,\n        ponderContent: '',\n        ponderContentOld: item.reasoning,\n        time: item.reasoning ? '1' : '',\n        dataList: [],\n        fileData: [],\n        chartData: [],\n        guideWord: []\n      };\n      if (type) {\n        chatData.value.unshift(twoData);\n        chatData.value.unshift(oneData);\n      } else {\n        chatData.value.push(oneData);\n        chatData.value.push(twoData);\n      }\n    };\n    var handleChatMessage = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(value) {\n        var _yield$api$aigptChatL, data, total, updateVal, index, item, _index, _item;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.aigptChatLogsList({\n                pageNo: value || 1,\n                pageSize: value ? 1 : 10,\n                isAsc: 1,\n                query: {\n                  chatId: props.chatId\n                }\n              });\n            case 2:\n              _yield$api$aigptChatL = _context.sent;\n              data = _yield$api$aigptChatL.data;\n              total = _yield$api$aigptChatL.total;\n              chatTotal.value = total;\n              if (value) {\n                updateVal = 0;\n                for (index = 0; index < data.length; index++) {\n                  item = data[index];\n                  if (item.reasoning) updateVal += 1;\n                  if (item.answer) updateVal += 1;\n                  handleOldChatMessage(item, true);\n                }\n                chatUpdate.value = updateVal;\n                nextTick(function () {\n                  scrollElHeight();\n                });\n              } else {\n                for (_index = 0; _index < data.length; _index++) {\n                  _item = data[_index];\n                  handleOldChatMessage(_item, false);\n                }\n                isScroll.value = false;\n                nextTick(function () {\n                  scrollDown();\n                });\n              }\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function handleChatMessage(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleCloseMessage = function handleCloseMessage() {\n      currentRequest = null;\n      loading.value = false;\n      isStreaming.value = false;\n      emit('handleStreamingCallback', false);\n    };\n    var handleStopMessage = function handleStopMessage() {\n      if (currentRequest) {\n        currentRequest.abort();\n        loading.value = false;\n        isStreaming.value = false;\n        emit('handleStreamingCallback', false);\n        console.log('启动流式接口停止');\n      }\n      handleCloseMessage();\n    };\n    var handleUpdate = function handleUpdate() {\n      if (!isScroll.value && wrapScrollHeight.value) {\n        nextTick(function () {\n          scrollElHeight();\n        });\n        chatUpdate.value = chatUpdate.value - 1;\n      } else {\n        chatUpdate.value = 0;\n        nextTick(function () {\n          scrollDown();\n        });\n      }\n    };\n    var handleScroll = function handleScroll(_ref3) {\n      var scrollTop = _ref3.scrollTop;\n      if (scrollTop === 0) {\n        wrapScrollHeight.value = scrollRef.value.wrapRef.scrollHeight;\n        if (chatTotal.value && chatTotal.value > chatData.value.length / 2) {\n          handleChatMessage(chatData.value.length / 2 + 1);\n        }\n      }\n      var _scrollRef$value$wrap = scrollRef.value.wrapRef,\n        scrollHeight = _scrollRef$value$wrap.scrollHeight,\n        clientHeight = _scrollRef$value$wrap.clientHeight;\n      if (scrollHeight - scrollTop <= clientHeight + 52) {\n        isScroll.value = false;\n      } else {\n        isScroll.value = true;\n      }\n    };\n    var scrollDown = function scrollDown() {\n      if (isScroll.value) return;\n      scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight;\n    };\n    var scrollElHeight = function scrollElHeight() {\n      scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - wrapScrollHeight.value;\n    };\n    var handlePreview = function handlePreview(row) {\n      globalFileLocation({\n        name: process.env.VUE_APP_NAME,\n        fileId: row.id,\n        fileType: row.extName,\n        fileName: row.originalFileName,\n        fileSize: row.fileSize\n      });\n    };\n    var handleLinkClick = function handleLinkClick(_ref4) {\n      var href = _ref4.href,\n        text = _ref4.text,\n        event = _ref4.event;\n      console.log('链接被点击:', href, text);\n      if (text === '查看原文比对') {\n        var token = sessionStorage.getItem('token') || '';\n        window.open(`${config.mainPath}VersionComparisonAi?chatId=${props.chatId}&token=${token}`, '_blank');\n      } else {\n        window.open(href, '_blank');\n      }\n    };\n    var handleDataList = function handleDataList(row) {\n      dataName.value = row.sourceName;\n      dataInfo.value = row;\n      dataShow.value = true;\n    };\n    var handleCopyMessage = function handleCopyMessage(content, i) {\n      var _elRefs$value$index;\n      var index = (i + 1) / 2 - 1;\n      var copyContent = ((_elRefs$value$index = elRefs.value[index]) === null || _elRefs$value$index === void 0 || (_elRefs$value$index = _elRefs$value$index.elRef) === null || _elRefs$value$index === void 0 ? void 0 : _elRefs$value$index.innerText) || content;\n      var textarea = document.createElement('textarea');\n      textarea.readOnly = 'readonly';\n      textarea.style.position = 'absolute';\n      textarea.style.left = '-9999px';\n      textarea.value = copyContent;\n      document.body.appendChild(textarea);\n      textarea.select();\n      var result = document.execCommand('Copy');\n      if (result) {\n        ElMessage({\n          message: '复制成功',\n          type: 'success'\n        });\n      }\n      document.body.removeChild(textarea);\n    };\n    var handleRetryMessage = function handleRetryMessage(item, index) {\n      var length = chatData.value.length - 1;\n      if (index === length && currentRequest) {\n        loading.value = true;\n        isStreaming.value = true;\n        emit('handleStreamingCallback', true);\n        chatData.value[length] = {\n          id: guid(),\n          type: false,\n          ponderShow: true,\n          isControls: false,\n          content: '',\n          contentOld: '',\n          ponderContent: '',\n          ponderContentOld: '',\n          time: '',\n          dataList: [],\n          fileData: [],\n          chartData: [],\n          guideWord: []\n        };\n        nextTick(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n          return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n            while (1) switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.next = 2;\n                return currentRequest.retry();\n              case 2:\n              case \"end\":\n                return _context2.stop();\n            }\n          }, _callee2);\n        })));\n      } else {\n        isScroll.value = false;\n        emit('handleRetryMessage', chatData.value[index - 1]);\n      }\n    };\n    var handlePromptWord = function handlePromptWord(data) {\n      emit('handlePromptWord', data);\n    };\n    var handleGuideWord = function handleGuideWord(data) {\n      emit('handleGuideWord', data);\n    };\n    var handleSendMessage = function handleSendMessage(value, params) {\n      if (isStreaming.value) return ElMessage({\n        type: 'warning',\n        message: '请先完成上一次对话再进行新对话！'\n      });\n      handleStopMessage();\n      loading.value = true;\n      isScroll.value = false;\n      handleNewChatMessage({\n        question: value\n      });\n      nextTick(function () {\n        scrollDown();\n        handleHttpStream(params);\n        emit('handleSendMessageCallback');\n      });\n    };\n    var handleHttpStream = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var params,\n          data,\n          quoteList,\n          newChartData,\n          newGuideWord,\n          index,\n          item,\n          _item$quoteList,\n          i,\n          row,\n          _choice$,\n          _elRef$value,\n          choice,\n          details,\n          _elPonderRef$value,\n          executionTime,\n          _elRef$value2,\n          _elRef$value3,\n          _args3 = arguments;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              params = _args3.length > 0 && _args3[0] !== undefined ? _args3[0] : {};\n              isStreaming.value = true;\n              emit('handleStreamingCallback', true);\n              _context3.prev = 3;\n              startTime = new Date();\n              currentRequest = http_stream('/aigpt/chatStream', {\n                body: JSON.stringify(_objectSpread({\n                  chatBusinessScene: props.AiChatCode,\n                  chatId: props.chatId\n                }, params)),\n                onMessage(event) {\n                  // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\n                  loading.value = false;\n                  if (event.data !== '[DONE]') {\n                    data = JSON.parse(event.data);\n                    if (Array.isArray(data)) {\n                      // console.log('[]', data)\n                      quoteList = [];\n                      newChartData = [];\n                      newGuideWord = [];\n                      for (index = 0; index < data.length; index++) {\n                        item = data[index];\n                        if (typeof item === 'string') {\n                          newGuideWord.push(_objectSpread(_objectSpread({}, params), {}, {\n                            question: item\n                          }));\n                        } else {\n                          if (item !== null && item !== void 0 && (_item$quoteList = item.quoteList) !== null && _item$quoteList !== void 0 && _item$quoteList.length) {\n                            for (i = 0; i < (item === null || item === void 0 ? void 0 : item.quoteList.length); i++) {\n                              row = item === null || item === void 0 ? void 0 : item.quoteList[i];\n                              quoteList.push(_objectSpread(_objectSpread({}, row), {}, {\n                                markdownContent: row.q\n                              }));\n                            }\n                          }\n                          if (item !== null && item !== void 0 && item.echartItem) {\n                            newChartData.push(stringToJson(item === null || item === void 0 ? void 0 : item.echartItem));\n                          }\n                        }\n                      }\n                      chatData.value[chatData.value.length - 1].dataList = quoteList;\n                      chatData.value[chatData.value.length - 1].chartData = newChartData;\n                      chatData.value[chatData.value.length - 1].guideWord = newGuideWord;\n                    } else {\n                      // console.log('{}', data)\n                      choice = (data === null || data === void 0 ? void 0 : data.choices) || [{}];\n                      details = ((_choice$ = choice[0]) === null || _choice$ === void 0 ? void 0 : _choice$.delta) || {};\n                      if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\n                        (_elPonderRef$value = elPonderRef.value) === null || _elPonderRef$value === void 0 || _elPonderRef$value.enqueueRender(details.reasoning_content || '');\n                        if (chatData.value[chatData.value.length - 1].time) {\n                          startTime = null;\n                          endTime = null;\n                        } else {\n                          endTime = new Date();\n                          executionTime = endTime - startTime;\n                          chatData.value[chatData.value.length - 1].time = formatDuring(executionTime);\n                        }\n                      }\n                      if (Object.prototype.hasOwnProperty.call(details, 'content')) (_elRef$value = elRef.value) === null || _elRef$value === void 0 || _elRef$value.enqueueRender(details.content || '');\n                      nextTick(function () {\n                        scrollDown();\n                      });\n                    }\n                  } else {\n                    // console.log(event.data)\n                    (_elRef$value2 = elRef.value) === null || _elRef$value2 === void 0 || _elRef$value2.enqueueRender('');\n                    nextTick(function () {\n                      scrollDown();\n                    });\n                  }\n                },\n                onError(err) {\n                  console.log('流式接口错误:', err);\n                },\n                onClose() {\n                  loading.value = false;\n                  isStreaming.value = false;\n                  emit('handleStreamingCallback', false);\n                  chatData.value[chatData.value.length - 1].isControls = true;\n                  emit('handleChatDialogueRefresh');\n                  console.log('流式接口关闭');\n                }\n              });\n              _context3.next = 8;\n              return currentRequest.promise;\n            case 8:\n              _context3.next = 18;\n              break;\n            case 10:\n              _context3.prev = 10;\n              _context3.t0 = _context3[\"catch\"](3);\n              loading.value = false;\n              isStreaming.value = false;\n              emit('handleStreamingCallback', false);\n              (_elRef$value3 = elRef.value) === null || _elRef$value3 === void 0 || _elRef$value3.enqueueRender('服务器繁忙，请稍后再试。');\n              nextTick(function () {\n                scrollDown();\n              });\n              console.error('启动流式接口失败:', _context3.t0);\n            case 18:\n              _context3.prev = 18;\n              return _context3.finish(18);\n            case 20:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[3, 10, 18, 20]]);\n      }));\n      return function handleHttpStream() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    __expose({\n      handleNewChat,\n      handleOldChat,\n      handleSendMessage,\n      handleStopMessage\n    });\n    var __returned__ = {\n      GlobalMarkdown,\n      GlobalAiChart,\n      GlobalAiChatData,\n      props,\n      emit,\n      tipsIcon,\n      loadingIcon,\n      ponderIcon,\n      guid,\n      scrollRef,\n      isScroll,\n      chatData,\n      chatTotal,\n      chatUpdate,\n      wrapScrollHeight,\n      promptWords,\n      elRefs,\n      getElRef,\n      elRef,\n      elPonderRefs,\n      getElPonderRef,\n      elPonderRef,\n      get currentRequest() {\n        return currentRequest;\n      },\n      set currentRequest(v) {\n        currentRequest = v;\n      },\n      loading,\n      isStreaming,\n      get startTime() {\n        return startTime;\n      },\n      set startTime(v) {\n        startTime = v;\n      },\n      get endTime() {\n        return endTime;\n      },\n      set endTime(v) {\n        endTime = v;\n      },\n      dataName,\n      dataInfo,\n      dataShow,\n      fileIcon,\n      formatDuring,\n      stringToJson,\n      handleNewChat,\n      handleOldChat,\n      handleNewChatMessage,\n      handleOldChatMessage,\n      handleChatMessage,\n      handleCloseMessage,\n      handleStopMessage,\n      handleUpdate,\n      handleScroll,\n      scrollDown,\n      scrollElHeight,\n      handlePreview,\n      handleLinkClick,\n      handleDataList,\n      handleCopyMessage,\n      handleRetryMessage,\n      handlePromptWord,\n      handleGuideWord,\n      handleSendMessage,\n      handleHttpStream,\n      get api() {\n        return api;\n      },\n      get config() {\n        return config;\n      },\n      get http_stream() {\n        return http_stream;\n      },\n      ref,\n      computed,\n      nextTick,\n      defineAsyncComponent,\n      get size2Str() {\n        return size2Str;\n      },\n      get globalFileLocation() {\n        return globalFileLocation;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "config", "http_stream", "ref", "computed", "nextTick", "defineAsyncComponent", "size2Str", "globalFileLocation", "ElMessage", "__default__", "GlobalMarkdown", "GlobalAiChart", "GlobalAiChatData", "props", "__props", "emit", "__emit", "tipsIcon", "loadingIcon", "ponderIcon", "guid", "replace", "Math", "random", "toString", "scrollRef", "isScroll", "chatData", "chatTotal", "chatUpdate", "wrapScrollHeight", "promptWords", "elRefs", "getElRef", "el", "index", "elRef", "elPonderRefs", "getElPonderRef", "elPonderRef", "currentRequest", "loading", "isStreaming", "startTime", "endTime", "dataName", "dataInfo", "dataShow", "fileIcon", "fileType", "IconClass", "docx", "doc", "wps", "xlsx", "xls", "pdf", "pptx", "ppt", "txt", "jpg", "png", "gif", "avi", "mp4", "zip", "rar", "formatDuring", "mss", "days", "parseInt", "hours", "minutes", "seconds", "time", "string<PERSON><PERSON><PERSON><PERSON>", "str", "obj", "JSON", "parse", "handleNewChat", "handleStopMessage", "handleOldChat", "handleChatMessage", "handleNewChatMessage", "item", "oneData", "id", "ponderShow", "isControls", "content", "question", "contentOld", "ponder<PERSON><PERSON>nt", "ponderContentOld", "dataList", "fileData", "chartData", "twoData", "guideWord", "handleOldChatMessage", "userQuestion", "attachments", "answer", "reasoning", "unshift", "_ref2", "_callee", "_yield$api$aigptChatL", "data", "total", "updateVal", "_index", "_item", "_callee$", "_context", "aigptChatLogsList", "pageNo", "pageSize", "isAsc", "query", "chatId", "scrollElHeight", "scrollDown", "_x", "handleCloseMessage", "abort", "console", "log", "handleUpdate", "handleScroll", "_ref3", "scrollTop", "wrapRef", "scrollHeight", "_scrollRef$value$wrap", "clientHeight", "handlePreview", "row", "process", "env", "VUE_APP_NAME", "fileId", "extName", "fileName", "originalFileName", "fileSize", "handleLinkClick", "_ref4", "href", "text", "event", "token", "sessionStorage", "getItem", "window", "open", "mainP<PERSON>", "handleDataList", "sourceName", "handleCopyMessage", "_elRefs$value$index", "copyContent", "innerText", "textarea", "document", "createElement", "readOnly", "style", "position", "left", "body", "append<PERSON><PERSON><PERSON>", "select", "result", "execCommand", "message", "<PERSON><PERSON><PERSON><PERSON>", "handleRetryMessage", "_callee2", "_callee2$", "_context2", "retry", "handlePromptWord", "handleGuideWord", "handleSendMessage", "params", "handleHttpStream", "_ref6", "_callee3", "quoteList", "newChartData", "newGuideWord", "_item$quoteList", "_choice$", "_elRef$value", "choice", "details", "_elPonderRef$value", "executionTime", "_elRef$value2", "_elRef$value3", "_args3", "_callee3$", "_context3", "undefined", "Date", "stringify", "_objectSpread", "chatBusinessScene", "AiChatCode", "onMessage", "Array", "isArray", "markdownContent", "q", "echartItem", "choices", "delta", "enqueueRender", "reasoning_content", "onError", "err", "onClose", "promise", "t0", "error", "__expose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalAiChat/GlobalAiChatScroll.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always ref=\"scrollRef\" class=\"GlobalAiChatScroll\" @scroll=\"handleScroll\">\r\n    <div class=\"GlobalAiChatBody\">\r\n      <div class=\"GlobalAiChatBodyTipsBody\" v-if=\"promptWords.length\">\r\n        <div class=\"GlobalAiChatBodyTipsVice\">您可以试着问我：</div>\r\n        <div class=\"GlobalAiChatBodyTipsItem\" v-for=\"item in promptWords\" :key=\"item.id\">\r\n          <div class=\"GlobalAiChatBodyTips\" @click=\"handlePromptWord(item)\">\r\n            <span v-html=\"tipsIcon\"></span>\r\n            {{ item.promptWord }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        :class=\"[item.type ? 'GlobalAiChatSelfMessage' : 'GlobalAiChatMessage']\"\r\n        v-for=\"(item, index) in chatData\"\r\n        :key=\"item.id\">\r\n        <template v-if=\"item.type\">\r\n          <div\r\n            class=\"GlobalAiChatSelfMessageFile\"\r\n            v-for=\"item in item.fileData\"\r\n            :key=\"item.id\"\r\n            @click=\"handlePreview(item)\">\r\n            <div class=\"globalFileIcon\" :class=\"fileIcon(item?.extName)\"></div>\r\n            <div class=\"GlobalChatMessagesFileName ellipsis\">{{ item?.originalFileName || '未知文件' }}</div>\r\n            <div class=\"GlobalChatMessagesFileSize\">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>\r\n          </div>\r\n          <div class=\"GlobalAiChatSelfMessageInfo\">{{ item.content }}</div>\r\n        </template>\r\n        <template v-if=\"!item.type\">\r\n          <!-- <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" /> -->\r\n          <div class=\"GlobalAiChatMessageInfo\">\r\n            <div\r\n              class=\"GlobalAiChatMessagePonder forbidSelect\"\r\n              @click=\"item.ponderShow = !item.ponderShow\"\r\n              v-if=\"item.time\">\r\n              <div v-html=\"ponderIcon\"></div>\r\n              已深度思考\r\n              <span v-if=\"item.time !== '1'\">（用时 {{ item.time }}）</span>\r\n              <el-icon>\r\n                <ArrowUpBold v-if=\"item.ponderShow\" />\r\n                <ArrowDownBold v-if=\"!item.ponderShow\" />\r\n              </el-icon>\r\n            </div>\r\n            <div class=\"GlobalAiChatMessagePonderContent\" v-show=\"item.ponderShow\">\r\n              <GlobalMarkdown\r\n                :ref=\"(el) => getElPonderRef(el, index)\"\r\n                v-model=\"item.ponderContent\"\r\n                :content=\"item.ponderContentOld\"\r\n                @update=\"handleUpdate\" />\r\n            </div>\r\n            <div class=\"GlobalAiChatMessageContent\">\r\n              <GlobalMarkdown\r\n                :ref=\"(el) => getElRef(el, index)\"\r\n                v-model=\"item.content\"\r\n                :content=\"item.contentOld\"\r\n                :on-link-click=\"handleLinkClick\"\r\n                @update=\"handleUpdate\" />\r\n              <div class=\"GlobalAiChatMessageDataList\">\r\n                <div class=\"GlobalAiChatMessageDataItem\" v-for=\"row in item.dataList\" @click=\"handleDataList(row)\">\r\n                  {{ row.sourceName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"GlobalAiChatMessageChart\">\r\n                <GlobalAiChart v-for=\"(row, i) in item.chartData\" :key=\"i + 'chartData'\" :option=\"row\" />\r\n              </div>\r\n              <div class=\"GlobalAiChatMessageLoading\" v-if=\"index === chatData.length - 1 && loading\">\r\n                <div class=\"answerLoading\" v-html=\"loadingIcon\"></div>\r\n              </div>\r\n              <div class=\"GlobalAiChatBodyTipsVice\" v-if=\"item.guideWord.length\">您是不是想问：</div>\r\n              <div class=\"GlobalAiChatBodyTipsItem\" v-for=\"(row, i) in item.guideWord\" :key=\"i + 'guideWord'\">\r\n                <div class=\"GlobalAiChatBodyTips\" @click=\"handleGuideWord(row)\">\r\n                  <span v-html=\"tipsIcon\"></span>\r\n                  {{ row.question }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"GlobalAiChatMessageControls\" v-if=\"item.isControls\">\r\n              <el-tooltip content=\"复制\" placement=\"top\">\r\n                <div class=\"GlobalAiChatMessageControlsItem\" @click=\"handleCopyMessage(item.content, index)\">\r\n                  <el-icon>\r\n                    <CopyDocument />\r\n                  </el-icon>\r\n                </div>\r\n              </el-tooltip>\r\n              <el-tooltip content=\"重新生成\" placement=\"top\">\r\n                <div class=\"GlobalAiChatMessageControlsItem\" @click=\"handleRetryMessage(item, index)\">\r\n                  <el-icon>\r\n                    <Refresh />\r\n                  </el-icon>\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n  </el-scrollbar>\r\n  <xyl-popup-window v-model=\"dataShow\" :name=\"dataName\">\r\n    <GlobalAiChatData :data=\"dataInfo\"></GlobalAiChatData>\r\n  </xyl-popup-window>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChatScroll' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport config from 'common/config'\r\nimport http_stream from 'common/http/stream.js'\r\nimport { ref, computed, nextTick, defineAsyncComponent } from 'vue'\r\nimport { size2Str } from 'common/js/utils.js'\r\nimport { globalFileLocation } from 'common/config/location.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst GlobalMarkdown = defineAsyncComponent(() => import('common/components/global-markdown/global-markdown.vue'))\r\nconst GlobalAiChart = defineAsyncComponent(() => import('./GlobalAiChart.vue'))\r\nconst GlobalAiChatData = defineAsyncComponent(() => import('./GlobalAiChatData.vue'))\r\nconst props = defineProps({\r\n  AiChatCode: { type: String, default: '' },\r\n  chatId: { type: String, default: '' },\r\n  chatName: { type: String, default: '' },\r\n  promptWords: { type: Array, default: () => [] },\r\n  fileData: { type: Array, default: () => [] }\r\n})\r\nconst emit = defineEmits([\r\n  'handlePromptWord',\r\n  'handleGuideWord',\r\n  'handleRetryMessage',\r\n  'handleStreamingCallback',\r\n  'handleSendMessageCallback',\r\n  'handleChatDialogueRefresh'\r\n])\r\nconst tipsIcon =\r\n  '<svg t=\"1741241762761\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"7848\" width=\"14\" height=\"14\"><path d=\"M115.152 356.453c-1.492-9.942-2.486-18.724-3.314-25.849-0.829-7.125-0.995-14.251-0.995-21.541 0-22.867 3.314-38.112 9.611-45.237 6.463-7.125 15.41-10.77 27.01-10.77h198.343L410.596 5.001c40.266 2.818 67.109 8.285 80.863 16.239 13.753 7.954 20.546 17.564 20.546 28.998v15.079L460.141 252.89h239.438L766.522 4.836c40.266 2.817 67.108 8.285 80.862 16.238 13.753 7.954 20.547 17.565 20.547 28.998 0 5.8-0.829 10.771-2.154 15.079l-49.71 187.739h170.34c2.817 8.617 4.309 16.902 4.309 24.855v22.701c0 21.541-3.314 36.289-9.776 44.242-6.463 7.954-15.41 11.765-27.01 11.765H788.063L710.35 643.281h200.498c1.326 10.108 2.485 19.056 3.314 27.01a217.169 217.169 0 0 1 1.159 22.701c0 37.448-12.262 56.007-36.619 56.007H682.181l-73.24 269.595c-40.265-2.816-66.942-8.285-79.867-16.072-12.925-7.954-19.387-17.564-19.387-29.164 0-5.634 0.662-10.107 2.154-12.925l56.006-211.269H326.421l-71.086 269.597c-40.265-2.817-67.606-8.286-82.022-16.074-14.416-7.953-21.541-17.564-21.541-29.163 0-2.816 0.331-4.971 0.994-6.462 0.663-1.326 0.994-3.646 0.994-6.463l58.327-211.269H39.592c-2.817-10.107-4.308-19.056-4.308-27.009V699.62c0-21.541 3.314-36.289 9.776-44.242 6.463-7.954 15.41-11.765 27.01-11.765h168.186l75.394-286.829H115.152v-0.331z m239.272 286.828H595.85l77.714-286.828H432.138l-77.714 286.828z\" p-id=\"7849\"></path></svg>'\r\nconst loadingIcon =\r\n  '<svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\"><path d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\" fill=\"#2c2c2c\" p-id=\"2363\"></path><path d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\" fill=\"#2c2c2c\" p-id=\"2364\"></path><path d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\" fill=\"#2c2c2c\" p-id=\"2365\"></path><path d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\" fill=\"#2c2c2c\" p-id=\"2366\"></path><path d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\" fill=\"#2c2c2c\" p-id=\"2367\"></path><path d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2368\"></path><path d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2369\"></path><path d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\" fill=\"#2c2c2c\" p-id=\"2370\"></path></svg>'\r\nconst ponderIcon =\r\n  '<svg t=\"1741658991857\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"11203\" width=\"16\" height=\"16\"><path d=\"M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z\" p-id=\"11204\"></path><path d=\"M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z\" p-id=\"11205\"></path><path d=\"M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z\" p-id=\"11206\"></path></svg>'\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\n\r\nconst scrollRef = ref()\r\nconst isScroll = ref(false)\r\nconst chatData = ref([])\r\nconst chatTotal = ref(0)\r\nconst chatUpdate = ref(0)\r\nconst wrapScrollHeight = ref(0)\r\n\r\nconst promptWords = computed(() => props.promptWords)\r\nconst elRefs = ref([])\r\nconst getElRef = (el, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  if (el) elRefs.value[index] = el\r\n}\r\nconst elRef = computed(() => elRefs.value[elRefs.value.length - 1])\r\nconst elPonderRefs = ref([])\r\nconst getElPonderRef = (el, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  if (el) elPonderRefs.value[index] = el\r\n}\r\nconst elPonderRef = computed(() => elPonderRefs.value[elPonderRefs.value.length - 1])\r\n\r\nlet currentRequest = null\r\nconst loading = ref(false)\r\nconst isStreaming = ref(false)\r\nlet startTime = null\r\nlet endTime = null\r\n\r\nconst dataName = ref('')\r\nconst dataInfo = ref({})\r\nconst dataShow = ref(false)\r\n\r\nconst fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nconst formatDuring = (mss) => {\r\n  const days = parseInt(mss / (1000 * 60 * 60 * 24))\r\n  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))\r\n  const seconds = (mss % (1000 * 60)) / 1000\r\n  var time = ''\r\n  if (days > 0) time += `${days} 天 `\r\n  if (hours > 0) time += `${hours} 小时 `\r\n  if (minutes > 0) time += `${minutes} 分钟 `\r\n  if (seconds > 0) time += `${seconds} 秒 `\r\n  return time\r\n}\r\nconst stringToJson = (str) => {\r\n  // 替换属性名\r\n  str = str.replace(/(\\w+):/g, '\"$1\":')\r\n  // 替换单引号为双引号\r\n  str = str.replace(/'/g, '\"')\r\n  const obj = JSON.parse(str)\r\n  return obj\r\n}\r\nconst handleNewChat = () => {\r\n  elRefs.value = []\r\n  elPonderRefs.value = []\r\n  chatData.value = []\r\n  chatTotal.value = 0\r\n  handleStopMessage()\r\n}\r\nconst handleOldChat = () => {\r\n  elRefs.value = []\r\n  elPonderRefs.value = []\r\n  chatData.value = []\r\n  chatTotal.value = 0\r\n  handleStopMessage()\r\n  handleChatMessage()\r\n}\r\nconst handleNewChatMessage = (item) => {\r\n  const oneData = {\r\n    id: guid(),\r\n    type: true,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: item.question,\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: props.fileData,\r\n    chartData: []\r\n  }\r\n  const twoData = {\r\n    id: guid(),\r\n    type: false,\r\n    ponderShow: true,\r\n    isControls: false,\r\n    content: '',\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: [],\r\n    chartData: [],\r\n    guideWord: []\r\n  }\r\n  chatData.value.push(oneData)\r\n  chatData.value.push(twoData)\r\n}\r\nconst handleOldChatMessage = (item, type) => {\r\n  const oneData = {\r\n    id: guid(),\r\n    type: true,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: item.userQuestion,\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: item.attachments,\r\n    chartData: [],\r\n    guideWord: []\r\n  }\r\n  const twoData = {\r\n    id: guid(),\r\n    type: false,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: '',\r\n    contentOld: item.answer,\r\n    ponderContent: '',\r\n    ponderContentOld: item.reasoning,\r\n    time: item.reasoning ? '1' : '',\r\n    dataList: [],\r\n    fileData: [],\r\n    chartData: [],\r\n    guideWord: []\r\n  }\r\n  if (type) {\r\n    chatData.value.unshift(twoData)\r\n    chatData.value.unshift(oneData)\r\n  } else {\r\n    chatData.value.push(oneData)\r\n    chatData.value.push(twoData)\r\n  }\r\n}\r\n\r\nconst handleChatMessage = async (value) => {\r\n  const { data, total } = await api.aigptChatLogsList({\r\n    pageNo: value || 1,\r\n    pageSize: value ? 1 : 10,\r\n    isAsc: 1,\r\n    query: { chatId: props.chatId }\r\n  })\r\n  chatTotal.value = total\r\n  if (value) {\r\n    let updateVal = 0\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      if (item.reasoning) updateVal += 1\r\n      if (item.answer) updateVal += 1\r\n      handleOldChatMessage(item, true)\r\n    }\r\n    chatUpdate.value = updateVal\r\n    nextTick(() => {\r\n      scrollElHeight()\r\n    })\r\n  } else {\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      handleOldChatMessage(item, false)\r\n    }\r\n    isScroll.value = false\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n  }\r\n}\r\nconst handleCloseMessage = () => {\r\n  currentRequest = null\r\n  loading.value = false\r\n  isStreaming.value = false\r\n  emit('handleStreamingCallback', false)\r\n}\r\nconst handleStopMessage = () => {\r\n  if (currentRequest) {\r\n    currentRequest.abort()\r\n    loading.value = false\r\n    isStreaming.value = false\r\n    emit('handleStreamingCallback', false)\r\n    console.log('启动流式接口停止')\r\n  }\r\n  handleCloseMessage()\r\n}\r\n\r\nconst handleUpdate = () => {\r\n  if (!isScroll.value && wrapScrollHeight.value) {\r\n    nextTick(() => {\r\n      scrollElHeight()\r\n    })\r\n    chatUpdate.value = chatUpdate.value - 1\r\n  } else {\r\n    chatUpdate.value = 0\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n  }\r\n}\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (scrollTop === 0) {\r\n    wrapScrollHeight.value = scrollRef.value.wrapRef.scrollHeight\r\n    if (chatTotal.value && chatTotal.value > chatData.value.length / 2) {\r\n      handleChatMessage(chatData.value.length / 2 + 1)\r\n    }\r\n  }\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 52) {\r\n    isScroll.value = false\r\n  } else {\r\n    isScroll.value = true\r\n  }\r\n}\r\nconst scrollDown = () => {\r\n  if (isScroll.value) return\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight\r\n}\r\nconst scrollElHeight = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - wrapScrollHeight.value\r\n}\r\nconst handlePreview = (row) => {\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nconst handleLinkClick = ({ href, text, event }) => {\r\n  console.log('链接被点击:', href, text)\r\n  if (text === '查看原文比对') {\r\n    const token = sessionStorage.getItem('token') || ''\r\n    window.open(`${config.mainPath}VersionComparisonAi?chatId=${props.chatId}&token=${token}`, '_blank')\r\n  } else {\r\n    window.open(href, '_blank')\r\n  }\r\n}\r\nconst handleDataList = (row) => {\r\n  dataName.value = row.sourceName\r\n  dataInfo.value = row\r\n  dataShow.value = true\r\n}\r\nconst handleCopyMessage = (content, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  const copyContent = elRefs.value[index]?.elRef?.innerText || content\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = copyContent\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) {\r\n    ElMessage({ message: '复制成功', type: 'success' })\r\n  }\r\n  document.body.removeChild(textarea)\r\n}\r\nconst handleRetryMessage = (item, index) => {\r\n  const length = chatData.value.length - 1\r\n  if (index === length && currentRequest) {\r\n    loading.value = true\r\n    isStreaming.value = true\r\n    emit('handleStreamingCallback', true)\r\n    chatData.value[length] = {\r\n      id: guid(),\r\n      type: false,\r\n      ponderShow: true,\r\n      isControls: false,\r\n      content: '',\r\n      contentOld: '',\r\n      ponderContent: '',\r\n      ponderContentOld: '',\r\n      time: '',\r\n      dataList: [],\r\n      fileData: [],\r\n      chartData: [],\r\n      guideWord: []\r\n    }\r\n    nextTick(async () => {\r\n      await currentRequest.retry()\r\n    })\r\n  } else {\r\n    isScroll.value = false\r\n    emit('handleRetryMessage', chatData.value[index - 1])\r\n  }\r\n}\r\nconst handlePromptWord = (data) => {\r\n  emit('handlePromptWord', data)\r\n}\r\nconst handleGuideWord = (data) => {\r\n  emit('handleGuideWord', data)\r\n}\r\n\r\nconst handleSendMessage = (value, params) => {\r\n  if (isStreaming.value) return ElMessage({ type: 'warning', message: '请先完成上一次对话再进行新对话！' })\r\n  handleStopMessage()\r\n  loading.value = true\r\n  isScroll.value = false\r\n  handleNewChatMessage({ question: value })\r\n  nextTick(() => {\r\n    scrollDown()\r\n    handleHttpStream(params)\r\n    emit('handleSendMessageCallback')\r\n  })\r\n}\r\n\r\nconst handleHttpStream = async (params = {}) => {\r\n  isStreaming.value = true\r\n  emit('handleStreamingCallback', true)\r\n  try {\r\n    startTime = new Date()\r\n    currentRequest = http_stream('/aigpt/chatStream', {\r\n      body: JSON.stringify({ chatBusinessScene: props.AiChatCode, chatId: props.chatId, ...params }),\r\n      onMessage(event) {\r\n        // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\r\n        loading.value = false\r\n        if (event.data !== '[DONE]') {\r\n          const data = JSON.parse(event.data)\r\n          if (Array.isArray(data)) {\r\n            // console.log('[]', data)\r\n            let quoteList = []\r\n            let newChartData = []\r\n            let newGuideWord = []\r\n            for (let index = 0; index < data.length; index++) {\r\n              const item = data[index]\r\n              if (typeof item === 'string') {\r\n                newGuideWord.push({ ...params, question: item })\r\n              } else {\r\n                if (item?.quoteList?.length) {\r\n                  for (let i = 0; i < item?.quoteList.length; i++) {\r\n                    const row = item?.quoteList[i]\r\n                    quoteList.push({ ...row, markdownContent: row.q })\r\n                  }\r\n                }\r\n                if (item?.echartItem) {\r\n                  newChartData.push(stringToJson(item?.echartItem))\r\n                }\r\n              }\r\n            }\r\n            chatData.value[chatData.value.length - 1].dataList = quoteList\r\n            chatData.value[chatData.value.length - 1].chartData = newChartData\r\n            chatData.value[chatData.value.length - 1].guideWord = newGuideWord\r\n          } else {\r\n            // console.log('{}', data)\r\n            const choice = data?.choices || [{}]\r\n            const details = choice[0]?.delta || {}\r\n            if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\r\n              elPonderRef.value?.enqueueRender(details.reasoning_content || '')\r\n              if (chatData.value[chatData.value.length - 1].time) {\r\n                startTime = null\r\n                endTime = null\r\n              } else {\r\n                endTime = new Date()\r\n                const executionTime = endTime - startTime\r\n                chatData.value[chatData.value.length - 1].time = formatDuring(executionTime)\r\n              }\r\n            }\r\n            if (Object.prototype.hasOwnProperty.call(details, 'content'))\r\n              elRef.value?.enqueueRender(details.content || '')\r\n            nextTick(() => {\r\n              scrollDown()\r\n            })\r\n          }\r\n        } else {\r\n          // console.log(event.data)\r\n          elRef.value?.enqueueRender('')\r\n          nextTick(() => {\r\n            scrollDown()\r\n          })\r\n        }\r\n      },\r\n      onError(err) {\r\n        console.log('流式接口错误:', err)\r\n      },\r\n      onClose() {\r\n        loading.value = false\r\n        isStreaming.value = false\r\n        emit('handleStreamingCallback', false)\r\n        chatData.value[chatData.value.length - 1].isControls = true\r\n        emit('handleChatDialogueRefresh')\r\n        console.log('流式接口关闭')\r\n      }\r\n    })\r\n    await currentRequest.promise\r\n  } catch (error) {\r\n    loading.value = false\r\n    isStreaming.value = false\r\n    emit('handleStreamingCallback', false)\r\n    elRef.value?.enqueueRender('服务器繁忙，请稍后再试。')\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n    console.error('启动流式接口失败:', error)\r\n  } finally {\r\n    // currentRequest = null\r\n  }\r\n}\r\n\r\ndefineExpose({ handleNewChat, handleOldChat, handleSendMessage, handleStopMessage })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiChatScroll {\r\n  width: 100%;\r\n  flex: 1;\r\n\r\n  .GlobalAiChatBody {\r\n    width: 100%;\r\n    padding: 0 12px;\r\n\r\n    .GlobalAiChatBodyTipsBody {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n    }\r\n\r\n    .GlobalAiChatBodyTipsVice {\r\n      width: 100%;\r\n      padding: 6px 0;\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-text-color-secondary);\r\n    }\r\n    .GlobalAiChatBodyTipsItem {\r\n      width: 100%;\r\n      padding: 6px 0;\r\n\r\n      .GlobalAiChatBodyTips {\r\n        display: inline-block;\r\n        padding: 6px 12px;\r\n        border-radius: 6px;\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n        font-size: var(--zy-text-font-size);\r\n        background: var(--zy-el-color-info-light-9);\r\n        cursor: pointer;\r\n\r\n        span {\r\n          width: 14px;\r\n          display: inline-block;\r\n          line-height: 1.2;\r\n          margin-right: 6px;\r\n          vertical-align: middle;\r\n\r\n          svg {\r\n            vertical-align: top;\r\n\r\n            path {\r\n              fill: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatMessage,\r\n    .GlobalAiChatSelfMessage {\r\n      padding: 12px 0;\r\n    }\r\n\r\n    .GlobalAiChatMessage {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-right: 12px;\r\n\r\n      .zy-el-image {\r\n        width: 32px;\r\n        height: 32px;\r\n      }\r\n\r\n      .GlobalAiChatMessageInfo {\r\n        width: 100%;\r\n        // width: calc(100% - 46px);\r\n        display: inline-block;\r\n        background: #fff;\r\n        position: relative;\r\n\r\n        .GlobalAiChatMessageLoading {\r\n          width: 100%;\r\n          height: 32px;\r\n          position: absolute;\r\n          top: 0;\r\n          left: 0;\r\n\r\n          @keyframes circleRoate {\r\n            from {\r\n              transform: translateY(-50%) rotate(0deg);\r\n            }\r\n\r\n            to {\r\n              transform: translateY(-50%) rotate(360deg);\r\n            }\r\n          }\r\n\r\n          .answerLoading {\r\n            width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            position: absolute;\r\n            top: 50%;\r\n            left: 0;\r\n            z-index: 3;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            animation: circleRoate 1s infinite linear;\r\n\r\n            path {\r\n              fill: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n\r\n          .answerLoading + .QuestionsAndAnswersChatText {\r\n            color: var(--zy-el-color-primary);\r\n            padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n          }\r\n        }\r\n\r\n        .GlobalAiChatMessagePonder {\r\n          height: 32px;\r\n          display: inline-flex;\r\n          align-items: center;\r\n          padding: 0 12px;\r\n          border-radius: 6px;\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          color: var(--zy-el-text-color-primary);\r\n          background: var(--zy-el-color-info-light-9);\r\n          margin-bottom: 12px;\r\n          cursor: pointer;\r\n\r\n          div {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 6px;\r\n          }\r\n\r\n          span {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            color: var(--zy-el-text-color-primary);\r\n          }\r\n        }\r\n\r\n        .GlobalAiChatMessagePonderContent {\r\n          width: 100%;\r\n          padding-left: 12px;\r\n          border-left: 2px solid var(--zy-el-border-color);\r\n\r\n          * {\r\n            color: var(--zy-el-text-color-secondary);\r\n          }\r\n        }\r\n\r\n        .GlobalAiChatMessageContent {\r\n          width: 100%;\r\n          padding-top: calc((32px - (var(--zy-line-height) * var(--zy-text-font-size))) / 2);\r\n\r\n          .GlobalAiChatMessageDataList {\r\n            padding-top: 12px;\r\n\r\n            .GlobalAiChatMessageDataItem {\r\n              color: var(--zy-el-color-primary);\r\n              line-height: var(--zy-line-height);\r\n              font-size: var(--zy-text-font-size);\r\n              cursor: pointer;\r\n            }\r\n          }\r\n        }\r\n\r\n        .GlobalAiChatMessageControls {\r\n          width: 100%;\r\n          display: flex;\r\n          align-items: center;\r\n          padding-top: 12px;\r\n\r\n          .GlobalAiChatMessageControlsItem {\r\n            width: 32px;\r\n            height: 32px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            border-radius: 6px;\r\n            cursor: pointer;\r\n            margin-right: 2px;\r\n\r\n            &:hover {\r\n              background: var(--zy-el-color-info-light-8);\r\n            }\r\n\r\n            .zy-el-icon {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatSelfMessage {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      align-items: flex-end;\r\n      flex-direction: column;\r\n      padding-left: 46px;\r\n      padding-right: 12px;\r\n\r\n      .GlobalAiChatSelfMessageFile {\r\n        width: 280px;\r\n        height: 52px;\r\n        display: inline-flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        background: #fff;\r\n        position: relative;\r\n        padding: 0 40px 0 12px;\r\n        border-radius: var(--el-border-radius-base);\r\n        border: 1px solid var(--zy-el-border-color-light);\r\n        background: var(--zy-el-color-info-light-9);\r\n        word-wrap: break-word;\r\n        white-space: pre-wrap;\r\n        cursor: pointer;\r\n        margin-bottom: 12px;\r\n\r\n        .GlobalChatMessagesFileName {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding-bottom: 2px;\r\n        }\r\n\r\n        .GlobalChatMessagesFileSize {\r\n          color: var(--zy-el-text-color-secondary);\r\n          font-size: calc(var(--zy-text-font-size) - 2px);\r\n        }\r\n\r\n        .globalFileIcon {\r\n          width: 28px;\r\n          height: 28px;\r\n          vertical-align: middle;\r\n          position: absolute;\r\n          top: 50%;\r\n          right: 6px;\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        .globalFileUnknown {\r\n          background: url('../img/file_type/unknown.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePDF {\r\n          background: url('../img/file_type/PDF.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWord {\r\n          background: url('../img/file_type/Word.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileExcel {\r\n          background: url('../img/file_type/Excel.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePicture {\r\n          background: url('../img/file_type/picture.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileVideo {\r\n          background: url('../img/file_type/video.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileTXT {\r\n          background: url('../img/file_type/TXT.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileCompress {\r\n          background: url('../img/file_type/compress.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWPS {\r\n          background: url('../img/file_type/WPS.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePPT {\r\n          background: url('../img/file_type/PPT.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n      }\r\n\r\n      .GlobalAiChatSelfMessageInfo {\r\n        display: inline-block;\r\n        padding: 12px;\r\n        border-radius: 6px;\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-text-font-size);\r\n        background: var(--zy-el-bg-color-page);\r\n        white-space: pre-wrap;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;+CA2GA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,MAAM,MAAM,eAAe;AAClC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,KAAK;AACnE,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,SAASC,SAAS,QAAQ,cAAc;AAVxC,IAAAC,WAAA,GAAe;EAAErC,IAAI,EAAE;AAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAW7C,IAAMsC,cAAc,GAAGL,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,uDAAuD,CAAC;IAAA,EAAC;IAClH,IAAMM,aAAa,GAAGN,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,qBAAqB,CAAC;IAAA,EAAC;IAC/E,IAAMO,gBAAgB,GAAGP,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,wBAAwB,CAAC;IAAA,EAAC;IACrF,IAAMQ,KAAK,GAAGC,OAMZ;IACF,IAAMC,IAAI,GAAGC,MAOX;IACF,IAAMC,QAAQ,GACZ,o7CAAo7C;IACt7C,IAAMC,WAAW,GACf,62CAA62C;IAC/2C,IAAMC,UAAU,GACd,qqDAAqqD;IAEvqD,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAACrH,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAIkI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9B5F,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAAC6F,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IAED,IAAMC,SAAS,GAAGvB,GAAG,CAAC,CAAC;IACvB,IAAMwB,QAAQ,GAAGxB,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMyB,QAAQ,GAAGzB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAM0B,SAAS,GAAG1B,GAAG,CAAC,CAAC,CAAC;IACxB,IAAM2B,UAAU,GAAG3B,GAAG,CAAC,CAAC,CAAC;IACzB,IAAM4B,gBAAgB,GAAG5B,GAAG,CAAC,CAAC,CAAC;IAE/B,IAAM6B,WAAW,GAAG5B,QAAQ,CAAC;MAAA,OAAMU,KAAK,CAACkB,WAAW;IAAA,EAAC;IACrD,IAAMC,MAAM,GAAG9B,GAAG,CAAC,EAAE,CAAC;IACtB,IAAM+B,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,EAAE,EAAEtI,CAAC,EAAK;MAC1B,IAAMuI,KAAK,GAAG,CAACvI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;MAC7B,IAAIsI,EAAE,EAAEF,MAAM,CAACrI,KAAK,CAACwI,KAAK,CAAC,GAAGD,EAAE;IAClC,CAAC;IACD,IAAME,KAAK,GAAGjC,QAAQ,CAAC;MAAA,OAAM6B,MAAM,CAACrI,KAAK,CAACqI,MAAM,CAACrI,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC;IAAA,EAAC;IACnE,IAAMqE,YAAY,GAAGnC,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMoC,cAAc,GAAG,SAAjBA,cAAcA,CAAIJ,EAAE,EAAEtI,CAAC,EAAK;MAChC,IAAMuI,KAAK,GAAG,CAACvI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;MAC7B,IAAIsI,EAAE,EAAEG,YAAY,CAAC1I,KAAK,CAACwI,KAAK,CAAC,GAAGD,EAAE;IACxC,CAAC;IACD,IAAMK,WAAW,GAAGpC,QAAQ,CAAC;MAAA,OAAMkC,YAAY,CAAC1I,KAAK,CAAC0I,YAAY,CAAC1I,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC;IAAA,EAAC;IAErF,IAAIwE,cAAc,GAAG,IAAI;IACzB,IAAMC,OAAO,GAAGvC,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMwC,WAAW,GAAGxC,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAIyC,SAAS,GAAG,IAAI;IACpB,IAAIC,OAAO,GAAG,IAAI;IAElB,IAAMC,QAAQ,GAAG3C,GAAG,CAAC,EAAE,CAAC;IACxB,IAAM4C,QAAQ,GAAG5C,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAM6C,QAAQ,GAAG7C,GAAG,CAAC,KAAK,CAAC;IAE3B,IAAM8C,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,QAAQ,EAAK;MAC7B,IAAMC,SAAS,GAAG;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,GAAG,EAAE,gBAAgB;QACrBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,iBAAiB;QACvBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,eAAe;QACrBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,oBAAoB;QACzBC,GAAG,EAAE;MACP,CAAC;MACD,OAAOhB,SAAS,CAACD,QAAQ,CAAC,IAAI,mBAAmB;IACnD,CAAC;IACD,IAAMkB,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAK;MAC5B,IAAMC,IAAI,GAAGC,QAAQ,CAACF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAClD,IAAMG,KAAK,GAAGD,QAAQ,CAAEF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACxE,IAAMI,OAAO,GAAGF,QAAQ,CAAEF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;MAChE,IAAMK,OAAO,GAAIL,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI;MAC1C,IAAIM,IAAI,GAAG,EAAE;MACb,IAAIL,IAAI,GAAG,CAAC,EAAEK,IAAI,IAAI,GAAGL,IAAI,KAAK;MAClC,IAAIE,KAAK,GAAG,CAAC,EAAEG,IAAI,IAAI,GAAGH,KAAK,MAAM;MACrC,IAAIC,OAAO,GAAG,CAAC,EAAEE,IAAI,IAAI,GAAGF,OAAO,MAAM;MACzC,IAAIC,OAAO,GAAG,CAAC,EAAEC,IAAI,IAAI,GAAGD,OAAO,KAAK;MACxC,OAAOC,IAAI;IACb,CAAC;IACD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAK;MAC5B;MACAA,GAAG,GAAGA,GAAG,CAACvD,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;MACrC;MACAuD,GAAG,GAAGA,GAAG,CAACvD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAC5B,IAAMwD,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;MAC3B,OAAOC,GAAG;IACZ,CAAC;IACD,IAAMG,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1BhD,MAAM,CAACrI,KAAK,GAAG,EAAE;MACjB0I,YAAY,CAAC1I,KAAK,GAAG,EAAE;MACvBgI,QAAQ,CAAChI,KAAK,GAAG,EAAE;MACnBiI,SAAS,CAACjI,KAAK,GAAG,CAAC;MACnBsL,iBAAiB,CAAC,CAAC;IACrB,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1BlD,MAAM,CAACrI,KAAK,GAAG,EAAE;MACjB0I,YAAY,CAAC1I,KAAK,GAAG,EAAE;MACvBgI,QAAQ,CAAChI,KAAK,GAAG,EAAE;MACnBiI,SAAS,CAACjI,KAAK,GAAG,CAAC;MACnBsL,iBAAiB,CAAC,CAAC;MACnBE,iBAAiB,CAAC,CAAC;IACrB,CAAC;IACD,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,IAAI,EAAK;MACrC,IAAMC,OAAO,GAAG;QACdC,EAAE,EAAEnE,IAAI,CAAC,CAAC;QACVtG,IAAI,EAAE,IAAI;QACV0K,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAEL,IAAI,CAACM,QAAQ;QACtBC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBpB,IAAI,EAAE,EAAE;QACRqB,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAEnF,KAAK,CAACmF,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAC;MACD,IAAMC,OAAO,GAAG;QACdX,EAAE,EAAEnE,IAAI,CAAC,CAAC;QACVtG,IAAI,EAAE,KAAK;QACX0K,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,EAAE;QACXE,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBpB,IAAI,EAAE,EAAE;QACRqB,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbE,SAAS,EAAE;MACb,CAAC;MACDxE,QAAQ,CAAChI,KAAK,CAACgE,IAAI,CAAC2H,OAAO,CAAC;MAC5B3D,QAAQ,CAAChI,KAAK,CAACgE,IAAI,CAACuI,OAAO,CAAC;IAC9B,CAAC;IACD,IAAME,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIf,IAAI,EAAEvK,IAAI,EAAK;MAC3C,IAAMwK,OAAO,GAAG;QACdC,EAAE,EAAEnE,IAAI,CAAC,CAAC;QACVtG,IAAI,EAAE,IAAI;QACV0K,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAEL,IAAI,CAACgB,YAAY;QAC1BT,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBpB,IAAI,EAAE,EAAE;QACRqB,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAEX,IAAI,CAACiB,WAAW;QAC1BL,SAAS,EAAE,EAAE;QACbE,SAAS,EAAE;MACb,CAAC;MACD,IAAMD,OAAO,GAAG;QACdX,EAAE,EAAEnE,IAAI,CAAC,CAAC;QACVtG,IAAI,EAAE,KAAK;QACX0K,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,EAAE;QACXE,UAAU,EAAEP,IAAI,CAACkB,MAAM;QACvBV,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAET,IAAI,CAACmB,SAAS;QAChC9B,IAAI,EAAEW,IAAI,CAACmB,SAAS,GAAG,GAAG,GAAG,EAAE;QAC/BT,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbE,SAAS,EAAE;MACb,CAAC;MACD,IAAIrL,IAAI,EAAE;QACR6G,QAAQ,CAAChI,KAAK,CAAC8M,OAAO,CAACP,OAAO,CAAC;QAC/BvE,QAAQ,CAAChI,KAAK,CAAC8M,OAAO,CAACnB,OAAO,CAAC;MACjC,CAAC,MAAM;QACL3D,QAAQ,CAAChI,KAAK,CAACgE,IAAI,CAAC2H,OAAO,CAAC;QAC5B3D,QAAQ,CAAChI,KAAK,CAACgE,IAAI,CAACuI,OAAO,CAAC;MAC9B;IACF,CAAC;IAED,IAAMf,iBAAiB;MAAA,IAAAuB,KAAA,GAAAhH,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsI,QAAOhN,KAAK;QAAA,IAAAiN,qBAAA,EAAAC,IAAA,EAAAC,KAAA,EAAAC,SAAA,EAAA5E,KAAA,EAAAkD,IAAA,EAAA2B,MAAA,EAAAC,KAAA;QAAA,OAAAhO,mBAAA,GAAAuB,IAAA,UAAA0M,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAArI,IAAA,GAAAqI,QAAA,CAAAhK,IAAA;YAAA;cAAAgK,QAAA,CAAAhK,IAAA;cAAA,OACN4C,GAAG,CAACqH,iBAAiB,CAAC;gBAClDC,MAAM,EAAE1N,KAAK,IAAI,CAAC;gBAClB2N,QAAQ,EAAE3N,KAAK,GAAG,CAAC,GAAG,EAAE;gBACxB4N,KAAK,EAAE,CAAC;gBACRC,KAAK,EAAE;kBAAEC,MAAM,EAAE5G,KAAK,CAAC4G;gBAAO;cAChC,CAAC,CAAC;YAAA;cAAAb,qBAAA,GAAAO,QAAA,CAAAvK,IAAA;cALMiK,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAAEC,KAAK,GAAAF,qBAAA,CAALE,KAAK;cAMnBlF,SAAS,CAACjI,KAAK,GAAGmN,KAAK;cACvB,IAAInN,KAAK,EAAE;gBACLoN,SAAS,GAAG,CAAC;gBACjB,KAAS5E,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG0E,IAAI,CAAC7I,MAAM,EAAEmE,KAAK,EAAE,EAAE;kBAC1CkD,IAAI,GAAGwB,IAAI,CAAC1E,KAAK,CAAC;kBACxB,IAAIkD,IAAI,CAACmB,SAAS,EAAEO,SAAS,IAAI,CAAC;kBAClC,IAAI1B,IAAI,CAACkB,MAAM,EAAEQ,SAAS,IAAI,CAAC;kBAC/BX,oBAAoB,CAACf,IAAI,EAAE,IAAI,CAAC;gBAClC;gBACAxD,UAAU,CAAClI,KAAK,GAAGoN,SAAS;gBAC5B3G,QAAQ,CAAC,YAAM;kBACbsH,cAAc,CAAC,CAAC;gBAClB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,KAASvF,MAAK,GAAG,CAAC,EAAEA,MAAK,GAAG0E,IAAI,CAAC7I,MAAM,EAAEmE,MAAK,EAAE,EAAE;kBAC1CkD,KAAI,GAAGwB,IAAI,CAAC1E,MAAK,CAAC;kBACxBiE,oBAAoB,CAACf,KAAI,EAAE,KAAK,CAAC;gBACnC;gBACA3D,QAAQ,CAAC/H,KAAK,GAAG,KAAK;gBACtByG,QAAQ,CAAC,YAAM;kBACbuH,UAAU,CAAC,CAAC;gBACd,CAAC,CAAC;cACJ;YAAC;YAAA;cAAA,OAAAR,QAAA,CAAAlI,IAAA;UAAA;QAAA,GAAA0H,OAAA;MAAA,CACF;MAAA,gBA9BKxB,iBAAiBA,CAAAyC,EAAA;QAAA,OAAAlB,KAAA,CAAA9G,KAAA,OAAAD,SAAA;MAAA;IAAA,GA8BtB;IACD,IAAMkI,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/BrF,cAAc,GAAG,IAAI;MACrBC,OAAO,CAAC9I,KAAK,GAAG,KAAK;MACrB+I,WAAW,CAAC/I,KAAK,GAAG,KAAK;MACzBoH,IAAI,CAAC,yBAAyB,EAAE,KAAK,CAAC;IACxC,CAAC;IACD,IAAMkE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B,IAAIzC,cAAc,EAAE;QAClBA,cAAc,CAACsF,KAAK,CAAC,CAAC;QACtBrF,OAAO,CAAC9I,KAAK,GAAG,KAAK;QACrB+I,WAAW,CAAC/I,KAAK,GAAG,KAAK;QACzBoH,IAAI,CAAC,yBAAyB,EAAE,KAAK,CAAC;QACtCgH,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB;MACAH,kBAAkB,CAAC,CAAC;IACtB,CAAC;IAED,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAI,CAACvG,QAAQ,CAAC/H,KAAK,IAAImI,gBAAgB,CAACnI,KAAK,EAAE;QAC7CyG,QAAQ,CAAC,YAAM;UACbsH,cAAc,CAAC,CAAC;QAClB,CAAC,CAAC;QACF7F,UAAU,CAAClI,KAAK,GAAGkI,UAAU,CAAClI,KAAK,GAAG,CAAC;MACzC,CAAC,MAAM;QACLkI,UAAU,CAAClI,KAAK,GAAG,CAAC;QACpByG,QAAQ,CAAC,YAAM;UACbuH,UAAU,CAAC,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAMO,YAAY,GAAG,SAAfA,YAAYA,CAAAC,KAAA,EAAsB;MAAA,IAAhBC,SAAS,GAAAD,KAAA,CAATC,SAAS;MAC/B,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnBtG,gBAAgB,CAACnI,KAAK,GAAG8H,SAAS,CAAC9H,KAAK,CAAC0O,OAAO,CAACC,YAAY;QAC7D,IAAI1G,SAAS,CAACjI,KAAK,IAAIiI,SAAS,CAACjI,KAAK,GAAGgI,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC,EAAE;UAClEmH,iBAAiB,CAACxD,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD;MACF;MACA,IAAAuK,qBAAA,GAAuC9G,SAAS,CAAC9H,KAAK,CAAC0O,OAAO;QAAtDC,YAAY,GAAAC,qBAAA,CAAZD,YAAY;QAAEE,YAAY,GAAAD,qBAAA,CAAZC,YAAY;MAClC,IAAIF,YAAY,GAAGF,SAAS,IAAII,YAAY,GAAG,EAAE,EAAE;QACjD9G,QAAQ,CAAC/H,KAAK,GAAG,KAAK;MACxB,CAAC,MAAM;QACL+H,QAAQ,CAAC/H,KAAK,GAAG,IAAI;MACvB;IACF,CAAC;IACD,IAAMgO,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIjG,QAAQ,CAAC/H,KAAK,EAAE;MACpB8H,SAAS,CAAC9H,KAAK,CAAC0O,OAAO,CAACD,SAAS,GAAG3G,SAAS,CAAC9H,KAAK,CAAC0O,OAAO,CAACC,YAAY;IAC1E,CAAC;IACD,IAAMZ,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3BjG,SAAS,CAAC9H,KAAK,CAAC0O,OAAO,CAACD,SAAS,GAAG3G,SAAS,CAAC9H,KAAK,CAAC0O,OAAO,CAACC,YAAY,GAAGxG,gBAAgB,CAACnI,KAAK;IACnG,CAAC;IACD,IAAM8O,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAK;MAC7BnI,kBAAkB,CAAC;QACjBnC,IAAI,EAAEuK,OAAO,CAACC,GAAG,CAACC,YAAY;QAC9BC,MAAM,EAAEJ,GAAG,CAACnD,EAAE;QACdtC,QAAQ,EAAEyF,GAAG,CAACK,OAAO;QACrBC,QAAQ,EAAEN,GAAG,CAACO,gBAAgB;QAC9BC,QAAQ,EAAER,GAAG,CAACQ;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,KAAA,EAA8B;MAAA,IAAxBC,IAAI,GAAAD,KAAA,CAAJC,IAAI;QAAEC,IAAI,GAAAF,KAAA,CAAJE,IAAI;QAAEC,KAAK,GAAAH,KAAA,CAALG,KAAK;MAC1CxB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEqB,IAAI,EAAEC,IAAI,CAAC;MACjC,IAAIA,IAAI,KAAK,QAAQ,EAAE;QACrB,IAAME,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;QACnDC,MAAM,CAACC,IAAI,CAAC,GAAG5J,MAAM,CAAC6J,QAAQ,8BAA8BhJ,KAAK,CAAC4G,MAAM,UAAU+B,KAAK,EAAE,EAAE,QAAQ,CAAC;MACtG,CAAC,MAAM;QACLG,MAAM,CAACC,IAAI,CAACP,IAAI,EAAE,QAAQ,CAAC;MAC7B;IACF,CAAC;IACD,IAAMS,cAAc,GAAG,SAAjBA,cAAcA,CAAIpB,GAAG,EAAK;MAC9B7F,QAAQ,CAAClJ,KAAK,GAAG+O,GAAG,CAACqB,UAAU;MAC/BjH,QAAQ,CAACnJ,KAAK,GAAG+O,GAAG;MACpB3F,QAAQ,CAACpJ,KAAK,GAAG,IAAI;IACvB,CAAC;IACD,IAAMqQ,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAItE,OAAO,EAAE9L,CAAC,EAAK;MAAA,IAAAqQ,mBAAA;MACxC,IAAM9H,KAAK,GAAG,CAACvI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;MAC7B,IAAMsQ,WAAW,GAAG,EAAAD,mBAAA,GAAAjI,MAAM,CAACrI,KAAK,CAACwI,KAAK,CAAC,cAAA8H,mBAAA,gBAAAA,mBAAA,GAAnBA,mBAAA,CAAqB7H,KAAK,cAAA6H,mBAAA,uBAA1BA,mBAAA,CAA4BE,SAAS,KAAIzE,OAAO;MACpE,IAAM0E,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACnDF,QAAQ,CAACG,QAAQ,GAAG,UAAU;MAC9BH,QAAQ,CAACI,KAAK,CAACC,QAAQ,GAAG,UAAU;MACpCL,QAAQ,CAACI,KAAK,CAACE,IAAI,GAAG,SAAS;MAC/BN,QAAQ,CAACzQ,KAAK,GAAGuQ,WAAW;MAC5BG,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,QAAQ,CAAC;MACnCA,QAAQ,CAACS,MAAM,CAAC,CAAC;MACjB,IAAMC,MAAM,GAAGT,QAAQ,CAACU,WAAW,CAAC,MAAM,CAAC;MAC3C,IAAID,MAAM,EAAE;QACVtK,SAAS,CAAC;UAAEwK,OAAO,EAAE,MAAM;UAAElQ,IAAI,EAAE;QAAU,CAAC,CAAC;MACjD;MACAuP,QAAQ,CAACM,IAAI,CAACM,WAAW,CAACb,QAAQ,CAAC;IACrC,CAAC;IACD,IAAMc,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI7F,IAAI,EAAElD,KAAK,EAAK;MAC1C,IAAMnE,MAAM,GAAG2D,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC;MACxC,IAAImE,KAAK,KAAKnE,MAAM,IAAIwE,cAAc,EAAE;QACtCC,OAAO,CAAC9I,KAAK,GAAG,IAAI;QACpB+I,WAAW,CAAC/I,KAAK,GAAG,IAAI;QACxBoH,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC;QACrCY,QAAQ,CAAChI,KAAK,CAACqE,MAAM,CAAC,GAAG;UACvBuH,EAAE,EAAEnE,IAAI,CAAC,CAAC;UACVtG,IAAI,EAAE,KAAK;UACX0K,UAAU,EAAE,IAAI;UAChBC,UAAU,EAAE,KAAK;UACjBC,OAAO,EAAE,EAAE;UACXE,UAAU,EAAE,EAAE;UACdC,aAAa,EAAE,EAAE;UACjBC,gBAAgB,EAAE,EAAE;UACpBpB,IAAI,EAAE,EAAE;UACRqB,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE,EAAE;UACbE,SAAS,EAAE;QACb,CAAC;QACD/F,QAAQ,cAAAV,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAC,SAAA8M,SAAA;UAAA,OAAAlS,mBAAA,GAAAuB,IAAA,UAAA4Q,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAvM,IAAA,GAAAuM,SAAA,CAAAlO,IAAA;cAAA;gBAAAkO,SAAA,CAAAlO,IAAA;gBAAA,OACDqF,cAAc,CAAC8I,KAAK,CAAC,CAAC;cAAA;cAAA;gBAAA,OAAAD,SAAA,CAAApM,IAAA;YAAA;UAAA,GAAAkM,QAAA;QAAA,CAC7B,GAAC;MACJ,CAAC,MAAM;QACLzJ,QAAQ,CAAC/H,KAAK,GAAG,KAAK;QACtBoH,IAAI,CAAC,oBAAoB,EAAEY,QAAQ,CAAChI,KAAK,CAACwI,KAAK,GAAG,CAAC,CAAC,CAAC;MACvD;IACF,CAAC;IACD,IAAMoJ,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI1E,IAAI,EAAK;MACjC9F,IAAI,CAAC,kBAAkB,EAAE8F,IAAI,CAAC;IAChC,CAAC;IACD,IAAM2E,eAAe,GAAG,SAAlBA,eAAeA,CAAI3E,IAAI,EAAK;MAChC9F,IAAI,CAAC,iBAAiB,EAAE8F,IAAI,CAAC;IAC/B,CAAC;IAED,IAAM4E,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAI9R,KAAK,EAAE+R,MAAM,EAAK;MAC3C,IAAIhJ,WAAW,CAAC/I,KAAK,EAAE,OAAO6G,SAAS,CAAC;QAAE1F,IAAI,EAAE,SAAS;QAAEkQ,OAAO,EAAE;MAAmB,CAAC,CAAC;MACzF/F,iBAAiB,CAAC,CAAC;MACnBxC,OAAO,CAAC9I,KAAK,GAAG,IAAI;MACpB+H,QAAQ,CAAC/H,KAAK,GAAG,KAAK;MACtByL,oBAAoB,CAAC;QAAEO,QAAQ,EAAEhM;MAAM,CAAC,CAAC;MACzCyG,QAAQ,CAAC,YAAM;QACbuH,UAAU,CAAC,CAAC;QACZgE,gBAAgB,CAACD,MAAM,CAAC;QACxB3K,IAAI,CAAC,2BAA2B,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC;IAED,IAAM4K,gBAAgB;MAAA,IAAAC,KAAA,GAAAlM,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwN,SAAA;QAAA,IAAAH,MAAA;UAAA7E,IAAA;UAAAiF,SAAA;UAAAC,YAAA;UAAAC,YAAA;UAAA7J,KAAA;UAAAkD,IAAA;UAAA4G,eAAA;UAAArS,CAAA;UAAA8O,GAAA;UAAAwD,QAAA;UAAAC,YAAA;UAAAC,MAAA;UAAAC,OAAA;UAAAC,kBAAA;UAAAC,aAAA;UAAAC,aAAA;UAAAC,aAAA;UAAAC,MAAA,GAAA/M,SAAA;QAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAAmS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9N,IAAA,GAAA8N,SAAA,CAAAzP,IAAA;YAAA;cAAOuO,MAAM,GAAAgB,MAAA,CAAA1O,MAAA,QAAA0O,MAAA,QAAAG,SAAA,GAAAH,MAAA,MAAG,CAAC,CAAC;cACzChK,WAAW,CAAC/I,KAAK,GAAG,IAAI;cACxBoH,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC;cAAA6L,SAAA,CAAA9N,IAAA;cAEnC6D,SAAS,GAAG,IAAImK,IAAI,CAAC,CAAC;cACtBtK,cAAc,GAAGvC,WAAW,CAAC,mBAAmB,EAAE;gBAChD0K,IAAI,EAAE7F,IAAI,CAACiI,SAAS,CAAAC,aAAA;kBAAGC,iBAAiB,EAAEpM,KAAK,CAACqM,UAAU;kBAAEzF,MAAM,EAAE5G,KAAK,CAAC4G;gBAAM,GAAKiE,MAAM,CAAE,CAAC;gBAC9FyB,SAASA,CAAC5D,KAAK,EAAE;kBACf;kBACA9G,OAAO,CAAC9I,KAAK,GAAG,KAAK;kBACrB,IAAI4P,KAAK,CAAC1C,IAAI,KAAK,QAAQ,EAAE;oBACrBA,IAAI,GAAG/B,IAAI,CAACC,KAAK,CAACwE,KAAK,CAAC1C,IAAI,CAAC;oBACnC,IAAIuG,KAAK,CAACC,OAAO,CAACxG,IAAI,CAAC,EAAE;sBACvB;sBACIiF,SAAS,GAAG,EAAE;sBACdC,YAAY,GAAG,EAAE;sBACjBC,YAAY,GAAG,EAAE;sBACrB,KAAS7J,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG0E,IAAI,CAAC7I,MAAM,EAAEmE,KAAK,EAAE,EAAE;wBAC1CkD,IAAI,GAAGwB,IAAI,CAAC1E,KAAK,CAAC;wBACxB,IAAI,OAAOkD,IAAI,KAAK,QAAQ,EAAE;0BAC5B2G,YAAY,CAACrO,IAAI,CAAAqP,aAAA,CAAAA,aAAA,KAAMtB,MAAM;4BAAE/F,QAAQ,EAAEN;0BAAI,EAAE,CAAC;wBAClD,CAAC,MAAM;0BACL,IAAIA,IAAI,aAAJA,IAAI,gBAAA4G,eAAA,GAAJ5G,IAAI,CAAEyG,SAAS,cAAAG,eAAA,eAAfA,eAAA,CAAiBjO,MAAM,EAAE;4BAC3B,KAASpE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAGyL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyG,SAAS,CAAC9N,MAAM,GAAEpE,CAAC,EAAE,EAAE;8BACzC8O,GAAG,GAAGrD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyG,SAAS,CAAClS,CAAC,CAAC;8BAC9BkS,SAAS,CAACnO,IAAI,CAAAqP,aAAA,CAAAA,aAAA,KAAMtE,GAAG;gCAAE4E,eAAe,EAAE5E,GAAG,CAAC6E;8BAAC,EAAE,CAAC;4BACpD;0BACF;0BACA,IAAIlI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmI,UAAU,EAAE;4BACpBzB,YAAY,CAACpO,IAAI,CAACgH,YAAY,CAACU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmI,UAAU,CAAC,CAAC;0BACnD;wBACF;sBACF;sBACA7L,QAAQ,CAAChI,KAAK,CAACgI,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC+H,QAAQ,GAAG+F,SAAS;sBAC9DnK,QAAQ,CAAChI,KAAK,CAACgI,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACiI,SAAS,GAAG8F,YAAY;sBAClEpK,QAAQ,CAAChI,KAAK,CAACgI,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACmI,SAAS,GAAG6F,YAAY;oBACpE,CAAC,MAAM;sBACL;sBACMI,MAAM,GAAG,CAAAvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4G,OAAO,KAAI,CAAC,CAAC,CAAC,CAAC;sBAC9BpB,OAAO,GAAG,EAAAH,QAAA,GAAAE,MAAM,CAAC,CAAC,CAAC,cAAAF,QAAA,uBAATA,QAAA,CAAWwB,KAAK,KAAI,CAAC,CAAC;sBACtC,IAAIrU,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAACqR,OAAO,EAAE,mBAAmB,CAAC,EAAE;wBACtE,CAAAC,kBAAA,GAAA/J,WAAW,CAAC5I,KAAK,cAAA2S,kBAAA,eAAjBA,kBAAA,CAAmBqB,aAAa,CAACtB,OAAO,CAACuB,iBAAiB,IAAI,EAAE,CAAC;wBACjE,IAAIjM,QAAQ,CAAChI,KAAK,CAACgI,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC0G,IAAI,EAAE;0BAClD/B,SAAS,GAAG,IAAI;0BAChBC,OAAO,GAAG,IAAI;wBAChB,CAAC,MAAM;0BACLA,OAAO,GAAG,IAAIkK,IAAI,CAAC,CAAC;0BACdP,aAAa,GAAG3J,OAAO,GAAGD,SAAS;0BACzChB,QAAQ,CAAChI,KAAK,CAACgI,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC0G,IAAI,GAAGP,YAAY,CAACoI,aAAa,CAAC;wBAC9E;sBACF;sBACA,IAAIlT,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAACqR,OAAO,EAAE,SAAS,CAAC,EAC1D,CAAAF,YAAA,GAAA/J,KAAK,CAACzI,KAAK,cAAAwS,YAAA,eAAXA,YAAA,CAAawB,aAAa,CAACtB,OAAO,CAAC3G,OAAO,IAAI,EAAE,CAAC;sBACnDtF,QAAQ,CAAC,YAAM;wBACbuH,UAAU,CAAC,CAAC;sBACd,CAAC,CAAC;oBACJ;kBACF,CAAC,MAAM;oBACL;oBACA,CAAA6E,aAAA,GAAApK,KAAK,CAACzI,KAAK,cAAA6S,aAAA,eAAXA,aAAA,CAAamB,aAAa,CAAC,EAAE,CAAC;oBAC9BvN,QAAQ,CAAC,YAAM;sBACbuH,UAAU,CAAC,CAAC;oBACd,CAAC,CAAC;kBACJ;gBACF,CAAC;gBACDkG,OAAOA,CAACC,GAAG,EAAE;kBACX/F,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE8F,GAAG,CAAC;gBAC7B,CAAC;gBACDC,OAAOA,CAAA,EAAG;kBACRtL,OAAO,CAAC9I,KAAK,GAAG,KAAK;kBACrB+I,WAAW,CAAC/I,KAAK,GAAG,KAAK;kBACzBoH,IAAI,CAAC,yBAAyB,EAAE,KAAK,CAAC;kBACtCY,QAAQ,CAAChI,KAAK,CAACgI,QAAQ,CAAChI,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACyH,UAAU,GAAG,IAAI;kBAC3D1E,IAAI,CAAC,2BAA2B,CAAC;kBACjCgH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;gBACvB;cACF,CAAC,CAAC;cAAA4E,SAAA,CAAAzP,IAAA;cAAA,OACIqF,cAAc,CAACwL,OAAO;YAAA;cAAApB,SAAA,CAAAzP,IAAA;cAAA;YAAA;cAAAyP,SAAA,CAAA9N,IAAA;cAAA8N,SAAA,CAAAqB,EAAA,GAAArB,SAAA;cAE5BnK,OAAO,CAAC9I,KAAK,GAAG,KAAK;cACrB+I,WAAW,CAAC/I,KAAK,GAAG,KAAK;cACzBoH,IAAI,CAAC,yBAAyB,EAAE,KAAK,CAAC;cACtC,CAAA0L,aAAA,GAAArK,KAAK,CAACzI,KAAK,cAAA8S,aAAA,eAAXA,aAAA,CAAakB,aAAa,CAAC,cAAc,CAAC;cAC1CvN,QAAQ,CAAC,YAAM;gBACbuH,UAAU,CAAC,CAAC;cACd,CAAC,CAAC;cACFI,OAAO,CAACmG,KAAK,CAAC,WAAW,EAAAtB,SAAA,CAAAqB,EAAO,CAAC;YAAA;cAAArB,SAAA,CAAA9N,IAAA;cAAA,OAAA8N,SAAA,CAAAvN,MAAA;YAAA;YAAA;cAAA,OAAAuN,SAAA,CAAA3N,IAAA;UAAA;QAAA,GAAA4M,QAAA;MAAA,CAIpC;MAAA,gBA1FKF,gBAAgBA,CAAA;QAAA,OAAAC,KAAA,CAAAhM,KAAA,OAAAD,SAAA;MAAA;IAAA,GA0FrB;IAEDwO,QAAY,CAAC;MAAEnJ,aAAa;MAAEE,aAAa;MAAEuG,iBAAiB;MAAExG;IAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}