{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LoginViewRegion\"\n};\nvar _hoisted_2 = {\n  class: \"LoginViewRegionBox\"\n};\nvar _hoisted_3 = {\n  class: \"LoginViewRegionLogo\"\n};\nvar _hoisted_4 = [\"innerHTML\"];\nvar _hoisted_5 = {\n  key: 1,\n  class: \"LoginViewRegionSlideVerify\"\n};\nvar _hoisted_6 = {\n  class: \"LoginViewRegionFormOperation\"\n};\nvar _hoisted_7 = {\n  key: 0,\n  class: \"LoginViewRegionOperation\"\n};\nvar _hoisted_8 = {\n  class: \"LoginViewRegionOperationBox\"\n};\nvar _hoisted_9 = {\n  class: \"LoginViewRegionQrCodeBox\"\n};\nvar _hoisted_10 = {\n  class: \"LoginViewRegionQrCodeNameBody\"\n};\nvar _hoisted_11 = {\n  class: \"LoginViewRegionQrCodeLogo\"\n};\nvar _hoisted_12 = {\n  class: \"LoginViewRegionQrCodeRefreshBody\"\n};\nvar _hoisted_13 = {\n  class: \"LoginViewRegionQrCodeRefresh\"\n};\nvar _hoisted_14 = {\n  class: \"LoginViewRegionQrCodeText\"\n};\nvar _hoisted_15 = {\n  class: \"LoginViewRegionOperationBox\"\n};\nvar _hoisted_16 = {\n  class: \"LoginViewRegionQrCodeBox\"\n};\nvar _hoisted_17 = {\n  class: \"LoginViewRegionQrCodeNameBody\"\n};\nvar _hoisted_18 = {\n  class: \"LoginViewRegionQrCodeLogo\"\n};\nvar _hoisted_19 = {\n  class: \"LoginViewRegionQrCodeText\"\n};\nvar _hoisted_20 = {\n  key: 1,\n  class: \"LoginViewRegionSystemTips\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_carousel_item = _resolveComponent(\"el-carousel-item\");\n  var _component_el_carousel = _resolveComponent(\"el-carousel\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_xyl_slide_verify = _resolveComponent(\"xyl-slide-verify\");\n  var _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [$setup.imgList.length ? (_openBlock(), _createBlock(_component_el_carousel, {\n    key: 0,\n    class: \"LoginViewRegionCarousel\",\n    height: \"100%\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.imgList, function (item) {\n        return _openBlock(), _createBlock(_component_el_carousel_item, {\n          class: \"LoginViewRegionCarouselBox\",\n          key: item.id\n        }, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_el_image, {\n              src: item.imgPath,\n              loading: \"lazy\",\n              fit: \"cover\"\n            }, null, 8 /* PROPS */, [\"src\"])];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n    src: $setup.systemLogo,\n    fit: \"cover\"\n  }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", {\n    class: \"LoginViewRegionName\",\n    innerHTML: $setup.loginSystemName\n  }, null, 8 /* PROPS */, _hoisted_4), _createVNode(_component_el_form, {\n    ref: \"LoginForm\",\n    model: $setup.form,\n    rules: $setup.rules,\n    class: \"LoginViewRegionForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        prop: \"account\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.account,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.account = $event;\n            }),\n            placeholder: \"账号/手机号\",\n            onBlur: $setup.handleBlur,\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"onBlur\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        prop: \"password\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            type: \"password\",\n            modelValue: $setup.form.password,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.password = $event;\n            }),\n            placeholder: \"密码\",\n            \"show-password\": \"\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.loginVerifyShow && $setup.whetherVerifyCode ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        class: \"smsValidation\",\n        prop: \"verifyCode\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.verifyCode,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.verifyCode = $event;\n            }),\n            placeholder: \"短信验证码\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: $setup.handleGetVerifyCode,\n            disabled: $setup.countDownText != '获取验证码'\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.countDownText), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onClick\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.loginVerifyShow && !$setup.whetherVerifyCode ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_xyl_slide_verify, {\n        ref: \"slideVerify\",\n        onAgain: $setup.onAgain,\n        onSuccess: $setup.onSuccess,\n        disabled: $setup.disabled\n      }, null, 8 /* PROPS */, [\"onAgain\", \"onSuccess\", \"disabled\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_checkbox, {\n        modelValue: $setup.checked,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.checked = $event;\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[8] || (_cache[8] = [_createTextVNode(\"记住用户名和密码\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", {\n        class: \"LoginViewRegionFormOperationText\",\n        onClick: _cache[4] || (_cache[4] = function ($event) {\n          return $setup.show = !$setup.show;\n        })\n      }, \"忘记密码？\")]), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[5] || (_cache[5] = function ($event) {\n          return $setup.submitForm($setup.LoginForm);\n        }),\n        class: \"LoginViewRegionFormButton\",\n        loading: $setup.loading,\n        disabled: $setup.loginDisabled\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.loading ? '登录中' : '登录'), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"loading\", \"disabled\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"]), $setup.appDownloadUrl ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_popover, {\n    placement: \"top\",\n    width: \"auto\",\n    onShow: $setup.refresh,\n    onHide: $setup.hideQrcode\n  }, {\n    reference: _withCtx(function () {\n      return _cache[11] || (_cache[11] = [_createElementVNode(\"div\", {\n        class: \"LoginViewRegionQrCode\"\n      }, null, -1 /* HOISTED */)]);\n    }),\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_image, {\n        src: $setup.systemLogo,\n        fit: \"cover\"\n      }, null, 8 /* PROPS */, [\"src\"])]), _cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n        class: \"LoginViewRegionQrCodeName\"\n      }, \"APP扫码登录\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_12, [_createVNode($setup[\"QrcodeVue\"], {\n        value: $setup.loginQrcode,\n        size: 120\n      }, null, 8 /* PROPS */, [\"value\"]), _withDirectives(_createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.refresh\n      }, {\n        default: _withCtx(function () {\n          return _cache[10] || (_cache[10] = [_createTextVNode(\"刷新\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])], 512 /* NEED_PATCH */), [[_vShow, $setup.loginQrcodeShow]])]), _createElementVNode(\"div\", _hoisted_14, \"请使用\" + _toDisplayString($setup.systemName) + \"APP扫码登录\", 1 /* TEXT */)])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onShow\", \"onHide\"]), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n    class: \"LoginViewRegionOperationText\"\n  }, \"APP扫码登录\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_popover, {\n    placement: \"top\",\n    width: \"auto\"\n  }, {\n    reference: _withCtx(function () {\n      return _cache[14] || (_cache[14] = [_createElementVNode(\"div\", {\n        class: \"LoginViewRegionApp\"\n      }, null, -1 /* HOISTED */)]);\n    }),\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_image, {\n        src: $setup.systemLogo,\n        fit: \"cover\"\n      }, null, 8 /* PROPS */, [\"src\"])]), _cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n        class: \"LoginViewRegionQrCodeName\"\n      }, \"手机APP下载\", -1 /* HOISTED */))]), _createVNode($setup[\"QrcodeVue\"], {\n        value: $setup.appDownloadUrl,\n        size: 120\n      }, null, 8 /* PROPS */, [\"value\"]), _createElementVNode(\"div\", _hoisted_19, \"使用其他软件扫码下载\" + _toDisplayString($setup.systemName) + \"APP\", 1 /* TEXT */)])];\n    }),\n    _: 1 /* STABLE */\n  }), _cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"LoginViewRegionOperationText\"\n  }, \"手机APP下载\", -1 /* HOISTED */))])])) : _createCommentVNode(\"v-if\", true), $setup.systemLoginContact ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, _toDisplayString($setup.systemLoginContact), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"重置密码\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"ResetPassword\"], {\n        onCallback: _cache[6] || (_cache[6] = function ($event) {\n          return $setup.show = !$setup.show;\n        })\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "$setup", "imgList", "length", "_createBlock", "_component_el_carousel", "height", "default", "_withCtx", "_Fragment", "_renderList", "item", "_component_el_carousel_item", "id", "_createVNode", "_component_el_image", "src", "imgPath", "loading", "fit", "_", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "systemLogo", "innerHTML", "loginSystemName", "_hoisted_4", "_component_el_form", "ref", "model", "form", "rules", "_component_el_form_item", "prop", "_component_el_input", "modelValue", "account", "_cache", "$event", "placeholder", "onBlur", "handleBlur", "clearable", "type", "password", "loginVerifyShow", "whetherVerifyCode", "verifyCode", "_component_el_button", "onClick", "handleGetVerifyCode", "disabled", "countDownText", "_createTextVNode", "_toDisplayString", "_hoisted_5", "_component_xyl_slide_verify", "onAgain", "onSuccess", "_hoisted_6", "_component_el_checkbox", "checked", "show", "submitForm", "LoginForm", "loginDisabled", "appDownloadUrl", "_hoisted_7", "_hoisted_8", "_component_el_popover", "placement", "width", "onShow", "refresh", "onHide", "hideQrcode", "reference", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "value", "loginQrcode", "size", "_hoisted_13", "loginQrcodeShow", "_hoisted_14", "systemName", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "systemLoginContact", "_hoisted_20", "_component_xyl_popup_window", "name", "onCallback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LoginViewRegion\\LoginViewRegion.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LoginViewRegion\">\r\n    <el-carousel class=\"LoginViewRegionCarousel\" v-if=\"imgList.length\" height=\"100%\">\r\n      <el-carousel-item class=\"LoginViewRegionCarouselBox\" v-for=\"item in imgList\" :key=\"item.id\">\r\n        <el-image :src=\"item.imgPath\" loading=\"lazy\" fit=\"cover\" />\r\n      </el-carousel-item>\r\n    </el-carousel>\r\n    <div class=\"LoginViewRegionBox\">\r\n      <div class=\"LoginViewRegionLogo\">\r\n        <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n      </div>\r\n      <div class=\"LoginViewRegionName\" v-html=\"loginSystemName\"></div>\r\n      <el-form ref=\"LoginForm\" :model=\"form\" :rules=\"rules\" class=\"LoginViewRegionForm\">\r\n        <el-form-item prop=\"account\">\r\n          <el-input v-model=\"form.account\" placeholder=\"账号/手机号\" @blur=\"handleBlur\" clearable />\r\n        </el-form-item>\r\n        <el-form-item prop=\"password\">\r\n          <el-input type=\"password\" v-model=\"form.password\" placeholder=\"密码\" show-password clearable />\r\n        </el-form-item>\r\n        <el-form-item class=\"smsValidation\" v-if=\"loginVerifyShow && whetherVerifyCode\" prop=\"verifyCode\">\r\n          <el-input v-model=\"form.verifyCode\" placeholder=\"短信验证码\" clearable></el-input>\r\n          <el-button type=\"primary\" @click=\"handleGetVerifyCode\" :disabled=\"countDownText != '获取验证码'\">\r\n            {{ countDownText }}\r\n          </el-button>\r\n        </el-form-item>\r\n        <div class=\"LoginViewRegionSlideVerify\" v-if=\"loginVerifyShow && !whetherVerifyCode\">\r\n          <xyl-slide-verify ref=\"slideVerify\" @again=\"onAgain\" @success=\"onSuccess\" :disabled=\"disabled\" />\r\n        </div>\r\n        <div class=\"LoginViewRegionFormOperation\">\r\n          <el-checkbox v-model=\"checked\">记住用户名和密码</el-checkbox>\r\n          <div class=\"LoginViewRegionFormOperationText\" @click=\"show = !show\">忘记密码？</div>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"submitForm(LoginForm)\" class=\"LoginViewRegionFormButton\" :loading=\"loading\"\r\n          :disabled=\"loginDisabled\">\r\n          {{ loading ? '登录中' : '登录' }}\r\n        </el-button>\r\n      </el-form>\r\n      <div class=\"LoginViewRegionOperation\" v-if=\"appDownloadUrl\">\r\n        <div class=\"LoginViewRegionOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\" @show=\"refresh\" @hide=\"hideQrcode\">\r\n            <div class=\"LoginViewRegionQrCodeBox\">\r\n              <div class=\"LoginViewRegionQrCodeNameBody\">\r\n                <div class=\"LoginViewRegionQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewRegionQrCodeName\">APP扫码登录</div>\r\n              </div>\r\n              <div class=\"LoginViewRegionQrCodeRefreshBody\">\r\n                <qrcode-vue :value=\"loginQrcode\" :size=\"120\" />\r\n                <div class=\"LoginViewRegionQrCodeRefresh\" v-show=\"loginQrcodeShow\">\r\n                  <el-button type=\"primary\" @click=\"refresh\">刷新</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"LoginViewRegionQrCodeText\">请使用{{ systemName }}APP扫码登录</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewRegionQrCode\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewRegionOperationText\">APP扫码登录</div>\r\n        </div>\r\n        <div class=\"LoginViewRegionOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\">\r\n            <div class=\"LoginViewRegionQrCodeBox\">\r\n              <div class=\"LoginViewRegionQrCodeNameBody\">\r\n                <div class=\"LoginViewRegionQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewRegionQrCodeName\">手机APP下载</div>\r\n              </div>\r\n              <qrcode-vue :value=\"appDownloadUrl\" :size=\"120\" />\r\n              <div class=\"LoginViewRegionQrCodeText\">使用其他软件扫码下载{{ systemName }}APP</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewRegionApp\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewRegionOperationText\">手机APP下载</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"LoginViewRegionSystemTips\" v-if=\"systemLoginContact\">{{ systemLoginContact }}</div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"重置密码\">\r\n      <ResetPassword @callback=\"show = !show\"></ResetPassword>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LoginViewRegion' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, computed, defineAsyncComponent } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport {\r\n  systemLogo,\r\n  systemName,\r\n  loginNameLineFeedPosition,\r\n  appDownloadUrl,\r\n  systemLoginContact\r\n} from 'common/js/system_var.js'\r\nimport { LoginView } from '../LoginView/LoginView.js'\r\nconst QrcodeVue = defineAsyncComponent(() => import('qrcode.vue'))\r\nconst ResetPassword = defineAsyncComponent(() => import('../LoginView/component/ResetPassword.vue'))\r\nconst route = useRoute()\r\nconst show = ref(false)\r\nconst imgList = ref([])\r\nconst localAreaName = ref('')\r\nconst localSystemName = ref('')\r\nconst localLoginNameLineFeedPosition = ref('')\r\nconst loginSystemName = computed(() => {\r\n  const name = (localAreaName.value || '') + (localSystemName.value || systemName.value)\r\n  const num = Number(localLoginNameLineFeedPosition.value || loginNameLineFeedPosition.value || '0') || 0\r\n  return num ? name.substring(0, num) + '\\n' + name.substring(num) : name\r\n})\r\nconst globalData = async () => {\r\n  const { data } = await api.loginImgRegion({}, route.query.areaId)\r\n  data.forEach((item) => {\r\n    item.imgPath = api.fileURL(item.imgPath)\r\n  })\r\n  imgList.value = data\r\n}\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.readOpenConfigRegion(\r\n    { codes: ['systemName', 'localAreaName', 'loginNameLineFeedPosition'] },\r\n    route.query.areaId\r\n  )\r\n  localSystemName.value = data?.systemName || ''\r\n  localAreaName.value = data?.localAreaName || ''\r\n  localLoginNameLineFeedPosition.value = data?.loginNameLineFeedPosition || ''\r\n}\r\nconst {\r\n  loginVerifyShow,\r\n  whetherVerifyCode,\r\n  loginDisabled,\r\n  loading,\r\n  checked,\r\n  LoginForm,\r\n  form,\r\n  rules,\r\n  countDownText,\r\n  slideVerify,\r\n  disabled,\r\n  loginQrcode,\r\n  loginQrcodeShow,\r\n  handleBlur,\r\n  handleGetVerifyCode,\r\n  onAgain,\r\n  onSuccess,\r\n  submitForm,\r\n  loginInfo,\r\n  refresh,\r\n  hideQrcode\r\n} = LoginView('/LoginViewRegion', { areaId: route.query.areaId })\r\nonMounted(() => {\r\n  loginInfo()\r\n  globalData()\r\n  globalReadConfig()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.LoginViewRegion {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n\r\n  .LoginViewRegionCarousel {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n\r\n    .LoginViewRegionCarouselBox {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .zy-el-carousel__indicators--horizontal {\r\n      .zy-el-carousel__button {\r\n        width: 16px;\r\n        height: 16px;\r\n        border-radius: 50%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewRegionBox {\r\n    padding: var(--zy-distance-one);\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    padding-bottom: var(--zy-distance-two);\r\n    border-radius: var(--el-border-radius-base);\r\n    margin-right: 80px;\r\n    background: #fff;\r\n    position: relative;\r\n    z-index: 2;\r\n\r\n    .LoginViewRegionLogo {\r\n      width: 60px;\r\n      margin: auto;\r\n      margin-bottom: var(--zy-distance-two);\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .LoginViewRegionName {\r\n      width: 320px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-align: center;\r\n      font-size: var(--zy-system-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      letter-spacing: 2px;\r\n      padding-bottom: var(--zy-distance-one);\r\n      white-space: pre-wrap;\r\n      margin: auto;\r\n    }\r\n\r\n    .LoginViewRegionForm {\r\n      width: 320px;\r\n      margin: auto;\r\n      padding-bottom: var(--zy-distance-one);\r\n\r\n      input:-webkit-autofill {\r\n        transition: background-color 5000s ease-in-out 0s;\r\n      }\r\n\r\n      .zy-el-form-item {\r\n        margin-bottom: var(--zy-form-distance-bottom);\r\n      }\r\n\r\n      .LoginViewRegionFormButton {\r\n        width: 100%;\r\n      }\r\n\r\n      .smsValidation {\r\n        .zy-el-form-item__content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n        }\r\n\r\n        .zy-el-input {\r\n          width: 56%;\r\n        }\r\n      }\r\n\r\n      .LoginViewRegionSlideVerify {\r\n        margin-bottom: var(--zy-distance-five);\r\n      }\r\n\r\n      .LoginViewRegionFormOperation {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: var(--zy-distance-three);\r\n\r\n        .zy-el-checkbox {\r\n          height: var(--zy-height-secondary);\r\n        }\r\n\r\n        .LoginViewRegionFormOperationText {\r\n          cursor: pointer;\r\n          color: var(--zy-el-color-primary);\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewRegionOperation {\r\n      width: 100%;\r\n      padding-bottom: var(--zy-distance-two);\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .LoginViewRegionOperationBox {\r\n        margin: 0 var(--zy-distance-two);\r\n        cursor: pointer;\r\n\r\n        .LoginViewRegionQrCode {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url('../img/login_qr_code.png');\r\n          background-size: 100% 100%;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewRegionApp {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url('../img/login_app.png') no-repeat;\r\n          background-size: auto 100%;\r\n          background-position: center;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewRegionOperationText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: var(--el-border-radius-small) 0;\r\n          text-align: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewRegionForm+.LoginViewRegionSystemTips {\r\n      padding-top: var(--zy-distance-one);\r\n    }\r\n\r\n    .LoginViewRegionSystemTips {\r\n      color: var(--zy-el-text-color-secondary);\r\n      font-size: var(--zy-text-font-size);\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.LoginViewRegionQrCodeBox {\r\n  width: 320px;\r\n  background-color: #fff;\r\n\r\n  canvas {\r\n    display: block;\r\n    margin: auto;\r\n  }\r\n\r\n  .LoginViewRegionQrCodeNameBody {\r\n    padding: var(--zy-distance-three);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LoginViewRegionQrCodeLogo {\r\n      width: 26px;\r\n      margin-right: 6px;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .LoginViewRegionQrCodeName {\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .LoginViewRegionQrCodeRefreshBody {\r\n    position: relative;\r\n\r\n    .LoginViewRegionQrCodeRefresh {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 120px;\r\n      height: 120px;\r\n      background-color: rgba(000, 000, 000, 0.6);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .zy-el-button {\r\n        --zy-el-button-size: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewRegionQrCodeText {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding: var(--zy-distance-three);\r\n    color: var(--zy-el-color-primary);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAMrBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAqB;iBARtC;;EAAAC,GAAA;EAyBaD,KAAK,EAAC;;;EAGNA,KAAK,EAAC;AAA8B;;EA5BjDC,GAAA;EAqCWD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA6B;;EAE/BA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAA2B;;EAKnCA,KAAK,EAAC;AAAkC;;EAEtCA,KAAK,EAAC;AAA8B;;EAItCA,KAAK,EAAC;AAA2B;;EAQvCA,KAAK,EAAC;AAA6B;;EAE/BA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAA2B;;EAMnCA,KAAK,EAAC;AAA2B;;EAvEpDC,GAAA;EAgFWD,KAAK,EAAC;;;;;;;;;;;;;;uBA/EfE,mBAAA,CAoFM,OApFNC,UAoFM,GAnF+CC,MAAA,CAAAC,OAAO,CAACC,MAAM,I,cAAjEC,YAAA,CAIcC,sBAAA;IANlBP,GAAA;IAEiBD,KAAK,EAAC,yBAAyB;IAAuBS,MAAM,EAAC;;IAF9EC,OAAA,EAAAC,QAAA,CAG2D;MAAA,OAAuB,E,kBAA5ET,mBAAA,CAEmBU,SAAA,QALzBC,WAAA,CAG0ET,MAAA,CAAAC,OAAO,EAHjF,UAGkES,IAAI;6BAAhEP,YAAA,CAEmBQ,2BAAA;UAFDf,KAAK,EAAC,4BAA4B;UAA0BC,GAAG,EAAEa,IAAI,CAACE;;UAH9FN,OAAA,EAAAC,QAAA,CAIQ;YAAA,OAA2D,CAA3DM,YAAA,CAA2DC,mBAAA;cAAhDC,GAAG,EAAEL,IAAI,CAACM,OAAO;cAAEC,OAAO,EAAC,MAAM;cAACC,GAAG,EAAC;;;UAJzDC,CAAA;;;;IAAAA,CAAA;QAAAC,mBAAA,gBAOIC,mBAAA,CA0EM,OA1ENC,UA0EM,GAzEJD,mBAAA,CAEM,OAFNE,UAEM,GADJV,YAAA,CAA0CC,mBAAA;IAA/BC,GAAG,EAAEf,MAAA,CAAAwB,UAAU;IAAEN,GAAG,EAAC;sCAElCG,mBAAA,CAAgE;IAA3DzB,KAAK,EAAC,qBAAqB;IAAC6B,SAAwB,EAAhBzB,MAAA,CAAA0B;0BAX/CC,UAAA,GAYMd,YAAA,CAwBUe,kBAAA;IAxBDC,GAAG,EAAC,WAAW;IAAEC,KAAK,EAAE9B,MAAA,CAAA+B,IAAI;IAAGC,KAAK,EAAEhC,MAAA,CAAAgC,KAAK;IAAEpC,KAAK,EAAC;;IAZlEU,OAAA,EAAAC,QAAA,CAaQ;MAAA,OAEe,CAFfM,YAAA,CAEeoB,uBAAA;QAFDC,IAAI,EAAC;MAAS;QAbpC5B,OAAA,EAAAC,QAAA,CAcU;UAAA,OAAqF,CAArFM,YAAA,CAAqFsB,mBAAA;YAd/FC,UAAA,EAc6BpC,MAAA,CAAA+B,IAAI,CAACM,OAAO;YAdzC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAc6BvC,MAAA,CAAA+B,IAAI,CAACM,OAAO,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,QAAQ;YAAEC,MAAI,EAAEzC,MAAA,CAAA0C,UAAU;YAAEC,SAAS,EAAT;;;QAdnFxB,CAAA;UAgBQN,YAAA,CAEeoB,uBAAA;QAFDC,IAAI,EAAC;MAAU;QAhBrC5B,OAAA,EAAAC,QAAA,CAiBU;UAAA,OAA6F,CAA7FM,YAAA,CAA6FsB,mBAAA;YAAnFS,IAAI,EAAC,UAAU;YAjBnCR,UAAA,EAiB6CpC,MAAA,CAAA+B,IAAI,CAACc,QAAQ;YAjB1D,uBAAAP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAiB6CvC,MAAA,CAAA+B,IAAI,CAACc,QAAQ,GAAAN,MAAA;YAAA;YAAEC,WAAW,EAAC,IAAI;YAAC,eAAa,EAAb,EAAa;YAACG,SAAS,EAAT;;;QAjB3FxB,CAAA;UAmBkDnB,MAAA,CAAA8C,eAAe,IAAI9C,MAAA,CAAA+C,iBAAiB,I,cAA9E5C,YAAA,CAKe8B,uBAAA;QAxBvBpC,GAAA;QAmBsBD,KAAK,EAAC,eAAe;QAA6CsC,IAAI,EAAC;;QAnB7F5B,OAAA,EAAAC,QAAA,CAoBU;UAAA,OAA6E,CAA7EM,YAAA,CAA6EsB,mBAAA;YApBvFC,UAAA,EAoB6BpC,MAAA,CAAA+B,IAAI,CAACiB,UAAU;YApB5C,uBAAAV,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoB6BvC,MAAA,CAAA+B,IAAI,CAACiB,UAAU,GAAAT,MAAA;YAAA;YAAEC,WAAW,EAAC,OAAO;YAACG,SAAS,EAAT;mDACxD9B,YAAA,CAEYoC,oBAAA;YAFDL,IAAI,EAAC,SAAS;YAAEM,OAAK,EAAElD,MAAA,CAAAmD,mBAAmB;YAAGC,QAAQ,EAAEpD,MAAA,CAAAqD,aAAa;;YArBzF/C,OAAA,EAAAC,QAAA,CAsBY;cAAA,OAAmB,CAtB/B+C,gBAAA,CAAAC,gBAAA,CAsBevD,MAAA,CAAAqD,aAAa,iB;;YAtB5BlC,CAAA;;;QAAAA,CAAA;YAAAC,mBAAA,gBAyBsDpB,MAAA,CAAA8C,eAAe,KAAK9C,MAAA,CAAA+C,iBAAiB,I,cAAnFjD,mBAAA,CAEM,OAFN0D,UAEM,GADJ3C,YAAA,CAAiG4C,2BAAA;QAA/E5B,GAAG,EAAC,aAAa;QAAE6B,OAAK,EAAE1D,MAAA,CAAA0D,OAAO;QAAGC,SAAO,EAAE3D,MAAA,CAAA2D,SAAS;QAAGP,QAAQ,EAAEpD,MAAA,CAAAoD;yEA1B/FhC,mBAAA,gBA4BQC,mBAAA,CAGM,OAHNuC,UAGM,GAFJ/C,YAAA,CAAqDgD,sBAAA;QA7B/DzB,UAAA,EA6BgCpC,MAAA,CAAA8D,OAAO;QA7BvC,uBAAAxB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA6BgCvC,MAAA,CAAA8D,OAAO,GAAAvB,MAAA;QAAA;;QA7BvCjC,OAAA,EAAAC,QAAA,CA6ByC;UAAA,OAAQ+B,MAAA,QAAAA,MAAA,OA7BjDgB,gBAAA,CA6ByC,UAAQ,E;;QA7BjDnC,CAAA;yCA8BUE,mBAAA,CAA+E;QAA1EzB,KAAK,EAAC,kCAAkC;QAAEsD,OAAK,EAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEvC,MAAA,CAAA+D,IAAI,IAAI/D,MAAA,CAAA+D,IAAI;QAAA;SAAE,OAAK,E,GAE3ElD,YAAA,CAGYoC,oBAAA;QAHDL,IAAI,EAAC,SAAS;QAAEM,OAAK,EAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEvC,MAAA,CAAAgE,UAAU,CAAChE,MAAA,CAAAiE,SAAS;QAAA;QAAGrE,KAAK,EAAC,2BAA2B;QAAEqB,OAAO,EAAEjB,MAAA,CAAAiB,OAAO;QACzGmC,QAAQ,EAAEpD,MAAA,CAAAkE;;QAjCrB5D,OAAA,EAAAC,QAAA,CAkCU;UAAA,OAA4B,CAlCtC+C,gBAAA,CAAAC,gBAAA,CAkCavD,MAAA,CAAAiB,OAAO,gC;;QAlCpBE,CAAA;;;IAAAA,CAAA;yCAqCkDnB,MAAA,CAAAmE,cAAc,I,cAA1DrE,mBAAA,CA0CM,OA1CNsE,UA0CM,GAzCJ/C,mBAAA,CAsBM,OAtBNgD,UAsBM,GArBJxD,YAAA,CAmBayD,qBAAA;IAnBDC,SAAS,EAAC,KAAK;IAACC,KAAK,EAAC,MAAM;IAAEC,MAAI,EAAEzE,MAAA,CAAA0E,OAAO;IAAGC,MAAI,EAAE3E,MAAA,CAAA4E;;IAgBnDC,SAAS,EAAAtE,QAAA,CAClB;MAAA,OAAyC+B,MAAA,SAAAA,MAAA,QAAzCjB,mBAAA,CAAyC;QAApCzB,KAAK,EAAC;MAAuB,2B;;IAxDhDU,OAAA,EAAAC,QAAA,CAwCY;MAAA,OAcM,CAdNc,mBAAA,CAcM,OAdNyD,UAcM,GAbJzD,mBAAA,CAKM,OALN0D,WAKM,GAJJ1D,mBAAA,CAEM,OAFN2D,WAEM,GADJnE,YAAA,CAA0CC,mBAAA;QAA/BC,GAAG,EAAEf,MAAA,CAAAwB,UAAU;QAAEN,GAAG,EAAC;oEAElCG,mBAAA,CAAoD;QAA/CzB,KAAK,EAAC;MAA2B,GAAC,SAAO,qB,GAEhDyB,mBAAA,CAKM,OALN4D,WAKM,GAJJpE,YAAA,CAA+Cb,MAAA;QAAlCkF,KAAK,EAAElF,MAAA,CAAAmF,WAAW;QAAGC,IAAI,EAAE;0DACxC/D,mBAAA,CAEM,OAFNgE,WAEM,GADJxE,YAAA,CAAyDoC,oBAAA;QAA9CL,IAAI,EAAC,SAAS;QAAEM,OAAK,EAAElD,MAAA,CAAA0E;;QAlDpDpE,OAAA,EAAAC,QAAA,CAkD6D;UAAA,OAAE+B,MAAA,SAAAA,MAAA,QAlD/DgB,gBAAA,CAkD6D,IAAE,E;;QAlD/DnC,CAAA;wEAiDkEnB,MAAA,CAAAsF,eAAe,E,KAInEjE,mBAAA,CAAuE,OAAvEkE,WAAuE,EAAhC,KAAG,GAAAhC,gBAAA,CAAGvD,MAAA,CAAAwF,UAAU,IAAG,SAAO,gB;;IArD/ErE,CAAA;uEA2DUE,mBAAA,CAAuD;IAAlDzB,KAAK,EAAC;EAA8B,GAAC,SAAO,qB,GAEnDyB,mBAAA,CAiBM,OAjBNoE,WAiBM,GAhBJ5E,YAAA,CAcayD,qBAAA;IAdDC,SAAS,EAAC,KAAK;IAACC,KAAK,EAAC;;IAWrBK,SAAS,EAAAtE,QAAA,CAClB;MAAA,OAAsC+B,MAAA,SAAAA,MAAA,QAAtCjB,mBAAA,CAAsC;QAAjCzB,KAAK,EAAC;MAAoB,2B;;IA1E7CU,OAAA,EAAAC,QAAA,CA+DY;MAAA,OASM,CATNc,mBAAA,CASM,OATNqE,WASM,GARJrE,mBAAA,CAKM,OALNsE,WAKM,GAJJtE,mBAAA,CAEM,OAFNuE,WAEM,GADJ/E,YAAA,CAA0CC,mBAAA;QAA/BC,GAAG,EAAEf,MAAA,CAAAwB,UAAU;QAAEN,GAAG,EAAC;sEAElCG,mBAAA,CAAoD;QAA/CzB,KAAK,EAAC;MAA2B,GAAC,SAAO,qB,GAEhDiB,YAAA,CAAkDb,MAAA;QAArCkF,KAAK,EAAElF,MAAA,CAAAmE,cAAc;QAAGiB,IAAI,EAAE;0CAC3C/D,mBAAA,CAA0E,OAA1EwE,WAA0E,EAAnC,YAAU,GAAAtC,gBAAA,CAAGvD,MAAA,CAAAwF,UAAU,IAAG,KAAG,gB;;IAvElFrE,CAAA;kCA6EUE,mBAAA,CAAuD;IAAlDzB,KAAK,EAAC;EAA8B,GAAC,SAAO,qB,OA7E3DwB,mBAAA,gBAgFmDpB,MAAA,CAAA8F,kBAAkB,I,cAA/DhG,mBAAA,CAA+F,OAA/FiG,WAA+F,EAAAxC,gBAAA,CAA3BvD,MAAA,CAAA8F,kBAAkB,oBAhF5F1E,mBAAA,e,GAkFIP,YAAA,CAEmBmF,2BAAA;IApFvB5D,UAAA,EAkF+BpC,MAAA,CAAA+D,IAAI;IAlFnC,uBAAAzB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAkF+BvC,MAAA,CAAA+D,IAAI,GAAAxB,MAAA;IAAA;IAAE0D,IAAI,EAAC;;IAlF1C3F,OAAA,EAAAC,QAAA,CAmFM;MAAA,OAAwD,CAAxDM,YAAA,CAAwDb,MAAA;QAAxCkG,UAAQ,EAAA5D,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEvC,MAAA,CAAA+D,IAAI,IAAI/D,MAAA,CAAA+D,IAAI;QAAA;;;IAnF5C5C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}