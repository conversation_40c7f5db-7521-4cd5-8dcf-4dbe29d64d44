{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport config from 'common/config';\nimport { ref, computed } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { useStore } from 'vuex';\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod.js';\nimport { user, systemLogo, systemName, systemNameAreaPrefix } from 'common/js/system_var.js';\nimport { AiToolBoxElement } from './AiToolBox.js';\nimport { ElMessageBox, ElMessage } from 'element-plus';\nvar exitIcon = `<svg t=\"1739268585892\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2352\" width=\"24\" height=\"24\"><path d=\"M0 192v640c0 70.7 57.3 128 128 128h352c17.7 0 32-14.3 32-32s-14.3-32-32-32H128c-35.3 0-64-28.7-64-64V192c0-35.3 28.7-64 64-64h352c17.7 0 32-14.3 32-32s-14.3-32-32-32H128C57.3 64 0 121.3 0 192z\" p-id=\"2353\" fill=\"#666666\"></path><path d=\"M1013.3 488.3L650.9 160.7c-41.2-37.2-106.9-8-106.9 47.5V339c0 4.4-3.6 8-8 8H224c-17.7 0-32 14.3-32 32v266c0 17.7 14.3 32 32 32h312c4.4 0 8 3.6 8 8v130.9c0 55.5 65.8 84.7 106.9 47.5l362.4-327.6c14.1-12.8 14.1-34.8 0-47.5zM256 597V427c0-8.8 7.2-16 16-16h304c17.7 0 32-14.3 32-32V244.9c0-13.9 16.4-21.2 26.7-11.9L938 506.1c3.5 3.2 3.5 8.7 0 11.9L634.7 791c-10.3 9.3-26.7 2-26.7-11.9V645c0-17.7-14.3-32-32-32H272c-8.8 0-16-7.2-16-16z\" p-id=\"2354\" fill=\"#666666\"></path></svg>`;\nexport default {\n  __name: 'GlobalAiToolBox',\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var router = useRouter();\n    var store = useStore();\n    var mainTop = computed(function () {\n      return `${config.API_URL}/pageImg/open/GlobalAiToolBoxMainTop?areaId=${user.value.areaId}`;\n    });\n    var navBottom = computed(function () {\n      return `${config.API_URL}/pageImg/open/GlobalAiToolBoxNavBottom?areaId=${user.value.areaId}`;\n    });\n    var regionName = ref('');\n    var toolId = ref('IntelligentErrorCorrection');\n    var toolName = ref('智能纠错');\n    var navList = ref([{\n      id: '1',\n      title: '工作通用工具',\n      tool: [{\n        id: 'IntelligentErrorCorrection',\n        name: '智能纠错'\n      }, {\n        id: 'OneClickLayout',\n        name: '一件排版'\n      }, {\n        id: 'ContentExtraction',\n        name: '内容提炼'\n      }, {\n        id: 'IntelligentManuscriptMerging',\n        name: '智能合稿'\n      }, {\n        id: 'TextComparison',\n        name: '文本比对'\n      }, {\n        id: 'TextPolishing',\n        name: '文本润色'\n      }, {\n        id: 'TextExpansion',\n        name: '文本扩写'\n      }, {\n        id: 'TextContinuation',\n        name: '文本续写'\n      }, {\n        id: 'TextRewrite',\n        name: '文本重写'\n      }, {\n        id: 'TextRecognition',\n        name: '文本识别'\n      }]\n    }, {\n      id: '2',\n      title: '业务专用工具',\n      tool: [{\n        id: 'ProposalAuxiliaryWriting',\n        name: '提案辅助撰写'\n      }]\n    }]);\n    var handleToolClick = function handleToolClick(tool) {\n      toolId.value = tool.id;\n      toolName.value = tool.name;\n    };\n    var handleClick = function handleClick() {\n      router.push('/');\n    };\n    var handleExit = function handleExit() {\n      ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        loginOut('已安全退出！');\n      }).catch(function () {\n        ElMessage({\n          type: 'info',\n          message: '已取消退出'\n        });\n      });\n    };\n    var loginOut = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(text) {\n        var _yield$api$loginOut, code, goal_login_router_path, goal_login_router_query;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.loginOut();\n            case 2:\n              _yield$api$loginOut = _context.sent;\n              code = _yield$api$loginOut.code;\n              if (code === 200) {\n                sessionStorage.clear();\n                goal_login_router_path = localStorage.getItem('goal_login_router_path');\n                if (goal_login_router_path) {\n                  goal_login_router_query = localStorage.getItem('goal_login_router_query') || '';\n                  router.push({\n                    path: goal_login_router_path,\n                    query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\n                  });\n                } else {\n                  router.push({\n                    path: '/LoginView'\n                  });\n                }\n                store.commit('setState');\n                globalReadOpenConfig();\n                // store.state.socket.disconnect()\n                // store.state.socket = null\n                ElMessage({\n                  message: text,\n                  showClose: true,\n                  type: 'success'\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function loginOut(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      router,\n      store,\n      mainTop,\n      navBottom,\n      exitIcon,\n      regionName,\n      toolId,\n      toolName,\n      navList,\n      handleToolClick,\n      handleClick,\n      handleExit,\n      loginOut,\n      get api() {\n        return api;\n      },\n      get config() {\n        return config;\n      },\n      ref,\n      computed,\n      get useRouter() {\n        return useRouter;\n      },\n      get useStore() {\n        return useStore;\n      },\n      get globalReadOpenConfig() {\n        return globalReadOpenConfig;\n      },\n      get user() {\n        return user;\n      },\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get systemNameAreaPrefix() {\n        return systemNameAreaPrefix;\n      },\n      get AiToolBoxElement() {\n        return AiToolBoxElement;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "config", "ref", "computed", "useRouter", "useStore", "globalReadOpenConfig", "user", "systemLogo", "systemName", "systemNameAreaPrefix", "AiToolBoxElement", "ElMessageBox", "ElMessage", "exitIcon", "router", "store", "mainTop", "API_URL", "areaId", "navBottom", "regionName", "toolId", "toolName", "navList", "id", "title", "tool", "handleToolClick", "handleClick", "handleExit", "confirm", "confirmButtonText", "cancelButtonText", "loginOut", "message", "_ref2", "_callee", "text", "_yield$api$loginOut", "code", "goal_login_router_path", "goal_login_router_query", "_callee$", "_context", "sessionStorage", "clear", "localStorage", "getItem", "path", "query", "JSON", "parse", "commit", "showClose", "_x"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiToolBox/GlobalAiToolBox.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"GlobalAiToolBox\">\r\n    <el-aside class=\"GlobalAiToolBoxNav\">\r\n      <div class=\"GlobalAiToolBoxHead\">\r\n        <div class=\"GlobalAiToolBoxLogo\">\r\n          <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n        </div>\r\n        <div class=\"GlobalAiToolBoxName ellipsis\">\r\n          {{ systemNameAreaPrefix === 'true' ? regionName : '' }}{{ systemName }}\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalAiToolBoxTabBody\">\r\n        <div class=\"GlobalAiToolBoxTab\">\r\n          <div class=\"GlobalAiToolBoxTabIcon\"></div>\r\n          <div class=\"GlobalAiToolBoxTabTitle\" @click=\"handleClick\">首页</div>\r\n        </div>\r\n      </div>\r\n      <el-scrollbar\r\n        class=\"GlobalAiToolBoxNav\"\r\n        :style=\"`background: url('${navBottom}') no-repeat;background-size: 100% auto;background-position: bottom center;`\">\r\n        <div class=\"GlobalAiToolBoxNavItem\" v-for=\"item in navList\" :key=\"item.id\">\r\n          <div class=\"GlobalAiToolBoxNavTitle\">{{ item.title }}</div>\r\n          <div class=\"GlobalAiToolBoxNavList\">\r\n            <div\r\n              class=\"GlobalAiToolBoxNavToolItem\"\r\n              v-for=\"tool in item.tool\"\r\n              :key=\"tool.id\"\r\n              :class=\"{ 'is-active': toolId == tool.id }\"\r\n              @click=\"handleToolClick(tool)\">\r\n              <div class=\"GlobalAiToolBoxNavToolItemIcon\">\r\n                <el-icon><Menu /></el-icon>\r\n              </div>\r\n              <div class=\"GlobalAiToolBoxNavToolItemTitle\">{{ tool.name }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n    </el-aside>\r\n    <el-main\r\n      class=\"GlobalAiToolBoxMain\"\r\n      :style=\"`background: url('${mainTop}') no-repeat #f7f7f7;background-size: 100% auto;background-position: top right;`\">\r\n      <div class=\"GlobalAiToolBoxMainHead\">\r\n        <div class=\"GlobalAiToolBoxMainHeadLeft\">\r\n          <div class=\"GlobalAiToolBoxMainHeadItem\">{{ toolName }}</div>\r\n        </div>\r\n        <div class=\"GlobalAiToolBoxMainHeadRight\">\r\n          <div class=\"GlobalAiToolBoxUser\">\r\n            <el-image :src=\"user.image\" fit=\"cover\" />\r\n            <span class=\"forbidSelect\">{{ user.userName }}</span>\r\n          </div>\r\n          <div class=\"GlobalAiToolBoxExit\" v-html=\"exitIcon\" @click=\"handleExit\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiToolBoxBody\">\r\n        <keep-alive>\r\n          <component :is=\"AiToolBoxElement[toolId]\" />\r\n        </keep-alive>\r\n      </div>\r\n    </el-main>\r\n  </el-container>\r\n</template>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport config from 'common/config'\r\nimport { ref, computed } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod.js'\r\nimport { user, systemLogo, systemName, systemNameAreaPrefix } from 'common/js/system_var.js'\r\nimport { AiToolBoxElement } from './AiToolBox.js'\r\nimport { ElMessageBox, ElMessage } from 'element-plus'\r\nconst router = useRouter()\r\nconst store = useStore()\r\nconst mainTop = computed(() => `${config.API_URL}/pageImg/open/GlobalAiToolBoxMainTop?areaId=${user.value.areaId}`)\r\nconst navBottom = computed(() => `${config.API_URL}/pageImg/open/GlobalAiToolBoxNavBottom?areaId=${user.value.areaId}`)\r\nconst exitIcon = `<svg t=\"1739268585892\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2352\" width=\"24\" height=\"24\"><path d=\"M0 192v640c0 70.7 57.3 128 128 128h352c17.7 0 32-14.3 32-32s-14.3-32-32-32H128c-35.3 0-64-28.7-64-64V192c0-35.3 28.7-64 64-64h352c17.7 0 32-14.3 32-32s-14.3-32-32-32H128C57.3 64 0 121.3 0 192z\" p-id=\"2353\" fill=\"#666666\"></path><path d=\"M1013.3 488.3L650.9 160.7c-41.2-37.2-106.9-8-106.9 47.5V339c0 4.4-3.6 8-8 8H224c-17.7 0-32 14.3-32 32v266c0 17.7 14.3 32 32 32h312c4.4 0 8 3.6 8 8v130.9c0 55.5 65.8 84.7 106.9 47.5l362.4-327.6c14.1-12.8 14.1-34.8 0-47.5zM256 597V427c0-8.8 7.2-16 16-16h304c17.7 0 32-14.3 32-32V244.9c0-13.9 16.4-21.2 26.7-11.9L938 506.1c3.5 3.2 3.5 8.7 0 11.9L634.7 791c-10.3 9.3-26.7 2-26.7-11.9V645c0-17.7-14.3-32-32-32H272c-8.8 0-16-7.2-16-16z\" p-id=\"2354\" fill=\"#666666\"></path></svg>`\r\nconst regionName = ref('')\r\nconst toolId = ref('IntelligentErrorCorrection')\r\nconst toolName = ref('智能纠错')\r\nconst navList = ref([\r\n  {\r\n    id: '1',\r\n    title: '工作通用工具',\r\n    tool: [\r\n      { id: 'IntelligentErrorCorrection', name: '智能纠错' },\r\n      { id: 'OneClickLayout', name: '一件排版' },\r\n      { id: 'ContentExtraction', name: '内容提炼' },\r\n      { id: 'IntelligentManuscriptMerging', name: '智能合稿' },\r\n      { id: 'TextComparison', name: '文本比对' },\r\n      { id: 'TextPolishing', name: '文本润色' },\r\n      { id: 'TextExpansion', name: '文本扩写' },\r\n      { id: 'TextContinuation', name: '文本续写' },\r\n      { id: 'TextRewrite', name: '文本重写' },\r\n      { id: 'TextRecognition', name: '文本识别' }\r\n    ]\r\n  },\r\n  {\r\n    id: '2',\r\n    title: '业务专用工具',\r\n    tool: [{ id: 'ProposalAuxiliaryWriting', name: '提案辅助撰写' }]\r\n  }\r\n])\r\nconst handleToolClick = (tool) => {\r\n  toolId.value = tool.id\r\n  toolName.value = tool.name\r\n}\r\nconst handleClick = () => {\r\n  router.push('/')\r\n}\r\nconst handleExit = () => {\r\n  ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  })\r\n    .then(() => {\r\n      loginOut('已安全退出！')\r\n    })\r\n    .catch(() => {\r\n      ElMessage({ type: 'info', message: '已取消退出' })\r\n    })\r\n}\r\nconst loginOut = async (text) => {\r\n  const { code } = await api.loginOut()\r\n  if (code === 200) {\r\n    sessionStorage.clear()\r\n    const goal_login_router_path = localStorage.getItem('goal_login_router_path')\r\n    if (goal_login_router_path) {\r\n      const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''\r\n      router.push({\r\n        path: goal_login_router_path,\r\n        query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\r\n      })\r\n    } else {\r\n      router.push({ path: '/LoginView' })\r\n    }\r\n    store.commit('setState')\r\n    globalReadOpenConfig()\r\n    // store.state.socket.disconnect()\r\n    // store.state.socket = null\r\n    ElMessage({ message: text, showClose: true, type: 'success' })\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiToolBox {\r\n  width: 100%;\r\n  height: 100%;\r\n  .GlobalAiToolBoxNav {\r\n    width: calc((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four));\r\n    height: 100%;\r\n    background: var(--zy-el-color-primary);\r\n\r\n    .GlobalAiToolBoxHead {\r\n      height: 78px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 var(--zy-distance-two);\r\n\r\n      .GlobalAiToolBoxLogo {\r\n        width: 50px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .GlobalAiToolBoxName {\r\n        width: calc(100% - 40px);\r\n        color: #fff;\r\n        font-weight: bold;\r\n        font-size: 22px;\r\n        padding-left: 12px;\r\n      }\r\n    }\r\n    .GlobalAiToolBoxTabBody {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding-bottom: var(--zy-distance-one);\r\n      .GlobalAiToolBoxTab {\r\n        width: 182px;\r\n        height: 36px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding-right: var(--zy-font-name-distance-five);\r\n        position: relative;\r\n        &::before {\r\n          content: '';\r\n          width: 100%;\r\n          height: 100%;\r\n          position: absolute;\r\n          top: 0;\r\n          left: 0;\r\n          border-radius: 18px;\r\n          box-sizing: border-box;\r\n          border: 1px solid #ffffff;\r\n          z-index: 1;\r\n        }\r\n        .GlobalAiToolBoxTabIcon {\r\n          width: 110px;\r\n          height: 36px;\r\n          border-radius: 18px;\r\n          background: url('../img/global_ai_tool_box_icon.png') no-repeat #e0e8ff;\r\n          background-size: 72px auto;\r\n          background-position: center center;\r\n          position: relative;\r\n          z-index: 2;\r\n        }\r\n        .GlobalAiToolBoxTabTitle {\r\n          width: calc(100% - 110px);\r\n          cursor: pointer;\r\n          color: #fff;\r\n          text-align: center;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          position: relative;\r\n          z-index: 2;\r\n        }\r\n      }\r\n    }\r\n    .GlobalAiToolBoxNav {\r\n      width: 100%;\r\n      height: calc(100% - (78px + 36px + var(--zy-distance-one)));\r\n      .GlobalAiToolBoxNavItem {\r\n        width: 100%;\r\n        padding: 0 var(--zy-distance-two);\r\n        .GlobalAiToolBoxNavTitle {\r\n          width: 100%;\r\n          color: #fff;\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n        .GlobalAiToolBoxNavList {\r\n          width: 100%;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding: var(--zy-distance-four) 0;\r\n          .GlobalAiToolBoxNavToolItem {\r\n            width: 112px;\r\n            height: 112px;\r\n            display: flex;\r\n            align-items: center;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n            border: 1px solid transparent;\r\n            background: rgba(255, 255, 255, 0.3);\r\n            border-radius: var(--el-border-radius-base);\r\n            border-image: linear-gradient(131deg, rgba(255, 255, 255, 0.2), rgba(224, 232, 255, 0.1)) 1 1;\r\n            margin-bottom: var(--zy-distance-four);\r\n            cursor: pointer;\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.9);\r\n              border: 1px solid rgba(255, 255, 255, 1);\r\n              .GlobalAiToolBoxNavToolItemTitle {\r\n                color: var(--zy-el-text-color-primary);\r\n              }\r\n            }\r\n            &.is-active {\r\n              background: rgba(255, 255, 255, 1);\r\n              border: 1px solid rgba(255, 255, 255, 1);\r\n              .GlobalAiToolBoxNavToolItemIcon {\r\n                background: var(--zy-el-color-primary);\r\n              }\r\n              .GlobalAiToolBoxNavToolItemTitle {\r\n                color: var(--zy-el-text-color-primary);\r\n              }\r\n            }\r\n            .GlobalAiToolBoxNavToolItemIcon {\r\n              width: 52px;\r\n              height: 52px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              background: var(--zy-el-color-primary-light-3);\r\n              border-radius: var(--el-border-radius-base);\r\n              margin: var(--zy-font-text-distance-five) 0;\r\n              .zy-el-icon {\r\n                font-size: 38px;\r\n                color: #fff;\r\n              }\r\n            }\r\n            .GlobalAiToolBoxNavToolItemTitle {\r\n              width: 100%;\r\n              color: #fff;\r\n              text-align: center;\r\n              font-size: var(--zy-text-font-size);\r\n              line-height: var(--zy-line-height);\r\n              padding-top: var(--zy-font-text-distance-five);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .GlobalAiToolBoxMain {\r\n    width: calc(100% - ((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four)));\r\n    height: 100%;\r\n    padding: 0;\r\n    .GlobalAiToolBoxMainHead {\r\n      width: 100%;\r\n      height: 68px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 0 var(--zy-distance-two);\r\n      padding-top: 10px;\r\n\r\n      .GlobalAiToolBoxUser {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-right: var(--zy-distance-two);\r\n        cursor: pointer;\r\n\r\n        .zy-el-image {\r\n          height: 38px;\r\n          width: 38px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        span {\r\n          margin-left: 8px;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n      }\r\n      .GlobalAiToolBoxMainHeadLeft {\r\n        display: flex;\r\n        align-items: center;\r\n        .GlobalAiToolBoxMainHeadItem {\r\n          width: 100%;\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n          padding: var(--zy-font-name-distance-five);\r\n          border-bottom: 3px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n      .GlobalAiToolBoxMainHeadRight {\r\n        display: flex;\r\n        align-items: center;\r\n        .GlobalAiToolBoxExit {\r\n          width: 30px;\r\n          height: 30px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n    .AiToolBoxBody {\r\n      width: 100%;\r\n      height: calc(100% - 68px);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAgEA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AACnC,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAEC,oBAAoB,QAAQ,yBAAyB;AAC5F,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,YAAY,EAAEC,SAAS,QAAQ,cAAc;AAKtD,IAAMC,QAAQ,GAAG,+1BAA+1B;;;;;;IAJh3B,IAAMC,MAAM,GAAGX,SAAS,CAAC,CAAC;IAC1B,IAAMY,KAAK,GAAGX,QAAQ,CAAC,CAAC;IACxB,IAAMY,OAAO,GAAGd,QAAQ,CAAC;MAAA,OAAM,GAAGF,MAAM,CAACiB,OAAO,+CAA+CX,IAAI,CAAC3G,KAAK,CAACuH,MAAM,EAAE;IAAA,EAAC;IACnH,IAAMC,SAAS,GAAGjB,QAAQ,CAAC;MAAA,OAAM,GAAGF,MAAM,CAACiB,OAAO,iDAAiDX,IAAI,CAAC3G,KAAK,CAACuH,MAAM,EAAE;IAAA,EAAC;IAEvH,IAAME,UAAU,GAAGnB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMoB,MAAM,GAAGpB,GAAG,CAAC,4BAA4B,CAAC;IAChD,IAAMqB,QAAQ,GAAGrB,GAAG,CAAC,MAAM,CAAC;IAC5B,IAAMsB,OAAO,GAAGtB,GAAG,CAAC,CAClB;MACEuB,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,CACJ;QAAEF,EAAE,EAAE,4BAA4B;QAAEpD,IAAI,EAAE;MAAO,CAAC,EAClD;QAAEoD,EAAE,EAAE,gBAAgB;QAAEpD,IAAI,EAAE;MAAO,CAAC,EACtC;QAAEoD,EAAE,EAAE,mBAAmB;QAAEpD,IAAI,EAAE;MAAO,CAAC,EACzC;QAAEoD,EAAE,EAAE,8BAA8B;QAAEpD,IAAI,EAAE;MAAO,CAAC,EACpD;QAAEoD,EAAE,EAAE,gBAAgB;QAAEpD,IAAI,EAAE;MAAO,CAAC,EACtC;QAAEoD,EAAE,EAAE,eAAe;QAAEpD,IAAI,EAAE;MAAO,CAAC,EACrC;QAAEoD,EAAE,EAAE,eAAe;QAAEpD,IAAI,EAAE;MAAO,CAAC,EACrC;QAAEoD,EAAE,EAAE,kBAAkB;QAAEpD,IAAI,EAAE;MAAO,CAAC,EACxC;QAAEoD,EAAE,EAAE,aAAa;QAAEpD,IAAI,EAAE;MAAO,CAAC,EACnC;QAAEoD,EAAE,EAAE,iBAAiB;QAAEpD,IAAI,EAAE;MAAO,CAAC;IAE3C,CAAC,EACD;MACEoD,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,CAAC;QAAEF,EAAE,EAAE,0BAA0B;QAAEpD,IAAI,EAAE;MAAS,CAAC;IAC3D,CAAC,CACF,CAAC;IACF,IAAMuD,eAAe,GAAG,SAAlBA,eAAeA,CAAID,IAAI,EAAK;MAChCL,MAAM,CAAC1H,KAAK,GAAG+H,IAAI,CAACF,EAAE;MACtBF,QAAQ,CAAC3H,KAAK,GAAG+H,IAAI,CAACtD,IAAI;IAC5B,CAAC;IACD,IAAMwD,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBd,MAAM,CAACnD,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IACD,IAAMkE,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvBlB,YAAY,CAACmB,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QAC9CC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBlH,IAAI,EAAE;MACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;QACV4F,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,CAAC,CACD3C,KAAK,CAAC,YAAM;QACXsB,SAAS,CAAC;UAAE9F,IAAI,EAAE,MAAM;UAAEoH,OAAO,EAAE;QAAQ,CAAC,CAAC;MAC/C,CAAC,CAAC;IACN,CAAC;IACD,IAAMD,QAAQ;MAAA,IAAAE,KAAA,GAAAzC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+D,QAAOC,IAAI;QAAA,IAAAC,mBAAA,EAAAC,IAAA,EAAAC,sBAAA,EAAAC,uBAAA;QAAA,OAAAxJ,mBAAA,GAAAuB,IAAA,UAAAkI,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA7D,IAAA,GAAA6D,QAAA,CAAAxF,IAAA;YAAA;cAAAwF,QAAA,CAAAxF,IAAA;cAAA,OACH4C,GAAG,CAACkC,QAAQ,CAAC,CAAC;YAAA;cAAAK,mBAAA,GAAAK,QAAA,CAAA/F,IAAA;cAA7B2F,IAAI,GAAAD,mBAAA,CAAJC,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBK,cAAc,CAACC,KAAK,CAAC,CAAC;gBAChBL,sBAAsB,GAAGM,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;gBAC7E,IAAIP,sBAAsB,EAAE;kBACpBC,uBAAuB,GAAGK,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE;kBACrFjC,MAAM,CAACnD,IAAI,CAAC;oBACVqF,IAAI,EAAER,sBAAsB;oBAC5BS,KAAK,EAAER,uBAAuB,GAAGS,IAAI,CAACC,KAAK,CAACV,uBAAuB,CAAC,GAAG,CAAC;kBAC1E,CAAC,CAAC;gBACJ,CAAC,MAAM;kBACL3B,MAAM,CAACnD,IAAI,CAAC;oBAAEqF,IAAI,EAAE;kBAAa,CAAC,CAAC;gBACrC;gBACAjC,KAAK,CAACqC,MAAM,CAAC,UAAU,CAAC;gBACxB/C,oBAAoB,CAAC,CAAC;gBACtB;gBACA;gBACAO,SAAS,CAAC;kBAAEsB,OAAO,EAAEG,IAAI;kBAAEgB,SAAS,EAAE,IAAI;kBAAEvI,IAAI,EAAE;gBAAU,CAAC,CAAC;cAChE;YAAC;YAAA;cAAA,OAAA6H,QAAA,CAAA1D,IAAA;UAAA;QAAA,GAAAmD,OAAA;MAAA,CACF;MAAA,gBApBKH,QAAQA,CAAAqB,EAAA;QAAA,OAAAnB,KAAA,CAAAvC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAoBb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}