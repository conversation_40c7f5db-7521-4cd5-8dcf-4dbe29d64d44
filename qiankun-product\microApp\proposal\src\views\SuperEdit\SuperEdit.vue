<template>
  <el-scrollbar always class="SuperEdit" v-loading="loading" :lement-loading-text="loadingText">
    <div class="SuperEditBody">
      <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
        <div class="globalFormName">基本信息</div>
        <el-form-item label="提案标题" prop="title" class="globalFormTitle">
          <el-input v-model="form.title" placeholder="请输入提案标题" show-word-limit :maxlength="suggestTitleNumber"
            clearable />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="提案者" prop="suggestUserId">
          <input-select-person v-model="form.suggestUserId" placeholder="请选择提案者" :tabCode="tabCode"
            soleKey="suggestUserId" />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'team'" label="提案者" prop="delegationId">
          <el-select v-model="form.delegationId" placeholder="请选择集体提案单位" clearable>
            <el-option v-for="item in delegationData" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="提交时间">
          <xyl-date-picker v-model="form.submitDate" type="datetime" value-format="x" placeholder="选择提交时间" />
        </el-form-item>

        <el-form-item label="提案所属届次" prop="termYearId">
          <el-select v-model="form.termYearId" placeholder="请选择提案所属届次" clearable>
            <el-option v-for="item in termYearData" :key="item.termYearId"
              :label="item.termYear?.circlesType?.label + item.termYear?.boutType?.label" :value="item.termYearId" />
          </el-select>
        </el-form-item>

        <el-form-item label="创建人" prop="createBy">
          <input-select-person v-model="form.createBy" placeholder="请选择创建人" :tabCode="tabCode" soleKey="createBy" />
        </el-form-item>

        <el-form-item label="创建时间">
          <xyl-date-picker v-model="form.createDate" type="datetime" value-format="x" placeholder="选择提交时间" />
        </el-form-item>
        <!--
        <el-form-item label="提案所属年份">
          <xyl-date-picker v-model="form.year"
                          type="year"
                          disabled
                          value-format="x"
                          format="YYYY"
                          placeholder="请选择年" />
        </el-form-item> -->

        <el-form-item label="会议类型" prop="suggestMeetingType">
          <el-select v-model="form.suggestMeetingType" placeholder="请选择会议类型" clearable>
            <el-option v-for="item in suggestMeetingTypeData" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="提案编号" prop="serialNumber">
          <el-input v-model="form.serialNumber" placeholder="请输入提案编号" show-word-limit :maxlength="20" clearable />
        </el-form-item>

        <el-form-item label="流水号" prop="streamNumber">
          <el-input v-model="form.streamNumber" placeholder="请输入流水号"
            @input="form.streamNumber = validNum(form.streamNumber)" show-word-limit :maxlength="20" clearable />
        </el-form-item>

        <el-form-item label="提案流程状态" prop="flowStreamId">
          <el-select v-model="form.flowStreamId" placeholder="请选择提案流程状态" clearable>
            <el-option v-for="item in historyStreams" :key="item.id" :label="item.nodeName" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="提案大类" prop="bigThemeId">
          <el-select v-model="form.bigThemeId" placeholder="请选择提案大类" @change="SuggestBigTypeChange" clearable>
            <el-option v-for="item in BigTypeArr" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="提案小类" prop="smallThemeId">
          <el-select v-model="form.smallThemeId" placeholder="请选择提案小类" clearable>
            <el-option v-for="item in SmallTypeArr" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item> -->

        <el-form-item label="是否重点提案" prop="isMajorSuggestion">
          <el-radio-group v-model="form.isMajorSuggestion">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否公开提案" prop="isOpen">
          <el-radio-group v-model="form.isOpen">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <!--<el-form-item label="是否优秀提案"
                      prop="delegationId">
          <el-radio-group v-model="form.isTop">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item> -->

        <template v-if="showVerify">
          <div class="globalFormName">审查信息</div>
          <el-form-item label="审查人" prop="verifyHandleUserId">
            <input-select-person v-model="form.verifyHandleUserId" placeholder="请选择审查人" :tabCode="tabCode"
              soleKey="verifyHandleUserId" />
          </el-form-item>

          <el-form-item label="审查时间">
            <xyl-date-picker v-model="form.verifyHandleTime" type="datetime" value-format="x" placeholder="选择审查时间" />
          </el-form-item>

          <el-form-item label="审查意见" prop="verifyHandleContent" class="globalFormTitle">
            <el-input v-model="form.verifyHandleContent" placeholder="请输入审查意见" show-word-limit :maxlength="200"
              clearable />
          </el-form-item>
        </template>
        <template v-if="showFirstSubmitHandle">
          <div class="globalFormName">交办信息</div>
          <el-form-item label="交办人" prop="firstSubmitHandleHandleUserId">
            <input-select-person v-model="form.firstSubmitHandleHandleUserId" placeholder="请选择交办人" :tabCode="tabCode"
              soleKey="firstSubmitHandleHandleUserId" />
          </el-form-item>

          <el-form-item label="交办时间">
            <xyl-date-picker v-model="form.firstSubmitHandleHandleTime" type="datetime" value-format="x"
              placeholder="请选择交办时间" />
          </el-form-item>

          <el-form-item label="交办意见" prop="firstSubmitHandleHandleContent" class="globalFormTitle">
            <el-input v-model="form.firstSubmitHandleHandleContent" placeholder="请输入交办意见" show-word-limit
              :maxlength="200" clearable />
          </el-form-item>
        </template>
        <template v-if="showPreAssignSubmitHandle">
          <div class="globalFormName">交办信息</div>
          <el-form-item label="预交办人" prop="PreAssignHandleUserId">
            <input-select-person v-model="form.PreAssignHandleUserId" placeholder="请选择预交办人" :tabCode="tabCode"
              soleKey="PreAssignHandleUserId" />
          </el-form-item>
          <el-form-item label="预交办时间">
            <xyl-date-picker v-model="form.PreAssignHandleTime" type="datetime" value-format="x"
              placeholder="请选择预交办时间" />
          </el-form-item>
          <el-form-item label="预交办意见" prop="PreAssignHandleContent" class="globalFormTitle">
            <el-input v-model="form.PreAssignHandleContent" placeholder="请输入预交办意见" show-word-limit :maxlength="200"
              clearable />
          </el-form-item>
          <el-form-item label="签收截止时间">
            <xyl-date-picker v-model="form.confirmStopDate" type="datetime" value-format="x" placeholder="请选择签收截止时间" />
          </el-form-item>
          <el-form-item label="是否签收">
            <el-radio-group v-model="form.hasConfirm">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="签收时间">
            <xyl-date-picker v-model="form.confirmDate" type="datetime" value-format="x" placeholder="请选择签收时间" />
          </el-form-item>
        </template>
        <template v-if="showSubmitHandle">
          <div class="globalFormName">交办信息</div>
          <el-form-item label="交办人" prop="submitHandleHandleUserId">
            <input-select-person v-model="form.submitHandleHandleUserId" placeholder="请选择交办人" :tabCode="tabCode"
              soleKey="submitHandleHandleUserId" />
          </el-form-item>

          <el-form-item label="交办时间">
            <xyl-date-picker v-model="form.submitHandleHandleTime" type="datetime" value-format="x"
              placeholder="请选择交办时间" />
          </el-form-item>

          <el-form-item label="交办意见" prop="submitHandleHandleContent" class="globalFormTitle">
            <el-input v-model="form.submitHandleHandleContent" placeholder="请输入交办意见" show-word-limit :maxlength="200"
              clearable />
          </el-form-item>
        </template>

        <template v-if="showHandlingMassing">
          <!-- <el-form-item label="办理方式"
                        prop="handleOfficeType">
            <el-select v-model="form.handleOfficeType"
                       placeholder="请选择办理方式"
                       clearable>
              <el-option label="主办/协办"
                         value="main_assist" />
              <el-option label="分办"
                         value="publish" />
              <el-option label="置空"
                         value="null" />
            </el-select>
          </el-form-item> -->

          <el-form-item label="答复截止时间">
            <xyl-date-picker v-model="form.answerStopDate" type="datetime" value-format="x" placeholder="选择答复截止时间" />
          </el-form-item>
          <el-form-item label="调整截止时间">
            <xyl-date-picker v-model="form.adjustStopDate" type="datetime" value-format="x" placeholder="选择调整截止时间" />
          </el-form-item>

          <!-- <template v-if="form.handleOfficeType === 'main_assist'">
          <el-form-item label="主办单位"
                        prop="mainHandleOfficeId"
                        class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.mainHandleOfficeId"
                                        :filterId="form.handleOfficeIds"
                                        :max="1"></suggest-simple-select-unit>
          </el-form-item>
        </template>
<template v-if="form.handleOfficeType === 'main_assist'">
          <el-form-item label="协办单位"
                        class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.handleOfficeIds"
                                        :filterId="form.mainHandleOfficeId"></suggest-simple-select-unit>
          </el-form-item>
        </template>
<template v-if="form.handleOfficeType === 'publish'">
          <el-form-item label="分办单位"
                        prop="handleOfficeIds"
                        class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.handleOfficeIds"></suggest-simple-select-unit>
          </el-form-item>
        </template> -->

          <div class="globalFormName">办理信息</div>
          <el-form-item label="实际答复时间">
            <xyl-date-picker v-model="form.massingAnswerDate" type="datetime" value-format="x" placeholder="选择实际答复时间" />
          </el-form-item>
        </template>
        <div class="globalFormButton">
          <el-button type="primary" @click="submitForm(formRef, 0)">提交</el-button>
          <el-button @click="resetForm">取消</el-button>
        </div>
      </el-form>
    </div>
  </el-scrollbar>
</template>
<script>
export default { name: 'SuperEdit' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onActivated, computed } from 'vue'
import { useRoute } from 'vue-router'
import { user } from 'common/js/system_var.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage } from 'element-plus'
import { validNum } from 'common/js/utils.js'
import { selectUser } from 'common/js/system_var.js'

const route = useRoute()
const loading = ref(false)
const loadingText = ref('')
const formRef = ref()
const form = reactive({
  suggestSubmitWay: 'cppcc_member',
  title: '', // 提案标题
  suggestUserId: '',
  cardNumber: '',
  delegationName: '',
  mobile: '',
  callAddress: '',
  delegationId: '',
  content: '',
  createBy: '',
  submitDate: '',
  createDate: '',
  termYearId: '',
  streamNumber: '',
  bigThemeId: '',
  smallThemeId: '',
  suggestMeetingType: '',
  isMajorSuggestion: '',
  handlingMassingId: '',
  isOpen: '',
  serialNumber: '',
  verifyHandleUserId: '',
  verifyHandleTime: '',
  answerStopDate: '',
  adjustStopDate: '',
  massingAnswerDate: '',
  submitHandleHandleId: '',
  handleOfficeType: '',
  verifyHandleId: '',
  verifyHandleContent: [],
  firstSubmitHandleHandleId: '',
  firstSubmitHandleHandleUserId: '',
  firstSubmitHandleHandleTime: '',
  firstSubmitHandleHandleContent: '',
  submitHandleHandleUserId: '',
  submitHandleHandleTime: '',
  submitHandleHandleContent: '',
  PreAssignHandleId: '',
  PreAssignHandleUserId: '',
  PreAssignHandleTime: '',
  PreAssignHandleContent: '',
  confirmStopDate: '',
  hasConfirm: '',
  confirmDate: ''
})
const rules = reactive({
  // suggestSubmitWay: [{ required: true, message: '请选择提案提交类型', trigger: ['blur', 'change'] }],
  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }]
  // content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }],
  // suggestUserId: [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }],
  // delegationId: [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]
})

const suggestMeetingTypeData = ref([
  { id: 'meeting', name: '大会' },
  { id: 'usual', name: '平时' }
])
const termYearData = ref([])
const suggestTitleNumber = ref(30)
const delegationData = ref([])
const tabCode = computed(() => selectUser.value.role)

const userParams = ref({})
const historyStreams = ref([])
const BigTypeArr = ref([])
const SmallTypeArr = ref([])
const showVerify = ref(false)
const showSubmitHandle = ref(false)
const showHandlingMassing = ref(false)
const showFirstSubmitHandle = ref(false)
const showPreAssignSubmitHandle = ref(false)
onActivated(() => {
  suggestionThemeSelect()
  suggestionTermYearList()

  globalReadConfig()

  if (route.query.id) {
    suggestionSuperDetail()
  } else {
    tabCode.value = ['npcMember']
    if (user.value.specialRoleKeys.includes('delegation_manager')) {
      tabCode.value = ['delegationManagerMemberChoose']
    }
    if (user.value.specialRoleKeys.includes('npc_contact_committee') || user.value.specialRoleKeys.includes('admin')) {
      tabCode.value = ['npcMember']
    }
    if (user.value.specialRoleKeys.includes('cppcc_member')) {
      form.suggestUserId = user.value.id
      if (
        user.value.specialRoleKeys.includes('delegation_manager') ||
        user.value.specialRoleKeys.includes('npc_contact_committee') ||
        user.value.specialRoleKeys.includes('admin')
      ) {
        // } else {
        //   form.suggestUserId = user.value.id
        //   npcMemberInfo(user.value.id)
      }
    }
    delegationSelect()
  }
})

const suggestionThemeSelect = async () => {
  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })
  var { data } = res
  BigTypeArr.value = data
}
const SuggestBigTypeChange = () => {
  if (form.bigThemeId) {
    for (let index = 0; index < BigTypeArr.value.length; index++) {
      const item = BigTypeArr.value[index]
      if (item.id === form.bigThemeId) {
        if (!item.children.map((v) => v.id).includes(form.smallThemeId)) {
          form.smallThemeId = ''
        }
        SmallTypeArr.value = item.children
      }
    }
  } else {
    form.smallThemeId = ''
    SmallTypeArr.value = []
  }
}

const suggestionTermYearList = async () => {
  var params = {
    pageNo: 1,
    pageSize: 100
  }
  const { data } = await api.suggestionTermYearList(params)
  termYearData.value = data || []
}

const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: ['suggestTitleNumber'] })
  if (data.suggestTitleNumber) {
    suggestTitleNumber.value = Number(data.suggestTitleNumber)
  }
}

const suggestionSuperDetail = async () => {
  try {
    const res = await api.suggestionSuperDetail({ detailId: route.query.id })
    var { data } = res
    form.suggestSubmitWay = data.suggestSubmitWay
    if (form.suggestSubmitWay === 'cppcc_member') {
      tabCode.value = ['npcMember']
      form.suggestUserId = data.suggestUserId
    }
    if (form.suggestSubmitWay === 'team') {
      form.delegationId = data.delegationId
      delegationData.value = data.delegationId ? [{ id: data.delegationId, name: data.delegationName }] : []
    }
    submitTypeChange()
    form.title = data.title
    form.content = data.content
    form.createBy = data.createBy
    form.createDate = data.createDate
    form.submitDate = data.submitDate
    form.termYearId = data.termYearId
    form.streamNumber = data.streamNumber
    form.bigThemeId = data.bigThemeId
    form.smallThemeId = data.smallThemeId
    form.isMajorSuggestion = data.isMajorSuggestion
    form.isOpen = data.isOpen
    form.serialNumber = data.serialNumber
    form.flowStreamId = data.flowStreamId
    historyStreams.value = data.historyStreams
    // 审查信息
    showVerify.value = data.verify !== null
    form.verifyHandleId = data?.verify?.processStreamId || ''
    form.verifyHandleUserId = data?.verify?.handleUserId || ''
    form.verifyHandleTime = data?.verify?.handleTime || ''
    form.verifyHandleContent = data?.verify?.handleContent || ''
    // 第一次交办信息
    showFirstSubmitHandle.value = data.firstSubmitHandle !== null
    form.firstSubmitHandleHandleId = data?.firstSubmitHandle?.processStreamId || ''
    form.firstSubmitHandleHandleUserId = data?.firstSubmitHandle?.handleUserId || ''
    form.firstSubmitHandleHandleTime = data?.firstSubmitHandle?.handleTime || ''
    form.firstSubmitHandleHandleContent = data?.firstSubmitHandle?.handleContent || ''
    // 预交办信息
    showPreAssignSubmitHandle.value = data.preAssignSubmitHandle !== null
    form.PreAssignHandleId = data?.preAssignSubmitHandle?.processStreamId || ''
    form.PreAssignHandleUserId = data?.preAssignSubmitHandle?.handleUserId || ''
    form.PreAssignHandleTime = data?.preAssignSubmitHandle?.handleTime || ''
    form.PreAssignHandleContent = data?.preAssignSubmitHandle?.handleContent || ''
    // 交办信息
    showSubmitHandle.value = data.submitHandle !== null
    form.submitHandleHandleId = data?.submitHandle?.processStreamId || ''
    form.submitHandleHandleUserId = data?.submitHandle?.handleUserId || ''
    form.submitHandleHandleTime = data?.submitHandle?.handleTime || ''
    form.submitHandleHandleContent = data?.submitHandle?.handleContent || ''

    showHandlingMassing.value = data.handlingMassing !== null && data?.handlingMassing?.answerStopDate
    form.handlingMassingId = data?.handlingMassing?.id || ''
    form.answerStopDate = data?.handlingMassing?.answerStopDate || ''
    form.adjustStopDate = data?.handlingMassing?.adjustStopDate || ''
    form.confirmStopDate = data?.handlingMassing?.confirmStopDate || ''
    form.hasConfirm = data?.handlingMassing?.hasConfirm
    form.confirmDate = data?.handlingMassing?.confirmDate || ''
    form.massingAnswerDate = data?.handlingMassing?.massingAnswerDate || ''
    form.handleOfficeType = data?.handlingMassing?.handleOfficeType || ''

    form.suggestMeetingType = data.suggestMeetingType
    form.suggestOpenType = data.suggestOpenType?.value
    userParams.value = { authorId: form.suggestUserId, content: form.content }
    SuggestBigTypeChange()
  } catch (err) {
    if (err.code === 500) {
      if (route.query.id) {
        qiankunMicro.setGlobalState({
          closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }
        })
      }
    }
  }
}

const submitTypeChange = () => {
  if (form.suggestSubmitWay === 'cppcc_member') {
    rules.suggestUserId = [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }]
    rules.delegationId = [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]
  } else if (form.suggestSubmitWay === 'team') {
    rules.suggestUserId = [{ required: false, message: '请选择提案者', trigger: ['blur', 'change'] }]
    rules.delegationId = [{ required: true, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]
  }
}

const delegationSelect = async () => {
  const { data } = await api.delegationSelect()
  if (data.length === 1) {
    form.delegationId = data[0].id
  }
  delegationData.value = data
}

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const globalJson = async () => {
  try {
    var params = {
      flowStreamId: form.flowStreamId,
      form: {
        submitDate: form.submitDate,
        id: route.query.id,
        suggestSubmitWay: form.suggestSubmitWay,
        title: form.title, // 提案标题
        suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,
        delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,
        content: form.content,
        suggestOpenType: form.suggestOpenType,
        isMajorSuggestion: form.isMajorSuggestion,
        isOpen: form.isOpen,
        serialNumber: form.serialNumber,
        termYearId: form.termYearId,
        bigThemeId: form.bigThemeId,
        smallThemeId: form.smallThemeId,
        suggestMeetingType: form.suggestMeetingType,
        createBy: form.createBy,
        createDate: form.createDate,
        streamNumber: form.streamNumber,
        currentNodeId:
          historyStreams?.value[historyStreams.value.findIndex((v) => v.id === form.flowStreamId)]?.nodeId || ''
      }
    }
    if (showVerify.value) {
      params.verify = {
        processStreamId: form.verifyHandleId,
        handleUserId: form.verifyHandleUserId,
        handleTime: form.verifyHandleTime,
        handleContent: form.verifyHandleContent
      }
    }
    if (showFirstSubmitHandle.value) {
      params.firstSubmitHandle = {
        processStreamId: form.firstSubmitHandleHandleId,
        handleUserId: form.firstSubmitHandleHandleUserId,
        handleTime: form.firstSubmitHandleHandleTime,
        handleContent: form.firstSubmitHandleHandleContent
      }
    }
    if (showSubmitHandle.value) {
      params.submitHandle = {
        processStreamId: form.submitHandleHandleId,
        handleUserId: form.submitHandleHandleUserId,
        handleTime: form.submitHandleHandleTime,
        handleContent: form.submitHandleHandleContent
      }
    }
    if (showPreAssignSubmitHandle.value) {
      params.preAssignSubmitHandle = {
        processStreamId: form.PreAssignHandleId,
        handleUserId: form.PreAssignHandleUserId,
        handleTime: form.PreAssignHandleTime,
        handleContent: form.PreAssignHandleContent
      }
      params.handlingMassing = {
        id: form.handlingMassingId,
        handleOfficeType: form.handleOfficeType,
        adjustStopDate: form.adjustStopDate,
        answerStopDate: form.answerStopDate,
        confirmStopDate: form.confirmStopDate,
        hasConfirm: form.hasConfirm,
        confirmDate: form.confirmDate,
        massingAnswerDate: form.massingAnswerDate
      }
    }
    if (showHandlingMassing.value) {
      params.handlingMassing = {
        id: form.handlingMassingId,
        handleOfficeType: form.handleOfficeType,
        adjustStopDate: form.adjustStopDate,
        answerStopDate: form.answerStopDate,
        confirmStopDate: form.confirmStopDate,
        hasConfirm: form.hasConfirm,
        confirmDate: form.confirmDate,
        massingAnswerDate: form.massingAnswerDate
      }
    }

    const { code } = await api.globalJson('/proposal/superEdit', params)
    if (code === 200) {
      ElMessage({ type: 'success', message: '编辑成功' })
      if (route.query.id) {
        qiankunMicro.setGlobalState({
          closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }
        })
      }
    }
  } catch (err) {
    loading.value = false
  }
}
const resetForm = () => {
  if (route.query.id) {
    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
  }
}
</script>
<style lang="scss">
.SuperEdit {
  width: 100%;
  height: 100%;

  .SuperEditBody {
    width: 990px;
    margin: 20px auto;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);

    .suggest-simple-select-unit {
      box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;
      border-radius: var(--zy-el-input-border-radius, var(--zy-el-border-radius-base));
    }
  }
}
</style>
