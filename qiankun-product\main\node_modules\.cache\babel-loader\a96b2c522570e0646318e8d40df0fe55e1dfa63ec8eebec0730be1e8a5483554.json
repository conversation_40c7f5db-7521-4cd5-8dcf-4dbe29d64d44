{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { reactive, ref, computed } from 'vue';\nimport { useStore } from 'vuex';\nimport md5 from 'crypto-js/md5';\nimport utils from 'common/js/utils.js';\nimport { appOnlyHeader } from 'common/js/system_var.js';\nimport { ElMessage } from 'element-plus';\nexport var LoginView = function LoginView() {\n  var codeValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var queryValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var store = useStore();\n  var openConfig = computed(function () {\n    return store.getters.getReadOpenConfig;\n  });\n  var uniqueId = ref('');\n  var localityId = ref('');\n  var loginAccount = ref('');\n  var loginVerifyShow = ref(false);\n  var whetherVerifyCode = ref(false);\n  var loading = ref(false);\n  var checked = ref(false);\n  var imgList = ref([]);\n  var LoginForm = ref();\n  var form = reactive({\n    account: '',\n    password: '',\n    verifyCode: ''\n  });\n  var rules = reactive({\n    account: [{\n      required: true,\n      message: '请输入账号/手机号',\n      trigger: 'blur'\n    }],\n    password: [{\n      required: true,\n      message: '请输入密码',\n      trigger: 'blur'\n    }],\n    verifyCode: [{\n      required: false,\n      message: '请输入验证码',\n      trigger: 'blur'\n    }]\n  });\n  var slideVerify = ref();\n  var disabled = ref(false);\n  var verifyCodeId = ref('');\n  var countDownText = ref('获取验证码');\n  var countDown = ref(0);\n  var timer = ref();\n  var loginQrcode = ref('');\n  var loginQrcodeId = ref('');\n  var loginQrcodeShow = ref(false);\n  var loginDisabled = ref(true);\n  var guid = function guid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      var r = Math.random() * 16 | 0,\n        v = c == 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  };\n  var generateUniqueId = function generateUniqueId(type) {\n    var navigatorInfo = window.navigator.userAgent; // 获取用户代理信息\n    var screenWidth = window.screen.width; // 获取屏幕宽度\n    var screenHeight = window.screen.height; // 获取屏幕高度\n    var language = window.navigator.language; // 获取浏览器语言\n    var platform = window.navigator.platform; // 获取操作系统平台\n    localityId.value = type ? guid() : localStorage.getItem('localityId') || guid();\n    loginVerifyShow.value = loginAccount.value && form.account ? !(loginAccount.value === form.account && localStorage.getItem('localityId')) : true;\n    return navigatorInfo + '-' + screenWidth + '-' + screenHeight + '-' + language + '-' + platform + '-' + localityId.value;\n  };\n  var handleBlur = function handleBlur() {\n    loginDisabled.value = true;\n    if (form.account) {\n      verifyLoginCode();\n    } else {\n      loginVerifyShow.value = false;\n      whetherVerifyCode.value = false;\n      rules.verifyCode = [{\n        required: false,\n        message: '请输入验证码',\n        trigger: 'blur'\n      }];\n    }\n  };\n  var globalData = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n      var _yield$api$loginImg, data;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.next = 2;\n            return api.loginImg();\n          case 2:\n            _yield$api$loginImg = _context.sent;\n            data = _yield$api$loginImg.data;\n            data.forEach(function (item) {\n              item.imgPath = api.fileURL(item.imgPath);\n            });\n            imgList.value = data;\n          case 6:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function globalData() {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  var verifyLoginCode = /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n      var type,\n        _yield$api$verifyLogi,\n        data,\n        _args2 = arguments;\n      return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            type = _args2.length > 0 && _args2[0] !== undefined ? _args2[0] : false;\n            uniqueId.value = generateUniqueId(type);\n            _context2.next = 4;\n            return api.verifyLoginCode({\n              account: form.account,\n              pcMachineCode: md5(uniqueId.value).toString()\n            });\n          case 4:\n            _yield$api$verifyLogi = _context2.sent;\n            data = _yield$api$verifyLogi.data;\n            loginDisabled.value = false;\n            whetherVerifyCode.value = data;\n            if (data) loginVerifyShow.value = data;\n            rules.verifyCode = [{\n              required: data,\n              message: '请输入验证码',\n              trigger: 'blur'\n            }];\n          case 10:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2);\n    }));\n    return function verifyLoginCode() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var handleGetVerifyCode = /*#__PURE__*/function () {\n    var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n      var _yield$api$userAccoun, data;\n      return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n        while (1) switch (_context3.prev = _context3.next) {\n          case 0:\n            if (form.account) {\n              _context3.next = 2;\n              break;\n            }\n            return _context3.abrupt(\"return\", ElMessage({\n              type: 'warning',\n              message: '请输入账号/手机号！'\n            }));\n          case 2:\n            if (form.password) {\n              _context3.next = 4;\n              break;\n            }\n            return _context3.abrupt(\"return\", ElMessage({\n              type: 'warning',\n              message: '请输入密码！'\n            }));\n          case 4:\n            _context3.next = 6;\n            return api.userAccountCheck({\n              detailId: form.account,\n              keyword: utils.encrypt(form.password, new Date().getTime(), '1')\n            });\n          case 6:\n            _yield$api$userAccoun = _context3.sent;\n            data = _yield$api$userAccoun.data;\n            if (data === '1') openVerifyCodeSend();\n          case 9:\n          case \"end\":\n            return _context3.stop();\n        }\n      }, _callee3);\n    }));\n    return function handleGetVerifyCode() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var openVerifyCodeSend = /*#__PURE__*/function () {\n    var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n      var _yield$api$openVerify, data, code;\n      return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n        while (1) switch (_context4.prev = _context4.next) {\n          case 0:\n            if (form.account) {\n              _context4.next = 2;\n              break;\n            }\n            return _context4.abrupt(\"return\", ElMessage({\n              type: 'warning',\n              message: '请输入账号/手机号！'\n            }));\n          case 2:\n            _context4.next = 4;\n            return api.openVerifyCodeSend({\n              mobile: form.account,\n              sendType: 'login'\n            });\n          case 4:\n            _yield$api$openVerify = _context4.sent;\n            data = _yield$api$openVerify.data;\n            code = _yield$api$openVerify.code;\n            if (code === 200) {\n              verifyCodeId.value = data;\n              countDown.value = 60;\n              _handleCountDown();\n              ElMessage({\n                type: 'success',\n                message: '短信验证码已发送！'\n              });\n            }\n          case 8:\n          case \"end\":\n            return _context4.stop();\n        }\n      }, _callee4);\n    }));\n    return function openVerifyCodeSend() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var _handleCountDown = function handleCountDown() {\n    if (countDown.value === 0) {\n      countDownText.value = '获取验证码';\n      countDown.value = 60;\n      return;\n    } else {\n      countDownText.value = '重新发送' + countDown.value + 'S';\n      countDown.value--;\n    }\n    setTimeout(function () {\n      _handleCountDown();\n    }, 1000);\n  };\n  var onAgain = function onAgain() {\n    ElMessage.error('检测到非人为操作的哦！!');\n    handleFefresh();\n  };\n  var onSuccess = function onSuccess() {\n    disabled.value = true;\n  };\n  var handleFefresh = function handleFefresh() {\n    var _slideVerify$value;\n    disabled.value = false;\n    (_slideVerify$value = slideVerify.value) === null || _slideVerify$value === void 0 || _slideVerify$value.refresh();\n  };\n  var submitForm = /*#__PURE__*/function () {\n    var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5(formEl, type) {\n      return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n        while (1) switch (_context5.prev = _context5.next) {\n          case 0:\n            if (formEl) {\n              _context5.next = 2;\n              break;\n            }\n            return _context5.abrupt(\"return\");\n          case 2:\n            loading.value = true;\n            _context5.next = 5;\n            return formEl.validate(function (valid) {\n              if (valid) {\n                if (!loginVerifyShow.value || disabled.value || whetherVerifyCode.value) {\n                  login(type);\n                } else {\n                  loading.value = false;\n                  ElMessage({\n                    type: 'warning',\n                    message: '请先通过验证在登录！'\n                  });\n                }\n              } else {\n                loading.value = false;\n              }\n            });\n          case 5:\n          case \"end\":\n            return _context5.stop();\n        }\n      }, _callee5);\n    }));\n    return function submitForm(_x, _x2) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  var login = /*#__PURE__*/function () {\n    var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6(type) {\n      var _yield$api$login, data, _openConfig$value, loginUserInfo;\n      return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n        while (1) switch (_context6.prev = _context6.next) {\n          case 0:\n            _context6.prev = 0;\n            _context6.next = 3;\n            return api.login(_objectSpread({\n              grant_type: 'password',\n              username: form.account,\n              password: utils.encrypt(form.password, new Date().getTime(), '1'),\n              pcMachineCode: md5(uniqueId.value).toString()\n            }, whetherVerifyCode.value ? {\n              verifyCodeId: verifyCodeId.value,\n              verifyCode: form.verifyCode\n            } : {}));\n          case 3:\n            _yield$api$login = _context6.sent;\n            data = _yield$api$login.data;\n            ElMessage({\n              message: '登录成功！',\n              showClose: true,\n              type: 'success'\n            });\n            if (data.passwordElementErrorMessage) ElMessage({\n              message: data.passwordElementErrorMessage,\n              showClose: true,\n              type: 'info'\n            });\n            if (codeValue === '/UnifyLogin') {\n              handleAuthorize(data.token);\n            } else {\n              sessionStorage.setItem('token', data.token);\n              sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`);\n              sessionStorage.setItem('expires', data.expires_in);\n              sessionStorage.setItem('expiration', data.refreshToken.expiration);\n              sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0);\n              if (((_openConfig$value = openConfig.value) === null || _openConfig$value === void 0 ? void 0 : _openConfig$value.whetherRegionSelect) === 'true') loginLastAreaId();\n              store.dispatch('loginUser', type ? '/GlobalLayoutChat' : 'login');\n              localStorage.setItem('goal_login_router_path', codeValue);\n              localStorage.setItem('goal_login_router_query', JSON.stringify(queryValue));\n            }\n            loginUserInfo = {\n              account: utils.encrypt(form.account, new Date().getTime(), '3'),\n              password: utils.encrypt(form.password, new Date().getTime(), '2'),\n              checked: checked.value\n            };\n            localStorage.setItem('localityId', localityId.value);\n            localStorage.setItem('loginUserInfo', JSON.stringify(loginUserInfo));\n            _context6.next = 17;\n            break;\n          case 13:\n            _context6.prev = 13;\n            _context6.t0 = _context6[\"catch\"](0);\n            loading.value = false;\n            handleFefresh();\n          case 17:\n          case \"end\":\n            return _context6.stop();\n        }\n      }, _callee6, null, [[0, 13]]);\n    }));\n    return function login(_x3) {\n      return _ref6.apply(this, arguments);\n    };\n  }();\n  var loginLastAreaId = /*#__PURE__*/function () {\n    var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n      var _yield$api$loginLastA, data;\n      return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n        while (1) switch (_context7.prev = _context7.next) {\n          case 0:\n            _context7.prev = 0;\n            _context7.next = 3;\n            return api.loginLastAreaId();\n          case 3:\n            _yield$api$loginLastA = _context7.sent;\n            data = _yield$api$loginLastA.data;\n            if (data !== null && data !== void 0 && data.id) sessionStorage.setItem('oldRegionInfo', JSON.stringify(data));\n            _context7.next = 12;\n            break;\n          case 8:\n            _context7.prev = 8;\n            _context7.t0 = _context7[\"catch\"](0);\n            loading.value = false;\n            handleFefresh();\n          case 12:\n          case \"end\":\n            return _context7.stop();\n        }\n      }, _callee7, null, [[0, 8]]);\n    }));\n    return function loginLastAreaId() {\n      return _ref7.apply(this, arguments);\n    };\n  }();\n  var loginInfo = function loginInfo() {\n    var loginUserInfo = JSON.parse(localStorage.getItem('loginUserInfo')) || '';\n    loginAccount.value = loginUserInfo ? utils.decrypt(loginUserInfo.account, new Date().getTime(), '3') : '';\n    if (loginUserInfo && loginUserInfo.checked) {\n      checked.value = loginUserInfo.checked;\n      form.account = utils.decrypt(loginUserInfo.account, new Date().getTime(), '3');\n      form.password = utils.decrypt(loginUserInfo.password, new Date().getTime(), '2');\n      verifyLoginCode();\n    }\n  };\n  var refresh = function refresh() {\n    clearTimeout(timer.value);\n    loginQrcodeShow.value = false;\n    loginQrcodeId.value = guid();\n    loginQrcode.value = `${appOnlyHeader.value}|login|${loginQrcodeId.value}`;\n    setTimeout(function () {\n      loginQrcodeShow.value = true;\n    }, 180000);\n    _appToken();\n  };\n  var hideQrcode = function hideQrcode() {\n    clearTimeout(timer.value);\n  };\n  var _appToken = /*#__PURE__*/function () {\n    var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8() {\n      var _yield$api$appToken, data;\n      return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n        while (1) switch (_context8.prev = _context8.next) {\n          case 0:\n            _context8.next = 2;\n            return api.appToken({\n              qrCodeId: loginQrcodeId.value\n            });\n          case 2:\n            _yield$api$appToken = _context8.sent;\n            data = _yield$api$appToken.data;\n            if (!(data !== null && data !== void 0 && data.token) && !loginQrcodeShow.value) {\n              timer.value = setTimeout(function () {\n                _appToken();\n              }, 2000);\n            }\n            if (data !== null && data !== void 0 && data.token) {\n              clearTimeout(timer.value);\n              ElMessage({\n                message: '登录成功！',\n                showClose: true,\n                type: 'success'\n              });\n              if (codeValue === '/UnifyLogin') {\n                handleAuthorize(data.token);\n              } else {\n                localStorage.setItem('goal_login_router_path', codeValue);\n                localStorage.setItem('goal_login_router_query', queryValue);\n                sessionStorage.setItem('token', data.token);\n                store.dispatch('loginUser', 'login');\n              }\n            }\n          case 6:\n          case \"end\":\n            return _context8.stop();\n        }\n      }, _callee8);\n    }));\n    return function appToken() {\n      return _ref8.apply(this, arguments);\n    };\n  }();\n  var handleAuthorize = function handleAuthorize(token) {\n    var client_id = sessionStorage.getItem('client_id');\n    var redirect_uri = sessionStorage.getItem('redirect_uri');\n    var params = `client_id=${client_id}&redirect_uri=${redirect_uri}&Authorization=${token}`;\n    window.open(api.authorize(params), '_self', '', true);\n  };\n  return {\n    loginVerifyShow,\n    whetherVerifyCode,\n    loginDisabled,\n    loading,\n    checked,\n    imgList,\n    LoginForm,\n    form,\n    rules,\n    countDownText,\n    slideVerify,\n    disabled,\n    loginQrcode,\n    loginQrcodeShow,\n    handleBlur,\n    handleGetVerifyCode,\n    onAgain,\n    onSuccess,\n    submitForm,\n    globalData,\n    verifyLoginCode,\n    loginInfo,\n    refresh,\n    hideQrcode\n  };\n};", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "reactive", "ref", "computed", "useStore", "md5", "utils", "appOnly<PERSON>eader", "ElMessage", "<PERSON><PERSON><PERSON>ie<PERSON>", "codeValue", "undefined", "queryValue", "store", "openConfig", "getters", "getReadOpenConfig", "uniqueId", "localityId", "loginAccount", "loginVerifyShow", "whetherVerifyCode", "loading", "checked", "imgList", "LoginForm", "form", "account", "password", "verifyCode", "rules", "required", "message", "trigger", "slideVerify", "disabled", "verifyCodeId", "countDownText", "countDown", "timer", "loginQrcode", "loginQrcodeId", "loginQrcodeShow", "loginDisabled", "guid", "replace", "Math", "random", "toString", "generateUniqueId", "navigatorInfo", "window", "navigator", "userAgent", "screenWidth", "screen", "width", "screenHeight", "height", "language", "platform", "localStorage", "getItem", "handleBlur", "verifyLoginCode", "globalData", "_ref", "_callee", "_yield$api$loginImg", "data", "_callee$", "_context", "loginImg", "item", "imgPath", "fileURL", "_ref2", "_callee2", "_yield$api$verifyLogi", "_args2", "_callee2$", "_context2", "pcMachineCode", "handleGetVerifyCode", "_ref3", "_callee3", "_yield$api$userAccoun", "_callee3$", "_context3", "userAccountCheck", "detailId", "keyword", "encrypt", "Date", "getTime", "openVerifyCodeSend", "_ref4", "_callee4", "_yield$api$openVerify", "code", "_callee4$", "_context4", "mobile", "sendType", "handleCountDown", "setTimeout", "onAgain", "error", "handleFefresh", "onSuccess", "_slideVerify$value", "refresh", "submitForm", "_ref5", "_callee5", "formEl", "_callee5$", "_context5", "validate", "valid", "login", "_x", "_x2", "_ref6", "_callee6", "_yield$api$login", "_openConfig$value", "loginUserInfo", "_callee6$", "_context6", "_objectSpread", "grant_type", "username", "showClose", "passwordElementErrorMessage", "handleAuthorize", "token", "sessionStorage", "setItem", "refreshToken", "expires_in", "expiration", "passwordElementMatchReg", "whetherRegionSelect", "loginLastAreaId", "dispatch", "JSON", "stringify", "t0", "_x3", "_ref7", "_callee7", "_yield$api$loginLastA", "_callee7$", "_context7", "id", "loginInfo", "parse", "decrypt", "clearTimeout", "appToken", "hideQrcode", "_ref8", "_callee8", "_yield$api$appToken", "_callee8$", "_context8", "qrCodeId", "client_id", "redirect_uri", "params", "open", "authorize"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LoginView/LoginView.js"], "sourcesContent": ["import api from '@/api'\r\nimport { reactive, ref, computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport md5 from 'crypto-js/md5'\r\nimport utils from 'common/js/utils.js'\r\nimport { appOnlyHeader } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nexport const LoginView = (codeValue = '', queryValue = {}) => {\r\n  const store = useStore()\r\n  const openConfig = computed(() => store.getters.getReadOpenConfig)\r\n  const uniqueId = ref('')\r\n  const localityId = ref('')\r\n  const loginAccount = ref('')\r\n  const loginVerifyShow = ref(false)\r\n  const whetherVerifyCode = ref(false)\r\n  const loading = ref(false)\r\n  const checked = ref(false)\r\n  const imgList = ref([])\r\n  const LoginForm = ref()\r\n  const form = reactive({ account: '', password: '', verifyCode: '' })\r\n  const rules = reactive({\r\n    account: [{ required: true, message: '请输入账号/手机号', trigger: 'blur' }],\r\n    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],\r\n    verifyCode: [{ required: false, message: '请输入验证码', trigger: 'blur' }]\r\n  })\r\n  const slideVerify = ref()\r\n  const disabled = ref(false)\r\n  const verifyCodeId = ref('')\r\n  const countDownText = ref('获取验证码')\r\n  const countDown = ref(0)\r\n  const timer = ref()\r\n  const loginQrcode = ref('')\r\n  const loginQrcodeId = ref('')\r\n  const loginQrcodeShow = ref(false)\r\n  const loginDisabled = ref(true)\r\n  const guid = () => {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n      var r = (Math.random() * 16) | 0,\r\n        v = c == 'x' ? r : (r & 0x3) | 0x8\r\n      return v.toString(16)\r\n    })\r\n  }\r\n  const generateUniqueId = (type) => {\r\n    var navigatorInfo = window.navigator.userAgent // 获取用户代理信息\r\n    var screenWidth = window.screen.width // 获取屏幕宽度\r\n    var screenHeight = window.screen.height // 获取屏幕高度\r\n    var language = window.navigator.language // 获取浏览器语言\r\n    var platform = window.navigator.platform // 获取操作系统平台\r\n    localityId.value = type ? guid() : localStorage.getItem('localityId') || guid()\r\n    loginVerifyShow.value =\r\n      loginAccount.value && form.account\r\n        ? !(loginAccount.value === form.account && localStorage.getItem('localityId'))\r\n        : true\r\n    return (\r\n      navigatorInfo + '-' + screenWidth + '-' + screenHeight + '-' + language + '-' + platform + '-' + localityId.value\r\n    )\r\n  }\r\n  const handleBlur = () => {\r\n    loginDisabled.value = true\r\n    if (form.account) {\r\n      verifyLoginCode()\r\n    } else {\r\n      loginVerifyShow.value = false\r\n      whetherVerifyCode.value = false\r\n      rules.verifyCode = [{ required: false, message: '请输入验证码', trigger: 'blur' }]\r\n    }\r\n  }\r\n\r\n  const globalData = async () => {\r\n    const { data } = await api.loginImg()\r\n    data.forEach((item) => {\r\n      item.imgPath = api.fileURL(item.imgPath)\r\n    })\r\n    imgList.value = data\r\n  }\r\n\r\n  const verifyLoginCode = async (type = false) => {\r\n    uniqueId.value = generateUniqueId(type)\r\n    const { data } = await api.verifyLoginCode({ account: form.account, pcMachineCode: md5(uniqueId.value).toString() })\r\n    loginDisabled.value = false\r\n    whetherVerifyCode.value = data\r\n    if (data) loginVerifyShow.value = data\r\n    rules.verifyCode = [{ required: data, message: '请输入验证码', trigger: 'blur' }]\r\n  }\r\n  const handleGetVerifyCode = async () => {\r\n    if (!form.account) return ElMessage({ type: 'warning', message: '请输入账号/手机号！' })\r\n    if (!form.password) return ElMessage({ type: 'warning', message: '请输入密码！' })\r\n    const { data } = await api.userAccountCheck({\r\n      detailId: form.account,\r\n      keyword: utils.encrypt(form.password, new Date().getTime(), '1')\r\n    })\r\n    if (data === '1') openVerifyCodeSend()\r\n  }\r\n  const openVerifyCodeSend = async () => {\r\n    if (!form.account) return ElMessage({ type: 'warning', message: '请输入账号/手机号！' })\r\n    const { data, code } = await api.openVerifyCodeSend({ mobile: form.account, sendType: 'login' })\r\n    if (code === 200) {\r\n      verifyCodeId.value = data\r\n      countDown.value = 60\r\n      handleCountDown()\r\n      ElMessage({ type: 'success', message: '短信验证码已发送！' })\r\n    }\r\n  }\r\n  const handleCountDown = () => {\r\n    if (countDown.value === 0) {\r\n      countDownText.value = '获取验证码'\r\n      countDown.value = 60\r\n      return\r\n    } else {\r\n      countDownText.value = '重新发送' + countDown.value + 'S'\r\n      countDown.value--\r\n    }\r\n    setTimeout(() => {\r\n      handleCountDown()\r\n    }, 1000)\r\n  }\r\n  const onAgain = () => {\r\n    ElMessage.error('检测到非人为操作的哦！!')\r\n    handleFefresh()\r\n  }\r\n  const onSuccess = () => {\r\n    disabled.value = true\r\n  }\r\n  const handleFefresh = () => {\r\n    disabled.value = false\r\n    slideVerify.value?.refresh()\r\n  }\r\n  const submitForm = async (formEl, type) => {\r\n    if (!formEl) return\r\n    loading.value = true\r\n    await formEl.validate((valid) => {\r\n      if (valid) {\r\n        if (!loginVerifyShow.value || disabled.value || whetherVerifyCode.value) {\r\n          login(type)\r\n        } else {\r\n          loading.value = false\r\n          ElMessage({ type: 'warning', message: '请先通过验证在登录！' })\r\n        }\r\n      } else {\r\n        loading.value = false\r\n      }\r\n    })\r\n  }\r\n  const login = async (type) => {\r\n    try {\r\n      const { data } = await api.login({\r\n        grant_type: 'password',\r\n        username: form.account,\r\n        password: utils.encrypt(form.password, new Date().getTime(), '1'),\r\n        pcMachineCode: md5(uniqueId.value).toString(),\r\n        ...(whetherVerifyCode.value ? { verifyCodeId: verifyCodeId.value, verifyCode: form.verifyCode } : {})\r\n      })\r\n      ElMessage({ message: '登录成功！', showClose: true, type: 'success' })\r\n      if (data.passwordElementErrorMessage)\r\n        ElMessage({ message: data.passwordElementErrorMessage, showClose: true, type: 'info' })\r\n      if (codeValue === '/UnifyLogin') {\r\n        handleAuthorize(data.token)\r\n      } else {\r\n        sessionStorage.setItem('token', data.token)\r\n        sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)\r\n        sessionStorage.setItem('expires', data.expires_in)\r\n        sessionStorage.setItem('expiration', data.refreshToken.expiration)\r\n        sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)\r\n        if (openConfig.value?.whetherRegionSelect === 'true') loginLastAreaId()\r\n        store.dispatch('loginUser', type ? '/GlobalLayoutChat' : 'login')\r\n        localStorage.setItem('goal_login_router_path', codeValue)\r\n        localStorage.setItem('goal_login_router_query', JSON.stringify(queryValue))\r\n      }\r\n      const loginUserInfo = {\r\n        account: utils.encrypt(form.account, new Date().getTime(), '3'),\r\n        password: utils.encrypt(form.password, new Date().getTime(), '2'),\r\n        checked: checked.value\r\n      }\r\n      localStorage.setItem('localityId', localityId.value)\r\n      localStorage.setItem('loginUserInfo', JSON.stringify(loginUserInfo))\r\n    } catch {\r\n      loading.value = false\r\n      handleFefresh()\r\n    }\r\n  }\r\n  const loginLastAreaId = async () => {\r\n    try {\r\n      const { data } = await api.loginLastAreaId()\r\n      if (data?.id) sessionStorage.setItem('oldRegionInfo', JSON.stringify(data))\r\n    } catch {\r\n      loading.value = false\r\n      handleFefresh()\r\n    }\r\n  }\r\n  const loginInfo = () => {\r\n    const loginUserInfo = JSON.parse(localStorage.getItem('loginUserInfo')) || ''\r\n    loginAccount.value = loginUserInfo ? utils.decrypt(loginUserInfo.account, new Date().getTime(), '3') : ''\r\n    if (loginUserInfo && loginUserInfo.checked) {\r\n      checked.value = loginUserInfo.checked\r\n      form.account = utils.decrypt(loginUserInfo.account, new Date().getTime(), '3')\r\n      form.password = utils.decrypt(loginUserInfo.password, new Date().getTime(), '2')\r\n      verifyLoginCode()\r\n    }\r\n  }\r\n  const refresh = () => {\r\n    clearTimeout(timer.value)\r\n    loginQrcodeShow.value = false\r\n    loginQrcodeId.value = guid()\r\n    loginQrcode.value = `${appOnlyHeader.value}|login|${loginQrcodeId.value}`\r\n    setTimeout(() => {\r\n      loginQrcodeShow.value = true\r\n    }, 180000)\r\n    appToken()\r\n  }\r\n  const hideQrcode = () => {\r\n    clearTimeout(timer.value)\r\n  }\r\n  const appToken = async () => {\r\n    const { data } = await api.appToken({ qrCodeId: loginQrcodeId.value })\r\n    if (!data?.token && !loginQrcodeShow.value) {\r\n      timer.value = setTimeout(() => {\r\n        appToken()\r\n      }, 2000)\r\n    }\r\n    if (data?.token) {\r\n      clearTimeout(timer.value)\r\n      ElMessage({ message: '登录成功！', showClose: true, type: 'success' })\r\n      if (codeValue === '/UnifyLogin') {\r\n        handleAuthorize(data.token)\r\n      } else {\r\n        localStorage.setItem('goal_login_router_path', codeValue)\r\n        localStorage.setItem('goal_login_router_query', queryValue)\r\n        sessionStorage.setItem('token', data.token)\r\n        store.dispatch('loginUser', 'login')\r\n      }\r\n    }\r\n  }\r\n  const handleAuthorize = (token) => {\r\n    const client_id = sessionStorage.getItem('client_id')\r\n    const redirect_uri = sessionStorage.getItem('redirect_uri')\r\n    const params = `client_id=${client_id}&redirect_uri=${redirect_uri}&Authorization=${token}`\r\n    window.open(api.authorize(params), '_self', '', true)\r\n  }\r\n  return {\r\n    loginVerifyShow,\r\n    whetherVerifyCode,\r\n    loginDisabled,\r\n    loading,\r\n    checked,\r\n    imgList,\r\n    LoginForm,\r\n    form,\r\n    rules,\r\n    countDownText,\r\n    slideVerify,\r\n    disabled,\r\n    loginQrcode,\r\n    loginQrcodeShow,\r\n    handleBlur,\r\n    handleGetVerifyCode,\r\n    onAgain,\r\n    onSuccess,\r\n    submitForm,\r\n    globalData,\r\n    verifyLoginCode,\r\n    loginInfo,\r\n    refresh,\r\n    hideQrcode\r\n  }\r\n}\r\n"], "mappings": ";;;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AAC7C,SAASC,QAAQ,QAAQ,MAAM;AAC/B,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,KAAK,MAAM,oBAAoB;AACtC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAO,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAwC;EAAA,IAApCC,SAAS,GAAAd,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAe,SAAA,GAAAf,SAAA,MAAG,EAAE;EAAA,IAAEgB,UAAU,GAAAhB,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAe,SAAA,GAAAf,SAAA,MAAG,CAAC,CAAC;EACvD,IAAMiB,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,IAAMU,UAAU,GAAGX,QAAQ,CAAC;IAAA,OAAMU,KAAK,CAACE,OAAO,CAACC,iBAAiB;EAAA,EAAC;EAClE,IAAMC,QAAQ,GAAGf,GAAG,CAAC,EAAE,CAAC;EACxB,IAAMgB,UAAU,GAAGhB,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAMiB,YAAY,GAAGjB,GAAG,CAAC,EAAE,CAAC;EAC5B,IAAMkB,eAAe,GAAGlB,GAAG,CAAC,KAAK,CAAC;EAClC,IAAMmB,iBAAiB,GAAGnB,GAAG,CAAC,KAAK,CAAC;EACpC,IAAMoB,OAAO,GAAGpB,GAAG,CAAC,KAAK,CAAC;EAC1B,IAAMqB,OAAO,GAAGrB,GAAG,CAAC,KAAK,CAAC;EAC1B,IAAMsB,OAAO,GAAGtB,GAAG,CAAC,EAAE,CAAC;EACvB,IAAMuB,SAAS,GAAGvB,GAAG,CAAC,CAAC;EACvB,IAAMwB,IAAI,GAAGzB,QAAQ,CAAC;IAAE0B,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,UAAU,EAAE;EAAG,CAAC,CAAC;EACpE,IAAMC,KAAK,GAAG7B,QAAQ,CAAC;IACrB0B,OAAO,EAAE,CAAC;MAAEI,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAO,CAAC,CAAC;IACpEL,QAAQ,EAAE,CAAC;MAAEG,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAO,CAAC,CAAC;IACjEJ,UAAU,EAAE,CAAC;MAAEE,QAAQ,EAAE,KAAK;MAAEC,OAAO,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAO,CAAC;EACtE,CAAC,CAAC;EACF,IAAMC,WAAW,GAAGhC,GAAG,CAAC,CAAC;EACzB,IAAMiC,QAAQ,GAAGjC,GAAG,CAAC,KAAK,CAAC;EAC3B,IAAMkC,YAAY,GAAGlC,GAAG,CAAC,EAAE,CAAC;EAC5B,IAAMmC,aAAa,GAAGnC,GAAG,CAAC,OAAO,CAAC;EAClC,IAAMoC,SAAS,GAAGpC,GAAG,CAAC,CAAC,CAAC;EACxB,IAAMqC,KAAK,GAAGrC,GAAG,CAAC,CAAC;EACnB,IAAMsC,WAAW,GAAGtC,GAAG,CAAC,EAAE,CAAC;EAC3B,IAAMuC,aAAa,GAAGvC,GAAG,CAAC,EAAE,CAAC;EAC7B,IAAMwC,eAAe,GAAGxC,GAAG,CAAC,KAAK,CAAC;EAClC,IAAMyC,aAAa,GAAGzC,GAAG,CAAC,IAAI,CAAC;EAC/B,IAAM0C,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;IACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAC5I,CAAC,EAAK;MACpE,IAAIZ,CAAC,GAAIyJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;QAC9BnH,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;MACpC,OAAOuC,CAAC,CAACoH,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIlI,IAAI,EAAK;IACjC,IAAImI,aAAa,GAAGC,MAAM,CAACC,SAAS,CAACC,SAAS,EAAC;IAC/C,IAAIC,WAAW,GAAGH,MAAM,CAACI,MAAM,CAACC,KAAK,EAAC;IACtC,IAAIC,YAAY,GAAGN,MAAM,CAACI,MAAM,CAACG,MAAM,EAAC;IACxC,IAAIC,QAAQ,GAAGR,MAAM,CAACC,SAAS,CAACO,QAAQ,EAAC;IACzC,IAAIC,QAAQ,GAAGT,MAAM,CAACC,SAAS,CAACQ,QAAQ,EAAC;IACzC1C,UAAU,CAACtH,KAAK,GAAGmB,IAAI,GAAG6H,IAAI,CAAC,CAAC,GAAGiB,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAIlB,IAAI,CAAC,CAAC;IAC/ExB,eAAe,CAACxH,KAAK,GACnBuH,YAAY,CAACvH,KAAK,IAAI8H,IAAI,CAACC,OAAO,GAC9B,EAAER,YAAY,CAACvH,KAAK,KAAK8H,IAAI,CAACC,OAAO,IAAIkC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,GAC5E,IAAI;IACV,OACEZ,aAAa,GAAG,GAAG,GAAGI,WAAW,GAAG,GAAG,GAAGG,YAAY,GAAG,GAAG,GAAGE,QAAQ,GAAG,GAAG,GAAGC,QAAQ,GAAG,GAAG,GAAG1C,UAAU,CAACtH,KAAK;EAErH,CAAC;EACD,IAAMmK,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvBpB,aAAa,CAAC/I,KAAK,GAAG,IAAI;IAC1B,IAAI8H,IAAI,CAACC,OAAO,EAAE;MAChBqC,eAAe,CAAC,CAAC;IACnB,CAAC,MAAM;MACL5C,eAAe,CAACxH,KAAK,GAAG,KAAK;MAC7ByH,iBAAiB,CAACzH,KAAK,GAAG,KAAK;MAC/BkI,KAAK,CAACD,UAAU,GAAG,CAAC;QAAEE,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,IAAMgC,UAAU;IAAA,IAAAC,IAAA,GAAAvE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6F,QAAA;MAAA,IAAAC,mBAAA,EAAAC,IAAA;MAAA,OAAAnL,mBAAA,GAAAuB,IAAA,UAAA6J,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAxF,IAAA,GAAAwF,QAAA,CAAAnH,IAAA;UAAA;YAAAmH,QAAA,CAAAnH,IAAA;YAAA,OACM4C,GAAG,CAACwE,QAAQ,CAAC,CAAC;UAAA;YAAAJ,mBAAA,GAAAG,QAAA,CAAA1H,IAAA;YAA7BwH,IAAI,GAAAD,mBAAA,CAAJC,IAAI;YACZA,IAAI,CAACrI,OAAO,CAAC,UAACyI,IAAI,EAAK;cACrBA,IAAI,CAACC,OAAO,GAAG1E,GAAG,CAAC2E,OAAO,CAACF,IAAI,CAACC,OAAO,CAAC;YAC1C,CAAC,CAAC;YACFlD,OAAO,CAAC5H,KAAK,GAAGyK,IAAI;UAAA;UAAA;YAAA,OAAAE,QAAA,CAAArF,IAAA;QAAA;MAAA,GAAAiF,OAAA;IAAA,CACrB;IAAA,gBANKF,UAAUA,CAAA;MAAA,OAAAC,IAAA,CAAArE,KAAA,OAAAD,SAAA;IAAA;EAAA,GAMf;EAED,IAAMoE,eAAe;IAAA,IAAAY,KAAA,GAAAjF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAuG,SAAA;MAAA,IAAA9J,IAAA;QAAA+J,qBAAA;QAAAT,IAAA;QAAAU,MAAA,GAAAnF,SAAA;MAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAAuK,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAlG,IAAA,GAAAkG,SAAA,CAAA7H,IAAA;UAAA;YAAOrC,IAAI,GAAAgK,MAAA,CAAA9G,MAAA,QAAA8G,MAAA,QAAApE,SAAA,GAAAoE,MAAA,MAAG,KAAK;YACzC9D,QAAQ,CAACrH,KAAK,GAAGqJ,gBAAgB,CAAClI,IAAI,CAAC;YAAAkK,SAAA,CAAA7H,IAAA;YAAA,OAChB4C,GAAG,CAACgE,eAAe,CAAC;cAAErC,OAAO,EAAED,IAAI,CAACC,OAAO;cAAEuD,aAAa,EAAE7E,GAAG,CAACY,QAAQ,CAACrH,KAAK,CAAC,CAACoJ,QAAQ,CAAC;YAAE,CAAC,CAAC;UAAA;YAAA8B,qBAAA,GAAAG,SAAA,CAAApI,IAAA;YAA5GwH,IAAI,GAAAS,qBAAA,CAAJT,IAAI;YACZ1B,aAAa,CAAC/I,KAAK,GAAG,KAAK;YAC3ByH,iBAAiB,CAACzH,KAAK,GAAGyK,IAAI;YAC9B,IAAIA,IAAI,EAAEjD,eAAe,CAACxH,KAAK,GAAGyK,IAAI;YACtCvC,KAAK,CAACD,UAAU,GAAG,CAAC;cAAEE,QAAQ,EAAEsC,IAAI;cAAErC,OAAO,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAO,CAAC,CAAC;UAAA;UAAA;YAAA,OAAAgD,SAAA,CAAA/F,IAAA;QAAA;MAAA,GAAA2F,QAAA;IAAA,CAC5E;IAAA,gBAPKb,eAAeA,CAAA;MAAA,OAAAY,KAAA,CAAA/E,KAAA,OAAAD,SAAA;IAAA;EAAA,GAOpB;EACD,IAAMuF,mBAAmB;IAAA,IAAAC,KAAA,GAAAzF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+G,SAAA;MAAA,IAAAC,qBAAA,EAAAjB,IAAA;MAAA,OAAAnL,mBAAA,GAAAuB,IAAA,UAAA8K,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAzG,IAAA,GAAAyG,SAAA,CAAApI,IAAA;UAAA;YAAA,IACrBsE,IAAI,CAACC,OAAO;cAAA6D,SAAA,CAAApI,IAAA;cAAA;YAAA;YAAA,OAAAoI,SAAA,CAAAxI,MAAA,WAASwD,SAAS,CAAC;cAAEzF,IAAI,EAAE,SAAS;cAAEiH,OAAO,EAAE;YAAa,CAAC,CAAC;UAAA;YAAA,IAC1EN,IAAI,CAACE,QAAQ;cAAA4D,SAAA,CAAApI,IAAA;cAAA;YAAA;YAAA,OAAAoI,SAAA,CAAAxI,MAAA,WAASwD,SAAS,CAAC;cAAEzF,IAAI,EAAE,SAAS;cAAEiH,OAAO,EAAE;YAAS,CAAC,CAAC;UAAA;YAAAwD,SAAA,CAAApI,IAAA;YAAA,OACrD4C,GAAG,CAACyF,gBAAgB,CAAC;cAC1CC,QAAQ,EAAEhE,IAAI,CAACC,OAAO;cACtBgE,OAAO,EAAErF,KAAK,CAACsF,OAAO,CAAClE,IAAI,CAACE,QAAQ,EAAE,IAAIiE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG;YACjE,CAAC,CAAC;UAAA;YAAAR,qBAAA,GAAAE,SAAA,CAAA3I,IAAA;YAHMwH,IAAI,GAAAiB,qBAAA,CAAJjB,IAAI;YAIZ,IAAIA,IAAI,KAAK,GAAG,EAAE0B,kBAAkB,CAAC,CAAC;UAAA;UAAA;YAAA,OAAAP,SAAA,CAAAtG,IAAA;QAAA;MAAA,GAAAmG,QAAA;IAAA,CACvC;IAAA,gBARKF,mBAAmBA,CAAA;MAAA,OAAAC,KAAA,CAAAvF,KAAA,OAAAD,SAAA;IAAA;EAAA,GAQxB;EACD,IAAMmG,kBAAkB;IAAA,IAAAC,KAAA,GAAArG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2H,SAAA;MAAA,IAAAC,qBAAA,EAAA7B,IAAA,EAAA8B,IAAA;MAAA,OAAAjN,mBAAA,GAAAuB,IAAA,UAAA2L,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAtH,IAAA,GAAAsH,SAAA,CAAAjJ,IAAA;UAAA;YAAA,IACpBsE,IAAI,CAACC,OAAO;cAAA0E,SAAA,CAAAjJ,IAAA;cAAA;YAAA;YAAA,OAAAiJ,SAAA,CAAArJ,MAAA,WAASwD,SAAS,CAAC;cAAEzF,IAAI,EAAE,SAAS;cAAEiH,OAAO,EAAE;YAAa,CAAC,CAAC;UAAA;YAAAqE,SAAA,CAAAjJ,IAAA;YAAA,OAClD4C,GAAG,CAAC+F,kBAAkB,CAAC;cAAEO,MAAM,EAAE5E,IAAI,CAACC,OAAO;cAAE4E,QAAQ,EAAE;YAAQ,CAAC,CAAC;UAAA;YAAAL,qBAAA,GAAAG,SAAA,CAAAxJ,IAAA;YAAxFwH,IAAI,GAAA6B,qBAAA,CAAJ7B,IAAI;YAAE8B,IAAI,GAAAD,qBAAA,CAAJC,IAAI;YAClB,IAAIA,IAAI,KAAK,GAAG,EAAE;cAChB/D,YAAY,CAACxI,KAAK,GAAGyK,IAAI;cACzB/B,SAAS,CAAC1I,KAAK,GAAG,EAAE;cACpB4M,gBAAe,CAAC,CAAC;cACjBhG,SAAS,CAAC;gBAAEzF,IAAI,EAAE,SAAS;gBAAEiH,OAAO,EAAE;cAAY,CAAC,CAAC;YACtD;UAAC;UAAA;YAAA,OAAAqE,SAAA,CAAAnH,IAAA;QAAA;MAAA,GAAA+G,QAAA;IAAA,CACF;IAAA,gBATKF,kBAAkBA,CAAA;MAAA,OAAAC,KAAA,CAAAnG,KAAA,OAAAD,SAAA;IAAA;EAAA,GASvB;EACD,IAAM4G,gBAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAC5B,IAAIlE,SAAS,CAAC1I,KAAK,KAAK,CAAC,EAAE;MACzByI,aAAa,CAACzI,KAAK,GAAG,OAAO;MAC7B0I,SAAS,CAAC1I,KAAK,GAAG,EAAE;MACpB;IACF,CAAC,MAAM;MACLyI,aAAa,CAACzI,KAAK,GAAG,MAAM,GAAG0I,SAAS,CAAC1I,KAAK,GAAG,GAAG;MACpD0I,SAAS,CAAC1I,KAAK,EAAE;IACnB;IACA6M,UAAU,CAAC,YAAM;MACfD,gBAAe,CAAC,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD,IAAME,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;IACpBlG,SAAS,CAACmG,KAAK,CAAC,cAAc,CAAC;IAC/BC,aAAa,CAAC,CAAC;EACjB,CAAC;EACD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtB1E,QAAQ,CAACvI,KAAK,GAAG,IAAI;EACvB,CAAC;EACD,IAAMgN,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAAA,IAAAE,kBAAA;IAC1B3E,QAAQ,CAACvI,KAAK,GAAG,KAAK;IACtB,CAAAkN,kBAAA,GAAA5E,WAAW,CAACtI,KAAK,cAAAkN,kBAAA,eAAjBA,kBAAA,CAAmBC,OAAO,CAAC,CAAC;EAC9B,CAAC;EACD,IAAMC,UAAU;IAAA,IAAAC,KAAA,GAAAtH,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4I,SAAOC,MAAM,EAAEpM,IAAI;MAAA,OAAA7B,mBAAA,GAAAuB,IAAA,UAAA2M,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAtI,IAAA,GAAAsI,SAAA,CAAAjK,IAAA;UAAA;YAAA,IAC/B+J,MAAM;cAAAE,SAAA,CAAAjK,IAAA;cAAA;YAAA;YAAA,OAAAiK,SAAA,CAAArK,MAAA;UAAA;YACXsE,OAAO,CAAC1H,KAAK,GAAG,IAAI;YAAAyN,SAAA,CAAAjK,IAAA;YAAA,OACd+J,MAAM,CAACG,QAAQ,CAAC,UAACC,KAAK,EAAK;cAC/B,IAAIA,KAAK,EAAE;gBACT,IAAI,CAACnG,eAAe,CAACxH,KAAK,IAAIuI,QAAQ,CAACvI,KAAK,IAAIyH,iBAAiB,CAACzH,KAAK,EAAE;kBACvE4N,KAAK,CAACzM,IAAI,CAAC;gBACb,CAAC,MAAM;kBACLuG,OAAO,CAAC1H,KAAK,GAAG,KAAK;kBACrB4G,SAAS,CAAC;oBAAEzF,IAAI,EAAE,SAAS;oBAAEiH,OAAO,EAAE;kBAAa,CAAC,CAAC;gBACvD;cACF,CAAC,MAAM;gBACLV,OAAO,CAAC1H,KAAK,GAAG,KAAK;cACvB;YACF,CAAC,CAAC;UAAA;UAAA;YAAA,OAAAyN,SAAA,CAAAnI,IAAA;QAAA;MAAA,GAAAgI,QAAA;IAAA,CACH;IAAA,gBAfKF,UAAUA,CAAAS,EAAA,EAAAC,GAAA;MAAA,OAAAT,KAAA,CAAApH,KAAA,OAAAD,SAAA;IAAA;EAAA,GAef;EACD,IAAM4H,KAAK;IAAA,IAAAG,KAAA,GAAAhI,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsJ,SAAO7M,IAAI;MAAA,IAAA8M,gBAAA,EAAAxD,IAAA,EAAAyD,iBAAA,EAAAC,aAAA;MAAA,OAAA7O,mBAAA,GAAAuB,IAAA,UAAAuN,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAlJ,IAAA,GAAAkJ,SAAA,CAAA7K,IAAA;UAAA;YAAA6K,SAAA,CAAAlJ,IAAA;YAAAkJ,SAAA,CAAA7K,IAAA;YAAA,OAEE4C,GAAG,CAACwH,KAAK,CAAAU,aAAA;cAC9BC,UAAU,EAAE,UAAU;cACtBC,QAAQ,EAAE1G,IAAI,CAACC,OAAO;cACtBC,QAAQ,EAAEtB,KAAK,CAACsF,OAAO,CAAClE,IAAI,CAACE,QAAQ,EAAE,IAAIiE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;cACjEZ,aAAa,EAAE7E,GAAG,CAACY,QAAQ,CAACrH,KAAK,CAAC,CAACoJ,QAAQ,CAAC;YAAC,GACzC3B,iBAAiB,CAACzH,KAAK,GAAG;cAAEwI,YAAY,EAAEA,YAAY,CAACxI,KAAK;cAAEiI,UAAU,EAAEH,IAAI,CAACG;YAAW,CAAC,GAAG,CAAC,CAAC,CACrG,CAAC;UAAA;YAAAgG,gBAAA,GAAAI,SAAA,CAAApL,IAAA;YANMwH,IAAI,GAAAwD,gBAAA,CAAJxD,IAAI;YAOZ7D,SAAS,CAAC;cAAEwB,OAAO,EAAE,OAAO;cAAEqG,SAAS,EAAE,IAAI;cAAEtN,IAAI,EAAE;YAAU,CAAC,CAAC;YACjE,IAAIsJ,IAAI,CAACiE,2BAA2B,EAClC9H,SAAS,CAAC;cAAEwB,OAAO,EAAEqC,IAAI,CAACiE,2BAA2B;cAAED,SAAS,EAAE,IAAI;cAAEtN,IAAI,EAAE;YAAO,CAAC,CAAC;YACzF,IAAI2F,SAAS,KAAK,aAAa,EAAE;cAC/B6H,eAAe,CAAClE,IAAI,CAACmE,KAAK,CAAC;YAC7B,CAAC,MAAM;cACLC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAErE,IAAI,CAACmE,KAAK,CAAC;cAC3CC,cAAc,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUrE,IAAI,CAACsE,YAAY,CAAC/O,KAAK,EAAE,CAAC;cAC5E6O,cAAc,CAACC,OAAO,CAAC,SAAS,EAAErE,IAAI,CAACuE,UAAU,CAAC;cAClDH,cAAc,CAACC,OAAO,CAAC,YAAY,EAAErE,IAAI,CAACsE,YAAY,CAACE,UAAU,CAAC;cAClEJ,cAAc,CAACC,OAAO,CAAC,QAAQ,EAAErE,IAAI,CAACyE,uBAAuB,GAAG,CAAC,GAAG,CAAC,CAAC;cACtE,IAAI,EAAAhB,iBAAA,GAAAhH,UAAU,CAAClH,KAAK,cAAAkO,iBAAA,uBAAhBA,iBAAA,CAAkBiB,mBAAmB,MAAK,MAAM,EAAEC,eAAe,CAAC,CAAC;cACvEnI,KAAK,CAACoI,QAAQ,CAAC,WAAW,EAAElO,IAAI,GAAG,mBAAmB,GAAG,OAAO,CAAC;cACjE8I,YAAY,CAAC6E,OAAO,CAAC,wBAAwB,EAAEhI,SAAS,CAAC;cACzDmD,YAAY,CAAC6E,OAAO,CAAC,yBAAyB,EAAEQ,IAAI,CAACC,SAAS,CAACvI,UAAU,CAAC,CAAC;YAC7E;YACMmH,aAAa,GAAG;cACpBpG,OAAO,EAAErB,KAAK,CAACsF,OAAO,CAAClE,IAAI,CAACC,OAAO,EAAE,IAAIkE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;cAC/DlE,QAAQ,EAAEtB,KAAK,CAACsF,OAAO,CAAClE,IAAI,CAACE,QAAQ,EAAE,IAAIiE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;cACjEvE,OAAO,EAAEA,OAAO,CAAC3H;YACnB,CAAC;YACDiK,YAAY,CAAC6E,OAAO,CAAC,YAAY,EAAExH,UAAU,CAACtH,KAAK,CAAC;YACpDiK,YAAY,CAAC6E,OAAO,CAAC,eAAe,EAAEQ,IAAI,CAACC,SAAS,CAACpB,aAAa,CAAC,CAAC;YAAAE,SAAA,CAAA7K,IAAA;YAAA;UAAA;YAAA6K,SAAA,CAAAlJ,IAAA;YAAAkJ,SAAA,CAAAmB,EAAA,GAAAnB,SAAA;YAEpE3G,OAAO,CAAC1H,KAAK,GAAG,KAAK;YACrBgN,aAAa,CAAC,CAAC;UAAA;UAAA;YAAA,OAAAqB,SAAA,CAAA/I,IAAA;QAAA;MAAA,GAAA0I,QAAA;IAAA,CAElB;IAAA,gBApCKJ,KAAKA,CAAA6B,GAAA;MAAA,OAAA1B,KAAA,CAAA9H,KAAA,OAAAD,SAAA;IAAA;EAAA,GAoCV;EACD,IAAMoJ,eAAe;IAAA,IAAAM,KAAA,GAAA3J,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiL,SAAA;MAAA,IAAAC,qBAAA,EAAAnF,IAAA;MAAA,OAAAnL,mBAAA,GAAAuB,IAAA,UAAAgP,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAA3K,IAAA,GAAA2K,SAAA,CAAAtM,IAAA;UAAA;YAAAsM,SAAA,CAAA3K,IAAA;YAAA2K,SAAA,CAAAtM,IAAA;YAAA,OAEG4C,GAAG,CAACgJ,eAAe,CAAC,CAAC;UAAA;YAAAQ,qBAAA,GAAAE,SAAA,CAAA7M,IAAA;YAApCwH,IAAI,GAAAmF,qBAAA,CAAJnF,IAAI;YACZ,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsF,EAAE,EAAElB,cAAc,CAACC,OAAO,CAAC,eAAe,EAAEQ,IAAI,CAACC,SAAS,CAAC9E,IAAI,CAAC,CAAC;YAAAqF,SAAA,CAAAtM,IAAA;YAAA;UAAA;YAAAsM,SAAA,CAAA3K,IAAA;YAAA2K,SAAA,CAAAN,EAAA,GAAAM,SAAA;YAE3EpI,OAAO,CAAC1H,KAAK,GAAG,KAAK;YACrBgN,aAAa,CAAC,CAAC;UAAA;UAAA;YAAA,OAAA8C,SAAA,CAAAxK,IAAA;QAAA;MAAA,GAAAqK,QAAA;IAAA,CAElB;IAAA,gBARKP,eAAeA,CAAA;MAAA,OAAAM,KAAA,CAAAzJ,KAAA,OAAAD,SAAA;IAAA;EAAA,GAQpB;EACD,IAAMgK,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtB,IAAM7B,aAAa,GAAGmB,IAAI,CAACW,KAAK,CAAChG,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,EAAE;IAC7E3C,YAAY,CAACvH,KAAK,GAAGmO,aAAa,GAAGzH,KAAK,CAACwJ,OAAO,CAAC/B,aAAa,CAACpG,OAAO,EAAE,IAAIkE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE;IACzG,IAAIiC,aAAa,IAAIA,aAAa,CAACxG,OAAO,EAAE;MAC1CA,OAAO,CAAC3H,KAAK,GAAGmO,aAAa,CAACxG,OAAO;MACrCG,IAAI,CAACC,OAAO,GAAGrB,KAAK,CAACwJ,OAAO,CAAC/B,aAAa,CAACpG,OAAO,EAAE,IAAIkE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;MAC9EpE,IAAI,CAACE,QAAQ,GAAGtB,KAAK,CAACwJ,OAAO,CAAC/B,aAAa,CAACnG,QAAQ,EAAE,IAAIiE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;MAChF9B,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EACD,IAAM+C,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;IACpBgD,YAAY,CAACxH,KAAK,CAAC3I,KAAK,CAAC;IACzB8I,eAAe,CAAC9I,KAAK,GAAG,KAAK;IAC7B6I,aAAa,CAAC7I,KAAK,GAAGgJ,IAAI,CAAC,CAAC;IAC5BJ,WAAW,CAAC5I,KAAK,GAAG,GAAG2G,aAAa,CAAC3G,KAAK,UAAU6I,aAAa,CAAC7I,KAAK,EAAE;IACzE6M,UAAU,CAAC,YAAM;MACf/D,eAAe,CAAC9I,KAAK,GAAG,IAAI;IAC9B,CAAC,EAAE,MAAM,CAAC;IACVoQ,SAAQ,CAAC,CAAC;EACZ,CAAC;EACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvBF,YAAY,CAACxH,KAAK,CAAC3I,KAAK,CAAC;EAC3B,CAAC;EACD,IAAMoQ,SAAQ;IAAA,IAAAE,KAAA,GAAAvK,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6L,SAAA;MAAA,IAAAC,mBAAA,EAAA/F,IAAA;MAAA,OAAAnL,mBAAA,GAAAuB,IAAA,UAAA4P,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAvL,IAAA,GAAAuL,SAAA,CAAAlN,IAAA;UAAA;YAAAkN,SAAA,CAAAlN,IAAA;YAAA,OACQ4C,GAAG,CAACgK,QAAQ,CAAC;cAAEO,QAAQ,EAAE9H,aAAa,CAAC7I;YAAM,CAAC,CAAC;UAAA;YAAAwQ,mBAAA,GAAAE,SAAA,CAAAzN,IAAA;YAA9DwH,IAAI,GAAA+F,mBAAA,CAAJ/F,IAAI;YACZ,IAAI,EAACA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmE,KAAK,KAAI,CAAC9F,eAAe,CAAC9I,KAAK,EAAE;cAC1C2I,KAAK,CAAC3I,KAAK,GAAG6M,UAAU,CAAC,YAAM;gBAC7BuD,SAAQ,CAAC,CAAC;cACZ,CAAC,EAAE,IAAI,CAAC;YACV;YACA,IAAI3F,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmE,KAAK,EAAE;cACfuB,YAAY,CAACxH,KAAK,CAAC3I,KAAK,CAAC;cACzB4G,SAAS,CAAC;gBAAEwB,OAAO,EAAE,OAAO;gBAAEqG,SAAS,EAAE,IAAI;gBAAEtN,IAAI,EAAE;cAAU,CAAC,CAAC;cACjE,IAAI2F,SAAS,KAAK,aAAa,EAAE;gBAC/B6H,eAAe,CAAClE,IAAI,CAACmE,KAAK,CAAC;cAC7B,CAAC,MAAM;gBACL3E,YAAY,CAAC6E,OAAO,CAAC,wBAAwB,EAAEhI,SAAS,CAAC;gBACzDmD,YAAY,CAAC6E,OAAO,CAAC,yBAAyB,EAAE9H,UAAU,CAAC;gBAC3D6H,cAAc,CAACC,OAAO,CAAC,OAAO,EAAErE,IAAI,CAACmE,KAAK,CAAC;gBAC3C3H,KAAK,CAACoI,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;cACtC;YACF;UAAC;UAAA;YAAA,OAAAqB,SAAA,CAAApL,IAAA;QAAA;MAAA,GAAAiL,QAAA;IAAA,CACF;IAAA,gBAnBKH,QAAQA,CAAA;MAAA,OAAAE,KAAA,CAAArK,KAAA,OAAAD,SAAA;IAAA;EAAA,GAmBb;EACD,IAAM2I,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;IACjC,IAAMgC,SAAS,GAAG/B,cAAc,CAAC3E,OAAO,CAAC,WAAW,CAAC;IACrD,IAAM2G,YAAY,GAAGhC,cAAc,CAAC3E,OAAO,CAAC,cAAc,CAAC;IAC3D,IAAM4G,MAAM,GAAG,aAAaF,SAAS,iBAAiBC,YAAY,kBAAkBjC,KAAK,EAAE;IAC3FrF,MAAM,CAACwH,IAAI,CAAC3K,GAAG,CAAC4K,SAAS,CAACF,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC;EACvD,CAAC;EACD,OAAO;IACLtJ,eAAe;IACfC,iBAAiB;IACjBsB,aAAa;IACbrB,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,SAAS;IACTC,IAAI;IACJI,KAAK;IACLO,aAAa;IACbH,WAAW;IACXC,QAAQ;IACRK,WAAW;IACXE,eAAe;IACfqB,UAAU;IACVoB,mBAAmB;IACnBuB,OAAO;IACPG,SAAS;IACTG,UAAU;IACV/C,UAAU;IACVD,eAAe;IACf4F,SAAS;IACT7C,OAAO;IACPkD;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}