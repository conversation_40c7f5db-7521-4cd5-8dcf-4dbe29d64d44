{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"CollectiveProposalUnit\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_export_excel = _resolveComponent(\"xyl-export-excel\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _component_xyl_import_excel = _resolveComponent(\"xyl-import-excel\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"序号\",\n        width: \"80\",\n        prop: \"sort\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"单位名称\",\n        \"min-width\": \"220\",\n        prop: \"name\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"类型\",\n        \"min-width\": \"220\",\n        prop: \"teamOfficeTheme\",\n        \"show-overflow-tooltip\": \"\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createElementVNode(\"span\", null, _toDisplayString(scope.row.teamOfficeTheme.name), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"联系人\",\n        \"min-width\": \"160\",\n        prop: \"contactUser\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"联系电话\",\n        \"min-width\": \"160\",\n        prop: \"contactPhone\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_xyl_global_table_button, {\n        data: $setup.tableButtonList,\n        onButtonClick: $setup.handleCommand\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.exportShow,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.exportShow = $event;\n    }),\n    name: \"导出Excel\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_xyl_export_excel, {\n        name: \"办理单位管理\",\n        exportId: $setup.exportId,\n        params: $setup.exportParams,\n        module: \"suggestionOfficeExportExcel\",\n        onExcelCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"exportId\", \"params\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.importShow,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.importShow = $event;\n    }),\n    name: \"Excel导入\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_xyl_import_excel, {\n        name: \"办理单位管理\",\n        type: \"proposalOfficeImportExcel\",\n        onCallback: $setup.callback\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: $setup.id ? '编辑单位' : '新增单位'\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SubmitCollectiveProposalUnit\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "ref", "data", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "default", "_component_el_table_column", "type", "width", "fixed", "label", "prop", "scope", "_toDisplayString", "row", "teamOfficeTheme", "name", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "handleCommand", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "exportShow", "_component_xyl_export_excel", "exportId", "params", "exportParams", "module", "onExcelCallback", "callback", "importShow", "_component_xyl_import_excel", "onCallback", "show", "id"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\CollectiveProposalUnit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CollectiveProposalUnit\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column label=\"序号\"\r\n                         width=\"80\"\r\n                         prop=\"sort\" />\r\n        <el-table-column label=\"单位名称\"\r\n                         min-width=\"220\"\r\n                         prop=\"name\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"类型\"\r\n                         min-width=\"220\"\r\n                         prop=\"teamOfficeTheme\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            <span>\r\n              {{ scope.row.teamOfficeTheme.name }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"联系人\"\r\n                         min-width=\"160\"\r\n                         prop=\"contactUser\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"联系电话\"\r\n                         min-width=\"160\"\r\n                         prop=\"contactPhone\"\r\n                         show-overflow-tooltip />\r\n        <xyl-global-table-button :data=\"tableButtonList\"\r\n                                 @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\"\r\n                      name=\"导出Excel\">\r\n      <xyl-export-excel name=\"办理单位管理\"\r\n                        :exportId=\"exportId\"\r\n                        :params=\"exportParams\"\r\n                        module=\"suggestionOfficeExportExcel\"\r\n                        @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"importShow\"\r\n                      name=\"Excel导入\">\r\n      <xyl-import-excel name=\"办理单位管理\"\r\n                        type=\"proposalOfficeImportExcel\"\r\n                        @callback=\"callback\"></xyl-import-excel>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      :name=\"id ? '编辑单位' : '新增单位'\">\r\n      <SubmitCollectiveProposalUnit :id=\"id\"\r\n                                    @callback=\"callback\"></SubmitCollectiveProposalUnit>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'CollectiveProposalUnit' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SubmitCollectiveProposalUnit from './component/SubmitCollectiveProposalUnit'\r\nconst buttonList = [\r\n  { id: 'new', name: '新增', type: 'primary', has: 'new' },\r\n  // { id: 'import', name: 'Excel导入', type: 'primary', has: 'import' },\r\n  // { id: 'export', name: '导出Excel', type: 'primary', has: 'export' },\r\n  { id: 'del', name: '删除', type: '', has: 'del' }\r\n]\r\nconst tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: 'edit' }]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst importShow = ref(false)\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset,\r\n  handleExportExcel\r\n} = GlobalTable({ tableApi: 'teamOfficeList', delApi: 'teamOfficeDel' })\r\n\r\nonActivated(() => { handleQuery() })\r\n\r\nconst handleButton = (id) => {\r\n  switch (id) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'import':\r\n      importShow.value = true\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    case 'del':\r\n      handleDel('单位')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleNew = () => {\r\n  id.value = ''\r\n  show.value = true\r\n}\r\nconst handleEdit = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n  exportShow.value = false\r\n  importShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.CollectiveProposalUnit {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(\r\n      100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px)\r\n    );\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAY5BA,KAAK,EAAC;AAAa;;EAuCnBA,KAAK,EAAC;AAAkB;;;;;;;;;;;uBAnD/BC,mBAAA,CAgFM,OAhFNC,UAgFM,GA/EJC,YAAA,CAUoBC,4BAAA;IAVAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IACvBC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IACvBC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC1BC,UAAU,EAAEN,MAAA,CAAAM;;IACnBC,MAAM,EAAAC,QAAA,CACf;MAAA,OAGsB,CAHtBX,YAAA,CAGsBY,mBAAA;QAV9BC,UAAA,EAO2BV,MAAA,CAAAW,OAAO;QAPlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAO2Bb,MAAA,CAAAW,OAAO,GAAAE,MAAA;QAAA;QAChBC,WAAW,EAAC,QAAQ;QACnBC,OAAK,EATxBC,SAAA,CASgChB,MAAA,CAAAC,WAAW;QACzBgB,SAAS,EAAT;;;IAVlBC,CAAA;uCAaIC,mBAAA,CAsCM,OAtCNC,UAsCM,GArCJvB,YAAA,CAoCWwB,mBAAA;IApCDC,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,IAAI;IACXC,IAAI,EAAEvB,MAAA,CAAAwB,SAAS;IACfC,QAAM,EAAEzB,MAAA,CAAA0B,iBAAiB;IACzBC,WAAU,EAAE3B,MAAA,CAAA0B;;IAlB7BE,OAAA,EAAApB,QAAA,CAmBQ;MAAA,OAGyB,CAHzBX,YAAA,CAGyBgC,0BAAA;QAHRC,IAAI,EAAC,WAAW;QAChB,mBAAiB,EAAjB,EAAiB;QACjBC,KAAK,EAAC,IAAI;QACVC,KAAK,EAAL;UACjBnC,YAAA,CAE+BgC,0BAAA;QAFdI,KAAK,EAAC,IAAI;QACVF,KAAK,EAAC,IAAI;QACVG,IAAI,EAAC;UACtBrC,YAAA,CAGyCgC,0BAAA;QAHxBI,KAAK,EAAC,MAAM;QACZ,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,MAAM;QACX,uBAAqB,EAArB;UACjBrC,YAAA,CASkBgC,0BAAA;QATDI,KAAK,EAAC,IAAI;QACV,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,iBAAiB;QACtB,uBAAqB,EAArB;;QACJN,OAAO,EAAApB,QAAA,CAChB,UAEO2B,KAHgB;UAAA,QACvBhB,mBAAA,CAEO,cAAAiB,gBAAA,CADFD,KAAK,CAACE,GAAG,CAACC,eAAe,CAACC,IAAI,iB;;QApC/CrB,CAAA;UAwCQrB,YAAA,CAGyCgC,0BAAA;QAHxBI,KAAK,EAAC,KAAK;QACX,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,aAAa;QAClB,uBAAqB,EAArB;UACjBrC,YAAA,CAGyCgC,0BAAA;QAHxBI,KAAK,EAAC,MAAM;QACZ,WAAS,EAAC,KAAK;QACfC,IAAI,EAAC,cAAc;QACnB,uBAAqB,EAArB;UACjBrC,YAAA,CACgF2C,kCAAA;QADtDjB,IAAI,EAAEvB,MAAA,CAAAyC,eAAe;QACrBC,aAAW,EAAE1C,MAAA,CAAA2C;;;IAjD/CzB,CAAA;4DAoDIC,mBAAA,CASM,OATNyB,UASM,GARJ/C,YAAA,CAO4BgD,wBAAA;IAPLC,WAAW,EAAE9C,MAAA,CAAA+C,MAAM;IArDhD,wBAAAnC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAqD0Cb,MAAA,CAAA+C,MAAM,GAAAlC,MAAA;IAAA;IACnB,WAAS,EAAEb,MAAA,CAAAgD,QAAQ;IAtDhD,qBAAApC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAsDwCb,MAAA,CAAAgD,QAAQ,GAAAnC,MAAA;IAAA;IAC1B,YAAU,EAAEb,MAAA,CAAAiD,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAEnD,MAAA,CAAAC,WAAW;IACxBmD,eAAc,EAAEpD,MAAA,CAAAC,WAAW;IAC3BoD,KAAK,EAAErD,MAAA,CAAAsD,MAAM;IACdC,UAAU,EAAV;qHAEjB1D,YAAA,CAOmB2D,2BAAA;IArEvB9C,UAAA,EA8D+BV,MAAA,CAAAyD,UAAU;IA9DzC,uBAAA7C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA8D+Bb,MAAA,CAAAyD,UAAU,GAAA5C,MAAA;IAAA;IACnB0B,IAAI,EAAC;;IA/D3BX,OAAA,EAAApB,QAAA,CAgEM;MAAA,OAI+D,CAJ/DX,YAAA,CAI+D6D,2BAAA;QAJ7CnB,IAAI,EAAC,QAAQ;QACZoB,QAAQ,EAAE3D,MAAA,CAAA2D,QAAQ;QAClBC,MAAM,EAAE5D,MAAA,CAAA6D,YAAY;QACrBC,MAAM,EAAC,6BAA6B;QACnCC,eAAa,EAAE/D,MAAA,CAAAgE;;;IApExC9C,CAAA;qCAsEIrB,YAAA,CAKmB2D,2BAAA;IA3EvB9C,UAAA,EAsE+BV,MAAA,CAAAiE,UAAU;IAtEzC,uBAAArD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAsE+Bb,MAAA,CAAAiE,UAAU,GAAApD,MAAA;IAAA;IACnB0B,IAAI,EAAC;;IAvE3BX,OAAA,EAAApB,QAAA,CAwEM;MAAA,OAE0D,CAF1DX,YAAA,CAE0DqE,2BAAA;QAFxC3B,IAAI,EAAC,QAAQ;QACbT,IAAI,EAAC,2BAA2B;QAC/BqC,UAAQ,EAAEnE,MAAA,CAAAgE;;;IA1EnC9C,CAAA;qCA4EIrB,YAAA,CAImB2D,2BAAA;IAhFvB9C,UAAA,EA4E+BV,MAAA,CAAAoE,IAAI;IA5EnC,uBAAAxD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA4E+Bb,MAAA,CAAAoE,IAAI,GAAAvD,MAAA;IAAA;IACZ0B,IAAI,EAAEvC,MAAA,CAAAqE,EAAE;;IA7E/BzC,OAAA,EAAApB,QAAA,CA8EM;MAAA,OACkF,CADlFX,YAAA,CACkFG,MAAA;QADnDqE,EAAE,EAAErE,MAAA,CAAAqE,EAAE;QACNF,UAAQ,EAAEnE,MAAA,CAAAgE;;;IA/E/C9C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}