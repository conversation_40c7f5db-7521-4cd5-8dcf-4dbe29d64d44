import { createRouter, createWebHistory } from 'vue-router'
/**
 * 提案配置
 */
// 集体提案单位
const CollectiveProposalUnit = () => import('@/views/SuggestConfig/CollectiveProposalUnit/CollectiveProposalUnit')
// 集体提案单位用户
const CollectiveProposalUnitUser = () =>
  import('@/views/SuggestConfig/CollectiveProposalUnitUser/CollectiveProposalUnitUser')
// 集体提案单位用户提交
const SubmitCollectiveProposalUnitUser = () =>
  import('@/views/SuggestConfig/CollectiveProposalUnitUser/SubmitCollectiveProposalUnitUser')
// 提案单位
const SuggestUnit = () => import('@/views/SuggestConfig/SuggestUnit/SuggestUnit')
// 提案单位用户
const SuggestUnitUser = () => import('@/views/SuggestConfig/SuggestUnitUser/SuggestUnitUser')
// 提案单位用户提交
const SuggestUnitUserSubmit = () => import('@/views/SuggestConfig/SuggestUnitUser/SuggestUnitUserSubmit')
// 提案届次编号管理
const SuggestNumbering = () => import('@/views/SuggestConfig/SuggestNumbering/SuggestNumbering')
// 提案分类
const SuggestType = () => import('@/views/SuggestConfig/SuggestType/SuggestType')
// 相似度详情
// const SimilarityDetails = () => import('@/views/SimilarityDetails/SimilarityDetails')
/**
 * 提案者
 */
// 提交提案
const SubmitSuggest = () => import('@/views/BehalfSuggest/SubmitSuggest/SubmitSuggest')
// 我领衔的提案
const MyLedSuggest = () => import('@/views/BehalfSuggest/MyLedSuggest/MyLedSuggest')
// 我附议的提案
const MyJointSuggest = () => import('@/views/BehalfSuggest/MyJointSuggest/MyJointSuggest')
// 草稿箱
const SuggestDraftBox = () => import('@/views/BehalfSuggest/SuggestDraftBox/SuggestDraftBox')
// 所有提案
const PersonalAllSuggest = () => import('@/views/PersonalAllSuggest/PersonalAllSuggest')
// 相似度对比页面
const TextQueryTool = () => import('@/views/BehalfSuggest/ProposalManagementTool/TextQueryTool')

// 提案线索
const ProposalClueList = () => import('@/views/ProposalClue/ProposalClueList')
const SubmitProposalClue = () => import('@/views/ProposalClue/SubmitProposalClue')
const ProposalClueDetail = () => import('@/views/ProposalClue/ProposalClueDetail')
/**
 * 提案管理
 */
// 所有提案
const AllSuggest = () => import('@/views/AllSuggest/AllSuggest')
const SuggestControls = () => import('@/views/SuggestControls/SuggestControls')
// 提案统计
const SuggestStatistics = () => import('@/views/SuggestStatistics/SuggestStatistics')
// 提案统计列表
const SuggestStatisticsList = () => import('@/views/SuggestStatistics/SuggestStatisticsList')
// 提案对比
const SuggestCompare = () => import('@/views/SuggestCompare/SuggestCompare')
// 提案详情
const SuggestDetail = () => import('@/views/SuggestDetail/SuggestDetail')
// 提案分类
const SuggestedClassification = () => import('@/views/SuggestedClassification/SuggestedClassification')
// 提案细分
const SuggestedSubdivide = () => import('@/views/SuggestedClassification/SuggestedSubdivide')
/**
 * 提案审查
 */
// 通用提案审查
const SuggestReview = () => import('@/views/SuggestReview/SuggestReview')
/**
 * 不接收提案
 */
const DoNotReceiveSuggest = () => import('@/views/DoNotReceiveSuggest/DoNotReceiveSuggest')
/**
 * 提案交办
 */
// 通用提案交办
const SuggestAssign = () => import('@/views/SuggestAssign/SuggestAssign')
// 建议预交办
const SuggestAdvanceAssign = () => import('@/views/SuggestAdvanceAssign/SuggestAdvanceAssign')
/**
 * 提案并案
 */
// 并案提案
const MergerProposalContainer = () => import('@/views/MergerProposal/MergerProposalContainer')
// 已并案提案
const HaveMergerProposal = () => import('@/views/MergerProposal/HaveMergerProposal')

// 办理中提案
const SuggestTransact = () => import('@/views/SuggestTransact/SuggestTransact')
// 申请延期提案
const SuggestApplyForPostpone = () => import('@/views/SuggestApplyForPostpone/SuggestApplyForPostpone')
// 申请调整提案
const SuggestApplyForAdjust = () => import('@/views/SuggestApplyForAdjust/SuggestApplyForAdjust')
// 预交办申请调整建议
const SuggestPreApplyForAdjust = () => import('@/views/SuggestPreApplyForAdjust/SuggestPreApplyForAdjust')
// 已答复提案
const SuggestReply = () => import('@/views/SuggestReply/SuggestReply')
// 跟踪办理提案
const SuggestTrackTransact = () => import('@/views/SuggestTrackTransact/SuggestTrackTransact')
// 已办结提案
const SuggestConclude = () => import('@/views/SuggestConclude/SuggestConclude')
/**
 * 承办单位
 */
// 所有提案
const UnitAllSuggest = () => import('@/views/UnitAllSuggest/UnitAllSuggest')
// 预交办建议
const UnitSuggestAdvanceAssign = () => import('@/views/UnitSuggestAdvanceAssign/UnitSuggestAdvanceAssign')
// 办理中提案
const UnitSuggestTransact = () => import('@/views/UnitSuggestTransact/UnitSuggestTransact')
// 已答复提案
const UnitSuggestReply = () => import('@/views/UnitSuggestReply/UnitSuggestReply')
// 跟踪办理提案
const UnitSuggestTrackTransact = () => import('@/views/UnitSuggestTrackTransact/UnitSuggestTrackTransact')
// 已办结提案
const UnitSuggestConclude = () => import('@/views/UnitSuggestConclude/UnitSuggestConclude')
// 已办结提案
const UnitSummaryReport = () => import('@/views/UnitSummaryReport/UnitSummaryReport')

/**
 * 提案线索征集
 */
// 征集文案管理
const SuggestDocument = () => import('@/views/SuggestClue/SuggestDocument/SuggestDocument')
// 提案线索征集管理
const SuggestClueControl = () => import('@/views/SuggestClue/SuggestClueControl/SuggestClueControl')
// 提案线索征集添加
const SuggestClueAdd = () => import('@/views/SuggestClue/SuggestClueAdd/SuggestClueAdd')
// 提案线索征集选登
const SuggestClueRegister = () => import('@/views/SuggestClue/SuggestClueRegister/SuggestClueRegister')
// 我的线索
const SuggestClueMine = () => import('@/views/SuggestClue/SuggestClueMine/SuggestClueMine')
// 超级修改
const SuperEdit = () => import('@/views/SuperEdit/SuperEdit.vue')
// 批量导出
const batchExport = () => import('@/views/batchExport/batchExport.vue')
// 历史提案
const HistoricalProposal = () => import('@/views/HistoricalProposal/HistoricalProposal.vue')

const routes = [
  {
    path: '/CollectiveProposalUnit',
    name: 'CollectiveProposalUnit',
    component: CollectiveProposalUnit,
    meta: { moduleName: 'main' }
  },
  {
    path: '/CollectiveProposalUnitUser',
    name: 'CollectiveProposalUnitUser',
    component: CollectiveProposalUnitUser,
    meta: { moduleName: 'main' }
  },
  {
    path: '/SubmitCollectiveProposalUnitUser',
    name: 'SubmitCollectiveProposalUnitUser',
    component: SubmitCollectiveProposalUnitUser,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestUnit', name: 'SuggestUnit', component: SuggestUnit, meta: { moduleName: 'main' } },
  { path: '/SuggestUnitUser', name: 'SuggestUnitUser', component: SuggestUnitUser, meta: { moduleName: 'main' } },
  {
    path: '/SuggestUnitUserSubmit',
    name: 'SuggestUnitUserSubmit',
    component: SuggestUnitUserSubmit,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestNumbering', name: 'SuggestNumbering', component: SuggestNumbering, meta: { moduleName: 'main' } },
  { path: '/SuggestType', name: 'SuggestType', component: SuggestType, meta: { moduleName: 'main' } },
  // { path: '/SimilarityDetails', name: 'SimilarityDetails', component: SimilarityDetails, meta: { moduleName: 'main' } },
  { path: '/SubmitSuggest', name: 'SubmitSuggest', component: SubmitSuggest, meta: { moduleName: 'main' } },
  { path: '/MyLedSuggest', name: 'MyLedSuggest', component: MyLedSuggest, meta: { moduleName: 'main' } },
  { path: '/MyJointSuggest', name: 'MyJointSuggest', component: MyJointSuggest, meta: { moduleName: 'main' } },
  { path: '/SuggestDraftBox', name: 'SuggestDraftBox', component: SuggestDraftBox, meta: { moduleName: 'main' } },
  {
    path: '/PersonalAllSuggest',
    name: 'PersonalAllSuggest',
    component: PersonalAllSuggest,
    meta: { moduleName: 'main' }
  },
  { path: '/TextQueryTool', name: 'TextQueryTool', component: TextQueryTool, meta: { moduleName: 'main' } },
  { path: '/ProposalClueList', name: 'ProposalClueList', component: ProposalClueList, meta: { moduleName: 'main' } },
  {
    path: '/SubmitProposalClue',
    name: 'SubmitProposalClue',
    component: SubmitProposalClue,
    meta: { moduleName: 'main' }
  },
  {
    path: '/ProposalClueDetail',
    name: 'ProposalClueDetail',
    component: ProposalClueDetail,
    meta: { moduleName: 'main' }
  },
  { path: '/AllSuggest', name: 'AllSuggest', component: AllSuggest, meta: { moduleName: 'main' } },
  { path: '/SuggestControls', name: 'SuggestControls', component: SuggestControls, meta: { moduleName: 'main' } },
  { path: '/SuggestStatistics', name: 'SuggestStatistics', component: SuggestStatistics, meta: { moduleName: 'main' } },
  {
    path: '/SuggestStatisticsList',
    name: 'SuggestStatisticsList',
    component: SuggestStatisticsList,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestCompare', name: 'SuggestCompare', component: SuggestCompare, meta: { moduleName: 'main' } },
  { path: '/SuggestDetail', name: 'SuggestDetail', component: SuggestDetail, meta: { moduleName: 'main' } },
  { path: '/SuggestReview', name: 'SuggestReview', component: SuggestReview, meta: { moduleName: 'main' } },
  {
    path: '/DoNotReceiveSuggest',
    name: 'DoNotReceiveSuggest',
    component: DoNotReceiveSuggest,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestAssign', name: 'SuggestAssign', component: SuggestAssign, meta: { moduleName: 'main' } },
  {
    path: '/SuggestAdvanceAssign',
    name: 'SuggestAdvanceAssign',
    component: SuggestAdvanceAssign,
    meta: { moduleName: 'main' }
  },
  {
    path: '/MergerProposalContainer',
    name: 'MergerProposalContainer',
    component: MergerProposalContainer,
    meta: { moduleName: 'main' }
  },
  {
    path: '/HaveMergerProposal',
    name: 'HaveMergerProposal',
    component: HaveMergerProposal,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestTransact', name: 'SuggestTransact', component: SuggestTransact, meta: { moduleName: 'main' } },
  {
    path: '/SuggestApplyForPostpone',
    name: 'SuggestApplyForPostpone',
    component: SuggestApplyForPostpone,
    meta: { moduleName: 'main' }
  },
  {
    path: '/SuggestPreApplyForAdjust',
    name: 'SuggestPreApplyForAdjust',
    component: SuggestPreApplyForAdjust,
    meta: { moduleName: 'main' }
  },
  {
    path: '/SuggestApplyForAdjust',
    name: 'SuggestApplyForAdjust',
    component: SuggestApplyForAdjust,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestReply', name: 'SuggestReply', component: SuggestReply, meta: { moduleName: 'main' } },
  {
    path: '/SuggestTrackTransact',
    name: 'SuggestTrackTransact',
    component: SuggestTrackTransact,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestConclude', name: 'SuggestConclude', component: SuggestConclude, meta: { moduleName: 'main' } },
  { path: '/UnitAllSuggest', name: 'UnitAllSuggest', component: UnitAllSuggest, meta: { moduleName: 'main' } },
  {
    path: '/UnitSuggestAdvanceAssign',
    name: 'UnitSuggestAdvanceAssign',
    component: UnitSuggestAdvanceAssign,
    meta: { moduleName: 'main' }
  },
  {
    path: '/UnitSuggestTransact',
    name: 'UnitSuggestTransact',
    component: UnitSuggestTransact,
    meta: { moduleName: 'main' }
  },
  { path: '/UnitSuggestReply', name: 'UnitSuggestReply', component: UnitSuggestReply, meta: { moduleName: 'main' } },
  {
    path: '/UnitSuggestTrackTransact',
    name: 'UnitSuggestTrackTransact',
    component: UnitSuggestTrackTransact,
    meta: { moduleName: 'main' }
  },
  {
    path: '/UnitSuggestConclude',
    name: 'UnitSuggestConclude',
    component: UnitSuggestConclude,
    meta: { moduleName: 'main' }
  },
  { path: '/UnitSummaryReport', name: 'UnitSummaryReport', component: UnitSummaryReport, meta: { moduleName: 'main' } },
  { path: '/SuggestDocument', name: 'SuggestDocument', component: SuggestDocument, meta: { moduleName: 'main' } },
  {
    path: '/SuggestClueControl',
    name: 'SuggestClueControl',
    component: SuggestClueControl,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestClueAdd', name: 'SuggestClueAdd', component: SuggestClueAdd, meta: { moduleName: 'main' } },
  {
    path: '/SuggestClueRegister',
    name: 'SuggestClueRegister',
    component: SuggestClueRegister,
    meta: { moduleName: 'main' }
  },
  { path: '/SuggestClueMine', name: 'SuggestClueMine', component: SuggestClueMine, meta: { moduleName: 'main' } },
  { path: '/SuggestedClassification', name: 'SuggestedClassification', component: SuggestedClassification, meta: { moduleName: 'main' } },
  { path: '/SuggestedSubdivide', name: 'SuggestedSubdivide', component: SuggestedSubdivide, meta: { moduleName: 'main' } },
  { path: '/SuperEdit', name: 'SuperEdit', component: SuperEdit, meta: { moduleName: 'main' } },
  { path: '/batchExport', name: 'batchExport', component: batchExport, meta: { moduleName: 'main' } },
  { path: '/HistoricalProposal', name: 'HistoricalProposal', component: HistoricalProposal, meta: { moduleName: 'main' } }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export { routes }
export default router
