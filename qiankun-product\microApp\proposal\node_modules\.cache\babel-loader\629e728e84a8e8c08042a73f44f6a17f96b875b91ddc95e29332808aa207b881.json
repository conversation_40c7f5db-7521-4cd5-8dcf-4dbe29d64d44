{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestDocument\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect,\n    onSortChange: $setup.handleSortChange,\n    \"header-cell-class-name\": $setup.handleHeaderClass\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"title\",\n        label: \"文案类标题\",\n        width: \"280\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"content\",\n        label: \"文案简介\",\n        width: \"725\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"userName\",\n        label: \"修改人\",\n        width: \"180\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"修改时间\",\n        width: \"280\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString($setup.format(scope.row.createDate)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        width: \"120\",\n        fixed: \"right\",\n        \"class-name\": \"globalTableCustom\"\n      }, {\n        header: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"操作\")]);\n        }),\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_button, {\n            onClick: function onClick($event) {\n              return $setup.handleEdit(scope.row);\n            },\n            type: \"primary\",\n            plain: \"\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[4] || (_cache[4] = [_createTextVNode(\"编辑\")]);\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\", \"onSortChange\", \"header-cell-class-name\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: $setup.id ? '编辑文案' : '新增文案'\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SuggestDocumentSubmit\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_table", "ref", "data", "$setup", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "onSortChange", "handleSortChange", "handleHeaderClass", "default", "_withCtx", "_component_el_table_column", "type", "width", "fixed", "prop", "label", "scope", "_createTextVNode", "_toDisplayString", "format", "row", "createDate", "_", "header", "_cache", "_component_el_button", "onClick", "$event", "handleEdit", "plain", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "handleQuery", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "modelValue", "show", "name", "id", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestDocument\\SuggestDocument.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestDocument\">\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\"\r\n                @sort-change=\"handleSortChange\"\r\n                :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column prop=\"title\"\r\n                         label=\"文案类标题\"\r\n                         width=\"280\" />\r\n        <el-table-column prop=\"content\"\r\n                         label=\"文案简介\"\r\n                         width=\"725\" />\r\n        <el-table-column prop=\"userName\"\r\n                         label=\"修改人\"\r\n                         width=\"180\" />\r\n        <el-table-column label=\"修改时间\"\r\n                         width=\"280\">\r\n          <template #default=\"scope\">{{ format(scope.row.createDate) }}</template>\r\n        </el-table-column>\r\n        <el-table-column width=\"120\"\r\n                         fixed=\"right\"\r\n                         class-name=\"globalTableCustom\">\r\n          <template #header>操作</template>\r\n          <template #default=\"scope\">\r\n            <el-button @click=\"handleEdit(scope.row)\"\r\n                       type=\"primary\"\r\n                       plain>编辑</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      :name=\"id ? '编辑文案' : '新增文案'\">\r\n      <SuggestDocumentSubmit :id=\"id\"\r\n                             @callback=\"callback\"></SuggestDocumentSubmit>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestDocument' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SuggestDocumentSubmit from './components/SuggestDocumentSubmit'\r\nimport { user } from 'common/js/system_var.js'\r\nconst show = ref(false)\r\nconst id = ref('')\r\nconst {\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n} = GlobalTable({ tableApi: 'proposalClueTheme', })\r\n\r\nonActivated(() => { handleQuery(); userLog() })\r\nconst handleEdit = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\n\r\nconst userLog = (() => {\r\n  console.log(user.value)\r\n})\r\n\r\nconst callback = () => {\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestDocument {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - 20px);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAa;;EAsCnBA,KAAK,EAAC;AAAkB;;;;;;;uBAvC/BC,mBAAA,CAsDM,OAtDNC,UAsDM,GArDJC,mBAAA,CAqCM,OArCNC,UAqCM,GApCJC,YAAA,CAkCWC,mBAAA;IAlCDC,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,IAAI;IACXC,IAAI,EAAEC,MAAA,CAAAC,SAAS;IACfC,QAAM,EAAEF,MAAA,CAAAG,iBAAiB;IACzBC,WAAU,EAAEJ,MAAA,CAAAG,iBAAiB;IAC7BE,YAAW,EAAEL,MAAA,CAAAM,gBAAgB;IAC7B,wBAAsB,EAAEN,MAAA,CAAAO;;IATzCC,OAAA,EAAAC,QAAA,CAUQ;MAAA,OAGyB,CAHzBb,YAAA,CAGyBc,0BAAA;QAHRC,IAAI,EAAC,WAAW;QAChB,mBAAiB,EAAjB,EAAiB;QACjBC,KAAK,EAAC,IAAI;QACVC,KAAK,EAAL;UACjBjB,YAAA,CAE+Bc,0BAAA;QAFdI,IAAI,EAAC,OAAO;QACZC,KAAK,EAAC,OAAO;QACbH,KAAK,EAAC;UACvBhB,YAAA,CAE+Bc,0BAAA;QAFdI,IAAI,EAAC,SAAS;QACdC,KAAK,EAAC,MAAM;QACZH,KAAK,EAAC;UACvBhB,YAAA,CAE+Bc,0BAAA;QAFdI,IAAI,EAAC,UAAU;QACfC,KAAK,EAAC,KAAK;QACXH,KAAK,EAAC;UACvBhB,YAAA,CAGkBc,0BAAA;QAHDK,KAAK,EAAC,MAAM;QACZH,KAAK,EAAC;;QACVJ,OAAO,EAAAC,QAAA,CAAS,UAAkCO,KAApC;UAAA,QAzBnCC,gBAAA,CAAAC,gBAAA,CAyBwClB,MAAA,CAAAmB,MAAM,CAACH,KAAK,CAACI,GAAG,CAACC,UAAU,kB;;QAzBnEC,CAAA;UA2BQ1B,YAAA,CASkBc,0BAAA;QATDE,KAAK,EAAC,KAAK;QACXC,KAAK,EAAC,OAAO;QACb,YAAU,EAAC;;QACfU,MAAM,EAAAd,QAAA,CAAC;UAAA,OAAEe,MAAA,QAAAA,MAAA,OA9B9BP,gBAAA,CA8B4B,IAAE,E;;QACTT,OAAO,EAAAC,QAAA,CAChB,UAE+BO,KAHR;UAAA,QACvBpB,YAAA,CAE+B6B,oBAAA;YAFnBC,OAAK,WAALA,OAAKA,CAAAC,MAAA;cAAA,OAAE3B,MAAA,CAAA4B,UAAU,CAACZ,KAAK,CAACI,GAAG;YAAA;YAC5BT,IAAI,EAAC,SAAS;YACdkB,KAAK,EAAL;;YAlCvBrB,OAAA,EAAAC,QAAA,CAkC6B;cAAA,OAAEe,MAAA,QAAAA,MAAA,OAlC/BP,gBAAA,CAkC6B,IAAE,E;;YAlC/BK,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA;sGAwCI5B,mBAAA,CASM,OATNoC,UASM,GARJlC,YAAA,CAO4BmC,wBAAA;IAPLC,WAAW,EAAEhC,MAAA,CAAAiC,MAAM;IAzChD,wBAAAT,MAAA,QAAAA,MAAA,gBAAAG,MAAA;MAAA,OAyC0C3B,MAAA,CAAAiC,MAAM,GAAAN,MAAA;IAAA;IACnB,WAAS,EAAE3B,MAAA,CAAAkC,QAAQ;IA1ChD,qBAAAV,MAAA,QAAAA,MAAA,gBAAAG,MAAA;MAAA,OA0CwC3B,MAAA,CAAAkC,QAAQ,GAAAP,MAAA;IAAA;IAC1B,YAAU,EAAE3B,MAAA,CAAAmC,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAErC,MAAA,CAAAsC,WAAW;IACxBC,eAAc,EAAEvC,MAAA,CAAAsC,WAAW;IAC3BE,KAAK,EAAExC,MAAA,CAAAyC,MAAM;IACdC,UAAU,EAAV;qHAEjB9C,YAAA,CAImB+C,2BAAA;IAtDvBC,UAAA,EAkD+B5C,MAAA,CAAA6C,IAAI;IAlDnC,uBAAArB,MAAA,QAAAA,MAAA,gBAAAG,MAAA;MAAA,OAkD+B3B,MAAA,CAAA6C,IAAI,GAAAlB,MAAA;IAAA;IACZmB,IAAI,EAAE9C,MAAA,CAAA+C,EAAE;;IAnD/BvC,OAAA,EAAAC,QAAA,CAoDM;MAAA,OACoE,CADpEb,YAAA,CACoEI,MAAA;QAD5C+C,EAAE,EAAE/C,MAAA,CAAA+C,EAAE;QACNC,UAAQ,EAAEhD,MAAA,CAAAiD;;;IArDxC3B,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}