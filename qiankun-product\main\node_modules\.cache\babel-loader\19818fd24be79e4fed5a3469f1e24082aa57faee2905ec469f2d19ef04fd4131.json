{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref } from 'vue';\nimport { useStore } from 'vuex';\nimport { setting, content_style, guid, trigerUpload } from '../../AiToolBox/AiToolBox.js';\nimport { elAttr } from './IntelligentErrorCorrection.js';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'IntelligentErrorCorrection'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var svg = '<path class=\"path\" d=\"M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15\" style=\"stroke-width: 4px; fill: rgba(0, 0, 0, 0)\"/>';\n    var loading = ref(false);\n    var loadingText = ref('');\n    var wordRef = ref();\n    var oldId = ref('');\n    var arrId = ref([]);\n    var content = ref('');\n    var checklist = ref([]);\n    var handleImport = function handleImport() {\n      trigerUpload().then(function (file) {\n        var fileType = file.name.substring(file.name.lastIndexOf('.') + 1);\n        var isShow = ['doc', 'docx', 'wps'].includes(fileType);\n        if (!isShow) return ElMessage({\n          type: 'warning',\n          message: `仅支持${['doc', 'docx', 'wps'].join('、')}格式!`\n        });\n        loading.value = true;\n        fileWordUpload(file);\n      });\n    };\n    var fileWordUpload = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(file) {\n        var param, _yield$api$fileword2h, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              param = new FormData();\n              param.append('file', file);\n              _context.next = 5;\n              return api.fileword2html(param);\n            case 5:\n              _yield$api$fileword2h = _context.sent;\n              data = _yield$api$fileword2h.data;\n              content.value = data.replace(/<\\/?html[^>]*>/g, '').replace(/<head\\b[^<]*(?:(?!<\\/head>)<[^<]*)*<\\/head>/gi, '').replace(/<\\/?body[^>]*>/g, '').replace(/<\\/?div[^>]*>/g, '');\n              loading.value = false;\n              _context.next = 14;\n              break;\n            case 11:\n              _context.prev = 11;\n              _context.t0 = _context[\"catch\"](0);\n              loading.value = false;\n            case 14:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 11]]);\n      }));\n      return function fileWordUpload(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleWrongWord = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _data$checklist;\n        var _yield$api$typingVeri, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              if (content.value) {\n                _context2.next = 2;\n                break;\n              }\n              return _context2.abrupt(\"return\", ElMessage({\n                type: 'warning',\n                message: '请先输入内容在进行智能纠错！'\n              }));\n            case 2:\n              loading.value = true;\n              _context2.next = 5;\n              return api.typingVerification({\n                text: content.value.replace(/&ldquo;/gi, '“').replace(/&rdquo;/gi, '”')\n              });\n            case 5:\n              _yield$api$typingVeri = _context2.sent;\n              data = _yield$api$typingVeri.data;\n              oldId.value = '';\n              arrId.value = [];\n              checklist.value = (data === null || data === void 0 || (_data$checklist = data.checklist) === null || _data$checklist === void 0 ? void 0 : _data$checklist.map(function (v) {\n                return _objectSpread(_objectSpread({}, v), {}, {\n                  id: guid()\n                });\n              })) || [];\n              content.value = (data === null || data === void 0 ? void 0 : data.replace_text) || '';\n              loading.value = false;\n            case 12:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function handleWrongWord() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var handleDetails = function handleDetails(row) {\n      var _wordRef$value, _iframe$contentWindow;\n      var iframe = (_wordRef$value = wordRef.value) === null || _wordRef$value === void 0 || (_wordRef$value = _wordRef$value.getEditor()) === null || _wordRef$value === void 0 ? void 0 : _wordRef$value.iframeElement;\n      var body = iframe === null || iframe === void 0 || (_iframe$contentWindow = iframe.contentWindow) === null || _iframe$contentWindow === void 0 || (_iframe$contentWindow = _iframe$contentWindow.document) === null || _iframe$contentWindow === void 0 ? void 0 : _iframe$contentWindow.body;\n      var elList = (body === null || body === void 0 ? void 0 : body.childNodes) || [];\n      if (oldId.value) {\n        var oldObj = elAttr(elList, oldId.value);\n        if (oldObj.elArr.length) {\n          for (var index = 0; index < oldObj.elArr.length; index++) {\n            var item = oldObj.elArr[index];\n            if (!index) iframe.contentWindow.scrollTo(0, item.offsetTop - 100);\n            item.style.color = '';\n            item.style.backgroundColor = '';\n          }\n        }\n      }\n      oldId.value = row.position + '';\n      var obj = elAttr(elList, row.position + '');\n      if (obj.elArr.length) {\n        for (var _index = 0; _index < obj.elArr.length; _index++) {\n          var _item = obj.elArr[_index];\n          if (!_index) iframe.contentWindow.scrollTo(0, _item.offsetTop - 100);\n          _item.style.color = '#fff';\n          _item.style.backgroundColor = 'red';\n        }\n      }\n    };\n    var handleReplace = function handleReplace(row) {\n      var _wordRef$value2, _iframe$contentWindow2;\n      var iframe = (_wordRef$value2 = wordRef.value) === null || _wordRef$value2 === void 0 || (_wordRef$value2 = _wordRef$value2.getEditor()) === null || _wordRef$value2 === void 0 ? void 0 : _wordRef$value2.iframeElement;\n      var body = iframe === null || iframe === void 0 || (_iframe$contentWindow2 = iframe.contentWindow) === null || _iframe$contentWindow2 === void 0 || (_iframe$contentWindow2 = _iframe$contentWindow2.document) === null || _iframe$contentWindow2 === void 0 ? void 0 : _iframe$contentWindow2.body;\n      var elList = (body === null || body === void 0 ? void 0 : body.childNodes) || [];\n      var obj = elAttr(elList, row.position + '');\n      if (obj.elArr.length > 1) {\n        var styleStr = '';\n        for (var key in obj.styleObj) {\n          styleStr += `${key}:${obj.styleObj[key]};`;\n        }\n        for (var index = 0; index < obj.elArr.length; index++) {\n          var item = obj.elArr[index];\n          var elParent = item;\n          if (!index) {\n            elParent.insertAdjacentHTML('beforebegin', `<span style=\"${styleStr}\">${row.suggest[0]}</span>`);\n          }\n          elParent.parentNode.removeChild(elParent);\n        }\n      } else {\n        obj.elArr[0].insertAdjacentHTML('beforebegin', row.suggest[0]);\n        obj.elArr[0].parentNode.removeChild(obj.elArr[0]);\n      }\n      arrId.value.push(row.id);\n    };\n    var handleAllReplace = function handleAllReplace() {\n      for (var index = 0; index < checklist.value.length; index++) {\n        var _item$suggest;\n        var item = checklist.value[index];\n        if (!arrId.value.includes(item.id) && item !== null && item !== void 0 && (_item$suggest = item.suggest) !== null && _item$suggest !== void 0 && _item$suggest.length) {\n          handleReplace(item);\n        }\n      }\n    };\n    var handleExportWord = function handleExportWord() {\n      store.commit('setExportWordHtmlObj', {\n        code: 'exportWord',\n        name: '智能纠错 --- 文档导出',\n        key: 'content',\n        data: {\n          content: content.value\n        }\n      });\n    };\n    var __returned__ = {\n      store,\n      svg,\n      loading,\n      loadingText,\n      wordRef,\n      oldId,\n      arrId,\n      content,\n      checklist,\n      handleImport,\n      fileWordUpload,\n      handleWrongWord,\n      handleDetails,\n      handleReplace,\n      handleAllReplace,\n      handleExportWord,\n      get api() {\n        return api;\n      },\n      ref,\n      get useStore() {\n        return useStore;\n      },\n      get setting() {\n        return setting;\n      },\n      get content_style() {\n        return content_style;\n      },\n      get guid() {\n        return guid;\n      },\n      get trigerUpload() {\n        return trigerUpload;\n      },\n      get elAttr() {\n        return elAttr;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "useStore", "setting", "content_style", "guid", "trigerUpload", "elAttr", "ElMessage", "__default__", "store", "svg", "loading", "loadingText", "wordRef", "oldId", "arrId", "content", "checklist", "handleImport", "file", "fileType", "substring", "lastIndexOf", "isShow", "includes", "message", "join", "fileWordUpload", "_ref2", "_callee", "param", "_yield$api$fileword2h", "data", "_callee$", "_context", "FormData", "append", "fileword2html", "replace", "t0", "_x", "handleWrongWord", "_ref3", "_callee2", "_data$checklist", "_yield$api$typingVeri", "_callee2$", "_context2", "typingVerification", "text", "map", "_objectSpread", "id", "replace_text", "handleDetails", "row", "_wordRef$value", "_iframe$contentWindow", "iframe", "getEditor", "iframeElement", "body", "contentWindow", "document", "elList", "childNodes", "oldObj", "el<PERSON>rr", "index", "item", "scrollTo", "offsetTop", "style", "color", "backgroundColor", "position", "obj", "handleReplace", "_wordRef$value2", "_iframe$contentWindow2", "styleStr", "key", "styleObj", "<PERSON><PERSON><PERSON><PERSON>", "insertAdjacentHTML", "suggest", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "handleAllReplace", "_item$suggest", "handleExportWord", "commit", "code"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiToolBoxFunction/IntelligentErrorCorrection/IntelligentErrorCorrection.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"IntelligentErrorCorrection\"\r\n    v-loading=\"loading\"\r\n    :element-loading-spinner=\"svg\"\r\n    :lement-loading-text=\"loadingText\"\r\n    element-loading-svg-view-box=\"-10, -10, 50, 50\">\r\n    <div class=\"IntelligentErrorCorrectionHead\">\r\n      <div class=\"IntelligentErrorCorrectionButton\">\r\n        <div class=\"IntelligentErrorCorrectionButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleImport\">文档导入</el-button>\r\n        </div>\r\n        <div class=\"IntelligentErrorCorrectionButtonItem\">\r\n          <el-button type=\"primary\">去一键排版</el-button>\r\n          <el-button type=\"primary\" @click=\"handleExportWord\">导出</el-button>\r\n        </div>\r\n      </div>\r\n      <div class=\"IntelligentErrorCorrectionButton\">\r\n        <div class=\"IntelligentErrorCorrectionButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleWrongWord\">智能纠错</el-button>\r\n        </div>\r\n        <div class=\"IntelligentErrorCorrectionButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleAllReplace\">全部替换</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"IntelligentErrorCorrectionBody\">\r\n      <div class=\"IntelligentErrorCorrectionBodyLeft\">\r\n        <TinyMceEditor ref=\"wordRef\" v-model=\"content\" :setting=\"setting\" :content_style=\"content_style\" />\r\n      </div>\r\n      <div class=\"IntelligentErrorCorrectionBodyRight\">\r\n        <div class=\"globalTable\">\r\n          <el-table :data=\"checklist\">\r\n            <el-table-column label=\"错误类型\" min-width=\"120\" prop=\"type.name\" />\r\n            <el-table-column label=\"错误内容\" min-width=\"120\">\r\n              <template #default=\"scope\">\r\n                <el-link @click=\"handleDetails(scope.row)\" :disabled=\"arrId.includes(scope.row.id)\" type=\"primary\">\r\n                  {{ scope.row.word }}\r\n                </el-link>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"修改建议\" min-width=\"120\">\r\n              <template #default=\"scope\">{{ scope.row?.suggest[0] || scope.row?.explanation }}</template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"100\" fixed=\"right\" class-name=\"globalTableCustom\">\r\n              <template #default=\"scope\">\r\n                <el-button\r\n                  @click=\"handleReplace(scope.row)\"\r\n                  :disabled=\"arrId.includes(scope.row.id) || !scope.row?.suggest?.length\"\r\n                  type=\"primary\"\r\n                  plain>\r\n                  替换\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'IntelligentErrorCorrection' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { setting, content_style, guid, trigerUpload } from '../../AiToolBox/AiToolBox.js'\r\nimport { elAttr } from './IntelligentErrorCorrection.js'\r\nimport { ElMessage } from 'element-plus'\r\n\r\nconst store = useStore()\r\n\r\nconst svg =\r\n  '<path class=\"path\" d=\"M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15\" style=\"stroke-width: 4px; fill: rgba(0, 0, 0, 0)\"/>'\r\n\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst wordRef = ref()\r\nconst oldId = ref('')\r\nconst arrId = ref([])\r\nconst content = ref('')\r\nconst checklist = ref([])\r\n\r\nconst handleImport = () => {\r\n  trigerUpload().then((file) => {\r\n    const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n    const isShow = ['doc', 'docx', 'wps'].includes(fileType)\r\n    if (!isShow) return ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'wps'].join('、')}格式!` })\r\n    loading.value = true\r\n    fileWordUpload(file)\r\n  })\r\n}\r\nconst fileWordUpload = async (file) => {\r\n  try {\r\n    const param = new FormData()\r\n    param.append('file', file)\r\n    const { data } = await api.fileword2html(param)\r\n    content.value = data\r\n      .replace(/<\\/?html[^>]*>/g, '')\r\n      .replace(/<head\\b[^<]*(?:(?!<\\/head>)<[^<]*)*<\\/head>/gi, '')\r\n      .replace(/<\\/?body[^>]*>/g, '')\r\n      .replace(/<\\/?div[^>]*>/g, '')\r\n    loading.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nconst handleWrongWord = async () => {\r\n  if (!content.value) return ElMessage({ type: 'warning', message: '请先输入内容在进行智能纠错！' })\r\n  loading.value = true\r\n  const { data } = await api.typingVerification({\r\n    text: content.value.replace(/&ldquo;/gi, '“').replace(/&rdquo;/gi, '”')\r\n  })\r\n  oldId.value = ''\r\n  arrId.value = []\r\n  checklist.value = data?.checklist?.map((v) => ({ ...v, id: guid() })) || []\r\n  content.value = data?.replace_text || ''\r\n  loading.value = false\r\n}\r\nconst handleDetails = (row) => {\r\n  const iframe = wordRef.value?.getEditor()?.iframeElement\r\n  const body = iframe?.contentWindow?.document?.body\r\n  const elList = body?.childNodes || []\r\n  if (oldId.value) {\r\n    const oldObj = elAttr(elList, oldId.value)\r\n    if (oldObj.elArr.length) {\r\n      for (let index = 0; index < oldObj.elArr.length; index++) {\r\n        const item = oldObj.elArr[index]\r\n        if (!index) iframe.contentWindow.scrollTo(0, item.offsetTop - 100)\r\n        item.style.color = ''\r\n        item.style.backgroundColor = ''\r\n      }\r\n    }\r\n  }\r\n  oldId.value = row.position + ''\r\n  const obj = elAttr(elList, row.position + '')\r\n  if (obj.elArr.length) {\r\n    for (let index = 0; index < obj.elArr.length; index++) {\r\n      const item = obj.elArr[index]\r\n      if (!index) iframe.contentWindow.scrollTo(0, item.offsetTop - 100)\r\n      item.style.color = '#fff'\r\n      item.style.backgroundColor = 'red'\r\n    }\r\n  }\r\n}\r\nconst handleReplace = (row) => {\r\n  const iframe = wordRef.value?.getEditor()?.iframeElement\r\n  const body = iframe?.contentWindow?.document?.body\r\n  const elList = body?.childNodes || []\r\n  const obj = elAttr(elList, row.position + '')\r\n  if (obj.elArr.length > 1) {\r\n    let styleStr = ''\r\n    for (let key in obj.styleObj) {\r\n      styleStr += `${key}:${obj.styleObj[key]};`\r\n    }\r\n    for (let index = 0; index < obj.elArr.length; index++) {\r\n      const item = obj.elArr[index]\r\n      const elParent = item\r\n      if (!index) {\r\n        elParent.insertAdjacentHTML('beforebegin', `<span style=\"${styleStr}\">${row.suggest[0]}</span>`)\r\n      }\r\n      elParent.parentNode.removeChild(elParent)\r\n    }\r\n  } else {\r\n    obj.elArr[0].insertAdjacentHTML('beforebegin', row.suggest[0])\r\n    obj.elArr[0].parentNode.removeChild(obj.elArr[0])\r\n  }\r\n  arrId.value.push(row.id)\r\n}\r\nconst handleAllReplace = () => {\r\n  for (let index = 0; index < checklist.value.length; index++) {\r\n    const item = checklist.value[index]\r\n    if (!arrId.value.includes(item.id) && item?.suggest?.length) {\r\n      handleReplace(item)\r\n    }\r\n  }\r\n}\r\nconst handleExportWord = () => {\r\n  store.commit('setExportWordHtmlObj', {\r\n    code: 'exportWord',\r\n    name: '智能纠错 --- 文档导出',\r\n    key: 'content',\r\n    data: { content: content.value }\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.IntelligentErrorCorrection {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .IntelligentErrorCorrectionHead {\r\n    width: 100%;\r\n    padding: var(--zy-distance-two) 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .IntelligentErrorCorrectionButton {\r\n      width: 796px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      & + .IntelligentErrorCorrectionButton {\r\n        width: calc(100% - 840px);\r\n      }\r\n      .IntelligentErrorCorrectionButtonItem {\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n  .IntelligentErrorCorrectionBody {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-two) * 2)));\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: var(--zy-distance-two);\r\n    .IntelligentErrorCorrectionBodyLeft {\r\n      width: 820px;\r\n      height: 100%;\r\n      .TinyMceEditor {\r\n        height: 100%;\r\n        .tox-tinymce {\r\n          border: none;\r\n        }\r\n        .tox-editor-header {\r\n          width: 796px;\r\n          border: 1px solid #ccc;\r\n          border-bottom: none;\r\n          margin: auto;\r\n          margin-right: 30px;\r\n        }\r\n      }\r\n    }\r\n    .IntelligentErrorCorrectionBodyRight {\r\n      width: calc(100% - 840px);\r\n      height: 100%;\r\n      .globalTable {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;+CAmEA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,QAAQ,KAAK;AACzB,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,OAAO,EAAEC,aAAa,EAAEC,IAAI,EAAEC,YAAY,QAAQ,8BAA8B;AACzF,SAASC,MAAM,QAAQ,iCAAiC;AACxD,SAASC,SAAS,QAAQ,cAAc;AATxC,IAAAC,WAAA,GAAe;EAAEpC,IAAI,EAAE;AAA6B,CAAC;;;;;IAWrD,IAAMqC,KAAK,GAAGR,QAAQ,CAAC,CAAC;IAExB,IAAMS,GAAG,GACP,sKAAsK;IAExK,IAAMC,OAAO,GAAGX,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMY,WAAW,GAAGZ,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMa,OAAO,GAAGb,GAAG,CAAC,CAAC;IACrB,IAAMc,KAAK,GAAGd,GAAG,CAAC,EAAE,CAAC;IACrB,IAAMe,KAAK,GAAGf,GAAG,CAAC,EAAE,CAAC;IACrB,IAAMgB,OAAO,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMiB,SAAS,GAAGjB,GAAG,CAAC,EAAE,CAAC;IAEzB,IAAMkB,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBb,YAAY,CAAC,CAAC,CAAChE,IAAI,CAAC,UAAC8E,IAAI,EAAK;QAC5B,IAAMC,QAAQ,GAAGD,IAAI,CAAC/C,IAAI,CAACiD,SAAS,CAACF,IAAI,CAAC/C,IAAI,CAACkD,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpE,IAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACJ,QAAQ,CAAC;QACxD,IAAI,CAACG,MAAM,EAAE,OAAOhB,SAAS,CAAC;UAAEzF,IAAI,EAAE,SAAS;UAAE2G,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAAM,CAAC,CAAC;QACxGf,OAAO,CAAChH,KAAK,GAAG,IAAI;QACpBgI,cAAc,CAACR,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMQ,cAAc;MAAA,IAAAC,KAAA,GAAAlC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwD,QAAOV,IAAI;QAAA,IAAAW,KAAA,EAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAA/I,mBAAA,GAAAuB,IAAA,UAAAyH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAApD,IAAA,GAAAoD,QAAA,CAAA/E,IAAA;YAAA;cAAA+E,QAAA,CAAApD,IAAA;cAExBgD,KAAK,GAAG,IAAIK,QAAQ,CAAC,CAAC;cAC5BL,KAAK,CAACM,MAAM,CAAC,MAAM,EAAEjB,IAAI,CAAC;cAAAe,QAAA,CAAA/E,IAAA;cAAA,OACH4C,GAAG,CAACsC,aAAa,CAACP,KAAK,CAAC;YAAA;cAAAC,qBAAA,GAAAG,QAAA,CAAAtF,IAAA;cAAvCoF,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZhB,OAAO,CAACrH,KAAK,GAAGqI,IAAI,CACjBM,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAC9BA,OAAO,CAAC,+CAA+C,EAAE,EAAE,CAAC,CAC5DA,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAC9BA,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;cAChC3B,OAAO,CAAChH,KAAK,GAAG,KAAK;cAAAuI,QAAA,CAAA/E,IAAA;cAAA;YAAA;cAAA+E,QAAA,CAAApD,IAAA;cAAAoD,QAAA,CAAAK,EAAA,GAAAL,QAAA;cAErBvB,OAAO,CAAChH,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAuI,QAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA4C,OAAA;MAAA,CAExB;MAAA,gBAdKF,cAAcA,CAAAa,EAAA;QAAA,OAAAZ,KAAA,CAAAhC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAcnB;IACD,IAAM8C,eAAe;MAAA,IAAAC,KAAA,GAAAhD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsE,SAAA;QAAA,IAAAC,eAAA;QAAA,IAAAC,qBAAA,EAAAb,IAAA;QAAA,OAAA/I,mBAAA,GAAAuB,IAAA,UAAAsI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjE,IAAA,GAAAiE,SAAA,CAAA5F,IAAA;YAAA;cAAA,IACjB6D,OAAO,CAACrH,KAAK;gBAAAoJ,SAAA,CAAA5F,IAAA;gBAAA;cAAA;cAAA,OAAA4F,SAAA,CAAAhG,MAAA,WAASwD,SAAS,CAAC;gBAAEzF,IAAI,EAAE,SAAS;gBAAE2G,OAAO,EAAE;cAAiB,CAAC,CAAC;YAAA;cACpFd,OAAO,CAAChH,KAAK,GAAG,IAAI;cAAAoJ,SAAA,CAAA5F,IAAA;cAAA,OACG4C,GAAG,CAACiD,kBAAkB,CAAC;gBAC5CC,IAAI,EAAEjC,OAAO,CAACrH,KAAK,CAAC2I,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,GAAG;cACxE,CAAC,CAAC;YAAA;cAAAO,qBAAA,GAAAE,SAAA,CAAAnG,IAAA;cAFMoF,IAAI,GAAAa,qBAAA,CAAJb,IAAI;cAGZlB,KAAK,CAACnH,KAAK,GAAG,EAAE;cAChBoH,KAAK,CAACpH,KAAK,GAAG,EAAE;cAChBsH,SAAS,CAACtH,KAAK,GAAG,CAAAqI,IAAI,aAAJA,IAAI,gBAAAY,eAAA,GAAJZ,IAAI,CAAEf,SAAS,cAAA2B,eAAA,uBAAfA,eAAA,CAAiBM,GAAG,CAAC,UAACvH,CAAC;gBAAA,OAAAwH,aAAA,CAAAA,aAAA,KAAWxH,CAAC;kBAAEyH,EAAE,EAAEhD,IAAI,CAAC;gBAAC;cAAA,CAAG,CAAC,KAAI,EAAE;cAC3EY,OAAO,CAACrH,KAAK,GAAG,CAAAqI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,YAAY,KAAI,EAAE;cACxC1C,OAAO,CAAChH,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAoJ,SAAA,CAAA9D,IAAA;UAAA;QAAA,GAAA0D,QAAA;MAAA,CACtB;MAAA,gBAXKF,eAAeA,CAAA;QAAA,OAAAC,KAAA,CAAA9C,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWpB;IACD,IAAM2D,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAK;MAAA,IAAAC,cAAA,EAAAC,qBAAA;MAC7B,IAAMC,MAAM,IAAAF,cAAA,GAAG3C,OAAO,CAAClH,KAAK,cAAA6J,cAAA,gBAAAA,cAAA,GAAbA,cAAA,CAAeG,SAAS,CAAC,CAAC,cAAAH,cAAA,uBAA1BA,cAAA,CAA4BI,aAAa;MACxD,IAAMC,IAAI,GAAGH,MAAM,aAANA,MAAM,gBAAAD,qBAAA,GAANC,MAAM,CAAEI,aAAa,cAAAL,qBAAA,gBAAAA,qBAAA,GAArBA,qBAAA,CAAuBM,QAAQ,cAAAN,qBAAA,uBAA/BA,qBAAA,CAAiCI,IAAI;MAClD,IAAMG,MAAM,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,UAAU,KAAI,EAAE;MACrC,IAAInD,KAAK,CAACnH,KAAK,EAAE;QACf,IAAMuK,MAAM,GAAG5D,MAAM,CAAC0D,MAAM,EAAElD,KAAK,CAACnH,KAAK,CAAC;QAC1C,IAAIuK,MAAM,CAACC,KAAK,CAACnG,MAAM,EAAE;UACvB,KAAK,IAAIoG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,MAAM,CAACC,KAAK,CAACnG,MAAM,EAAEoG,KAAK,EAAE,EAAE;YACxD,IAAMC,IAAI,GAAGH,MAAM,CAACC,KAAK,CAACC,KAAK,CAAC;YAChC,IAAI,CAACA,KAAK,EAAEV,MAAM,CAACI,aAAa,CAACQ,QAAQ,CAAC,CAAC,EAAED,IAAI,CAACE,SAAS,GAAG,GAAG,CAAC;YAClEF,IAAI,CAACG,KAAK,CAACC,KAAK,GAAG,EAAE;YACrBJ,IAAI,CAACG,KAAK,CAACE,eAAe,GAAG,EAAE;UACjC;QACF;MACF;MACA5D,KAAK,CAACnH,KAAK,GAAG4J,GAAG,CAACoB,QAAQ,GAAG,EAAE;MAC/B,IAAMC,GAAG,GAAGtE,MAAM,CAAC0D,MAAM,EAAET,GAAG,CAACoB,QAAQ,GAAG,EAAE,CAAC;MAC7C,IAAIC,GAAG,CAACT,KAAK,CAACnG,MAAM,EAAE;QACpB,KAAK,IAAIoG,MAAK,GAAG,CAAC,EAAEA,MAAK,GAAGQ,GAAG,CAACT,KAAK,CAACnG,MAAM,EAAEoG,MAAK,EAAE,EAAE;UACrD,IAAMC,KAAI,GAAGO,GAAG,CAACT,KAAK,CAACC,MAAK,CAAC;UAC7B,IAAI,CAACA,MAAK,EAAEV,MAAM,CAACI,aAAa,CAACQ,QAAQ,CAAC,CAAC,EAAED,KAAI,CAACE,SAAS,GAAG,GAAG,CAAC;UAClEF,KAAI,CAACG,KAAK,CAACC,KAAK,GAAG,MAAM;UACzBJ,KAAI,CAACG,KAAK,CAACE,eAAe,GAAG,KAAK;QACpC;MACF;IACF,CAAC;IACD,IAAMG,aAAa,GAAG,SAAhBA,aAAaA,CAAItB,GAAG,EAAK;MAAA,IAAAuB,eAAA,EAAAC,sBAAA;MAC7B,IAAMrB,MAAM,IAAAoB,eAAA,GAAGjE,OAAO,CAAClH,KAAK,cAAAmL,eAAA,gBAAAA,eAAA,GAAbA,eAAA,CAAenB,SAAS,CAAC,CAAC,cAAAmB,eAAA,uBAA1BA,eAAA,CAA4BlB,aAAa;MACxD,IAAMC,IAAI,GAAGH,MAAM,aAANA,MAAM,gBAAAqB,sBAAA,GAANrB,MAAM,CAAEI,aAAa,cAAAiB,sBAAA,gBAAAA,sBAAA,GAArBA,sBAAA,CAAuBhB,QAAQ,cAAAgB,sBAAA,uBAA/BA,sBAAA,CAAiClB,IAAI;MAClD,IAAMG,MAAM,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,UAAU,KAAI,EAAE;MACrC,IAAMW,GAAG,GAAGtE,MAAM,CAAC0D,MAAM,EAAET,GAAG,CAACoB,QAAQ,GAAG,EAAE,CAAC;MAC7C,IAAIC,GAAG,CAACT,KAAK,CAACnG,MAAM,GAAG,CAAC,EAAE;QACxB,IAAIgH,QAAQ,GAAG,EAAE;QACjB,KAAK,IAAIC,GAAG,IAAIL,GAAG,CAACM,QAAQ,EAAE;UAC5BF,QAAQ,IAAI,GAAGC,GAAG,IAAIL,GAAG,CAACM,QAAQ,CAACD,GAAG,CAAC,GAAG;QAC5C;QACA,KAAK,IAAIb,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGQ,GAAG,CAACT,KAAK,CAACnG,MAAM,EAAEoG,KAAK,EAAE,EAAE;UACrD,IAAMC,IAAI,GAAGO,GAAG,CAACT,KAAK,CAACC,KAAK,CAAC;UAC7B,IAAMe,QAAQ,GAAGd,IAAI;UACrB,IAAI,CAACD,KAAK,EAAE;YACVe,QAAQ,CAACC,kBAAkB,CAAC,aAAa,EAAE,gBAAgBJ,QAAQ,KAAKzB,GAAG,CAAC8B,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;UAClG;UACAF,QAAQ,CAACG,UAAU,CAACC,WAAW,CAACJ,QAAQ,CAAC;QAC3C;MACF,CAAC,MAAM;QACLP,GAAG,CAACT,KAAK,CAAC,CAAC,CAAC,CAACiB,kBAAkB,CAAC,aAAa,EAAE7B,GAAG,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9DT,GAAG,CAACT,KAAK,CAAC,CAAC,CAAC,CAACmB,UAAU,CAACC,WAAW,CAACX,GAAG,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC;MACnD;MACApD,KAAK,CAACpH,KAAK,CAACgE,IAAI,CAAC4F,GAAG,CAACH,EAAE,CAAC;IAC1B,CAAC;IACD,IAAMoC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B,KAAK,IAAIpB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGnD,SAAS,CAACtH,KAAK,CAACqE,MAAM,EAAEoG,KAAK,EAAE,EAAE;QAAA,IAAAqB,aAAA;QAC3D,IAAMpB,IAAI,GAAGpD,SAAS,CAACtH,KAAK,CAACyK,KAAK,CAAC;QACnC,IAAI,CAACrD,KAAK,CAACpH,KAAK,CAAC6H,QAAQ,CAAC6C,IAAI,CAACjB,EAAE,CAAC,IAAIiB,IAAI,aAAJA,IAAI,gBAAAoB,aAAA,GAAJpB,IAAI,CAAEgB,OAAO,cAAAI,aAAA,eAAbA,aAAA,CAAezH,MAAM,EAAE;UAC3D6G,aAAa,CAACR,IAAI,CAAC;QACrB;MACF;IACF,CAAC;IACD,IAAMqB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7BjF,KAAK,CAACkF,MAAM,CAAC,sBAAsB,EAAE;QACnCC,IAAI,EAAE,YAAY;QAClBxH,IAAI,EAAE,eAAe;QACrB6G,GAAG,EAAE,SAAS;QACdjD,IAAI,EAAE;UAAEhB,OAAO,EAAEA,OAAO,CAACrH;QAAM;MACjC,CAAC,CAAC;IACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}