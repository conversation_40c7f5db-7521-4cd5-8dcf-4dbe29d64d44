{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ApplyForAdjustResult\"\n};\nvar _hoisted_2 = {\n  key: 0\n};\nvar _hoisted_3 = {\n  key: 1\n};\nvar _hoisted_4 = {\n  key: 2\n};\nvar _hoisted_5 = {\n  key: 0\n};\nvar _hoisted_6 = {\n  key: 1\n};\nvar _hoisted_7 = {\n  key: 2\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  var _component_global_info_item = _resolveComponent(\"global-info-item\");\n  var _component_global_info_line = _resolveComponent(\"global-info-line\");\n  var _component_global_info = _resolveComponent(\"global-info\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"ApplyForAdjustResultName\"\n  }, \"提案单位申请调整结果\", -1 /* HOISTED */)), !$setup.dataList.length ? (_openBlock(), _createBlock(_component_el_empty, {\n    key: 0,\n    description: \"暂无记录\"\n  })) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dataList, function (item) {\n    return _openBlock(), _createBlock(_component_global_info, {\n      key: item.id\n    }, {\n      default: _withCtx(function () {\n        return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.applyOfficeInfo, function (row) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: row.applyAdjustId\n          }, [_createVNode(_component_global_info_line, null, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_global_info_item, {\n                label: \"申请单位\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(row.officeName), 1 /* TEXT */)];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n                label: \"申请时间\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.format(row.applyTime)), 1 /* TEXT */)];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n            label: \"申请调整理由\"\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"pre\", null, _toDisplayString(row.applyReason), 1 /* TEXT */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1024 /* DYNAMIC_SLOTS */)]);\n        }), 128 /* KEYED_FRAGMENT */)), _createVNode(_component_global_info_item, {\n          label: \"调整时间\"\n        }, {\n          default: _withCtx(function () {\n            return [_createTextVNode(_toDisplayString($setup.format(item.adjustTime)), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n          label: \"调整前\"\n        }, {\n          default: _withCtx(function () {\n            return [item.oldMain ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, \"主办：\" + _toDisplayString(item.oldMain), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.oldAssist ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, \"协办：\" + _toDisplayString(item.oldAssist), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.oldPublish ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \"分办：\" + _toDisplayString(item.oldPublish), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n          label: \"调整后\"\n        }, {\n          default: _withCtx(function () {\n            return [item.newMain ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, \"主办：\" + _toDisplayString(item.newMain), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.newAssist ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, \"协办：\" + _toDisplayString(item.newAssist), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.newPublish ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, \"分办：\" + _toDisplayString(item.newPublish), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */);\n  }), 128 /* KEYED_FRAGMENT */))]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "$setup", "dataList", "length", "_createBlock", "_component_el_empty", "description", "_createCommentVNode", "_Fragment", "_renderList", "item", "_component_global_info", "id", "default", "_withCtx", "applyOfficeInfo", "row", "applyAdjustId", "_createVNode", "_component_global_info_line", "_component_global_info_item", "label", "_createTextVNode", "_toDisplayString", "officeName", "_", "format", "applyTime", "applyReason", "adjustTime", "<PERSON><PERSON><PERSON>", "_hoisted_2", "oldAssist", "_hoisted_3", "oldPublish", "_hoisted_4", "new<PERSON>ain", "_hoisted_5", "newAssist", "_hoisted_6", "newPublish", "_hoisted_7"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\ApplyForAdjustResult\\ApplyForAdjustResult.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ApplyForAdjustResult\">\r\n    <div class=\"ApplyForAdjustResultName\">提案单位申请调整结果</div>\r\n    <el-empty v-if=\"!dataList.length\"\r\n              description=\"暂无记录\" />\r\n    <global-info v-for=\"item in dataList\"\r\n                 :key=\"item.id\">\r\n      <div v-for=\"row in item.applyOfficeInfo\"\r\n           :key=\"row.applyAdjustId\">\r\n        <global-info-line>\r\n          <global-info-item label=\"申请单位\">{{ row.officeName }}</global-info-item>\r\n          <global-info-item label=\"申请时间\">{{ format(row.applyTime) }}</global-info-item>\r\n        </global-info-line>\r\n        <global-info-item label=\"申请调整理由\">\r\n          <pre>{{ row.applyReason }}</pre>\r\n        </global-info-item>\r\n      </div>\r\n      <global-info-item label=\"调整时间\">{{ format(item.adjustTime) }}</global-info-item>\r\n      <global-info-item label=\"调整前\">\r\n        <div v-if=\"item.oldMain\">主办：{{ item.oldMain }}</div>\r\n        <div v-if=\"item.oldAssist\">协办：{{ item.oldAssist }}</div>\r\n        <div v-if=\"item.oldPublish\">分办：{{ item.oldPublish }}</div>\r\n      </global-info-item>\r\n      <global-info-item label=\"调整后\">\r\n        <div v-if=\"item.newMain\">主办：{{ item.newMain }}</div>\r\n        <div v-if=\"item.newAssist\">协办：{{ item.newAssist }}</div>\r\n        <div v-if=\"item.newPublish\">分办：{{ item.newPublish }}</div>\r\n      </global-info-item>\r\n    </global-info>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ApplyForAdjustResult' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst dataList = ref([])\r\nonMounted(() => { handingPortionAdjustRecord() })\r\n\r\nconst handingPortionAdjustRecord = async () => {\r\n  const { data } = await api.handingPortionAdjustRecord({ suggestionId: props.id })\r\n  dataList.value = data.map(v => ({\r\n    id: v.recordId,\r\n    adjustTime: v.adjustTime,\r\n    applyOfficeInfo: v.applyOfficeInfo,\r\n    oldMain: v.beforeOffices.filter(v => v.type === 'main').map(v => v.officeName).join('、'),\r\n    oldAssist: v.beforeOffices.filter(v => v.type === 'assist').map(v => v.officeName).join('、'),\r\n    oldPublish: v.beforeOffices.filter(v => v.type === 'publish').map(v => v.officeName).join('、'),\r\n    newMain: v.afterOffices.filter(v => v.type === 'main').map(v => v.officeName).join('、'),\r\n    newAssist: v.afterOffices.filter(v => v.type === 'assist').map(v => v.officeName).join('、'),\r\n    newPublish: v.afterOffices.filter(v => v.type === 'publish').map(v => v.officeName).join('、')\r\n  }))\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ApplyForAdjustResult {\r\n  width: 990px;\r\n  padding: 0 var(--zy-distance-one);\r\n  padding-top: var(--zy-distance-one);\r\n\r\n  .ApplyForAdjustResultName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    color: var(--zy-el-color-primary);\r\n    border-bottom: 1px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .global-info {\r\n    padding-bottom: 12px;\r\n\r\n    .global-info-item {\r\n      .global-info-label {\r\n        width: 160px;\r\n      }\r\n\r\n      .global-info-content {\r\n        width: calc(100% - 160px);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EADnCC,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;;;;;uBACEC,mBAAA,CA4BM,OA5BNC,UA4BM,G,0BA3BJC,mBAAA,CAAsD;IAAjDJ,KAAK,EAAC;EAA0B,GAAC,YAAU,sB,CAC/BK,MAAA,CAAAC,QAAQ,CAACC,MAAM,I,cAAhCC,YAAA,CAC+BC,mBAAA;IAJnCR,GAAA;IAIcS,WAAW,EAAC;QAJ1BC,mBAAA,iB,kBAKIT,mBAAA,CAuBcU,SAAA,QA5BlBC,WAAA,CAKgCR,MAAA,CAAAC,QAAQ,EALxC,UAKwBQ,IAAI;yBAAxBN,YAAA,CAuBcO,sBAAA;MAtBAd,GAAG,EAAEa,IAAI,CAACE;;MAN5BC,OAAA,EAAAC,QAAA,CAOW;QAAA,OAAmC,E,kBAAxChB,mBAAA,CASMU,SAAA,QAhBZC,WAAA,CAOyBC,IAAI,CAACK,eAAe,EAP7C,UAOkBC,GAAG;+BAAflB,mBAAA,CASM;YARAD,GAAG,EAAEmB,GAAG,CAACC;cACbC,YAAA,CAGmBC,2BAAA;YAZ3BN,OAAA,EAAAC,QAAA,CAUU;cAAA,OAAsE,CAAtEI,YAAA,CAAsEE,2BAAA;gBAApDC,KAAK,EAAC;cAAM;gBAVxCR,OAAA,EAAAC,QAAA,CAUyC;kBAAA,OAAoB,CAV7DQ,gBAAA,CAAAC,gBAAA,CAU4CP,GAAG,CAACQ,UAAU,iB;;gBAV1DC,CAAA;4CAWUP,YAAA,CAA6EE,2BAAA;gBAA3DC,KAAK,EAAC;cAAM;gBAXxCR,OAAA,EAAAC,QAAA,CAWyC;kBAAA,OAA2B,CAXpEQ,gBAAA,CAAAC,gBAAA,CAW4CtB,MAAA,CAAAyB,MAAM,CAACV,GAAG,CAACW,SAAS,kB;;gBAXhEF,CAAA;;;YAAAA,CAAA;wCAaQP,YAAA,CAEmBE,2BAAA;YAFDC,KAAK,EAAC;UAAQ;YAbxCR,OAAA,EAAAC,QAAA,CAcU;cAAA,OAAgC,CAAhCd,mBAAA,CAAgC,aAAAuB,gBAAA,CAAxBP,GAAG,CAACY,WAAW,iB;;YAdjCH,CAAA;;wCAiBMP,YAAA,CAA+EE,2BAAA;UAA7DC,KAAK,EAAC;QAAM;UAjBpCR,OAAA,EAAAC,QAAA,CAiBqC;YAAA,OAA6B,CAjBlEQ,gBAAA,CAAAC,gBAAA,CAiBwCtB,MAAA,CAAAyB,MAAM,CAAChB,IAAI,CAACmB,UAAU,kB;;UAjB9DJ,CAAA;sCAkBMP,YAAA,CAImBE,2BAAA;UAJDC,KAAK,EAAC;QAAK;UAlBnCR,OAAA,EAAAC,QAAA,CAgB2B;YAAA,OAAoD,CAG5DJ,IAAI,CAACoB,OAAO,I,cAAvBhC,mBAAA,CAAoD,OAnB5DiC,UAAA,EAmBiC,KAAG,GAAAR,gBAAA,CAAGb,IAAI,CAACoB,OAAO,oBAnBnDvB,mBAAA,gBAoBmBG,IAAI,CAACsB,SAAS,I,cAAzBlC,mBAAA,CAAwD,OApBhEmC,UAAA,EAoBmC,KAAG,GAAAV,gBAAA,CAAGb,IAAI,CAACsB,SAAS,oBApBvDzB,mBAAA,gBAqBmBG,IAAI,CAACwB,UAAU,I,cAA1BpC,mBAAA,CAA0D,OArBlEqC,UAAA,EAqBoC,KAAG,GAAAZ,gBAAA,CAAGb,IAAI,CAACwB,UAAU,oBArBzD3B,mBAAA,e;;UAAAkB,CAAA;sCAuBMP,YAAA,CAImBE,2BAAA;UAJDC,KAAK,EAAC;QAAK;UAvBnCR,OAAA,EAAAC,QAAA,CAqBiD;YAAA,OAAoD,CAGlFJ,IAAI,CAAC0B,OAAO,I,cAAvBtC,mBAAA,CAAoD,OAxB5DuC,UAAA,EAwBiC,KAAG,GAAAd,gBAAA,CAAGb,IAAI,CAAC0B,OAAO,oBAxBnD7B,mBAAA,gBAyBmBG,IAAI,CAAC4B,SAAS,I,cAAzBxC,mBAAA,CAAwD,OAzBhEyC,UAAA,EAyBmC,KAAG,GAAAhB,gBAAA,CAAGb,IAAI,CAAC4B,SAAS,oBAzBvD/B,mBAAA,gBA0BmBG,IAAI,CAAC8B,UAAU,I,cAA1B1C,mBAAA,CAA0D,OA1BlE2C,UAAA,EA0BoC,KAAG,GAAAlB,gBAAA,CAAGb,IAAI,CAAC8B,UAAU,oBA1BzDjC,mBAAA,e;;UAAAkB,CAAA;;;MAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}