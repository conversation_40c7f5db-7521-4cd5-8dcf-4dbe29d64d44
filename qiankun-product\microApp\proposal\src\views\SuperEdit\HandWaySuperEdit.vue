<template>
  <el-scrollbar always class="HandWaySuperEdit">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">

      <el-form-item label="办理方式" prop="transactType" class="globalFormTitle">
        <el-select v-model="form.transactType" placeholder="请选择办理方式" @change="transactTypeChange" :clearable="false">
          <el-option label="主办/协办" value="main_assist" />
          <el-option label="分办" value="publish" />
        </el-select>
      </el-form-item>

      <template v-if="form.transactType === 'main_assist'">
        <el-form-item label="主办单位" prop="mainHandleOfficeId" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.mainHandleOfficeId" :filterId="form.handleOfficeIds"
            :max="1"></suggest-simple-select-unit>
        </el-form-item>
      </template>
      <template v-if="form.transactType === 'main_assist'">
        <el-form-item label="协办单位" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.handleOfficeIds"
            :filterId="form.mainHandleOfficeId"></suggest-simple-select-unit>
        </el-form-item>
      </template>
      <template v-if="form.transactType === 'publish'">
        <el-form-item label="分办单位" prop="handleOfficeIds" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.handleOfficeIds"></suggest-simple-select-unit>
        </el-form-item>
      </template>

      <el-form-item label="答复截止时间">
        <xyl-date-picker v-model="form.answerStopDate" type="datetime" value-format="x" placeholder="选择答复截止时间" />
      </el-form-item>
      <el-form-item label="调整截止时间">
        <xyl-date-picker v-model="form.adjustStopDate" type="datetime" value-format="x" placeholder="选择调整截止时间" />
      </el-form-item>

      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef, 0)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </el-scrollbar>
</template>
<script>
export default { name: 'HandWaySuperEdit' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['callback'])
const props = defineProps({ suggestionId: { type: String, default: '' } })
const formRef = ref()
const form = reactive({
  verifyStatus: 1,
  transactType: '', // 请选择办理方式
  mainHandleOfficeId: [],
  handleOfficeIds: [],
  answerStopDate: '',
  adjustStopDate: '',
})
const rules = reactive({
  verifyStatus: [{ required: true, message: '请选择是否同意调整申请', trigger: ['blur', 'change'] }],
  transactType: [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }],
  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],
  handleOfficeIds: [{ type: 'array', required: false, message: '请选择协办单位', trigger: ['blur', 'change'] }],
})

onMounted(() => {
  if (props.suggestionId) {
    handingPortionAdjustOfficeInfo()
  }
})

const transactTypeChange = () => {
  if (form.transactType === 'main_assist') {
    rules.mainHandleOfficeId = [{ type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else if (form.transactType === 'publish') {
    form.mainHandleOfficeId = []
    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else {
    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  }
}

const handingPortionAdjustOfficeInfo = async () => {
  try {
    const { data } = await api.handingPortionAdjustOfficeInfo({ suggestionId: props.suggestionId })
    form.transactType = data.handleOfficeType
    form.answerStopDate = data.answerStopDate
    form.adjustStopDate = data.adjustStopDate
    form.mainHandleOfficeId = data.mainHandleOffice?.handleOfficeId ? [data.mainHandleOffice?.handleOfficeId] : []
    form.handleOfficeIds = data?.handleOffices?.map(v => v.handleOfficeId) || []
  } catch (err) {
    if (err.code === 500) {
      setTimeout(() => {
        emit('callback')
      }, 1000);
    }
  }
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const globalJson = async () => {
  try {
    const { code } = await api.globalJson('/cppcc/handingPortionAdjust/verify', {
      isDirectAdjust: 1,
      suggestionId: props.suggestionId,
      verifyStatus: form.verifyStatus,
      answerStopDate: form.answerStopDate,
      adjustStopDate: form.adjustStopDate,
      handleOfficeType: form.verifyStatus === 1 ? form.transactType : null, // 办理方式
      mainHandleOfficeId: form.verifyStatus === 1 ? form.mainHandleOfficeId.join('') : null, // 主办单位
      handleOfficeIds: form.verifyStatus === 1 ? form.handleOfficeIds : null, // 协办或分办单位
    })
    if (code === 200) {
      ElMessage({ type: 'success', message: '操作成功' })
      emit('callback')
    }

  } catch (err) {
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.HandWaySuperEdit {
  width: 680px;
  height: 100%;

  .suggest-simple-select-unit {
    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;
    border-radius: var(--zy-el-input-border-radius,
        var(--zy-el-border-radius-base));
  }
}
</style>
