{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_suggest_simple_select_unit = _resolveComponent(\"suggest-simple-select-unit\");\n  var _component_xyl_date_picker = _resolveComponent(\"xyl-date-picker\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    always: \"\",\n    class: \"HandWaySuperEdit\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form, {\n        ref: \"formRef\",\n        model: $setup.form,\n        rules: $setup.rules,\n        inline: \"\",\n        \"label-position\": \"top\",\n        class: \"globalForm\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_form_item, {\n            label: \"办理方式\",\n            prop: \"transactType\",\n            class: \"globalFormTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_select, {\n                modelValue: $setup.form.transactType,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n                  return $setup.form.transactType = $event;\n                }),\n                placeholder: \"请选择办理方式\",\n                onChange: $setup.transactTypeChange,\n                clearable: false\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_el_option, {\n                    label: \"主办/协办\",\n                    value: \"main_assist\"\n                  }), _createVNode(_component_el_option, {\n                    label: \"分办\",\n                    value: \"publish\"\n                  })];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), $setup.form.transactType === 'main_assist' ? (_openBlock(), _createBlock(_component_el_form_item, {\n            key: 0,\n            label: \"主办单位\",\n            prop: \"mainHandleOfficeId\",\n            class: \"globalFormTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_suggest_simple_select_unit, {\n                modelValue: $setup.form.mainHandleOfficeId,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n                  return $setup.form.mainHandleOfficeId = $event;\n                }),\n                filterId: $setup.form.handleOfficeIds,\n                max: 1\n              }, null, 8 /* PROPS */, [\"modelValue\", \"filterId\"])];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), $setup.form.transactType === 'main_assist' ? (_openBlock(), _createBlock(_component_el_form_item, {\n            key: 1,\n            label: \"协办单位\",\n            class: \"globalFormTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_suggest_simple_select_unit, {\n                modelValue: $setup.form.handleOfficeIds,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n                  return $setup.form.handleOfficeIds = $event;\n                }),\n                filterId: $setup.form.mainHandleOfficeId\n              }, null, 8 /* PROPS */, [\"modelValue\", \"filterId\"])];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), $setup.form.transactType === 'publish' ? (_openBlock(), _createBlock(_component_el_form_item, {\n            key: 2,\n            label: \"分办单位\",\n            prop: \"handleOfficeIds\",\n            class: \"globalFormTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_suggest_simple_select_unit, {\n                modelValue: $setup.form.handleOfficeIds,\n                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n                  return $setup.form.handleOfficeIds = $event;\n                })\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n            label: \"答复截止时间\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_xyl_date_picker, {\n                modelValue: $setup.form.answerStopDate,\n                \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n                  return $setup.form.answerStopDate = $event;\n                }),\n                type: \"datetime\",\n                \"value-format\": \"x\",\n                placeholder: \"选择答复截止时间\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"调整截止时间\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_xyl_date_picker, {\n                modelValue: $setup.form.adjustStopDate,\n                \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n                  return $setup.form.adjustStopDate = $event;\n                }),\n                type: \"datetime\",\n                \"value-format\": \"x\",\n                placeholder: \"选择调整截止时间\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: _cache[6] || (_cache[6] = function ($event) {\n              return $setup.submitForm($setup.formRef, 0);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[7] || (_cache[7] = [_createTextVNode(\"提交\")]);\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_button, {\n            onClick: $setup.resetForm\n          }, {\n            default: _withCtx(function () {\n              return _cache[8] || (_cache[8] = [_createTextVNode(\"取消\")]);\n            }),\n            _: 1 /* STABLE */\n          })])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\"])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_scrollbar", "always", "default", "_withCtx", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "_component_el_form_item", "label", "prop", "_component_el_select", "modelValue", "transactType", "_cache", "$event", "placeholder", "onChange", "transactTypeChange", "clearable", "_component_el_option", "value", "_", "key", "_component_suggest_simple_select_unit", "mainHandleOfficeId", "filterId", "handleOfficeIds", "max", "_createCommentVNode", "_component_xyl_date_picker", "answerStopDate", "type", "adjustStopDate", "_createElementVNode", "_hoisted_1", "_component_el_button", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandWaySuperEdit.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"HandWaySuperEdit\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n\r\n      <el-form-item label=\"办理方式\" prop=\"transactType\" class=\"globalFormTitle\">\r\n        <el-select v-model=\"form.transactType\" placeholder=\"请选择办理方式\" @change=\"transactTypeChange\" :clearable=\"false\">\r\n          <el-option label=\"主办/协办\" value=\"main_assist\" />\r\n          <el-option label=\"分办\" value=\"publish\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <template v-if=\"form.transactType === 'main_assist'\">\r\n        <el-form-item label=\"主办单位\" prop=\"mainHandleOfficeId\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.mainHandleOfficeId\" :filterId=\"form.handleOfficeIds\"\r\n            :max=\"1\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n      </template>\r\n      <template v-if=\"form.transactType === 'main_assist'\">\r\n        <el-form-item label=\"协办单位\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"\r\n            :filterId=\"form.mainHandleOfficeId\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n      </template>\r\n      <template v-if=\"form.transactType === 'publish'\">\r\n        <el-form-item label=\"分办单位\" prop=\"handleOfficeIds\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n      </template>\r\n\r\n      <el-form-item label=\"答复截止时间\">\r\n        <xyl-date-picker v-model=\"form.answerStopDate\" type=\"datetime\" value-format=\"x\" placeholder=\"选择答复截止时间\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"调整截止时间\">\r\n        <xyl-date-picker v-model=\"form.adjustStopDate\" type=\"datetime\" value-format=\"x\" placeholder=\"选择调整截止时间\" />\r\n      </el-form-item>\r\n\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef, 0)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'HandWaySuperEdit' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\n\r\nconst emit = defineEmits(['callback'])\r\nconst props = defineProps({ suggestionId: { type: String, default: '' } })\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  verifyStatus: 1,\r\n  transactType: '', // 请选择办理方式\r\n  mainHandleOfficeId: [],\r\n  handleOfficeIds: [],\r\n  answerStopDate: '',\r\n  adjustStopDate: '',\r\n})\r\nconst rules = reactive({\r\n  verifyStatus: [{ required: true, message: '请选择是否同意调整申请', trigger: ['blur', 'change'] }],\r\n  transactType: [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }],\r\n  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],\r\n  handleOfficeIds: [{ type: 'array', required: false, message: '请选择协办单位', trigger: ['blur', 'change'] }],\r\n})\r\n\r\nonMounted(() => {\r\n  if (props.suggestionId) {\r\n    handingPortionAdjustOfficeInfo()\r\n  }\r\n})\r\n\r\nconst transactTypeChange = () => {\r\n  if (form.transactType === 'main_assist') {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.transactType === 'publish') {\r\n    form.mainHandleOfficeId = []\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\n\r\nconst handingPortionAdjustOfficeInfo = async () => {\r\n  try {\r\n    const { data } = await api.handingPortionAdjustOfficeInfo({ suggestionId: props.suggestionId })\r\n    form.transactType = data.handleOfficeType\r\n    form.answerStopDate = data.answerStopDate\r\n    form.adjustStopDate = data.adjustStopDate\r\n    form.mainHandleOfficeId = data.mainHandleOffice?.handleOfficeId ? [data.mainHandleOffice?.handleOfficeId] : []\r\n    form.handleOfficeIds = data?.handleOffices?.map(v => v.handleOfficeId) || []\r\n  } catch (err) {\r\n    if (err.code === 500) {\r\n      setTimeout(() => {\r\n        emit('callback')\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      globalJson()\r\n    } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  try {\r\n    const { code } = await api.globalJson('/cppcc/handingPortionAdjust/verify', {\r\n      isDirectAdjust: 1,\r\n      suggestionId: props.suggestionId,\r\n      verifyStatus: form.verifyStatus,\r\n      answerStopDate: form.answerStopDate,\r\n      adjustStopDate: form.adjustStopDate,\r\n      handleOfficeType: form.verifyStatus === 1 ? form.transactType : null, // 办理方式\r\n      mainHandleOfficeId: form.verifyStatus === 1 ? form.mainHandleOfficeId.join('') : null, // 主办单位\r\n      handleOfficeIds: form.verifyStatus === 1 ? form.handleOfficeIds : null, // 协办或分办单位\r\n    })\r\n    if (code === 200) {\r\n      ElMessage({ type: 'success', message: '操作成功' })\r\n      emit('callback')\r\n    }\r\n\r\n  } catch (err) {\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.HandWaySuperEdit {\r\n  width: 680px;\r\n  height: 100%;\r\n\r\n  .suggest-simple-select-unit {\r\n    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;\r\n    border-radius: var(--zy-el-input-border-radius,\r\n        var(--zy-el-border-radius-base));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAoCWA,KAAK,EAAC;AAAkB;;;;;;;;;;uBAnCjCC,YAAA,CAwCeC,uBAAA;IAxCDC,MAAM,EAAN,EAAM;IAACH,KAAK,EAAC;;IAD7BI,OAAA,EAAAC,QAAA,CAEI;MAAA,OAsCU,CAtCVC,YAAA,CAsCUC,kBAAA;QAtCDC,GAAG,EAAC,SAAS;QAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;QAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;QAAEC,MAAM,EAAN,EAAM;QAAC,gBAAc,EAAC,KAAK;QAACb,KAAK,EAAC;;QAF1FI,OAAA,EAAAC,QAAA,CAIM;UAAA,OAKe,CALfC,YAAA,CAKeQ,uBAAA;YALDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,cAAc;YAAChB,KAAK,EAAC;;YAJ3DI,OAAA,EAAAC,QAAA,CAKQ;cAAA,OAGY,CAHZC,YAAA,CAGYW,oBAAA;gBARpBC,UAAA,EAK4BR,MAAA,CAAAC,IAAI,CAACQ,YAAY;gBAL7C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAK4BX,MAAA,CAAAC,IAAI,CAACQ,YAAY,GAAAE,MAAA;gBAAA;gBAAEC,WAAW,EAAC,SAAS;gBAAEC,QAAM,EAAEb,MAAA,CAAAc,kBAAkB;gBAAGC,SAAS,EAAE;;gBAL9GrB,OAAA,EAAAC,QAAA,CAMU;kBAAA,OAA+C,CAA/CC,YAAA,CAA+CoB,oBAAA;oBAApCX,KAAK,EAAC,OAAO;oBAACY,KAAK,EAAC;sBAC/BrB,YAAA,CAAwCoB,oBAAA;oBAA7BX,KAAK,EAAC,IAAI;oBAACY,KAAK,EAAC;;;gBAPtCC,CAAA;;;YAAAA,CAAA;cAWsBlB,MAAA,CAAAC,IAAI,CAACQ,YAAY,sB,cAC/BlB,YAAA,CAGea,uBAAA;YAfvBe,GAAA;YAYsBd,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,oBAAoB;YAAChB,KAAK,EAAC;;YAZnEI,OAAA,EAAAC,QAAA,CAaU;cAAA,OACwC,CADxCC,YAAA,CACwCwB,qCAAA;gBAdlDZ,UAAA,EAa+CR,MAAA,CAAAC,IAAI,CAACoB,kBAAkB;gBAbtE,uBAAAX,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAa+CX,MAAA,CAAAC,IAAI,CAACoB,kBAAkB,GAAAV,MAAA;gBAAA;gBAAGW,QAAQ,EAAEtB,MAAA,CAAAC,IAAI,CAACsB,eAAe;gBAC1FC,GAAG,EAAE;;;YAdlBN,CAAA;gBAAAO,mBAAA,gBAiBsBzB,MAAA,CAAAC,IAAI,CAACQ,YAAY,sB,cAC/BlB,YAAA,CAGea,uBAAA;YArBvBe,GAAA;YAkBsBd,KAAK,EAAC,MAAM;YAACf,KAAK,EAAC;;YAlBzCI,OAAA,EAAAC,QAAA,CAmBU;cAAA,OACmE,CADnEC,YAAA,CACmEwB,qCAAA;gBApB7EZ,UAAA,EAmB+CR,MAAA,CAAAC,IAAI,CAACsB,eAAe;gBAnBnE,uBAAAb,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAmB+CX,MAAA,CAAAC,IAAI,CAACsB,eAAe,GAAAZ,MAAA;gBAAA;gBACtDW,QAAQ,EAAEtB,MAAA,CAAAC,IAAI,CAACoB;;;YApB5BH,CAAA;gBAAAO,mBAAA,gBAuBsBzB,MAAA,CAAAC,IAAI,CAACQ,YAAY,kB,cAC/BlB,YAAA,CAEea,uBAAA;YA1BvBe,GAAA;YAwBsBd,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,iBAAiB;YAAChB,KAAK,EAAC;;YAxBhEI,OAAA,EAAAC,QAAA,CAyBU;cAAA,OAAwF,CAAxFC,YAAA,CAAwFwB,qCAAA;gBAzBlGZ,UAAA,EAyB+CR,MAAA,CAAAC,IAAI,CAACsB,eAAe;gBAzBnE,uBAAAb,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAyB+CX,MAAA,CAAAC,IAAI,CAACsB,eAAe,GAAAZ,MAAA;gBAAA;;;YAzBnEO,CAAA;gBAAAO,mBAAA,gBA6BM7B,YAAA,CAEeQ,uBAAA;YAFDC,KAAK,EAAC;UAAQ;YA7BlCX,OAAA,EAAAC,QAAA,CA8BQ;cAAA,OAAyG,CAAzGC,YAAA,CAAyG8B,0BAAA;gBA9BjHlB,UAAA,EA8BkCR,MAAA,CAAAC,IAAI,CAAC0B,cAAc;gBA9BrD,uBAAAjB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OA8BkCX,MAAA,CAAAC,IAAI,CAAC0B,cAAc,GAAAhB,MAAA;gBAAA;gBAAEiB,IAAI,EAAC,UAAU;gBAAC,cAAY,EAAC,GAAG;gBAAChB,WAAW,EAAC;;;YA9BpGM,CAAA;cAgCMtB,YAAA,CAEeQ,uBAAA;YAFDC,KAAK,EAAC;UAAQ;YAhClCX,OAAA,EAAAC,QAAA,CAiCQ;cAAA,OAAyG,CAAzGC,YAAA,CAAyG8B,0BAAA;gBAjCjHlB,UAAA,EAiCkCR,MAAA,CAAAC,IAAI,CAAC4B,cAAc;gBAjCrD,uBAAAnB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAiCkCX,MAAA,CAAAC,IAAI,CAAC4B,cAAc,GAAAlB,MAAA;gBAAA;gBAAEiB,IAAI,EAAC,UAAU;gBAAC,cAAY,EAAC,GAAG;gBAAChB,WAAW,EAAC;;;YAjCpGM,CAAA;cAoCMY,mBAAA,CAGM,OAHNC,UAGM,GAFJnC,YAAA,CAAwEoC,oBAAA;YAA7DJ,IAAI,EAAC,SAAS;YAAEK,OAAK,EAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEX,MAAA,CAAAkC,UAAU,CAAClC,MAAA,CAAAmC,OAAO;YAAA;;YArC5DzC,OAAA,EAAAC,QAAA,CAqCkE;cAAA,OAAEe,MAAA,QAAAA,MAAA,OArCpE0B,gBAAA,CAqCkE,IAAE,E;;YArCpElB,CAAA;cAsCQtB,YAAA,CAA4CoC,oBAAA;YAAhCC,OAAK,EAAEjC,MAAA,CAAAqC;UAAS;YAtCpC3C,OAAA,EAAAC,QAAA,CAsCsC;cAAA,OAAEe,MAAA,QAAAA,MAAA,OAtCxC0B,gBAAA,CAsCsC,IAAE,E;;YAtCxClB,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}