{"ast": null, "code": "/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome && 'undefined' != typeof chrome.storage ? chrome.storage.local : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = ['#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC', '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF', '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC', '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF', '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC', '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033', '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366', '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933', '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC', '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF', '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // Internet Explorer and Edge do not support colors.\n  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n    return false;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance ||\n  // is firebug? http://stackoverflow.com/a/398120/376773\n  typeof window !== 'undefined' && window.console && (window.console.firebug || window.console.exception && window.console.table) ||\n  // is firefox >= v31?\n  // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31 ||\n  // double check webkit in userAgent just in case we are in a worker\n  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function (v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n  args[0] = (useColors ? '%c' : '') + this.namespace + (useColors ? ' %c' : ' ') + args[0] + (useColors ? '%c ' : ' ') + '+' + exports.humanize(this.diff);\n  if (!useColors) return;\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit');\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function (match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console && console.log && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch (e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch (e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}", "map": {"version": 3, "names": ["exports", "module", "require", "log", "formatArgs", "save", "load", "useColors", "storage", "chrome", "local", "localstorage", "colors", "window", "process", "type", "navigator", "userAgent", "toLowerCase", "match", "document", "documentElement", "style", "WebkitAppearance", "console", "firebug", "exception", "table", "parseInt", "RegExp", "$1", "formatters", "j", "v", "JSON", "stringify", "err", "message", "args", "namespace", "humanize", "diff", "c", "color", "splice", "index", "lastC", "replace", "Function", "prototype", "apply", "call", "arguments", "namespaces", "removeItem", "debug", "e", "r", "env", "DEBUG", "enable", "localStorage"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/debug@3.1.0/node_modules/debug/src/browser.js"], "sourcesContent": ["/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  '#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC',\n  '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF',\n  '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC',\n  '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF',\n  '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC',\n  '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033',\n  '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366',\n  '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933',\n  '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC',\n  '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF',\n  '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // Internet Explorer and Edge do not support colors.\n  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n    return false;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEAA,OAAO,GAAGC,MAAM,CAACD,OAAO,GAAGE,OAAO,CAAC,SAAS,CAAC;AAC7CF,OAAO,CAACG,GAAG,GAAGA,GAAG;AACjBH,OAAO,CAACI,UAAU,GAAGA,UAAU;AAC/BJ,OAAO,CAACK,IAAI,GAAGA,IAAI;AACnBL,OAAO,CAACM,IAAI,GAAGA,IAAI;AACnBN,OAAO,CAACO,SAAS,GAAGA,SAAS;AAC7BP,OAAO,CAACQ,OAAO,GAAG,WAAW,IAAI,OAAOC,MAAM,IAC5B,WAAW,IAAI,OAAOA,MAAM,CAACD,OAAO,GAClCC,MAAM,CAACD,OAAO,CAACE,KAAK,GACpBC,YAAY,CAAC,CAAC;;AAElC;AACA;AACA;;AAEAX,OAAO,CAACY,MAAM,GAAG,CACf,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC3E,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CACjE;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASL,SAASA,CAAA,EAAG;EACnB;EACA;EACA;EACA,IAAI,OAAOM,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;IACzF,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,uBAAuB,CAAC,EAAE;IAC/H,OAAO,KAAK;EACd;;EAEA;EACA;EACA,OAAQ,OAAOC,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACC,eAAe,IAAID,QAAQ,CAACC,eAAe,CAACC,KAAK,IAAIF,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,gBAAgB;EACtJ;EACC,OAAOV,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACW,OAAO,KAAKX,MAAM,CAACW,OAAO,CAACC,OAAO,IAAKZ,MAAM,CAACW,OAAO,CAACE,SAAS,IAAIb,MAAM,CAACW,OAAO,CAACG,KAAM,CAAE;EACnI;EACA;EACC,OAAOX,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,gBAAgB,CAAC,IAAIS,QAAQ,CAACC,MAAM,CAACC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAG;EACvJ;EACC,OAAOd,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,oBAAoB,CAAE;AAC9H;;AAEA;AACA;AACA;;AAEAnB,OAAO,CAAC+B,UAAU,CAACC,CAAC,GAAG,UAASC,CAAC,EAAE;EACjC,IAAI;IACF,OAAOC,IAAI,CAACC,SAAS,CAACF,CAAC,CAAC;EAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,OAAO,8BAA8B,GAAGA,GAAG,CAACC,OAAO;EACrD;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA;;AAEA,SAASjC,UAAUA,CAACkC,IAAI,EAAE;EACxB,IAAI/B,SAAS,GAAG,IAAI,CAACA,SAAS;EAE9B+B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC/B,SAAS,GAAG,IAAI,GAAG,EAAE,IAC5B,IAAI,CAACgC,SAAS,IACbhC,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC,GACzB+B,IAAI,CAAC,CAAC,CAAC,IACN/B,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC,GACzB,GAAG,GAAGP,OAAO,CAACwC,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAC;EAErC,IAAI,CAAClC,SAAS,EAAE;EAEhB,IAAImC,CAAC,GAAG,SAAS,GAAG,IAAI,CAACC,KAAK;EAC9BL,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEF,CAAC,EAAE,gBAAgB,CAAC;;EAEtC;EACA;EACA;EACA,IAAIG,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAG,CAAC;EACbR,IAAI,CAAC,CAAC,CAAC,CAACS,OAAO,CAAC,aAAa,EAAE,UAAS5B,KAAK,EAAE;IAC7C,IAAI,IAAI,KAAKA,KAAK,EAAE;IACpB0B,KAAK,EAAE;IACP,IAAI,IAAI,KAAK1B,KAAK,EAAE;MAClB;MACA;MACA2B,KAAK,GAAGD,KAAK;IACf;EACF,CAAC,CAAC;EAEFP,IAAI,CAACM,MAAM,CAACE,KAAK,EAAE,CAAC,EAAEJ,CAAC,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASvC,GAAGA,CAAA,EAAG;EACb;EACA;EACA,OAAO,QAAQ,KAAK,OAAOqB,OAAO,IAC7BA,OAAO,CAACrB,GAAG,IACX6C,QAAQ,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC3B,OAAO,CAACrB,GAAG,EAAEqB,OAAO,EAAE4B,SAAS,CAAC;AACrE;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS/C,IAAIA,CAACgD,UAAU,EAAE;EACxB,IAAI;IACF,IAAI,IAAI,IAAIA,UAAU,EAAE;MACtBrD,OAAO,CAACQ,OAAO,CAAC8C,UAAU,CAAC,OAAO,CAAC;IACrC,CAAC,MAAM;MACLtD,OAAO,CAACQ,OAAO,CAAC+C,KAAK,GAAGF,UAAU;IACpC;EACF,CAAC,CAAC,OAAMG,CAAC,EAAE,CAAC;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASlD,IAAIA,CAAA,EAAG;EACd,IAAImD,CAAC;EACL,IAAI;IACFA,CAAC,GAAGzD,OAAO,CAACQ,OAAO,CAAC+C,KAAK;EAC3B,CAAC,CAAC,OAAMC,CAAC,EAAE,CAAC;;EAEZ;EACA,IAAI,CAACC,CAAC,IAAI,OAAO3C,OAAO,KAAK,WAAW,IAAI,KAAK,IAAIA,OAAO,EAAE;IAC5D2C,CAAC,GAAG3C,OAAO,CAAC4C,GAAG,CAACC,KAAK;EACvB;EAEA,OAAOF,CAAC;AACV;;AAEA;AACA;AACA;;AAEAzD,OAAO,CAAC4D,MAAM,CAACtD,IAAI,CAAC,CAAC,CAAC;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASK,YAAYA,CAAA,EAAG;EACtB,IAAI;IACF,OAAOE,MAAM,CAACgD,YAAY;EAC5B,CAAC,CAAC,OAAOL,CAAC,EAAE,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}