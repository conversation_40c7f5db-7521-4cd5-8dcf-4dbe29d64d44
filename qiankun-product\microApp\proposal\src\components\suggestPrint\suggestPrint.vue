<template>
  <div class="suggestPrint" ref="printRef">
    <div class="suggestPrintBody" v-for="item in printData" :key="item.id">
      <!-- <div class="suggestPrintType">
        <div><span>案号：</span>{{ item.serialNumber }}</div>
        <div><span>类别：</span>{{ item.bigThemeName }}</div>
      </div> -->
      <!-- <div class="suggestPrintName"
           @click="handlePrint">{{ item.redHeadTitle }}</div> -->
      <!-- <div class="suggestPrintItem">
        <span class="suggestPrintItemTitle">提案者11</span>
        <span class="suggestPrintItemColon">：</span>
        <span class="suggestPrintItemContent">{{ item.suggestUserName }}</span>
      </div>
      <div class="suggestPrintItemFlex">
        <div class="suggestPrintItem">
          <span class="suggestPrintItemTitle">委员证号</span>
          <span class="suggestPrintItemColon">：</span>
          <span class="suggestPrintItemContent">{{ item.cardNumberNpc }}</span>
        </div>
        <div class="suggestPrintItem">
          <span class="suggestPrintItemTitle">界别</span>
          <span class="suggestPrintItemColon">：</span>
          <span class="suggestPrintItemContent">{{ item.delegationName }}</span>
        </div>
      </div>
      <div class="suggestPrintItem">
        <span class="suggestPrintItemTitle">单位职务</span>
        <span class="suggestPrintItemColon">：</span>
        <span class="suggestPrintItemContent">{{ item.position }}</span>
      </div>
      <div class="suggestPrintItem">
        <span class="suggestPrintItemTitle">通讯地址</span>
        <span class="suggestPrintItemColon">：</span>
        <span class="suggestPrintItemContent">{{ item.callAddress }}</span>
      </div>
      <div class="suggestPrintItemFlex suggestPrintItemInfo">
        <div class="suggestPrintItem">
          <span class="suggestPrintItemTitle">邮政编码</span>
          <span class="suggestPrintItemColon">：</span>
          <span class="suggestPrintItemContent">{{ item.postcode }}</span>
        </div>
        <div class="suggestPrintItem">
          <span class="suggestPrintItemTitle">联系电话</span>
          <span class="suggestPrintItemColon">：</span>
          <span class="suggestPrintItemContent">{{ item.mobile }}</span>
        </div>
      </div>
      <div class="suggestPrintItem suggestPrintItemJoin">
        <span class="suggestPrintItemTitle">联名人</span>
        <span class="suggestPrintItemColon">：</span>
        <span class="suggestPrintItemContent">{{ item.joinUser }}</span>
      </div>
      <div class="suggestPrintItem">
        <span class="suggestPrintItemTitle">提案标题</span>
        <span class="suggestPrintItemColon">：</span>
        <span class="suggestPrintItemContent">{{ item.titleone }}</span>
      </div>
      <div class="suggestPrintItemTwo"
           v-for="(text, index) in item.titletwoArr"
           :key="index">{{ text }}</div>
      <div class="suggestPrintItemTime">提出时间：{{ item.submitDate }}</div>
      <div style="page-break-after:always"></div>
      <div class="mainModulePageTableName">其他相关信息</div>
      <table>
        <tbody>
          <tr v-if="item.suggestSurveyTypeName">
            <td :rowspan="rowspan(item)">相关情况</td>
            <td>{{ item.suggestSurveyTypeName }}</td>
            <td>{{ item.suggestSurveyTypeView }}</td>
          </tr>
          <tr v-if="item.notHandleTimeTypeName">
            <td :rowspan="rowspan(item)" v-if="!item.suggestSurveyTypeName">相关情况</td>
            <td>{{ item.notHandleTimeTypeName }}</td>
            <td>{{ item.notHandleTimeTypeView }}</td>
          </tr>
          <tr v-if="item.suggestOpenTypeName">
            <td :rowspan="rowspan(item)" v-if="!item.suggestSurveyTypeName && !item.notHandleTimeTypeName">相关情况</td>
            <td>{{ item.suggestOpenTypeName }}</td>
            <td>{{ item.suggestOpenTypeView }}</td>
          </tr>
          <tr v-if="item.isMakeMineJobName">
            <td :rowspan="rowspan(item)"
              v-if="!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName">相关情况</td>
            <td>{{ item.isMakeMineJobName }}</td>
            <td>{{ item.isMakeMineJobView }}</td>
          </tr>
          <tr v-if="item.isHopeEnhanceTalkName">
            <td :rowspan="rowspan(item)"
              v-if="!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName">
              相关情况</td>
            <td>{{ item.isHopeEnhanceTalkName }}</td>
            <td>{{ item.isHopeEnhanceTalkView }}</td>
          </tr>
          <tr v-if="item.isNeedPaperAnswerName">
            <td
              v-if="!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName && !item.isHopeEnhanceTalkName">
              相关情况</td>
            <td>{{ item.isNeedPaperAnswerName }}</td>
            <td>{{ item.isNeedPaperAnswerView }}</td>
          </tr>
          <tr>
            <td>希望送交的承办单位（仅供参考）</td>
            <td colspan="2">{{ item.hopeHandleOfficeName }}</td>
          </tr>
        </tbody>
      </table>
      <div class="suggestPrintContent"
           v-html="item.content"></div>
      <div style="page-break-after:always"></div>
      <div class="mainModulePageTableName">联名人信息表</div>
      <table>
        <tbody>
          <tr>
            <td>姓名</td>
            <td>界别</td>
            <td>委员证号</td>
            <td>单位职务</td>
            <td>联系方式</td>
            <td>通讯地址</td>
          </tr>
          <tr v-for="row in item.joinUsers" :key="row.userId">
            <td>{{ row.userName }}</td>
            <td>{{ row.sector }}</td>
            <td>{{ row.cardNumber }}</td>
            <td>{{ row.position }}</td>
            <td>{{ row.mobile }}</td>
            <td>{{ row.callAddress }}</td>
          </tr>
        </tbody>
      </table>
      <div class="mainModulePageTableName">提案联系人</div>
      <table>
        <tbody>
          <tr>
            <td colspan="2">姓名</td>
            <td colspan="2">联系电话</td>
            <td colspan="3">通讯地址</td>
          </tr>
          <tr v-for="row in item.contacters" :key="row.id">
            <td colspan="2">{{ row.contacterName }}</td>
            <td colspan="2">{{ row.contacterMobile }}</td>
            <td colspan="3">{{ row.contacterAddress }}</td>
          </tr>
        </tbody>
      </table>
      <div class="mainModulePageTableName">审查和办理信息表</div>
      <table>
        <tbody>
          <tr>
            <td>本提案目前状态</td>
            <td colspan="2">{{ item.currentStatus }}</td>
          </tr>
          <tr>
            <td>审查情况</td>
            <td colspan="2">{{ item.verifyStatus }}</td>
          </tr>
          <tr v-if="item.mainHandleOffice">
            <td rowspan="2">办理单位</td>
            <td>主办</td>
            <td>{{ item.mainHandleOffice }}</td>
          </tr>
          <tr v-if="item.mainHandleOffice">
            <td>协办</td>
            <td>{{ item.assistHandleOffice }}</td>
          </tr>
          <tr v-if="item.publishHandleOffice">
            <td>办理单位</td>
            <td>分办</td>
            <td>{{ item.publishHandleOffice }}</td>
          </tr>
        </tbody>
      </table>
      <div style="page-break-after:always"></div> -->

      <div class="suggestPrintName" @click="handlePrint">中国人民政治协商会议{{ areaName }}委员会{{ item.circlesType.name
      }}{{ item.boutType.name }}会议</div>
      <div class="suggestPrintText">提&nbsp;&nbsp;案</div>
      <div class="suggestPrintTextNumber">提案编号：第{{ item.serialNumber }}号</div>
      <div class="suggestPrintContainer">
        <div class="suggestPrintContainerTitle">
          <div class="suggestPrintContainerTitleLabel">提案题目</div>
          <div class="suggestPrintContainerTitleContent">{{ item.title }}</div>
        </div>
        <div class="suggestPrintContainerContent">
          <div class="suggestPrintContainerContentLabel">交办意见</div>
          <div class="suggestPrintContainerContentContent">
            <div class="suggestPrintContainerContentContentItem">主办单位：{{ item.mainHandleOffice
            }}{{ item.publishHandleOffice }}</div>
            <div class="suggestPrintContainerContentContentItem">协办单位：{{ item.assistHandleOffice }}</div>
            <div style="height: 80px;"></div>
            <div class="suggestPrintContainerContentContentRight">
              {{ areaName }}政协提案委员会
            </div>
            <img :src="areaLogo" alt="Stamp" class="suggestPrintContainerContentContentStamp">
            <div class="suggestPrintContainerContentContentTime">年 月 日</div>
          </div>
        </div>
      </div>
      <div class="proposerBasicInformationTitle">提案者基本情况</div>
      <div class="proposerBasicInformationTable">
        <!-- 第一行 -->
        <div class="cell cell-title">第一提案者</div>
        <div class="cell">{{ item.suggestUserName }}</div>
        <div class="cell cell-title">提案者类型</div>
        <div class="cell">{{ item.teamOfficeTheme }}</div>

        <!-- 第二行 -->
        <div class="cell cell-title">工作单位及职务</div>
        <div class="cell col-span-3">{{ item.position }}</div>

        <!-- 第三行 -->
        <div class="cell cell-title">通讯地址</div>
        <div class="cell col-span-3">{{ item.callAddress }}</div>

        <!-- 第四行 -->
        <div class="cell cell-title">办公电话</div>
        <div class="cell">{{ item.officePhone }}</div>
        <div class="cell cell-title">手机</div>
        <div class="cell">{{ item.mobile }}</div>

        <!-- 第五行 -->
        <div class="cell cell-title">界别</div>
        <div class="cell">{{ item.delegationName }}</div>
        <div class="cell cell-title">党派</div>
        <div class="cell">{{ item.party.name }}</div>

        <!-- 第六行 -->
        <div class="cell cell-title">联名提案者</div>
        <div class="cell col-span-3">{{ item.joinUser }}</div>
      </div>
      <div class="remarks">
        附注：1、承办单位应在提案委员会交办提案之日起3个月内办结提案并答复提案者，特殊情况不得超过6个月。2、主办单位应在所主办提案全部答复提案者并填写完成《政协提案答复情况征询意见表》后10日内，整理答复意见文件和征询意见表，形成办理工作总结，一并报送市政协提案委和市委市政府督查室。
      </div>
      <div>
        <div class="proposalTitle">{{ item.title }}</div>
        <div class="proposalContent" v-html="item.content"></div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'suggestPrint' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, nextTick } from 'vue'
import { filterTableData } from '@/assets/js/suggestExportWord'
import { Print } from 'common/js/print'
import { user } from 'common/js/system_var.js'
const props = defineProps({ params: { type: Object, default: () => ({}) } })
const emit = defineEmits(['callback'])
const printData = ref([])
const printRef = ref()
const areaName = ref('')
const areaLogo = ref('')
const handlePrint = () => {
  Print.init(printRef.value)
}
onMounted(() => {
  const areaId = user.value.areaId
  if (areaId == '370500') {
    areaName.value = '东营市'
    areaLogo.value = require('../../assets/img/dongyingshizhang.png')
  } else if (areaId == '370502') {
    areaName.value = '东营区'
    areaLogo.value = require('../../assets/img/daitianjia.png')
  } else if (areaId == '370503') {
    areaName.value = '河口区'
    areaLogo.value = require('../../assets/img/daitianjia.png')
  } else if (areaId == '370505') {
    areaName.value = '垦利区'
    areaLogo.value = require('../../assets/img/daitianjia.png')
  } else if (areaId == '370522') {
    areaName.value = '利津县'
    areaLogo.value = require('../../assets/img/daitianjia.png')
  } else if (areaId == '370523') {
    areaName.value = '广饶县'
    areaLogo.value = require('../../assets/img/guangraoxianzhang.png')
  } else {
    areaName.value = ''
    areaLogo.value = ''
  }

  suggestionWord()
})
// const rowspan = (item) => {
//   let rowspannum = 0
//   if (item.suggestSurveyTypeName) { rowspannum += 1 }
//   if (item.notHandleTimeTypeName) { rowspannum += 1 }
//   if (item.suggestOpenTypeName) { rowspannum += 1 }
//   if (item.isMakeMineJobName) { rowspannum += 1 }
//   if (item.isHopeEnhanceTalkName) { rowspannum += 1 }
//   if (item.isNeedPaperAnswerName) { rowspannum += 1 }
//   return rowspannum
// }
const suggestionWord = async () => {
  const { data } = await api.suggestionWord(props.params)
  if (data.length) {
    printData.value = []
    for (let index = 0; index < data.length; index++) {
      printData.value.push(filterTableData(data[index]))
    }
    nextTick(() => {
      handlePrint()
      emit('callback')
    })
  }
}
defineExpose({ print: handlePrint })
</script>
<style lang="scss">
@font-face {
  font-family: "FZXiaoBiaoSong";
  src: url(../../assets/img/FZXiaoBiaoSong-B05S.ttf);
}

.suggestPrint {
  width: 100%;

  .suggestPrintBody {
    width: 100%;

    // .suggestPrintType {
    //   font-size: 16pt;
    //   font-weight: bold;
    //   padding-bottom: 40pt;
    //   display: flex;
    //   justify-content: space-between;

    //   span {
    //     color: red;
    //   }
    // }

    // .suggestPrintName {
    //   color: red;
    //   font-size: 24pt;
    //   line-height: 1.2;
    //   font-weight: bold;
    //   text-align: center;
    //   padding-bottom: 60pt;
    // }

    // .suggestPrintItem {
    //   width: 100%;
    //   display: flex;
    //   font-size: 16pt;
    //   line-height: 1.5;
    //   padding-bottom: 8pt;

    //   span {
    //     display: inline-block;
    //   }

    //   .suggestPrintItemTitle {
    //     width: 92px;
    //     color: red;
    //     font-weight: bold;
    //     text-align: justify;
    //     text-align-last: justify;
    //   }

    //   .suggestPrintItemColon {
    //     width: 16px;
    //     color: red;
    //   }

    //   .suggestPrintItemContent {
    //     width: calc(100% - 108px);
    //     border-bottom: 1px solid #262626;
    //   }
    // }

    // .suggestPrintItemInfo {
    //   padding-bottom: 54pt;
    // }

    // .suggestPrintItemJoin {
    //   padding-bottom: 32pt;
    // }

    // .suggestPrintItemTwo {
    //   width: 100%;
    //   font-size: 16pt;
    //   line-height: 1.5;
    //   min-height: calc(16pt * 1.5);
    //   border-bottom: 1px solid #262626;
    //   margin-bottom: 8pt;
    // }

    // .suggestPrintItemFlex {
    //   width: 100%;
    //   display: flex;
    //   justify-content: space-between;

    //   .suggestPrintItem {
    //     width: 46%;
    //   }
    // }

    // .suggestPrintItemTime {
    //   color: red;
    //   text-align: center;
    //   font-size: 14pt;
    //   line-height: 1.5;
    //   padding-top: 80pt;
    //   font-family: "Times New Roman";
    // }

    // .mainModulePageTableName {
    //   font-size: 14pt;
    //   line-height: 1.5;
    //   text-align: center;
    //   margin-bottom: 7pt;
    // }

    // table {
    //   width: 100%;
    //   table-layout: fixed;
    //   word-break: break-all;
    //   border-collapse: collapse;
    //   margin-bottom: 32pt;

    //   tr {
    //     page-break-inside: avoid;

    //     td {
    //       text-align: center;
    //       line-height: 1.5;
    //       padding: 8px;
    //       font-size: 12pt;
    //       border: 1px solid #000;
    //       font-family: "宋体";
    //       display: table-cell;
    //       vertical-align: middle;
    //     }
    //   }
    // }

    // .suggestPrintContent {
    //   overflow: hidden;
    //   line-height: var(--zy-line-height);

    //   img,
    //   video {
    //     max-width: 100%;
    //     height: auto !important;
    //   }

    //   table {
    //     border-collapse: collapse;
    //     border-spacing: 0;

    //     tr {
    //       page-break-inside: avoid;
    //     }
    //   }
    // }

    .suggestPrintName {
      font-size: 16pt;
      line-height: 1.2;
      text-align: center;
      padding-bottom: 20pt;
      font-family: 宋体;
    }

    .suggestPrintText {
      font-size: 42pt;
      text-align: center;
      padding-bottom: 10pt;
      font-weight: bold;
      color: #000;
      font-family: 宋体;
    }

    .suggestPrintTextNumber {
      font-size: 14pt;
      text-align: center;
      padding-bottom: 10pt;
      font-family: 宋体;
    }

    .suggestPrintContainer {
      width: 600px;
      border: 1px solid black;
      display: grid;
      grid-template-rows: auto 1fr;
      font-family: Arial, sans-serif;

      .suggestPrintContainerTitle {
        display: flex;
        border-bottom: 1px solid black;

        .suggestPrintContainerTitle:last-child {
          border-bottom: none;
        }

        .suggestPrintContainerTitleLabel,
        .suggestPrintContainerTitleContent {
          padding: 15px;
        }

        .suggestPrintContainerTitleLabel {
          width: 120px;
          border-right: 1px solid black;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 15pt;
          font-family: 黑体;
        }

        .suggestPrintContainerTitleContent {
          flex: 1;
          position: relative;
          font-size: 14pt;
          font-family: 宋体;
        }
      }

      .suggestPrintContainerContent {
        display: flex;

        .suggestPrintContainerContentLabel,
        .suggestPrintContainerContentContent {
          padding: 15px;
        }

        .suggestPrintContainerContentLabel {
          width: 120px;
          border-right: 1px solid black;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 15pt;
          font-family: 黑体;
        }

        .suggestPrintContainerContentContent {
          flex: 1;
          position: relative;

          .suggestPrintContainerContentContentItem {
            margin: 10px 0;
            font-size: 14pt;
            font-family: 宋体;
          }

          .suggestPrintContainerContentContentRight {
            position: absolute;
            right: 10px;
            bottom: 25px;
            font-size: 14pt;
            font-family: 宋体;
          }

          .suggestPrintContainerContentContentStamp {
            position: absolute;
            right: 0px;
            bottom: 10px;
            width: 130px;
            opacity: 0.6;
          }

          .suggestPrintContainerContentContentTime {
            position: absolute;
            right: 10px;
            bottom: 0px;
            font-size: 14pt;
            font-family: 宋体;
          }
        }
      }
    }

    .proposerBasicInformationTitle {
      font-size: 18pt;
      font-family: 黑体;
      text-align: center;
      margin: 10px 0;
    }

    .proposerBasicInformationTable {
      display: grid;
      width: 600px;
      border: 1px solid black;
      grid-template-columns: 160px 170px 120px 150px;
      font-family: Arial, sans-serif;

      .cell {
        border: 1px solid black;
        padding: 8px;
        text-align: center;
        font-size: 14pt;
        font-family: 宋体;
      }

      table {
        max-width: 100%;
        border-collapse: collapse;
        border-spacing: 0;

        tr {
          page-break-inside: avoid;
        }
      }

      .col-span-3 {
        grid-column: span 3;
        text-align: left;
      }

      .col-span-2 {
        grid-column: span 2;
        text-align: left;
      }
    }

    .remarks {
      font-size: 12pt;
      line-height: 1.6;
      font-family: 楷体;
    }

    .proposalTitle {
      text-align: center;
      font-size: 22pt;
      font-family: FZXiaoBiaoSong;
      margin-top: 50px;
    }
  }
}
</style>
