<template>
  <div class="HistoricalProposalNew">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="届次" prop="termYearId">
        <el-select v-model="form.termYearId" placeholder="请选择届次" clearable>
          <el-option v-for="item in termYearData" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="案号" prop="serialNumber">
        <el-input v-model="form.serialNumber" placeholder="请输入案号" clearable />
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入标题" clearable />
      </el-form-item>
      <el-form-item label="分类">
        <el-select v-model="form.type" placeholder="请选择分类" clearable>
          <el-option v-for="item in typeData" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="提案者" prop="suggestUserId">
        <el-input v-model="form.suggestUserId" placeholder="请输入提案者" clearable />
      </el-form-item>
      <el-form-item label="党派">
        <el-select v-model="form.party" placeholder="请选择党派" clearable>
          <el-option v-for="item in partyData" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="界别">
        <el-select v-model="form.circles" placeholder="请选择界别" clearable>
          <el-option v-for="item in circlesData" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="联名人">
        <el-input v-model="form.joinProposal" placeholder="请输入联名人" clearable />
      </el-form-item>
      <el-form-item label="提交时间" prop="submitDate">
        <el-date-picker v-model="form.submitDate" type="date" value-format="x" placeholder="请选择提交时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="内容" class="globalFormTitle">
        <TinyMceEditor v-model="form.content" />
      </el-form-item>
      <el-form-item label="附件" class="globalFormTitle">
        <xyl-upload-file :fileData="form.attachmentIds" @fileUpload="fileUpload" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="form.processStatus" placeholder="请选择状态" clearable>
          <el-option v-for="item in processStatusData" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="不予立案理由" v-if="form.processStatus == '不予立案'">
        <el-select v-model="form.notRegisteredReasons" placeholder="请选择不予立案理由" clearable>
          <el-option v-for="item in notRegisteredReasonsData" :key="item.key" :label="item.name" :value="item.key" />
        </el-select>
      </el-form-item>
      <el-form-item label="办理方式">
        <el-select v-model="form.handlingMethod" placeholder="请选择办理方式" clearable>
          <el-option v-for="item in handlingMethodData" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="办理单位">
        <el-input v-model="form.handlingUnit" placeholder="请输入办理单位" clearable />
      </el-form-item>
      <el-form-item label="答复文件" class="globalFormTitle">
        <xyl-upload-file :fileData="form.replyDocument" @fileUpload="fileReplyDocumentUpload" />
      </el-form-item>
      <el-form-item label="办理情况" v-if="false">
        <el-select v-model="form.processingStatus" placeholder="请选择办理情况" clearable>
          <el-option v-for="item in processingStatusData" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="提案评价" prop="unitEvaluationName" v-if="false">
        <el-radio-group v-model="form.unitEvaluationName">
          <el-radio label="优秀">优秀</el-radio>
          <el-radio label="良好">良好</el-radio>
          <el-radio label="一般">一般</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="征询意见表满意度" prop="memberEvaluationName" v-if="false">
        <el-radio-group v-model="form.memberEvaluationName">
          <el-radio label="满意">满意</el-radio>
          <el-radio label="基本满意">基本满意</el-radio>
          <el-radio label="不满意">不满意</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'HistoricalProposalNew' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
// import { validNum } from 'common/js/utils.js'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  termYearId: '', // 届次
  serialNumber: '', // 案号
  title: '', // 标题
  type: '', // 分类
  suggestUserId: '', // 提案者
  party: '', // 党派
  circles: '', // 界别
  joinProposal: '', // 联名人
  submitDate: '', // 提交时间
  content: '', // 内容
  attachmentIds: [], // 附件
  processStatus: '', // 状态
  notRegisteredReasons: '', // 不予立案理由
  handlingMethod: '', // 办理方式
  handlingUnit: '', // 办理单位
  replyDocument: [], // 答复文件
  processingStatus: '', // 办理情况
  unitEvaluationName: '', // 提案评价
  memberEvaluationName: '', // 征询意见表满意度
})
const rules = reactive({
  termYearId: [{ required: true, message: '请选择届次', trigger: ['blur', 'change'] }],
  serialNumber: [{ required: true, message: '请输入案号', trigger: ['blur', 'change'] }],
  title: [{ required: true, message: '请输入标题', trigger: ['blur', 'change'] }],
  suggestUserId: [{ required: true, message: '请输入标题', trigger: ['blur', 'change'] }],
  submitDate: [{ required: true, message: '请选择提交时间', trigger: ['blur', 'change'] }],
})
const termYearData = ref([])
const typeData = ref([])
const partyData = ref([])
const circlesData = ref([])
const processStatusData = ref([
  { key: '1', name: '立案' },
  { key: '2', name: '不予立案' },
  { key: '3', name: '转来信' },
  { key: '4', name: '转社情民意' }
])
const notRegisteredReasonsData = ref([])
const handlingMethodData = ref([
  { key: '1', name: '主协办' },
  { key: '2', name: '分办' },
])
const processingStatusData = ref([])
onMounted(() => {
  // getTermYearList()
  getThemeSelectList()
  dictionaryData()
  if (props.id) { suggestionInfo() }
})
// 字典
const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['sector_type', 'party', 'suggestion_reject_reason', 'suggestion_answer_type', 'history_proposal_session'] })
  var { data } = res
  partyData.value = data.party
  circlesData.value = data.sector_type
  notRegisteredReasonsData.value = data.suggestion_reject_reason
  processingStatusData.value = data.suggestion_answer_type
  termYearData.value = data.history_proposal_session
}
// 届次
// const getTermYearList = async () => {
//   const res = await api.suggestionTermYearList({ pageNo: 1, pageSize: 100 })
//   var { data } = res
//   termYearData.value = data
// }
// 分类
const getThemeSelectList = async () => {
  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })
  var { data } = res
  typeData.value = data
}
// 上传附件
const fileUpload = (file) => {
  form.attachmentIds = file
}
// 上传答复文件
const fileReplyDocumentUpload = (file) => {
  form.replyDocument = file
}
// 详情
const suggestionInfo = async () => {
  const res = await api.proposalHistoryV2Info({ detailId: props.id })
  var { data } = res
  form.termYearId = data.termYearId
  form.serialNumber = data.serialNumber
  form.title = data.title
  form.type = data.type
  form.suggestUserId = data.suggestUserId
  form.party = data.party
  form.circles = data.circles
  form.joinProposal = data.joinProposal
  form.submitDate = data.submitDate
  form.content = data.content
  form.attachmentIds = data.fileInfoList
  form.processStatus = data.processStatus
  form.notRegisteredReasons = data.notRegisteredReasonId
  form.handlingMethod = data.handlingMethod
  form.handlingUnit = data.handlingUnit
  form.replyDocument = data.replyFileInfoList
  form.processingStatus = data.handlingContent
  form.unitEvaluationName = data.unitEvaluationName
  form.memberEvaluationName = data.memberEvaluationName
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(props.id ? '/proposalHistoryV2/edit' : '/proposalHistoryV2/add', {
    form: {
      id: props.id,
      termYearId: form.termYearId,
      serialNumber: form.serialNumber,
      title: form.title,
      type: form.type,
      suggestUserId: form.suggestUserId,
      party: form.party,
      circles: form.circles,
      joinProposal: form.joinProposal,
      submitDate: form.submitDate,
      content: form.content,
      fileId: form.attachmentIds.map(v => v.id).join(','),
      processStatus: form.processStatus,
      notRegisteredReasonId: form.notRegisteredReasons,
      handlingMethod: form.handlingMethod,
      handlingUnit: form.handlingUnit,
      replyFileId: form.replyDocument.map(v => v.id).join(','),
      handlingContent: form.processingStatus,
      unitEvaluationName: form.unitEvaluationName,
      memberEvaluationName: form.memberEvaluationName
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}
const resetForm = () => { emit('callback') }
</script>
<style lang="scss">
.HistoricalProposalNew {
  width: 990px;
}
</style>
