{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted } from 'vue';\nimport * as RongIMLib from '@rongcloud/imlib-next';\nimport { format } from 'common/js/time.js';\nimport { appOnlyHeader } from 'common/js/system_var.js';\nimport { mobileIcon, maleIcon, femaleIcon } from '../js/icon.js';\nimport { ElMessage } from 'element-plus';\nimport { Search } from '@element-plus/icons-vue';\nimport config from 'common/config';\nimport QrcodeVue from 'qrcode.vue';\nimport { useStore } from 'vuex';\nvar __default__ = {\n  name: 'GlobalChatAddressBook'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  emits: ['send'],\n  setup(__props, _ref) {\n    var _window$electron;\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    var store = useStore();\n    var emit = __emit;\n    var isMac = (_window$electron = window.electron) === null || _window$electron === void 0 ? void 0 : _window$electron.isMac;\n    var treeRef = ref();\n    var keyword = ref('');\n    var labelAll = ref([]);\n    var userId = ref('');\n    var userInfo = ref({});\n    var oftenList = ref([]);\n    var imgUrl = function imgUrl(url) {\n      return url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg');\n    };\n    var shareUrl = ref('');\n    var querySearch = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(queryString, cb) {\n        var results;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (!queryString) {\n                _context.next = 6;\n                break;\n              }\n              _context.next = 3;\n              return handleQuery(queryString);\n            case 3:\n              _context.t0 = _context.sent;\n              _context.next = 7;\n              break;\n            case 6:\n              _context.t0 = [];\n            case 7:\n              results = _context.t0;\n              cb(results);\n            case 9:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function querySearch(_x, _x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleQuery = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var newUserDataAll, index, item, newUserData;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              newUserDataAll = [];\n              index = 0;\n            case 2:\n              if (!(index < labelAll.value.length)) {\n                _context2.next = 11;\n                break;\n              }\n              item = labelAll.value[index];\n              _context2.next = 6;\n              return handleUserData(item.id);\n            case 6:\n              newUserData = _context2.sent;\n              newUserDataAll = [].concat(_toConsumableArray(newUserDataAll), _toConsumableArray(newUserData));\n            case 8:\n              index++;\n              _context2.next = 2;\n              break;\n            case 11:\n              return _context2.abrupt(\"return\", _toConsumableArray(new Map(newUserDataAll.map(function (item) {\n                return [item.id, item];\n              })).values()));\n            case 12:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function handleQuery() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var handleUserData = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(id) {\n        var _yield$api$SelectPers, data, newUserData, index, item;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return api.SelectPersonBookUser({\n                isOpen: 1,\n                keyword: keyword.value,\n                labelCode: id,\n                nodeId: '',\n                relationBookId: '',\n                tabCode: 'relationBooksTemp'\n              });\n            case 3:\n              _yield$api$SelectPers = _context3.sent;\n              data = _yield$api$SelectPers.data;\n              newUserData = [];\n              for (index = 0; index < data.length; index++) {\n                item = data[index];\n                if (item.userId) newUserData.push({\n                  id: item.userId,\n                  label: item.userName,\n                  children: [],\n                  type: 'user',\n                  user: item,\n                  isLeaf: true\n                });\n              }\n              return _context3.abrupt(\"return\", newUserData);\n            case 10:\n              _context3.prev = 10;\n              _context3.t0 = _context3[\"catch\"](0);\n              return _context3.abrupt(\"return\", []);\n            case 13:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 10]]);\n      }));\n      return function handleUserData(_x3) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var handleClick = function handleClick(item) {\n      userId.value = item.id;\n      getUserInfo(item);\n    };\n    var getUserInfo = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _yield$api$userInfo, data;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.userInfo({\n                detailId: userId.value\n              });\n            case 2:\n              _yield$api$userInfo = _context4.sent;\n              data = _yield$api$userInfo.data;\n              userInfo.value = data;\n              globalReadConfig();\n            case 6:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function getUserInfo() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n\n    // 获取个人二维码\n    var globalReadConfig = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var _yield$api$globalRead, data;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.next = 2;\n              return api.globalReadConfig({\n                codes: ['appShareAddress']\n              });\n            case 2:\n              _yield$api$globalRead = _context5.sent;\n              data = _yield$api$globalRead.data;\n              getUserQrCode(data.appShareAddress);\n            case 5:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function globalReadConfig() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var getUserQrCode = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6(appShareAddress) {\n        var params;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              params = {\n                n: 'mo_npcinfo_details',\n                u: '../mo_npcinfo_details/mo_npcinfo_details.stml',\n                p: {\n                  id: userId.value\n                }\n              };\n              longShortLink(appShareAddress + 'pages/index/?' + JSON.stringify(params).replace(/\\{/g, \"%7B\").replace(/\\}/g, \"%7D\").replace(/\\u0022/g, \"%22\"));\n            case 2:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function getUserQrCode(_x4) {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var longShortLink = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7(url) {\n        var _yield$api$longShortL, data;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _context7.next = 2;\n              return api.longShortLink(encodeURIComponent(url));\n            case 2:\n              _yield$api$longShortL = _context7.sent;\n              data = _yield$api$longShortL.data;\n              shareUrl.value = `${config.API_URL}/viewing/${data}`;\n            case 5:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7);\n      }));\n      return function longShortLink(_x5) {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    // 跳转个人信息\n    var openUser = function openUser(item) {\n      if (item.roleIds) {\n        if (item.roleIds.includes('1640259895343255554')) {\n          store.commit('setOpenRoute', {\n            name: '委员信息详情',\n            path: '/cppccMember/CppccMemberDetails',\n            query: {\n              id: item.id\n            }\n          });\n          return;\n        }\n        if (item.roleIds.includes('1684119713426243586') || item.roleIds.includes('1743150705994133506') || item.roleIds.includes('1643857093725327362')) {\n          store.commit('setOpenRoute', {\n            name: '用户信息详情',\n            path: '/system/SubmitUser',\n            query: {\n              id: item.id,\n              utype: 1\n            }\n          });\n        }\n      }\n    };\n    var handleMessages = /*#__PURE__*/function () {\n      var _ref9 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8(conversationType, targetId) {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              _context8.next = 2;\n              return RongIMLib.getConversation({\n                conversationType,\n                targetId\n              });\n            case 2:\n              res = _context8.sent;\n              return _context8.abrupt(\"return\", res);\n            case 4:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8);\n      }));\n      return function handleMessages(_x6, _x7) {\n        return _ref9.apply(this, arguments);\n      };\n    }();\n    var handleSendMessage = /*#__PURE__*/function () {\n      var _ref10 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9() {\n        var targetId, _yield$handleMessages, code, data, _data$latestMessage, _data$latestMessage2, _data$latestMessage3, newSendMessage;\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1) switch (_context9.prev = _context9.next) {\n            case 0:\n              targetId = appOnlyHeader.value + userInfo.value.accountId;\n              _context9.next = 3;\n              return handleMessages(1, targetId);\n            case 3:\n              _yield$handleMessages = _context9.sent;\n              code = _yield$handleMessages.code;\n              data = _yield$handleMessages.data;\n              if (!code) {\n                newSendMessage = {\n                  isTemporary: true,\n                  isTop: data.isTop,\n                  isNotInform: data.notificationStatus,\n                  id: data.targetId,\n                  targetId: data.targetId,\n                  type: data.conversationType,\n                  chatObjectInfo: {\n                    uid: data.targetId,\n                    id: userInfo.value.accountId,\n                    name: userInfo.value.userName,\n                    img: userInfo.value.photo || userInfo.value.headImg,\n                    userInfo: {\n                      userId: userInfo.value.id,\n                      userName: userInfo.value.userName,\n                      photo: userInfo.value.photo,\n                      headImg: userInfo.value.headImg\n                    }\n                  },\n                  sentTime: ((_data$latestMessage = data.latestMessage) === null || _data$latestMessage === void 0 ? void 0 : _data$latestMessage.sentTime) || Date.parse(new Date()),\n                  messageType: ((_data$latestMessage2 = data.latestMessage) === null || _data$latestMessage2 === void 0 ? void 0 : _data$latestMessage2.messageType) || 'RC:TxtMsg',\n                  content: ((_data$latestMessage3 = data.latestMessage) === null || _data$latestMessage3 === void 0 ? void 0 : _data$latestMessage3.content) || {\n                    content: ''\n                  },\n                  count: data.unreadMessageCount\n                };\n                emit('send', newSendMessage);\n              }\n            case 7:\n            case \"end\":\n              return _context9.stop();\n          }\n        }, _callee9);\n      }));\n      return function handleSendMessage() {\n        return _ref10.apply(this, arguments);\n      };\n    }();\n    var SelectPersonTab = /*#__PURE__*/function () {\n      var _ref11 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee10(resolve) {\n        var _yield$api$SelectPers2, data, newLabelData, index, _data$, _data$2, item;\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1) switch (_context10.prev = _context10.next) {\n            case 0:\n              _context10.next = 2;\n              return api.SelectPersonTab({\n                tabCodes: ['relationBooksTemp']\n              });\n            case 2:\n              _yield$api$SelectPers2 = _context10.sent;\n              data = _yield$api$SelectPers2.data;\n              newLabelData = [];\n              for (index = 0; index < ((_data$ = data[0]) === null || _data$ === void 0 ? void 0 : _data$.chooseLabels.length); index++) {\n                item = (_data$2 = data[0]) === null || _data$2 === void 0 ? void 0 : _data$2.chooseLabels[index];\n                newLabelData.push({\n                  id: item.labelCode,\n                  label: item.name,\n                  children: [],\n                  type: 'label',\n                  isLeaf: false\n                });\n              }\n              labelAll.value = newLabelData;\n              resolve(newLabelData);\n            case 8:\n            case \"end\":\n              return _context10.stop();\n          }\n        }, _callee10);\n      }));\n      return function SelectPersonTab(_x8) {\n        return _ref11.apply(this, arguments);\n      };\n    }();\n    var _handleTreeData = function handleTreeData(id, data) {\n      var newLabelData = [];\n      for (var index = 0; index < data.length; index++) {\n        var item = data[index];\n        if (item.code !== id) {\n          var children = _handleTreeData(id, item.children);\n          newLabelData.push({\n            id: item.code,\n            label: item.name,\n            children: children,\n            type: 'tree',\n            isLeaf: false\n          });\n        }\n      }\n      return newLabelData;\n    };\n    var SelectPersonGroup = /*#__PURE__*/function () {\n      var _ref12 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee11(id) {\n        var _yield$api$SelectPers3, data;\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              _context11.next = 2;\n              return api.SelectPersonGroup({\n                labelCode: id,\n                tabCode: 'relationBooksTemp'\n              });\n            case 2:\n              _yield$api$SelectPers3 = _context11.sent;\n              data = _yield$api$SelectPers3.data;\n              return _context11.abrupt(\"return\", _handleTreeData(id, data));\n            case 5:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee11);\n      }));\n      return function SelectPersonGroup(_x9) {\n        return _ref12.apply(this, arguments);\n      };\n    }();\n    var SelectPersonBookUser = /*#__PURE__*/function () {\n      var _ref13 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee12(parentId, id) {\n        var _yield$api$SelectPers4, data, newUserData, index, item;\n        return _regeneratorRuntime().wrap(function _callee12$(_context12) {\n          while (1) switch (_context12.prev = _context12.next) {\n            case 0:\n              _context12.next = 2;\n              return api.SelectPersonBookUser({\n                isOpen: 1,\n                keyword: '',\n                labelCode: parentId,\n                nodeId: id,\n                relationBookId: id,\n                tabCode: 'relationBooksTemp'\n              });\n            case 2:\n              _yield$api$SelectPers4 = _context12.sent;\n              data = _yield$api$SelectPers4.data;\n              newUserData = [];\n              for (index = 0; index < data.length; index++) {\n                item = data[index];\n                if (item.userId) newUserData.push({\n                  id: item.userId,\n                  label: item.userName,\n                  children: [],\n                  type: 'user',\n                  user: item,\n                  isLeaf: true\n                });\n              }\n              return _context12.abrupt(\"return\", newUserData);\n            case 7:\n            case \"end\":\n              return _context12.stop();\n          }\n        }, _callee12);\n      }));\n      return function SelectPersonBookUser(_x10, _x11) {\n        return _ref13.apply(this, arguments);\n      };\n    }();\n    var loadNode = /*#__PURE__*/function () {\n      var _ref14 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee13(node, resolve) {\n        var _node$data, _node$data2, newTreeData, newUserData, newData, _newUserData, _newTreeData, _newUserData2;\n        return _regeneratorRuntime().wrap(function _callee13$(_context13) {\n          while (1) switch (_context13.prev = _context13.next) {\n            case 0:\n              if (!(node.level === 0)) {\n                _context13.next = 4;\n                break;\n              }\n              SelectPersonTab(resolve);\n              _context13.next = 27;\n              break;\n            case 4:\n              if (!((_node$data = node.data) !== null && _node$data !== void 0 && (_node$data = _node$data.children) !== null && _node$data !== void 0 && _node$data.length)) {\n                _context13.next = 13;\n                break;\n              }\n              newTreeData = (_node$data2 = node.data) === null || _node$data2 === void 0 ? void 0 : _node$data2.children;\n              _context13.next = 8;\n              return SelectPersonBookUser(node.parent.key, node.key);\n            case 8:\n              newUserData = _context13.sent;\n              newData = [].concat(_toConsumableArray(newTreeData), _toConsumableArray(newUserData));\n              resolve(newData);\n              _context13.next = 27;\n              break;\n            case 13:\n              if (!node.parent.level) {\n                _context13.next = 20;\n                break;\n              }\n              _context13.next = 16;\n              return SelectPersonBookUser(node.parent.key, node.key);\n            case 16:\n              _newUserData = _context13.sent;\n              resolve(_newUserData);\n              _context13.next = 27;\n              break;\n            case 20:\n              _context13.next = 22;\n              return SelectPersonGroup(node.key);\n            case 22:\n              _newTreeData = _context13.sent;\n              _context13.next = 25;\n              return SelectPersonBookUser(node.key, node.key);\n            case 25:\n              _newUserData2 = _context13.sent;\n              resolve([].concat(_toConsumableArray(_newTreeData), _toConsumableArray(_newUserData2)));\n            case 27:\n            case \"end\":\n              return _context13.stop();\n          }\n        }, _callee13);\n      }));\n      return function loadNode(_x12, _x13) {\n        return _ref14.apply(this, arguments);\n      };\n    }();\n    var relationBookMemberOftenList = /*#__PURE__*/function () {\n      var _ref15 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee14(id) {\n        var _yield$api$relationBo, data;\n        return _regeneratorRuntime().wrap(function _callee14$(_context14) {\n          while (1) switch (_context14.prev = _context14.next) {\n            case 0:\n              _context14.next = 2;\n              return api.relationBookMemberOftenList();\n            case 2:\n              _yield$api$relationBo = _context14.sent;\n              data = _yield$api$relationBo.data;\n              oftenList.value = data;\n            case 5:\n            case \"end\":\n              return _context14.stop();\n          }\n        }, _callee14);\n      }));\n      return function relationBookMemberOftenList(_x14) {\n        return _ref15.apply(this, arguments);\n      };\n    }();\n    var handleOftenList = function handleOftenList(type) {\n      relationBookMemberSetOften(type);\n    };\n    var relationBookMemberSetOften = /*#__PURE__*/function () {\n      var _ref16 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee15(type) {\n        var _yield$api$relationBo2, code;\n        return _regeneratorRuntime().wrap(function _callee15$(_context15) {\n          while (1) switch (_context15.prev = _context15.next) {\n            case 0:\n              _context15.next = 2;\n              return api.relationBookMemberSetOften({\n                isOften: type,\n                userIds: [userId.value]\n              });\n            case 2:\n              _yield$api$relationBo2 = _context15.sent;\n              code = _yield$api$relationBo2.code;\n              if (code === 200) {\n                relationBookMemberOftenList();\n                SelectPersonTab(function (data) {\n                  var _treeRef$value;\n                  (_treeRef$value = treeRef.value) === null || _treeRef$value === void 0 || _treeRef$value.store.setData(data);\n                });\n                ElMessage({\n                  message: `${type ? '收藏为' : '移除'}常用联系人成功！`,\n                  type: 'success'\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context15.stop();\n          }\n        }, _callee15);\n      }));\n      return function relationBookMemberSetOften(_x15) {\n        return _ref16.apply(this, arguments);\n      };\n    }();\n    var handleRefresh = /*#__PURE__*/function () {\n      var _ref17 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee16() {\n        return _regeneratorRuntime().wrap(function _callee16$(_context16) {\n          while (1) switch (_context16.prev = _context16.next) {\n            case 0:\n              SelectPersonTab(function (data) {\n                var _treeRef$value2;\n                (_treeRef$value2 = treeRef.value) === null || _treeRef$value2 === void 0 || _treeRef$value2.store.setData(data);\n              });\n            case 1:\n            case \"end\":\n              return _context16.stop();\n          }\n        }, _callee16);\n      }));\n      return function handleRefresh() {\n        return _ref17.apply(this, arguments);\n      };\n    }();\n    onMounted(function () {\n      relationBookMemberOftenList();\n    });\n    __expose({\n      refresh: handleRefresh\n    });\n    var __returned__ = {\n      store,\n      emit,\n      isMac,\n      treeRef,\n      keyword,\n      labelAll,\n      userId,\n      userInfo,\n      oftenList,\n      imgUrl,\n      shareUrl,\n      querySearch,\n      handleQuery,\n      handleUserData,\n      handleClick,\n      getUserInfo,\n      globalReadConfig,\n      getUserQrCode,\n      longShortLink,\n      openUser,\n      handleMessages,\n      handleSendMessage,\n      SelectPersonTab,\n      handleTreeData: _handleTreeData,\n      SelectPersonGroup,\n      SelectPersonBookUser,\n      loadNode,\n      relationBookMemberOftenList,\n      handleOftenList,\n      relationBookMemberSetOften,\n      handleRefresh,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      get RongIMLib() {\n        return RongIMLib;\n      },\n      get format() {\n        return format;\n      },\n      get appOnlyHeader() {\n        return appOnlyHeader;\n      },\n      get mobileIcon() {\n        return mobileIcon;\n      },\n      get maleIcon() {\n        return maleIcon;\n      },\n      get femaleIcon() {\n        return femaleIcon;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get Search() {\n        return Search;\n      },\n      get config() {\n        return config;\n      },\n      QrcodeVue,\n      get useStore() {\n        return useStore;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "RongIMLib", "format", "appOnly<PERSON>eader", "mobileIcon", "maleIcon", "femaleIcon", "ElMessage", "Search", "config", "QrcodeVue", "useStore", "__default__", "store", "emit", "__emit", "isMac", "_window$electron", "window", "electron", "treeRef", "keyword", "labelAll", "userId", "userInfo", "oftenList", "imgUrl", "url", "fileURL", "defaultImgURL", "shareUrl", "querySearch", "_ref2", "_callee", "queryString", "cb", "results", "_callee$", "_context", "handleQuery", "t0", "_x", "_x2", "_ref3", "_callee2", "newUserDataAll", "index", "item", "newUserData", "_callee2$", "_context2", "handleUserData", "id", "concat", "_toConsumableArray", "Map", "map", "_ref4", "_callee3", "_yield$api$SelectPers", "data", "_callee3$", "_context3", "SelectPersonBookUser", "isOpen", "labelCode", "nodeId", "relationBookId", "tabCode", "label", "userName", "children", "user", "<PERSON><PERSON><PERSON><PERSON>", "_x3", "handleClick", "getUserInfo", "_ref5", "_callee4", "_yield$api$userInfo", "_callee4$", "_context4", "detailId", "globalReadConfig", "_ref6", "_callee5", "_yield$api$globalRead", "_callee5$", "_context5", "codes", "getUserQrCode", "appShareAddress", "_ref7", "_callee6", "params", "_callee6$", "_context6", "longShortLink", "JSON", "stringify", "replace", "_x4", "_ref8", "_callee7", "_yield$api$longShortL", "_callee7$", "_context7", "encodeURIComponent", "API_URL", "_x5", "openUser", "roleIds", "includes", "commit", "path", "query", "utype", "handleMessages", "_ref9", "_callee8", "conversationType", "targetId", "res", "_callee8$", "_context8", "getConversation", "_x6", "_x7", "handleSendMessage", "_ref10", "_callee9", "_yield$handleMessages", "code", "_data$latestMessage", "_data$latestMessage2", "_data$latestMessage3", "newSendMessage", "_callee9$", "_context9", "accountId", "isTemporary", "isTop", "isNotInform", "notificationStatus", "chatObjectInfo", "uid", "img", "photo", "headImg", "sentTime", "latestMessage", "Date", "parse", "messageType", "content", "count", "unreadMessageCount", "SelectPersonTab", "_ref11", "_callee10", "_yield$api$SelectPers2", "newLabelData", "_data$", "_data$2", "_callee10$", "_context10", "tabCodes", "<PERSON><PERSON><PERSON><PERSON>", "_x8", "handleTreeData", "SelectPersonGroup", "_ref12", "_callee11", "_yield$api$SelectPers3", "_callee11$", "_context11", "_x9", "_ref13", "_callee12", "parentId", "_yield$api$SelectPers4", "_callee12$", "_context12", "_x10", "_x11", "loadNode", "_ref14", "_callee13", "node", "_node$data", "_node$data2", "newTreeData", "newData", "_newUserData", "_newTreeData", "_newUserData2", "_callee13$", "_context13", "level", "parent", "key", "_x12", "_x13", "relationBookMemberOftenList", "_ref15", "_callee14", "_yield$api$relationBo", "_callee14$", "_context14", "_x14", "handleOftenList", "relationBookMemberSetOften", "_ref16", "_callee15", "_yield$api$relationBo2", "_callee15$", "_context15", "isOften", "userIds", "_treeRef$value", "setData", "message", "_x15", "handleRefresh", "_ref17", "_callee16", "_callee16$", "_context16", "_treeRef$value2", "__expose", "refresh"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/components/GlobalChatAddressBook.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatAddressBook\" :class=\"{ GlobalChatMacAddressBook: isMac }\">\r\n    <div class=\"GlobalChatAddressBookList forbidSelect\">\r\n      <div class=\"GlobalChatAddressBookInput\">\r\n        <el-autocomplete v-model=\"keyword\" :prefix-icon=\"Search\" :fetch-suggestions=\"querySearch\" placeholder=\"搜索\"\r\n          popper-class=\"GlobalChatAddressBookAutocomplete\" clearable @select=\"handleClick\">\r\n          <template #default=\"{ item }\">\r\n            <div class=\"GlobalChatAddressBookItem forbidSelect\">\r\n              <el-image :src=\"imgUrl(item.user.photo || item.user.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n              <div class=\"GlobalChatAddressBookName ellipsis\">{{ item.user.userName }}</div>\r\n            </div>\r\n          </template>\r\n        </el-autocomplete>\r\n      </div>\r\n      <el-scrollbar class=\"GlobalChatAddressBookScrollbar\">\r\n        <el-tree ref=\"treeRef\" lazy :load=\"loadNode\" node-key=\"id\" :props=\"{ isLeaf: 'isLeaf' }\">\r\n          <template #default=\"{ data }\">\r\n            <div class=\"GlobalChatAddressBookLabel\" v-if=\"data.type !== 'user'\">{{ data.label }}</div>\r\n            <div :class=\"['GlobalChatAddressBookItem', { 'is-active': data.id === userId }]\" @click=\"handleClick(data)\"\r\n              v-if=\"data.type === 'user'\">\r\n              <el-image :src=\"imgUrl(data.user.photo || data.user.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n              <div class=\"GlobalChatAddressBookName ellipsis\">{{ data.user.userName }}</div>\r\n            </div>\r\n          </template>\r\n        </el-tree>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"GlobalChatAddressBookBody\" v-if=\"userId\">\r\n      <div class=\"GlobalChatAddressBookInfo\">\r\n        <el-image :src=\"imgUrl(userInfo.photo || userInfo.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n        <div class=\"GlobalChatAddressBookInfoBody\">\r\n          <div class=\"GlobalChatAddressBookName ellipsis\" @click=\"openUser(userInfo)\">{{ userInfo.userName }}\r\n            <span v-html=\"maleIcon\" v-if=\"userInfo?.sex?.value === '1'\"></span>\r\n            <span v-html=\"femaleIcon\" v-if=\"userInfo?.sex?.value === '2'\"></span>\r\n            <div class=\"GlobalChatAddressBookIcon\">\r\n              <el-icon @click=\"handleOftenList(1)\" v-if=\"!oftenList?.includes(userId)\">\r\n                <Star />\r\n              </el-icon>\r\n              <el-icon class=\"is-active\" @click=\"handleOftenList(0)\" v-if=\"oftenList?.includes(userId)\">\r\n                <StarFilled />\r\n              </el-icon>\r\n            </div>\r\n          </div>\r\n          <div class=\"GlobalChatAddressBookText ellipsis\">\r\n            <span v-html=\"mobileIcon\"></span>\r\n            {{ userInfo.mobile }}\r\n          </div>\r\n          <div class=\"GlobalChatAddressBookText ellipsis\">{{ userInfo.position }}</div>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"handleSendMessage\">发送消息</el-button>\r\n      </div>\r\n      <el-scrollbar class=\"GlobalChatAddressBookUserScroll\">\r\n        <div class=\"GlobalChatAddressBookUserBody\">\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\">民族：{{ userInfo?.nation?.label }}</div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\">\r\n            出生年月：{{ format(userInfo.birthday, 'YYYY-MM-DD') }}\r\n          </div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\">籍贯：{{ userInfo.nativePlace }}</div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\"\r\n            v-if=\"userInfo.roleIds?.includes('1684119713426243586') || userInfo.roleIds?.includes('1743150705994133506') || userInfo.roleIds?.includes('1643857093725327362')\">\r\n            办公室电话：{{ userInfo.officePhone }}</div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\" v-if=\"userInfo.roleIds?.includes('1640259895343255554')\">\r\n            地址：{{\r\n              userInfo.callAddress }}</div>\r\n          <div class=\"GlobalChatAddressBookUserText ellipsis\" v-if=\"userInfo.roleIds?.includes('1640259895343255554')\">\r\n            个人二维码：</div>\r\n          <div style=\"margin: 10px 0;\" v-if=\"userInfo.roleIds?.includes('1640259895343255554')\">\r\n            <qrcode-vue :value=\"shareUrl\" render-as=\"svg\" level=\"L\" :size=\"90\" />\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"GlobalChatAddressBookDrag\" v-if=\"!userId\"></div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatAddressBook' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { format } from 'common/js/time.js'\r\nimport { appOnlyHeader } from 'common/js/system_var.js'\r\nimport { mobileIcon, maleIcon, femaleIcon } from '../js/icon.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Search } from '@element-plus/icons-vue'\r\nimport config from 'common/config'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport { useStore } from 'vuex'\r\nconst store = useStore()\r\nconst emit = defineEmits(['send'])\r\nconst isMac = window.electron?.isMac\r\nconst treeRef = ref()\r\nconst keyword = ref('')\r\nconst labelAll = ref([])\r\nconst userId = ref('')\r\nconst userInfo = ref({})\r\nconst oftenList = ref([])\r\nconst imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))\r\nconst shareUrl = ref('')\r\nconst querySearch = async (queryString, cb) => {\r\n  const results = queryString ? await handleQuery(queryString) : []\r\n  cb(results)\r\n}\r\nconst handleQuery = async () => {\r\n  let newUserDataAll = []\r\n  for (let index = 0; index < labelAll.value.length; index++) {\r\n    const item = labelAll.value[index]\r\n    const newUserData = await handleUserData(item.id)\r\n    newUserDataAll = [...newUserDataAll, ...newUserData]\r\n  }\r\n  return [...new Map(newUserDataAll.map((item) => [item.id, item])).values()]\r\n}\r\nconst handleUserData = async (id) => {\r\n  try {\r\n    const { data } = await api.SelectPersonBookUser({\r\n      isOpen: 1,\r\n      keyword: keyword.value,\r\n      labelCode: id,\r\n      nodeId: '',\r\n      relationBookId: '',\r\n      tabCode: 'relationBooksTemp'\r\n    })\r\n    const newUserData = []\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      if (item.userId)\r\n        newUserData.push({\r\n          id: item.userId,\r\n          label: item.userName,\r\n          children: [],\r\n          type: 'user',\r\n          user: item,\r\n          isLeaf: true\r\n        })\r\n    }\r\n    return newUserData\r\n  } catch (error) {\r\n    return []\r\n  }\r\n}\r\nconst handleClick = (item) => {\r\n  userId.value = item.id\r\n  getUserInfo(item)\r\n}\r\nconst getUserInfo = async () => {\r\n  const { data } = await api.userInfo({ detailId: userId.value })\r\n  userInfo.value = data\r\n  globalReadConfig()\r\n}\r\n\r\n// 获取个人二维码\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: ['appShareAddress'] })\r\n  getUserQrCode(data.appShareAddress)\r\n}\r\nconst getUserQrCode = async (appShareAddress) => {\r\n  var params = {\r\n    n: 'mo_npcinfo_details',\r\n    u: '../mo_npcinfo_details/mo_npcinfo_details.stml',\r\n    p: {\r\n      id: userId.value\r\n    }\r\n  }\r\n  longShortLink(appShareAddress + 'pages/index/?' + JSON.stringify(params).replace(/\\{/g, \"%7B\").replace(/\\}/g, \"%7D\").replace(/\\u0022/g, \"%22\"))\r\n}\r\nconst longShortLink = async (url) => {\r\n  const { data } = await api.longShortLink(encodeURIComponent(url))\r\n  shareUrl.value = `${config.API_URL}/viewing/${data}`\r\n}\r\n// 跳转个人信息\r\nconst openUser = (item) => {\r\n  if (item.roleIds) {\r\n    if (item.roleIds.includes('1640259895343255554')) {\r\n      store.commit('setOpenRoute', { name: '委员信息详情', path: '/cppccMember/CppccMemberDetails', query: { id: item.id } })\r\n      return\r\n    }\r\n    if (item.roleIds.includes('1684119713426243586') || item.roleIds.includes('1743150705994133506') || item.roleIds.includes('1643857093725327362')) {\r\n      store.commit('setOpenRoute', { name: '用户信息详情', path: '/system/SubmitUser', query: { id: item.id, utype: 1 } })\r\n    }\r\n  }\r\n}\r\n\r\nconst handleMessages = async (conversationType, targetId) => {\r\n  const res = await RongIMLib.getConversation({ conversationType, targetId })\r\n  return res\r\n}\r\nconst handleSendMessage = async () => {\r\n  const targetId = appOnlyHeader.value + userInfo.value.accountId\r\n  const { code, data } = await handleMessages(1, targetId)\r\n  if (!code) {\r\n    let newSendMessage = {\r\n      isTemporary: true,\r\n      isTop: data.isTop,\r\n      isNotInform: data.notificationStatus,\r\n      id: data.targetId,\r\n      targetId: data.targetId,\r\n      type: data.conversationType,\r\n      chatObjectInfo: {\r\n        uid: data.targetId,\r\n        id: userInfo.value.accountId,\r\n        name: userInfo.value.userName,\r\n        img: userInfo.value.photo || userInfo.value.headImg,\r\n        userInfo: {\r\n          userId: userInfo.value.id,\r\n          userName: userInfo.value.userName,\r\n          photo: userInfo.value.photo,\r\n          headImg: userInfo.value.headImg\r\n        }\r\n      },\r\n      sentTime: data.latestMessage?.sentTime || Date.parse(new Date()),\r\n      messageType: data.latestMessage?.messageType || 'RC:TxtMsg',\r\n      content: data.latestMessage?.content || { content: '' },\r\n      count: data.unreadMessageCount\r\n    }\r\n    emit('send', newSendMessage)\r\n  }\r\n}\r\nconst SelectPersonTab = async (resolve) => {\r\n  const { data } = await api.SelectPersonTab({ tabCodes: ['relationBooksTemp'] })\r\n  const newLabelData = []\r\n  for (let index = 0; index < data[0]?.chooseLabels.length; index++) {\r\n    const item = data[0]?.chooseLabels[index]\r\n    newLabelData.push({ id: item.labelCode, label: item.name, children: [], type: 'label', isLeaf: false })\r\n  }\r\n  labelAll.value = newLabelData\r\n  resolve(newLabelData)\r\n}\r\n\r\nconst handleTreeData = (id, data) => {\r\n  const newLabelData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.code !== id) {\r\n      const children = handleTreeData(id, item.children)\r\n      newLabelData.push({ id: item.code, label: item.name, children: children, type: 'tree', isLeaf: false })\r\n    }\r\n  }\r\n  return newLabelData\r\n}\r\nconst SelectPersonGroup = async (id) => {\r\n  const { data } = await api.SelectPersonGroup({ labelCode: id, tabCode: 'relationBooksTemp' })\r\n  return handleTreeData(id, data)\r\n}\r\nconst SelectPersonBookUser = async (parentId, id) => {\r\n  const { data } = await api.SelectPersonBookUser({\r\n    isOpen: 1,\r\n    keyword: '',\r\n    labelCode: parentId,\r\n    nodeId: id,\r\n    relationBookId: id,\r\n    tabCode: 'relationBooksTemp'\r\n  })\r\n  const newUserData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.userId)\r\n      newUserData.push({ id: item.userId, label: item.userName, children: [], type: 'user', user: item, isLeaf: true })\r\n  }\r\n  return newUserData\r\n}\r\nconst loadNode = async (node, resolve) => {\r\n  if (node.level === 0) {\r\n    SelectPersonTab(resolve)\r\n  } else {\r\n    if (node.data?.children?.length) {\r\n      const newTreeData = node.data?.children\r\n      const newUserData = await SelectPersonBookUser(node.parent.key, node.key)\r\n      const newData = [...newTreeData, ...newUserData]\r\n      resolve(newData)\r\n    } else {\r\n      if (node.parent.level) {\r\n        const newUserData = await SelectPersonBookUser(node.parent.key, node.key)\r\n        resolve(newUserData)\r\n      } else {\r\n        const newTreeData = await SelectPersonGroup(node.key)\r\n        const newUserData = await SelectPersonBookUser(node.key, node.key)\r\n        resolve([...newTreeData, ...newUserData])\r\n      }\r\n    }\r\n  }\r\n}\r\nconst relationBookMemberOftenList = async (id) => {\r\n  const { data } = await api.relationBookMemberOftenList()\r\n  oftenList.value = data\r\n}\r\nconst handleOftenList = (type) => {\r\n  relationBookMemberSetOften(type)\r\n}\r\nconst relationBookMemberSetOften = async (type) => {\r\n  const { code } = await api.relationBookMemberSetOften({ isOften: type, userIds: [userId.value] })\r\n  if (code === 200) {\r\n    relationBookMemberOftenList()\r\n    SelectPersonTab((data) => {\r\n      treeRef.value?.store.setData(data)\r\n    })\r\n    ElMessage({ message: `${type ? '收藏为' : '移除'}常用联系人成功！`, type: 'success' })\r\n  }\r\n}\r\nconst handleRefresh = async () => {\r\n  SelectPersonTab((data) => {\r\n    treeRef.value?.store.setData(data)\r\n  })\r\n}\r\nonMounted(() => {\r\n  relationBookMemberOftenList()\r\n})\r\ndefineExpose({ refresh: handleRefresh })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalChatAddressBook {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n\r\n  &.GlobalChatMacAddressBook {\r\n    .GlobalChatAddressBookList {\r\n      .GlobalChatAddressBookInput {\r\n        height: 56px;\r\n      }\r\n\r\n      .GlobalChatAddressBookScrollbar {\r\n        height: calc(100% - 56px);\r\n      }\r\n    }\r\n\r\n    .GlobalChatAddressBookDrag {\r\n      height: 56px;\r\n    }\r\n  }\r\n\r\n  .GlobalChatAddressBookList {\r\n    width: 280px;\r\n    height: 100%;\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalChatAddressBookInput {\r\n      width: 100%;\r\n      height: 66px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n      -webkit-app-region: drag;\r\n\r\n      .zy-el-autocomplete {\r\n        width: 240px;\r\n        height: var(--zy-height-routine);\r\n        -webkit-app-region: no-drag;\r\n\r\n        .zy-el-input {\r\n          width: 240px;\r\n          height: var(--zy-height-routine);\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatAddressBookScrollbar {\r\n      width: 100%;\r\n      height: calc(100% - 66px);\r\n\r\n      .zy-el-tree {\r\n        padding: 0 20px 20px 20px;\r\n\r\n        .zy-el-tree-node {\r\n          .zy-el-tree-node__content {\r\n            height: auto;\r\n            padding: 10px 0;\r\n            position: relative;\r\n            background: transparent;\r\n          }\r\n        }\r\n      }\r\n\r\n      .GlobalChatAddressBookLabel {\r\n        &::after {\r\n          content: '';\r\n          width: 100%;\r\n          border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n          position: absolute;\r\n          right: 0;\r\n          bottom: 0;\r\n        }\r\n      }\r\n\r\n      .GlobalChatAddressBookItem {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        position: relative;\r\n\r\n        &.is-active {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 38px;\r\n          height: 38px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .GlobalChatAddressBookName {\r\n          width: calc(100% - 54px);\r\n          font-size: 14px;\r\n\r\n          &::after {\r\n            content: '';\r\n            width: calc(100% - 54px);\r\n            border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n            position: absolute;\r\n            right: 0;\r\n            bottom: -10px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatAddressBookDrag {\r\n    width: calc(100% - 280px);\r\n    height: 66px;\r\n    position: relative;\r\n    -webkit-app-region: drag;\r\n\r\n    &::before {\r\n      content: '';\r\n      width: 96px;\r\n      height: 28px;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      background: transparent;\r\n      -webkit-app-region: no-drag;\r\n    }\r\n  }\r\n\r\n  .GlobalChatAddressBookBody {\r\n    width: calc(100% - 280px);\r\n    height: 100%;\r\n\r\n    .GlobalChatAddressBookInfo {\r\n      width: 100%;\r\n      height: 180px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 40px;\r\n      position: relative;\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      -webkit-app-region: drag;\r\n\r\n      &::before {\r\n        content: '';\r\n        width: 96px;\r\n        height: 28px;\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        background: transparent;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .zy-el-image {\r\n        width: 78px;\r\n        height: 78px;\r\n        border-radius: 8%;\r\n        overflow: hidden;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .GlobalChatAddressBookInfoBody {\r\n        padding-left: 16px;\r\n        -webkit-app-region: no-drag;\r\n\r\n        .GlobalChatAddressBookName {\r\n          width: 100%;\r\n          display: flex;\r\n          align-items: center;\r\n          font-size: 18px;\r\n          line-height: 26px;\r\n          font-weight: bold;\r\n          padding-bottom: 6px;\r\n\r\n          span {\r\n            width: 20px;\r\n            height: 20px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-left: 12px;\r\n          }\r\n\r\n          .GlobalChatAddressBookIcon {\r\n            height: 20px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding-left: 20px;\r\n\r\n            .zy-el-icon {\r\n              cursor: pointer;\r\n              font-size: 18px;\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n\r\n            .is-active {\r\n              font-size: 20px;\r\n              color: var(--zy-el-color-warning);\r\n            }\r\n          }\r\n        }\r\n\r\n        .GlobalChatAddressBookText {\r\n          width: 100%;\r\n          height: 24px;\r\n          font-size: 14px;\r\n          line-height: 24px;\r\n          padding-top: 2px;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          span {\r\n            width: 20px;\r\n            height: 20px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 6px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-button {\r\n        position: absolute;\r\n        right: var(--zy-distance-two);\r\n        bottom: 12px;\r\n        height: var(--zy-height-secondary);\r\n        -webkit-app-region: no-drag;\r\n      }\r\n    }\r\n\r\n    .GlobalChatAddressBookUserScroll {\r\n      width: 100%;\r\n      height: calc(100% - 180px);\r\n\r\n      .GlobalChatAddressBookUserBody {\r\n        padding: 20px 40px;\r\n\r\n        .GlobalChatAddressBookUserText {\r\n          width: 100%;\r\n          height: 24px;\r\n          font-size: 14px;\r\n          line-height: 24px;\r\n          padding-top: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatAddressBookAutocomplete {\r\n  .GlobalChatAddressBookItem {\r\n    width: 218px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 10px 0;\r\n    position: relative;\r\n\r\n    &.is-active {\r\n      color: var(--zy-el-color-primary);\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 38px;\r\n      height: 38px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .GlobalChatAddressBookName {\r\n      width: calc(100% - 54px);\r\n      font-size: 14px;\r\n      line-height: normal;\r\n\r\n      &::after {\r\n        content: '';\r\n        width: calc(100% - 54px);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n        position: absolute;\r\n        right: 0;\r\n        bottom: -10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;+CAgFA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,QAAQ,KAAK;AACpC,OAAO,KAAKC,SAAS,MAAM,uBAAuB;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AAChE,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,MAAM,MAAM,eAAe;AAClC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,QAAQ,QAAQ,MAAM;AAb/B,IAAAC,WAAA,GAAe;EAAEzC,IAAI,EAAE;AAAwB,CAAC;;;;;;;IAchD,IAAM0C,KAAK,GAAGF,QAAQ,CAAC,CAAC;IACxB,IAAMG,IAAI,GAAGC,MAAqB;IAClC,IAAMC,KAAK,IAAAC,gBAAA,GAAGC,MAAM,CAACC,QAAQ,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBD,KAAK;IACpC,IAAMI,OAAO,GAAGrB,GAAG,CAAC,CAAC;IACrB,IAAMsB,OAAO,GAAGtB,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMuB,QAAQ,GAAGvB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMwB,MAAM,GAAGxB,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMyB,QAAQ,GAAGzB,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAM0B,SAAS,GAAG1B,GAAG,CAAC,EAAE,CAAC;IACzB,IAAM2B,MAAM,GAAG,SAATA,MAAMA,CAAIC,GAAG;MAAA,OAAMA,GAAG,GAAG7B,GAAG,CAAC8B,OAAO,CAACD,GAAG,CAAC,GAAG7B,GAAG,CAAC+B,aAAa,CAAC,uBAAuB,CAAC;IAAA,CAAC;IAC7F,IAAMC,QAAQ,GAAG/B,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMgC,WAAW;MAAA,IAAAC,KAAA,GAAAvC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6D,QAAOC,WAAW,EAAEC,EAAE;QAAA,IAAAC,OAAA;QAAA,OAAApJ,mBAAA,GAAAuB,IAAA,UAAA8H,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAzD,IAAA,GAAAyD,QAAA,CAAApF,IAAA;YAAA;cAAA,KACxBgF,WAAW;gBAAAI,QAAA,CAAApF,IAAA;gBAAA;cAAA;cAAAoF,QAAA,CAAApF,IAAA;cAAA,OAASqF,WAAW,CAACL,WAAW,CAAC;YAAA;cAAAI,QAAA,CAAAE,EAAA,GAAAF,QAAA,CAAA3F,IAAA;cAAA2F,QAAA,CAAApF,IAAA;cAAA;YAAA;cAAAoF,QAAA,CAAAE,EAAA,GAAG,EAAE;YAAA;cAA3DJ,OAAO,GAAAE,QAAA,CAAAE,EAAA;cACbL,EAAE,CAACC,OAAO,CAAC;YAAA;YAAA;cAAA,OAAAE,QAAA,CAAAtD,IAAA;UAAA;QAAA,GAAAiD,OAAA;MAAA,CACZ;MAAA,gBAHKF,WAAWA,CAAAU,EAAA,EAAAC,GAAA;QAAA,OAAAV,KAAA,CAAArC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGhB;IACD,IAAM6C,WAAW;MAAA,IAAAI,KAAA,GAAAlD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwE,SAAA;QAAA,IAAAC,cAAA,EAAAC,KAAA,EAAAC,IAAA,EAAAC,WAAA;QAAA,OAAAhK,mBAAA,GAAAuB,IAAA,UAAA0I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArE,IAAA,GAAAqE,SAAA,CAAAhG,IAAA;YAAA;cACd2F,cAAc,GAAG,EAAE;cACdC,KAAK,GAAG,CAAC;YAAA;cAAA,MAAEA,KAAK,GAAGxB,QAAQ,CAAC5H,KAAK,CAACqE,MAAM;gBAAAmF,SAAA,CAAAhG,IAAA;gBAAA;cAAA;cACzC6F,IAAI,GAAGzB,QAAQ,CAAC5H,KAAK,CAACoJ,KAAK,CAAC;cAAAI,SAAA,CAAAhG,IAAA;cAAA,OACRiG,cAAc,CAACJ,IAAI,CAACK,EAAE,CAAC;YAAA;cAA3CJ,WAAW,GAAAE,SAAA,CAAAvG,IAAA;cACjBkG,cAAc,MAAAQ,MAAA,CAAAC,kBAAA,CAAOT,cAAc,GAAAS,kBAAA,CAAKN,WAAW,EAAC;YAAA;cAHHF,KAAK,EAAE;cAAAI,SAAA,CAAAhG,IAAA;cAAA;YAAA;cAAA,OAAAgG,SAAA,CAAApG,MAAA,WAAAwG,kBAAA,CAK/C,IAAIC,GAAG,CAACV,cAAc,CAACW,GAAG,CAAC,UAACT,IAAI;gBAAA,OAAK,CAACA,IAAI,CAACK,EAAE,EAAEL,IAAI,CAAC;cAAA,EAAC,CAAC,CAACpH,MAAM,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAuH,SAAA,CAAAlE,IAAA;UAAA;QAAA,GAAA4D,QAAA;MAAA,CAC3E;MAAA,gBARKL,WAAWA,CAAA;QAAA,OAAAI,KAAA,CAAAhD,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQhB;IACD,IAAMyD,cAAc;MAAA,IAAAM,KAAA,GAAAhE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsF,SAAON,EAAE;QAAA,IAAAO,qBAAA,EAAAC,IAAA,EAAAZ,WAAA,EAAAF,KAAA,EAAAC,IAAA;QAAA,OAAA/J,mBAAA,GAAAuB,IAAA,UAAAsJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjF,IAAA,GAAAiF,SAAA,CAAA5G,IAAA;YAAA;cAAA4G,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAA5G,IAAA;cAAA,OAEL4C,GAAG,CAACiE,oBAAoB,CAAC;gBAC9CC,MAAM,EAAE,CAAC;gBACT3C,OAAO,EAAEA,OAAO,CAAC3H,KAAK;gBACtBuK,SAAS,EAAEb,EAAE;gBACbc,MAAM,EAAE,EAAE;gBACVC,cAAc,EAAE,EAAE;gBAClBC,OAAO,EAAE;cACX,CAAC,CAAC;YAAA;cAAAT,qBAAA,GAAAG,SAAA,CAAAnH,IAAA;cAPMiH,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAQNZ,WAAW,GAAG,EAAE;cACtB,KAASF,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGc,IAAI,CAAC7F,MAAM,EAAE+E,KAAK,EAAE,EAAE;gBAC1CC,IAAI,GAAGa,IAAI,CAACd,KAAK,CAAC;gBACxB,IAAIC,IAAI,CAACxB,MAAM,EACbyB,WAAW,CAACtF,IAAI,CAAC;kBACf0F,EAAE,EAAEL,IAAI,CAACxB,MAAM;kBACf8C,KAAK,EAAEtB,IAAI,CAACuB,QAAQ;kBACpBC,QAAQ,EAAE,EAAE;kBACZ1J,IAAI,EAAE,MAAM;kBACZ2J,IAAI,EAAEzB,IAAI;kBACV0B,MAAM,EAAE;gBACV,CAAC,CAAC;cACN;cAAC,OAAAX,SAAA,CAAAhH,MAAA,WACMkG,WAAW;YAAA;cAAAc,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAAtB,EAAA,GAAAsB,SAAA;cAAA,OAAAA,SAAA,CAAAhH,MAAA,WAEX,EAAE;YAAA;YAAA;cAAA,OAAAgH,SAAA,CAAA9E,IAAA;UAAA;QAAA,GAAA0E,QAAA;MAAA,CAEZ;MAAA,gBA3BKP,cAAcA,CAAAuB,GAAA;QAAA,OAAAjB,KAAA,CAAA9D,KAAA,OAAAD,SAAA;MAAA;IAAA,GA2BnB;IACD,IAAMiF,WAAW,GAAG,SAAdA,WAAWA,CAAI5B,IAAI,EAAK;MAC5BxB,MAAM,CAAC7H,KAAK,GAAGqJ,IAAI,CAACK,EAAE;MACtBwB,WAAW,CAAC7B,IAAI,CAAC;IACnB,CAAC;IACD,IAAM6B,WAAW;MAAA,IAAAC,KAAA,GAAApF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0G,SAAA;QAAA,IAAAC,mBAAA,EAAAnB,IAAA;QAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAAyK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApG,IAAA,GAAAoG,SAAA,CAAA/H,IAAA;YAAA;cAAA+H,SAAA,CAAA/H,IAAA;cAAA,OACK4C,GAAG,CAAC0B,QAAQ,CAAC;gBAAE0D,QAAQ,EAAE3D,MAAM,CAAC7H;cAAM,CAAC,CAAC;YAAA;cAAAqL,mBAAA,GAAAE,SAAA,CAAAtI,IAAA;cAAvDiH,IAAI,GAAAmB,mBAAA,CAAJnB,IAAI;cACZpC,QAAQ,CAAC9H,KAAK,GAAGkK,IAAI;cACrBuB,gBAAgB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAjG,IAAA;UAAA;QAAA,GAAA8F,QAAA;MAAA,CACnB;MAAA,gBAJKF,WAAWA,CAAA;QAAA,OAAAC,KAAA,CAAAlF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAIhB;;IAED;IACA,IAAMyF,gBAAgB;MAAA,IAAAC,KAAA,GAAA3F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiH,SAAA;QAAA,IAAAC,qBAAA,EAAA1B,IAAA;QAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAAgL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3G,IAAA,GAAA2G,SAAA,CAAAtI,IAAA;YAAA;cAAAsI,SAAA,CAAAtI,IAAA;cAAA,OACA4C,GAAG,CAACqF,gBAAgB,CAAC;gBAAEM,KAAK,EAAE,CAAC,iBAAiB;cAAE,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAA7I,IAAA;cAAnEiH,IAAI,GAAA0B,qBAAA,CAAJ1B,IAAI;cACZ8B,aAAa,CAAC9B,IAAI,CAAC+B,eAAe,CAAC;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAxG,IAAA;UAAA;QAAA,GAAAqG,QAAA;MAAA,CACpC;MAAA,gBAHKF,gBAAgBA,CAAA;QAAA,OAAAC,KAAA,CAAAzF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGrB;IACD,IAAMgG,aAAa;MAAA,IAAAE,KAAA,GAAAnG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAyH,SAAOF,eAAe;QAAA,IAAAG,MAAA;QAAA,OAAA9M,mBAAA,GAAAuB,IAAA,UAAAwL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnH,IAAA,GAAAmH,SAAA,CAAA9I,IAAA;YAAA;cACtC4I,MAAM,GAAG;gBACXxM,CAAC,EAAE,oBAAoB;gBACvBW,CAAC,EAAE,+CAA+C;gBAClDsB,CAAC,EAAE;kBACD6H,EAAE,EAAE7B,MAAM,CAAC7H;gBACb;cACF,CAAC;cACDuM,aAAa,CAACN,eAAe,GAAG,eAAe,GAAGO,IAAI,CAACC,SAAS,CAACL,MAAM,CAAC,CAACM,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAJ,SAAA,CAAAhH,IAAA;UAAA;QAAA,GAAA6G,QAAA;MAAA,CAChJ;MAAA,gBATKH,aAAaA,CAAAW,GAAA;QAAA,OAAAT,KAAA,CAAAjG,KAAA,OAAAD,SAAA;MAAA;IAAA,GASlB;IACD,IAAMuG,aAAa;MAAA,IAAAK,KAAA,GAAA7G,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAmI,SAAO5E,GAAG;QAAA,IAAA6E,qBAAA,EAAA5C,IAAA;QAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAAkM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7H,IAAA,GAAA6H,SAAA,CAAAxJ,IAAA;YAAA;cAAAwJ,SAAA,CAAAxJ,IAAA;cAAA,OACP4C,GAAG,CAACmG,aAAa,CAACU,kBAAkB,CAAChF,GAAG,CAAC,CAAC;YAAA;cAAA6E,qBAAA,GAAAE,SAAA,CAAA/J,IAAA;cAAzDiH,IAAI,GAAA4C,qBAAA,CAAJ5C,IAAI;cACZ9B,QAAQ,CAACpI,KAAK,GAAG,GAAG+G,MAAM,CAACmG,OAAO,YAAYhD,IAAI,EAAE;YAAA;YAAA;cAAA,OAAA8C,SAAA,CAAA1H,IAAA;UAAA;QAAA,GAAAuH,QAAA;MAAA,CACrD;MAAA,gBAHKN,aAAaA,CAAAY,GAAA;QAAA,OAAAP,KAAA,CAAA3G,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGlB;IACD;IACA,IAAMoH,QAAQ,GAAG,SAAXA,QAAQA,CAAI/D,IAAI,EAAK;MACzB,IAAIA,IAAI,CAACgE,OAAO,EAAE;QAChB,IAAIhE,IAAI,CAACgE,OAAO,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;UAChDnG,KAAK,CAACoG,MAAM,CAAC,cAAc,EAAE;YAAE9I,IAAI,EAAE,QAAQ;YAAE+I,IAAI,EAAE,iCAAiC;YAAEC,KAAK,EAAE;cAAE/D,EAAE,EAAEL,IAAI,CAACK;YAAG;UAAE,CAAC,CAAC;UACjH;QACF;QACA,IAAIL,IAAI,CAACgE,OAAO,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IAAIjE,IAAI,CAACgE,OAAO,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IAAIjE,IAAI,CAACgE,OAAO,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;UAChJnG,KAAK,CAACoG,MAAM,CAAC,cAAc,EAAE;YAAE9I,IAAI,EAAE,QAAQ;YAAE+I,IAAI,EAAE,oBAAoB;YAAEC,KAAK,EAAE;cAAE/D,EAAE,EAAEL,IAAI,CAACK,EAAE;cAAEgE,KAAK,EAAE;YAAE;UAAE,CAAC,CAAC;QAChH;MACF;IACF,CAAC;IAED,IAAMC,cAAc;MAAA,IAAAC,KAAA,GAAA7H,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAmJ,SAAOC,gBAAgB,EAAEC,QAAQ;QAAA,IAAAC,GAAA;QAAA,OAAA1O,mBAAA,GAAAuB,IAAA,UAAAoN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/I,IAAA,GAAA+I,SAAA,CAAA1K,IAAA;YAAA;cAAA0K,SAAA,CAAA1K,IAAA;cAAA,OACpC+C,SAAS,CAAC4H,eAAe,CAAC;gBAAEL,gBAAgB;gBAAEC;cAAS,CAAC,CAAC;YAAA;cAArEC,GAAG,GAAAE,SAAA,CAAAjL,IAAA;cAAA,OAAAiL,SAAA,CAAA9K,MAAA,WACF4K,GAAG;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAA5I,IAAA;UAAA;QAAA,GAAAuI,QAAA;MAAA,CACX;MAAA,gBAHKF,cAAcA,CAAAS,GAAA,EAAAC,GAAA;QAAA,OAAAT,KAAA,CAAA3H,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGnB;IACD,IAAMsI,iBAAiB;MAAA,IAAAC,MAAA,GAAAxI,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8J,SAAA;QAAA,IAAAT,QAAA,EAAAU,qBAAA,EAAAC,IAAA,EAAAxE,IAAA,EAAAyE,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,cAAA;QAAA,OAAAxP,mBAAA,GAAAuB,IAAA,UAAAkO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7J,IAAA,GAAA6J,SAAA,CAAAxL,IAAA;YAAA;cAClBuK,QAAQ,GAAGtH,aAAa,CAACzG,KAAK,GAAG8H,QAAQ,CAAC9H,KAAK,CAACiP,SAAS;cAAAD,SAAA,CAAAxL,IAAA;cAAA,OAClCmK,cAAc,CAAC,CAAC,EAAEI,QAAQ,CAAC;YAAA;cAAAU,qBAAA,GAAAO,SAAA,CAAA/L,IAAA;cAAhDyL,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAAExE,IAAI,GAAAuE,qBAAA,CAAJvE,IAAI;cAClB,IAAI,CAACwE,IAAI,EAAE;gBACLI,cAAc,GAAG;kBACnBI,WAAW,EAAE,IAAI;kBACjBC,KAAK,EAAEjF,IAAI,CAACiF,KAAK;kBACjBC,WAAW,EAAElF,IAAI,CAACmF,kBAAkB;kBACpC3F,EAAE,EAAEQ,IAAI,CAAC6D,QAAQ;kBACjBA,QAAQ,EAAE7D,IAAI,CAAC6D,QAAQ;kBACvB5M,IAAI,EAAE+I,IAAI,CAAC4D,gBAAgB;kBAC3BwB,cAAc,EAAE;oBACdC,GAAG,EAAErF,IAAI,CAAC6D,QAAQ;oBAClBrE,EAAE,EAAE5B,QAAQ,CAAC9H,KAAK,CAACiP,SAAS;oBAC5BxK,IAAI,EAAEqD,QAAQ,CAAC9H,KAAK,CAAC4K,QAAQ;oBAC7B4E,GAAG,EAAE1H,QAAQ,CAAC9H,KAAK,CAACyP,KAAK,IAAI3H,QAAQ,CAAC9H,KAAK,CAAC0P,OAAO;oBACnD5H,QAAQ,EAAE;sBACRD,MAAM,EAAEC,QAAQ,CAAC9H,KAAK,CAAC0J,EAAE;sBACzBkB,QAAQ,EAAE9C,QAAQ,CAAC9H,KAAK,CAAC4K,QAAQ;sBACjC6E,KAAK,EAAE3H,QAAQ,CAAC9H,KAAK,CAACyP,KAAK;sBAC3BC,OAAO,EAAE5H,QAAQ,CAAC9H,KAAK,CAAC0P;oBAC1B;kBACF,CAAC;kBACDC,QAAQ,EAAE,EAAAhB,mBAAA,GAAAzE,IAAI,CAAC0F,aAAa,cAAAjB,mBAAA,uBAAlBA,mBAAA,CAAoBgB,QAAQ,KAAIE,IAAI,CAACC,KAAK,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC;kBAChEE,WAAW,EAAE,EAAAnB,oBAAA,GAAA1E,IAAI,CAAC0F,aAAa,cAAAhB,oBAAA,uBAAlBA,oBAAA,CAAoBmB,WAAW,KAAI,WAAW;kBAC3DC,OAAO,EAAE,EAAAnB,oBAAA,GAAA3E,IAAI,CAAC0F,aAAa,cAAAf,oBAAA,uBAAlBA,oBAAA,CAAoBmB,OAAO,KAAI;oBAAEA,OAAO,EAAE;kBAAG,CAAC;kBACvDC,KAAK,EAAE/F,IAAI,CAACgG;gBACd,CAAC;gBACD9I,IAAI,CAAC,MAAM,EAAE0H,cAAc,CAAC;cAC9B;YAAC;YAAA;cAAA,OAAAE,SAAA,CAAA1J,IAAA;UAAA;QAAA,GAAAkJ,QAAA;MAAA,CACF;MAAA,gBA9BKF,iBAAiBA,CAAA;QAAA,OAAAC,MAAA,CAAAtI,KAAA,OAAAD,SAAA;MAAA;IAAA,GA8BtB;IACD,IAAMmK,eAAe;MAAA,IAAAC,MAAA,GAAArK,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2L,UAAO7N,OAAO;QAAA,IAAA8N,sBAAA,EAAApG,IAAA,EAAAqG,YAAA,EAAAnH,KAAA,EAAAoH,MAAA,EAAAC,OAAA,EAAApH,IAAA;QAAA,OAAA/J,mBAAA,GAAAuB,IAAA,UAAA6P,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxL,IAAA,GAAAwL,UAAA,CAAAnN,IAAA;YAAA;cAAAmN,UAAA,CAAAnN,IAAA;cAAA,OACb4C,GAAG,CAAC+J,eAAe,CAAC;gBAAES,QAAQ,EAAE,CAAC,mBAAmB;cAAE,CAAC,CAAC;YAAA;cAAAN,sBAAA,GAAAK,UAAA,CAAA1N,IAAA;cAAvEiH,IAAI,GAAAoG,sBAAA,CAAJpG,IAAI;cACNqG,YAAY,GAAG,EAAE;cACvB,KAASnH,KAAK,GAAG,CAAC,EAAEA,KAAK,KAAAoH,MAAA,GAAGtG,IAAI,CAAC,CAAC,CAAC,cAAAsG,MAAA,uBAAPA,MAAA,CAASK,YAAY,CAACxM,MAAM,GAAE+E,KAAK,EAAE,EAAE;gBAC3DC,IAAI,IAAAoH,OAAA,GAAGvG,IAAI,CAAC,CAAC,CAAC,cAAAuG,OAAA,uBAAPA,OAAA,CAASI,YAAY,CAACzH,KAAK,CAAC;gBACzCmH,YAAY,CAACvM,IAAI,CAAC;kBAAE0F,EAAE,EAAEL,IAAI,CAACkB,SAAS;kBAAEI,KAAK,EAAEtB,IAAI,CAAC5E,IAAI;kBAAEoG,QAAQ,EAAE,EAAE;kBAAE1J,IAAI,EAAE,OAAO;kBAAE4J,MAAM,EAAE;gBAAM,CAAC,CAAC;cACzG;cACAnD,QAAQ,CAAC5H,KAAK,GAAGuQ,YAAY;cAC7B/N,OAAO,CAAC+N,YAAY,CAAC;YAAA;YAAA;cAAA,OAAAI,UAAA,CAAArL,IAAA;UAAA;QAAA,GAAA+K,SAAA;MAAA,CACtB;MAAA,gBATKF,eAAeA,CAAAW,GAAA;QAAA,OAAAV,MAAA,CAAAnK,KAAA,OAAAD,SAAA;MAAA;IAAA,GASpB;IAED,IAAM+K,eAAc,GAAG,SAAjBA,cAAcA,CAAIrH,EAAE,EAAEQ,IAAI,EAAK;MACnC,IAAMqG,YAAY,GAAG,EAAE;MACvB,KAAK,IAAInH,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGc,IAAI,CAAC7F,MAAM,EAAE+E,KAAK,EAAE,EAAE;QAChD,IAAMC,IAAI,GAAGa,IAAI,CAACd,KAAK,CAAC;QACxB,IAAIC,IAAI,CAACqF,IAAI,KAAKhF,EAAE,EAAE;UACpB,IAAMmB,QAAQ,GAAGkG,eAAc,CAACrH,EAAE,EAAEL,IAAI,CAACwB,QAAQ,CAAC;UAClD0F,YAAY,CAACvM,IAAI,CAAC;YAAE0F,EAAE,EAAEL,IAAI,CAACqF,IAAI;YAAE/D,KAAK,EAAEtB,IAAI,CAAC5E,IAAI;YAAEoG,QAAQ,EAAEA,QAAQ;YAAE1J,IAAI,EAAE,MAAM;YAAE4J,MAAM,EAAE;UAAM,CAAC,CAAC;QACzG;MACF;MACA,OAAOwF,YAAY;IACrB,CAAC;IACD,IAAMS,iBAAiB;MAAA,IAAAC,MAAA,GAAAlL,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwM,UAAOxH,EAAE;QAAA,IAAAyH,sBAAA,EAAAjH,IAAA;QAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAAuQ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlM,IAAA,GAAAkM,UAAA,CAAA7N,IAAA;YAAA;cAAA6N,UAAA,CAAA7N,IAAA;cAAA,OACV4C,GAAG,CAAC4K,iBAAiB,CAAC;gBAAEzG,SAAS,EAAEb,EAAE;gBAAEgB,OAAO,EAAE;cAAoB,CAAC,CAAC;YAAA;cAAAyG,sBAAA,GAAAE,UAAA,CAAApO,IAAA;cAArFiH,IAAI,GAAAiH,sBAAA,CAAJjH,IAAI;cAAA,OAAAmH,UAAA,CAAAjO,MAAA,WACL2N,eAAc,CAACrH,EAAE,EAAEQ,IAAI,CAAC;YAAA;YAAA;cAAA,OAAAmH,UAAA,CAAA/L,IAAA;UAAA;QAAA,GAAA4L,SAAA;MAAA,CAChC;MAAA,gBAHKF,iBAAiBA,CAAAM,GAAA;QAAA,OAAAL,MAAA,CAAAhL,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGtB;IACD,IAAMqE,oBAAoB;MAAA,IAAAkH,MAAA,GAAAxL,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8M,UAAOC,QAAQ,EAAE/H,EAAE;QAAA,IAAAgI,sBAAA,EAAAxH,IAAA,EAAAZ,WAAA,EAAAF,KAAA,EAAAC,IAAA;QAAA,OAAA/J,mBAAA,GAAAuB,IAAA,UAAA8Q,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAzM,IAAA,GAAAyM,UAAA,CAAApO,IAAA;YAAA;cAAAoO,UAAA,CAAApO,IAAA;cAAA,OACvB4C,GAAG,CAACiE,oBAAoB,CAAC;gBAC9CC,MAAM,EAAE,CAAC;gBACT3C,OAAO,EAAE,EAAE;gBACX4C,SAAS,EAAEkH,QAAQ;gBACnBjH,MAAM,EAAEd,EAAE;gBACVe,cAAc,EAAEf,EAAE;gBAClBgB,OAAO,EAAE;cACX,CAAC,CAAC;YAAA;cAAAgH,sBAAA,GAAAE,UAAA,CAAA3O,IAAA;cAPMiH,IAAI,GAAAwH,sBAAA,CAAJxH,IAAI;cAQNZ,WAAW,GAAG,EAAE;cACtB,KAASF,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGc,IAAI,CAAC7F,MAAM,EAAE+E,KAAK,EAAE,EAAE;gBAC1CC,IAAI,GAAGa,IAAI,CAACd,KAAK,CAAC;gBACxB,IAAIC,IAAI,CAACxB,MAAM,EACbyB,WAAW,CAACtF,IAAI,CAAC;kBAAE0F,EAAE,EAAEL,IAAI,CAACxB,MAAM;kBAAE8C,KAAK,EAAEtB,IAAI,CAACuB,QAAQ;kBAAEC,QAAQ,EAAE,EAAE;kBAAE1J,IAAI,EAAE,MAAM;kBAAE2J,IAAI,EAAEzB,IAAI;kBAAE0B,MAAM,EAAE;gBAAK,CAAC,CAAC;cACrH;cAAC,OAAA6G,UAAA,CAAAxO,MAAA,WACMkG,WAAW;YAAA;YAAA;cAAA,OAAAsI,UAAA,CAAAtM,IAAA;UAAA;QAAA,GAAAkM,SAAA;MAAA,CACnB;MAAA,gBAhBKnH,oBAAoBA,CAAAwH,IAAA,EAAAC,IAAA;QAAA,OAAAP,MAAA,CAAAtL,KAAA,OAAAD,SAAA;MAAA;IAAA,GAgBzB;IACD,IAAM+L,QAAQ;MAAA,IAAAC,MAAA,GAAAjM,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAuN,UAAOC,IAAI,EAAE1P,OAAO;QAAA,IAAA2P,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAA/I,WAAA,EAAAgJ,OAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,aAAA;QAAA,OAAAnT,mBAAA,GAAAuB,IAAA,UAAA6R,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxN,IAAA,GAAAwN,UAAA,CAAAnP,IAAA;YAAA;cAAA,MAC/B0O,IAAI,CAACU,KAAK,KAAK,CAAC;gBAAAD,UAAA,CAAAnP,IAAA;gBAAA;cAAA;cAClB2M,eAAe,CAAC3N,OAAO,CAAC;cAAAmQ,UAAA,CAAAnP,IAAA;cAAA;YAAA;cAAA,OAAA2O,UAAA,GAEpBD,IAAI,CAAChI,IAAI,cAAAiI,UAAA,gBAAAA,UAAA,GAATA,UAAA,CAAWtH,QAAQ,cAAAsH,UAAA,eAAnBA,UAAA,CAAqB9N,MAAM;gBAAAsO,UAAA,CAAAnP,IAAA;gBAAA;cAAA;cACvB6O,WAAW,IAAAD,WAAA,GAAGF,IAAI,CAAChI,IAAI,cAAAkI,WAAA,uBAATA,WAAA,CAAWvH,QAAQ;cAAA8H,UAAA,CAAAnP,IAAA;cAAA,OACb6G,oBAAoB,CAAC6H,IAAI,CAACW,MAAM,CAACC,GAAG,EAAEZ,IAAI,CAACY,GAAG,CAAC;YAAA;cAAnExJ,WAAW,GAAAqJ,UAAA,CAAA1P,IAAA;cACXqP,OAAO,MAAA3I,MAAA,CAAAC,kBAAA,CAAOyI,WAAW,GAAAzI,kBAAA,CAAKN,WAAW;cAC/C9G,OAAO,CAAC8P,OAAO,CAAC;cAAAK,UAAA,CAAAnP,IAAA;cAAA;YAAA;cAAA,KAEZ0O,IAAI,CAACW,MAAM,CAACD,KAAK;gBAAAD,UAAA,CAAAnP,IAAA;gBAAA;cAAA;cAAAmP,UAAA,CAAAnP,IAAA;cAAA,OACO6G,oBAAoB,CAAC6H,IAAI,CAACW,MAAM,CAACC,GAAG,EAAEZ,IAAI,CAACY,GAAG,CAAC;YAAA;cAAnExJ,YAAW,GAAAqJ,UAAA,CAAA1P,IAAA;cACjBT,OAAO,CAAC8G,YAAW,CAAC;cAAAqJ,UAAA,CAAAnP,IAAA;cAAA;YAAA;cAAAmP,UAAA,CAAAnP,IAAA;cAAA,OAEMwN,iBAAiB,CAACkB,IAAI,CAACY,GAAG,CAAC;YAAA;cAA/CT,YAAW,GAAAM,UAAA,CAAA1P,IAAA;cAAA0P,UAAA,CAAAnP,IAAA;cAAA,OACS6G,oBAAoB,CAAC6H,IAAI,CAACY,GAAG,EAAEZ,IAAI,CAACY,GAAG,CAAC;YAAA;cAA5DxJ,aAAW,GAAAqJ,UAAA,CAAA1P,IAAA;cACjBT,OAAO,IAAAmH,MAAA,CAAAC,kBAAA,CAAKyI,YAAW,GAAAzI,kBAAA,CAAKN,aAAW,EAAC,CAAC;YAAA;YAAA;cAAA,OAAAqJ,UAAA,CAAArN,IAAA;UAAA;QAAA,GAAA2M,SAAA;MAAA,CAIhD;MAAA,gBApBKF,QAAQA,CAAAgB,IAAA,EAAAC,IAAA;QAAA,OAAAhB,MAAA,CAAA/L,KAAA,OAAAD,SAAA;MAAA;IAAA,GAoBb;IACD,IAAMiN,2BAA2B;MAAA,IAAAC,MAAA,GAAAnN,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAyO,UAAOzJ,EAAE;QAAA,IAAA0J,qBAAA,EAAAlJ,IAAA;QAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAAwS,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnO,IAAA,GAAAmO,UAAA,CAAA9P,IAAA;YAAA;cAAA8P,UAAA,CAAA9P,IAAA;cAAA,OACpB4C,GAAG,CAAC6M,2BAA2B,CAAC,CAAC;YAAA;cAAAG,qBAAA,GAAAE,UAAA,CAAArQ,IAAA;cAAhDiH,IAAI,GAAAkJ,qBAAA,CAAJlJ,IAAI;cACZnC,SAAS,CAAC/H,KAAK,GAAGkK,IAAI;YAAA;YAAA;cAAA,OAAAoJ,UAAA,CAAAhO,IAAA;UAAA;QAAA,GAAA6N,SAAA;MAAA,CACvB;MAAA,gBAHKF,2BAA2BA,CAAAM,IAAA;QAAA,OAAAL,MAAA,CAAAjN,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGhC;IACD,IAAMwN,eAAe,GAAG,SAAlBA,eAAeA,CAAIrS,IAAI,EAAK;MAChCsS,0BAA0B,CAACtS,IAAI,CAAC;IAClC,CAAC;IACD,IAAMsS,0BAA0B;MAAA,IAAAC,MAAA,GAAA3N,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiP,UAAOxS,IAAI;QAAA,IAAAyS,sBAAA,EAAAlF,IAAA;QAAA,OAAApP,mBAAA,GAAAuB,IAAA,UAAAgT,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3O,IAAA,GAAA2O,UAAA,CAAAtQ,IAAA;YAAA;cAAAsQ,UAAA,CAAAtQ,IAAA;cAAA,OACrB4C,GAAG,CAACqN,0BAA0B,CAAC;gBAAEM,OAAO,EAAE5S,IAAI;gBAAE6S,OAAO,EAAE,CAACnM,MAAM,CAAC7H,KAAK;cAAE,CAAC,CAAC;YAAA;cAAA4T,sBAAA,GAAAE,UAAA,CAAA7Q,IAAA;cAAzFyL,IAAI,GAAAkF,sBAAA,CAAJlF,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBuE,2BAA2B,CAAC,CAAC;gBAC7B9C,eAAe,CAAC,UAACjG,IAAI,EAAK;kBAAA,IAAA+J,cAAA;kBACxB,CAAAA,cAAA,GAAAvM,OAAO,CAAC1H,KAAK,cAAAiU,cAAA,eAAbA,cAAA,CAAe9M,KAAK,CAAC+M,OAAO,CAAChK,IAAI,CAAC;gBACpC,CAAC,CAAC;gBACFrD,SAAS,CAAC;kBAAEsN,OAAO,EAAE,GAAGhT,IAAI,GAAG,KAAK,GAAG,IAAI,UAAU;kBAAEA,IAAI,EAAE;gBAAU,CAAC,CAAC;cAC3E;YAAC;YAAA;cAAA,OAAA2S,UAAA,CAAAxO,IAAA;UAAA;QAAA,GAAAqO,SAAA;MAAA,CACF;MAAA,gBATKF,0BAA0BA,CAAAW,IAAA;QAAA,OAAAV,MAAA,CAAAzN,KAAA,OAAAD,SAAA;MAAA;IAAA,GAS/B;IACD,IAAMqO,aAAa;MAAA,IAAAC,MAAA,GAAAvO,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6P,UAAA;QAAA,OAAAjV,mBAAA,GAAAuB,IAAA,UAAA2T,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtP,IAAA,GAAAsP,UAAA,CAAAjR,IAAA;YAAA;cACpB2M,eAAe,CAAC,UAACjG,IAAI,EAAK;gBAAA,IAAAwK,eAAA;gBACxB,CAAAA,eAAA,GAAAhN,OAAO,CAAC1H,KAAK,cAAA0U,eAAA,eAAbA,eAAA,CAAevN,KAAK,CAAC+M,OAAO,CAAChK,IAAI,CAAC;cACpC,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAuK,UAAA,CAAAnP,IAAA;UAAA;QAAA,GAAAiP,SAAA;MAAA,CACH;MAAA,gBAJKF,aAAaA,CAAA;QAAA,OAAAC,MAAA,CAAArO,KAAA,OAAAD,SAAA;MAAA;IAAA,GAIlB;IACDM,SAAS,CAAC,YAAM;MACd2M,2BAA2B,CAAC,CAAC;IAC/B,CAAC,CAAC;IACF0B,QAAY,CAAC;MAAEC,OAAO,EAAEP;IAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}