// 导入封装的方法
import HTTP from 'common/http'
import GlobalApi from 'common/http/GlobalApi'
const api = {
  ...GlobalApi,
  userBatch (params) { // 用户批量操作
    return HTTP.json('/user/batch', params)
  },
  consultUserList (params) { // 相关单位管理员列表
    return HTTP.json('/consultUser/list', params)
  },
  consultUserInfo (params) { // 相关单位管理员详情
    return HTTP.json('/consultUser/info', params)
  },
  consultUserDel (params) { // 相关单位管理员删除
    return HTTP.json('/consultUser/dels', params)
  },
  consultCompanyList (params) { // 相关单位管理列表
    return HTTP.json('/consultCompany/list', params)
  },
  consultCompanyInfo (params) { // 相关单位管理详情
    return HTTP.json('/consultCompany/info', params)
  },
  consultCompanyDel (params) { // 相关单位管理删除
    return HTTP.json('/consultCompany/dels', params)
  },
  consultCompanyUsing (params) { // 相关单位管理启用
    return HTTP.json('/consultCompany/using', params)
  },
  consultCompanyUnUsing (params) { // 相关单位管理禁用
    return HTTP.json('/consultCompany/unUsing', params)
  },
  consultCompanySelect (params) { // 相关单位管理下拉选
    return HTTP.json('/consultCompany/select', params)
  },
  consultShapeList (params) { // 协商形式列表
    return HTTP.json('/consultShape/list', params)
  },
  consultShapeInfo (params) { // 协商形式详情
    return HTTP.json('/consultShape/info', params)
  },
  consultShapeDel (params) { // 协商形式删除
    return HTTP.json('/consultShape/dels', params)
  },
  consultShapeSelect (params) { // 协商形式下拉选
    return HTTP.json('/consultShape/select', params)
  },
  consultPlanLoadFormData (params) { // 协商形式下拉选单位动态表头
    return HTTP.json('/consultPlan/loadFormData', params)
  },
  consultPlanList (params) { // 协商计划列表
    return HTTP.json('/consultPlan/list', params)
  },
  consultPlanInfo (params) { // 协商计划详情
    return HTTP.json('/consultPlan/info', params)
  },
  consultPlanDel (params) { // 协商计划删除
    return HTTP.json('/consultPlan/dels', params)
  },
  consultPlanSelect (params) { // 协商计划下拉选
    return HTTP.json('/consultPlan/select', params)
  },
  consultActivityList (params) { // 协商活动列表
    return HTTP.json('/consultActivity/list', params)
  },
  consultActivityInfo (params) { // 协商活动详情
    return HTTP.json('/consultActivity/info', params)
  },
  consultActivityDel (params) { // 协商活动删除
    return HTTP.json('/consultActivity/dels', params)
  },
  consultActivityVariableList (params) { // 协商活动流程节点列表
    return HTTP.json('/consultActivityVariable/list', params)
  },
  consultActivityVariableInfo (params) { // 协商活动流程节点详情
    return HTTP.json('/consultActivityVariable/info', params)
  },
  consultActivityVariableDel (params) { // 协商活动流程节点删除
    return HTTP.json('/consultActivityVariable/dels', params)
  },
  consultRegulationBannerTree (params) { // 协商制度栏目列表
    return HTTP.json('/consultRegulationBanner/tree', params)
  },
  consultRegulationBannerInfo (params) { // 协商制度栏目详情
    return HTTP.json('/consultRegulationBanner/info', params)
  },
  consultRegulationBannerDel (params) { // 协商制度栏目删除
    return HTTP.json('/consultRegulationBanner/dels', params)
  },
  consultActivityConferenceList (params) { // 远程协商列表
    return HTTP.json('/consultActivityConference/list', params)
  },
  consultActivityConferenceInfo (params) { // 远程协商详情
    return HTTP.json('/consultActivityConference/info', params)
  },
  consultActivityConferenceDel (params) { // 远程协商删除
    return HTTP.json('/consultActivityConference/dels', params)
  },
  consultActivityConferenceUploadList (params) { // 远程协商回放列表
    return HTTP.json('/consultActivityConference/uploadList', params)
  },
  consultActivityConferenceUpload (params) { // 远程协商回放新增
    return HTTP.json('/consultActivityConference/upload', params)
  },
  consultActivityConferenceDelUpload (params) { // 远程协商回放删除
    return HTTP.json('/consultActivityConference/delUpload', params)
  },
  consultRegulationList (params) { // 协商制度列表
    return HTTP.json('/consultRegulation/list', params)
  },
  consultRegulationInfo (params) { // 协商制度详情
    return HTTP.json('/consultRegulation/info', params)
  },
  consultRegulationDel (params) { // 协商制度删除
    return HTTP.json('/consultRegulation/dels', params)
  },
  opinioncollectList (params) { // 会议发言所有列表
    return HTTP.json('/conferenceSpeaking/list', params)
  },
  opinioncollectManageList (params) { // 会议发言管理列表
    return HTTP.json('/conferenceSpeaking/managelist', params)
  },
  opinioncollectAdd (params) { // 新增会议发言
    return HTTP.json('/conferenceSpeaking/add', params)
  },
  opinioncollectDels (params) { // 删除会议发言
    return HTTP.json('/conferenceSpeaking/dels', params)
  },
  opinioncollectEdit (params) { // 修改会议发言
    return HTTP.json('/conferenceSpeaking/edit', params)
  },
  opinioncollectInfo (params) { // 会议发言详情
    return HTTP.json('/conferenceSpeaking/info', params)
  },
  opinioncollectPublish (params) { // 会议发言发布
    return HTTP.json('/conferenceSpeaking/publish', params)
  },
  opinioncollectIsTop (params) { // 会议发言置顶
    return HTTP.json('/conferenceSpeaking/istop', params)
  },
  commentAdd (params) { // 会议发言提报
    return HTTP.json('/comment/add', params)
  },
  commentCheckPass (params) { // 评论审核通过
    return HTTP.json('/comment/check/pass', params)
  },
  commentCheckNoPass (params) {// 评论审核不通过
    return HTTP.json('/comment/check/nopass', params)
  },
  opinioncollectUseroffice (params) { // 查询本用户下的发布单位
    return HTTP.json('/conferenceSpeaking/useroffice', params)
  },
  activityTypeSelect (params) { // 活动类型下拉选
    return HTTP.json('/activitytype/selecttree', params)
  },
  activityList (params) { // 活动列表
    return HTTP.json('/servantactivity/list', params)
  },
  activityInfo (params) { // 活动详情
    return HTTP.json('/servantactivity/info', params)
  },
  activityDel (params) { // 活动删除
    return HTTP.json('/servantactivity/dels', params)
  },
  activitydocList (params) { // 活动材料列表
    return HTTP.json('/activitydoc/managelist', params)
  },
  activitydocOpenList (params) { // 活动材料公开列表
    return HTTP.json('/activitydoc/list', params)
  },
  activitydocInfo (params) { // 活动材料详情
    return HTTP.json('/activitydoc/info', params)
  },
  activitydocDel (params) { // 活动材料删除
    return HTTP.json('/activitydoc/dels', params)
  },
  activitydocIsPublic (params) { // 活动材料公开
    return HTTP.json('/activitydoc/ispublic', params)
  },
  userJoin (params) { // 推荐用户
    return HTTP.json('/common/activityRecommendation', params, { noErrorTip: true })
  },
  videoConnectionList (params) { // 视频会议列表
    return HTTP.json('/videoConnection/list', params)
  },
  videoConnectionInfo (params) { // 视频会议详情
    return HTTP.json('/videoConnection/info', params)
  },
  videoConnectionDel (params) { // 视频会议删除
    return HTTP.json('/videoConnection/dels', params)
  },
  networkDiscussionsList (params) { // 网络议政管理列表
    return HTTP.json('/opinioncollect/managelist', params)
  },
  networkDiscussionsAdd (params) { // 新增网络议政
    return HTTP.json('/opinioncollect/add', params)
  },
  networkDiscussionsDels (params) { // 删除网络议政
    return HTTP.json('/opinioncollect/dels', params)
  },
  networkDiscussionsEdit (params) { // 修改网络议政
    return HTTP.json('/opinioncollect/edit', params)
  },
  networkDiscussionsInfo (params) { // 网络议政详情
    return HTTP.json('/opinioncollect/info', params)
  },
  networkDiscussionsPublish (params) { // 网络议政发布
    return HTTP.json('/opinioncollect/publish', params)
  },
  networkDiscussionsIsTop (params) { // 网络议政置顶
    return HTTP.json('/opinioncollect/istop', params)
  },
  networkDiscussionsUseroffice (params) { // 查询本用户下的发布单位
    return HTTP.json('/opinioncollect/useroffice', params)
  },
  longShortLinkExchange (url) { // 长链接转短链接
    return HTTP.get(`/longShortLink/exchange?longUrl=${url}`)
  },
  feedbackList (params) { // 反馈记录列表
    return HTTP.json('/feedback/list', params)
  },
  feedbackAdd (params) { // 新增反馈记录
    return HTTP.json('/feedback/add', params)
  },
  feedbackInfo (params) { // 反馈记录详情
    return HTTP.json('/feedback/info', params)
  },
  feedbackDels (params) { // 删除反馈记录
    return HTTP.json('/feedback/dels', params)
  },
  feedbackEdit (params) { // 编辑反馈记录
    return HTTP.json('/feedback/edit', params)
  },
  studypaperList (params) { // 问卷调查列表
    return HTTP.json('studypaper/managelist', params)
  },
  studypaperInfo (params) { // 问卷调查详情
    return HTTP.json('/studypaper/info', params)
  },
  studypaperDel (params) { // 问卷调查删除
    return HTTP.json('/studypaper/dels', params)
  },
  studypaperDetailquestion (params) { // 结果统计
    return HTTP.json('/studypaper/detailquestion', params)
  },
  studypaperRevoke (params) { // 问卷调查撤回
    return HTTP.json('/studypaper/revoke', params)
  },
  studyexamineList (params) { // 参与情况
    return HTTP.json('/studyexamine/list', params)
  },
  studypaperDetail (params) { // 考试详情
    return HTTP.json('/studypaper/detail', params)
  },
  studyexamineResult (params) { // 考试结果
    return HTTP.json('/studyexamine/result', params)
  },
  // studypaperExampaper (params) { // 考试详情
  //   return HTTP.json('/studypaper/exampaper', params)
  // },

  // studypaperPublish (params) { // 考试发布
  //   return HTTP.json('/studypaper/publish', params)
  // },

  // studypaperTotal (params) { // 考试统计
  //   return HTTP.json('/studypaper/total', params)
  // },
  studytypeList (params) { // 题库类型列表
    return HTTP.json('/studytype/list', params)
  },
  studytypeInfo (params) { // 题库类型详情
    return HTTP.json('/studytype/info', params)
  },
  studytypeDel (params) { // 题库类型删除
    return HTTP.json('/studytype/dels', params)
  },
  studytopicList (params) { // 题库列表
    return HTTP.json('/studytopic/list', params)
  },
  studytopicInfo (params) { // 题库详情
    return HTTP.json('/studytopic/info', params)
  },
  studytopicDel (params) { // 题库删除
    return HTTP.json('/studytopic/dels', params)
  },
  studytopicMove (params) { // 题库移动
    return HTTP.json('/studytopic/move', params)
  },
  studytopicType (params) { // 题库题型字典
    return HTTP.json('/studytopic/type', params)
  },
  studypapertopicList (params) { // 题库导入列表查询
    return HTTP.json('/studypapertopic/list', params)
  },
  studypapertopicInfo (params) { // 题库导入详情
    return HTTP.json('/studypapertopic/info', params)
  },
  studypapertopicImport (params) { // 题库导入生成新id
    return HTTP.json('/studypapertopic/import', params)
  },
  studypapertopicDel (params) { // 题库导入删除
    return HTTP.json('/studypapertopic/dels', params)
  },
  studypapertopicMytopics (params) { // 任务发布
    return HTTP.json('/studypapertopic/mytopics', params)
  },
  microAdviceMyListCount (params) { // 我的成果统计
    return HTTP.json('/microAdvice/myListCount', params)
  },
  microAdviceInfo (params) { // 成果详情
    return HTTP.json('/microAdvice/info', params)
  },
  microAdviceMyList (params) { // 我的成果管理列表
    return HTTP.json('/microAdvice/myList', params)
  },
  microAdviceDels (params) { // 成果删除
    return HTTP.json('/microAdvice/dels', params)
  },
  microAdviceList (params) { // 成果管理列表
    return HTTP.json('/microAdvice/list', params)
  },
  microAdviceGroupList (params) { // 成果办理列表
    return HTTP.json('/microAdvice/groupList', params)
  },
  microAdviceGroupListCount (params) { // 成果办理统计
    return HTTP.json('/microAdvice/groupListCount', params)
  },
  complete (params) { // 协商成果审核
    return HTTP.json('/microadviceflow/complete', params)
  },
  microAdviceGroupSelector (params) { // 办理单位
    return HTTP.json('/microAdvice/group/selector', params)
  },
  microAdviceUnderAreaSelector (params) { // 下级地区
    return HTTP.json('/microAdvice/underArea/selector', params)
  },
  manageJointly (params) { // 协办
    return HTTP.json('/microAdvice/manageJointly', params)
  },
  microFlowRecordEdit (params) { // 回复编辑
    return HTTP.json('/microFlowRecord/edit', params)
  },
  batchToAudit (params) { // 微建议一件还原待审核
    return HTTP.json('/microAdvice/batchToAudit', params)
  },
  batchToUnAnswer (params) { // 微建议一件还原未回复
    return HTTP.json('/microAdvice/batchToUnAnswer', params)
  },
  microFlowRecordComplete (params) { // 签收、拒签
    return HTTP.json('/microAdvice/complete', params)
  },
}
export default api
