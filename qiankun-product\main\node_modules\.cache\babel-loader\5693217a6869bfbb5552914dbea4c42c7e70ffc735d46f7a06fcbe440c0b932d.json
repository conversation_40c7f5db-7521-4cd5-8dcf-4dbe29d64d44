{"ast": null, "code": "import { ref, onMounted, computed, defineAsyncComponent } from 'vue';\nimport { systemLogo, systemName, platformAreaName, loginNameLineFeedPosition, appDownloadUrl, systemLoginContact } from 'common/js/system_var.js';\nimport { LoginView } from '../LoginView/LoginView.js';\nvar __default__ = {\n  name: 'UnifyLogin'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var QrcodeVue = defineAsyncComponent(function () {\n      return import('qrcode.vue');\n    });\n    var ResetPassword = defineAsyncComponent(function () {\n      return import('../LoginView/component/ResetPassword.vue');\n    });\n    var show = ref(false);\n    var loginSystemName = computed(function () {\n      var name = (platformAreaName.value || '') + systemName.value;\n      var num = Number(loginNameLineFeedPosition.value || '0') || 0;\n      return num ? name.substring(0, num) + '\\n' + name.substring(num) : name;\n    });\n    var _LoginView = LoginView('/UnifyLogin'),\n      loginVerifyShow = _LoginView.loginVerifyShow,\n      whetherVerifyCode = _LoginView.whetherVerifyCode,\n      loginDisabled = _LoginView.loginDisabled,\n      loading = _LoginView.loading,\n      checked = _LoginView.checked,\n      imgList = _LoginView.imgList,\n      LoginForm = _LoginView.LoginForm,\n      form = _LoginView.form,\n      rules = _LoginView.rules,\n      countDownText = _LoginView.countDownText,\n      slideVerify = _LoginView.slideVerify,\n      disabled = _LoginView.disabled,\n      loginQrcode = _LoginView.loginQrcode,\n      loginQrcodeShow = _LoginView.loginQrcodeShow,\n      handleBlur = _LoginView.handleBlur,\n      handleGetVerifyCode = _LoginView.handleGetVerifyCode,\n      onAgain = _LoginView.onAgain,\n      onSuccess = _LoginView.onSuccess,\n      globalData = _LoginView.globalData,\n      submitForm = _LoginView.submitForm,\n      loginInfo = _LoginView.loginInfo,\n      refresh = _LoginView.refresh,\n      hideQrcode = _LoginView.hideQrcode;\n    onMounted(function () {\n      loginInfo();\n      globalData();\n    });\n    var __returned__ = {\n      QrcodeVue,\n      ResetPassword,\n      show,\n      loginSystemName,\n      loginVerifyShow,\n      whetherVerifyCode,\n      loginDisabled,\n      loading,\n      checked,\n      imgList,\n      LoginForm,\n      form,\n      rules,\n      countDownText,\n      slideVerify,\n      disabled,\n      loginQrcode,\n      loginQrcodeShow,\n      handleBlur,\n      handleGetVerifyCode,\n      onAgain,\n      onSuccess,\n      globalData,\n      submitForm,\n      loginInfo,\n      refresh,\n      hideQrcode,\n      ref,\n      onMounted,\n      computed,\n      defineAsyncComponent,\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get platformAreaName() {\n        return platformAreaName;\n      },\n      get loginNameLineFeedPosition() {\n        return loginNameLineFeedPosition;\n      },\n      get appDownloadUrl() {\n        return appDownloadUrl;\n      },\n      get systemLoginContact() {\n        return systemLoginContact;\n      },\n      get LoginView() {\n        return LoginView;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "defineAsyncComponent", "systemLogo", "systemName", "platformAreaName", "loginNameLineFeedPosition", "appDownloadUrl", "systemLoginContact", "<PERSON><PERSON><PERSON>ie<PERSON>", "__default__", "name", "QrcodeVue", "ResetPassword", "show", "loginSystemName", "value", "num", "Number", "substring", "_<PERSON><PERSON><PERSON>ie<PERSON>", "loginVerifyShow", "whetherVerifyCode", "loginDisabled", "loading", "checked", "imgList", "LoginForm", "form", "rules", "countDownText", "slideVerify", "disabled", "loginQrcode", "loginQrcodeShow", "handleBlur", "handleGetVerifyCode", "onAgain", "onSuccess", "globalData", "submitForm", "loginInfo", "refresh", "hideQrcode"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/UnifyLogin/UnifyLogin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"UnifyLogin\">\r\n    <el-carousel class=\"UnifyLoginCarousel\" v-if=\"imgList.length\" height=\"100%\">\r\n      <el-carousel-item class=\"UnifyLoginCarouselBox\" v-for=\"item in imgList\" :key=\"item.id\">\r\n        <el-image :src=\"item.imgPath\" loading=\"lazy\" fit=\"cover\" />\r\n      </el-carousel-item>\r\n    </el-carousel>\r\n    <div class=\"UnifyLoginBox\">\r\n      <div class=\"UnifyLoginLogo\">\r\n        <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n      </div>\r\n      <div class=\"UnifyLoginName\" v-html=\"loginSystemName\"></div>\r\n      <el-form ref=\"LoginForm\" :model=\"form\" :rules=\"rules\" class=\"UnifyLoginForm\">\r\n        <el-form-item prop=\"account\">\r\n          <el-input v-model=\"form.account\" placeholder=\"账号/手机号\" @blur=\"handleBlur\" clearable />\r\n        </el-form-item>\r\n        <el-form-item prop=\"password\">\r\n          <el-input type=\"password\" v-model=\"form.password\" placeholder=\"密码\" show-password clearable />\r\n        </el-form-item>\r\n        <el-form-item class=\"smsValidation\" v-if=\"loginVerifyShow && whetherVerifyCode\" prop=\"verifyCode\">\r\n          <el-input v-model=\"form.verifyCode\" placeholder=\"短信验证码\" clearable></el-input>\r\n          <el-button type=\"primary\" @click=\"handleGetVerifyCode\" :disabled=\"countDownText != '获取验证码'\">\r\n            {{ countDownText }}\r\n          </el-button>\r\n        </el-form-item>\r\n        <div class=\"UnifyLoginSlideVerify\" v-if=\"loginVerifyShow && !whetherVerifyCode\">\r\n          <xyl-slide-verify ref=\"slideVerify\" @again=\"onAgain\" @success=\"onSuccess\" :disabled=\"disabled\" />\r\n        </div>\r\n        <div class=\"UnifyLoginFormOperation\">\r\n          <el-checkbox v-model=\"checked\">记住用户名和密码</el-checkbox>\r\n          <div class=\"UnifyLoginFormOperationText\" @click=\"show = !show\">忘记密码？</div>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"submitForm(LoginForm)\" class=\"UnifyLoginFormButton\" :loading=\"loading\"\r\n          :disabled=\"loginDisabled\">\r\n          {{ loading ? '登录中' : '登录' }}\r\n        </el-button>\r\n      </el-form>\r\n      <div class=\"UnifyLoginOperation\" v-if=\"appDownloadUrl\">\r\n        <div class=\"UnifyLoginOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\" @show=\"refresh\" @hide=\"hideQrcode\">\r\n            <div class=\"UnifyLoginQrCodeBox\">\r\n              <div class=\"UnifyLoginQrCodeNameBody\">\r\n                <div class=\"UnifyLoginQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"UnifyLoginQrCodeName\">APP扫码登录</div>\r\n              </div>\r\n              <div class=\"UnifyLoginQrCodeRefreshBody\">\r\n                <qrcode-vue :value=\"loginQrcode\" :size=\"120\" />\r\n                <div class=\"UnifyLoginQrCodeRefresh\" v-show=\"loginQrcodeShow\">\r\n                  <el-button type=\"primary\" @click=\"refresh\">刷新</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"UnifyLoginQrCodeText\">请使用{{ systemName }}APP扫码登录</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"UnifyLoginQrCode\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"UnifyLoginOperationText\">APP扫码登录</div>\r\n        </div>\r\n        <div class=\"UnifyLoginOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\">\r\n            <div class=\"UnifyLoginQrCodeBox\">\r\n              <div class=\"UnifyLoginQrCodeNameBody\">\r\n                <div class=\"UnifyLoginQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"UnifyLoginQrCodeName\">手机APP下载</div>\r\n              </div>\r\n              <qrcode-vue :value=\"appDownloadUrl\" :size=\"120\" />\r\n              <div class=\"UnifyLoginQrCodeText\">使用其他软件扫码下载{{ systemName }}APP</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"UnifyLoginApp\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"UnifyLoginOperationText\">手机APP下载</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"UnifyLoginSystemTips\" v-if=\"systemLoginContact\">{{ systemLoginContact }}</div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"重置密码\">\r\n      <ResetPassword @callback=\"show = !show\"></ResetPassword>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'UnifyLogin' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted, computed, defineAsyncComponent } from 'vue'\r\nimport {\r\n  systemLogo,\r\n  systemName,\r\n  platformAreaName,\r\n  loginNameLineFeedPosition,\r\n  appDownloadUrl,\r\n  systemLoginContact\r\n} from 'common/js/system_var.js'\r\nimport { LoginView } from '../LoginView/LoginView.js'\r\nconst QrcodeVue = defineAsyncComponent(() => import('qrcode.vue'))\r\nconst ResetPassword = defineAsyncComponent(() => import('../LoginView/component/ResetPassword.vue'))\r\nconst show = ref(false)\r\nconst loginSystemName = computed(() => {\r\n  const name = (platformAreaName.value || '') + systemName.value\r\n  const num = Number(loginNameLineFeedPosition.value || '0') || 0\r\n  return num ? name.substring(0, num) + '\\n' + name.substring(num) : name\r\n})\r\nconst {\r\n  loginVerifyShow,\r\n  whetherVerifyCode,\r\n  loginDisabled,\r\n  loading,\r\n  checked,\r\n  imgList,\r\n  LoginForm,\r\n  form,\r\n  rules,\r\n  countDownText,\r\n  slideVerify,\r\n  disabled,\r\n  loginQrcode,\r\n  loginQrcodeShow,\r\n  handleBlur,\r\n  handleGetVerifyCode,\r\n  onAgain,\r\n  onSuccess,\r\n  globalData,\r\n  submitForm,\r\n  loginInfo,\r\n  refresh,\r\n  hideQrcode\r\n} = LoginView('/UnifyLogin')\r\nonMounted(() => {\r\n  loginInfo()\r\n  globalData()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.UnifyLogin {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n\r\n  .UnifyLoginCarousel {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n\r\n    .UnifyLoginCarouselBox {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .zy-el-carousel__indicators--horizontal {\r\n      .zy-el-carousel__button {\r\n        width: 16px;\r\n        height: 16px;\r\n        border-radius: 50%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .UnifyLoginBox {\r\n    padding: var(--zy-distance-one);\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    padding-bottom: var(--zy-distance-two);\r\n    border-radius: var(--el-border-radius-base);\r\n    margin-right: 80px;\r\n    background: #fff;\r\n    position: relative;\r\n    z-index: 2;\r\n\r\n    .UnifyLoginLogo {\r\n      width: 60px;\r\n      margin: auto;\r\n      margin-bottom: var(--zy-distance-two);\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .UnifyLoginName {\r\n      width: 320px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-align: center;\r\n      font-size: var(--zy-system-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      letter-spacing: 2px;\r\n      padding-bottom: var(--zy-distance-one);\r\n      white-space: pre-wrap;\r\n      margin: auto;\r\n    }\r\n\r\n    .UnifyLoginForm {\r\n      width: 320px;\r\n      margin: auto;\r\n      padding-bottom: var(--zy-distance-one);\r\n\r\n      input:-webkit-autofill {\r\n        transition: background-color 5000s ease-in-out 0s;\r\n      }\r\n\r\n      .zy-el-form-item {\r\n        margin-bottom: var(--zy-form-distance-bottom);\r\n      }\r\n\r\n      .UnifyLoginFormButton {\r\n        width: 100%;\r\n      }\r\n\r\n      .smsValidation {\r\n        .zy-el-form-item__content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n        }\r\n\r\n        .zy-el-input {\r\n          width: 56%;\r\n        }\r\n      }\r\n\r\n      .UnifyLoginSlideVerify {\r\n        margin-bottom: var(--zy-distance-five);\r\n      }\r\n\r\n      .UnifyLoginFormOperation {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: var(--zy-distance-three);\r\n\r\n        .zy-el-checkbox {\r\n          height: var(--zy-height-secondary);\r\n        }\r\n\r\n        .UnifyLoginFormOperationText {\r\n          cursor: pointer;\r\n          color: var(--zy-el-color-primary);\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n      }\r\n    }\r\n\r\n    .UnifyLoginOperation {\r\n      width: 100%;\r\n      padding-bottom: var(--zy-distance-two);\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .UnifyLoginOperationBox {\r\n        margin: 0 var(--zy-distance-two);\r\n        cursor: pointer;\r\n\r\n        .UnifyLoginQrCode {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url('../img/login_qr_code.png');\r\n          background-size: 100% 100%;\r\n          margin: auto;\r\n        }\r\n\r\n        .UnifyLoginApp {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url('../img/login_app.png') no-repeat;\r\n          background-size: auto 100%;\r\n          background-position: center;\r\n          margin: auto;\r\n        }\r\n\r\n        .UnifyLoginOperationText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: var(--el-border-radius-small) 0;\r\n          text-align: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .UnifyLoginForm+.UnifyLoginSystemTips {\r\n      padding-top: var(--zy-distance-one);\r\n    }\r\n\r\n    .UnifyLoginSystemTips {\r\n      color: var(--zy-el-text-color-secondary);\r\n      font-size: var(--zy-text-font-size);\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.UnifyLoginQrCodeBox {\r\n  width: 320px;\r\n  background-color: #fff;\r\n\r\n  canvas {\r\n    display: block;\r\n    margin: auto;\r\n  }\r\n\r\n  .UnifyLoginQrCodeNameBody {\r\n    padding: var(--zy-distance-three);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .UnifyLoginQrCodeLogo {\r\n      width: 26px;\r\n      margin-right: 6px;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .UnifyLoginQrCodeName {\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .UnifyLoginQrCodeRefreshBody {\r\n    position: relative;\r\n\r\n    .UnifyLoginQrCodeRefresh {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 120px;\r\n      height: 120px;\r\n      background-color: rgba(000, 000, 000, 0.6);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .zy-el-button {\r\n        --zy-el-button-size: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .UnifyLoginQrCodeText {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding: var(--zy-distance-three);\r\n    color: var(--zy-el-color-primary);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA2FA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,KAAK;AACpE,SACEC,UAAU,EACVC,UAAU,EACVC,gBAAgB,EAChBC,yBAAyB,EACzBC,cAAc,EACdC,kBAAkB,QACb,yBAAyB;AAChC,SAASC,SAAS,QAAQ,2BAA2B;AAZrD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAa,CAAC;;;;;IAarC,IAAMC,SAAS,GAAGV,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,YAAY,CAAC;IAAA,EAAC;IAClE,IAAMW,aAAa,GAAGX,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,0CAA0C,CAAC;IAAA,EAAC;IACpG,IAAMY,IAAI,GAAGf,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMgB,eAAe,GAAGd,QAAQ,CAAC,YAAM;MACrC,IAAMU,IAAI,GAAG,CAACN,gBAAgB,CAACW,KAAK,IAAI,EAAE,IAAIZ,UAAU,CAACY,KAAK;MAC9D,IAAMC,GAAG,GAAGC,MAAM,CAACZ,yBAAyB,CAACU,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC;MAC/D,OAAOC,GAAG,GAAGN,IAAI,CAACQ,SAAS,CAAC,CAAC,EAAEF,GAAG,CAAC,GAAG,IAAI,GAAGN,IAAI,CAACQ,SAAS,CAACF,GAAG,CAAC,GAAGN,IAAI;IACzE,CAAC,CAAC;IACF,IAAAS,UAAA,GAwBIX,SAAS,CAAC,aAAa,CAAC;MAvB1BY,eAAe,GAAAD,UAAA,CAAfC,eAAe;MACfC,iBAAiB,GAAAF,UAAA,CAAjBE,iBAAiB;MACjBC,aAAa,GAAAH,UAAA,CAAbG,aAAa;MACbC,OAAO,GAAAJ,UAAA,CAAPI,OAAO;MACPC,OAAO,GAAAL,UAAA,CAAPK,OAAO;MACPC,OAAO,GAAAN,UAAA,CAAPM,OAAO;MACPC,SAAS,GAAAP,UAAA,CAATO,SAAS;MACTC,IAAI,GAAAR,UAAA,CAAJQ,IAAI;MACJC,KAAK,GAAAT,UAAA,CAALS,KAAK;MACLC,aAAa,GAAAV,UAAA,CAAbU,aAAa;MACbC,WAAW,GAAAX,UAAA,CAAXW,WAAW;MACXC,QAAQ,GAAAZ,UAAA,CAARY,QAAQ;MACRC,WAAW,GAAAb,UAAA,CAAXa,WAAW;MACXC,eAAe,GAAAd,UAAA,CAAfc,eAAe;MACfC,UAAU,GAAAf,UAAA,CAAVe,UAAU;MACVC,mBAAmB,GAAAhB,UAAA,CAAnBgB,mBAAmB;MACnBC,OAAO,GAAAjB,UAAA,CAAPiB,OAAO;MACPC,SAAS,GAAAlB,UAAA,CAATkB,SAAS;MACTC,UAAU,GAAAnB,UAAA,CAAVmB,UAAU;MACVC,UAAU,GAAApB,UAAA,CAAVoB,UAAU;MACVC,SAAS,GAAArB,UAAA,CAATqB,SAAS;MACTC,OAAO,GAAAtB,UAAA,CAAPsB,OAAO;MACPC,UAAU,GAAAvB,UAAA,CAAVuB,UAAU;IAEZ3C,SAAS,CAAC,YAAM;MACdyC,SAAS,CAAC,CAAC;MACXF,UAAU,CAAC,CAAC;IACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}