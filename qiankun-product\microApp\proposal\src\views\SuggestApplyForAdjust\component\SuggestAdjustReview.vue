<template>
  <div class="SuggestAdjustReview">
    <div class="SuggestAdjustReviewNameBody">
      <div class="SuggestAdjustReviewName">
        <div>申请调整办理单位审查</div>
      </div>
    </div>
    <div class="SuggestAdjustReviewBody">
      <global-info v-for="item in adjustList" :key="item.id">
        <global-info-line>
          <global-info-item label="申请单位">{{ item.handleOfficeName }}</global-info-item>
          <global-info-item label="申请时间">{{ format(item.createDate) }}</global-info-item>
        </global-info-line>
        <global-info-item label="申请调整理由">
          <pre>{{ item.adjustReason }}</pre>
        </global-info-item>
        <global-info-item label="希望办理单位">
          <pre>{{ item.hopeHandleOffice }}</pre>
        </global-info-item>
        <global-info-item v-if="item.verifyStatus" label="是否同意调整申请">
          {{ item.verifyStatus === 1 ? '同意申请' : '驳回' }}
        </global-info-item>
        <global-info-item v-if="item.verifyStatus" :label="item.verifyStatus === 1 ? '同意调整意见' : '驳回理由'">
          <pre>{{ item.noPassReason }}</pre>
        </global-info-item>
      </global-info>
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="是否同意调整申请" prop="verifyStatus" class="globalFormTitle">
        <el-radio-group v-model="form.verifyStatus" @change="reviewResultChange">
          <el-radio :label="1">同意申请</el-radio>
          <el-radio :label="2">驳回</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="form.verifyStatus === 1 ? '同意调整意见' : '驳回理由'" class="globalFormTitle">
        <el-input
          v-model="form.noPassReason"
          :placeholder="form.verifyStatus === 1 ? '请输入同意调整意见' : '请输入驳回理由'"
          type="textarea"
          :rows="5"
          clearable />
      </el-form-item>
      <template v-if="form.verifyStatus === 1">
        <el-form-item label="办理方式" prop="transactType">
          <el-select v-model="form.transactType" placeholder="请选择办理方式" @change="transactTypeChange" clearable>
            <el-option label="主办/协办" value="main_assist" />
            <el-option label="分办" value="publish" />
          </el-select>
        </el-form-item>
        <template v-if="form.transactType === 'main_assist'">
          <el-form-item label="主办单位" prop="mainHandleOfficeId" class="globalFormTitle">
            <suggest-simple-select-unit
              v-model="form.mainHandleOfficeId"
              :filterId="form.handleOfficeIds"
              :max="1"></suggest-simple-select-unit>
          </el-form-item>
        </template>
        <template v-if="form.transactType === 'main_assist'">
          <el-form-item label="协办单位" class="globalFormTitle">
            <suggest-simple-select-unit
              v-model="form.handleOfficeIds"
              :filterId="form.mainHandleOfficeId"></suggest-simple-select-unit>
          </el-form-item>
        </template>
        <template v-if="form.transactType === 'publish'">
          <el-form-item label="分办单位" prop="handleOfficeIds" class="globalFormTitle">
            <suggest-simple-select-unit v-model="form.handleOfficeIds"></suggest-simple-select-unit>
          </el-form-item>
        </template>
        <div class="zy-el-form-item-br"></div>
        <template v-if="!isPreAssign">
          <el-form-item label="答复截止时间" prop="answerStopDate">
            <xyl-date-picker
              v-model="form.answerStopDate"
              type="datetime"
              value-format="x"
              placeholder="请选择答复截止时间"
              :disabled-date="disabledDate"></xyl-date-picker>
          </el-form-item>
          <el-form-item label="调整截止时间" prop="adjustStopDate">
            <xyl-date-picker
              v-model="form.adjustStopDate"
              type="datetime"
              value-format="x"
              placeholder="请选择调整截止时间"
              :disabled-date="disabledDate"></xyl-date-picker>
          </el-form-item>
        </template>
        <template v-if="isPreAssign">
          <el-form-item label="签收截止时间" prop="confirmStopDate">
            <xyl-date-picker
              v-model="form.confirmStopDate"
              type="datetime"
              value-format="x"
              placeholder="请选择签收截止时间"
              :disabled-date="disabledDate"></xyl-date-picker>
          </el-form-item>
        </template>
      </template>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SuggestAdjustReview' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onActivated, watch } from 'vue'
import { format } from 'common/js/time.js'
import { ElMessage } from 'element-plus'
const props = defineProps({
  id: { type: String, default: '' },
  transactObj: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  verifyStatus: 1,
  noPassReason: '', // 交办意见
  transactType: '', // 请选择办理方式
  mainHandleOfficeId: [],
  handleOfficeIds: [],
  answerStopDate: '',
  adjustStopDate: '',
  confirmStopDate: ''
})
const rules = reactive({
  verifyStatus: [{ required: true, message: '请选择是否同意调整申请', trigger: ['blur', 'change'] }],
  transactType: [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }],
  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],
  handleOfficeIds: [{ type: 'array', required: false, message: '请选择协办单位', trigger: ['blur', 'change'] }],
  answerStopDate: [{ required: true, message: '请选择答复截止时间', trigger: ['blur', 'change'] }],
  adjustStopDate: [{ required: true, message: '请选择调整截止时间', trigger: ['blur', 'change'] }],
  confirmStopDate: [{ required: true, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]
})
const disabledDate = (time) => time.getTime() < Date.now() + 3600 * 1000 * 24 * 3
const adjustList = ref([])
const isPreAssign = ref(false)

onActivated(() => {
  globalReadConfig()
  handingPortionAdjustList()
})

const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: ['proposal_enable_pre_assign', 'SuggestSignTime'] })
  if (data.proposal_enable_pre_assign) {
    isPreAssign.value = Boolean(data?.proposal_enable_pre_assign)
  }
  if (data.SuggestSignTime) {
    form.confirmStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestSignTime)
  }
}

const handingPortionAdjustList = async () => {
  const res = await api.handingPortionAdjustList({ query: { suggestionId: props.id } })
  var { data } = res
  adjustList.value = data.filter((v) => !v.verifyStatus)
}

const reviewResultChange = () => {
  rules.transactType = [{ required: false, message: '请选择办理方式', trigger: ['blur', 'change'] }]
  rules.answerStopDate = [{ required: false, message: '请选择答复截止时间', trigger: ['blur', 'change'] }]
  rules.adjustStopDate = [{ required: false, message: '请选择调整截止时间', trigger: ['blur', 'change'] }]
  rules.confirmStopDate = [{ required: false, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]
  if (form.verifyStatus === 1) {
    rules.transactType = [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }]
    rules.answerStopDate = [{ required: true, message: '请选择答复截止时间', trigger: ['blur', 'change'] }]
    rules.adjustStopDate = [{ required: true, message: '请选择调整截止时间', trigger: ['blur', 'change'] }]
    rules.confirmStopDate = [{ required: true, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]
  }
}
const transactTypeChange = () => {
  if (form.transactType === 'main_assist') {
    rules.mainHandleOfficeId = [
      { type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }
    ]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else if (form.transactType === 'publish') {
    rules.mainHandleOfficeId = [
      { type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }
    ]
    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else {
    rules.mainHandleOfficeId = [
      { type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }
    ]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  }
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}

const globalJson = async () => {
  const { code } = await api.globalJson('/cppcc/handingPortionAdjust/verify', {
    suggestionId: props.id,
    verifyStatus: form.verifyStatus,
    noPassReason: form.noPassReason,
    handleOfficeType: form.verifyStatus === 1 ? form.transactType : null, // 办理方式
    mainHandleOfficeId: form.verifyStatus === 1 ? form.mainHandleOfficeId.join('') : null, // 主办单位
    handleOfficeIds: form.verifyStatus === 1 ? form.handleOfficeIds : null, // 协办或分办单位
    answerStopDate: form.verifyStatus === 1 ? form.answerStopDate : null,
    adjustStopDate: form.verifyStatus === 1 ? form.adjustStopDate : null,
    confirmStopDate: form.verifyStatus === 1 ? form.confirmStopDate : null
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '交办成功' })
    emit('callback')
  }
}
const resetForm = () => {
  emit('callback')
}
watch(
  () => props.transactObj,
  () => {
    if (form.transactType === '') {
      if (props.transactObj.transactType) {
        form.transactType = props.transactObj.transactType
        form.mainHandleOfficeId = props.transactObj.mainHandleOfficeId
        form.handleOfficeIds = props.transactObj.handleOfficeIds
        transactTypeChange()
      }
    }
  },
  { immediate: true }
)
</script>
<style lang="scss">
.SuggestAdjustReview {
  width: 100%;
  height: 100%;

  .SuggestAdjustReviewNameBody {
    padding: 0 var(--zy-distance-one);
    padding-top: var(--zy-distance-one);

    .SuggestAdjustReviewName {
      width: 100%;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      position: relative;
      text-align: center;

      div {
        display: inline-block;
        background-color: #fff;
        position: relative;
        z-index: 2;
        padding: 0 20px;
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 1px;
        background-color: var(--zy-el-color-primary);
      }
    }
  }

  .SuggestAdjustReviewBody {
    padding: var(--zy-distance-one);
    padding-bottom: 0;

    .global-info {
      padding-bottom: 12px;

      .global-info-item {
        .global-info-label {
          width: 160px;
        }

        .global-info-content {
          width: calc(100% - 160px);
        }
      }
    }
  }

  .suggest-simple-select-unit {
    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;
    border-radius: var(--zy-el-input-border-radius, var(--zy-el-border-radius-base));
  }
}
</style>
