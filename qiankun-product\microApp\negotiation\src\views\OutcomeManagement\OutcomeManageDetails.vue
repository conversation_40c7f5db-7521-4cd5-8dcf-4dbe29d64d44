<template>
  <el-scrollbar @scroll="scroll" ref="scrollbarRef" class="OutcomeManageDetails">
    <div class="MinSuggestManageDetailsLine" v-if="details.auditInfo && details.auditInfo.auditUser">
      <div class="detailsNavigation">
        <div :class="['detailsNavigationItem', isActive === 1 ? 'ActivityIsActive' : '']"
          @click="AnchorLinkTo({ offsetTop: 0 })">协商成果审核信息</div>
        <div :class="['detailsNavigationItem', isActive === 2 ? 'ActivityIsActive' : '']"
          @click="AnchorLinkTo(auditRef)">协商成果交办信息</div>
        <div :class="['detailsNavigationItem', isActive === 3 ? 'ActivityIsActive' : '']"
          @click="AnchorLinkTo(dataRef)">协商成果内容</div>
      </div>
    </div>
    <div class="MinSuggestManageDetailsContent" ref="printRef">
      <div class="MinSuggestManageDetailsContentTitle" ref="auditRef"
        v-if="details.auditInfo && details.auditInfo.auditUser">
        <div>
          <img src="../../assets/img/column.png" />
          <span>协商成果审核信息</span>
        </div>
      </div>
      <div class="MinSuggestManageDetailsContentGlobal" v-if="details.auditInfo && details.auditInfo.auditUser">
        <global-info>
          <global-info-line>
            <global-info-item label="审核者">{{ details.auditInfo.auditUser }}</global-info-item>
            <global-info-item label="审核时间">{{ format(details.auditInfo.createDate, 'YYYY-MM-DD hh:mm')
            }}</global-info-item>
          </global-info-line>
          <global-info-line>
            <global-info-item label="审核结果">{{ details.auditInfo.auditResult === 1 ? '通过' : '不通过' }}</global-info-item>
            <global-info-item label="审核意见">{{ details.auditInfo.auditOpinion }}</global-info-item>
          </global-info-line>
        </global-info>
      </div>
      <div class="MinSuggestManageDetailsContentTitle" ref="transactRef"
        v-if="details.transactListVo && details.transactListVo.length > 0">
        <div>
          <img src="../../assets/img/column.png" />
          <span>协商成果交办信息</span>
        </div>
        <el-button type="primary" @click="onChange(details.changeList)"
          v-if="details.changeList && details.changeList.length > 0">查看调整记录</el-button>
      </div>
      <div v-if="details.transactListVo && details.transactListVo.length > 0">
        <div class="MinSuggestManageDetailsContentGlobal" v-for="item in details.transactListVo"
          :key="item.transactDate">
          <global-info>
            <global-info-line>
              <global-info-item label="交办人">{{ item.transactUserName }}</global-info-item>
              <global-info-item label="交办时间">{{ format(item.transactDate, 'YYYY-MM-DD hh:mm') }}</global-info-item>
            </global-info-line>
            <global-info-line>
              <global-info-item label="交办">{{ item.transactTo }}</global-info-item>
              <global-info-item label="交办意见">{{ item.transactOpinion }}</global-info-item>
            </global-info-line>
          </global-info>
        </div>
      </div>
      <div class="MinSuggestManageDetailsContentTitle" ref="transactRef"
        v-if="details.negotiateGroupListVo && details.negotiateGroupListVo.length > 0">
        <div>
          <img src="../../assets/img/column.png" />
          <span>协商成果交办信息</span>
        </div>
        <!-- <el-button type="primary" @click="onChange(details.changeList)"
          v-if="details.changeList && details.changeList.length > 0">查看调整记录</el-button> -->
      </div>
      <div v-if="details.negotiateGroupListVo && details.negotiateGroupListVo.length > 0">
        <div style="padding: 10px 40px;" v-for="item in details.negotiateGroupListVo" :key="item.transactDate">
          <global-info>
            <global-info-line>
              <global-info-item label="交办人">{{ item.transactUserName }}</global-info-item>
              <global-info-item label="交办时间">{{ format(item.transactDate, 'YYYY-MM-DD hh:mm') }}</global-info-item>
            </global-info-line>
            <global-info-line>
              <global-info-item :label="item.type">{{ item.transactTo }}</global-info-item>
              <global-info-item label="状态">{{ item.statusName }}</global-info-item>
            </global-info-line>
            <global-info-item label="交办意见">{{ item.transactOpinion }}</global-info-item>
          </global-info>
        </div>
      </div>
      <div class="MinSuggestManageDetailsContentTitle" v-if="details.groupName || details.rejectInfo">
        <div>
          <img src="../../assets/img/column.png" />
          <span>协商成果办理信息</span>
        </div>
      </div>
      <div class="MinSuggestManageDetailsContentGlobal" v-if="details.rejectInfo && details.rejectInfo.groupName">
        <global-info>
          <global-info-item label="办理单位">{{ details.rejectInfo.groupName }}</global-info-item>
          <global-info-item label="不予受理">{{ details.rejectInfo.opinion }}</global-info-item>
        </global-info>
      </div>
      <div class="MinSuggestManageDetailsContentGlobal" v-if="details.groupName">
        <global-info>
          <global-info-item label="办理单位">{{ details.groupName }}</global-info-item>
          <global-info-item label="回复内容" v-for="item in details.replyList" :key="item.id">
            <div>{{ item.opinion }}</div>
            <div>{{ format(item.createDate, 'YYYY-MM-DD hh:mm') }}</div>
            <xyl-global-file :fileData="item.attachments"></xyl-global-file>
          </global-info-item>
          <global-info-item v-if="details.evaluationInfo" label="满意度测评">
            {{ details.evaluationInfo?.appText }}{{ details.evaluationInfo?.opinion }}
          </global-info-item>
        </global-info>
      </div>
      <div>
        <!-- 审核 -->
        <SubmitExamine v-if="details.showAudit" :id="route.query.id" name="审核" width="100%" @callback="examineCallback"
          nextNodeId=""></SubmitExamine>
        <!-- 回复 -->
        <SubmitReply v-if="route.query.userType && details.isShow" :id="route.query.id" name="回复" width="100%"
          :userType="route.query.userType" @callback="submitCallback"></SubmitReply>
        <!-- 交办 -->
        <SubmitHandle v-if="details.showHandle && !route.query.userType" :id="route.query.id" name="交办" width="100%"
          @callback="examineCallback"></SubmitHandle>
      </div>
      <div class="MinSuggestManageDetailsContentLine" v-if="details.auditInfo && details.auditInfo.auditUser"></div>
      <div class="MinSuggestManageDetailsContentRecommendation" ref="dataRef">
        <div class="MinSuggestManageDetailsContentRecommendationTitle">协商成果</div>
        <div class="MinSuggestManageDetailsContentRecommendationTime">提交时间：
          {{ format(details.submitDate, 'YYYY-MM-DD hh: mm') }}
        </div>
        <div class="MinSuggestManageDetailsContentRecommendationName">{{ details.title }}</div>
        <div class="info">
          <div class="info_item">
            <div class="name">提交人:</div>
            <div class="value">{{ details.submitUserName }}</div>
          </div>
          <div class="info_item">
            <div class="name">委员证号:</div>
            <div class="value">{{ details.submitUserInfo?.cardNumber }}</div>
          </div>
        </div>
        <div class="info">
          <div class="info_item">
            <div class="name">界别:</div>
            <div class="value">{{ details.submitUserInfo?.sector }}</div>
          </div>
          <div class="info_item">
            <div class="name">联系电话:</div>
            <div class="value">{{ details.submitUserInfo?.mobile }}</div>
          </div>
        </div>
        <div class="info">
          <div class="info_item">
            <div class="name">办公电话:</div>
            <div class="value">{{ details.submitUserInfo?.officePhone }}</div>
          </div>
          <div class="info_item">
            <div class="name">邮政编码:</div>
            <div class="value">{{ details.submitUserInfo?.postcode }}</div>
          </div>
        </div>
        <div class="info">
          <div class="name">单位及职务:</div>
          <div class="value">{{ details.submitUserInfo?.position }}</div>
        </div>
        <div class="info">
          <div class="name">通讯地址:</div>
          <div class="value">{{ details.submitUserInfo?.callAddress }}</div>
        </div>
        <div class="info_content" v-html="details.content"></div>
        <xyl-global-file :fileData="details.attachments"></xyl-global-file>
      </div>
      <div class="SuggestDetailInfoName" v-if="details.colarUserInfo">领办人</div>
      <div class="SuggestDetailTable" v-if="details.colarUserInfo">
        <div class="SuggestDetailTableHead">
          <div class="SuggestDetailTableItem row1">姓名</div>
          <div class="SuggestDetailTableItem row1">委员证号</div>
          <div class="SuggestDetailTableItem row1">联系电话</div>
          <div class="SuggestDetailTableItem row3">通讯地址</div>
        </div>
        <div class="SuggestDetailTableBody">
          <div class="SuggestDetailTableItem row1">{{ details.colarUserInfo?.userName }}</div>
          <div class="SuggestDetailTableItem row1">{{ details.colarUserInfo?.cardNumber }}</div>
          <div class="SuggestDetailTableItem row1">{{ details.colarUserInfo?.mobile }}</div>
          <div class="SuggestDetailTableItem row3">{{ details.colarUserInfo?.callAddress }}</div>
        </div>
      </div>
      <div class="SuggestDetailInfoName" v-if="details.inviteList?.length">协办人</div>
      <div class="SuggestDetailTable" v-if="details.inviteList?.length">
        <div class="SuggestDetailTableHead">
          <div class="SuggestDetailTableItem row1">姓名</div>
          <div class="SuggestDetailTableItem row1">委员证号</div>
          <div class="SuggestDetailTableItem row1">联系电话</div>
          <div class="SuggestDetailTableItem row3">通讯地址</div>
        </div>
        <div class="SuggestDetailTableBody" v-for="item in details.inviteList" :key="item.userId">
          <div class="SuggestDetailTableItem row1">{{ item.userName }}</div>
          <div class="SuggestDetailTableItem row1">{{ item.cardNumber }}</div>
          <div class="SuggestDetailTableItem row1">{{ item.mobile }}</div>
          <div class="SuggestDetailTableItem row3">{{ item.callAddress }}</div>
        </div>
      </div>
    </div>
    <div class="MinSuggestManageDetailsPrint">
      <div @click="handlePrint" class="detailsFunction">
        <img src="../../assets/img/print.png" />
        <span>打印</span>
      </div>
    </div>
    <xyl-popup-window v-model="changeShow" name="查看调整记录">
      <div class="globalTable">
        <el-table ref="tableRef" row-key="id" :data="tableData">
          <el-table-column label="调整单位" min-width="120" prop="groupName" />
          <el-table-column label="调整类型" min-width="100" prop="typeName" />
          <el-table-column label="提交时间" min-width="120" prop="submitDate">
            <template #default="scope">
              {{ format(scope.row.submitDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>

          <el-table-column label="原因说明" min-width="120" prop="opinion" />
          <el-table-column label="审核结果" min-width="180" prop="auditResult" />
        </el-table>
      </div>
    </xyl-popup-window>
    <publicOpinionPrint v-if="elPrintWhetherShow" :params="printParams" @callback="printCallback"></publicOpinionPrint>

  </el-scrollbar>
</template>
<script>
export default { name: 'OutcomeManageDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { format } from 'common/js/time.js'
import { ElMessage } from 'element-plus'
import SubmitExamine from "./component/SubmitExamine"
import SubmitReply from "./component/SubmitReply"
import SubmitHandle from "./component/SubmitHandle"
import publicOpinionPrint from '@/components/publicOpinionPrint/publicOpinionPrint'
import { qiankunMicro } from "common/config/MicroGlobal";
// import {qiankunMicro} from "common/config/MicroGlobal";
// import {Print} from "common/js/print";
const route = useRoute()
// const props = defineProps({ id: { type: String, default: '' } })
const details = ref({})
const printRef = ref()
const changeShow = ref(false)
const tableData = ref([])
const scrollbarTop = ref(0)
const isActive = ref(1)
const auditRef = ref()
const transactRef = ref()
const dataRef = ref()
const scrollbarRef = ref()
const timer = ref()
const printParams = ref({})
const elPrintWhetherShow = ref(false)
const isScrollTop = ref(0)
onMounted(() => { microAdviceInfo() })
const scroll = ({ scrollTop }) => {
  scrollbarTop.value = scrollTop
  if (scrollbarTop.value < auditRef.value?.offsetTop) {
    isActive.value = 1
  } else if (scrollbarTop.value < transactRef?.value?.offsetTop) {
    if (!auditRef.value) {
      isActive.value = 1
    } else {
      isActive.value = 2
    }
  } else if (scrollbarTop.value < dataRef.value.offsetTop) {
    isActive.value = 3
  }
  if (scrollbarTop.value > isScrollTop.value) {
    isActive.value = 3
  }
}
// const callback = (type) => {
//   elPrintWhetherShow.value = false
//   if (type) {
//     qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
//   }
// }
const printCallback = () => {
  elPrintWhetherShow.value = false
}

const AnchorLinkTo = (navEl) => {
  clearInterval(timer.value)
  if (scrollbarTop.value >= navEl.offsetTop) {
    timer.value = setInterval(function () {
      if (scrollbarTop.value <= navEl.offsetTop) {
        clearInterval(timer.value)
      } else {
        if ((scrollbarTop.value - 52) <= navEl.offsetTop) {
          scrollbarRef.value?.setScrollTop(navEl.offsetTop)
        } else {
          scrollbarRef.value?.setScrollTop(scrollbarTop.value - 52)
        }
      }
    }, 2)
  } else {
    timer.value = setInterval(function () {
      if (scrollbarTop.value >= navEl.offsetTop || scrollbarTop.value > isScrollTop.value) {
        if (scrollbarTop.value > isScrollTop.value) { isActive.value = 3 }
        clearInterval(timer.value)
      } else {
        if ((scrollbarTop.value + 52) >= navEl.offsetTop) {
          scrollbarRef.value?.setScrollTop(navEl.offsetTop)
        } else {
          scrollbarRef.value?.setScrollTop(scrollbarTop.value + 52)
        }
      }
    }, 6)
  }
}
const handlePrint = () => {
  // Print.init(printRef.value)
  if (JSON.stringify(details.value) === '{}') return ElMessage({ type: 'warning', message: '请等待详情加载完成再进行导出！' })
  printParams.value = { ids: [route.query.id] }
  elPrintWhetherShow.value = true

}
const onChange = (e) => {
  changeShow.value = true
  tableData.value = e
}
const examineCallback = () => {
  qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
}
const submitCallback = () => {
  qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
}
const microAdviceInfo = async () => {
  const res = await api.microAdviceInfo({ detailId: route.query.id })
  var { data } = res
  details.value = data
  details.value.isShow = true
  nextTick(() => {
    const MinSuggestManageDetailsContent = scrollbarRef.value.$el.querySelector('.MinSuggestManageDetailsContent')
    isScrollTop.value = (MinSuggestManageDetailsContent.offsetHeight - scrollbarRef.value.$el.offsetHeight) + 38
  })
}
</script>
<style lang="scss">
.OutcomeManageDetails {
  width: 100%;
  padding: 20px 20px 0 20px;
  height: 100%;

  .MinSuggestManageDetailsLine {
    width: 990px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .detailsNavigation {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(-120%, -50%);

      .detailsNavigationItem {
        font-size: var(--zy-name-font-size);
        line-height: var(--zy-line-height);
        padding-right: 20px;
        margin-bottom: 40px;
        position: relative;
        cursor: pointer;

        &::after {
          content: "";
          position: absolute;
          top: 50%;
          right: 0;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          border: 2px solid var(--zy-el-border-color-lighter);
          transform: translateY(-50%);
        }

        &::before {
          content: "";
          position: absolute;
          top: calc(50% + 5px);
          right: 4px;
          width: 2px;
          height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + 30px);
          background-color: var(--zy-el-border-color-lighter);
        }

        &:last-child {
          &::before {
            background-color: transparent;
          }
        }
      }

      .ActivityIsActive {
        font-weight: bold;

        &::after {
          border: 2px solid var(--zy-el-color-primary);
        }
      }
    }
  }

  .MinSuggestManageDetailsContent {
    max-width: 990px;
    margin: 20px auto;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);
    position: relative;
    padding: 20px 40px;

    .SuggestDetailInfoName {
      padding-top: 15px;
      padding-bottom: 5px;
      font-weight: bold;
      font-size: var(--zy-text-font-size);
      line-height: var(--zy-line-height);
    }

    .MinSuggestManageDetailsContentTitle {
      div {
        display: flex;
        align-items: center;
      }

      margin-top: 20px;
      display: flex;
      align-items: center;
      padding:0 40px;
      font-size: var(--zy-name-font-size);
      font-weight: bold;
      display: flex;
      justify-content: space-between;

      img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }

    .MinSuggestManageDetailsContentGlobal {
      padding: 20px 40px;
    }

    .MinSuggestManageDetailsContentLine {
      background-color: #F8F8F8;
      height: 10px;
      width: 100%;
    }

    .MinSuggestManageDetailsContentRecommendation {
      padding: 40px;

      .MinSuggestManageDetailsContentRecommendationTitle {
        padding-bottom: 20px;
        text-align: center;
        font-weight: bold;
        font-size: var(--zy-title-font-size);
        border-bottom: 2px solid #3657C0;
      }

      .MinSuggestManageDetailsContentRecommendationTime {
        font-size: var(--zy-text-font-size);
        color: #999999;
        margin-top: 20px;
      }

      .MinSuggestManageDetailsContentRecommendationName {
        font-size: 20px;
        margin-top: 20px;
        font-weight: bold;
      }

      .info {
        width: 100%;
        margin-top: 20px;
        display: flex;
        font-size: 14px;

        .info_item {
          width: 50%;
          display: flex;
        }

        .name {
          width: 80px;
        }
      }

      .info_content {
        margin-top: 40px;
        line-height: 32px;
        font-size: var(zy-name-font-size);
        font-weight: 400;
        text-indent: 30px;
        padding-bottom: 10px;

      }
    }

    .SuggestDetailTable {
      width: 100%;
      margin-bottom: 20px;
      border-top: 1px solid var(--zy-el-border-color-lighter);
      border-right: 1px solid var(--zy-el-border-color-lighter);

      .SuggestDetailTableHead,
      .SuggestDetailTableBody {
        width: 100%;
        display: flex;
        border-bottom: 1px solid var(--zy-el-border-color-lighter);
      }

      .SuggestDetailTableHead {
        background-color: var(--zy-el-color-info-light-9);
      }

      .SuggestDetailTableBody {
        border-bottom: 1px solid var(--zy-el-border-color-lighter);
      }

      .SuggestDetailTableItem {
        text-align: center;
        border-left: 1px solid var(--zy-el-border-color-lighter);
        font-size: var(--zy-text-font-size);
        line-height: var(--zy-line-height);
        padding: 10px;
        overflow: hidden;
        white-space: nowrap;
      }

      .row1 {
        flex: 1;
      }

      .row2 {
        flex: 2;
      }

      .row3 {
        flex: 3;
      }

      .row5 {
        flex: 5;
      }
    }
  }

  .MinSuggestManageDetailsPrint {
    width: 990px;
    position: absolute;
    top: 50px;
    left: 51%;
    transform: translateX(-50%);

    .detailsFunction {
      position: absolute;
      top: 0;
      right: 0;
      transform: translateX(112%);
      font-size: var(zy-text-font-size);
    }

    img {
      width: 20px;
      height: 20px;
      margin-right: 6px;
    }
  }
}

@media screen and (max-width: 1580px) {
  .OutcomeManageDetails {
    .MinSuggestManageDetailsLine {
      .detailsNavigation {
        .detailsNavigationItem {
          color: transparent;
        }
      }
    }

    .MinSuggestManageDetailsPrint {
      .detailsFunction {
        color: transparent;
      }
    }
  }
}
</style>
