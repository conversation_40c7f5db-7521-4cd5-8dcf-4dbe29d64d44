<template>
  <el-scrollbar class="AiUseStatistics">
    <div class="AiUseStatistics-header">
      <div class="AiUseStatistics-header-box">
        <div class="AiUseStatistics-header-left">
          <div class="AiUseStatistics-header-left-title">AI使用统计分析</div>
          <div class="AiUseStatistics-header-left-desc">实时监控AI服务使用情况与趋势分析</div>
        </div>
        <div class="AiUseStatistics-header-right" @click="aigptStatistics(true)">
          刷新数据
          <el-icon><RefreshRight /></el-icon>
        </div>
      </div>
      <div class="AiUseStatistics-header-content">
        <div class="AiUseStatistics-header-content-item">
          <div class="AiUseStatistics-header-content-item-left">
            <div class="AiUseStatistics-header-content-item-left-title">
              服务总人次
              <!-- <img src="../img/AiHelp.png" alt="" /> -->
            </div>
            <div class="AiUseStatistics-header-content-item-left-num">{{ overviewData.totalServiceTimes || 0 }}</div>
          </div>
          <div class="AiUseStatistics-header-content-item-right">
            <img src="../img/AiIcon3.png" alt="" />
          </div>
        </div>
        <div class="AiUseStatistics-header-content-item">
          <div class="AiUseStatistics-header-content-item-left">
            <div class="AiUseStatistics-header-content-item-left-title">
              服务总人数
              <!-- <img src="../img/AiHelp.png" alt="" /> -->
            </div>
            <div class="AiUseStatistics-header-content-item-left-num">{{ overviewData.totalUsers || 0 }}</div>
          </div>
          <div class="AiUseStatistics-header-content-item-right">
            <img src="../img/AiIcon2.png" alt="" />
          </div>
        </div>
        <div class="AiUseStatistics-header-content-item">
          <div class="AiUseStatistics-header-content-item-left">
            <div class="AiUseStatistics-header-content-item-left-title">
              累计问答次数
              <!-- <img src="../img/AiHelp.png" alt="" /> -->
            </div>
            <div class="AiUseStatistics-header-content-item-left-num">
              {{ overviewData.totalAnswerTimes || 0 }}
              <span @click="handleDetail">
                详情
                <el-icon><ArrowRight /></el-icon>
              </span>
            </div>
          </div>
          <div class="AiUseStatistics-header-content-item-right">
            <img src="../img/AiIcon1.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div class="AiUseStatistics-chart">
      <div class="AiUseStatistics-chart-box">
        <div class="AiUseStatistics-chart-timeSelect">
          <el-date-picker
            v-model="year"
            :clearable="false"
            type="year"
            value-format="YYYY"
            placeholder="选择年份"
            @change="getLineData" />
        </div>
        <barAndPie :data="lineData" />
      </div>
      <div class="AiUseStatistics-chart-box-right">
        <cockpitChart :data="detailData" />
      </div>
    </div>
    <div class="AiUseStatistics-chart-content">
      <div class="AiUseStatistics-chart-content-left">
        <div class="AiUseStatistics-chart-content-right-title">
          <div class="AiUseStatistics-chart-content-right-title-left">业务线热词分布</div>
          <div class="AiUseStatistics-chart-content-right-title-right">
            <el-select v-model="hottype" placeholder="请选择业务线" @change="getHotWordData" filterable>
              <el-option v-for="item in hottypeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </div>
        </div>
        <div class="hotWordBox">
          <div class="hotWordContent">
            <wordCloudChart :data="hotWordData" />
          </div>
        </div>
        <div class="hotWordBox-content">
          <div class="hotWordBox-content-title">
            {{ getHottypeName() }}
          </div>
          <div class="hotWordBox-content-list">
            <div
              class="hotWordBox-content-list-item"
              v-show="index < 3"
              v-for="(item, index) in hotWordData"
              :key="item.id">
              <div class="hotWordBox-content-list-item-title">
                {{ item.hotWord }}
              </div>
              <div class="hotWordBox-content-list-item-num">{{ item.appearTimes }} 次</div>
            </div>
          </div>
        </div>
      </div>
      <div class="AiUseStatistics-chart-content-right">
        <div class="AiUseStatistics-chart-content-right-title">
          <div class="AiUseStatistics-chart-content-right-title-left">近期对话记录</div>
          <div class="AiUseStatistics-chart-content-right-title-right">
            <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <!-- 查看更多
            <el-icon><ArrowRight /></el-icon> -->
          </div>
        </div>
        <div class="AiUseStatistics-chart-content-right-content">
          <div class="globalTable">
            <el-table ref="tableRef" row-key="id" :data="tableData">
              <el-table-column label="用户" prop="createUserName" width="100"></el-table-column>
              <el-table-column label="对话内容" prop="userQuestion" min-width="120">
                <template #default="{ row }">
                  <!-- <el-tooltip
                    popper-class="AiUseStatistics-tooltip"
                    :effect="'light'"
                    :content="removeHtmlTag(row.promptQuestion)"
                    :disabled="row.promptQuestion.length < 10"
                    placement="top-start">
                    <span class="AiUseStatistics-tooltip-content">{{ removeHtmlTag(row.promptQuestion) }}</span>
                  </el-tooltip> -->
                  <!-- <span class="AiUseStatistics-tooltip-content">{{ removeHtmlTag(row.promptQuestion) }}</span> -->
                  <el-button type="primary" link text @click="lookDetail(row)" class="AiUseStatistics-tooltip-content">
                    {{ removeHtmlTag(row.userQuestion) }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="业务线" prop="chatBusinessName" width="180"></el-table-column>
              <el-table-column label="时间" prop="createDate" width="160">
                <template #default="{ row }">
                  {{ row.createDate ? format(row.createDate, 'YYYY-MM-DD HH:mm') : '' }}
                </template>
              </el-table-column>
              <xyl-global-table-button
                v-if="false"
                :data="tableButtonList"
                @buttonClick="handleCommand"></xyl-global-table-button>
            </el-table>
          </div>
          <div class="globalPagination">
            <el-pagination
              v-model:currentPage="pageNo"
              v-model:page-size="pageSize"
              :page-sizes="pageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleQuery"
              @current-change="handleQuery"
              :total="totals"
              background />
          </div>
        </div>
      </div>
    </div>
    <xyl-popup-window v-model="showMore" name="详情">
      <div class="AiUseStatistics-detail">
        <div class="detail-item" v-for="item in detailData" :key="item.id">
          <div class="detail-item-title">
            <img src="../img/tongjiIcon.png" alt="" />
            <el-tooltip :content="item.name" :disabled="item.name.length < 10" placement="top">
              <span>{{ item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name }}</span>
            </el-tooltip>
          </div>
          <div class="detail-item-content">
            {{ item.count }}
            <span>次</span>
          </div>
        </div>
      </div>
    </xyl-popup-window>
    <xyl-popup-window v-model="showDetail" name="近期对话详情">
      <div class="chatDetail">
        <div class="questionHead">
          <div class="questionName">
            <el-image :src="imgUrl(chatDetail.headImg)" fit="cover" />
            <span>{{ chatDetail.createUserName }}</span>
          </div>
          <div class="questionTime">
            <el-tag type="primary">{{ chatDetail.chatBusinessName }}</el-tag>
            <span type="primary">提问于： {{ format(chatDetail.createDate, 'YYYY-MM-DD HH:mm') }}</span>
          </div>
        </div>
        <div class="questionContent">
          <div class="questionContent-title">
            <!-- <span>{{ chatDetail.userQuestion }}</span> -->
            <div v-html="chatDetail.userQuestion"></div>
          </div>
          <div class="questionContent-answer" v-html="chatDetail.answer"></div>
        </div>
      </div>
    </xyl-popup-window>
  </el-scrollbar>
</template>
<script>
export default {
  name: 'AiUseStatistics'
}
</script>
<script setup>
import { ref, onMounted } from 'vue'
import api from '@/api'
import barAndPie from './common/barAndPie.vue'
import wordCloudChart from './common/wordCloudChart.vue'
import cockpitChart from './common/cockpitChart.vue'
import { format } from 'common/js/time.js'
import { GlobalTable } from 'common/js/GlobalTable.js'
const tableButtonList = [{ id: 'edit', name: '查看详情', width: 100, has: '' }]

const imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))
const { tableRef, totals, pageNo, pageSize, pageSizes, tableData, keyword, handleQuery } = GlobalTable({
  tableApi: 'aigptChatLogsList'
})

onMounted(() => {
  aigptStatistics()
  getLineData()
  handleQuery()
  getHottypeList()
  handleDetail()
})
const overviewData = ref({})
const aigptStatistics = async (forceRefresh = false) => {
  const res = await api.globalJson('/aigptStatistics/overview', { forceRefresh })
  overviewData.value = res.data
}
const lineData = ref([])
const year = ref(new Date().getFullYear() + '')
const getLineData = async () => {
  const res = await api.globalJson('/aigptStatistics/trend', {
    beginDate: new Date(year.value + '-01-01').getTime(),
    endDate: new Date(year.value + '-12-31').getTime(),
    timeDimension: 'month'
  })
  const arr = [
    {
      name: '对话量',
      data: res.data.map((v) => ({ ...v, value: v.dialogueCount, name: v.bucket })),
      type: 'line',
      color: 'rgba(31, 198, 255, 1)'
    },
    {
      name: '活跃用户',
      data: res.data.map((v) => ({ ...v, value: v.activeUsers, name: v.bucket })),
      type: 'line',
      color: 'rgba(245, 231, 79, 1)'
    }
  ]
  lineData.value = arr
}
const hottype = ref('')
const hottypeList = ref([])
const getHottypeName = () => {
  return hottypeList.value.find((v) => v.id === hottype.value)?.name || '全部业务线'
}
const getHottypeList = async () => {
  const res = await api.globalJson('/aigptChatScene/selector')
  hottypeList.value = res.data
  hottypeList.value.unshift({ id: '', name: '全部业务线' })
  if (res.data.length > 0) {
    hottype.value = res.data[0].id
    getHotWordData()
  }
}
const hotWordData = ref([])
const getHotWordData = async () => {
  const res = await api.globalJson('/aigptStatistics/hotwordTop', {
    chatBusinessScene: hottype.value
  })
  hotWordData.value = res.data
}
const showMore = ref(false)
const detailData = ref([])
const handleDetail = async () => {
  if (detailData.value.length) {
    showMore.value = true
    return
  }
  const res = await api.globalJson('/aigptStatistics/overviewdetail')
  detailData.value = res.data.sort((a, b) => b.count - a.count)
}

const removeHtmlTag = (str) => {
  return decodeURIComponent(str.replace(/<[^>]*>?/g, ''))
}
const decodeURIComponent = (str) => {
  return str
    .replace(/<[^>]*>?/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
}

const showDetail = ref(false)
const chatDetail = ref({})
const lookDetail = async (row) => {
  const res = await api.globalJson('/aigptChatLogs/info', { detailId: row.id })
  console.log('🚀 ~ lookDetail ~ res:', res)
  chatDetail.value = res.data
  chatDetail.value.chatBusinessName = row.chatBusinessName
  chatDetail.value.headImg = row.headImg
  showDetail.value = true
}
</script>

<style lang="scss">
.AiUseStatistics {
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
  padding-top: 20px;
  padding-bottom: 20px;
  .AiUseStatistics-header {
    background: #ffffff;
    border-radius: 0px 0px 0px 0px;
    padding: 20px;
    margin-bottom: 20px;
    .AiUseStatistics-header-box {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
      .AiUseStatistics-header-left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
      }
      .AiUseStatistics-header-right {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .AiUseStatistics-header-left-title {
        font-size: 20px;
        font-weight: 600;
        color: #333333;
        margin-bottom: 10px;
      }
      .AiUseStatistics-header-left-desc {
        font-size: 14px;
        color: #999999;
      }
      .AiUseStatistics-header-right {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--zy-el-color-primary);
        cursor: pointer;
        font-size: 14px;
        .zy-el-icon {
          font-size: 16px;
          margin-left: 5px;
        }
      }
    }
    .AiUseStatistics-header-content {
      display: flex;
      gap: 20px;
      .AiUseStatistics-header-content-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f5f7fa;
        border-radius: 6px 6px 6px 6px;
        padding: 20px;
        .AiUseStatistics-header-content-item-left {
          .AiUseStatistics-header-content-item-left-title {
            color: #474b4f;
            font-size: 16px;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            img {
              width: 16px;
              height: 16px;
              margin-left: 5px;
            }
          }
          .AiUseStatistics-header-content-item-left-num {
            color: var(--zy-el-color-primary);
            font-size: 26px;
            font-weight: 600;
            span {
              margin-left: 20px;
              color: var(--zy-el-color-primary);
              cursor: pointer;
              font-size: 14px;
              .zy-el-icon {
                font-size: 14px;
                margin-left: 5px;
              }
            }
          }
        }
        .AiUseStatistics-header-content-item-right {
          width: 60px;
          height: 60px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .AiUseStatistics-chart {
    width: 100%;
    height: 334px;
    padding-top: 20px;
    margin-bottom: 20px;
    background: #ffffff;
    position: relative;
    display: flex;
    .AiUseStatistics-chart-box {
      width: 33%;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
    }
    .AiUseStatistics-chart-timeSelect {
      position: absolute;
      right: 200px;
      top: 0px;
      z-index: 10;
      .zy-el-date-editor {
        width: 120px;
      }
    }
    .AiUseStatistics-chart-box-right {
      width: 66%;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
    }
  }
  .AiUseStatistics-chart-content {
    display: flex;
    gap: 20px;
    height: 384px;

    .AiUseStatistics-chart-content-right-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      .AiUseStatistics-chart-content-right-title-left {
        font-size: 20px;
        font-weight: 600;
      }

      .AiUseStatistics-chart-content-right-title-right {
        color: var(--zy-el-color-primary);
        cursor: pointer;
        font-size: 14px;
        display: flex;
        .zy-el-input {
          width: 160px;
          margin-right: 10px;
        }
        .zy-el-select {
          width: 160px;
        }
        .zy-el-icon {
          font-size: 14px;
          margin-left: 5px;
        }
      }
    }

    .AiUseStatistics-chart-content-left {
      width: 33%;
      flex-shrink: 0;
      background: #ffffff;
      padding: 0 20px;
      .hotWordBox {
        height: 158px;
        background: #f5f7fa;
        .hotWordContent {
          width: 80%;
          height: 158px;
          margin: auto;
          background-image: url('../img/hotwordbg.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;
          border-radius: 0px 0px 0px 0px;
          opacity: 0.5;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 20px;
        }
      }
      .hotWordBox-content {
        height: calc(100% - 158px - 62px);
        padding-top: 20px;
        .hotWordBox-content-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 10px;
        }
        .hotWordBox-content-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
          .hotWordBox-content-list-item {
            display: flex;
            justify-content: space-between;
            .hotWordBox-content-list-item-title {
              padding-left: 16px;
              font-size: 16px;
              font-weight: 600;
              position: relative;
              &:after {
                content: '';
                display: block;
                width: 6px;
                height: 6px;
                background: var(--zy-el-color-primary);
                border-radius: 50%;
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                z-index: 10;
              }
            }
            .hotWordBox-content-list-item-num {
              font-size: 14px;
              color: var(--zy-el-color-primary);
            }
          }
        }
      }
    }
    .AiUseStatistics-chart-content-right {
      flex-shrink: 0;
      width: 66%;
      background: #ffffff;
      .AiUseStatistics-chart-content-right-content {
        height: calc(100% - 46px);
        .globalTable {
          height: calc(100% - 42px);
        }
      }
    }
  }
}

.AiUseStatistics-detail {
  width: 990px;
  padding: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
  .detail-item {
    width: 32%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f5f7fa;
    border-radius: 4px 4px 4px 4px;
    padding: 12px 16px;
    .detail-item-title {
      display: flex;
      align-items: center;
      img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
    .detail-item-content {
      font-size: 20px;
      font-weight: 600;
      color: var(--zy-el-color-primary);
      span {
        font-size: 14px;
        color: #999999;
      }
    }
  }
}
.AiUseStatistics-tooltip-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.AiUseStatistics-tooltip {
  max-width: 600px;
  background: #f0f2f5 !important;
}

.chatDetail {
  width: 800px;
  padding: 24px;
  .questionHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .questionName {
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      .zy-el-image {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 10px;
      }
    }
    .questionTime {
      font-size: 14px;
      color: #999999;
      display: flex;
      align-items: center;
      .zy-el-tag {
        margin-right: 10px;
      }
    }
  }
  .questionContent {
    .questionContent-title {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 10px;
    }
    .questionContent-answer {
      font-size: 16px;
      color: #666666;
      line-height: 24px;
    }
  }
}
</style>
