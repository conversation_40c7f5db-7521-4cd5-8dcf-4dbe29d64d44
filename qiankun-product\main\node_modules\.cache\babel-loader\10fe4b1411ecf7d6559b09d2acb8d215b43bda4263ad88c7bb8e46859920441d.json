{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createTextVNode as _createTextVNode, withModifiers as _withModifiers, normalizeClass as _normalizeClass, vShow as _vShow, normalizeStyle as _normalizeStyle, withDirectives as _withDirectives, createBlock as _createBlock, resolveDirective as _resolveDirective } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalChatViewList forbidSelect\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalChatViewListHead\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalChatViewMessagesItem forbidSelect\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalChatViewMessagesInfo\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalChatViewMessagesName\"\n};\nvar _hoisted_6 = {\n  class: \"ellipsis\"\n};\nvar _hoisted_7 = [\"innerHTML\"];\nvar _hoisted_8 = {\n  key: 1,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_9 = {\n  key: 2,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_10 = {\n  key: 3,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_11 = {\n  key: 4,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_12 = {\n  key: 5,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_13 = {\n  key: 6,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_14 = {\n  key: 7,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_15 = [\"innerHTML\"];\nvar _hoisted_16 = [\"onClick\", \"onContextmenu\"];\nvar _hoisted_17 = {\n  class: \"GlobalChatViewMessagesInfo\"\n};\nvar _hoisted_18 = {\n  class: \"GlobalChatViewMessagesName\"\n};\nvar _hoisted_19 = {\n  key: 0,\n  class: \"ellipsis\"\n};\nvar _hoisted_20 = {\n  key: 1,\n  class: \"GlobalChatViewMessagesNameGroup ellipsis\"\n};\nvar _hoisted_21 = [\"innerHTML\"];\nvar _hoisted_22 = {\n  key: 1,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_23 = {\n  key: 2,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_24 = {\n  key: 3,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_25 = {\n  key: 4,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_26 = {\n  key: 5,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_27 = {\n  key: 6,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_28 = {\n  key: 7,\n  class: \"GlobalChatViewMessagesText ellipsis\"\n};\nvar _hoisted_29 = [\"innerHTML\"];\nvar _hoisted_30 = {\n  class: \"GlobalChatWindowTitle forbidSelect\"\n};\nvar _hoisted_31 = {\n  key: 0\n};\nvar _hoisted_32 = {\n  key: 0,\n  class: \"GlobalChatGroupAnnouncement\"\n};\nvar _hoisted_33 = {\n  class: \"GlobalChatGroupAnnouncementTitle\"\n};\nvar _hoisted_34 = [\"innerHTML\"];\nvar _hoisted_35 = {\n  class: \"GlobalChatGroupAnnouncementContent\"\n};\nvar _hoisted_36 = {\n  class: \"GlobalChatWindowBody\"\n};\nvar _hoisted_37 = {\n  class: \"GlobalChatWindowUserImg\"\n};\nvar _hoisted_38 = {\n  class: \"GlobalChatMessagesInfo\"\n};\nvar _hoisted_39 = [\"onContextmenu\"];\nvar _hoisted_40 = [\"onContextmenu\"];\nvar _hoisted_41 = [\"innerHTML\"];\nvar _hoisted_42 = [\"onContextmenu\", \"onClick\"];\nvar _hoisted_43 = [\"onClick\"];\nvar _hoisted_44 = [\"onContextmenu\"];\nvar _hoisted_45 = [\"onContextmenu\", \"onClick\"];\nvar _hoisted_46 = {\n  class: \"GlobalChatMessagesCustomName\"\n};\nvar _hoisted_47 = {\n  class: \"GlobalChatMessagesCustomText\"\n};\nvar _hoisted_48 = {\n  class: \"GlobalChatMessagesVoteTitleBody\"\n};\nvar _hoisted_49 = {\n  class: \"GlobalChatMessagesVoteTitle\"\n};\nvar _hoisted_50 = [\"innerHTML\"];\nvar _hoisted_51 = {\n  class: \"GlobalChatMessagesVoteTime\"\n};\nvar _hoisted_52 = {\n  class: \"GlobalChatMessagesVoteInfo\"\n};\nvar _hoisted_53 = [\"innerHTML\"];\nvar _hoisted_54 = {\n  class: \"GlobalChatMessagesVoteName\"\n};\nvar _hoisted_55 = {\n  key: 2,\n  class: \"GlobalChatMessagesCustomUnknown\"\n};\nvar _hoisted_56 = [\"onContextmenu\"];\nvar _hoisted_57 = {\n  class: \"GlobalChatMessagesFileName\"\n};\nvar _hoisted_58 = {\n  class: \"GlobalChatMessagesFileSize\"\n};\nvar _hoisted_59 = {\n  key: 0,\n  class: \"GlobalChatViewMenuItem\"\n};\nvar _hoisted_60 = {\n  key: 2,\n  class: \"GlobalChatViewMenuLine\"\n};\nvar _hoisted_61 = {\n  class: \"GlobalChatViewNoMessage\"\n};\nvar _hoisted_62 = {\n  key: 1,\n  class: \"GlobalChatViewDrag\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$setup$chatInfo$chat, _$setup$messagesMenuI, _$setup$messagesMenuI2, _$setup$chatType;\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_badge = _resolveComponent(\"el-badge\");\n  var _component_el_autocomplete = _resolveComponent(\"el-autocomplete\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_MoreFilled = _resolveComponent(\"MoreFilled\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_CircleCloseFilled = _resolveComponent(\"CircleCloseFilled\");\n  var _component_Warning = _resolveComponent(\"Warning\");\n  var _directive_copy = _resolveDirective(\"copy\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"GlobalChatView\", {\n      GlobalChatMacView: $setup.isMac\n    }]),\n    onClick: _withModifiers($setup.handleElClick, [\"prevent\"]),\n    onContextmenu: _withModifiers($setup.handleElContextmenu, [\"prevent\"])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_autocomplete, {\n    modelValue: $setup.keyword,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.keyword = $event;\n    }),\n    \"prefix-icon\": $setup.Search,\n    \"fetch-suggestions\": $setup.querySearch,\n    placeholder: \"搜索\",\n    \"popper-class\": \"GlobalChatViewAutocomplete\",\n    clearable: \"\",\n    onSelect: $setup.handleClick\n  }, {\n    default: _withCtx(function (_ref) {\n      var _item$chatObjectInfo2, _item$content, _item$content2, _item$content3, _item$content4;\n      var item = _ref.item;\n      return [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_badge, {\n        value: item.count,\n        hidden: !item.count,\n        \"is-dot\": item.isNotInform === 1\n      }, {\n        default: _withCtx(function () {\n          var _item$chatObjectInfo;\n          return [_createVNode(_component_el_image, {\n            src: $setup.imgUrl((_item$chatObjectInfo = item.chatObjectInfo) === null || _item$chatObjectInfo === void 0 ? void 0 : _item$chatObjectInfo.img),\n            fit: \"cover\",\n            draggable: \"false\"\n          }, null, 8 /* PROPS */, [\"src\"])];\n        }),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\", \"hidden\", \"is-dot\"]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString((_item$chatObjectInfo2 = item.chatObjectInfo) === null || _item$chatObjectInfo2 === void 0 ? void 0 : _item$chatObjectInfo2.name), 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString($setup.handleTimeFormat(item === null || item === void 0 ? void 0 : item.sentTime)), 1 /* TEXT */)]), item.isNotInform === 1 ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"GlobalChatViewNotInform\",\n        innerHTML: $setup.notificationIcon\n      }, null, 8 /* PROPS */, _hoisted_7)) : _createCommentVNode(\"v-if\", true), item.messageType === 'RC:ImgTextMsg' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, _toDisplayString(item === null || item === void 0 || (_item$content = item.content) === null || _item$content === void 0 ? void 0 : _item$content.title), 1 /* TEXT */)) : item.messageType === 'RC:ImgMsg' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, \" [图片] \")) : item.messageType === 'RC:HQVCMsg' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \" [语音] \" + _toDisplayString(item === null || item === void 0 || (_item$content2 = item.content) === null || _item$content2 === void 0 ? void 0 : _item$content2.duration) + \"\\\" \", 1 /* TEXT */)) : item.messageType === 'RC:CmdNtf' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, _toDisplayString(item === null || item === void 0 || (_item$content3 = item.content) === null || _item$content3 === void 0 ? void 0 : _item$content3.name), 1 /* TEXT */)) : item.messageType === 'RC:LBSMsg' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, \" [不支持的消息，请在移动端进行查看] \")) : item.messageType === 'RC:RcCmd' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, _toDisplayString(item.revocationMessage), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_14, _toDisplayString(item === null || item === void 0 || (_item$content4 = item.content) === null || _item$content4 === void 0 ? void 0 : _item$content4.content), 1 /* TEXT */))])])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"]), _createElementVNode(\"div\", {\n    class: \"GlobalChatViewListHeadIcon\",\n    innerHTML: $setup.initGroupChatIcon,\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.handleCreateGroup('');\n    })\n  }, null, 8 /* PROPS */, _hoisted_15)]), _createVNode(_component_el_scrollbar, {\n    class: \"GlobalChatViewMessagesList\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.chatList, function (item) {\n        var _item$chatObjectInfo4, _item$chatObjectInfo5, _item$chatObjectInfo6, _item$content5, _item$content6, _item$content7, _item$content8;\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: _normalizeClass(['GlobalChatViewMessagesItem', {\n            'is-top': item.isTop\n          }, {\n            'is-active': item.id === $setup.chatId\n          }]),\n          key: item.id,\n          onClick: function onClick($event) {\n            return $setup.handleClick(item);\n          },\n          onContextmenu: _withModifiers(function ($event) {\n            return $setup.handleChatMenu($event, item);\n          }, [\"prevent\"])\n        }, [_createVNode(_component_el_badge, {\n          value: item.count,\n          hidden: !item.count,\n          \"is-dot\": item.isNotInform === 1\n        }, {\n          default: _withCtx(function () {\n            var _item$chatObjectInfo3;\n            return [_createVNode(_component_el_image, {\n              src: $setup.imgUrl((_item$chatObjectInfo3 = item.chatObjectInfo) === null || _item$chatObjectInfo3 === void 0 ? void 0 : _item$chatObjectInfo3.img),\n              fit: \"cover\",\n              draggable: \"false\"\n            }, null, 8 /* PROPS */, [\"src\"])];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\", \"hidden\", \"is-dot\"]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [!((_item$chatObjectInfo4 = item.chatObjectInfo) !== null && _item$chatObjectInfo4 !== void 0 && _item$chatObjectInfo4.chatGroupType) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, _toDisplayString((_item$chatObjectInfo5 = item.chatObjectInfo) === null || _item$chatObjectInfo5 === void 0 ? void 0 : _item$chatObjectInfo5.name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.type === 3 && item.chatObjectInfo.chatGroupType ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createTextVNode(_toDisplayString((_item$chatObjectInfo6 = item.chatObjectInfo) === null || _item$chatObjectInfo6 === void 0 ? void 0 : _item$chatObjectInfo6.name) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString(item.chatObjectInfo.chatGroupType), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", null, _toDisplayString($setup.handleTimeFormat(item === null || item === void 0 ? void 0 : item.sentTime)), 1 /* TEXT */)]), item.isNotInform === 1 ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"GlobalChatViewNotInform\",\n          innerHTML: $setup.notificationIcon\n        }, null, 8 /* PROPS */, _hoisted_21)) : _createCommentVNode(\"v-if\", true), item.messageType === 'RC:ImgTextMsg' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, _toDisplayString(item === null || item === void 0 || (_item$content5 = item.content) === null || _item$content5 === void 0 ? void 0 : _item$content5.title), 1 /* TEXT */)) : item.messageType === 'RC:ImgMsg' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, \"[图片]\")) : item.messageType === 'RC:HQVCMsg' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, \" [语音] \" + _toDisplayString(item === null || item === void 0 || (_item$content6 = item.content) === null || _item$content6 === void 0 ? void 0 : _item$content6.duration) + \"\\\" \", 1 /* TEXT */)) : item.messageType === 'RC:CmdNtf' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, _toDisplayString(item === null || item === void 0 || (_item$content7 = item.content) === null || _item$content7 === void 0 ? void 0 : _item$content7.name), 1 /* TEXT */)) : item.messageType === 'RC:LBSMsg' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, \" [不支持的消息，请在移动端进行查看] \")) : item.messageType === 'RC:RcCmd' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, _toDisplayString(item.revocationMessage), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_28, _toDisplayString(item === null || item === void 0 || (_item$content8 = item.content) === null || _item$content8 === void 0 ? void 0 : _item$content8.content), 1 /* TEXT */))])], 42 /* CLASS, PROPS, NEED_HYDRATION */, _hoisted_16);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", {\n    class: \"GlobalChatClearAway\",\n    onClick: $setup.handleClearAway\n  }, [_createElementVNode(\"div\", {\n    innerHTML: $setup.clearAwayIcon\n  }, null, 8 /* PROPS */, _hoisted_29), _cache[20] || (_cache[20] = _createTextVNode(\" 清除未读 \"))]), _withDirectives(_createElementVNode(\"div\", {\n    class: \"GlobalChatViewMenu\",\n    style: _normalizeStyle({\n      left: $setup.chatMenuLeft + 'px',\n      top: $setup.chatMenuTop + 'px'\n    })\n  }, [!$setup.chatMenuItem.isTop ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"GlobalChatViewMenuItem\",\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      return $setup.handleIsTopClick(true);\n    })\n  }, \"置顶\")) : _createCommentVNode(\"v-if\", true), $setup.chatMenuItem.isTop ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"GlobalChatViewMenuItem\",\n    onClick: _cache[3] || (_cache[3] = function ($event) {\n      return $setup.handleIsTopClick(false);\n    })\n  }, \"取消置顶\")) : _createCommentVNode(\"v-if\", true), $setup.chatMenuItem.isNotInform === 2 ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 2,\n    class: \"GlobalChatViewMenuItem\",\n    onClick: _cache[4] || (_cache[4] = function ($event) {\n      return $setup.handleNotificationClick(1);\n    })\n  }, \" 消息免打扰 \")) : _createCommentVNode(\"v-if\", true), $setup.chatMenuItem.isNotInform === 1 ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 3,\n    class: \"GlobalChatViewMenuItem\",\n    onClick: _cache[5] || (_cache[5] = function ($event) {\n      return $setup.handleNotificationClick(2);\n    })\n  }, \" 允许消息通知 \")) : _createCommentVNode(\"v-if\", true), _cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    class: \"GlobalChatViewMenuLine\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", {\n    class: \"GlobalChatViewMenuItem\",\n    onClick: $setup.handleDelChat\n  }, \"删除\")], 4 /* STYLE */), [[_vShow, $setup.chatMenuShow]])]), $setup.chatId ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"GlobalChatWindow\",\n    onDragover: _cache[7] || (_cache[7] = _withModifiers(function () {}, [\"prevent\"])),\n    onDrop: $setup.handleDrop\n  }, [_createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", {\n    class: \"ellipsis\",\n    onClick: $setup.handleSetting\n  }, [_createTextVNode(_toDisplayString((_$setup$chatInfo$chat = $setup.chatInfo.chatObjectInfo) === null || _$setup$chatInfo$chat === void 0 ? void 0 : _$setup$chatInfo$chat.name) + \" \", 1 /* TEXT */), $setup.chatInfo.type === 3 && $setup.groupUser.length ? (_openBlock(), _createElementBlock(\"span\", _hoisted_31, \"（\" + _toDisplayString($setup.groupUser.length) + \"）\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", {\n    class: \"GlobalChatWindowMore\",\n    onClick: $setup.handleSetting\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_MoreFilled)];\n    }),\n    _: 1 /* STABLE */\n  })])]), _createVNode(_component_el_scrollbar, {\n    ref: \"scrollRef\",\n    always: \"\",\n    class: _normalizeClass([\"GlobalChatWindowScroll\", {\n      GlobalChatWindowNoChat: !$setup.isChat\n    }]),\n    onScroll: $setup.handleMessagesScroll\n  }, {\n    default: _withCtx(function () {\n      return [$setup.isChatGroupAnnouncement ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n        innerHTML: $setup.announcementIcon\n      }, null, 8 /* PROPS */, _hoisted_34), _cache[22] || (_cache[22] = _createTextVNode(\" 群公告 \"))]), _createVNode(_component_el_icon, {\n        onClick: $setup.handleChatGroupAnnouncement\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_CircleCloseFilled)];\n        }),\n        _: 1 /* STABLE */\n      })]), _createElementVNode(\"div\", _hoisted_35, _toDisplayString($setup.chatGroupAnnouncement), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_36, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.chatInfoMessagesData, function (item) {\n        var _item$content9, _item$chatObjectInfo7, _item$chatObjectInfo8, _item$content10, _item$content11, _item$content12, _item$content13, _ref2, _$setup$isElectronFil, _item$file, _item$file2, _item$file3, _item$vote, _item$vote2, _item$content14, _item$content15, _item$content16, _item$content17;\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: item.id || item.uid,\n          class: _normalizeClass(item.className)\n        }, [item.type === 'time' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 0\n        }, [_createTextVNode(_toDisplayString(item === null || item === void 0 ? void 0 : item.content), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : item.type === 'RC:CmdNtf' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 1\n        }, [_createTextVNode(_toDisplayString(item === null || item === void 0 || (_item$content9 = item.content) === null || _item$content9 === void 0 ? void 0 : _item$content9.name), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : item.type === 'RC:RcCmd' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 2\n        }, [_createTextVNode(_toDisplayString(item.chatObjectInfoType ? '你' : item.userName) + \"撤回了一条消息 \", 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n          key: 3\n        }, [_createElementVNode(\"div\", _hoisted_37, [_createVNode(_component_el_image, {\n          src: $setup.imgUrl((_item$chatObjectInfo7 = item.chatObjectInfo) === null || _item$chatObjectInfo7 === void 0 ? void 0 : _item$chatObjectInfo7.img),\n          fit: \"cover\",\n          draggable: \"false\"\n        }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_38, [!item.chatObjectInfoType ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"GlobalChatMessagesName ellipsis\",\n          onContextmenu: _withModifiers(function ($event) {\n            return $setup.handleMessagesMenu($event, item);\n          }, [\"prevent\"])\n        }, _toDisplayString((_item$chatObjectInfo8 = item.chatObjectInfo) === null || _item$chatObjectInfo8 === void 0 ? void 0 : _item$chatObjectInfo8.name), 41 /* TEXT, PROPS, NEED_HYDRATION */, _hoisted_39)) : _createCommentVNode(\"v-if\", true), item.type === 'RC:TxtMsg' ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 1,\n          class: \"GlobalChatMessagesText\",\n          onContextmenu: _withModifiers(function ($event) {\n            return $setup.handleMessagesMenu($event, item);\n          }, [\"prevent\"])\n        }, [_cache[23] || (_cache[23] = _createElementVNode(\"span\", null, null, -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: \"GlobalChatEmotion\",\n          innerHTML: item === null || item === void 0 || (_item$content10 = item.content) === null || _item$content10 === void 0 ? void 0 : _item$content10.htmlContent\n        }, null, 8 /* PROPS */, _hoisted_41)], 40 /* PROPS, NEED_HYDRATION */, _hoisted_40)) : _createCommentVNode(\"v-if\", true), item.type === 'RC:HQVCMsg' ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 2,\n          class: \"GlobalChatMessagesText\",\n          onContextmenu: _withModifiers(function ($event) {\n            return $setup.handleMessagesMenu($event, item);\n          }, [\"prevent\"]),\n          onClick: function onClick($event) {\n            return $setup.handleAudio(item);\n          }\n        }, [_cache[24] || (_cache[24] = _createElementVNode(\"span\", null, null, -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: _normalizeClass(['GlobalChatVoice', {\n            'is-active': $setup.chatInfoAudio.id === item.id\n          }]),\n          style: _normalizeStyle({\n            width: `${88 + ((item === null || item === void 0 || (_item$content11 = item.content) === null || _item$content11 === void 0 ? void 0 : _item$content11.duration) || 0)}px`\n          })\n        }, _toDisplayString(item === null || item === void 0 || (_item$content12 = item.content) === null || _item$content12 === void 0 ? void 0 : _item$content12.duration) + \"\\\" \", 7 /* TEXT, CLASS, STYLE */), $setup.chatInfoAudio.id !== item.id && $setup.chatInfoAudioObj[item.id] ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"GlobalChatVoiceContinue\",\n          onClick: _withModifiers(function ($event) {\n            return $setup.handleGoAudio(item);\n          }, [\"stop\"])\n        }, \" 继续播放 \", 8 /* PROPS */, _hoisted_43)) : _createCommentVNode(\"v-if\", true)], 40 /* PROPS, NEED_HYDRATION */, _hoisted_42)) : _createCommentVNode(\"v-if\", true), item.type === 'RC:ImgMsg' ? (_openBlock(), _createBlock(_component_el_image, {\n          key: 3,\n          src: item === null || item === void 0 || (_item$content13 = item.content) === null || _item$content13 === void 0 ? void 0 : _item$content13.imageUri,\n          fit: \"cover\",\n          \"preview-src-list\": $setup.chatInfoImg,\n          \"initial-index\": item.imgIndex,\n          onLoad: $setup.handleImgLoad,\n          onContextmenu: _withModifiers(function ($event) {\n            return $setup.handleMessagesMenu($event, item);\n          }, [\"prevent\"])\n        }, {\n          error: _withCtx(function () {\n            return [_createElementVNode(\"div\", {\n              class: \"GlobalChatMessagesImgSlot\",\n              onContextmenu: _withModifiers(function ($event) {\n                return $setup.handleMessagesMenu($event, item);\n              }, [\"prevent\"])\n            }, [_createVNode(_component_el_icon, null, {\n              default: _withCtx(function () {\n                return [_createVNode($setup[\"Picture\"])];\n              }),\n              _: 1 /* STABLE */\n            })], 40 /* PROPS, NEED_HYDRATION */, _hoisted_44)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"src\", \"preview-src-list\", \"initial-index\", \"onContextmenu\"])) : _createCommentVNode(\"v-if\", true), (_ref2 = ['RC:ImgTextMsg', 'RC:LBSMsg']) !== null && _ref2 !== void 0 && _ref2.includes(item.type) ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 4,\n          class: _normalizeClass(['GlobalChatMessagesCustom', item.customType]),\n          onContextmenu: _withModifiers(function ($event) {\n            return $setup.handleMessagesMenu($event, item);\n          }, [\"prevent\"]),\n          onClick: function onClick($event) {\n            return $setup.handleCustom(item);\n          }\n        }, [_cache[26] || (_cache[26] = _createElementVNode(\"span\", null, null, -1 /* HOISTED */)), item.customType === 'file' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 0\n        }, [(_$setup$isElectronFil = $setup.isElectronFile) !== null && _$setup$isElectronFil !== void 0 && _$setup$isElectronFil.includes(item.file.id) ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"GlobalChatMessagesFileDownload\",\n          style: _normalizeStyle({\n            width: $setup.isElectronFileObj[item.file.id]\n          })\n        }, null, 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon((_item$file = item.file) === null || _item$file === void 0 ? void 0 : _item$file.extName)])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_46, _toDisplayString(((_item$file2 = item.file) === null || _item$file2 === void 0 ? void 0 : _item$file2.originalFileName) || '未知文件'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_47, _toDisplayString((_item$file3 = item.file) !== null && _item$file3 !== void 0 && _item$file3.fileSize ? $setup.size2Str(item.file.fileSize) : '0KB'), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), item.customType === 'vote' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 1\n        }, [_createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"div\", {\n          class: \"GlobalChatMessagesVoteIcon\",\n          innerHTML: $setup.voteListIcon\n        }, null, 8 /* PROPS */, _hoisted_50), _cache[25] || (_cache[25] = _createTextVNode(\" 投票 \"))]), _createElementVNode(\"div\", _hoisted_51, _toDisplayString($setup.format((_item$vote = item.vote) === null || _item$vote === void 0 ? void 0 : _item$vote.createDate)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"div\", {\n          class: \"GlobalChatMessagesVoteInfoIcon\",\n          innerHTML: $setup.voteBgIcon\n        }, null, 8 /* PROPS */, _hoisted_53), _createElementVNode(\"div\", _hoisted_54, _toDisplayString((_item$vote2 = item.vote) === null || _item$vote2 === void 0 ? void 0 : _item$vote2.topic), 1 /* TEXT */)])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), item.customType === 'unknown' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_55, \"[不支持的消息，请在移动端进行查看]\")) : _createCommentVNode(\"v-if\", true)], 42 /* CLASS, PROPS, NEED_HYDRATION */, _hoisted_45)) : _createCommentVNode(\"v-if\", true), item.type === 'RC:FileMsg' ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 5,\n          class: \"GlobalChatMessagesFile\",\n          onContextmenu: _withModifiers(function ($event) {\n            return $setup.handleMessagesMenu($event, item);\n          }, [\"prevent\"])\n        }, [_cache[27] || (_cache[27] = _createElementVNode(\"span\", null, null, -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon(item === null || item === void 0 || (_item$content14 = item.content) === null || _item$content14 === void 0 ? void 0 : _item$content14.type)])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_57, _toDisplayString((item === null || item === void 0 || (_item$content15 = item.content) === null || _item$content15 === void 0 ? void 0 : _item$content15.name) || '未知文件'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_58, _toDisplayString(item !== null && item !== void 0 && (_item$content16 = item.content) !== null && _item$content16 !== void 0 && _item$content16.size ? $setup.size2Str(item === null || item === void 0 || (_item$content17 = item.content) === null || _item$content17 === void 0 ? void 0 : _item$content17.size) : '0KB'), 1 /* TEXT */)], 40 /* PROPS, NEED_HYDRATION */, _hoisted_56)) : _createCommentVNode(\"v-if\", true)])], 64 /* STABLE_FRAGMENT */))], 2 /* CLASS */);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _withDirectives(_createElementVNode(\"div\", {\n    class: \"GlobalChatViewMenu\",\n    style: _normalizeStyle({\n      left: $setup.messagesMenuLeft + 'px',\n      top: $setup.messagesMenuTop + 'px'\n    })\n  }, [_createCommentVNode(\" <div class=\\\"GlobalChatViewMenuItem\\\" v-if=\\\"chatType?.includes(messagesMenuItem.type)\\\">转发</div> \"), _createCommentVNode(\" <div class=\\\"GlobalChatViewMenuItem\\\">收藏</div> \"), $setup.messagesMenuItem.type === 'RC:TxtMsg' ? _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_59, _cache[28] || (_cache[28] = [_createTextVNode(\" 复制 \")]))), [[_directive_copy, (_$setup$messagesMenuI = $setup.messagesMenuItem) === null || _$setup$messagesMenuI === void 0 ? void 0 : _$setup$messagesMenuI.copyContent]]) : _createCommentVNode(\"v-if\", true), $setup.isElectron && ((_$setup$messagesMenuI2 = $setup.messagesMenuItem) === null || _$setup$messagesMenuI2 === void 0 ? void 0 : _$setup$messagesMenuI2.customType) === 'file' ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"GlobalChatViewMenuItem\",\n    onClick: $setup.handleFolderSelectFile\n  }, _toDisplayString($setup.isMacText() ? '在 Finder 中显示' : '在资源管理器中显示'), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), (_$setup$chatType = $setup.chatType) !== null && _$setup$chatType !== void 0 && _$setup$chatType.includes($setup.messagesMenuItem.type) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_60)) : _createCommentVNode(\"v-if\", true), !$setup.messagesMenuItem.chatObjectInfoType || !$setup.messagesMenuItem.isWithdraw ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 3,\n    class: \"GlobalChatViewMenuItem\",\n    onClick: $setup.handleDelMessage\n  }, \" 删除 \")) : _createCommentVNode(\"v-if\", true), $setup.messagesMenuItem.chatObjectInfoType && $setup.messagesMenuItem.isWithdraw ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 4,\n    class: \"GlobalChatViewMenuItem\",\n    onClick: $setup.handleWithdrawMessage\n  }, \" 撤回 \")) : _createCommentVNode(\"v-if\", true)], 4 /* STYLE */), [[_vShow, $setup.messagesMenuShow]]), _withDirectives(_createVNode($setup[\"GlobalChatEditor\"], {\n    ref: \"editorRef\",\n    isVote: $setup.chatInfo.type === 3,\n    userData: $setup.groupUser,\n    onHandleFile: $setup.fileUpload,\n    onHandleVote: $setup.handleVote,\n    onHandlePasteImg: $setup.handlePasteImg,\n    onHandleSendMessage: $setup.handleKeyCode\n  }, null, 8 /* PROPS */, [\"isVote\", \"userData\"]), [[_vShow, $setup.isChat]]), _withDirectives(_createElementVNode(\"div\", _hoisted_61, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Warning)];\n    }),\n    _: 1 /* STABLE */\n  }), _cache[29] || (_cache[29] = _createTextVNode(\" 无法在已退出的群聊中接收和发送消息 \"))], 512 /* NEED_PATCH */), [[_vShow, !$setup.isChat]]), _createVNode($setup[\"SettingPopupWindow\"], {\n    modelValue: $setup.settingShow,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n      return $setup.settingShow = $event;\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalChatViewWindow\"], {\n        chatInfo: $setup.chatInfo,\n        groupUser: $setup.groupUser,\n        onRefresh: $setup.handleRefresh,\n        onCallback: $setup.handleGroup\n      }, null, 8 /* PROPS */, [\"chatInfo\", \"groupUser\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])], 32 /* NEED_HYDRATION */)) : _createCommentVNode(\"v-if\", true), !$setup.chatId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_62)) : _createCommentVNode(\"v-if\", true), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.fileShow,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n      return $setup.fileShow = $event;\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"ChatSendFile\"], {\n        chatInfo: $setup.chatInfo,\n        fileList: $setup.fileList,\n        onCallback: $setup.fileCallback\n      }, null, 8 /* PROPS */, [\"chatInfo\", \"fileList\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.imgShow,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n      return $setup.imgShow = $event;\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"ChatSendImg\"], {\n        chatInfo: $setup.chatInfo,\n        fileImg: $setup.fileImg,\n        onCallback: $setup.imgCallback\n      }, null, 8 /* PROPS */, [\"chatInfo\", \"fileImg\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.createGroupShow,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = function ($event) {\n      return $setup.createGroupShow = $event;\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalCreateGroup\"], {\n        userId: $setup.userId,\n        onCallback: $setup.createCallback\n      }, null, 8 /* PROPS */, [\"userId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.addShow,\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = function ($event) {\n      return $setup.addShow = $event;\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalGroupAddUser\"], {\n        infoId: $setup.infoId,\n        onCallback: $setup.addCallback\n      }, null, 8 /* PROPS */, [\"infoId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.delShow,\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = function ($event) {\n      return $setup.delShow = $event;\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalGroupDelUser\"], {\n        infoId: $setup.infoId,\n        onCallback: $setup.delCallback\n      }, null, 8 /* PROPS */, [\"infoId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.nameShow,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = function ($event) {\n      return $setup.nameShow = $event;\n    }),\n    class: \"GlobalGroupNamePopupWindow\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalGroupName\"], {\n        infoId: $setup.infoId,\n        onCallback: $setup.nameCallback\n      }, null, 8 /* PROPS */, [\"infoId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.qrShow,\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = function ($event) {\n      return $setup.qrShow = $event;\n    }),\n    class: \"GlobalGroupQrPopupWindow\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalGroupQr\"], {\n        infoId: $setup.infoId\n      }, null, 8 /* PROPS */, [\"infoId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.announcementShow,\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = function ($event) {\n      return $setup.announcementShow = $event;\n    }),\n    class: \"GlobalGroupAnnouncementPopupWindow\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalGroupAnnouncement\"], {\n        infoId: $setup.infoId,\n        isOwner: $setup.isGroupOwner,\n        onCallback: $setup.announcementCallback\n      }, null, 8 /* PROPS */, [\"infoId\", \"isOwner\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.transferShow,\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = function ($event) {\n      return $setup.transferShow = $event;\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalGroupTransfer\"], {\n        infoId: $setup.infoId,\n        onCallback: $setup.transferCallback\n      }, null, 8 /* PROPS */, [\"infoId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.voteShow,\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = function ($event) {\n      return $setup.voteShow = $event;\n    }),\n    class: \"GlobalGroupVotePopupWindow\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalGroupVote\"], {\n        id: $setup.infoId,\n        refresh: $setup.voteRefresh,\n        onCallback: $setup.voteCallback,\n        onSendMessage: $setup.handleSendCustomMessage\n      }, null, 8 /* PROPS */, [\"id\", \"refresh\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.createVoteShow,\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = function ($event) {\n      return $setup.createVoteShow = $event;\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalCreateVote\"], {\n        dataId: $setup.infoId,\n        onCallback: $setup.handleVoteCallback\n      }, null, 8 /* PROPS */, [\"dataId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode($setup[\"ChatPopupWindow\"], {\n    modelValue: $setup.voteDetailsShow,\n    \"onUpdate:modelValue\": _cache[19] || (_cache[19] = function ($event) {\n      return $setup.voteDetailsShow = $event;\n    }),\n    class: \"GlobalGroupVotePopupWindow\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalVoteDetails\"], {\n        id: $setup.voteId,\n        onCallback: $setup.handleVoteCallback,\n        onSendMessage: $setup.handleSendCustomMessage\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])], 34 /* CLASS, NEED_HYDRATION */);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_normalizeClass", "GlobalChatMacView", "$setup", "isMac", "onClick", "_withModifiers", "handleElClick", "onContextmenu", "handleElContextmenu", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_autocomplete", "modelValue", "keyword", "_cache", "$event", "Search", "querySearch", "placeholder", "clearable", "onSelect", "handleClick", "default", "_withCtx", "_ref", "_item$chatObjectInfo2", "_item$content", "_item$content2", "_item$content3", "_item$content4", "item", "_hoisted_3", "_component_el_badge", "value", "count", "hidden", "isNotInform", "_item$chatObjectInfo", "_component_el_image", "src", "imgUrl", "chatObjectInfo", "img", "fit", "draggable", "_", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_toDisplayString", "name", "handleTimeFormat", "sentTime", "innerHTML", "notificationIcon", "_hoisted_7", "_createCommentVNode", "messageType", "_hoisted_8", "content", "title", "_hoisted_9", "_hoisted_10", "duration", "_hoisted_11", "_hoisted_12", "_hoisted_13", "revocationMessage", "_hoisted_14", "initGroupChatIcon", "handleCreateGroup", "_hoisted_15", "_component_el_scrollbar", "_Fragment", "_renderList", "chatList", "_item$chatObjectInfo4", "_item$chatObjectInfo5", "_item$chatObjectInfo6", "_item$content5", "_item$content6", "_item$content7", "_item$content8", "isTop", "id", "chatId", "handleChatMenu", "_item$chatObjectInfo3", "_hoisted_17", "_hoisted_18", "chatGroupType", "_hoisted_19", "type", "_hoisted_20", "_createTextVNode", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_16", "handleClearAway", "clearAwayIcon", "_hoisted_29", "style", "_normalizeStyle", "left", "chatMenuLeft", "top", "chatMenuTop", "chatMenuItem", "handleIsTopClick", "handleNotificationClick", "handleDelChat", "chatMenuShow", "onDragover", "onDrop", "handleDrop", "_hoisted_30", "handleSetting", "_$setup$chatInfo$chat", "chatInfo", "groupUser", "length", "_hoisted_31", "_component_el_icon", "_component_MoreFilled", "ref", "always", "GlobalChatWindowNoChat", "isChat", "onScroll", "handleMessagesScroll", "isChatGroupAnnouncement", "_hoisted_32", "_hoisted_33", "announcementIcon", "_hoisted_34", "handleChatGroupAnnouncement", "_component_CircleCloseFilled", "_hoisted_35", "chatGroupAnnouncement", "_hoisted_36", "chatInfoMessagesData", "_item$content9", "_item$chatObjectInfo7", "_item$chatObjectInfo8", "_item$content10", "_item$content11", "_item$content12", "_item$content13", "_ref2", "_$setup$isElectronFil", "_item$file", "_item$file2", "_item$file3", "_item$vote", "_item$vote2", "_item$content14", "_item$content15", "_item$content16", "_item$content17", "uid", "className", "chatObjectInfoType", "userName", "_hoisted_37", "_hoisted_38", "handleMessagesMenu", "_hoisted_39", "htmlContent", "_hoisted_41", "_hoisted_40", "handleAudio", "chatInfoAudio", "width", "chatInfoAudioObj", "handleGoAudio", "_hoisted_43", "_hoisted_42", "_createBlock", "imageUri", "chatInfoImg", "imgIndex", "onLoad", "handleImgLoad", "error", "_hoisted_44", "includes", "customType", "handleCustom", "isElectronFile", "file", "isElectronFileObj", "fileIcon", "extName", "_hoisted_46", "originalFileName", "_hoisted_47", "fileSize", "size2Str", "_hoisted_48", "_hoisted_49", "voteListIcon", "_hoisted_50", "_hoisted_51", "format", "vote", "createDate", "_hoisted_52", "voteBgIcon", "_hoisted_53", "_hoisted_54", "topic", "_hoisted_55", "_hoisted_45", "_hoisted_57", "_hoisted_58", "size", "_hoisted_56", "messagesMenuLeft", "messagesMenuTop", "messagesMenuItem", "_hoisted_59", "_$setup$messagesMenuI", "copyContent", "isElectron", "_$setup$messagesMenuI2", "handleFolderSelectFile", "isMacText", "chatType", "_$setup$chatType", "_hoisted_60", "isWithdraw", "handleDelMessage", "handleWithdrawMessage", "messagesMenuShow", "isVote", "userData", "onHandleFile", "fileUpload", "onHandleVote", "handleVote", "onHandlePasteImg", "handlePasteImg", "onHandleSendMessage", "handleKeyCode", "_hoisted_61", "_component_Warning", "settingShow", "onRefresh", "handleRefresh", "onCallback", "handleGroup", "_hoisted_62", "fileShow", "fileList", "fileCallback", "imgShow", "fileImg", "imgCallback", "createGroupShow", "userId", "createCallback", "addShow", "infoId", "addCallback", "delShow", "del<PERSON><PERSON><PERSON>", "nameShow", "nameCallback", "qrShow", "announcementShow", "isOwner", "isGroupOwner", "announcementCallback", "transferShow", "transferCallback", "voteShow", "refresh", "voteRefresh", "voteCallback", "onSendMessage", "handleSendCustomMessage", "createVoteShow", "dataId", "handleVoteCallback", "voteDetailsShow", "voteId"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalChatView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatView\" :class=\"{ GlobalChatMacView: isMac }\" @click.prevent=\"handleElClick\"\r\n    @contextmenu.prevent=\"handleElContextmenu\">\r\n    <div class=\"GlobalChatViewList forbidSelect\">\r\n      <div class=\"GlobalChatViewListHead\">\r\n        <el-autocomplete v-model=\"keyword\" :prefix-icon=\"Search\" :fetch-suggestions=\"querySearch\" placeholder=\"搜索\"\r\n          popper-class=\"GlobalChatViewAutocomplete\" clearable @select=\"handleClick\">\r\n          <template #default=\"{ item }\">\r\n            <div class=\"GlobalChatViewMessagesItem forbidSelect\">\r\n              <el-badge :value=\"item.count\" :hidden=\"!item.count\" :is-dot=\"item.isNotInform === 1\">\r\n                <el-image :src=\"imgUrl(item.chatObjectInfo?.img)\" fit=\"cover\" draggable=\"false\" />\r\n              </el-badge>\r\n              <div class=\"GlobalChatViewMessagesInfo\">\r\n                <div class=\"GlobalChatViewMessagesName\">\r\n                  <div class=\"ellipsis\">{{ item.chatObjectInfo?.name }}</div>\r\n                  <span>{{ handleTimeFormat(item?.sentTime) }}</span>\r\n                </div>\r\n                <div class=\"GlobalChatViewNotInform\" v-html=\"notificationIcon\" v-if=\"item.isNotInform === 1\"></div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-if=\"item.messageType === 'RC:ImgTextMsg'\">\r\n                  {{ item?.content?.title }}\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:ImgMsg'\">\r\n                  [图片]\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:HQVCMsg'\">\r\n                  [语音] {{ item?.content?.duration }}\"\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:CmdNtf'\">\r\n                  {{ item?.content?.name }}\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:LBSMsg'\">\r\n                  [不支持的消息，请在移动端进行查看]\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:RcCmd'\">\r\n                  {{ item.revocationMessage }}\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else>{{ item?.content?.content }}</div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-autocomplete>\r\n        <div class=\"GlobalChatViewListHeadIcon\" v-html=\"initGroupChatIcon\" @click=\"handleCreateGroup('')\"></div>\r\n      </div>\r\n      <el-scrollbar class=\"GlobalChatViewMessagesList\">\r\n        <div :class=\"['GlobalChatViewMessagesItem', { 'is-top': item.isTop }, { 'is-active': item.id === chatId }]\"\r\n          v-for=\"item in chatList\" :key=\"item.id\" @click=\"handleClick(item)\"\r\n          @contextmenu.prevent=\"handleChatMenu($event, item)\">\r\n          <el-badge :value=\"item.count\" :hidden=\"!item.count\" :is-dot=\"item.isNotInform === 1\">\r\n            <el-image :src=\"imgUrl(item.chatObjectInfo?.img)\" fit=\"cover\" draggable=\"false\" />\r\n          </el-badge>\r\n          <div class=\"GlobalChatViewMessagesInfo\">\r\n            <div class=\"GlobalChatViewMessagesName\">\r\n              <div class=\"ellipsis\" v-if=\"!item.chatObjectInfo?.chatGroupType\">{{ item.chatObjectInfo?.name }}</div>\r\n              <div class=\"GlobalChatViewMessagesNameGroup ellipsis\"\r\n                v-if=\"item.type === 3 && item.chatObjectInfo.chatGroupType\">\r\n                {{ item.chatObjectInfo?.name }}\r\n                <span>{{ item.chatObjectInfo.chatGroupType }}</span>\r\n              </div>\r\n              <span>{{ handleTimeFormat(item?.sentTime) }}</span>\r\n            </div>\r\n            <div class=\"GlobalChatViewNotInform\" v-html=\"notificationIcon\" v-if=\"item.isNotInform === 1\"></div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-if=\"item.messageType === 'RC:ImgTextMsg'\">\r\n              {{ item?.content?.title }}\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:ImgMsg'\">[图片]</div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:HQVCMsg'\">\r\n              [语音] {{ item?.content?.duration }}\"\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:CmdNtf'\">\r\n              {{ item?.content?.name }}\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:LBSMsg'\">\r\n              [不支持的消息，请在移动端进行查看]\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:RcCmd'\">\r\n              {{ item.revocationMessage }}\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else>{{ item?.content?.content }}</div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <div class=\"GlobalChatClearAway\" @click=\"handleClearAway\">\r\n        <div v-html=\"clearAwayIcon\"></div>\r\n        清除未读\r\n      </div>\r\n      <div class=\"GlobalChatViewMenu\" :style=\"{ left: chatMenuLeft + 'px', top: chatMenuTop + 'px' }\"\r\n        v-show=\"chatMenuShow\">\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleIsTopClick(true)\" v-if=\"!chatMenuItem.isTop\">置顶</div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleIsTopClick(false)\" v-if=\"chatMenuItem.isTop\">取消置顶</div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleNotificationClick(1)\" v-if=\"chatMenuItem.isNotInform === 2\">\r\n          消息免打扰\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleNotificationClick(2)\" v-if=\"chatMenuItem.isNotInform === 1\">\r\n          允许消息通知\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuLine\"></div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleDelChat\">删除</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"GlobalChatWindow\" @dragover.prevent @drop=\"handleDrop\" v-if=\"chatId\">\r\n      <div class=\"GlobalChatWindowTitle forbidSelect\">\r\n        <div class=\"ellipsis\" @click=\"handleSetting\">\r\n          {{ chatInfo.chatObjectInfo?.name }}\r\n          <span v-if=\"chatInfo.type === 3 && groupUser.length\">（{{ groupUser.length }}）</span>\r\n        </div>\r\n        <div class=\"GlobalChatWindowMore\" @click=\"handleSetting\">\r\n          <el-icon>\r\n            <MoreFilled />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n      <el-scrollbar ref=\"scrollRef\" always class=\"GlobalChatWindowScroll\" :class=\"{ GlobalChatWindowNoChat: !isChat }\"\r\n        @scroll=\"handleMessagesScroll\">\r\n        <div class=\"GlobalChatGroupAnnouncement\" v-if=\"isChatGroupAnnouncement\">\r\n          <div class=\"GlobalChatGroupAnnouncementTitle\">\r\n            <div>\r\n              <span v-html=\"announcementIcon\"></span>\r\n              群公告\r\n            </div>\r\n            <el-icon @click=\"handleChatGroupAnnouncement\">\r\n              <CircleCloseFilled />\r\n            </el-icon>\r\n          </div>\r\n          <div class=\"GlobalChatGroupAnnouncementContent\">{{ chatGroupAnnouncement }}</div>\r\n        </div>\r\n        <div class=\"GlobalChatWindowBody\">\r\n          <div v-for=\"item in chatInfoMessagesData\" :key=\"item.id || item.uid\" :class=\"item.className\">\r\n            <template v-if=\"item.type === 'time'\">{{ item?.content }}</template>\r\n            <template v-else-if=\"item.type === 'RC:CmdNtf'\">{{ item?.content?.name }}</template>\r\n            <template v-else-if=\"item.type === 'RC:RcCmd'\">\r\n              {{ item.chatObjectInfoType ? '你' : item.userName }}撤回了一条消息\r\n            </template>\r\n            <template v-else>\r\n              <div class=\"GlobalChatWindowUserImg\">\r\n                <el-image :src=\"imgUrl(item.chatObjectInfo?.img)\" fit=\"cover\" draggable=\"false\" />\r\n              </div>\r\n              <div class=\"GlobalChatMessagesInfo\">\r\n                <div class=\"GlobalChatMessagesName ellipsis\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\"\r\n                  v-if=\"!item.chatObjectInfoType\">\r\n                  {{ item.chatObjectInfo?.name }}\r\n                </div>\r\n                <div class=\"GlobalChatMessagesText\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\"\r\n                  v-if=\"item.type === 'RC:TxtMsg'\">\r\n                  <span></span>\r\n                  <div class=\"GlobalChatEmotion\" v-html=\"item?.content?.htmlContent\"></div>\r\n                </div>\r\n                <div class=\"GlobalChatMessagesText\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\"\r\n                  @click=\"handleAudio(item)\" v-if=\"item.type === 'RC:HQVCMsg'\">\r\n                  <span></span>\r\n                  <div :class=\"['GlobalChatVoice', { 'is-active': chatInfoAudio.id === item.id }]\"\r\n                    :style=\"{ width: `${88 + (item?.content?.duration || 0)}px` }\">\r\n                    {{ item?.content?.duration }}\"\r\n                  </div>\r\n                  <div class=\"GlobalChatVoiceContinue\" @click.stop=\"handleGoAudio(item)\"\r\n                    v-if=\"chatInfoAudio.id !== item.id && chatInfoAudioObj[item.id]\">\r\n                    继续播放\r\n                  </div>\r\n                </div>\r\n                <el-image :src=\"item?.content?.imageUri\" fit=\"cover\" :preview-src-list=\"chatInfoImg\"\r\n                  :initial-index=\"item.imgIndex\" @load=\"handleImgLoad\"\r\n                  @contextmenu.prevent=\"handleMessagesMenu($event, item)\" v-if=\"item.type === 'RC:ImgMsg'\">\r\n                  <template #error>\r\n                    <div class=\"GlobalChatMessagesImgSlot\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\">\r\n                      <el-icon>\r\n                        <Picture />\r\n                      </el-icon>\r\n                    </div>\r\n                  </template>\r\n                </el-image>\r\n                <div :class=\"['GlobalChatMessagesCustom', item.customType]\"\r\n                  @contextmenu.prevent=\"handleMessagesMenu($event, item)\" @click=\"handleCustom(item)\"\r\n                  v-if=\"['RC:ImgTextMsg', 'RC:LBSMsg']?.includes(item.type)\">\r\n                  <span></span>\r\n                  <template v-if=\"item.customType === 'file'\">\r\n                    <div class=\"GlobalChatMessagesFileDownload\" :style=\"{ width: isElectronFileObj[item.file.id] }\"\r\n                      v-if=\"isElectronFile?.includes(item.file.id)\"></div>\r\n                    <div class=\"globalFileIcon\" :class=\"fileIcon(item.file?.extName)\"></div>\r\n                    <div class=\"GlobalChatMessagesCustomName\">{{ item.file?.originalFileName || '未知文件' }}</div>\r\n                    <div class=\"GlobalChatMessagesCustomText\">\r\n                      {{ item.file?.fileSize ? size2Str(item.file.fileSize) : '0KB' }}\r\n                    </div>\r\n                  </template>\r\n                  <template v-if=\"item.customType === 'vote'\">\r\n                    <div class=\"GlobalChatMessagesVoteTitleBody\">\r\n                      <div class=\"GlobalChatMessagesVoteTitle\">\r\n                        <div class=\"GlobalChatMessagesVoteIcon\" v-html=\"voteListIcon\"></div>\r\n                        投票\r\n                      </div>\r\n                      <div class=\"GlobalChatMessagesVoteTime\">{{ format(item.vote?.createDate) }}</div>\r\n                    </div>\r\n                    <div class=\"GlobalChatMessagesVoteInfo\">\r\n                      <div class=\"GlobalChatMessagesVoteInfoIcon\" v-html=\"voteBgIcon\"></div>\r\n                      <div class=\"GlobalChatMessagesVoteName\">{{ item.vote?.topic }}</div>\r\n                    </div>\r\n                  </template>\r\n                  <template v-if=\"item.customType === 'unknown'\">\r\n                    <div class=\"GlobalChatMessagesCustomUnknown\">[不支持的消息，请在移动端进行查看]</div>\r\n                  </template>\r\n                </div>\r\n                <div class=\"GlobalChatMessagesFile\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\"\r\n                  v-if=\"item.type === 'RC:FileMsg'\">\r\n                  <span></span>\r\n                  <div class=\"globalFileIcon\" :class=\"fileIcon(item?.content?.type)\"></div>\r\n                  <div class=\"GlobalChatMessagesFileName\">{{ item?.content?.name || '未知文件' }}</div>\r\n                  <div class=\"GlobalChatMessagesFileSize\">\r\n                    {{ item?.content?.size ? size2Str(item?.content?.size) : '0KB' }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <div class=\"GlobalChatViewMenu\" :style=\"{ left: messagesMenuLeft + 'px', top: messagesMenuTop + 'px' }\"\r\n        v-show=\"messagesMenuShow\">\r\n        <!-- <div class=\"GlobalChatViewMenuItem\" v-if=\"chatType?.includes(messagesMenuItem.type)\">转发</div> -->\r\n        <!-- <div class=\"GlobalChatViewMenuItem\">收藏</div> -->\r\n        <div class=\"GlobalChatViewMenuItem\" v-copy=\"messagesMenuItem?.copyContent\"\r\n          v-if=\"messagesMenuItem.type === 'RC:TxtMsg'\">\r\n          复制\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleFolderSelectFile\"\r\n          v-if=\"isElectron && messagesMenuItem?.customType === 'file'\">\r\n          {{ isMacText() ? '在 Finder 中显示' : '在资源管理器中显示' }}\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuLine\" v-if=\"chatType?.includes(messagesMenuItem.type)\"></div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleDelMessage\"\r\n          v-if=\"!messagesMenuItem.chatObjectInfoType || !messagesMenuItem.isWithdraw\">\r\n          删除\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleWithdrawMessage\"\r\n          v-if=\"messagesMenuItem.chatObjectInfoType && messagesMenuItem.isWithdraw\">\r\n          撤回\r\n        </div>\r\n      </div>\r\n      <GlobalChatEditor ref=\"editorRef\" :isVote=\"chatInfo.type === 3\" :userData=\"groupUser\" @handleFile=\"fileUpload\"\r\n        @handleVote=\"handleVote\" @handlePasteImg=\"handlePasteImg\" @handleSendMessage=\"handleKeyCode\" v-show=\"isChat\">\r\n      </GlobalChatEditor>\r\n      <div class=\"GlobalChatViewNoMessage\" v-show=\"!isChat\">\r\n        <el-icon>\r\n          <Warning />\r\n        </el-icon>\r\n        无法在已退出的群聊中接收和发送消息\r\n      </div>\r\n      <setting-popup-window v-model=\"settingShow\">\r\n        <GlobalChatViewWindow :chatInfo=\"chatInfo\" :groupUser=\"groupUser\" @refresh=\"handleRefresh\"\r\n          @callback=\"handleGroup\" />\r\n      </setting-popup-window>\r\n    </div>\r\n    <div class=\"GlobalChatViewDrag\" v-if=\"!chatId\"></div>\r\n    <chat-popup-window v-model=\"fileShow\">\r\n      <ChatSendFile :chatInfo=\"chatInfo\" :fileList=\"fileList\" @callback=\"fileCallback\"></ChatSendFile>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"imgShow\">\r\n      <ChatSendImg :chatInfo=\"chatInfo\" :fileImg=\"fileImg\" @callback=\"imgCallback\"></ChatSendImg>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"createGroupShow\">\r\n      <GlobalCreateGroup :userId=\"userId\" @callback=\"createCallback\"></GlobalCreateGroup>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"addShow\">\r\n      <GlobalGroupAddUser :infoId=\"infoId\" @callback=\"addCallback\"></GlobalGroupAddUser>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"delShow\">\r\n      <GlobalGroupDelUser :infoId=\"infoId\" @callback=\"delCallback\"></GlobalGroupDelUser>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"nameShow\" class=\"GlobalGroupNamePopupWindow\">\r\n      <GlobalGroupName :infoId=\"infoId\" @callback=\"nameCallback\"></GlobalGroupName>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"qrShow\" class=\"GlobalGroupQrPopupWindow\">\r\n      <GlobalGroupQr :infoId=\"infoId\"></GlobalGroupQr>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"announcementShow\" class=\"GlobalGroupAnnouncementPopupWindow\">\r\n      <GlobalGroupAnnouncement :infoId=\"infoId\" :isOwner=\"isGroupOwner\" @callback=\"announcementCallback\">\r\n      </GlobalGroupAnnouncement>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"transferShow\">\r\n      <GlobalGroupTransfer :infoId=\"infoId\" @callback=\"transferCallback\"></GlobalGroupTransfer>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"voteShow\" class=\"GlobalGroupVotePopupWindow\">\r\n      <GlobalGroupVote :id=\"infoId\" :refresh=\"voteRefresh\" @callback=\"voteCallback\"\r\n        @sendMessage=\"handleSendCustomMessage\">\r\n      </GlobalGroupVote>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"createVoteShow\">\r\n      <GlobalCreateVote :dataId=\"infoId\" @callback=\"handleVoteCallback\"></GlobalCreateVote>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"voteDetailsShow\" class=\"GlobalGroupVotePopupWindow\">\r\n      <GlobalVoteDetails :id=\"voteId\" @callback=\"handleVoteCallback\" @sendMessage=\"handleSendCustomMessage\">\r\n      </GlobalVoteDetails>\r\n    </chat-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatView' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, onUnmounted, watch, nextTick, defineAsyncComponent } from 'vue'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { format } from 'common/js/time.js'\r\nimport utils, { size2Str } from 'common/js/utils.js'\r\nimport { globalFileLocation } from 'common/config/location'\r\nimport { user, appOnlyHeader } from 'common/js/system_var.js'\r\n// import { emotion } from '../js/emotion.js' emoteIcon, folderIcon, lineFeedIcon, voteIcon,\r\nimport { chatGroupMemberList } from '../js/ChatMethod.js'\r\nimport { fileIcon, handleTimeFormat, handleHistoryMessages, getUniqueFileName } from '../js/ChatViewMethod.js'\r\nimport {\r\n  notificationIcon,\r\n  initGroupChatIcon,\r\n  announcementIcon,\r\n  clearAwayIcon,\r\n  voteListIcon,\r\n  voteBgIcon\r\n} from '../js/icon.js'\r\nimport { Search, Picture } from '@element-plus/icons-vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nconst GlobalChatEditor = defineAsyncComponent(() => import('./GlobalChatEditor.vue'))\r\nconst ChatPopupWindow = defineAsyncComponent(() => import('./chat-popup-window/chat-popup-window.vue'))\r\nconst SettingPopupWindow = defineAsyncComponent(() => import('./setting-popup-window/setting-popup-window.vue'))\r\nconst ChatSendImg = defineAsyncComponent(() => import('./ChatSendImg/ChatSendImg.vue'))\r\nconst ChatSendFile = defineAsyncComponent(() => import('./ChatSendFile/ChatSendFile.vue'))\r\nconst GlobalCreateGroup = defineAsyncComponent(() => import('./GlobalCreateGroup/GlobalCreateGroup.vue'))\r\nconst GlobalGroupAddUser = defineAsyncComponent(() => import('./GlobalGroupAddUser/GlobalGroupAddUser.vue'))\r\nconst GlobalGroupDelUser = defineAsyncComponent(() => import('./GlobalGroupDelUser/GlobalGroupDelUser.vue'))\r\nconst GlobalGroupName = defineAsyncComponent(() => import('./GlobalGroupName/GlobalGroupName.vue'))\r\nconst GlobalGroupQr = defineAsyncComponent(() => import('./GlobalGroupQr/GlobalGroupQr.vue'))\r\nconst GlobalGroupAnnouncement = defineAsyncComponent(() =>\r\n  import('./GlobalGroupAnnouncement/GlobalGroupAnnouncement.vue')\r\n)\r\nconst GlobalGroupTransfer = defineAsyncComponent(() => import('./GlobalGroupTransfer/GlobalGroupTransfer.vue'))\r\nconst GlobalChatViewWindow = defineAsyncComponent(() => import('./GlobalChatViewWindow/GlobalChatViewWindow.vue'))\r\nconst GlobalGroupVote = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalGroupVote.vue'))\r\nconst GlobalCreateVote = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalCreateVote.vue'))\r\nconst GlobalVoteDetails = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalVoteDetails.vue'))\r\nconst props = defineProps({ modelValue: [String, Number], chatList: { type: Array, default: () => [] } })\r\nconst emit = defineEmits(['update:modelValue', 'time', 'refresh', 'send'])\r\nconst isMac = window.electron?.isMac\r\nconst isElectron = window.electron ? true : false\r\nconst keyword = ref('')\r\nconst chatId = computed({\r\n  get () {\r\n    return props.modelValue\r\n  },\r\n  set (value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst chatType = ['RC:TxtMsg', 'RC:ImgTextMsg']\r\nconst chatList = computed(() => props.chatList)\r\nconst isChat = ref(true)\r\nconst chatInfo = ref({})\r\nconst groupUser = ref([])\r\nconst chatInfoImg = ref([])\r\nconst chatInfoAudio = ref({})\r\nconst chatInfoAudioObj = ref({})\r\nconst electronFile = ref([])\r\nconst isElectronFile = ref([])\r\nconst isElectronFileObj = ref({})\r\nconst electronRecordObj = ref({})\r\nconst electronRecordData = ref([])\r\nconst chatInfoUser = ref({})\r\nconst chatMenuTop = ref(0)\r\nconst chatMenuLeft = ref(0)\r\nconst chatMenuItem = ref({})\r\nconst chatMenuShow = ref(false)\r\nconst isChatMenuShow = ref(false)\r\nconst messagesMenuTop = ref(0)\r\nconst messagesMenuLeft = ref(0)\r\nconst messagesMenuItem = ref({})\r\nconst messagesMenuShow = ref(false)\r\nconst isMessagesMenuShow = ref(false)\r\nconst fileList = ref([])\r\nconst fileShow = ref(false)\r\nconst fileImg = ref({})\r\nconst imgShow = ref(false)\r\nconst userId = ref([])\r\nconst createGroupShow = ref(false)\r\nconst infoId = ref('')\r\nconst addShow = ref(false)\r\nconst delShow = ref(false)\r\nconst nameShow = ref(false)\r\nconst qrShow = ref(false)\r\nconst isGroupOwner = ref(false)\r\nconst announcementShow = ref(false)\r\nconst transferShow = ref(false)\r\nconst settingShow = ref(false)\r\nconst chatGroupAnnouncement = ref('')\r\nconst isChatGroupAnnouncement = ref(false)\r\nconst voteId = ref('')\r\nconst voteRefresh = ref('')\r\nconst voteShow = ref(false)\r\nconst createVoteShow = ref(false)\r\nconst voteDetailsShow = ref(false)\r\n// 图片地址拼接组合\r\nconst imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst scrollRef = ref()\r\nconst editorRef = ref()\r\nconst scrollHeight = ref(0)\r\nconst scrollTopNum = ref(0)\r\nconst scrollShow = ref(true)\r\nconst chatInfoMessages = ref([])\r\nconst chatInfoMessagesData = ref([])\r\nonMounted(() => { })\r\nconst handleElClick = (e) => {\r\n  chatMenuShow.value = false\r\n  messagesMenuShow.value = false\r\n  e.preventDefault()\r\n}\r\nconst handleElContextmenu = (e) => {\r\n  if (isChatMenuShow.value) {\r\n    isChatMenuShow.value = false\r\n  } else {\r\n    chatMenuShow.value = false\r\n  }\r\n  if (isMessagesMenuShow.value) {\r\n    isMessagesMenuShow.value = false\r\n  } else {\r\n    messagesMenuShow.value = false\r\n  }\r\n  e.preventDefault()\r\n}\r\nconst handleCustom = (item) => {\r\n  if (item.customType === 'file') isElectron ? handleElectronFile(item.uid, item.file, false) : handlePreview(item.file)\r\n  if (item.customType === 'vote') handleVoteDetails(item.vote)\r\n}\r\nconst handleElectronFile = async (uid, file, type = false) => {\r\n  if (isElectronFile.value?.includes(uid)) return\r\n  let fileName = file.originalFileName\r\n  if (electronRecordObj.value[uid]) {\r\n    fileName = electronRecordObj.value[uid]\r\n  } else {\r\n    fileName = getUniqueFileName(fileName, electronRecordData.value)\r\n    electronRecordObj.value[uid] = fileName\r\n    electronRecordData.value.push(fileName)\r\n  }\r\n  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'\r\n  const result = await window.electron.fileExists(fileFolderPath, fileName)\r\n  if (result) type ? handleOpenFolderSelectFile(fileName) : handleElectronOpenFile(fileName)\r\n  if (!result) globalElectronFileDownload(uid, file, fileName, type)\r\n}\r\nconst onDownloadProgress = (progressEvent, id) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    isElectronFileObj.value[id] = 100 - parseInt(progress) + '%'\r\n  }\r\n}\r\nconst globalElectronFileDownload = async (uid, file, fileName, type = false) => {\r\n  isElectronFile.value.push(uid)\r\n  isElectronFileObj.value[uid] = '100%'\r\n  const res = await api.globalElectronFileDownload(uid, file.id, {}, onDownloadProgress)\r\n  isElectronFile.value = isElectronFile.value.filter((v) => v !== uid)\r\n  delete isElectronFileObj.value[uid]\r\n  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'\r\n  const saveRes = await window.electron.saveFile(fileFolderPath, fileName, res)\r\n  if (saveRes.type === 'success') type ? handleOpenFolderSelectFile(fileName) : handleElectronOpenFile(fileName)\r\n  if (saveRes.type === 'error') ElMessage({ type: 'error', message: saveRes.message })\r\n}\r\nconst handleElectronOpenFile = async (fileName) => {\r\n  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'\r\n  const openRes = await window.electron.openFile(fileFolderPath, fileName)\r\n  if (openRes.type === 'error') ElMessage({ type: 'error', message: openRes.message })\r\n}\r\nconst handleOpenFolderSelectFile = async (fileName) => {\r\n  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'\r\n  const openRes = await window.electron.openFolderSelectFile(`chat_files/${fileFolderPath}`, fileName)\r\n  if (openRes.type === 'error') ElMessage({ type: 'error', message: openRes.message })\r\n}\r\nconst handleFolderSelectFile = async () => {\r\n  const uid = messagesMenuItem.value?.uid\r\n  const file = messagesMenuItem.value?.file\r\n  handleElectronFile(uid, file, true)\r\n}\r\nconst globalElectronSaveRecord = async (recordId, data) => {\r\n  const fileName = recordId + '_' + user.value.accountId + '_record.txt'\r\n  const fileContent = utils.gm_encrypt(data, 'zysoft2017-08-11', 'zysoft2017-08-11')\r\n  const res = await window.electron.saveRecordFile('chat_record', fileName, fileContent)\r\n  console.log(res)\r\n}\r\nconst globalElectronReadRecord = async (recordId) => {\r\n  const fileName = recordId + '_' + user.value.accountId + '_record.txt'\r\n  const res = await window.electron.readRecordFile('chat_record', fileName)\r\n  if (res.type === 'success') {\r\n    const recordObj = JSON.parse(utils.gm_decrypt(res.data, 'zysoft2017-08-11', 'zysoft2017-08-11'))\r\n    const recordData = []\r\n    for (const key in recordObj) {\r\n      if (Object.prototype.hasOwnProperty.call(recordObj, key)) {\r\n        const value = recordObj[key]\r\n        recordData.push(value)\r\n      }\r\n    }\r\n    electronRecordObj.value = recordObj\r\n    electronRecordData.value = recordData\r\n  }\r\n}\r\nconst handlePreview = (row) => {\r\n  if (!row) return\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nconst querySearch = (queryString, cb) => {\r\n  const results = queryString\r\n    ? chatList.value.filter((v) => v.chatObjectInfo?.name?.toLowerCase().includes(queryString?.toLowerCase()))\r\n    : []\r\n  cb(results)\r\n}\r\nconst scrollDown = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight\r\n}\r\nconst scrollElHeight = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - scrollHeight.value\r\n}\r\nconst handleMessagesScroll = ({ scrollTop }) => {\r\n  scrollTopNum.value = scrollTop\r\n  if (scrollTop === 0) {\r\n    scrollHeight.value = scrollRef.value.wrapRef.scrollHeight\r\n    getHistoryMessages(chatInfoMessages.value[0]?.sentTime || 0)\r\n  }\r\n}\r\nconst handleClick = async (item) => {\r\n  if (chatId.value === item.id) return\r\n  isChat.value = true\r\n  chatId.value = item.id\r\n}\r\nconst handleChatClick = async (item, type) => {\r\n  settingShow.value = false\r\n  chatInfo.value = item\r\n  scrollHeight.value = 0\r\n  chatInfoMessages.value = []\r\n  electronFile.value = []\r\n  isElectronFile.value = []\r\n  isElectronFileObj.value = {}\r\n  if (!type) {\r\n    editorRef.value?.clearMessage()\r\n    chatGroupAnnouncement.value = ''\r\n    isChatGroupAnnouncement.value = false\r\n  }\r\n  getHistoryMessages()\r\n  if (!item.isTemporary) clearMessagesUnreadStatus()\r\n}\r\nconst handleClearAway = () => {\r\n  ElMessageBox.confirm('此操作将清除所有消息的未读状态, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  })\r\n    .then(() => {\r\n      clearAllMessagesUnreadStatus()\r\n    })\r\n    .catch(() => {\r\n      ElMessage({ type: 'info', message: '已取消清除' })\r\n    })\r\n}\r\nconst clearAllMessagesUnreadStatus = async () => {\r\n  const { code, msg } = await RongIMLib.clearAllMessagesUnreadStatus()\r\n  if (code === 0) {\r\n    handleRefresh()\r\n  } else {\r\n    console.log(code, msg)\r\n  }\r\n}\r\nconst clearMessagesUnreadStatus = async (type) => {\r\n  const { code, msg } = await RongIMLib.clearMessagesUnreadStatus({\r\n    conversationType: chatInfo.value.type,\r\n    targetId: chatInfo.value.targetId\r\n  })\r\n  if (code === 0) {\r\n    if (!type) handleRefresh()\r\n  } else {\r\n    console.log(code, msg)\r\n  }\r\n}\r\nconst getHistoryMessages = async (timestamp = 0) => {\r\n  const option = { timestamp, count: 20, order: 0 }\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const { code, data, msg } = await RongIMLib.getHistoryMessages(conversation, option)\r\n  if (code === 0) {\r\n    await handleUser()\r\n    scrollShow.value = timestamp === 0\r\n    handleMessages(data.list, timestamp === 0)\r\n  } else {\r\n    console.log(code, msg)\r\n  }\r\n}\r\nconst getNewestMessages = async () => {\r\n  const option = { timestamp: chatInfo.value.sentTime, count: 20, order: 1 }\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const { code, data, msg } = await RongIMLib.getHistoryMessages(conversation, option)\r\n  if (code === 0) {\r\n    scrollShow.value = true\r\n    clearMessagesUnreadStatus(true)\r\n    handleMessages(data.list, true)\r\n  } else {\r\n    console.log(code, msg)\r\n  }\r\n}\r\nconst chatGroupInfo = async (id) => {\r\n  const { data } = await api.chatGroupInfo({ detailId: id.slice(appOnlyHeader.value.length) })\r\n  const groupAnnouncement = JSON.parse(localStorage.getItem('isChatGroupAnnouncement')) || {}\r\n  const groupAnnouncementItem = groupAnnouncement.hasOwnProperty(id)\r\n    ? groupAnnouncement[id]\r\n    : { show: true, callBoard: '' }\r\n  if (data.callBoard && groupAnnouncementItem.callBoard !== data.callBoard) {\r\n    chatGroupAnnouncement.value = data.callBoard\r\n    isChatGroupAnnouncement.value = true\r\n  }\r\n  return data\r\n}\r\nconst handleChatGroupAnnouncement = () => {\r\n  const groupAnnouncement = JSON.parse(localStorage.getItem('isChatGroupAnnouncement')) || {}\r\n  let newGroupAnnouncement = { ...groupAnnouncement }\r\n  newGroupAnnouncement[chatInfo.value.targetId] = { show: false, callBoard: chatGroupAnnouncement.value }\r\n  localStorage.setItem('isChatGroupAnnouncement', JSON.stringify(newGroupAnnouncement))\r\n  chatGroupAnnouncement.value = ''\r\n  isChatGroupAnnouncement.value = false\r\n}\r\nconst handleAreArraysEqual = (arr1, arr2) => {\r\n  // 如果数组长度不相等，直接返回 false\r\n  if (arr1.length !== arr2.length) return false\r\n  // 将两个数组排序后比较\r\n  const sortedArr1 = [...arr1].sort()\r\n  const sortedArr2 = [...arr2].sort()\r\n  // 遍历比较每个元素\r\n  for (let i = 0; i < sortedArr1.length; i++) {\r\n    if (sortedArr1[i] !== sortedArr2[i]) {\r\n      return false\r\n    }\r\n  }\r\n  return true\r\n}\r\n\r\nconst handleUser = async () => {\r\n  let newUserObj = {}\r\n  if (chatInfo.value.type === 3) {\r\n    const newGroupInfo = await chatGroupInfo(chatInfo.value.targetId)\r\n    if (!newGroupInfo.id) {\r\n      ElMessageBox.alert('当前群组已解散！', '提示', {\r\n        confirmButtonText: '确定',\r\n        callback: () => {\r\n          scrollHeight.value = 0\r\n          chatInfoMessages.value = []\r\n          chatInfoMessagesData.value = []\r\n          chatMenuItem.value = chatInfo.value\r\n          handleDelChat()\r\n        }\r\n      })\r\n    }\r\n    const isGroupUser = handleAreArraysEqual(\r\n      newGroupInfo.memberUserIds,\r\n      groupUser.value.map((v) => v.accountId)\r\n    )\r\n    if (!isGroupUser)\r\n      groupUser.value = await chatGroupMemberList(chatInfo.value.targetId.slice(appOnlyHeader.value.length))\r\n    let isChatGroup = false\r\n    for (let index = 0; index < groupUser.value.length; index++) {\r\n      const item = groupUser.value[index]\r\n      if (item.accountId === user.value.accountId) isChatGroup = true\r\n      newUserObj[appOnlyHeader.value + item.accountId] = {\r\n        uid: appOnlyHeader.value + item.accountId,\r\n        id: item.accountId,\r\n        name: item.userName,\r\n        img: item.photo || item.headImg,\r\n        userInfo: { userId: item.id, userName: item.userName, photo: item.photo, headImg: item.headImg }\r\n      }\r\n    }\r\n    isChat.value = newGroupInfo.id ? isChatGroup : true\r\n  } else {\r\n    isChat.value = true\r\n    groupUser.value = []\r\n    newUserObj[appOnlyHeader.value + user.value.accountId] = {\r\n      uid: appOnlyHeader.value + user.value.accountId,\r\n      id: user.value.accountId,\r\n      name: user.value.userName,\r\n      img: user.value.photo || user.value.headImg,\r\n      userInfo: {\r\n        userId: user.value.id,\r\n        userName: user.value.userName,\r\n        photo: user.value.photo,\r\n        headImg: user.value.headImg\r\n      }\r\n    }\r\n    newUserObj[chatInfo.value?.chatObjectInfo.uid] = chatInfo.value?.chatObjectInfo\r\n  }\r\n  chatInfoUser.value = newUserObj\r\n}\r\nconst handleMessages = async (data, type) => {\r\n  const { newMessages, withdrawId } = await handleHistoryMessages(data, chatInfoUser.value)\r\n  console.log(newMessages)\r\n  if (type) {\r\n    chatInfoMessages.value = [...chatInfoMessages.value, ...newMessages].filter((v) => !withdrawId?.includes(v.uid))\r\n    handleRenderMessages(withdrawId)\r\n  } else {\r\n    chatInfoMessages.value = [...newMessages, ...chatInfoMessages.value].filter((v) => !withdrawId?.includes(v.uid))\r\n    handleRenderMessages(withdrawId)\r\n  }\r\n}\r\nconst handleRenderMessages = async (withdrawId, type) => {\r\n  let timeData = []\r\n  let newMessages = []\r\n  let newChatInfoImg = []\r\n  let newMessagesId = []\r\n  let isGroupAnnouncement = false\r\n  for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n    const item = chatInfoMessages.value[index]\r\n    if (item.content?.content?.includes('群公告：\\n')) isGroupAnnouncement = true\r\n    if (!newMessagesId?.includes(item.uid) && !withdrawId?.includes(item.uid)) {\r\n      newMessagesId.push(item.uid)\r\n      if (!timeData?.includes(format(item.sentTime))) {\r\n        timeData = [format(item.sentTime), format(item.sentTime + 60000)]\r\n        newMessages.push({\r\n          id: item.sentTime,\r\n          type: 'time',\r\n          className: 'GlobalChatMessagesTime',\r\n          content: format(item.sentTime)\r\n        })\r\n      }\r\n      if (item.type === 'RC:ImgMsg') {\r\n        newMessages.push({ ...item, imgIndex: newChatInfoImg.length })\r\n        newChatInfoImg.push(item.content.imageUri)\r\n      } else if (item.type === 'RC:HQVCMsg') {\r\n        newMessages.push({ ...item, audio: new Audio(item.content.remoteUrl) })\r\n      } else if (item.type === 'RC:ImgTextMsg') {\r\n        newMessages.push(item)\r\n        electronFile.value.push(item?.uid)\r\n      } else {\r\n        newMessages.push(item)\r\n      }\r\n    }\r\n  }\r\n  if (isGroupAnnouncement) chatGroupInfo(chatInfo.value.targetId)\r\n  chatInfoImg.value = newChatInfoImg\r\n  chatInfoMessagesData.value = newMessages\r\n  if (type) {\r\n    nextTick(() => {\r\n      scrollRef.value.wrapRef.scrollTop = scrollTopNum.value\r\n    })\r\n  } else {\r\n    nextTick(() => {\r\n      scrollShow.value ? scrollDown() : scrollElHeight()\r\n    })\r\n  }\r\n}\r\nconst handleChatMenu = (e, item) => {\r\n  chatMenuItem.value = item\r\n  chatMenuTop.value = e.pageY\r\n  chatMenuLeft.value = e.pageX\r\n  chatMenuShow.value = true\r\n  isChatMenuShow.value = true\r\n}\r\nconst handleMessagesMenu = (e, item) => {\r\n  const selection = window.getSelection()\r\n  const copyContent = selection.toString() || item?.content?.content\r\n  const now = new Date().getTime()\r\n  const timeDifference = now - item.sentTime\r\n  const isWithdraw = timeDifference < 180000\r\n  messagesMenuItem.value = { ...item, copyContent, isWithdraw }\r\n  messagesMenuTop.value = e.pageY\r\n  messagesMenuLeft.value = e.pageX\r\n  messagesMenuShow.value = true\r\n  isMessagesMenuShow.value = true\r\n}\r\nconst handleAudio = (data) => {\r\n  if (chatInfoAudio.value.id) {\r\n    if (chatInfoAudio.value.id === data.id) {\r\n      chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime\r\n      handlePauseAudio()\r\n    } else {\r\n      chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime\r\n      handlePauseAudio()\r\n      chatInfoAudio.value = { id: data.id, audio: data.audio }\r\n      chatInfoAudio.value.audio.currentTime = 0\r\n      handlePlayAudio()\r\n    }\r\n  } else {\r\n    chatInfoAudio.value = { id: data.id, audio: data.audio }\r\n    chatInfoAudio.value.audio.currentTime = 0\r\n    handlePlayAudio()\r\n  }\r\n}\r\nconst handleGoAudio = (data) => {\r\n  if (chatInfoAudio.value.id) {\r\n    chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime\r\n    handlePauseAudio()\r\n  }\r\n  chatInfoAudio.value = { id: data.id, audio: data.audio }\r\n  chatInfoAudio.value.audio.currentTime = chatInfoAudioObj.value[chatInfoAudio.value.id] || 0\r\n  handlePlayAudio()\r\n}\r\nconst handlePlayAudio = () => {\r\n  chatInfoAudio.value.audio.play()\r\n  chatInfoAudio.value.audio.addEventListener('ended', () => {\r\n    chatInfoAudioObj.value[chatInfoAudio.value.id] = 0\r\n    chatInfoAudio.value = {}\r\n  })\r\n}\r\nconst handlePauseAudio = () => {\r\n  chatInfoAudio.value.audio.pause()\r\n  chatInfoAudio.value = {}\r\n}\r\nconst handleImgLoad = () => {\r\n  if (scrollShow.value) scrollDown()\r\n}\r\nconst handleDrop = (event) => {\r\n  event.preventDefault()\r\n  const files = event.dataTransfer.files\r\n  if (files.length) {\r\n    const mewFileList = []\r\n    for (let index = 0; index < files.length; index++) {\r\n      const file = files.item(index)\r\n      const extName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()\r\n      mewFileList.push({ id: guid(), name: file.name, extName, size: file.size, file: file })\r\n    }\r\n    fileList.value = mewFileList\r\n    fileShow.value = true\r\n  }\r\n}\r\nconst fileCallback = (data) => {\r\n  if (data) {\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      fileUpload(item)\r\n    }\r\n  }\r\n  fileList.value = []\r\n  fileShow.value = false\r\n}\r\nconst handlePasteImg = (file) => {\r\n  fileImg.value = file\r\n  imgShow.value = true\r\n}\r\nconst imgCallback = (type) => {\r\n  if (type) fileUpload(fileImg.value)\r\n  fileImg.value = {}\r\n  imgShow.value = false\r\n}\r\nconst fileUpload = async (file) => {\r\n  const params = new FormData()\r\n  params.append('file', file.file)\r\n  params.append('isKeepAlive', true)\r\n  params.append('uid', file.id || file.uid || guid())\r\n  const { data } = await api.globalUpload(params, () => { })\r\n  if (['png', 'jpg', 'jpeg']?.includes(data.extName)) {\r\n    handleSendImgMessage(api.openImgURL(data.newFileName))\r\n  } else {\r\n    localStorage.setItem(data.id, JSON.stringify(data))\r\n    handleSendFileMessage(data.id)\r\n    if (chatInfo.value.type === 3) submitChatGroupFile(data.id)\r\n  }\r\n}\r\nconst submitChatGroupFile = async (fileId) => {\r\n  await api.chatGroupFileAdd({\r\n    form: { chatGroupId: chatInfo.value.targetId.slice(appOnlyHeader.value.length), fileId }\r\n  })\r\n}\r\nconst isMacText = () => {\r\n  const userAgent = navigator.userAgent.toLowerCase()\r\n  return userAgent?.includes('macintosh') || userAgent?.includes('mac os x')\r\n}\r\nconst handleSetting = () => {\r\n  if (!isChat.value) return\r\n  settingShow.value = !settingShow.value\r\n}\r\nconst handleKeyCode = (data) => {\r\n  const userIdData = data?.mentions?.map((v) => appOnlyHeader.value + v.userInfo.accountId)\r\n  const mentionData = userIdData.length\r\n    ? { mentionedContent: '', type: 2, userIdList: Array.from(new Set(userIdData)) }\r\n    : {}\r\n  handleSendTextMessage(data.content, mentionData)\r\n}\r\nconst handleSendTextMessage = (contentText, mentionData = {}) => {\r\n  if (!contentText.replace(/^\\s+|\\s+$/g, '')) return\r\n  const message = new RongIMLib.TextMessage({ content: contentText, mentionedInfo: mentionData })\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const options = {\r\n    onSendBefore: (message) => {\r\n      scrollShow.value = true\r\n      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)\r\n    }\r\n  }\r\n  handleSendMessage(conversation, message, options, (code, msg, data) => {\r\n    if (code === 0) {\r\n      let newChatInfoMessages = []\r\n      for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n        const item = chatInfoMessages.value[index]\r\n        newChatInfoMessages.push(\r\n          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item\r\n        )\r\n      }\r\n      chatInfoMessages.value = newChatInfoMessages\r\n      handleRenderMessages([], true)\r\n      console.log('消息发送成功：', data)\r\n    } else {\r\n      console.log('消息发送失败：', code, msg)\r\n    }\r\n  })\r\n}\r\nconst handleSendImgMessage = (url) => {\r\n  const message = new RongIMLib.ImageMessage({ content: '', imageUri: url })\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const options = {\r\n    onSendBefore: (message) => {\r\n      scrollShow.value = true\r\n      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)\r\n    }\r\n  }\r\n  handleSendMessage(conversation, message, options, (code, msg, data) => {\r\n    if (code === 0) {\r\n      let newChatInfoMessages = []\r\n      for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n        const item = chatInfoMessages.value[index]\r\n        newChatInfoMessages.push(\r\n          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item\r\n        )\r\n      }\r\n      chatInfoMessages.value = newChatInfoMessages\r\n      handleRenderMessages([], true)\r\n      console.log('消息发送成功：', data)\r\n    } else {\r\n      console.log('消息发送失败：', code, msg)\r\n    }\r\n  })\r\n}\r\nconst handleSendFileMessage = (fileId) => {\r\n  const PersonMessage = RongIMLib.registerMessageType('RC:ImgTextMsg', true, true, [], false)\r\n  const message = new PersonMessage({ content: `[文件],${fileId}`, title: '[文件]' })\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const options = {\r\n    onSendBefore: (message) => {\r\n      scrollShow.value = true\r\n      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)\r\n    }\r\n  }\r\n  handleSendMessage(conversation, message, options, (code, msg, data) => {\r\n    if (code === 0) {\r\n      let newChatInfoMessages = []\r\n      for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n        const item = chatInfoMessages.value[index]\r\n        newChatInfoMessages.push(\r\n          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item\r\n        )\r\n      }\r\n      chatInfoMessages.value = newChatInfoMessages\r\n      handleRenderMessages([], true)\r\n      console.log('消息发送成功：', data)\r\n    } else {\r\n      console.log('消息发送失败：', code, msg)\r\n    }\r\n  })\r\n}\r\nconst handleSendCustomMessage = (params) => {\r\n  const PersonMessage = RongIMLib.registerMessageType('RC:CmdNtf', true, true, [], false)\r\n  const message = new PersonMessage(params)\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const options = {\r\n    onSendBefore: (message) => {\r\n      scrollShow.value = true\r\n      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)\r\n    }\r\n  }\r\n  handleSendMessage(conversation, message, options, (code, msg, data) => {\r\n    if (code === 0) {\r\n      let newChatInfoMessages = []\r\n      for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n        const item = chatInfoMessages.value[index]\r\n        newChatInfoMessages.push(\r\n          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item\r\n        )\r\n      }\r\n      chatInfoMessages.value = newChatInfoMessages\r\n      handleRenderMessages([], true)\r\n      console.log('消息发送成功：', data)\r\n    } else {\r\n      console.log('消息发送失败：', code, msg)\r\n    }\r\n  })\r\n}\r\nconst handleSendMessage = async (conversation, message, options, callback) => {\r\n  const { code, msg, data } = await RongIMLib.sendMessage(conversation, message, options)\r\n  if (code === 0) handleRefresh()\r\n  callback(code, msg, data)\r\n}\r\nconst handleNotificationClick = async (notification) => {\r\n  const { code } = await RongIMLib.setConversationNotificationStatus(\r\n    { conversationType: chatMenuItem.value.type, targetId: chatMenuItem.value.id },\r\n    notification\r\n  )\r\n  if (!code) handleRefresh()\r\n}\r\nconst handleIsTopClick = async (isTop) => {\r\n  const { code } = await RongIMLib.setConversationToTop(\r\n    { conversationType: chatMenuItem.value.type, targetId: chatMenuItem.value.id },\r\n    isTop\r\n  )\r\n  if (!code) handleRefresh()\r\n}\r\nconst handleDelChat = async () => {\r\n  const { code, msg } = await RongIMLib.removeConversation({\r\n    conversationType: chatMenuItem.value.type,\r\n    targetId: chatMenuItem.value.id\r\n  })\r\n  if (code === 0) {\r\n    console.log('消息删除成功')\r\n    handleRefresh('del', chatMenuItem.value)\r\n  } else {\r\n    console.log('消息删除失败：', code, msg)\r\n  }\r\n}\r\nconst handleDelMessage = async () => {\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const messagesData = [\r\n    {\r\n      messageUId: messagesMenuItem.value.uid,\r\n      sentTime: messagesMenuItem.value.sentTime,\r\n      messageDirection: messagesMenuItem.value.direction\r\n    }\r\n  ]\r\n  const { code, msg } = await RongIMLib.deleteMessages(conversation, messagesData)\r\n  if (code === 0) {\r\n    console.log('消息删除成功')\r\n    chatInfoMessages.value = chatInfoMessages.value.filter((item) => item.uid !== messagesMenuItem.value.uid)\r\n    handleRenderMessages([], true)\r\n  } else {\r\n    console.log('消息删除失败：', code, msg)\r\n  }\r\n}\r\nconst handleWithdrawMessage = async () => {\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const messagesData = {\r\n    messageUId: messagesMenuItem.value.uid,\r\n    sentTime: messagesMenuItem.value.sentTime,\r\n    disableNotification: true\r\n  }\r\n  const { code, msg } = await RongIMLib.recallMessage(conversation, messagesData)\r\n  if (code === 0) {\r\n    handleChatClick(chatInfo.value, true)\r\n    console.log('消息撤回成功')\r\n  } else {\r\n    console.log('消息撤回失败：', code, msg)\r\n  }\r\n}\r\nconst handleGroup = async (type, data, isOwner) => {\r\n  if (type === 'create') handleCreateGroup(data?.chatObjectInfo?.id)\r\n  if (type === 'add') handleGroupAddUser(data?.chatObjectInfo?.id)\r\n  if (type === 'del') handleGroupDelUser(data?.chatObjectInfo?.id)\r\n  if (type === 'name') handleGroupName(data?.chatObjectInfo?.id)\r\n  if (type === 'qr') handleGroupQr(data?.chatObjectInfo?.id)\r\n  if (type === 'announcement') handleGroupAnnouncement(data?.chatObjectInfo?.id, isOwner)\r\n  if (type === 'transfer') handleGroupTransfer(data?.chatObjectInfo?.id)\r\n  if (type === 'quit') {\r\n    const PersonMessage = RongIMLib.registerMessageType('RC:CmdNtf', true, true, [], false)\r\n    const message = new PersonMessage(data)\r\n    const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n    const options = { onSendBefore: () => { } }\r\n    await RongIMLib.sendMessage(conversation, message, options)\r\n    handleQuitDelChat()\r\n  }\r\n}\r\nconst handleQuitDelChat = async () => {\r\n  const { code, msg } = await RongIMLib.removeConversation({\r\n    conversationType: chatInfo.value.type,\r\n    targetId: chatInfo.value.targetId\r\n  })\r\n  if (code === 0) {\r\n    console.log('消息删除成功')\r\n    handleRefresh()\r\n  } else {\r\n    console.log('消息删除失败：', code, msg)\r\n  }\r\n}\r\nconst handleCreateGroup = (id) => {\r\n  userId.value = id ? [user.value?.accountId, id] : [user.value?.accountId]\r\n  createGroupShow.value = true\r\n}\r\nconst handleGroupAddUser = (id) => {\r\n  infoId.value = id\r\n  addShow.value = true\r\n}\r\nconst handleGroupDelUser = (id) => {\r\n  infoId.value = id\r\n  delShow.value = true\r\n}\r\nconst handleGroupName = (id) => {\r\n  infoId.value = id\r\n  nameShow.value = true\r\n}\r\nconst handleGroupQr = (id) => {\r\n  infoId.value = id\r\n  qrShow.value = true\r\n}\r\nconst handleGroupAnnouncement = (id, isOwner) => {\r\n  infoId.value = id\r\n  isGroupOwner.value = isOwner\r\n  announcementShow.value = true\r\n}\r\nconst handleGroupTransfer = (id) => {\r\n  infoId.value = id\r\n  transferShow.value = true\r\n}\r\nconst handleVote = () => {\r\n  infoId.value = chatInfo.value.targetId.slice(appOnlyHeader.value.length)\r\n  voteShow.value = true\r\n}\r\nconst handleVoteDetails = (row) => {\r\n  voteId.value = row.id\r\n  voteDetailsShow.value = true\r\n}\r\nconst createCallback = (data) => {\r\n  settingShow.value = false\r\n  if (data) emit('send', data)\r\n  createGroupShow.value = false\r\n}\r\nconst addCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    await handleUser()\r\n    handleSendCustomMessage(data)\r\n  }\r\n  addShow.value = false\r\n}\r\nconst delCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    await handleUser()\r\n    handleSendCustomMessage(data)\r\n  }\r\n  delShow.value = false\r\n}\r\nconst nameCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    await handleUser()\r\n    handleTime()\r\n    handleSendCustomMessage(data)\r\n  }\r\n  nameShow.value = false\r\n}\r\nconst announcementCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    handleSendTextMessage(data)\r\n  }\r\n  announcementShow.value = false\r\n}\r\nconst transferCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    await handleUser()\r\n    handleSendCustomMessage(data)\r\n  }\r\n  transferShow.value = false\r\n}\r\nconst voteCallback = () => {\r\n  createVoteShow.value = true\r\n}\r\nconst handleVoteCallback = () => {\r\n  voteRefresh.value = guid()\r\n  createVoteShow.value = false\r\n  voteDetailsShow.value = false\r\n}\r\nconst handleTime = () => {\r\n  emit('time')\r\n}\r\nconst handleRefresh = (type, data) => {\r\n  emit('refresh', type, data)\r\n}\r\nonUnmounted(() => {\r\n  if (isElectron) {\r\n    const fileContent = JSON.stringify(electronRecordObj.value)\r\n    if (chatId.value) globalElectronSaveRecord(chatId.value, fileContent)\r\n  }\r\n})\r\nwatch(\r\n  () => chatId.value,\r\n  (newValue, oldValue) => {\r\n    if (isElectron) {\r\n      const fileContent = JSON.stringify(electronRecordObj.value)\r\n      electronRecordObj.value = {}\r\n      electronRecordData.value = []\r\n      if (oldValue) globalElectronSaveRecord(oldValue, fileContent)\r\n      if (newValue) globalElectronReadRecord(newValue)\r\n    }\r\n    if (chatId.value) {\r\n      for (let index = 0; index < props.chatList.length; index++) {\r\n        const item = props.chatList[index]\r\n        if (chatId.value === item.id) handleChatClick(item)\r\n      }\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => chatList.value,\r\n  () => {\r\n    if (chatId.value) {\r\n      let isShow = true\r\n      for (let index = 0; index < props.chatList.length; index++) {\r\n        const item = props.chatList[index]\r\n        if (chatId.value === item.id) {\r\n          isShow = false\r\n          chatInfo.value = item\r\n        }\r\n      }\r\n      if (isShow) {\r\n        chatId.value = ''\r\n        chatInfo.value = {}\r\n      }\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\ndefineExpose({ getNewestMessages })\r\n</script>\r\n<style lang=\"scss\">\r\n@import url('../scss/emotion.scss');\r\n\r\n.GlobalChatView {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n\r\n  &.GlobalChatMacView {\r\n    .GlobalChatViewList {\r\n      .GlobalChatViewListHead {\r\n        height: 56px;\r\n      }\r\n\r\n      .GlobalChatViewMessagesList {\r\n        height: calc(100% - 92px);\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewDrag {\r\n      height: 56px;\r\n    }\r\n\r\n    .GlobalChatWindow {\r\n      .GlobalChatWindowTitle {\r\n        height: 56px;\r\n\r\n        .GlobalChatWindowMore {\r\n          margin: 0;\r\n        }\r\n      }\r\n\r\n      .GlobalChatWindowScroll {\r\n        height: calc(100% - 222px);\r\n\r\n        &.GlobalChatWindowNoChat {\r\n          height: calc(100% - (56px + var(--zy-height)));\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatViewList {\r\n    width: 280px;\r\n    height: 100%;\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalChatViewListHead {\r\n      width: 100%;\r\n      height: 66px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n      -webkit-app-region: drag;\r\n\r\n      .zy-el-autocomplete {\r\n        width: 199px;\r\n        height: var(--zy-height-routine);\r\n        -webkit-app-region: no-drag;\r\n\r\n        .zy-el-input {\r\n          width: 199px;\r\n          height: var(--zy-height-routine);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewListHeadIcon {\r\n        width: 32px;\r\n        height: 32px;\r\n        cursor: pointer;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewMessagesList {\r\n      width: 100%;\r\n      height: calc(100% - 102px);\r\n\r\n      .GlobalChatViewMessagesItem {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 10px 20px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          background: #f9f9fa;\r\n        }\r\n\r\n        &.is-top {\r\n          background: #f6f6f6;\r\n        }\r\n\r\n        &.is-active {\r\n          background: #f2f2f2;\r\n        }\r\n\r\n        .zy-el-badge {\r\n          width: 42px;\r\n          height: 42px;\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 42px;\r\n          height: 42px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .GlobalChatViewMessagesInfo {\r\n          width: calc(100% - 56px);\r\n          height: 42px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: space-between;\r\n          position: relative;\r\n\r\n          .GlobalChatViewNotInform {\r\n            width: 16px;\r\n            height: 16px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            position: absolute;\r\n            right: 0;\r\n            bottom: 0;\r\n\r\n            path {\r\n              fill: var(--zy-el-text-color-secondary);\r\n            }\r\n          }\r\n\r\n          .GlobalChatViewMessagesName {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            div {\r\n              flex: 1;\r\n              font-size: 14px;\r\n            }\r\n\r\n            .GlobalChatViewMessagesNameGroup {\r\n              position: relative;\r\n              padding-right: 39px;\r\n\r\n              span {\r\n                padding: 2px 6px;\r\n                font-size: 12px;\r\n                border-radius: 3px;\r\n                position: absolute;\r\n                top: 50%;\r\n                right: 3px;\r\n                transform: translateY(-50%);\r\n                text-align: center;\r\n                color: var(--zy-el-color-success);\r\n                background: var(--zy-el-color-success-light-9);\r\n              }\r\n            }\r\n\r\n            span {\r\n              font-size: 12px;\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n          }\r\n\r\n          .GlobalChatViewNotInform+.GlobalChatViewMessagesText {\r\n            padding-right: 18px;\r\n          }\r\n\r\n          .GlobalChatViewMessagesText {\r\n            font-size: 12px;\r\n            color: var(--zy-el-text-color-secondary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatClearAway {\r\n      width: 100%;\r\n      height: 36px;\r\n      display: flex;\r\n      align-items: center;\r\n      color: var(--zy-el-text-color-secondary);\r\n      border-top: 1px solid var(--zy-el-border-color-lighter);\r\n      font-size: 14px;\r\n      padding-top: 4px;\r\n      padding-left: 46px;\r\n      position: relative;\r\n      cursor: pointer;\r\n\r\n      div {\r\n        width: 22px;\r\n        height: 22px;\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 20px;\r\n        transform: translateY(-50%);\r\n\r\n        .icon {\r\n          width: 22px;\r\n          height: 22px;\r\n\r\n          path {\r\n            fill: var(--zy-el-text-color-secondary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatViewDrag {\r\n    width: calc(100% - 280px);\r\n    height: 66px;\r\n    position: relative;\r\n    -webkit-app-region: drag;\r\n\r\n    &::before {\r\n      content: '';\r\n      width: 96px;\r\n      height: 28px;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      background: transparent;\r\n      -webkit-app-region: no-drag;\r\n    }\r\n  }\r\n\r\n  .GlobalChatWindow {\r\n    width: calc(100% - 280px);\r\n    height: 100%;\r\n    background: #f9f9fa;\r\n    position: relative;\r\n\r\n    .GlobalChatWindowTitle {\r\n      width: 100%;\r\n      height: 66px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n      background: #fff;\r\n      position: relative;\r\n      -webkit-app-region: drag;\r\n\r\n      &::after {\r\n        content: '';\r\n        width: 100%;\r\n        height: 1px;\r\n        position: absolute;\r\n        left: 0;\r\n        bottom: 0;\r\n        background: var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        width: 96px;\r\n        height: 28px;\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        background: transparent;\r\n        -webkit-app-region: no-drag;\r\n        z-index: 8;\r\n      }\r\n\r\n      .ellipsis {\r\n        max-width: calc(100% - 52px);\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .GlobalChatWindowMore {\r\n        width: 32px;\r\n        height: 32px;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        -webkit-app-region: no-drag;\r\n        margin-top: 16px;\r\n\r\n        .zy-el-icon {\r\n          font-size: 20px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewNoMessage {\r\n      width: 100%;\r\n      height: var(--zy-height);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background: rgba(0, 0, 0, 0.6);\r\n      color: #fff;\r\n      font-size: 12px;\r\n\r\n      .zy-el-icon {\r\n        font-size: 16px;\r\n        margin-right: 2px;\r\n      }\r\n    }\r\n\r\n    .GlobalChatWindowScroll {\r\n      width: 100%;\r\n      height: calc(100% - 232px);\r\n\r\n      &.GlobalChatWindowNoChat {\r\n        height: calc(100% - (66px + var(--zy-height)));\r\n      }\r\n\r\n      .GlobalChatGroupAnnouncement {\r\n        width: calc(100% - 40px);\r\n        position: absolute;\r\n        top: 10px;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        box-shadow: var(--zy-el-box-shadow-light);\r\n        border-radius: 4px;\r\n        padding: 10px 20px;\r\n        background: #fff;\r\n        z-index: 6;\r\n\r\n        .GlobalChatGroupAnnouncementTitle {\r\n          width: 100%;\r\n          height: 32px;\r\n          display: flex;\r\n          align-items: flex-end;\r\n          justify-content: space-between;\r\n          padding-bottom: 9px;\r\n\r\n          div {\r\n            display: flex;\r\n            align-items: flex-end;\r\n            justify-content: center;\r\n            font-size: 14px;\r\n            line-height: 14px;\r\n            font-weight: bold;\r\n            padding-left: 2px;\r\n\r\n            span {\r\n              width: 22px;\r\n              height: 22px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              margin-right: 6px;\r\n\r\n              .icon {\r\n                width: 22px;\r\n                height: 22px;\r\n\r\n                path {\r\n                  fill: var(--zy-el-color-primary);\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .zy-el-icon {\r\n            color: #ccc;\r\n            font-size: 18px;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n\r\n        .GlobalChatGroupAnnouncementContent {\r\n          font-size: 14px;\r\n          line-height: 1.6;\r\n        }\r\n      }\r\n\r\n      .GlobalChatWindowBody {\r\n        padding: var(--zy-distance-five) 0;\r\n\r\n        .GlobalChatMessagesTime {\r\n          width: 100%;\r\n          font-size: 14px;\r\n          text-align: center;\r\n          color: var(--zy-el-text-color-secondary);\r\n          padding: var(--zy-distance-five) var(--zy-distance-two);\r\n        }\r\n\r\n        .GlobalChatMessagesInform {\r\n          width: 100%;\r\n          font-size: 12px;\r\n          text-align: center;\r\n          color: var(--zy-el-text-color-secondary);\r\n          padding: var(--zy-distance-two);\r\n        }\r\n\r\n        .GlobalChatMessages,\r\n        .GlobalChatSelfMessages {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: var(--zy-distance-five) var(--zy-distance-two);\r\n\r\n          .GlobalChatWindowUserImg {\r\n            width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n\r\n            .zy-el-image {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 50%;\r\n            }\r\n          }\r\n\r\n          .GlobalChatMessagesInfo {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            width: calc(100% - ((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n            position: relative;\r\n\r\n            .GlobalChatMessagesImgSlot {\r\n              width: 168px;\r\n              height: 68px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              font-size: 28px;\r\n              background: #fff;\r\n              color: var(--zy-el-text-color-regular);\r\n            }\r\n\r\n            .zy-el-image {\r\n              width: 168px;\r\n              height: auto;\r\n              border-radius: 8px;\r\n              overflow: hidden;\r\n            }\r\n\r\n            .GlobalChatMessagesName {\r\n              width: 100%;\r\n              font-size: 12px;\r\n              line-height: 12px;\r\n              padding-bottom: 6px;\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n\r\n            .GlobalChatMessagesText {\r\n              position: relative;\r\n              display: inline-block;\r\n              max-width: 100%;\r\n              padding: 10px var(--zy-distance-five);\r\n              font-size: var(--zy-text-font-size);\r\n              line-height: var(--zy-line-height);\r\n              background: #fff;\r\n              border-radius: var(--el-border-radius-base);\r\n              border: 1px solid var(--zy-el-border-color-light);\r\n              word-wrap: break-word;\r\n              white-space: pre-wrap;\r\n              z-index: 2;\r\n\r\n              span {\r\n                position: absolute;\r\n                width: 10px;\r\n                height: 10px;\r\n                z-index: 2;\r\n                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);\r\n\r\n                &::after {\r\n                  content: '';\r\n                  position: absolute;\r\n                  width: 10px;\r\n                  height: 10px;\r\n                  transform: rotate(45deg);\r\n                  background: #fff;\r\n                  border: 1px solid var(--zy-el-border-color-light);\r\n                  box-sizing: border-box;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesCustom {\r\n              width: 320px;\r\n              padding: 12px 16px;\r\n              display: flex;\r\n              flex-direction: column;\r\n              background: #fff;\r\n              position: relative;\r\n              border-radius: var(--el-border-radius-base);\r\n              border: 1px solid var(--zy-el-border-color-light);\r\n              word-wrap: break-word;\r\n              white-space: pre-wrap;\r\n              cursor: pointer;\r\n\r\n              &.file {\r\n                padding-right: 58px;\r\n              }\r\n\r\n              &.unknown {\r\n                width: auto;\r\n              }\r\n\r\n              span {\r\n                position: absolute;\r\n                width: 10px;\r\n                height: 10px;\r\n                z-index: 2;\r\n                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);\r\n\r\n                &::after {\r\n                  content: '';\r\n                  position: absolute;\r\n                  width: 10px;\r\n                  height: 10px;\r\n                  transform: rotate(45deg);\r\n                  background: #fff;\r\n                  border: 1px solid var(--zy-el-border-color-light);\r\n                  box-sizing: border-box;\r\n                }\r\n              }\r\n\r\n              .GlobalChatMessagesCustomName {\r\n                font-size: var(--zy-text-font-size);\r\n                line-height: var(--zy-line-height);\r\n                padding-bottom: var(--zy-font-text-distance-five);\r\n                word-break: break-all;\r\n              }\r\n\r\n              .GlobalChatMessagesCustomText {\r\n                color: var(--zy-el-text-color-secondary);\r\n                font-size: calc(var(--zy-text-font-size) - 2px);\r\n              }\r\n\r\n              .GlobalChatMessagesFileDownload {\r\n                width: 100%;\r\n                height: 100%;\r\n                background: rgba(0, 0, 0, 0.2);\r\n                position: absolute;\r\n                top: 0;\r\n                right: 0;\r\n                z-index: 9;\r\n              }\r\n\r\n              .globalFileIcon {\r\n                width: 40px;\r\n                height: 40px;\r\n                vertical-align: middle;\r\n                position: absolute;\r\n                top: 12px;\r\n                right: 12px;\r\n\r\n                &.globalFileUnknown {\r\n                  background: url('../img/unknown.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePDF {\r\n                  background: url('../img/PDF.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileWord {\r\n                  background: url('../img/Word.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileExcel {\r\n                  background: url('../img/Excel.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePicture {\r\n                  background: url('../img/picture.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileVideo {\r\n                  background: url('../img/video.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileTXT {\r\n                  background: url('../img/TXT.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileCompress {\r\n                  background: url('../img/compress.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileWPS {\r\n                  background: url('../img/WPS.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePPT {\r\n                  background: url('../img/PPT.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n              }\r\n\r\n              .GlobalChatMessagesVoteTitleBody {\r\n                width: 100%;\r\n                display: flex;\r\n                align-items: flex-end;\r\n                justify-content: space-between;\r\n                padding: 0 2px 8px 2px;\r\n\r\n                .GlobalChatMessagesVoteTitle {\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  font-weight: bold;\r\n                  font-size: var(--zy-name-font-size);\r\n\r\n                  .GlobalChatMessagesVoteIcon {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    margin-right: 6px;\r\n\r\n                    svg {\r\n                      width: 16px;\r\n                      height: 16px;\r\n\r\n                      path {\r\n                        fill: var(--zy-el-color-warning);\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n\r\n                .GlobalChatMessagesVoteTime {\r\n                  font-size: var(--zy-text-font-size);\r\n                  color: var(--zy-el-text-color-secondary);\r\n                }\r\n              }\r\n\r\n              .GlobalChatMessagesVoteInfo {\r\n                width: 100%;\r\n                height: 92px;\r\n                display: flex;\r\n                align-items: center;\r\n                position: relative;\r\n                padding-top: 2px;\r\n\r\n                .GlobalChatMessagesVoteInfoIcon {\r\n                  width: 100%;\r\n                  height: 100%;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  position: absolute;\r\n                  top: 0;\r\n                  left: 0;\r\n                  z-index: 1;\r\n                }\r\n\r\n                .GlobalChatMessagesVoteName {\r\n                  width: 100%;\r\n                  height: calc((var(--zy-text-font-size) * var(--zy-line-height)) * 3);\r\n                  color: #fff;\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 3;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  font-size: var(--zy-text-font-size);\r\n                  line-height: var(--zy-line-height);\r\n                  padding: 0 72px 0 16px;\r\n                  overflow: hidden;\r\n                  position: relative;\r\n                  z-index: 2;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesFile {\r\n              width: 320px;\r\n              padding: 12px 16px;\r\n              display: flex;\r\n              flex-direction: column;\r\n              background: #fff;\r\n              position: relative;\r\n              padding-right: 58px;\r\n              border-radius: var(--el-border-radius-base);\r\n              border: 1px solid var(--zy-el-border-color-light);\r\n              word-wrap: break-word;\r\n              white-space: pre-wrap;\r\n              cursor: pointer;\r\n\r\n              span {\r\n                position: absolute;\r\n                width: 10px;\r\n                height: 10px;\r\n                z-index: 2;\r\n                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);\r\n\r\n                &::after {\r\n                  content: '';\r\n                  position: absolute;\r\n                  width: 10px;\r\n                  height: 10px;\r\n                  transform: rotate(45deg);\r\n                  background: #fff;\r\n                  border: 1px solid var(--zy-el-border-color-light);\r\n                  box-sizing: border-box;\r\n                }\r\n              }\r\n\r\n              .GlobalChatMessagesFileName {\r\n                font-size: var(--zy-text-font-size);\r\n                line-height: var(--zy-line-height);\r\n                padding-bottom: var(--zy-font-text-distance-five);\r\n                word-break: break-all;\r\n              }\r\n\r\n              .GlobalChatMessagesFileSize {\r\n                color: var(--zy-el-text-color-secondary);\r\n                font-size: calc(var(--zy-text-font-size) - 2px);\r\n              }\r\n\r\n              .globalFileIcon {\r\n                width: 40px;\r\n                height: 40px;\r\n                vertical-align: middle;\r\n                position: absolute;\r\n                top: 12px;\r\n                right: 12px;\r\n\r\n                &.globalFileUnknown {\r\n                  background: url('../img/unknown.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePDF {\r\n                  background: url('../img/PDF.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileWord {\r\n                  background: url('../img/Word.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileExcel {\r\n                  background: url('../img/Excel.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePicture {\r\n                  background: url('../img/picture.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileVideo {\r\n                  background: url('../img/video.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileTXT {\r\n                  background: url('../img/TXT.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileCompress {\r\n                  background: url('../img/compress.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileWPS {\r\n                  background: url('../img/WPS.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePPT {\r\n                  background: url('../img/PPT.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesCustomUnknown {\r\n              font-size: var(--zy-text-font-size);\r\n              line-height: var(--zy-line-height);\r\n            }\r\n          }\r\n        }\r\n\r\n        .GlobalChatMessages {\r\n          .GlobalChatMessagesInfo {\r\n            padding-right: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n\r\n            .GlobalChatMessagesText {\r\n              span {\r\n                left: 0;\r\n                transform: translate(-50%, -50%);\r\n\r\n                &::after {\r\n                  border-top-color: transparent !important;\r\n                  border-right-color: transparent !important;\r\n                }\r\n              }\r\n\r\n              .GlobalChatVoice {\r\n                padding: 0 26px;\r\n                position: relative;\r\n                cursor: pointer;\r\n\r\n                &::after {\r\n                  content: '';\r\n                  width: 26px;\r\n                  height: 19px;\r\n                  position: absolute;\r\n                  top: 50%;\r\n                  left: 0;\r\n                  transform: translateY(-50%);\r\n                  background: url('../img/record_receive.png') no-repeat;\r\n                  background-size: auto 19px;\r\n                  background-position: center left;\r\n                }\r\n\r\n                &.is-active {\r\n                  &::after {\r\n                    background: url('../img/record_receive_gif.gif') no-repeat;\r\n                    background-size: auto 19px;\r\n                    background-position: center left;\r\n                  }\r\n                }\r\n              }\r\n\r\n              .GlobalChatVoiceContinue {\r\n                width: 68px;\r\n                height: 20px;\r\n                font-size: 10px;\r\n                line-height: 20px;\r\n                position: absolute;\r\n                top: 50%;\r\n                left: 110%;\r\n                transform: translateY(-50%);\r\n                color: var(--zy-el-text-color-regular);\r\n                background: #e6e6e6;\r\n                text-align: center;\r\n                border-radius: 10px;\r\n                cursor: pointer;\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesCustom {\r\n              span {\r\n                left: 0;\r\n                transform: translate(-50%, -50%);\r\n\r\n                &::after {\r\n                  border-top-color: transparent !important;\r\n                  border-right-color: transparent !important;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesFile {\r\n              span {\r\n                left: 0;\r\n                transform: translate(-50%, -50%);\r\n\r\n                &::after {\r\n                  border-top-color: transparent !important;\r\n                  border-right-color: transparent !important;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .GlobalChatSelfMessages {\r\n          flex-direction: row-reverse;\r\n\r\n          .GlobalChatMessagesInfo {\r\n            justify-content: flex-end;\r\n            padding-left: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n\r\n            .GlobalChatMessagesText {\r\n              span {\r\n                right: 0;\r\n                transform: translate(50%, -50%);\r\n\r\n                &::after {\r\n                  border-left-color: transparent !important;\r\n                  border-bottom-color: transparent !important;\r\n                }\r\n              }\r\n\r\n              .GlobalChatVoice {\r\n                padding: 0 26px;\r\n                position: relative;\r\n                cursor: pointer;\r\n                text-align: right;\r\n\r\n                &::after {\r\n                  content: '';\r\n                  width: 26px;\r\n                  height: 19px;\r\n                  position: absolute;\r\n                  top: 50%;\r\n                  right: 0;\r\n                  transform: translateY(-50%) rotate(180deg);\r\n                  background: url('../img/record_receive.png') no-repeat;\r\n                  background-size: auto 19px;\r\n                  background-position: center left;\r\n                }\r\n\r\n                &.is-active {\r\n                  &::after {\r\n                    background: url('../img/record_receive_gif.gif') no-repeat;\r\n                    background-size: auto 19px;\r\n                    background-position: center left;\r\n                  }\r\n                }\r\n              }\r\n\r\n              .GlobalChatVoiceContinue {\r\n                width: 68px;\r\n                height: 20px;\r\n                font-size: 10px;\r\n                line-height: 20px;\r\n                position: absolute;\r\n                top: 50%;\r\n                right: 110%;\r\n                transform: translateY(-50%);\r\n                color: var(--zy-el-text-color-regular);\r\n                background: #e6e6e6;\r\n                text-align: center;\r\n                border-radius: 10px;\r\n                cursor: pointer;\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesCustom {\r\n              span {\r\n                right: 0;\r\n                transform: translate(50%, -50%);\r\n\r\n                &::after {\r\n                  border-left-color: transparent !important;\r\n                  border-bottom-color: transparent !important;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesFile {\r\n              span {\r\n                right: 0;\r\n                transform: translate(50%, -50%);\r\n\r\n                &::after {\r\n                  border-left-color: transparent !important;\r\n                  border-bottom-color: transparent !important;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatViewMenu {\r\n    position: fixed;\r\n    background: #fff;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border-radius: var(--el-border-radius-base);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n    padding: 6px;\r\n    z-index: 9;\r\n\r\n    .GlobalChatViewMenuItem {\r\n      min-width: 52px;\r\n      font-size: 12px;\r\n      padding: 3px 9px;\r\n      border-radius: 4px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background: #f8f8fa;\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewMenuLine {\r\n      width: 100%;\r\n      height: 7px;\r\n      position: relative;\r\n\r\n      &::after {\r\n        content: '';\r\n        width: calc(100% - 16px);\r\n        border-top: 1px solid var(--zy-el-border-color-light);\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalGroupNamePopupWindow {\r\n    .chat-popup-window-body {\r\n      height: 168px;\r\n    }\r\n  }\r\n\r\n  .GlobalGroupQrPopupWindow {\r\n    .chat-popup-window-body {\r\n      height: auto;\r\n    }\r\n  }\r\n\r\n  .GlobalGroupAnnouncementPopupWindow {\r\n    .chat-popup-window-body {\r\n      height: auto;\r\n    }\r\n  }\r\n\r\n  .GlobalGroupVotePopupWindow {\r\n    .chat-popup-window-body {\r\n      height: 82%;\r\n      max-height: 680px;\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatViewAutocomplete {\r\n  .GlobalChatViewMessagesItem {\r\n    width: 218px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 10px 0;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      background: #f9f9fa;\r\n    }\r\n\r\n    &.is-top {\r\n      background: #f6f6f6;\r\n    }\r\n\r\n    &.is-active {\r\n      background: #f2f2f2;\r\n    }\r\n\r\n    .zy-el-badge {\r\n      width: 42px;\r\n      height: 42px;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 42px;\r\n      height: 42px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .GlobalChatViewMessagesInfo {\r\n      width: calc(100% - 56px);\r\n      height: 42px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      line-height: normal;\r\n      position: relative;\r\n\r\n      .GlobalChatViewNotInform {\r\n        width: 16px;\r\n        height: 16px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        position: absolute;\r\n        right: 0;\r\n        bottom: 0;\r\n\r\n        path {\r\n          fill: var(--zy-el-text-color-secondary);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewMessagesName {\r\n        display: flex;\r\n\r\n        div {\r\n          flex: 1;\r\n          font-size: 14px;\r\n        }\r\n\r\n        span {\r\n          font-size: 12px;\r\n          color: var(--zy-el-text-color-secondary);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewNotInform+.GlobalChatViewMessagesText {\r\n        padding-right: 18px;\r\n      }\r\n\r\n      .GlobalChatViewMessagesText {\r\n        font-size: 12px;\r\n        color: var(--zy-el-text-color-secondary);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAGSA,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC;AAAwB;;EAIxBA,KAAK,EAAC;AAAyC;;EAI7CA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAU;iBAdvC;;EAAAC,GAAA;EAkBqBD,KAAK,EAAC;;;EAlB3BC,GAAA;EAqBqBD,KAAK,EAAC;;;EArB3BC,GAAA;EAwBqBD,KAAK,EAAC;;;EAxB3BC,GAAA;EA2BqBD,KAAK,EAAC;;;EA3B3BC,GAAA;EA8BqBD,KAAK,EAAC;;;EA9B3BC,GAAA;EAiCqBD,KAAK,EAAC;;;EAjC3BC,GAAA;EAoCqBD,KAAK,EAAC;;kBApC3B;kBAAA;;EAkDeA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAA4B;;EAnDnDC,GAAA;EAoDmBD,KAAK,EAAC;;;EApDzBC,GAAA;EAqDmBD,KAAK,EAAC;;kBArDzB;;EAAAC,GAAA;EA6DiBD,KAAK,EAAC;;;EA7DvBC,GAAA;EAgEiBD,KAAK,EAAC;;;EAhEvBC,GAAA;EAiEiBD,KAAK,EAAC;;;EAjEvBC,GAAA;EAoEiBD,KAAK,EAAC;;;EApEvBC,GAAA;EAuEiBD,KAAK,EAAC;;;EAvEvBC,GAAA;EA0EiBD,KAAK,EAAC;;;EA1EvBC,GAAA;EA6EiBD,KAAK,EAAC;;kBA7EvB;;EAoGWA,KAAK,EAAC;AAAoC;;EApGrDC,GAAA;AAAA;;EAAAA,GAAA;EAiHaD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAkC;kBAlHvD;;EA2HeA,KAAK,EAAC;AAAoC;;EAE5CA,KAAK,EAAC;AAAsB;;EAQtBA,KAAK,EAAC;AAAyB;;EAG/BA,KAAK,EAAC;AAAwB;kBAxIjD;kBAAA;kBAAA;kBAAA;kBAAA;kBAAA;kBAAA;;EAiLyBA,KAAK,EAAC;AAA8B;;EACpCA,KAAK,EAAC;AAA8B;;EAKpCA,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC;AAA6B;kBAxL9D;;EA4L2BA,KAAK,EAAC;AAA4B;;EAEpCA,KAAK,EAAC;AAA4B;kBA9L3D;;EAgM2BA,KAAK,EAAC;AAA4B;;EAhM7DC,GAAA;EAoMyBD,KAAK,EAAC;;kBApM/B;;EA2MuBA,KAAK,EAAC;AAA4B;;EAClCA,KAAK,EAAC;AAA4B;;EA5MzDC,GAAA;EAyNaD,KAAK,EAAC;;;EAzNnBC,GAAA;EAiOaD,KAAK,EAAC;;;EAaRA,KAAK,EAAC;AAAyB;;EA9O1CC,GAAA;EAyPSD,KAAK,EAAC;;;;;;;;;;;;;uBAxPbE,mBAAA,CAiSM;IAjSDF,KAAK,EADZG,eAAA,EACa,gBAAgB;MAAAC,iBAAA,EAA8BC,MAAA,CAAAC;IAAK;IAAKC,OAAK,EAD1EC,cAAA,CACoFH,MAAA,CAAAI,aAAa;IAC5FC,aAAW,EAFhBF,cAAA,CAE0BH,MAAA,CAAAM,mBAAmB;MACzCC,mBAAA,CA+FM,OA/FNC,UA+FM,GA9FJD,mBAAA,CAsCM,OAtCNE,UAsCM,GArCJC,YAAA,CAmCkBC,0BAAA;IAxC1BC,UAAA,EAKkCZ,MAAA,CAAAa,OAAO;IALzC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAKkCf,MAAA,CAAAa,OAAO,GAAAE,MAAA;IAAA;IAAG,aAAW,EAAEf,MAAA,CAAAgB,MAAM;IAAG,mBAAiB,EAAEhB,MAAA,CAAAiB,WAAW;IAAEC,WAAW,EAAC,IAAI;IACxG,cAAY,EAAC,4BAA4B;IAACC,SAAS,EAAT,EAAS;IAAEC,QAAM,EAAEpB,MAAA,CAAAqB;;IAClDC,OAAO,EAAAC,QAAA,CAChB,UAAAC,IAAA;MAAA,IAAAC,qBAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;MAAA,IADoBC,IAAI,GAAAN,IAAA,CAAJM,IAAI;MAAA,QACxBvB,mBAAA,CA8BM,OA9BNwB,UA8BM,GA7BJrB,YAAA,CAEWsB,mBAAA;QAFAC,KAAK,EAAEH,IAAI,CAACI,KAAK;QAAGC,MAAM,GAAGL,IAAI,CAACI,KAAK;QAAG,QAAM,EAAEJ,IAAI,CAACM,WAAW;;QAT3Fd,OAAA,EAAAC,QAAA,CAUgB;UAAA,IAAAc,oBAAA;UAAA,OAAkF,CAAlF3B,YAAA,CAAkF4B,mBAAA;YAAvEC,GAAG,EAAEvC,MAAA,CAAAwC,MAAM,EAAAH,oBAAA,GAACP,IAAI,CAACW,cAAc,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBK,GAAG;YAAGC,GAAG,EAAC,OAAO;YAACC,SAAS,EAAC;;;QAVxFC,CAAA;0EAYctC,mBAAA,CAyBM,OAzBNuC,UAyBM,GAxBJvC,mBAAA,CAGM,OAHNwC,UAGM,GAFJxC,mBAAA,CAA2D,OAA3DyC,UAA2D,EAAAC,gBAAA,EAAAxB,qBAAA,GAAlCK,IAAI,CAACW,cAAc,cAAAhB,qBAAA,uBAAnBA,qBAAA,CAAqByB,IAAI,kBAClD3C,mBAAA,CAAmD,cAAA0C,gBAAA,CAA1CjD,MAAA,CAAAmD,gBAAgB,CAACrB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,QAAQ,kB,GAE2BtB,IAAI,CAACM,WAAW,U,cAArFvC,mBAAA,CAAmG;QAjBnHD,GAAA;QAiBqBD,KAAK,EAAC,yBAAyB;QAAC0D,SAAyB,EAAjBrD,MAAA,CAAAsD;8BAjB7DC,UAAA,KAAAC,mBAAA,gBAkBuE1B,IAAI,CAAC2B,WAAW,wB,cAAvE5D,mBAAA,CAEM,OAFN6D,UAEM,EAAAT,gBAAA,CADDnB,IAAI,aAAJA,IAAI,gBAAAJ,aAAA,GAAJI,IAAI,CAAE6B,OAAO,cAAAjC,aAAA,uBAAbA,aAAA,CAAekC,KAAK,oBAEmC9B,IAAI,CAAC2B,WAAW,oB,cAA5E5D,mBAAA,CAEM,OAFNgE,UAEM,EAFwF,QAE9F,KAC4D/B,IAAI,CAAC2B,WAAW,qB,cAA5E5D,mBAAA,CAEM,OAFNiE,WAEM,EAFyF,QACxF,GAAAb,gBAAA,CAAGnB,IAAI,aAAJA,IAAI,gBAAAH,cAAA,GAAJG,IAAI,CAAE6B,OAAO,cAAAhC,cAAA,uBAAbA,cAAA,CAAeoC,QAAQ,IAAG,KACpC,mBAC4DjC,IAAI,CAAC2B,WAAW,oB,cAA5E5D,mBAAA,CAEM,OAFNmE,WAEM,EAAAf,gBAAA,CADDnB,IAAI,aAAJA,IAAI,gBAAAF,cAAA,GAAJE,IAAI,CAAE6B,OAAO,cAAA/B,cAAA,uBAAbA,cAAA,CAAesB,IAAI,oBAEoCpB,IAAI,CAAC2B,WAAW,oB,cAA5E5D,mBAAA,CAEM,OAFNoE,WAEM,EAFwF,sBAE9F,KAC4DnC,IAAI,CAAC2B,WAAW,mB,cAA5E5D,mBAAA,CAEM,OAFNqE,WAEM,EAAAjB,gBAAA,CADDnB,IAAI,CAACqC,iBAAiB,qB,cAE3BtE,mBAAA,CAA0F,OAA1FuE,WAA0F,EAAAnB,gBAAA,CAA/BnB,IAAI,aAAJA,IAAI,gBAAAD,cAAA,GAAJC,IAAI,CAAE6B,OAAO,cAAA9B,cAAA,uBAAbA,cAAA,CAAe8B,OAAO,kB;;IApCjGd,CAAA;oDAyCQtC,mBAAA,CAAwG;IAAnGZ,KAAK,EAAC,4BAA4B;IAAC0D,SAA0B,EAAlBrD,MAAA,CAAAqE,iBAAiB;IAAGnE,OAAK,EAAAY,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEf,MAAA,CAAAsE,iBAAiB;IAAA;0BAzCpGC,WAAA,E,GA2CM7D,YAAA,CAqCe8D,uBAAA;IArCD7E,KAAK,EAAC;EAA4B;IA3CtD2B,OAAA,EAAAC,QAAA,CA6CU;MAAA,OAAwB,E,kBAD1B1B,mBAAA,CAmCM4E,SAAA,QA/EdC,WAAA,CA6CyB1E,MAAA,CAAA2E,QAAQ,EA7CjC,UA6CiB7C,IAAI;QAAA,IAAA8C,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;6BADbrF,mBAAA,CAmCM;UAnCAF,KAAK,EA5CnBG,eAAA;YAAA,UA4CgEgC,IAAI,CAACqD;UAAK;YAAA,aAAmBrD,IAAI,CAACsD,EAAE,KAAKpF,MAAA,CAAAqF;UAAM;UAC3EzF,GAAG,EAAEkC,IAAI,CAACsD,EAAE;UAAGlF,OAAK,WAALA,OAAKA,CAAAa,MAAA;YAAA,OAAEf,MAAA,CAAAqB,WAAW,CAACS,IAAI;UAAA;UAC/DzB,aAAW,EA9CtBF,cAAA,WAAAY,MAAA;YAAA,OA8CgCf,MAAA,CAAAsF,cAAc,CAACvE,MAAM,EAAEe,IAAI;UAAA;YACjDpB,YAAA,CAEWsB,mBAAA;UAFAC,KAAK,EAAEH,IAAI,CAACI,KAAK;UAAGC,MAAM,GAAGL,IAAI,CAACI,KAAK;UAAG,QAAM,EAAEJ,IAAI,CAACM,WAAW;;UA/CvFd,OAAA,EAAAC,QAAA,CAgDY;YAAA,IAAAgE,qBAAA;YAAA,OAAkF,CAAlF7E,YAAA,CAAkF4B,mBAAA;cAAvEC,GAAG,EAAEvC,MAAA,CAAAwC,MAAM,EAAA+C,qBAAA,GAACzD,IAAI,CAACW,cAAc,cAAA8C,qBAAA,uBAAnBA,qBAAA,CAAqB7C,GAAG;cAAGC,GAAG,EAAC,OAAO;cAACC,SAAS,EAAC;;;UAhDpFC,CAAA;4EAkDUtC,mBAAA,CA4BM,OA5BNiF,WA4BM,GA3BJjF,mBAAA,CAQM,OARNkF,WAQM,G,2BAPyB3D,IAAI,CAACW,cAAc,cAAAmC,qBAAA,eAAnBA,qBAAA,CAAqBc,aAAa,K,cAA/D7F,mBAAA,CAAsG,OAAtG8F,WAAsG,EAAA1C,gBAAA,EAAA4B,qBAAA,GAAlC/C,IAAI,CAACW,cAAc,cAAAoC,qBAAA,uBAAnBA,qBAAA,CAAqB3B,IAAI,oBApD3GM,mBAAA,gBAsDsB1B,IAAI,CAAC8D,IAAI,UAAU9D,IAAI,CAACW,cAAc,CAACiD,aAAa,I,cAD5D7F,mBAAA,CAIM,OAJNgG,WAIM,GAzDpBC,gBAAA,CAAA7C,gBAAA,EAAA6B,qBAAA,GAuDmBhD,IAAI,CAACW,cAAc,cAAAqC,qBAAA,uBAAnBA,qBAAA,CAAqB5B,IAAI,IAAG,GAC/B,iBAAA3C,mBAAA,CAAoD,cAAA0C,gBAAA,CAA3CnB,IAAI,CAACW,cAAc,CAACiD,aAAa,iB,KAxD1DlC,mBAAA,gBA0DcjD,mBAAA,CAAmD,cAAA0C,gBAAA,CAA1CjD,MAAA,CAAAmD,gBAAgB,CAACrB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,QAAQ,kB,GAE2BtB,IAAI,CAACM,WAAW,U,cAArFvC,mBAAA,CAAmG;UA5D/GD,GAAA;UA4DiBD,KAAK,EAAC,yBAAyB;UAAC0D,SAAyB,EAAjBrD,MAAA,CAAAsD;gCA5DzDyC,WAAA,KAAAvC,mBAAA,gBA6DmE1B,IAAI,CAAC2B,WAAW,wB,cAAvE5D,mBAAA,CAEM,OAFNmG,WAEM,EAAA/C,gBAAA,CADDnB,IAAI,aAAJA,IAAI,gBAAAiD,cAAA,GAAJjD,IAAI,CAAE6B,OAAO,cAAAoB,cAAA,uBAAbA,cAAA,CAAenB,KAAK,oBAEmC9B,IAAI,CAAC2B,WAAW,oB,cAA5E5D,mBAAA,CAAwG,OAAxGoG,WAAwG,EAAV,MAAI,KACtCnE,IAAI,CAAC2B,WAAW,qB,cAA5E5D,mBAAA,CAEM,OAFNqG,WAEM,EAFyF,QACxF,GAAAjD,gBAAA,CAAGnB,IAAI,aAAJA,IAAI,gBAAAkD,cAAA,GAAJlD,IAAI,CAAE6B,OAAO,cAAAqB,cAAA,uBAAbA,cAAA,CAAejB,QAAQ,IAAG,KACpC,mBAC4DjC,IAAI,CAAC2B,WAAW,oB,cAA5E5D,mBAAA,CAEM,OAFNsG,WAEM,EAAAlD,gBAAA,CADDnB,IAAI,aAAJA,IAAI,gBAAAmD,cAAA,GAAJnD,IAAI,CAAE6B,OAAO,cAAAsB,cAAA,uBAAbA,cAAA,CAAe/B,IAAI,oBAEoCpB,IAAI,CAAC2B,WAAW,oB,cAA5E5D,mBAAA,CAEM,OAFNuG,WAEM,EAFwF,sBAE9F,KAC4DtE,IAAI,CAAC2B,WAAW,mB,cAA5E5D,mBAAA,CAEM,OAFNwG,WAEM,EAAApD,gBAAA,CADDnB,IAAI,CAACqC,iBAAiB,qB,cAE3BtE,mBAAA,CAA0F,OAA1FyG,WAA0F,EAAArD,gBAAA,CAA/BnB,IAAI,aAAJA,IAAI,gBAAAoD,cAAA,GAAJpD,IAAI,CAAE6B,OAAO,cAAAuB,cAAA,uBAAbA,cAAA,CAAevB,OAAO,kB,2CA7E7F4C,WAAA;;;IAAA1D,CAAA;MAiFMtC,mBAAA,CAGM;IAHDZ,KAAK,EAAC,qBAAqB;IAAEO,OAAK,EAAEF,MAAA,CAAAwG;MACvCjG,mBAAA,CAAkC;IAA7B8C,SAAsB,EAAdrD,MAAA,CAAAyG;EAAa,wBAlFlCC,WAAA,G,4BAAAZ,gBAAA,CAkF0C,QAEpC,G,mBACAvF,mBAAA,CAYM;IAZDZ,KAAK,EAAC,oBAAoB;IAAEgH,KAAK,EArF5CC,eAAA;MAAAC,IAAA,EAqFsD7G,MAAA,CAAA8G,YAAY;MAAAC,GAAA,EAAc/G,MAAA,CAAAgH,WAAW;IAAA;OAERhH,MAAA,CAAAiH,YAAY,CAAC9B,KAAK,I,cAA7FtF,mBAAA,CAAuG;IAvF/GD,GAAA;IAuFaD,KAAK,EAAC,wBAAwB;IAAEO,OAAK,EAAAY,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEf,MAAA,CAAAkH,gBAAgB;IAAA;KAAmC,IAAE,KAvFzG1D,mBAAA,gBAwFmFxD,MAAA,CAAAiH,YAAY,CAAC9B,KAAK,I,cAA7FtF,mBAAA,CAAyG;IAxFjHD,GAAA;IAwFaD,KAAK,EAAC,wBAAwB;IAAEO,OAAK,EAAAY,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEf,MAAA,CAAAkH,gBAAgB;IAAA;KAAmC,MAAI,KAxF3G1D,mBAAA,gBAyFsFxD,MAAA,CAAAiH,YAAY,CAAC7E,WAAW,U,cAAtGvC,mBAAA,CAEM;IA3FdD,GAAA;IAyFaD,KAAK,EAAC,wBAAwB;IAAEO,OAAK,EAAAY,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEf,MAAA,CAAAmH,uBAAuB;IAAA;KAA2C,SAE9G,KA3FR3D,mBAAA,gBA4FsFxD,MAAA,CAAAiH,YAAY,CAAC7E,WAAW,U,cAAtGvC,mBAAA,CAEM;IA9FdD,GAAA;IA4FaD,KAAK,EAAC,wBAAwB;IAAEO,OAAK,EAAAY,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEf,MAAA,CAAAmH,uBAAuB;IAAA;KAA2C,UAE9G,KA9FR3D,mBAAA,gB,4BA+FQjD,mBAAA,CAA0C;IAArCZ,KAAK,EAAC;EAAwB,6BACnCY,mBAAA,CAAmE;IAA9DZ,KAAK,EAAC,wBAAwB;IAAEO,OAAK,EAAEF,MAAA,CAAAoH;KAAe,IAAE,E,4BAVrDpH,MAAA,CAAAqH,YAAY,E,KAaiDrH,MAAA,CAAAqF,MAAM,I,cAA/ExF,mBAAA,CAqJM;IAxPVD,GAAA;IAmGSD,KAAK,EAAC,kBAAkB;IAAE2H,UAAQ,EAAAxG,MAAA,QAAAA,MAAA,MAnG3CX,cAAA,CAmGkC,cAAiB;IAAEoH,MAAI,EAAEvH,MAAA,CAAAwH;MACrDjH,mBAAA,CAUM,OAVNkH,WAUM,GATJlH,mBAAA,CAGM;IAHDZ,KAAK,EAAC,UAAU;IAAEO,OAAK,EAAEF,MAAA,CAAA0H;MArGtC5B,gBAAA,CAAA7C,gBAAA,EAAA0E,qBAAA,GAsGa3H,MAAA,CAAA4H,QAAQ,CAACnF,cAAc,cAAAkF,qBAAA,uBAAvBA,qBAAA,CAAyBzE,IAAI,IAAG,GACnC,iBAAYlD,MAAA,CAAA4H,QAAQ,CAAChC,IAAI,UAAU5F,MAAA,CAAA6H,SAAS,CAACC,MAAM,I,cAAnDjI,mBAAA,CAAoF,QAvG9FkI,WAAA,EAuG+D,GAAC,GAAA9E,gBAAA,CAAGjD,MAAA,CAAA6H,SAAS,CAACC,MAAM,IAAG,GAAC,mBAvGvFtE,mBAAA,e,GAyGQjD,mBAAA,CAIM;IAJDZ,KAAK,EAAC,sBAAsB;IAAEO,OAAK,EAAEF,MAAA,CAAA0H;MACxChH,YAAA,CAEUsH,kBAAA;IA5GpB1G,OAAA,EAAAC,QAAA,CA2GY;MAAA,OAAc,CAAdb,YAAA,CAAcuH,qBAAA,E;;IA3G1BpF,CAAA;UA+GMnC,YAAA,CAqGe8D,uBAAA;IArGD0D,GAAG,EAAC,WAAW;IAACC,MAAM,EAAN,EAAM;IAACxI,KAAK,EA/GhDG,eAAA,EA+GiD,wBAAwB;MAAAsI,sBAAA,GAAoCpI,MAAA,CAAAqI;IAAM;IAC1GC,QAAM,EAAEtI,MAAA,CAAAuI;;IAhHjBjH,OAAA,EAAAC,QAAA,CAqJI;MAAA,OAgBwF,CApDrCvB,MAAA,CAAAwI,uBAAuB,I,cAAtE3I,mBAAA,CAWM,OAXN4I,WAWM,GAVJlI,mBAAA,CAQM,OARNmI,WAQM,GAPJnI,mBAAA,CAGM,cAFJA,mBAAA,CAAuC;QAAjC8C,SAAyB,EAAjBrD,MAAA,CAAA2I;MAAgB,wBApH5CC,WAAA,G,4BAAA9C,gBAAA,CAoHqD,OAEzC,G,GACApF,YAAA,CAEUsH,kBAAA;QAFA9H,OAAK,EAAEF,MAAA,CAAA6I;MAA2B;QAvHxDvH,OAAA,EAAAC,QAAA,CAwHc;UAAA,OAAqB,CAArBb,YAAA,CAAqBoI,4BAAA,E;;QAxHnCjG,CAAA;YA2HUtC,mBAAA,CAAiF,OAAjFwI,WAAiF,EAAA9F,gBAAA,CAA9BjD,MAAA,CAAAgJ,qBAAqB,iB,KA3HlFxF,mBAAA,gBA6HQjD,mBAAA,CAsFM,OAtFN0I,WAsFM,I,kBArFJpJ,mBAAA,CAoFM4E,SAAA,QAlNhBC,WAAA,CA8H8B1E,MAAA,CAAAkJ,oBAAoB,EA9HlD,UA8HsBpH,IAAI;QAAA,IAAAqH,cAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,KAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA;6BAAhBvK,mBAAA,CAoFM;UApFqCD,GAAG,EAAEkC,IAAI,CAACsD,EAAE,IAAItD,IAAI,CAACuI,GAAG;UAAG1K,KAAK,EA9HrFG,eAAA,CA8HuFgC,IAAI,CAACwI,SAAS;YACzExI,IAAI,CAAC8D,IAAI,e,cAAzB/F,mBAAA,CAAoE4E,SAAA;UA/HhF7E,GAAA;QAAA,IAAAkG,gBAAA,CAAA7C,gBAAA,CA+HqDnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,OAAO,iB,+BACjC7B,IAAI,CAAC8D,IAAI,oB,cAA9B/F,mBAAA,CAAoF4E,SAAA;UAhIhG7E,GAAA;QAAA,IAAAkG,gBAAA,CAAA7C,gBAAA,CAgI+DnB,IAAI,aAAJA,IAAI,gBAAAqH,cAAA,GAAJrH,IAAI,CAAE6B,OAAO,cAAAwF,cAAA,uBAAbA,cAAA,CAAejG,IAAI,iB,+BACjDpB,IAAI,CAAC8D,IAAI,mB,cAA9B/F,mBAAA,CAEW4E,SAAA;UAnIvB7E,GAAA;QAAA,IAAAkG,gBAAA,CAAA7C,gBAAA,CAkIiBnB,IAAI,CAACyI,kBAAkB,SAASzI,IAAI,CAAC0I,QAAQ,IAAG,UACrD,gB,8CACA3K,mBAAA,CA6EW4E,SAAA;UAjNvB7E,GAAA;QAAA,IAqIcW,mBAAA,CAEM,OAFNkK,WAEM,GADJ/J,YAAA,CAAkF4B,mBAAA;UAAvEC,GAAG,EAAEvC,MAAA,CAAAwC,MAAM,EAAA4G,qBAAA,GAACtH,IAAI,CAACW,cAAc,cAAA2G,qBAAA,uBAAnBA,qBAAA,CAAqB1G,GAAG;UAAGC,GAAG,EAAC,OAAO;UAACC,SAAS,EAAC;4CAE1ErC,mBAAA,CAwEM,OAxENmK,WAwEM,G,CAtEK5I,IAAI,CAACyI,kBAAkB,I,cADhC1K,mBAAA,CAGM;UA5ItBD,GAAA;UAyIqBD,KAAK,EAAC,iCAAiC;UAAEU,aAAW,EAzIzEF,cAAA,WAAAY,MAAA;YAAA,OAyImFf,MAAA,CAAA2K,kBAAkB,CAAC5J,MAAM,EAAEe,IAAI;UAAA;qDAE7FA,IAAI,CAACW,cAAc,cAAA4G,qBAAA,uBAAnBA,qBAAA,CAAqBnG,IAAI,yCA3I9C0H,WAAA,KAAApH,mBAAA,gBA8IwB1B,IAAI,CAAC8D,IAAI,oB,cADjB/F,mBAAA,CAIM;UAjJtBD,GAAA;UA6IqBD,KAAK,EAAC,wBAAwB;UAAEU,aAAW,EA7IhEF,cAAA,WAAAY,MAAA;YAAA,OA6I0Ef,MAAA,CAAA2K,kBAAkB,CAAC5J,MAAM,EAAEe,IAAI;UAAA;wCAEvFvB,mBAAA,CAAa,wCACbA,mBAAA,CAAyE;UAApEZ,KAAK,EAAC,mBAAmB;UAAC0D,SAAmC,EAA3BvB,IAAI,aAAJA,IAAI,gBAAAwH,eAAA,GAAJxH,IAAI,CAAE6B,OAAO,cAAA2F,eAAA,uBAAbA,eAAA,CAAeuB;gCAhJxEC,WAAA,E,kCAAAC,WAAA,KAAAvH,mBAAA,gBAmJmD1B,IAAI,CAAC8D,IAAI,qB,cAD5C/F,mBAAA,CAWM;UA7JtBD,GAAA;UAkJqBD,KAAK,EAAC,wBAAwB;UAAEU,aAAW,EAlJhEF,cAAA,WAAAY,MAAA;YAAA,OAkJ0Ef,MAAA,CAAA2K,kBAAkB,CAAC5J,MAAM,EAAEe,IAAI;UAAA;UACtF5B,OAAK,WAALA,OAAKA,CAAAa,MAAA;YAAA,OAAEf,MAAA,CAAAgL,WAAW,CAAClJ,IAAI;UAAA;wCACxBvB,mBAAA,CAAa,wCACbA,mBAAA,CAGM;UAHAZ,KAAK,EArJ7BG,eAAA;YAAA,aAqJkEE,MAAA,CAAAiL,aAAa,CAAC7F,EAAE,KAAKtD,IAAI,CAACsD;UAAE;UACzEuB,KAAK,EAtJ1BC,eAAA;YAAAsE,KAAA,WAsJ8C,CAAApJ,IAAI,aAAJA,IAAI,gBAAAyH,eAAA,GAAJzH,IAAI,CAAE6B,OAAO,cAAA4F,eAAA,uBAAbA,eAAA,CAAexF,QAAQ;UAAA;4BAC9CjC,IAAI,aAAJA,IAAI,gBAAA0H,eAAA,GAAJ1H,IAAI,CAAE6B,OAAO,cAAA6F,eAAA,uBAAbA,eAAA,CAAezF,QAAQ,IAAG,KAC/B,+BAEQ/D,MAAA,CAAAiL,aAAa,CAAC7F,EAAE,KAAKtD,IAAI,CAACsD,EAAE,IAAIpF,MAAA,CAAAmL,gBAAgB,CAACrJ,IAAI,CAACsD,EAAE,K,cADhEvF,mBAAA,CAGM;UA5JxBD,GAAA;UAyJuBD,KAAK,EAAC,yBAAyB;UAAEO,OAAK,EAzJ7DC,cAAA,WAAAY,MAAA;YAAA,OAyJoEf,MAAA,CAAAoL,aAAa,CAACtJ,IAAI;UAAA;WACD,QAEnE,iBA5JlBuJ,WAAA,KAAA7H,mBAAA,e,kCAAA8H,WAAA,KAAA9H,mBAAA,gBAgKgF1B,IAAI,CAAC8D,IAAI,oB,cAFzE2F,YAAA,CAUWjJ,mBAAA;UAxK3B1C,GAAA;UA8J2B2C,GAAG,EAAET,IAAI,aAAJA,IAAI,gBAAA2H,eAAA,GAAJ3H,IAAI,CAAE6B,OAAO,cAAA8F,eAAA,uBAAbA,eAAA,CAAe+B,QAAQ;UAAE7I,GAAG,EAAC,OAAO;UAAE,kBAAgB,EAAE3C,MAAA,CAAAyL,WAAW;UAChF,eAAa,EAAE3J,IAAI,CAAC4J,QAAQ;UAAGC,MAAI,EAAE3L,MAAA,CAAA4L,aAAa;UAClDvL,aAAW,EAhK9BF,cAAA,WAAAY,MAAA;YAAA,OAgKwCf,MAAA,CAAA2K,kBAAkB,CAAC5J,MAAM,EAAEe,IAAI;UAAA;;UAC1C+J,KAAK,EAAAtK,QAAA,CACd;YAAA,OAIM,CAJNhB,mBAAA,CAIM;cAJDZ,KAAK,EAAC,2BAA2B;cAAEU,aAAW,EAlKvEF,cAAA,WAAAY,MAAA;gBAAA,OAkKiFf,MAAA,CAAA2K,kBAAkB,CAAC5J,MAAM,EAAEe,IAAI;cAAA;gBAC1FpB,YAAA,CAEUsH,kBAAA;cArKhC1G,OAAA,EAAAC,QAAA,CAoKwB;gBAAA,OAAW,CAAXb,YAAA,CAAWV,MAAA,a;;cApKnC6C,CAAA;iDAAAiJ,WAAA,E;;UAAAjJ,CAAA;8GAAAW,mBAAA,gB,+EA2KwDuI,QAAQ,CAACjK,IAAI,CAAC8D,IAAI,K,cAF1D/F,mBAAA,CA6BM;UAtMtBD,GAAA;UAyKsBD,KAAK,EAzK3BG,eAAA,8BAyK0DgC,IAAI,CAACkK,UAAU;UACtD3L,aAAW,EA1K9BF,cAAA,WAAAY,MAAA;YAAA,OA0KwCf,MAAA,CAAA2K,kBAAkB,CAAC5J,MAAM,EAAEe,IAAI;UAAA;UAAI5B,OAAK,WAALA,OAAKA,CAAAa,MAAA;YAAA,OAAEf,MAAA,CAAAiM,YAAY,CAACnK,IAAI;UAAA;wCAEjFvB,mBAAA,CAAa,wCACGuB,IAAI,CAACkK,UAAU,e,cAA/BnM,mBAAA,CAQW4E,SAAA;UArL7B7E,GAAA;QAAA,I,yBA+K4BI,MAAA,CAAAkM,cAAc,cAAAvC,qBAAA,eAAdA,qBAAA,CAAgBoC,QAAQ,CAACjK,IAAI,CAACqK,IAAI,CAAC/G,EAAE,K,cAD7CvF,mBAAA,CACsD;UA/K1ED,GAAA;UA8KyBD,KAAK,EAAC,gCAAgC;UAAEgH,KAAK,EA9KtEC,eAAA;YAAAsE,KAAA,EA8KiFlL,MAAA,CAAAoM,iBAAiB,CAACtK,IAAI,CAACqK,IAAI,CAAC/G,EAAE;UAAA;mCA9K/G5B,mBAAA,gBAgLoBjD,mBAAA,CAAwE;UAAnEZ,KAAK,EAhL9BG,eAAA,EAgL+B,gBAAgB,EAASE,MAAA,CAAAqM,QAAQ,EAAAzC,UAAA,GAAC9H,IAAI,CAACqK,IAAI,cAAAvC,UAAA,uBAATA,UAAA,CAAW0C,OAAO;iCAC/D/L,mBAAA,CAA2F,OAA3FgM,WAA2F,EAAAtJ,gBAAA,CAA9C,EAAA4G,WAAA,GAAA/H,IAAI,CAACqK,IAAI,cAAAtC,WAAA,uBAATA,WAAA,CAAW2C,gBAAgB,6BACxEjM,mBAAA,CAEM,OAFNkM,WAEM,EAAAxJ,gBAAA,CADD,CAAA6G,WAAA,GAAAhI,IAAI,CAACqK,IAAI,cAAArC,WAAA,eAATA,WAAA,CAAW4C,QAAQ,GAAG1M,MAAA,CAAA2M,QAAQ,CAAC7K,IAAI,CAACqK,IAAI,CAACO,QAAQ,0B,+BAnL1ElJ,mBAAA,gBAsLkC1B,IAAI,CAACkK,UAAU,e,cAA/BnM,mBAAA,CAYW4E,SAAA;UAlM7B7E,GAAA;QAAA,IAuLoBW,mBAAA,CAMM,OANNqM,WAMM,GALJrM,mBAAA,CAGM,OAHNsM,WAGM,GAFJtM,mBAAA,CAAoE;UAA/DZ,KAAK,EAAC,4BAA4B;UAAC0D,SAAqB,EAAbrD,MAAA,CAAA8M;gCAzLxEC,WAAA,G,4BAAAjH,gBAAA,CAyL4F,MAEtE,G,GACAvF,mBAAA,CAAiF,OAAjFyM,WAAiF,EAAA/J,gBAAA,CAAtCjD,MAAA,CAAAiN,MAAM,EAAAlD,UAAA,GAACjI,IAAI,CAACoL,IAAI,cAAAnD,UAAA,uBAATA,UAAA,CAAWoD,UAAU,kB,GAEzE5M,mBAAA,CAGM,OAHN6M,WAGM,GAFJ7M,mBAAA,CAAsE;UAAjEZ,KAAK,EAAC,gCAAgC;UAAC0D,SAAmB,EAAXrD,MAAA,CAAAqN;gCA/L1EC,WAAA,GAgMsB/M,mBAAA,CAAoE,OAApEgN,WAAoE,EAAAtK,gBAAA,EAAA+G,WAAA,GAAzBlI,IAAI,CAACoL,IAAI,cAAAlD,WAAA,uBAATA,WAAA,CAAWwD,KAAK,iB,iCAhMjFhK,mBAAA,gBAmMkC1B,IAAI,CAACkK,UAAU,kB,cAC7BnM,mBAAA,CAAqE,OAArE4N,WAAqE,EAAxB,oBAAkB,KApMnFjK,mBAAA,e,yCAAAkK,WAAA,KAAAlK,mBAAA,gBAwMwB1B,IAAI,CAAC8D,IAAI,qB,cADjB/F,mBAAA,CAQM;UA/MtBD,GAAA;UAuMqBD,KAAK,EAAC,wBAAwB;UAAEU,aAAW,EAvMhEF,cAAA,WAAAY,MAAA;YAAA,OAuM0Ef,MAAA,CAAA2K,kBAAkB,CAAC5J,MAAM,EAAEe,IAAI;UAAA;wCAEvFvB,mBAAA,CAAa,wCACbA,mBAAA,CAAyE;UAApEZ,KAAK,EA1M5BG,eAAA,EA0M6B,gBAAgB,EAASE,MAAA,CAAAqM,QAAQ,CAACvK,IAAI,aAAJA,IAAI,gBAAAmI,eAAA,GAAJnI,IAAI,CAAE6B,OAAO,cAAAsG,eAAA,uBAAbA,eAAA,CAAerE,IAAI;iCAChErF,mBAAA,CAAiF,OAAjFoN,WAAiF,EAAA1K,gBAAA,CAAtC,CAAAnB,IAAI,aAAJA,IAAI,gBAAAoI,eAAA,GAAJpI,IAAI,CAAE6B,OAAO,cAAAuG,eAAA,uBAAbA,eAAA,CAAehH,IAAI,6BAC9D3C,mBAAA,CAEM,OAFNqN,WAEM,EAAA3K,gBAAA,CADDnB,IAAI,aAAJA,IAAI,gBAAAqI,eAAA,GAAJrI,IAAI,CAAE6B,OAAO,cAAAwG,eAAA,eAAbA,eAAA,CAAe0D,IAAI,GAAG7N,MAAA,CAAA2M,QAAQ,CAAC7K,IAAI,aAAJA,IAAI,gBAAAsI,eAAA,GAAJtI,IAAI,CAAE6B,OAAO,cAAAyG,eAAA,uBAAbA,eAAA,CAAeyD,IAAI,0B,kCA7MzEC,WAAA,KAAAtK,mBAAA,e;;;IAAAX,CAAA;gDAqNMtC,mBAAA,CAqBM;IArBDZ,KAAK,EAAC,oBAAoB;IAAEgH,KAAK,EArN5CC,eAAA;MAAAC,IAAA,EAqNsD7G,MAAA,CAAA+N,gBAAgB;MAAAhH,GAAA,EAAc/G,MAAA,CAAAgO,eAAe;IAAA;MAE3FxK,mBAAA,uGAAsG,EACtGA,mBAAA,oDAAqD,EAE7CxD,MAAA,CAAAiO,gBAAgB,CAACrI,IAAI,mB,+BAD7B/F,mBAAA,CAGM,OAHNqO,WAGM,EAAApN,MAAA,SAAAA,MAAA,QA5NdgF,gBAAA,CA0NuD,MAE/C,E,iDAH4C9F,MAAA,CAAAiO,gBAAgB,cAAAE,qBAAA,uBAAhBA,qBAAA,CAAkBC,WAAW,E,IAzNjF5K,mBAAA,gBA8NgBxD,MAAA,CAAAqO,UAAU,IAAI,EAAAC,sBAAA,GAAAtO,MAAA,CAAAiO,gBAAgB,cAAAK,sBAAA,uBAAhBA,sBAAA,CAAkBtC,UAAU,gB,cADlDnM,mBAAA,CAGM;IAhOdD,GAAA;IA6NaD,KAAK,EAAC,wBAAwB;IAAEO,OAAK,EAAEF,MAAA,CAAAuO;sBAEvCvO,MAAA,CAAAwO,SAAS,qDA/NtBhL,mBAAA,gB,oBAiOkDxD,MAAA,CAAAyO,QAAQ,cAAAC,gBAAA,eAARA,gBAAA,CAAU3C,QAAQ,CAAC/L,MAAA,CAAAiO,gBAAgB,CAACrI,IAAI,K,cAAlF/F,mBAAA,CAA2F,OAA3F8O,WAA2F,KAjOnGnL,mBAAA,gB,CAmOiBxD,MAAA,CAAAiO,gBAAgB,CAAC1D,kBAAkB,KAAKvK,MAAA,CAAAiO,gBAAgB,CAACW,UAAU,I,cAD5E/O,mBAAA,CAGM;IArOdD,GAAA;IAkOaD,KAAK,EAAC,wBAAwB;IAAEO,OAAK,EAAEF,MAAA,CAAA6O;KACkC,MAE9E,KArORrL,mBAAA,gBAuOgBxD,MAAA,CAAAiO,gBAAgB,CAAC1D,kBAAkB,IAAIvK,MAAA,CAAAiO,gBAAgB,CAACW,UAAU,I,cAD1E/O,mBAAA,CAGM;IAzOdD,GAAA;IAsOaD,KAAK,EAAC,wBAAwB;IAAEO,OAAK,EAAEF,MAAA,CAAA8O;KACgC,MAE5E,KAzORtL,mBAAA,e,4BAsNgBxD,MAAA,CAAA+O,gBAAgB,E,mBAqB1BrO,YAAA,CAEmBV,MAAA;IAFDkI,GAAG,EAAC,WAAW;IAAE8G,MAAM,EAAEhP,MAAA,CAAA4H,QAAQ,CAAChC,IAAI;IAASqJ,QAAQ,EAAEjP,MAAA,CAAA6H,SAAS;IAAGqH,YAAU,EAAElP,MAAA,CAAAmP,UAAU;IAC1GC,YAAU,EAAEpP,MAAA,CAAAqP,UAAU;IAAGC,gBAAc,EAAEtP,MAAA,CAAAuP,cAAc;IAAGC,mBAAiB,EAAExP,MAAA,CAAAyP;6DAAuBzP,MAAA,CAAAqI,MAAM,E,mBAE7G9H,mBAAA,CAKM,OALNmP,WAKM,GAJJhP,YAAA,CAEUsH,kBAAA;IAjPlB1G,OAAA,EAAAC,QAAA,CAgPU;MAAA,OAAW,CAAXb,YAAA,CAAWiP,kBAAA,E;;IAhPrB9M,CAAA;kCAAAiD,gBAAA,CAiPkB,qBAEZ,G,oCAL8C9F,MAAA,CAAAqI,MAAM,E,GAMpD3H,YAAA,CAGuBV,MAAA;IAvP7BY,UAAA,EAoPqCZ,MAAA,CAAA4P,WAAW;IApPhD,uBAAA9O,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoPqCf,MAAA,CAAA4P,WAAW,GAAA7O,MAAA;IAAA;;IApPhDO,OAAA,EAAAC,QAAA,CAqPQ;MAAA,OAC4B,CAD5Bb,YAAA,CAC4BV,MAAA;QADL4H,QAAQ,EAAE5H,MAAA,CAAA4H,QAAQ;QAAGC,SAAS,EAAE7H,MAAA,CAAA6H,SAAS;QAAGgI,SAAO,EAAE7P,MAAA,CAAA8P,aAAa;QACtFC,UAAQ,EAAE/P,MAAA,CAAAgQ;;;IAtPrBnN,CAAA;kEAAAW,mBAAA,gB,CAyP2CxD,MAAA,CAAAqF,MAAM,I,cAA7CxF,mBAAA,CAAqD,OAArDoQ,WAAqD,KAzPzDzM,mBAAA,gBA0PI9C,YAAA,CAEoBV,MAAA;IA5PxBY,UAAA,EA0PgCZ,MAAA,CAAAkQ,QAAQ;IA1PxC,uBAAApP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA0PgCf,MAAA,CAAAkQ,QAAQ,GAAAnP,MAAA;IAAA;;IA1PxCO,OAAA,EAAAC,QAAA,CA2PM;MAAA,OAAgG,CAAhGb,YAAA,CAAgGV,MAAA;QAAjF4H,QAAQ,EAAE5H,MAAA,CAAA4H,QAAQ;QAAGuI,QAAQ,EAAEnQ,MAAA,CAAAmQ,QAAQ;QAAGJ,UAAQ,EAAE/P,MAAA,CAAAoQ;;;IA3PzEvN,CAAA;qCA6PInC,YAAA,CAEoBV,MAAA;IA/PxBY,UAAA,EA6PgCZ,MAAA,CAAAqQ,OAAO;IA7PvC,uBAAAvP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA6PgCf,MAAA,CAAAqQ,OAAO,GAAAtP,MAAA;IAAA;;IA7PvCO,OAAA,EAAAC,QAAA,CA8PM;MAAA,OAA2F,CAA3Fb,YAAA,CAA2FV,MAAA;QAA7E4H,QAAQ,EAAE5H,MAAA,CAAA4H,QAAQ;QAAG0I,OAAO,EAAEtQ,MAAA,CAAAsQ,OAAO;QAAGP,UAAQ,EAAE/P,MAAA,CAAAuQ;;;IA9PtE1N,CAAA;qCAgQInC,YAAA,CAEoBV,MAAA;IAlQxBY,UAAA,EAgQgCZ,MAAA,CAAAwQ,eAAe;IAhQ/C,uBAAA1P,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAgQgCf,MAAA,CAAAwQ,eAAe,GAAAzP,MAAA;IAAA;;IAhQ/CO,OAAA,EAAAC,QAAA,CAiQM;MAAA,OAAmF,CAAnFb,YAAA,CAAmFV,MAAA;QAA/DyQ,MAAM,EAAEzQ,MAAA,CAAAyQ,MAAM;QAAGV,UAAQ,EAAE/P,MAAA,CAAA0Q;;;IAjQrD7N,CAAA;qCAmQInC,YAAA,CAEoBV,MAAA;IArQxBY,UAAA,EAmQgCZ,MAAA,CAAA2Q,OAAO;IAnQvC,uBAAA7P,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAmQgCf,MAAA,CAAA2Q,OAAO,GAAA5P,MAAA;IAAA;;IAnQvCO,OAAA,EAAAC,QAAA,CAoQM;MAAA,OAAkF,CAAlFb,YAAA,CAAkFV,MAAA;QAA7D4Q,MAAM,EAAE5Q,MAAA,CAAA4Q,MAAM;QAAGb,UAAQ,EAAE/P,MAAA,CAAA6Q;;;IApQtDhO,CAAA;qCAsQInC,YAAA,CAEoBV,MAAA;IAxQxBY,UAAA,EAsQgCZ,MAAA,CAAA8Q,OAAO;IAtQvC,uBAAAhQ,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAsQgCf,MAAA,CAAA8Q,OAAO,GAAA/P,MAAA;IAAA;;IAtQvCO,OAAA,EAAAC,QAAA,CAuQM;MAAA,OAAkF,CAAlFb,YAAA,CAAkFV,MAAA;QAA7D4Q,MAAM,EAAE5Q,MAAA,CAAA4Q,MAAM;QAAGb,UAAQ,EAAE/P,MAAA,CAAA+Q;;;IAvQtDlO,CAAA;qCAyQInC,YAAA,CAEoBV,MAAA;IA3QxBY,UAAA,EAyQgCZ,MAAA,CAAAgR,QAAQ;IAzQxC,uBAAAlQ,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAyQgCf,MAAA,CAAAgR,QAAQ,GAAAjQ,MAAA;IAAA;IAAEpB,KAAK,EAAC;;IAzQhD2B,OAAA,EAAAC,QAAA,CA0QM;MAAA,OAA6E,CAA7Eb,YAAA,CAA6EV,MAAA;QAA3D4Q,MAAM,EAAE5Q,MAAA,CAAA4Q,MAAM;QAAGb,UAAQ,EAAE/P,MAAA,CAAAiR;;;IA1QnDpO,CAAA;qCA4QInC,YAAA,CAEoBV,MAAA;IA9QxBY,UAAA,EA4QgCZ,MAAA,CAAAkR,MAAM;IA5QtC,uBAAApQ,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OA4QgCf,MAAA,CAAAkR,MAAM,GAAAnQ,MAAA;IAAA;IAAEpB,KAAK,EAAC;;IA5Q9C2B,OAAA,EAAAC,QAAA,CA6QM;MAAA,OAAgD,CAAhDb,YAAA,CAAgDV,MAAA;QAAhC4Q,MAAM,EAAE5Q,MAAA,CAAA4Q;MAAM,oC;;IA7QpC/N,CAAA;qCA+QInC,YAAA,CAGoBV,MAAA;IAlRxBY,UAAA,EA+QgCZ,MAAA,CAAAmR,gBAAgB;IA/QhD,uBAAArQ,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OA+QgCf,MAAA,CAAAmR,gBAAgB,GAAApQ,MAAA;IAAA;IAAEpB,KAAK,EAAC;;IA/QxD2B,OAAA,EAAAC,QAAA,CAgRM;MAAA,OAC0B,CAD1Bb,YAAA,CAC0BV,MAAA;QADA4Q,MAAM,EAAE5Q,MAAA,CAAA4Q,MAAM;QAAGQ,OAAO,EAAEpR,MAAA,CAAAqR,YAAY;QAAGtB,UAAQ,EAAE/P,MAAA,CAAAsR;;;IAhRnFzO,CAAA;qCAmRInC,YAAA,CAEoBV,MAAA;IArRxBY,UAAA,EAmRgCZ,MAAA,CAAAuR,YAAY;IAnR5C,uBAAAzQ,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAmRgCf,MAAA,CAAAuR,YAAY,GAAAxQ,MAAA;IAAA;;IAnR5CO,OAAA,EAAAC,QAAA,CAoRM;MAAA,OAAyF,CAAzFb,YAAA,CAAyFV,MAAA;QAAnE4Q,MAAM,EAAE5Q,MAAA,CAAA4Q,MAAM;QAAGb,UAAQ,EAAE/P,MAAA,CAAAwR;;;IApRvD3O,CAAA;qCAsRInC,YAAA,CAIoBV,MAAA;IA1RxBY,UAAA,EAsRgCZ,MAAA,CAAAyR,QAAQ;IAtRxC,uBAAA3Q,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAsRgCf,MAAA,CAAAyR,QAAQ,GAAA1Q,MAAA;IAAA;IAAEpB,KAAK,EAAC;;IAtRhD2B,OAAA,EAAAC,QAAA,CAuRM;MAAA,OAEkB,CAFlBb,YAAA,CAEkBV,MAAA;QAFAoF,EAAE,EAAEpF,MAAA,CAAA4Q,MAAM;QAAGc,OAAO,EAAE1R,MAAA,CAAA2R,WAAW;QAAG5B,UAAQ,EAAE/P,MAAA,CAAA4R,YAAY;QACzEC,aAAW,EAAE7R,MAAA,CAAA8R;;;IAxRtBjP,CAAA;qCA2RInC,YAAA,CAEoBV,MAAA;IA7RxBY,UAAA,EA2RgCZ,MAAA,CAAA+R,cAAc;IA3R9C,uBAAAjR,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OA2RgCf,MAAA,CAAA+R,cAAc,GAAAhR,MAAA;IAAA;;IA3R9CO,OAAA,EAAAC,QAAA,CA4RM;MAAA,OAAqF,CAArFb,YAAA,CAAqFV,MAAA;QAAlEgS,MAAM,EAAEhS,MAAA,CAAA4Q,MAAM;QAAGb,UAAQ,EAAE/P,MAAA,CAAAiS;;;IA5RpDpP,CAAA;qCA8RInC,YAAA,CAGoBV,MAAA;IAjSxBY,UAAA,EA8RgCZ,MAAA,CAAAkS,eAAe;IA9R/C,uBAAApR,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OA8RgCf,MAAA,CAAAkS,eAAe,GAAAnR,MAAA;IAAA;IAAEpB,KAAK,EAAC;;IA9RvD2B,OAAA,EAAAC,QAAA,CA+RM;MAAA,OACoB,CADpBb,YAAA,CACoBV,MAAA;QADAoF,EAAE,EAAEpF,MAAA,CAAAmS,MAAM;QAAGpC,UAAQ,EAAE/P,MAAA,CAAAiS,kBAAkB;QAAGJ,aAAW,EAAE7R,MAAA,CAAA8R;;;IA/RnFjP,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}