{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted, nextTick, defineAsyncComponent } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { size2Str } from 'common/js/utils.js';\nimport { guid, svg } from '../../AiToolBox/AiToolBox.js';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'ContentExtraction'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var GlobalAiChatScroll = defineAsyncComponent(function () {\n      return import('../../GlobalAiChat/GlobalAiChatScroll.vue');\n    });\n    var GlobalAiChatFile = defineAsyncComponent(function () {\n      return import('../../GlobalAiChat/GlobalAiChatFile.vue');\n    });\n    var GlobalAiChatEditor = defineAsyncComponent(function () {\n      return import('../../GlobalAiChat/GlobalAiChatEditor.vue');\n    });\n    var PreviewPdf = defineAsyncComponent(function () {\n      return import('@/components/global-file-preview/components/preview-pdf.vue');\n    });\n    var loading = ref(false);\n    var loadingText = ref('');\n    var file = ref({});\n    var fileProgress = ref(0);\n    var isShowProgress = ref(false);\n    var progressText = ref('审查意见的补充报告.docx');\n    var progressType = ref('docx');\n    var scrollRef = ref();\n    var pageNo = ref(1);\n    var pageSize = ref(10);\n    var tableData = ref([]);\n    var totals = ref(0);\n    var isShow = ref(false);\n    var isLoading = ref(true);\n    var loadingScroll = ref(false);\n    var chatScrollRef = ref();\n    var editorRef = ref();\n    var chatId = ref('');\n    var fileData = ref([]);\n    var fileList = ref([]);\n    var sendContent = ref('');\n    var disabled = ref(false);\n    var sendMessageIndex = ref(0);\n    var fileIcon = function fileIcon(name) {\n      var type = name.substring(name.lastIndexOf('.') + 1) || '';\n      var IconClass = {\n        docx: 'globalFileWord',\n        doc: 'globalFileWord',\n        wps: 'globalFileWPS',\n        pdf: 'globalFilePDF',\n        txt: 'globalFileTXT'\n      };\n      return IconClass[type] || 'globalFileUnknown';\n    };\n    onMounted(function () {\n      chatId.value = guid();\n      aigptContentExtractionLogList();\n    });\n    var handleScroll = function handleScroll(_ref2) {\n      var scrollTop = _ref2.scrollTop;\n      if (!scrollRef.value) return;\n      var _scrollRef$value$wrap = scrollRef.value.wrapRef,\n        scrollHeight = _scrollRef$value$wrap.scrollHeight,\n        clientHeight = _scrollRef$value$wrap.clientHeight;\n      if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {\n        load();\n      }\n    };\n    var load = function load() {\n      if (pageNo.value * pageSize.value >= totals.value) return;\n      loadingScroll.value = true;\n      pageNo.value += 1;\n      aigptContentExtractionLogList();\n    };\n    var aigptContentExtractionLogList = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$aigptConte, data, total;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.aigptContentExtractionLogList({\n                pageNo: pageNo.value,\n                pageSize: pageSize.value\n              });\n            case 2:\n              _yield$api$aigptConte = _context.sent;\n              data = _yield$api$aigptConte.data;\n              total = _yield$api$aigptConte.total;\n              tableData.value = [].concat(_toConsumableArray(tableData.value), _toConsumableArray(data));\n              totals.value = total;\n              isLoading.value = pageNo.value * pageSize.value < totals.value;\n              isShow.value = pageNo.value * pageSize.value >= totals.value;\n              loadingScroll.value = false;\n            case 10:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function aigptContentExtractionLogList() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var handleHistory = function handleHistory(item) {\n      var type = item.fileName.substring(item.fileName.lastIndexOf('.') + 1) || '';\n      var data = {\n        extName: type,\n        fileSize: item.fileSize,\n        id: item.fileId,\n        newFileName: item.fileId + '.' + type,\n        originalFileName: item.fileName\n      };\n      chatId.value = item.chatId;\n      sendMessageIndex.value = 1;\n      // file.value = data\n      // editorRef.value?.handleSetFile([data])\n      nextTick(function () {\n        var _chatScrollRef$value;\n        (_chatScrollRef$value = chatScrollRef.value) === null || _chatScrollRef$value === void 0 || _chatScrollRef$value.handleOldChat();\n      });\n    };\n    var handleReset = function handleReset() {\n      var _editorRef$value;\n      chatId.value = guid();\n      file.value = {};\n      (_editorRef$value = editorRef.value) === null || _editorRef$value === void 0 || _editorRef$value.handleSetFile([]);\n      pageNo.value = 1;\n      pageSize.value = 10;\n      tableData.value = [];\n      totals.value = 0;\n      isShow.value = false;\n      isLoading.value = true;\n      loadingScroll.value = false;\n      sendMessageIndex.value = 0;\n      aigptContentExtractionLogList();\n      nextTick(function () {\n        var _chatScrollRef$value2;\n        (_chatScrollRef$value2 = chatScrollRef.value) === null || _chatScrollRef$value2 === void 0 || _chatScrollRef$value2.handleNewChat();\n      });\n    };\n    var handleFileUpload = function handleFileUpload(data) {\n      fileList.value = data;\n    };\n    var handleFileCallback = function handleFileCallback(data) {\n      fileData.value = data;\n    };\n    var handleClose = function handleClose(item) {\n      var _editorRef$value2;\n      (_editorRef$value2 = editorRef.value) === null || _editorRef$value2 === void 0 || _editorRef$value2.handleSetFile(fileData.value.filter(function (v) {\n        return v.id !== item.id;\n      }));\n    };\n    var handleSendMessage = function handleSendMessage(value) {\n      var _chatScrollRef$value3;\n      if (!fileData.value.length && !sendMessageIndex.value) return ElMessage({\n        type: 'warning',\n        message: `请先上传相关资料在进行内容提炼!`\n      });\n      var fileId = fileData.value.map(function (v) {\n        return v.id;\n      }).join(',');\n      var params = {\n        question: value,\n        attachmentIds: fileId\n      };\n      (_chatScrollRef$value3 = chatScrollRef.value) === null || _chatScrollRef$value3 === void 0 || _chatScrollRef$value3.handleSendMessage(value, params);\n    };\n    var handlePromptWord = function handlePromptWord(data) {\n      handleSendMessage(data.promptWord);\n    };\n    var handleGuideWord = function handleGuideWord(data) {\n      var _chatScrollRef$value4;\n      (_chatScrollRef$value4 = chatScrollRef.value) === null || _chatScrollRef$value4 === void 0 || _chatScrollRef$value4.handleSendMessage(data.question, data);\n    };\n    var handleRetryMessage = function handleRetryMessage(data) {\n      fileData.value = data.fileData;\n      handleSendMessage(data.content);\n    };\n    var handleStopMessage = function handleStopMessage() {\n      var _chatScrollRef$value5;\n      (_chatScrollRef$value5 = chatScrollRef.value) === null || _chatScrollRef$value5 === void 0 || _chatScrollRef$value5.handleStopMessage();\n    };\n    var handleStreamingCallback = function handleStreamingCallback(data) {\n      disabled.value = data;\n    };\n    var handleSendMessageCallback = function handleSendMessageCallback() {\n      var _editorRef$value3, _editorRef$value4;\n      (_editorRef$value3 = editorRef.value) === null || _editorRef$value3 === void 0 || _editorRef$value3.handleSetFile([]);\n      (_editorRef$value4 = editorRef.value) === null || _editorRef$value4 === void 0 || _editorRef$value4.handleSetContent('');\n    };\n\n    /**\r\n     * 限制上传附件的文件类型\r\n     */\n    var handleFile = function handleFile(file) {\n      var fileType = file.name.substring(file.name.lastIndexOf('.') + 1);\n      var isShow = ['doc', 'docx', 'pdf'].includes(fileType);\n      if (!isShow) ElMessage({\n        type: 'warning',\n        message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!`\n      });\n      isShowProgress.value = true;\n      fileProgress.value = 0;\n      progressText.value = file.name;\n      progressType.value = fileType;\n      return isShow;\n    };\n    var onUploadProgress = function onUploadProgress(progressEvent) {\n      var _progressEvent$event;\n      if (progressEvent !== null && progressEvent !== void 0 && (_progressEvent$event = progressEvent.event) !== null && _progressEvent$event !== void 0 && _progressEvent$event.lengthComputable) {\n        var progress = (progressEvent.loaded / progressEvent.total * 100).toFixed(0);\n        fileProgress.value = parseInt(progress);\n      }\n    };\n    /**\r\n     * 上传附件请求方法\r\n     */\n    var fileUpload = function fileUpload(file) {\n      var param = new FormData();\n      param.append('file', file.file);\n      handleGlobalUpload(param);\n    };\n    var handleGlobalUpload = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(params) {\n        var _editorRef$value5, _yield$api$globalUplo, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return api.globalUpload(params, onUploadProgress, guid());\n            case 3:\n              _yield$api$globalUplo = _context2.sent;\n              data = _yield$api$globalUplo.data;\n              file.value = data;\n              (_editorRef$value5 = editorRef.value) === null || _editorRef$value5 === void 0 || _editorRef$value5.handleSetFile([data]);\n              loading.value = false;\n              isShowProgress.value = false;\n              _context2.next = 15;\n              break;\n            case 11:\n              _context2.prev = 11;\n              _context2.t0 = _context2[\"catch\"](0);\n              loading.value = false;\n              isShowProgress.value = false;\n            case 15:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 11]]);\n      }));\n      return function handleGlobalUpload(_x) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      GlobalAiChatScroll,\n      GlobalAiChatFile,\n      GlobalAiChatEditor,\n      PreviewPdf,\n      loading,\n      loadingText,\n      file,\n      fileProgress,\n      isShowProgress,\n      progressText,\n      progressType,\n      scrollRef,\n      pageNo,\n      pageSize,\n      tableData,\n      totals,\n      isShow,\n      isLoading,\n      loadingScroll,\n      chatScrollRef,\n      editorRef,\n      chatId,\n      fileData,\n      fileList,\n      sendContent,\n      disabled,\n      sendMessageIndex,\n      fileIcon,\n      handleScroll,\n      load,\n      aigptContentExtractionLogList,\n      handleHistory,\n      handleReset,\n      handleFileUpload,\n      handleFileCallback,\n      handleClose,\n      handleSendMessage,\n      handlePromptWord,\n      handleGuideWord,\n      handleRetryMessage,\n      handleStopMessage,\n      handleStreamingCallback,\n      handleSendMessageCallback,\n      handleFile,\n      onUploadProgress,\n      fileUpload,\n      handleGlobalUpload,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      nextTick,\n      defineAsyncComponent,\n      get format() {\n        return format;\n      },\n      get size2Str() {\n        return size2Str;\n      },\n      get guid() {\n        return guid;\n      },\n      get svg() {\n        return svg;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "nextTick", "defineAsyncComponent", "format", "size2Str", "guid", "svg", "ElMessage", "__default__", "GlobalAiChatScroll", "GlobalAiChatFile", "GlobalAiChatEditor", "PreviewPdf", "loading", "loadingText", "file", "fileProgress", "isShowProgress", "progressText", "progressType", "scrollRef", "pageNo", "pageSize", "tableData", "totals", "isShow", "isLoading", "loadingScroll", "chatScrollRef", "editor<PERSON><PERSON>", "chatId", "fileData", "fileList", "send<PERSON><PERSON><PERSON>", "disabled", "sendMessageIndex", "fileIcon", "substring", "lastIndexOf", "IconClass", "docx", "doc", "wps", "pdf", "txt", "aigptContentExtractionLogList", "handleScroll", "_ref2", "scrollTop", "_scrollRef$value$wrap", "wrapRef", "scrollHeight", "clientHeight", "load", "_ref3", "_callee", "_yield$api$aigptConte", "data", "total", "_callee$", "_context", "concat", "handleHistory", "item", "fileName", "extName", "fileSize", "id", "fileId", "newFileName", "originalFileName", "_chatScrollRef$value", "handleOldChat", "handleReset", "_editorRef$value", "handleSetFile", "_chatScrollRef$value2", "handleNewChat", "handleFileUpload", "handleFileCallback", "handleClose", "_editorRef$value2", "filter", "handleSendMessage", "_chatScrollRef$value3", "message", "map", "join", "params", "question", "attachmentIds", "handlePromptWord", "promptWord", "handleGuideWord", "_chatScrollRef$value4", "handleRetryMessage", "content", "handleStopMessage", "_chatScrollRef$value5", "handleStreamingCallback", "handleSendMessageCallback", "_editorRef$value3", "_editorRef$value4", "handleSetContent", "handleFile", "fileType", "includes", "onUploadProgress", "progressEvent", "_progressEvent$event", "event", "lengthComputable", "progress", "loaded", "toFixed", "parseInt", "fileUpload", "param", "FormData", "append", "handleGlobalUpload", "_ref4", "_callee2", "_editorRef$value5", "_yield$api$globalUplo", "_callee2$", "_context2", "globalUpload", "t0", "_x"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiToolBoxFunction/ContentExtraction/ContentExtraction.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"ContentExtraction\"\r\n    v-loading=\"loading\"\r\n    :element-loading-spinner=\"svg\"\r\n    :lement-loading-text=\"loadingText\"\r\n    element-loading-svg-view-box=\"-10, -10, 50, 50\">\r\n    <div class=\"ContentExtractionHead\">\r\n      <div class=\"ContentExtractionButton\">\r\n        <div class=\"ContentExtractionButtonItem\" v-show=\"file.id\">\r\n          <el-button type=\"primary\" @click=\"handleReset\">重新上传</el-button>\r\n        </div>\r\n        <div class=\"ContentExtractionButtonItem\"></div>\r\n      </div>\r\n      <div class=\"ContentExtractionButton\">\r\n        <div class=\"ContentExtractionButtonItem\"></div>\r\n        <div class=\"ContentExtractionButtonItem\">\r\n          <el-button type=\"primary\">导出</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"ContentExtractionBody\">\r\n      <div class=\"ContentExtractionBodyLeft\">\r\n        <div class=\"ContentExtractionUploadBody\" v-show=\"!file.id\">\r\n          <div class=\"ContentExtractionUpload\">\r\n            <el-upload\r\n              drag\r\n              action=\"/\"\r\n              :before-upload=\"handleFile\"\r\n              :http-request=\"fileUpload\"\r\n              :show-file-list=\"false\"\r\n              multiple>\r\n              <el-icon class=\"zy-el-icon--upload\">\r\n                <upload-filled />\r\n              </el-icon>\r\n              <div class=\"zy-el-upload__text\">\r\n                将附件拖拽至此区域，或\r\n                <em>点击上传</em>\r\n              </div>\r\n              <div class=\"zy-el-upload__tip\">仅支持{{ ['doc', 'docx', 'pdf'].join('、') }}格式</div>\r\n            </el-upload>\r\n            <div class=\"ContentExtractionUploadProgressBody\" @click.stop v-show=\"isShowProgress\">\r\n              <div class=\"ContentExtractionUploadProgressInfo\">\r\n                <div class=\"ContentExtractionUploadProgress\">\r\n                  <div class=\"globalFileIcon\" :class=\"fileIcon(progressType)\"></div>\r\n                  <div class=\"ContentExtractionUploadProgressBox\">\r\n                    <div class=\"ContentExtractionUploadProgressName ellipsis\">{{ progressText }}</div>\r\n                    <div class=\"ContentExtractionUploadProgressText\">正在解析</div>\r\n                  </div>\r\n                  <!-- <div class=\"ContentExtractionUploadProgressClose\">\r\n                    <el-icon><Close /></el-icon>\r\n                  </div> -->\r\n                </div>\r\n                <el-progress :percentage=\"fileProgress\" :show-text=\"false\" :stroke-width=\"12\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"ContentExtractionUploadHistoryTitle\">最近阅读</div>\r\n          <el-scrollbar class=\"ContentExtractionUploadHistoryScroll\" ref=\"scrollRef\" @scroll=\"handleScroll\">\r\n            <div class=\"ContentExtractionUploadHistoryList\">\r\n              <div\r\n                class=\"ContentExtractionUploadHistoryItem\"\r\n                v-for=\"item in tableData\"\r\n                :key=\"item.id\"\r\n                @click=\"handleHistory(item)\">\r\n                <div class=\"globalFileIcon\" :class=\"fileIcon(item.fileName)\"></div>\r\n                <div class=\"ContentExtractionUploadHistoryItemName ellipsis\">{{ item.fileName }}</div>\r\n                <div class=\"ContentExtractionUploadHistoryItemInfo\">\r\n                  <div class=\"ContentExtractionUploadHistoryItemFileSize\">\r\n                    {{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}\r\n                  </div>\r\n                  <div class=\"ContentExtractionUploadHistoryItemTime\">{{ format(item.createDate) }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"ContentExtractionUploadHistoryLoadingText\" v-if=\"isLoading\">加载中...</div>\r\n            <div class=\"ContentExtractionUploadHistoryLoadingText\" v-if=\"isShow\">没有更多了</div>\r\n          </el-scrollbar>\r\n        </div>\r\n        <div class=\"ContentExtractionWord\" v-if=\"file.id\">\r\n          <preview-pdf :id=\"file.id\" :type=\"file.extName\" :name=\"file.newFileName\"></preview-pdf>\r\n        </div>\r\n      </div>\r\n      <div class=\"ContentExtractionBodyRight\">\r\n        <div class=\"ContentExtractionChatBody\">\r\n          <GlobalAiChatScroll\r\n            ref=\"chatScrollRef\"\r\n            AiChatCode=\"content_extraction\"\r\n            :chatId=\"chatId\"\r\n            :fileData=\"fileData\"\r\n            @handlePromptWord=\"handlePromptWord\"\r\n            @handleGuideWord=\"handleGuideWord\"\r\n            @handleRetryMessage=\"handleRetryMessage\"\r\n            @handleStreamingCallback=\"handleStreamingCallback\"\r\n            @handleSendMessageCallback=\"handleSendMessageCallback\"></GlobalAiChatScroll>\r\n          <div class=\"ContentExtractionChatBodyEditor\">\r\n            <div class=\"ContentExtractionChatBodyEditorBody\">\r\n              <GlobalAiChatFile\r\n                :fileList=\"fileList\"\r\n                :fileData=\"fileData\"\r\n                @close=\"handleClose\"\r\n                v-show=\"fileList.length || fileData.length\" />\r\n              <GlobalAiChatEditor\r\n                ref=\"editorRef\"\r\n                v-model=\"sendContent\"\r\n                :disabled=\"disabled\"\r\n                @send=\"handleSendMessage\"\r\n                @stop=\"handleStopMessage\"\r\n                @uploadCallback=\"handleFileUpload\"\r\n                @fileCallback=\"handleFileCallback\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ContentExtraction' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick, defineAsyncComponent } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { size2Str } from 'common/js/utils.js'\r\nimport { guid, svg } from '../../AiToolBox/AiToolBox.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst GlobalAiChatScroll = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatScroll.vue'))\r\nconst GlobalAiChatFile = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatFile.vue'))\r\nconst GlobalAiChatEditor = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatEditor.vue'))\r\nconst PreviewPdf = defineAsyncComponent(() => import('@/components/global-file-preview/components/preview-pdf.vue'))\r\n\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n\r\nconst file = ref({})\r\nconst fileProgress = ref(0)\r\nconst isShowProgress = ref(false)\r\nconst progressText = ref('审查意见的补充报告.docx')\r\nconst progressType = ref('docx')\r\n\r\nconst scrollRef = ref()\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst tableData = ref([])\r\nconst totals = ref(0)\r\nconst isShow = ref(false)\r\nconst isLoading = ref(true)\r\nconst loadingScroll = ref(false)\r\n\r\nconst chatScrollRef = ref()\r\nconst editorRef = ref()\r\nconst chatId = ref('')\r\nconst fileData = ref([])\r\nconst fileList = ref([])\r\nconst sendContent = ref('')\r\nconst disabled = ref(false)\r\nconst sendMessageIndex = ref(0)\r\n\r\nconst fileIcon = (name) => {\r\n  const type = name.substring(name.lastIndexOf('.') + 1) || ''\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    pdf: 'globalFilePDF',\r\n    txt: 'globalFileTXT'\r\n  }\r\n  return IconClass[type] || 'globalFileUnknown'\r\n}\r\n\r\nonMounted(() => {\r\n  chatId.value = guid()\r\n  aigptContentExtractionLogList()\r\n})\r\n\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (!scrollRef.value) return\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {\r\n    load()\r\n  }\r\n}\r\nconst load = () => {\r\n  if (pageNo.value * pageSize.value >= totals.value) return\r\n  loadingScroll.value = true\r\n  pageNo.value += 1\r\n  aigptContentExtractionLogList()\r\n}\r\nconst aigptContentExtractionLogList = async () => {\r\n  const { data, total } = await api.aigptContentExtractionLogList({ pageNo: pageNo.value, pageSize: pageSize.value })\r\n  tableData.value = [...tableData.value, ...data]\r\n  totals.value = total\r\n  isLoading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  loadingScroll.value = false\r\n}\r\n\r\nconst handleHistory = (item) => {\r\n  const type = item.fileName.substring(item.fileName.lastIndexOf('.') + 1) || ''\r\n  const data = {\r\n    extName: type,\r\n    fileSize: item.fileSize,\r\n    id: item.fileId,\r\n    newFileName: item.fileId + '.' + type,\r\n    originalFileName: item.fileName\r\n  }\r\n  chatId.value = item.chatId\r\n  sendMessageIndex.value = 1\r\n  // file.value = data\r\n  // editorRef.value?.handleSetFile([data])\r\n  nextTick(() => {\r\n    chatScrollRef.value?.handleOldChat()\r\n  })\r\n}\r\nconst handleReset = () => {\r\n  chatId.value = guid()\r\n  file.value = {}\r\n  editorRef.value?.handleSetFile([])\r\n  pageNo.value = 1\r\n  pageSize.value = 10\r\n  tableData.value = []\r\n  totals.value = 0\r\n  isShow.value = false\r\n  isLoading.value = true\r\n  loadingScroll.value = false\r\n  sendMessageIndex.value = 0\r\n  aigptContentExtractionLogList()\r\n  nextTick(() => {\r\n    chatScrollRef.value?.handleNewChat()\r\n  })\r\n}\r\n\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!fileData.value.length && !sendMessageIndex.value)\r\n    return ElMessage({ type: 'warning', message: `请先上传相关资料在进行内容提炼!` })\r\n  const fileId = fileData.value.map((v) => v.id).join(',')\r\n  const params = { question: value, attachmentIds: fileId }\r\n  chatScrollRef.value?.handleSendMessage(value, params)\r\n}\r\nconst handlePromptWord = (data) => {\r\n  handleSendMessage(data.promptWord)\r\n}\r\nconst handleGuideWord = (data) => {\r\n  chatScrollRef.value?.handleSendMessage(data.question, data)\r\n}\r\nconst handleRetryMessage = (data) => {\r\n  fileData.value = data.fileData\r\n  handleSendMessage(data.content)\r\n}\r\nconst handleStopMessage = () => {\r\n  chatScrollRef.value?.handleStopMessage()\r\n}\r\nconst handleStreamingCallback = (data) => {\r\n  disabled.value = data\r\n}\r\nconst handleSendMessageCallback = () => {\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n}\r\n\r\n/**\r\n * 限制上传附件的文件类型\r\n */\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  const isShow = ['doc', 'docx', 'pdf'].includes(fileType)\r\n  if (!isShow) ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!` })\r\n  isShowProgress.value = true\r\n  fileProgress.value = 0\r\n  progressText.value = file.name\r\n  progressType.value = fileType\r\n  return isShow\r\n}\r\n\r\nconst onUploadProgress = (progressEvent) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    fileProgress.value = parseInt(progress)\r\n  }\r\n}\r\n/**\r\n * 上传附件请求方法\r\n */\r\nconst fileUpload = (file) => {\r\n  const param = new FormData()\r\n  param.append('file', file.file)\r\n  handleGlobalUpload(param)\r\n}\r\n\r\nconst handleGlobalUpload = async (params) => {\r\n  try {\r\n    const { data } = await api.globalUpload(params, onUploadProgress, guid())\r\n    file.value = data\r\n    editorRef.value?.handleSetFile([data])\r\n    loading.value = false\r\n    isShowProgress.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n    isShowProgress.value = false\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ContentExtraction {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .ContentExtractionHead {\r\n    width: 100%;\r\n    padding: var(--zy-distance-four) 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .ContentExtractionButton {\r\n      width: 796px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      & + .ContentExtractionButton {\r\n        width: calc(100% - 828px);\r\n      }\r\n      .ContentExtractionButtonItem {\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n  .ContentExtractionBody {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2)));\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: var(--zy-distance-four);\r\n    .ContentExtractionBodyLeft {\r\n      width: 812px;\r\n      height: 100%;\r\n      .ContentExtractionUploadBody {\r\n        width: 800px;\r\n        height: 100%;\r\n        background: #fff;\r\n        .globalFileIcon {\r\n          width: 32px;\r\n          height: 32px;\r\n          vertical-align: middle;\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 0;\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        .globalFileUnknown {\r\n          background: url('../../img/file_type/unknown.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePDF {\r\n          background: url('../../img/file_type/PDF.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWord {\r\n          background: url('../../img/file_type/Word.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileTXT {\r\n          background: url('../../img/file_type/TXT.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWPS {\r\n          background: url('../../img/file_type/WPS.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .ContentExtractionUpload {\r\n          width: 100%;\r\n          padding: var(--zy-distance-two);\r\n          position: relative;\r\n\r\n          .zy-el-upload {\r\n            --zy-el-upload-dragger-padding-horizontal: 20px;\r\n            --zy-el-upload-dragger-padding-vertical: 10px;\r\n            .zy-el-upload-dragger {\r\n              height: 220px;\r\n              display: flex;\r\n              align-items: center;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n            }\r\n            .zy-el-icon {\r\n              font-size: 99px;\r\n            }\r\n            .zy-el-upload__text {\r\n              line-height: var(--zy-line-height);\r\n            }\r\n\r\n            .zy-el-upload__tip {\r\n              padding: 0 var(--zy-distance-one);\r\n              line-height: var(--zy-line-height);\r\n            }\r\n          }\r\n\r\n          .ContentExtractionUploadProgressBody {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: var(--zy-distance-two) 0;\r\n            position: absolute;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            background: rgba(255, 255, 255, 0.8);\r\n\r\n            .ContentExtractionUploadProgressInfo {\r\n              width: 460px;\r\n              .ContentExtractionUploadProgress {\r\n                width: 100%;\r\n                padding: var(--zy-distance-five) 40px;\r\n                position: relative;\r\n                .ContentExtractionUploadProgressBox {\r\n                  width: 100%;\r\n                  .ContentExtractionUploadProgressName {\r\n                    font-size: var(--zy-name-font-size);\r\n                    line-height: var(--zy-line-height);\r\n                  }\r\n                  .ContentExtractionUploadProgressText {\r\n                    color: var(--zy-el-color-primary);\r\n                    font-size: var(--zy-text-font-size);\r\n                    line-height: var(--zy-line-height);\r\n                  }\r\n                }\r\n                .ContentExtractionUploadProgressClose {\r\n                  width: 40px;\r\n                  height: 40px;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  position: absolute;\r\n                  right: 0;\r\n                  bottom: calc(var(--zy-distance-five) / 2);\r\n                  cursor: pointer;\r\n                  .zy-el-icon {\r\n                    font-size: 26px;\r\n                    &:hover {\r\n                      color: var(--zy-el-color-danger);\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .ContentExtractionUploadHistoryTitle {\r\n          width: 100%;\r\n          height: var(--zy-height);\r\n          line-height: var(--zy-height);\r\n          font-size: var(--zy-name-font-size);\r\n          color: var(--zy-el-text-color-secondary);\r\n          padding: 0 var(--zy-distance-two);\r\n        }\r\n        .ContentExtractionUploadHistoryScroll {\r\n          width: 100%;\r\n          height: calc(100% - (260px + var(--zy-height)));\r\n        }\r\n        .ContentExtractionUploadHistoryList {\r\n          width: 100%;\r\n          padding: 0 var(--zy-distance-two);\r\n          .ContentExtractionUploadHistoryItem {\r\n            width: 100%;\r\n            position: relative;\r\n            padding-left: 40px;\r\n            padding-top: var(--zy-distance-five);\r\n            padding-bottom: var(--zy-distance-five);\r\n            border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n            cursor: pointer;\r\n\r\n            .ContentExtractionUploadHistoryItemName {\r\n              width: 100%;\r\n              font-size: var(--zy-name-font-size);\r\n              line-height: var(--zy-line-height);\r\n              padding-bottom: var(--zy-font-text-distance-five);\r\n            }\r\n            .ContentExtractionUploadHistoryItemInfo {\r\n              width: 100%;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: space-between;\r\n              .ContentExtractionUploadHistoryItemFileSize {\r\n                font-size: var(--zy-text-font-size);\r\n                line-height: var(--zy-line-height);\r\n                color: var(--zy-el-text-color-secondary);\r\n              }\r\n              .ContentExtractionUploadHistoryItemTime {\r\n                font-size: var(--zy-text-font-size);\r\n                line-height: var(--zy-line-height);\r\n                color: var(--zy-el-text-color-secondary);\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .ContentExtractionUploadHistoryLoadingText {\r\n          text-align: center;\r\n          color: var(--zy-el-text-color-regular);\r\n          padding: var(--zy-distance-three) 0;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n      }\r\n      .ContentExtractionWord {\r\n        width: 100%;\r\n        height: 100%;\r\n        .vue-office-pdf-wrapper {\r\n          padding: 0 !important;\r\n          canvas {\r\n            left: 0 !important;\r\n            transform: none !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .ContentExtractionBodyRight {\r\n      width: calc(100% - 820px);\r\n      height: 100%;\r\n      .ContentExtractionChatBody {\r\n        width: 100%;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        background: #fff;\r\n        .GlobalAiChatBody {\r\n          padding-top: 12px;\r\n        }\r\n        .ContentExtractionChatBodyEditor {\r\n          width: 100%;\r\n          padding: 12px;\r\n          .GlobalAiChatFileItemClose {\r\n            display: none !important;\r\n          }\r\n          .ContentExtractionChatBodyEditorBody {\r\n            width: 100%;\r\n            background: #fff;\r\n            border-radius: 8px;\r\n            box-shadow: var(--zy-el-box-shadow);\r\n            border: 1px solid var(--zy-el-border-color-lighter);\r\n            .GlobalAiChatEditorUpload {\r\n              & > div {\r\n                display: none;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA2HA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAArG,CAAA,WAAAsG,kBAAA,CAAAtG,CAAA,KAAAuG,gBAAA,CAAAvG,CAAA,KAAAwG,2BAAA,CAAAxG,CAAA,KAAAyG,kBAAA;AAAA,SAAAA,mBAAA,cAAA5C,SAAA;AAAA,SAAA2C,4BAAAxG,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAA0G,iBAAA,CAAA1G,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAA4G,QAAA,CAAA/E,IAAA,CAAA5B,CAAA,EAAA4F,KAAA,6BAAA7F,CAAA,IAAAC,CAAA,CAAA+E,WAAA,KAAAhF,CAAA,GAAAC,CAAA,CAAA+E,WAAA,CAAAC,IAAA,aAAAjF,CAAA,cAAAA,CAAA,GAAA6G,KAAA,CAAAC,IAAA,CAAA7G,CAAA,oBAAAD,CAAA,+CAAA+G,IAAA,CAAA/G,CAAA,IAAA2G,iBAAA,CAAA1G,CAAA,EAAAU,CAAA;AAAA,SAAA6F,iBAAAvG,CAAA,8BAAAS,MAAA,YAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,aAAAX,CAAA,uBAAA4G,KAAA,CAAAC,IAAA,CAAA7G,CAAA;AAAA,SAAAsG,mBAAAtG,CAAA,QAAA4G,KAAA,CAAAG,OAAA,CAAA/G,CAAA,UAAA0G,iBAAA,CAAA1G,CAAA;AAAA,SAAA0G,kBAAA1G,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA4E,MAAA,MAAAlE,CAAA,GAAAV,CAAA,CAAA4E,MAAA,YAAA9E,CAAA,MAAAK,CAAA,GAAAyG,KAAA,CAAAlG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AAAA,SAAA6G,mBAAA7G,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAA4G,kBAAA9G,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAoH,SAAA,aAAA5B,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAgH,KAAA,CAAApH,CAAA,EAAAD,CAAA,YAAAsH,MAAAjH,CAAA,IAAA6G,kBAAA,CAAAtG,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAA+G,KAAA,EAAAC,MAAA,UAAAlH,CAAA,cAAAkH,OAAAlH,CAAA,IAAA6G,kBAAA,CAAAtG,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAA+G,KAAA,EAAAC,MAAA,WAAAlH,CAAA,KAAAiH,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,KAAK;AACpE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,IAAI,EAAEC,GAAG,QAAQ,8BAA8B;AACxD,SAASC,SAAS,QAAQ,cAAc;AATxC,IAAAC,WAAA,GAAe;EAAEhD,IAAI,EAAE;AAAoB,CAAC;;;;;IAU5C,IAAMiD,kBAAkB,GAAGP,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;IAAA,EAAC;IAC1G,IAAMQ,gBAAgB,GAAGR,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,yCAAyC,CAAC;IAAA,EAAC;IACtG,IAAMS,kBAAkB,GAAGT,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;IAAA,EAAC;IAC1G,IAAMU,UAAU,GAAGV,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,6DAA6D,CAAC;IAAA,EAAC;IAEpH,IAAMW,OAAO,GAAGd,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMe,WAAW,GAAGf,GAAG,CAAC,EAAE,CAAC;IAE3B,IAAMgB,IAAI,GAAGhB,GAAG,CAAC,CAAC,CAAC,CAAC;IACpB,IAAMiB,YAAY,GAAGjB,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAMkB,cAAc,GAAGlB,GAAG,CAAC,KAAK,CAAC;IACjC,IAAMmB,YAAY,GAAGnB,GAAG,CAAC,gBAAgB,CAAC;IAC1C,IAAMoB,YAAY,GAAGpB,GAAG,CAAC,MAAM,CAAC;IAEhC,IAAMqB,SAAS,GAAGrB,GAAG,CAAC,CAAC;IACvB,IAAMsB,MAAM,GAAGtB,GAAG,CAAC,CAAC,CAAC;IACrB,IAAMuB,QAAQ,GAAGvB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMwB,SAAS,GAAGxB,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMyB,MAAM,GAAGzB,GAAG,CAAC,CAAC,CAAC;IACrB,IAAM0B,MAAM,GAAG1B,GAAG,CAAC,KAAK,CAAC;IACzB,IAAM2B,SAAS,GAAG3B,GAAG,CAAC,IAAI,CAAC;IAC3B,IAAM4B,aAAa,GAAG5B,GAAG,CAAC,KAAK,CAAC;IAEhC,IAAM6B,aAAa,GAAG7B,GAAG,CAAC,CAAC;IAC3B,IAAM8B,SAAS,GAAG9B,GAAG,CAAC,CAAC;IACvB,IAAM+B,MAAM,GAAG/B,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMgC,QAAQ,GAAGhC,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMiC,QAAQ,GAAGjC,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMkC,WAAW,GAAGlC,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMmC,QAAQ,GAAGnC,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMoC,gBAAgB,GAAGpC,GAAG,CAAC,CAAC,CAAC;IAE/B,IAAMqC,QAAQ,GAAG,SAAXA,QAAQA,CAAI5E,IAAI,EAAK;MACzB,IAAMtD,IAAI,GAAGsD,IAAI,CAAC6E,SAAS,CAAC7E,IAAI,CAAC8E,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;MAC5D,IAAMC,SAAS,GAAG;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,GAAG,EAAE,gBAAgB;QACrBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE;MACP,CAAC;MACD,OAAOL,SAAS,CAACrI,IAAI,CAAC,IAAI,mBAAmB;IAC/C,CAAC;IAED8F,SAAS,CAAC,YAAM;MACd8B,MAAM,CAAC/I,KAAK,GAAGsH,IAAI,CAAC,CAAC;MACrBwC,6BAA6B,CAAC,CAAC;IACjC,CAAC,CAAC;IAEF,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAAC,KAAA,EAAsB;MAAA,IAAhBC,SAAS,GAAAD,KAAA,CAATC,SAAS;MAC/B,IAAI,CAAC5B,SAAS,CAACrI,KAAK,EAAE;MACtB,IAAAkK,qBAAA,GAAuC7B,SAAS,CAACrI,KAAK,CAACmK,OAAO;QAAtDC,YAAY,GAAAF,qBAAA,CAAZE,YAAY;QAAEC,YAAY,GAAAH,qBAAA,CAAZG,YAAY;MAClC,IAAID,YAAY,GAAGH,SAAS,IAAII,YAAY,GAAG,EAAE,IAAI,CAACzB,aAAa,CAAC5I,KAAK,EAAE;QACzEsK,IAAI,CAAC,CAAC;MACR;IACF,CAAC;IACD,IAAMA,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,IAAIhC,MAAM,CAACtI,KAAK,GAAGuI,QAAQ,CAACvI,KAAK,IAAIyI,MAAM,CAACzI,KAAK,EAAE;MACnD4I,aAAa,CAAC5I,KAAK,GAAG,IAAI;MAC1BsI,MAAM,CAACtI,KAAK,IAAI,CAAC;MACjB8J,6BAA6B,CAAC,CAAC;IACjC,CAAC;IACD,IAAMA,6BAA6B;MAAA,IAAAS,KAAA,GAAA7D,iBAAA,cAAApH,mBAAA,GAAAoF,IAAA,CAAG,SAAA8F,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA,EAAAC,KAAA;QAAA,OAAArL,mBAAA,GAAAuB,IAAA,UAAA+J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA1F,IAAA,GAAA0F,QAAA,CAAArH,IAAA;YAAA;cAAAqH,QAAA,CAAArH,IAAA;cAAA,OACNuD,GAAG,CAAC+C,6BAA6B,CAAC;gBAAExB,MAAM,EAAEA,MAAM,CAACtI,KAAK;gBAAEuI,QAAQ,EAAEA,QAAQ,CAACvI;cAAM,CAAC,CAAC;YAAA;cAAAyK,qBAAA,GAAAI,QAAA,CAAA5H,IAAA;cAA3GyH,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAAEC,KAAK,GAAAF,qBAAA,CAALE,KAAK;cACnBnC,SAAS,CAACxI,KAAK,MAAA8K,MAAA,CAAAhF,kBAAA,CAAO0C,SAAS,CAACxI,KAAK,GAAA8F,kBAAA,CAAK4E,IAAI,EAAC;cAC/CjC,MAAM,CAACzI,KAAK,GAAG2K,KAAK;cACpBhC,SAAS,CAAC3I,KAAK,GAAGsI,MAAM,CAACtI,KAAK,GAAGuI,QAAQ,CAACvI,KAAK,GAAGyI,MAAM,CAACzI,KAAK;cAC9D0I,MAAM,CAAC1I,KAAK,GAAGsI,MAAM,CAACtI,KAAK,GAAGuI,QAAQ,CAACvI,KAAK,IAAIyI,MAAM,CAACzI,KAAK;cAC5D4I,aAAa,CAAC5I,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAA6K,QAAA,CAAAvF,IAAA;UAAA;QAAA,GAAAkF,OAAA;MAAA,CAC5B;MAAA,gBAPKV,6BAA6BA,CAAA;QAAA,OAAAS,KAAA,CAAA3D,KAAA,OAAAD,SAAA;MAAA;IAAA,GAOlC;IAED,IAAMoE,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9B,IAAM7J,IAAI,GAAG6J,IAAI,CAACC,QAAQ,CAAC3B,SAAS,CAAC0B,IAAI,CAACC,QAAQ,CAAC1B,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;MAC9E,IAAMmB,IAAI,GAAG;QACXQ,OAAO,EAAE/J,IAAI;QACbgK,QAAQ,EAAEH,IAAI,CAACG,QAAQ;QACvBC,EAAE,EAAEJ,IAAI,CAACK,MAAM;QACfC,WAAW,EAAEN,IAAI,CAACK,MAAM,GAAG,GAAG,GAAGlK,IAAI;QACrCoK,gBAAgB,EAAEP,IAAI,CAACC;MACzB,CAAC;MACDlC,MAAM,CAAC/I,KAAK,GAAGgL,IAAI,CAACjC,MAAM;MAC1BK,gBAAgB,CAACpJ,KAAK,GAAG,CAAC;MAC1B;MACA;MACAkH,QAAQ,CAAC,YAAM;QAAA,IAAAsE,oBAAA;QACb,CAAAA,oBAAA,GAAA3C,aAAa,CAAC7I,KAAK,cAAAwL,oBAAA,eAAnBA,oBAAA,CAAqBC,aAAa,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MAAA,IAAAC,gBAAA;MACxB5C,MAAM,CAAC/I,KAAK,GAAGsH,IAAI,CAAC,CAAC;MACrBU,IAAI,CAAChI,KAAK,GAAG,CAAC,CAAC;MACf,CAAA2L,gBAAA,GAAA7C,SAAS,CAAC9I,KAAK,cAAA2L,gBAAA,eAAfA,gBAAA,CAAiBC,aAAa,CAAC,EAAE,CAAC;MAClCtD,MAAM,CAACtI,KAAK,GAAG,CAAC;MAChBuI,QAAQ,CAACvI,KAAK,GAAG,EAAE;MACnBwI,SAAS,CAACxI,KAAK,GAAG,EAAE;MACpByI,MAAM,CAACzI,KAAK,GAAG,CAAC;MAChB0I,MAAM,CAAC1I,KAAK,GAAG,KAAK;MACpB2I,SAAS,CAAC3I,KAAK,GAAG,IAAI;MACtB4I,aAAa,CAAC5I,KAAK,GAAG,KAAK;MAC3BoJ,gBAAgB,CAACpJ,KAAK,GAAG,CAAC;MAC1B8J,6BAA6B,CAAC,CAAC;MAC/B5C,QAAQ,CAAC,YAAM;QAAA,IAAA2E,qBAAA;QACb,CAAAA,qBAAA,GAAAhD,aAAa,CAAC7I,KAAK,cAAA6L,qBAAA,eAAnBA,qBAAA,CAAqBC,aAAa,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC;IAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIrB,IAAI,EAAK;MACjCzB,QAAQ,CAACjJ,KAAK,GAAG0K,IAAI;IACvB,CAAC;IACD,IAAMsB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAItB,IAAI,EAAK;MACnC1B,QAAQ,CAAChJ,KAAK,GAAG0K,IAAI;IACvB,CAAC;IACD,IAAMuB,WAAW,GAAG,SAAdA,WAAWA,CAAIjB,IAAI,EAAK;MAAA,IAAAkB,iBAAA;MAC5B,CAAAA,iBAAA,GAAApD,SAAS,CAAC9I,KAAK,cAAAkM,iBAAA,eAAfA,iBAAA,CAAiBN,aAAa,CAAC5C,QAAQ,CAAChJ,KAAK,CAACmM,MAAM,CAAC,UAACnK,CAAC;QAAA,OAAKA,CAAC,CAACoJ,EAAE,KAAKJ,IAAI,CAACI,EAAE;MAAA,EAAC,CAAC;IAChF,CAAC;IACD,IAAMgB,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIpM,KAAK,EAAK;MAAA,IAAAqM,qBAAA;MACnC,IAAI,CAACrD,QAAQ,CAAChJ,KAAK,CAACqE,MAAM,IAAI,CAAC+E,gBAAgB,CAACpJ,KAAK,EACnD,OAAOwH,SAAS,CAAC;QAAErG,IAAI,EAAE,SAAS;QAAEmL,OAAO,EAAE;MAAmB,CAAC,CAAC;MACpE,IAAMjB,MAAM,GAAGrC,QAAQ,CAAChJ,KAAK,CAACuM,GAAG,CAAC,UAACvK,CAAC;QAAA,OAAKA,CAAC,CAACoJ,EAAE;MAAA,EAAC,CAACoB,IAAI,CAAC,GAAG,CAAC;MACxD,IAAMC,MAAM,GAAG;QAAEC,QAAQ,EAAE1M,KAAK;QAAE2M,aAAa,EAAEtB;MAAO,CAAC;MACzD,CAAAgB,qBAAA,GAAAxD,aAAa,CAAC7I,KAAK,cAAAqM,qBAAA,eAAnBA,qBAAA,CAAqBD,iBAAiB,CAACpM,KAAK,EAAEyM,MAAM,CAAC;IACvD,CAAC;IACD,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIlC,IAAI,EAAK;MACjC0B,iBAAiB,CAAC1B,IAAI,CAACmC,UAAU,CAAC;IACpC,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIpC,IAAI,EAAK;MAAA,IAAAqC,qBAAA;MAChC,CAAAA,qBAAA,GAAAlE,aAAa,CAAC7I,KAAK,cAAA+M,qBAAA,eAAnBA,qBAAA,CAAqBX,iBAAiB,CAAC1B,IAAI,CAACgC,QAAQ,EAAEhC,IAAI,CAAC;IAC7D,CAAC;IACD,IAAMsC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAItC,IAAI,EAAK;MACnC1B,QAAQ,CAAChJ,KAAK,GAAG0K,IAAI,CAAC1B,QAAQ;MAC9BoD,iBAAiB,CAAC1B,IAAI,CAACuC,OAAO,CAAC;IACjC,CAAC;IACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAAA,IAAAC,qBAAA;MAC9B,CAAAA,qBAAA,GAAAtE,aAAa,CAAC7I,KAAK,cAAAmN,qBAAA,eAAnBA,qBAAA,CAAqBD,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IACD,IAAME,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAI1C,IAAI,EAAK;MACxCvB,QAAQ,CAACnJ,KAAK,GAAG0K,IAAI;IACvB,CAAC;IACD,IAAM2C,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAA,EAAS;MAAA,IAAAC,iBAAA,EAAAC,iBAAA;MACtC,CAAAD,iBAAA,GAAAxE,SAAS,CAAC9I,KAAK,cAAAsN,iBAAA,eAAfA,iBAAA,CAAiB1B,aAAa,CAAC,EAAE,CAAC;MAClC,CAAA2B,iBAAA,GAAAzE,SAAS,CAAC9I,KAAK,cAAAuN,iBAAA,eAAfA,iBAAA,CAAiBC,gBAAgB,CAAC,EAAE,CAAC;IACvC,CAAC;;IAED;AACA;AACA;IACA,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIzF,IAAI,EAAK;MAC3B,IAAM0F,QAAQ,GAAG1F,IAAI,CAACvD,IAAI,CAAC6E,SAAS,CAACtB,IAAI,CAACvD,IAAI,CAAC8E,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACpE,IAAMb,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACiF,QAAQ,CAACD,QAAQ,CAAC;MACxD,IAAI,CAAChF,MAAM,EAAElB,SAAS,CAAC;QAAErG,IAAI,EAAE,SAAS;QAAEmL,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;MAAM,CAAC,CAAC;MACjGtE,cAAc,CAAClI,KAAK,GAAG,IAAI;MAC3BiI,YAAY,CAACjI,KAAK,GAAG,CAAC;MACtBmI,YAAY,CAACnI,KAAK,GAAGgI,IAAI,CAACvD,IAAI;MAC9B2D,YAAY,CAACpI,KAAK,GAAG0N,QAAQ;MAC7B,OAAOhF,MAAM;IACf,CAAC;IAED,IAAMkF,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,aAAa,EAAK;MAAA,IAAAC,oBAAA;MAC1C,IAAID,aAAa,aAAbA,aAAa,gBAAAC,oBAAA,GAAbD,aAAa,CAAEE,KAAK,cAAAD,oBAAA,eAApBA,oBAAA,CAAsBE,gBAAgB,EAAE;QAC1C,IAAMC,QAAQ,GAAG,CAAEJ,aAAa,CAACK,MAAM,GAAGL,aAAa,CAAClD,KAAK,GAAI,GAAG,EAAEwD,OAAO,CAAC,CAAC,CAAC;QAChFlG,YAAY,CAACjI,KAAK,GAAGoO,QAAQ,CAACH,QAAQ,CAAC;MACzC;IACF,CAAC;IACD;AACA;AACA;IACA,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CAAIrG,IAAI,EAAK;MAC3B,IAAMsG,KAAK,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC5BD,KAAK,CAACE,MAAM,CAAC,MAAM,EAAExG,IAAI,CAACA,IAAI,CAAC;MAC/ByG,kBAAkB,CAACH,KAAK,CAAC;IAC3B,CAAC;IAED,IAAMG,kBAAkB;MAAA,IAAAC,KAAA,GAAAhI,iBAAA,cAAApH,mBAAA,GAAAoF,IAAA,CAAG,SAAAiK,SAAOlC,MAAM;QAAA,IAAAmC,iBAAA,EAAAC,qBAAA,EAAAnE,IAAA;QAAA,OAAApL,mBAAA,GAAAuB,IAAA,UAAAiO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5J,IAAA,GAAA4J,SAAA,CAAAvL,IAAA;YAAA;cAAAuL,SAAA,CAAA5J,IAAA;cAAA4J,SAAA,CAAAvL,IAAA;cAAA,OAEbuD,GAAG,CAACiI,YAAY,CAACvC,MAAM,EAAEmB,gBAAgB,EAAEtG,IAAI,CAAC,CAAC,CAAC;YAAA;cAAAuH,qBAAA,GAAAE,SAAA,CAAA9L,IAAA;cAAjEyH,IAAI,GAAAmE,qBAAA,CAAJnE,IAAI;cACZ1C,IAAI,CAAChI,KAAK,GAAG0K,IAAI;cACjB,CAAAkE,iBAAA,GAAA9F,SAAS,CAAC9I,KAAK,cAAA4O,iBAAA,eAAfA,iBAAA,CAAiBhD,aAAa,CAAC,CAAClB,IAAI,CAAC,CAAC;cACtC5C,OAAO,CAAC9H,KAAK,GAAG,KAAK;cACrBkI,cAAc,CAAClI,KAAK,GAAG,KAAK;cAAA+O,SAAA,CAAAvL,IAAA;cAAA;YAAA;cAAAuL,SAAA,CAAA5J,IAAA;cAAA4J,SAAA,CAAAE,EAAA,GAAAF,SAAA;cAE5BjH,OAAO,CAAC9H,KAAK,GAAG,KAAK;cACrBkI,cAAc,CAAClI,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAA+O,SAAA,CAAAzJ,IAAA;UAAA;QAAA,GAAAqJ,QAAA;MAAA,CAE/B;MAAA,gBAXKF,kBAAkBA,CAAAS,EAAA;QAAA,OAAAR,KAAA,CAAA9H,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}