<template>
  <div class="UnifyLogin">
    <el-carousel class="UnifyLoginCarousel" v-if="imgList.length" height="100%">
      <el-carousel-item class="UnifyLoginCarouselBox" v-for="item in imgList" :key="item.id">
        <el-image :src="item.imgPath" loading="lazy" fit="cover" />
      </el-carousel-item>
    </el-carousel>
    <div class="UnifyLoginBox">
      <div class="UnifyLoginLogo">
        <el-image :src="systemLogo" fit="cover" />
      </div>
      <div class="UnifyLoginName" v-html="loginSystemName"></div>
      <el-form ref="LoginForm" :model="form" :rules="rules" class="UnifyLoginForm">
        <el-form-item prop="account">
          <el-input v-model="form.account" placeholder="账号/手机号" @blur="handleBlur" clearable />
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" v-model="form.password" placeholder="密码" show-password clearable />
        </el-form-item>
        <el-form-item class="smsValidation" v-if="loginVerifyShow && whetherVerifyCode" prop="verifyCode">
          <el-input v-model="form.verifyCode" placeholder="短信验证码" clearable></el-input>
          <el-button type="primary" @click="handleGetVerifyCode" :disabled="countDownText != '获取验证码'">
            {{ countDownText }}
          </el-button>
        </el-form-item>
        <div class="UnifyLoginSlideVerify" v-if="loginVerifyShow && !whetherVerifyCode">
          <xyl-slide-verify ref="slideVerify" @again="onAgain" @success="onSuccess" :disabled="disabled" />
        </div>
        <div class="UnifyLoginFormOperation">
          <el-checkbox v-model="checked">记住用户名和密码</el-checkbox>
          <div class="UnifyLoginFormOperationText" @click="show = !show">忘记密码？</div>
        </div>
        <el-button type="primary" @click="submitForm(LoginForm)" class="UnifyLoginFormButton" :loading="loading"
          :disabled="loginDisabled">
          {{ loading ? '登录中' : '登录' }}
        </el-button>
      </el-form>
      <div class="UnifyLoginOperation" v-if="appDownloadUrl">
        <div class="UnifyLoginOperationBox">
          <el-popover placement="top" width="auto" @show="refresh" @hide="hideQrcode">
            <div class="UnifyLoginQrCodeBox">
              <div class="UnifyLoginQrCodeNameBody">
                <div class="UnifyLoginQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="UnifyLoginQrCodeName">APP扫码登录</div>
              </div>
              <div class="UnifyLoginQrCodeRefreshBody">
                <qrcode-vue :value="loginQrcode" :size="120" />
                <div class="UnifyLoginQrCodeRefresh" v-show="loginQrcodeShow">
                  <el-button type="primary" @click="refresh">刷新</el-button>
                </div>
              </div>
              <div class="UnifyLoginQrCodeText">请使用{{ systemName }}APP扫码登录</div>
            </div>
            <template #reference>
              <div class="UnifyLoginQrCode"></div>
            </template>
          </el-popover>
          <div class="UnifyLoginOperationText">APP扫码登录</div>
        </div>
        <div class="UnifyLoginOperationBox">
          <el-popover placement="top" width="auto">
            <div class="UnifyLoginQrCodeBox">
              <div class="UnifyLoginQrCodeNameBody">
                <div class="UnifyLoginQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="UnifyLoginQrCodeName">手机APP下载</div>
              </div>
              <qrcode-vue :value="appDownloadUrl" :size="120" />
              <div class="UnifyLoginQrCodeText">使用其他软件扫码下载{{ systemName }}APP</div>
            </div>
            <template #reference>
              <div class="UnifyLoginApp"></div>
            </template>
          </el-popover>
          <div class="UnifyLoginOperationText">手机APP下载</div>
        </div>
      </div>
      <div class="UnifyLoginSystemTips" v-if="systemLoginContact">{{ systemLoginContact }}</div>
    </div>
    <xyl-popup-window v-model="show" name="重置密码">
      <ResetPassword @callback="show = !show"></ResetPassword>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'UnifyLogin' }
</script>
<script setup>
import { ref, onMounted, computed, defineAsyncComponent } from 'vue'
import {
  systemLogo,
  systemName,
  platformAreaName,
  loginNameLineFeedPosition,
  appDownloadUrl,
  systemLoginContact
} from 'common/js/system_var.js'
import { LoginView } from '../LoginView/LoginView.js'
const QrcodeVue = defineAsyncComponent(() => import('qrcode.vue'))
const ResetPassword = defineAsyncComponent(() => import('../LoginView/component/ResetPassword.vue'))
const show = ref(false)
const loginSystemName = computed(() => {
  const name = (platformAreaName.value || '') + systemName.value
  const num = Number(loginNameLineFeedPosition.value || '0') || 0
  return num ? name.substring(0, num) + '\n' + name.substring(num) : name
})
const {
  loginVerifyShow,
  whetherVerifyCode,
  loginDisabled,
  loading,
  checked,
  imgList,
  LoginForm,
  form,
  rules,
  countDownText,
  slideVerify,
  disabled,
  loginQrcode,
  loginQrcodeShow,
  handleBlur,
  handleGetVerifyCode,
  onAgain,
  onSuccess,
  globalData,
  submitForm,
  loginInfo,
  refresh,
  hideQrcode
} = LoginView('/UnifyLogin')
onMounted(() => {
  loginInfo()
  globalData()
})
</script>
<style lang="scss">
.UnifyLogin {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .UnifyLoginCarousel {
    width: 100%;
    height: 100%;
    position: absolute;

    .UnifyLoginCarouselBox {
      width: 100%;
      height: 100%;

      .zy-el-image {
        width: 100%;
        height: 100%;
      }
    }

    .zy-el-carousel__indicators--horizontal {
      .zy-el-carousel__button {
        width: 16px;
        height: 16px;
        border-radius: 50%;
      }
    }
  }

  .UnifyLoginBox {
    padding: var(--zy-distance-one);
    box-shadow: var(--zy-el-box-shadow);
    padding-bottom: var(--zy-distance-two);
    border-radius: var(--el-border-radius-base);
    margin-right: 80px;
    background: #fff;
    position: relative;
    z-index: 2;

    .UnifyLoginLogo {
      width: 60px;
      margin: auto;
      margin-bottom: var(--zy-distance-two);

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .UnifyLoginName {
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: var(--zy-system-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      letter-spacing: 2px;
      padding-bottom: var(--zy-distance-one);
      white-space: pre-wrap;
      margin: auto;
    }

    .UnifyLoginForm {
      width: 320px;
      margin: auto;
      padding-bottom: var(--zy-distance-one);

      input:-webkit-autofill {
        transition: background-color 5000s ease-in-out 0s;
      }

      .zy-el-form-item {
        margin-bottom: var(--zy-form-distance-bottom);
      }

      .UnifyLoginFormButton {
        width: 100%;
      }

      .smsValidation {
        .zy-el-form-item__content {
          display: flex;
          justify-content: space-between;
        }

        .zy-el-input {
          width: 56%;
        }
      }

      .UnifyLoginSlideVerify {
        margin-bottom: var(--zy-distance-five);
      }

      .UnifyLoginFormOperation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--zy-distance-three);

        .zy-el-checkbox {
          height: var(--zy-height-secondary);
        }

        .UnifyLoginFormOperationText {
          cursor: pointer;
          color: var(--zy-el-color-primary);
          font-size: var(--zy-text-font-size);
        }
      }
    }

    .UnifyLoginOperation {
      width: 100%;
      padding-bottom: var(--zy-distance-two);
      display: flex;
      justify-content: space-between;

      .UnifyLoginOperationBox {
        margin: 0 var(--zy-distance-two);
        cursor: pointer;

        .UnifyLoginQrCode {
          width: 50px;
          height: 50px;
          background: url('../img/login_qr_code.png');
          background-size: 100% 100%;
          margin: auto;
        }

        .UnifyLoginApp {
          width: 50px;
          height: 50px;
          background: url('../img/login_app.png') no-repeat;
          background-size: auto 100%;
          background-position: center;
          margin: auto;
        }

        .UnifyLoginOperationText {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          padding: var(--el-border-radius-small) 0;
          text-align: center;
        }
      }
    }

    .UnifyLoginForm+.UnifyLoginSystemTips {
      padding-top: var(--zy-distance-one);
    }

    .UnifyLoginSystemTips {
      color: var(--zy-el-text-color-secondary);
      font-size: var(--zy-text-font-size);
      text-align: center;
    }
  }
}

.UnifyLoginQrCodeBox {
  width: 320px;
  background-color: #fff;

  canvas {
    display: block;
    margin: auto;
  }

  .UnifyLoginQrCodeNameBody {
    padding: var(--zy-distance-three);
    display: flex;
    align-items: center;
    justify-content: center;

    .UnifyLoginQrCodeLogo {
      width: 26px;
      margin-right: 6px;

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .UnifyLoginQrCodeName {
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
    }
  }

  .UnifyLoginQrCodeRefreshBody {
    position: relative;

    .UnifyLoginQrCodeRefresh {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 120px;
      background-color: rgba(000, 000, 000, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;

      .zy-el-button {
        --zy-el-button-size: var(--zy-height-secondary);
      }
    }
  }

  .UnifyLoginQrCodeText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-three);
    color: var(--zy-el-color-primary);
  }
}
</style>
