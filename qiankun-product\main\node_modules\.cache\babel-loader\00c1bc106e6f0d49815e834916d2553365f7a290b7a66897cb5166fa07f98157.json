{"ast": null, "code": "function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct.bind();\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n  return _construct.apply(null, arguments);\n}\nfunction _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}\nfunction _wrapNativeSuper(Class) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n  _wrapNativeSuper = function _wrapNativeSuper(Class) {\n    if (Class === null || !_isNativeFunction(Class)) return Class;\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    if (typeof _cache !== \"undefined\") {\n      if (_cache.has(Class)) return _cache.get(Class);\n      _cache.set(Class, Wrapper);\n    }\n    function Wrapper() {\n      return _construct(Class, arguments, _getPrototypeOf(this).constructor);\n    }\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    return _setPrototypeOf(Wrapper, Class);\n  };\n  return _wrapNativeSuper(Class);\n}\n\n/* eslint no-console:0 */\nvar formatRegExp = /%[sdj%]/g;\nvar warning = function warning() {}; // don't print warning message when in production env or node runtime\n\nif (typeof process !== 'undefined' && process.env && process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && typeof document !== 'undefined') {\n  warning = function warning(type, errors) {\n    if (typeof console !== 'undefined' && console.warn && typeof ASYNC_VALIDATOR_NO_WARNING === 'undefined') {\n      if (errors.every(function (e) {\n        return typeof e === 'string';\n      })) {\n        console.warn(type, errors);\n      }\n    }\n  };\n}\nfunction convertFieldsError(errors) {\n  if (!errors || !errors.length) return null;\n  var fields = {};\n  errors.forEach(function (error) {\n    var field = error.field;\n    fields[field] = fields[field] || [];\n    fields[field].push(error);\n  });\n  return fields;\n}\nfunction format(template) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  var i = 0;\n  var len = args.length;\n  if (typeof template === 'function') {\n    return template.apply(null, args);\n  }\n  if (typeof template === 'string') {\n    var str = template.replace(formatRegExp, function (x) {\n      if (x === '%%') {\n        return '%';\n      }\n      if (i >= len) {\n        return x;\n      }\n      switch (x) {\n        case '%s':\n          return String(args[i++]);\n        case '%d':\n          return Number(args[i++]);\n        case '%j':\n          try {\n            return JSON.stringify(args[i++]);\n          } catch (_) {\n            return '[Circular]';\n          }\n          break;\n        default:\n          return x;\n      }\n    });\n    return str;\n  }\n  return template;\n}\nfunction isNativeStringType(type) {\n  return type === 'string' || type === 'url' || type === 'hex' || type === 'email' || type === 'date' || type === 'pattern';\n}\nfunction isEmptyValue(value, type) {\n  if (value === undefined || value === null) {\n    return true;\n  }\n  if (type === 'array' && Array.isArray(value) && !value.length) {\n    return true;\n  }\n  if (isNativeStringType(type) && typeof value === 'string' && !value) {\n    return true;\n  }\n  return false;\n}\nfunction asyncParallelArray(arr, func, callback) {\n  var results = [];\n  var total = 0;\n  var arrLength = arr.length;\n  function count(errors) {\n    results.push.apply(results, errors || []);\n    total++;\n    if (total === arrLength) {\n      callback(results);\n    }\n  }\n  arr.forEach(function (a) {\n    func(a, count);\n  });\n}\nfunction asyncSerialArray(arr, func, callback) {\n  var index = 0;\n  var arrLength = arr.length;\n  function next(errors) {\n    if (errors && errors.length) {\n      callback(errors);\n      return;\n    }\n    var original = index;\n    index = index + 1;\n    if (original < arrLength) {\n      func(arr[original], next);\n    } else {\n      callback([]);\n    }\n  }\n  next([]);\n}\nfunction flattenObjArr(objArr) {\n  var ret = [];\n  Object.keys(objArr).forEach(function (k) {\n    ret.push.apply(ret, objArr[k] || []);\n  });\n  return ret;\n}\nvar AsyncValidationError = /*#__PURE__*/function (_Error) {\n  _inheritsLoose(AsyncValidationError, _Error);\n  function AsyncValidationError(errors, fields) {\n    var _this;\n    _this = _Error.call(this, 'Async Validation Error') || this;\n    _this.errors = errors;\n    _this.fields = fields;\n    return _this;\n  }\n  return AsyncValidationError;\n}(/*#__PURE__*/_wrapNativeSuper(Error));\nfunction asyncMap(objArr, option, func, callback, source) {\n  if (option.first) {\n    var _pending = new Promise(function (resolve, reject) {\n      var next = function next(errors) {\n        callback(errors);\n        return errors.length ? reject(new AsyncValidationError(errors, convertFieldsError(errors))) : resolve(source);\n      };\n      var flattenArr = flattenObjArr(objArr);\n      asyncSerialArray(flattenArr, func, next);\n    });\n    _pending[\"catch\"](function (e) {\n      return e;\n    });\n    return _pending;\n  }\n  var firstFields = option.firstFields === true ? Object.keys(objArr) : option.firstFields || [];\n  var objArrKeys = Object.keys(objArr);\n  var objArrLength = objArrKeys.length;\n  var total = 0;\n  var results = [];\n  var pending = new Promise(function (resolve, reject) {\n    var next = function next(errors) {\n      results.push.apply(results, errors);\n      total++;\n      if (total === objArrLength) {\n        callback(results);\n        return results.length ? reject(new AsyncValidationError(results, convertFieldsError(results))) : resolve(source);\n      }\n    };\n    if (!objArrKeys.length) {\n      callback(results);\n      resolve(source);\n    }\n    objArrKeys.forEach(function (key) {\n      var arr = objArr[key];\n      if (firstFields.indexOf(key) !== -1) {\n        asyncSerialArray(arr, func, next);\n      } else {\n        asyncParallelArray(arr, func, next);\n      }\n    });\n  });\n  pending[\"catch\"](function (e) {\n    return e;\n  });\n  return pending;\n}\nfunction isErrorObj(obj) {\n  return !!(obj && obj.message !== undefined);\n}\nfunction getValue(value, path) {\n  var v = value;\n  for (var i = 0; i < path.length; i++) {\n    if (v == undefined) {\n      return v;\n    }\n    v = v[path[i]];\n  }\n  return v;\n}\nfunction complementError(rule, source) {\n  return function (oe) {\n    var fieldValue;\n    if (rule.fullFields) {\n      fieldValue = getValue(source, rule.fullFields);\n    } else {\n      fieldValue = source[oe.field || rule.fullField];\n    }\n    if (isErrorObj(oe)) {\n      oe.field = oe.field || rule.fullField;\n      oe.fieldValue = fieldValue;\n      return oe;\n    }\n    return {\n      message: typeof oe === 'function' ? oe() : oe,\n      fieldValue: fieldValue,\n      field: oe.field || rule.fullField\n    };\n  };\n}\nfunction deepMerge(target, source) {\n  if (source) {\n    for (var s in source) {\n      if (source.hasOwnProperty(s)) {\n        var value = source[s];\n        if (typeof value === 'object' && typeof target[s] === 'object') {\n          target[s] = _extends({}, target[s], value);\n        } else {\n          target[s] = value;\n        }\n      }\n    }\n  }\n  return target;\n}\nvar required$1 = function required(rule, value, source, errors, options, type) {\n  if (rule.required && (!source.hasOwnProperty(rule.field) || isEmptyValue(value, type || rule.type))) {\n    errors.push(format(options.messages.required, rule.fullField));\n  }\n};\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\n\nvar whitespace = function whitespace(rule, value, source, errors, options) {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(format(options.messages.whitespace, rule.fullField));\n  }\n};\n\n// https://github.com/kevva/url-regex/blob/master/index.js\nvar urlReg;\nvar getUrlRegex = function getUrlRegex() {\n  if (urlReg) {\n    return urlReg;\n  }\n  var word = '[a-fA-F\\\\d:]';\n  var b = function b(options) {\n    return options && options.includeBoundaries ? \"(?:(?<=\\\\s|^)(?=\" + word + \")|(?<=\" + word + \")(?=\\\\s|$))\" : '';\n  };\n  var v4 = '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n  var v6seg = '[a-fA-F\\\\d]{1,4}';\n  var v6 = (\"\\n(?:\\n(?:\" + v6seg + \":){7}(?:\" + v6seg + \"|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\\n(?:\" + v6seg + \":){6}(?:\" + v4 + \"|:\" + v6seg + \"|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\\n(?:\" + v6seg + \":){5}(?::\" + v4 + \"|(?::\" + v6seg + \"){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\\n(?:\" + v6seg + \":){4}(?:(?::\" + v6seg + \"){0,1}:\" + v4 + \"|(?::\" + v6seg + \"){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\\n(?:\" + v6seg + \":){3}(?:(?::\" + v6seg + \"){0,2}:\" + v4 + \"|(?::\" + v6seg + \"){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\\n(?:\" + v6seg + \":){2}(?:(?::\" + v6seg + \"){0,3}:\" + v4 + \"|(?::\" + v6seg + \"){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\\n(?:\" + v6seg + \":){1}(?:(?::\" + v6seg + \"){0,4}:\" + v4 + \"|(?::\" + v6seg + \"){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\\n(?::(?:(?::\" + v6seg + \"){0,5}:\" + v4 + \"|(?::\" + v6seg + \"){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\\n\").replace(/\\s*\\/\\/.*$/gm, '').replace(/\\n/g, '').trim(); // Pre-compile only the exact regexes because adding a global flag make regexes stateful\n\n  var v46Exact = new RegExp(\"(?:^\" + v4 + \"$)|(?:^\" + v6 + \"$)\");\n  var v4exact = new RegExp(\"^\" + v4 + \"$\");\n  var v6exact = new RegExp(\"^\" + v6 + \"$\");\n  var ip = function ip(options) {\n    return options && options.exact ? v46Exact : new RegExp(\"(?:\" + b(options) + v4 + b(options) + \")|(?:\" + b(options) + v6 + b(options) + \")\", 'g');\n  };\n  ip.v4 = function (options) {\n    return options && options.exact ? v4exact : new RegExp(\"\" + b(options) + v4 + b(options), 'g');\n  };\n  ip.v6 = function (options) {\n    return options && options.exact ? v6exact : new RegExp(\"\" + b(options) + v6 + b(options), 'g');\n  };\n  var protocol = \"(?:(?:[a-z]+:)?//)\";\n  var auth = '(?:\\\\S+(?::\\\\S*)?@)?';\n  var ipv4 = ip.v4().source;\n  var ipv6 = ip.v6().source;\n  var host = \"(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)\";\n  var domain = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*\";\n  var tld = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\";\n  var port = '(?::\\\\d{2,5})?';\n  var path = '(?:[/?#][^\\\\s\"]*)?';\n  var regex = \"(?:\" + protocol + \"|www\\\\.)\" + auth + \"(?:localhost|\" + ipv4 + \"|\" + ipv6 + \"|\" + host + domain + tld + \")\" + port + path;\n  urlReg = new RegExp(\"(?:^\" + regex + \"$)\", 'i');\n  return urlReg;\n};\n\n/* eslint max-len:0 */\n\nvar pattern$2 = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+\\.)+[a-zA-Z\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}))$/,\n  // url: new RegExp(\n  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\n  //   'i',\n  // ),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i\n};\nvar types = {\n  integer: function integer(value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  \"float\": function float(value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array: function array(value) {\n    return Array.isArray(value);\n  },\n  regexp: function regexp(value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date: function date(value) {\n    return typeof value.getTime === 'function' && typeof value.getMonth === 'function' && typeof value.getYear === 'function' && !isNaN(value.getTime());\n  },\n  number: function number(value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object: function object(value) {\n    return typeof value === 'object' && !types.array(value);\n  },\n  method: function method(value) {\n    return typeof value === 'function';\n  },\n  email: function email(value) {\n    return typeof value === 'string' && value.length <= 320 && !!value.match(pattern$2.email);\n  },\n  url: function url(value) {\n    return typeof value === 'string' && value.length <= 2048 && !!value.match(getUrlRegex());\n  },\n  hex: function hex(value) {\n    return typeof value === 'string' && !!value.match(pattern$2.hex);\n  }\n};\nvar type$1 = function type(rule, value, source, errors, options) {\n  if (rule.required && value === undefined) {\n    required$1(rule, value, source, errors, options);\n    return;\n  }\n  var custom = ['integer', 'float', 'array', 'regexp', 'object', 'method', 'email', 'number', 'date', 'url', 'hex'];\n  var ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n    } // straight typeof check\n  } else if (ruleType && typeof value !== rule.type) {\n    errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n  }\n};\nvar range = function range(rule, value, source, errors, options) {\n  var len = typeof rule.len === 'number';\n  var min = typeof rule.min === 'number';\n  var max = typeof rule.max === 'number'; // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n\n  var spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  var val = value;\n  var key = null;\n  var num = typeof value === 'number';\n  var str = typeof value === 'string';\n  var arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  } // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(format(options.messages[key].range, rule.fullField, rule.min, rule.max));\n  }\n};\nvar ENUM$1 = 'enum';\nvar enumerable$1 = function enumerable(rule, value, source, errors, options) {\n  rule[ENUM$1] = Array.isArray(rule[ENUM$1]) ? rule[ENUM$1] : [];\n  if (rule[ENUM$1].indexOf(value) === -1) {\n    errors.push(format(options.messages[ENUM$1], rule.fullField, rule[ENUM$1].join(', ')));\n  }\n};\nvar pattern$1 = function pattern(rule, value, source, errors, options) {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    } else if (typeof rule.pattern === 'string') {\n      var _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    }\n  }\n};\nvar rules = {\n  required: required$1,\n  whitespace: whitespace,\n  type: type$1,\n  range: range,\n  \"enum\": enumerable$1,\n  pattern: pattern$1\n};\nvar string = function string(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'string');\n    if (!isEmptyValue(value, 'string')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n      rules.pattern(rule, value, source, errors, options);\n      if (rule.whitespace === true) {\n        rules.whitespace(rule, value, source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\nvar method = function method(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar number = function number(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (value === '') {\n      value = undefined;\n    }\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar _boolean = function _boolean(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar regexp = function regexp(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar integer = function integer(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar floatFn = function floatFn(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar array = function array(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if ((value === undefined || value === null) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (value !== undefined && value !== null) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar object = function object(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar ENUM = 'enum';\nvar enumerable = function enumerable(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules[ENUM](rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar pattern = function pattern(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'string')) {\n      rules.pattern(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar date = function date(rule, value, callback, source, options) {\n  // console.log('integer rule called %j', rule);\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field); // console.log('validate on %s value', value);\n\n  if (validate) {\n    if (isEmptyValue(value, 'date') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'date')) {\n      var dateObject;\n      if (value instanceof Date) {\n        dateObject = value;\n      } else {\n        dateObject = new Date(value);\n      }\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\nvar required = function required(rule, value, callback, source, options) {\n  var errors = [];\n  var type = Array.isArray(value) ? 'array' : typeof value;\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n};\nvar type = function type(rule, value, callback, source, options) {\n  var ruleType = rule.type;\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, ruleType) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, ruleType);\n    if (!isEmptyValue(value, ruleType)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar any = function any(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n  }\n  callback(errors);\n};\nvar validators = {\n  string: string,\n  method: method,\n  number: number,\n  \"boolean\": _boolean,\n  regexp: regexp,\n  integer: integer,\n  \"float\": floatFn,\n  array: array,\n  object: object,\n  \"enum\": enumerable,\n  pattern: pattern,\n  date: date,\n  url: type,\n  hex: type,\n  email: type,\n  required: required,\n  any: any\n};\nfunction newMessages() {\n  return {\n    \"default\": 'Validation error on field %s',\n    required: '%s is required',\n    \"enum\": '%s must be one of %s',\n    whitespace: '%s cannot be empty',\n    date: {\n      format: '%s date %s is invalid for format %s',\n      parse: '%s date could not be parsed, %s is invalid ',\n      invalid: '%s date %s is invalid'\n    },\n    types: {\n      string: '%s is not a %s',\n      method: '%s is not a %s (function)',\n      array: '%s is not an %s',\n      object: '%s is not an %s',\n      number: '%s is not a %s',\n      date: '%s is not a %s',\n      \"boolean\": '%s is not a %s',\n      integer: '%s is not an %s',\n      \"float\": '%s is not a %s',\n      regexp: '%s is not a valid %s',\n      email: '%s is not a valid %s',\n      url: '%s is not a valid %s',\n      hex: '%s is not a valid %s'\n    },\n    string: {\n      len: '%s must be exactly %s characters',\n      min: '%s must be at least %s characters',\n      max: '%s cannot be longer than %s characters',\n      range: '%s must be between %s and %s characters'\n    },\n    number: {\n      len: '%s must equal %s',\n      min: '%s cannot be less than %s',\n      max: '%s cannot be greater than %s',\n      range: '%s must be between %s and %s'\n    },\n    array: {\n      len: '%s must be exactly %s in length',\n      min: '%s cannot be less than %s in length',\n      max: '%s cannot be greater than %s in length',\n      range: '%s must be between %s and %s in length'\n    },\n    pattern: {\n      mismatch: '%s value %s does not match pattern %s'\n    },\n    clone: function clone() {\n      var cloned = JSON.parse(JSON.stringify(this));\n      cloned.clone = this.clone;\n      return cloned;\n    }\n  };\n}\nvar messages = newMessages();\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\n\nvar Schema = /*#__PURE__*/function () {\n  // ========================= Static =========================\n  // ======================== Instance ========================\n  function Schema(descriptor) {\n    this.rules = null;\n    this._messages = messages;\n    this.define(descriptor);\n  }\n  var _proto = Schema.prototype;\n  _proto.define = function define(rules) {\n    var _this = this;\n    if (!rules) {\n      throw new Error('Cannot configure a schema with no rules');\n    }\n    if (typeof rules !== 'object' || Array.isArray(rules)) {\n      throw new Error('Rules must be an object');\n    }\n    this.rules = {};\n    Object.keys(rules).forEach(function (name) {\n      var item = rules[name];\n      _this.rules[name] = Array.isArray(item) ? item : [item];\n    });\n  };\n  _proto.messages = function messages(_messages) {\n    if (_messages) {\n      this._messages = deepMerge(newMessages(), _messages);\n    }\n    return this._messages;\n  };\n  _proto.validate = function validate(source_, o, oc) {\n    var _this2 = this;\n    if (o === void 0) {\n      o = {};\n    }\n    if (oc === void 0) {\n      oc = function oc() {};\n    }\n    var source = source_;\n    var options = o;\n    var callback = oc;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!this.rules || Object.keys(this.rules).length === 0) {\n      if (callback) {\n        callback(null, source);\n      }\n      return Promise.resolve(source);\n    }\n    function complete(results) {\n      var errors = [];\n      var fields = {};\n      function add(e) {\n        if (Array.isArray(e)) {\n          var _errors;\n          errors = (_errors = errors).concat.apply(_errors, e);\n        } else {\n          errors.push(e);\n        }\n      }\n      for (var i = 0; i < results.length; i++) {\n        add(results[i]);\n      }\n      if (!errors.length) {\n        callback(null, source);\n      } else {\n        fields = convertFieldsError(errors);\n        callback(errors, fields);\n      }\n    }\n    if (options.messages) {\n      var messages$1 = this.messages();\n      if (messages$1 === messages) {\n        messages$1 = newMessages();\n      }\n      deepMerge(messages$1, options.messages);\n      options.messages = messages$1;\n    } else {\n      options.messages = this.messages();\n    }\n    var series = {};\n    var keys = options.keys || Object.keys(this.rules);\n    keys.forEach(function (z) {\n      var arr = _this2.rules[z];\n      var value = source[z];\n      arr.forEach(function (r) {\n        var rule = r;\n        if (typeof rule.transform === 'function') {\n          if (source === source_) {\n            source = _extends({}, source);\n          }\n          value = source[z] = rule.transform(value);\n        }\n        if (typeof rule === 'function') {\n          rule = {\n            validator: rule\n          };\n        } else {\n          rule = _extends({}, rule);\n        } // Fill validator. Skip if nothing need to validate\n\n        rule.validator = _this2.getValidationMethod(rule);\n        if (!rule.validator) {\n          return;\n        }\n        rule.field = z;\n        rule.fullField = rule.fullField || z;\n        rule.type = _this2.getType(rule);\n        series[z] = series[z] || [];\n        series[z].push({\n          rule: rule,\n          value: value,\n          source: source,\n          field: z\n        });\n      });\n    });\n    var errorFields = {};\n    return asyncMap(series, options, function (data, doIt) {\n      var rule = data.rule;\n      var deep = (rule.type === 'object' || rule.type === 'array') && (typeof rule.fields === 'object' || typeof rule.defaultField === 'object');\n      deep = deep && (rule.required || !rule.required && data.value);\n      rule.field = data.field;\n      function addFullField(key, schema) {\n        return _extends({}, schema, {\n          fullField: rule.fullField + \".\" + key,\n          fullFields: rule.fullFields ? [].concat(rule.fullFields, [key]) : [key]\n        });\n      }\n      function cb(e) {\n        if (e === void 0) {\n          e = [];\n        }\n        var errorList = Array.isArray(e) ? e : [e];\n        if (!options.suppressWarning && errorList.length) {\n          Schema.warning('async-validator:', errorList);\n        }\n        if (errorList.length && rule.message !== undefined) {\n          errorList = [].concat(rule.message);\n        } // Fill error info\n\n        var filledErrors = errorList.map(complementError(rule, source));\n        if (options.first && filledErrors.length) {\n          errorFields[rule.field] = 1;\n          return doIt(filledErrors);\n        }\n        if (!deep) {\n          doIt(filledErrors);\n        } else {\n          // if rule is required but the target object\n          // does not exist fail at the rule level and don't\n          // go deeper\n          if (rule.required && !data.value) {\n            if (rule.message !== undefined) {\n              filledErrors = [].concat(rule.message).map(complementError(rule, source));\n            } else if (options.error) {\n              filledErrors = [options.error(rule, format(options.messages.required, rule.field))];\n            }\n            return doIt(filledErrors);\n          }\n          var fieldsSchema = {};\n          if (rule.defaultField) {\n            Object.keys(data.value).map(function (key) {\n              fieldsSchema[key] = rule.defaultField;\n            });\n          }\n          fieldsSchema = _extends({}, fieldsSchema, data.rule.fields);\n          var paredFieldsSchema = {};\n          Object.keys(fieldsSchema).forEach(function (field) {\n            var fieldSchema = fieldsSchema[field];\n            var fieldSchemaList = Array.isArray(fieldSchema) ? fieldSchema : [fieldSchema];\n            paredFieldsSchema[field] = fieldSchemaList.map(addFullField.bind(null, field));\n          });\n          var schema = new Schema(paredFieldsSchema);\n          schema.messages(options.messages);\n          if (data.rule.options) {\n            data.rule.options.messages = options.messages;\n            data.rule.options.error = options.error;\n          }\n          schema.validate(data.value, data.rule.options || options, function (errs) {\n            var finalErrors = [];\n            if (filledErrors && filledErrors.length) {\n              finalErrors.push.apply(finalErrors, filledErrors);\n            }\n            if (errs && errs.length) {\n              finalErrors.push.apply(finalErrors, errs);\n            }\n            doIt(finalErrors.length ? finalErrors : null);\n          });\n        }\n      }\n      var res;\n      if (rule.asyncValidator) {\n        res = rule.asyncValidator(rule, data.value, cb, data.source, options);\n      } else if (rule.validator) {\n        try {\n          res = rule.validator(rule, data.value, cb, data.source, options);\n        } catch (error) {\n          console.error == null ? void 0 : console.error(error); // rethrow to report error\n\n          if (!options.suppressValidatorError) {\n            setTimeout(function () {\n              throw error;\n            }, 0);\n          }\n          cb(error.message);\n        }\n        if (res === true) {\n          cb();\n        } else if (res === false) {\n          cb(typeof rule.message === 'function' ? rule.message(rule.fullField || rule.field) : rule.message || (rule.fullField || rule.field) + \" fails\");\n        } else if (res instanceof Array) {\n          cb(res);\n        } else if (res instanceof Error) {\n          cb(res.message);\n        }\n      }\n      if (res && res.then) {\n        res.then(function () {\n          return cb();\n        }, function (e) {\n          return cb(e);\n        });\n      }\n    }, function (results) {\n      complete(results);\n    }, source);\n  };\n  _proto.getType = function getType(rule) {\n    if (rule.type === undefined && rule.pattern instanceof RegExp) {\n      rule.type = 'pattern';\n    }\n    if (typeof rule.validator !== 'function' && rule.type && !validators.hasOwnProperty(rule.type)) {\n      throw new Error(format('Unknown rule type %s', rule.type));\n    }\n    return rule.type || 'string';\n  };\n  _proto.getValidationMethod = function getValidationMethod(rule) {\n    if (typeof rule.validator === 'function') {\n      return rule.validator;\n    }\n    var keys = Object.keys(rule);\n    var messageIndex = keys.indexOf('message');\n    if (messageIndex !== -1) {\n      keys.splice(messageIndex, 1);\n    }\n    if (keys.length === 1 && keys[0] === 'required') {\n      return validators.required;\n    }\n    return validators[this.getType(rule)] || undefined;\n  };\n  return Schema;\n}();\nSchema.register = function register(type, validator) {\n  if (typeof validator !== 'function') {\n    throw new Error('Cannot register a validator by type, validator is not a function');\n  }\n  validators[type] = validator;\n};\nSchema.warning = warning;\nSchema.messages = messages;\nSchema.validators = validators;\nexport { Schema as default };", "map": {"version": 3, "names": ["formatRegExp", "warning", "process", "env", "NODE_ENV", "window", "document", "type", "errors", "console", "warn", "ASYNC_VALIDATOR_NO_WARNING", "every", "e", "convertFieldsError", "length", "fields", "for<PERSON>ach", "error", "field", "push", "format", "template", "_len", "arguments", "args", "Array", "_key", "i", "len", "apply", "str", "replace", "x", "String", "Number", "JSON", "stringify", "_", "isNativeStringType", "isEmptyValue", "value", "undefined", "isArray", "asyncParallelArray", "arr", "func", "callback", "results", "total", "arr<PERSON><PERSON><PERSON>", "count", "a", "asyncSerialArray", "index", "next", "original", "flatten<PERSON>bj<PERSON>rr", "obj<PERSON>rr", "ret", "Object", "keys", "k", "AsyncValidationError", "_Error", "_inherits<PERSON><PERSON>e", "_this", "call", "_wrapNativeSuper", "Error", "asyncMap", "option", "source", "first", "_pending", "Promise", "resolve", "reject", "flattenArr", "firstFields", "obj<PERSON><PERSON><PERSON><PERSON><PERSON>", "obj<PERSON><PERSON><PERSON><PERSON><PERSON>", "pending", "key", "indexOf", "isErrorObj", "obj", "message", "getValue", "path", "v", "complementError", "rule", "oe", "fieldValue", "fullFields", "fullField", "deepMerge", "target", "s", "hasOwnProperty", "_extends", "required$1", "required", "options", "messages", "whitespace", "test", "urlReg", "getUrlRegex", "word", "b", "includeBoundaries", "v4", "v6seg", "v6", "trim", "v46Exact", "RegExp", "v4exact", "v6exact", "ip", "exact", "protocol", "auth", "ipv4", "ipv6", "host", "domain", "tld", "port", "regex", "pattern$2", "email", "hex", "types", "integer", "number", "parseInt", "float", "array", "regexp", "date", "getTime", "getMonth", "getYear", "isNaN", "object", "method", "match", "url", "type$1", "custom", "ruleType", "range", "min", "max", "spRegexp", "val", "num", "ENUM$1", "enumerable$1", "enumerable", "join", "pattern$1", "pattern", "lastIndex", "mismatch", "_pattern", "rules", "string", "validate", "_boolean", "boolean", "floatFn", "ENUM", "dateObject", "Date", "any", "validators", "newMessages", "parse", "invalid", "clone", "cloned", "<PERSON><PERSON><PERSON>", "descriptor", "_messages", "define", "name", "item", "_proto", "source_", "o", "oc", "_this2", "complete", "add", "_errors", "concat", "messages$1", "series", "z", "r", "transform", "validator", "getValidationMethod", "getType", "errorFields", "data", "doIt", "deep", "defaultField", "addFullField", "schema", "cb", "errorList", "suppressWarning", "filledErrors", "map", "fieldsSchema", "paredFieldsSchema", "fieldSchema", "fieldSchemaList", "bind", "errs", "finalErrors", "res", "asyncValidator", "suppressValidatorError", "setTimeout", "then", "messageIndex", "splice", "register"], "sources": ["../../src/util.ts", "../../src/rule/required.ts", "../../src/rule/whitespace.ts", "../../src/rule/url.ts", "../../src/rule/type.ts", "../../src/rule/range.ts", "../../src/rule/enum.ts", "../../src/rule/pattern.ts", "../../src/rule/index.ts", "../../src/validator/string.ts", "../../src/validator/method.ts", "../../src/validator/number.ts", "../../src/validator/boolean.ts", "../../src/validator/regexp.ts", "../../src/validator/integer.ts", "../../src/validator/float.ts", "../../src/validator/array.ts", "../../src/validator/object.ts", "../../src/validator/enum.ts", "../../src/validator/pattern.ts", "../../src/validator/date.ts", "../../src/validator/required.ts", "../../src/validator/type.ts", "../../src/validator/any.ts", "../../src/validator/index.ts", "../../src/messages.ts", "../../src/index.ts"], "sourcesContent": ["/* eslint no-console:0 */\n\nimport {\n  ValidateError,\n  ValidateOption,\n  RuleValuePackage,\n  InternalRuleItem,\n  SyncErrorType,\n  RuleType,\n  Value,\n  Values,\n} from './interface';\n\nconst formatRegExp = /%[sdj%]/g;\n\ndeclare var ASYNC_VALIDATOR_NO_WARNING;\n\nexport let warning: (type: string, errors: SyncErrorType[]) => void = () => {};\n\n// don't print warning message when in production env or node runtime\nif (\n  typeof process !== 'undefined' &&\n  process.env &&\n  process.env.NODE_ENV !== 'production' &&\n  typeof window !== 'undefined' &&\n  typeof document !== 'undefined'\n) {\n  warning = (type, errors) => {\n    if (\n      typeof console !== 'undefined' &&\n      console.warn &&\n      typeof ASYNC_VALIDATOR_NO_WARNING === 'undefined'\n    ) {\n      if (errors.every(e => typeof e === 'string')) {\n        console.warn(type, errors);\n      }\n    }\n  };\n}\n\nexport function convertFieldsError(\n  errors: ValidateError[],\n): Record<string, ValidateError[]> {\n  if (!errors || !errors.length) return null;\n  const fields = {};\n  errors.forEach(error => {\n    const field = error.field;\n    fields[field] = fields[field] || [];\n    fields[field].push(error);\n  });\n  return fields;\n}\n\nexport function format(\n  template: ((...args: any[]) => string) | string,\n  ...args: any[]\n): string {\n  let i = 0;\n  const len = args.length;\n  if (typeof template === 'function') {\n    return template.apply(null, args);\n  }\n  if (typeof template === 'string') {\n    let str = template.replace(formatRegExp, x => {\n      if (x === '%%') {\n        return '%';\n      }\n      if (i >= len) {\n        return x;\n      }\n      switch (x) {\n        case '%s':\n          return String(args[i++]);\n        case '%d':\n          return (Number(args[i++]) as unknown) as string;\n        case '%j':\n          try {\n            return JSON.stringify(args[i++]);\n          } catch (_) {\n            return '[Circular]';\n          }\n          break;\n        default:\n          return x;\n      }\n    });\n    return str;\n  }\n  return template;\n}\n\nfunction isNativeStringType(type: string) {\n  return (\n    type === 'string' ||\n    type === 'url' ||\n    type === 'hex' ||\n    type === 'email' ||\n    type === 'date' ||\n    type === 'pattern'\n  );\n}\n\nexport function isEmptyValue(value: Value, type?: string) {\n  if (value === undefined || value === null) {\n    return true;\n  }\n  if (type === 'array' && Array.isArray(value) && !value.length) {\n    return true;\n  }\n  if (isNativeStringType(type) && typeof value === 'string' && !value) {\n    return true;\n  }\n  return false;\n}\n\nexport function isEmptyObject(obj: object) {\n  return Object.keys(obj).length === 0;\n}\n\nfunction asyncParallelArray(\n  arr: RuleValuePackage[],\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n) {\n  const results: ValidateError[] = [];\n  let total = 0;\n  const arrLength = arr.length;\n\n  function count(errors: ValidateError[]) {\n    results.push(...(errors || []));\n    total++;\n    if (total === arrLength) {\n      callback(results);\n    }\n  }\n\n  arr.forEach(a => {\n    func(a, count);\n  });\n}\n\nfunction asyncSerialArray(\n  arr: RuleValuePackage[],\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n) {\n  let index = 0;\n  const arrLength = arr.length;\n\n  function next(errors: ValidateError[]) {\n    if (errors && errors.length) {\n      callback(errors);\n      return;\n    }\n    const original = index;\n    index = index + 1;\n    if (original < arrLength) {\n      func(arr[original], next);\n    } else {\n      callback([]);\n    }\n  }\n\n  next([]);\n}\n\nfunction flattenObjArr(objArr: Record<string, RuleValuePackage[]>) {\n  const ret: RuleValuePackage[] = [];\n  Object.keys(objArr).forEach(k => {\n    ret.push(...(objArr[k] || []));\n  });\n  return ret;\n}\n\nexport class AsyncValidationError extends Error {\n  errors: ValidateError[];\n  fields: Record<string, ValidateError[]>;\n\n  constructor(\n    errors: ValidateError[],\n    fields: Record<string, ValidateError[]>,\n  ) {\n    super('Async Validation Error');\n    this.errors = errors;\n    this.fields = fields;\n  }\n}\n\ntype ValidateFunc = (\n  data: RuleValuePackage,\n  doIt: (errors: ValidateError[]) => void,\n) => void;\n\nexport function asyncMap(\n  objArr: Record<string, RuleValuePackage[]>,\n  option: ValidateOption,\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n  source: Values,\n): Promise<Values> {\n  if (option.first) {\n    const pending = new Promise<Values>((resolve, reject) => {\n      const next = (errors: ValidateError[]) => {\n        callback(errors);\n        return errors.length\n          ? reject(new AsyncValidationError(errors, convertFieldsError(errors)))\n          : resolve(source);\n      };\n      const flattenArr = flattenObjArr(objArr);\n      asyncSerialArray(flattenArr, func, next);\n    });\n    pending.catch(e => e);\n    return pending;\n  }\n  const firstFields =\n    option.firstFields === true\n      ? Object.keys(objArr)\n      : option.firstFields || [];\n\n  const objArrKeys = Object.keys(objArr);\n  const objArrLength = objArrKeys.length;\n  let total = 0;\n  const results: ValidateError[] = [];\n  const pending = new Promise<Values>((resolve, reject) => {\n    const next = (errors: ValidateError[]) => {\n      results.push.apply(results, errors);\n      total++;\n      if (total === objArrLength) {\n        callback(results);\n        return results.length\n          ? reject(\n              new AsyncValidationError(results, convertFieldsError(results)),\n            )\n          : resolve(source);\n      }\n    };\n    if (!objArrKeys.length) {\n      callback(results);\n      resolve(source);\n    }\n    objArrKeys.forEach(key => {\n      const arr = objArr[key];\n      if (firstFields.indexOf(key) !== -1) {\n        asyncSerialArray(arr, func, next);\n      } else {\n        asyncParallelArray(arr, func, next);\n      }\n    });\n  });\n  pending.catch(e => e);\n  return pending;\n}\n\nfunction isErrorObj(\n  obj: ValidateError | string | (() => string),\n): obj is ValidateError {\n  return !!(obj && (obj as ValidateError).message !== undefined);\n}\n\nfunction getValue(value: Values, path: string[]) {\n  let v = value;\n  for (let i = 0; i < path.length; i++) {\n    if (v == undefined) {\n      return v;\n    }\n    v = v[path[i]];\n  }\n  return v;\n}\n\nexport function complementError(rule: InternalRuleItem, source: Values) {\n  return (oe: ValidateError | (() => string) | string): ValidateError => {\n    let fieldValue;\n    if (rule.fullFields) {\n      fieldValue = getValue(source, rule.fullFields);\n    } else {\n      fieldValue = source[(oe as any).field || rule.fullField];\n    }\n    if (isErrorObj(oe)) {\n      oe.field = oe.field || rule.fullField;\n      oe.fieldValue = fieldValue;\n      return oe;\n    }\n    return {\n      message: typeof oe === 'function' ? oe() : oe,\n      fieldValue,\n      field: ((oe as unknown) as ValidateError).field || rule.fullField,\n    };\n  };\n}\n\nexport function deepMerge<T extends object>(target: T, source: Partial<T>): T {\n  if (source) {\n    for (const s in source) {\n      if (source.hasOwnProperty(s)) {\n        const value = source[s];\n        if (typeof value === 'object' && typeof target[s] === 'object') {\n          target[s] = {\n            ...target[s],\n            ...value,\n          };\n        } else {\n          target[s] = value;\n        }\n      }\n    }\n  }\n  return target;\n}\n", "import { ExecuteRule } from '../interface';\nimport { format, isEmptyValue } from '../util';\n\nconst required: ExecuteRule = (rule, value, source, errors, options, type) => {\n  if (\n    rule.required &&\n    (!source.hasOwnProperty(rule.field) ||\n      isEmptyValue(value, type || rule.type))\n  ) {\n    errors.push(format(options.messages.required, rule.fullField));\n  }\n};\n\nexport default required;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nconst whitespace: ExecuteRule = (rule, value, source, errors, options) => {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(format(options.messages.whitespace, rule.fullField));\n  }\n};\n\nexport default whitespace;\n", "// https://github.com/kevva/url-regex/blob/master/index.js\nlet urlReg: RegExp;\n\nexport default () => {\n  if (urlReg) {\n    return urlReg;\n  }\n\n  const word = '[a-fA-F\\\\d:]';\n  const b = options =>\n    options && options.includeBoundaries\n      ? `(?:(?<=\\\\s|^)(?=${word})|(?<=${word})(?=\\\\s|$))`\n      : '';\n\n  const v4 =\n    '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n\n  const v6seg = '[a-fA-F\\\\d]{1,4}';\n  const v6 = `\n(?:\n(?:${v6seg}:){7}(?:${v6seg}|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:${v6seg}:){6}(?:${v4}|:${v6seg}|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\n(?:${v6seg}:){5}(?::${v4}|(?::${v6seg}){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\n(?:${v6seg}:){4}(?:(?::${v6seg}){0,1}:${v4}|(?::${v6seg}){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\n(?:${v6seg}:){3}(?:(?::${v6seg}){0,2}:${v4}|(?::${v6seg}){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\n(?:${v6seg}:){2}(?:(?::${v6seg}){0,3}:${v4}|(?::${v6seg}){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\n(?:${v6seg}:){1}(?:(?::${v6seg}){0,4}:${v4}|(?::${v6seg}){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\n(?::(?:(?::${v6seg}){0,5}:${v4}|(?::${v6seg}){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n`\n    .replace(/\\s*\\/\\/.*$/gm, '')\n    .replace(/\\n/g, '')\n    .trim();\n\n  // Pre-compile only the exact regexes because adding a global flag make regexes stateful\n  const v46Exact = new RegExp(`(?:^${v4}$)|(?:^${v6}$)`);\n  const v4exact = new RegExp(`^${v4}$`);\n  const v6exact = new RegExp(`^${v6}$`);\n\n  const ip = options =>\n    options && options.exact\n      ? v46Exact\n      : new RegExp(\n          `(?:${b(options)}${v4}${b(options)})|(?:${b(options)}${v6}${b(\n            options,\n          )})`,\n          'g',\n        );\n\n  ip.v4 = (options?) =>\n    options && options.exact\n      ? v4exact\n      : new RegExp(`${b(options)}${v4}${b(options)}`, 'g');\n  ip.v6 = (options?) =>\n    options && options.exact\n      ? v6exact\n      : new RegExp(`${b(options)}${v6}${b(options)}`, 'g');\n\n  const protocol = `(?:(?:[a-z]+:)?//)`;\n  const auth = '(?:\\\\S+(?::\\\\S*)?@)?';\n  const ipv4 = ip.v4().source;\n  const ipv6 = ip.v6().source;\n  const host = '(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)';\n  const domain =\n    '(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*';\n  const tld = `(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))`;\n  const port = '(?::\\\\d{2,5})?';\n  const path = '(?:[/?#][^\\\\s\"]*)?';\n  const regex = `(?:${protocol}|www\\\\.)${auth}(?:localhost|${ipv4}|${ipv6}|${host}${domain}${tld})${port}${path}`;\n  urlReg = new RegExp(`(?:^${regex}$)`, 'i');\n  return urlReg;\n};\n", "import { ExecuteRule, Value } from '../interface';\nimport { format } from '../util';\nimport required from './required';\nimport getUrlRegex from './url';\n/* eslint max-len:0 */\n\nconst pattern = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+\\.)+[a-zA-Z\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}))$/,\n  // url: new RegExp(\n  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\n  //   'i',\n  // ),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,\n};\n\nconst types = {\n  integer(value: Value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  float(value: Value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array(value: Value) {\n    return Array.isArray(value);\n  },\n  regexp(value: Value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date(value: Value) {\n    return (\n      typeof value.getTime === 'function' &&\n      typeof value.getMonth === 'function' &&\n      typeof value.getYear === 'function' &&\n      !isNaN(value.getTime())\n    );\n  },\n  number(value: Value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object(value: Value) {\n    return typeof value === 'object' && !types.array(value);\n  },\n  method(value: Value) {\n    return typeof value === 'function';\n  },\n  email(value: Value) {\n    return (\n      typeof value === 'string' &&\n      value.length <= 320 &&\n      !!value.match(pattern.email)\n    );\n  },\n  url(value: Value) {\n    return (\n      typeof value === 'string' &&\n      value.length <= 2048 &&\n      !!value.match(getUrlRegex())\n    );\n  },\n  hex(value: Value) {\n    return typeof value === 'string' && !!value.match(pattern.hex);\n  },\n};\n\nconst type: ExecuteRule = (rule, value, source, errors, options) => {\n  if (rule.required && value === undefined) {\n    required(rule, value, source, errors, options);\n    return;\n  }\n  const custom = [\n    'integer',\n    'float',\n    'array',\n    'regexp',\n    'object',\n    'method',\n    'email',\n    'number',\n    'date',\n    'url',\n    'hex',\n  ];\n  const ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(\n        format(options.messages.types[ruleType], rule.fullField, rule.type),\n      );\n    }\n    // straight typeof check\n  } else if (ruleType && typeof value !== rule.type) {\n    errors.push(\n      format(options.messages.types[ruleType], rule.fullField, rule.type),\n    );\n  }\n};\n\nexport default type;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst range: ExecuteRule = (rule, value, source, errors, options) => {\n  const len = typeof rule.len === 'number';\n  const min = typeof rule.min === 'number';\n  const max = typeof rule.max === 'number';\n  // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n  const spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  let val = value;\n  let key = null;\n  const num = typeof value === 'number';\n  const str = typeof value === 'string';\n  const arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  }\n  // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(\n      format(options.messages[key].range, rule.fullField, rule.min, rule.max),\n    );\n  }\n};\n\nexport default range;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst ENUM = 'enum' as const;\n\nconst enumerable: ExecuteRule = (rule, value, source, errors, options) => {\n  rule[ENUM] = Array.isArray(rule[ENUM]) ? rule[ENUM] : [];\n  if (rule[ENUM].indexOf(value) === -1) {\n    errors.push(\n      format(options.messages[ENUM], rule.fullField, rule[ENUM].join(', ')),\n    );\n  }\n};\n\nexport default enumerable;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst pattern: ExecuteRule = (rule, value, source, errors, options) => {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(\n          format(\n            options.messages.pattern.mismatch,\n            rule.fullField,\n            value,\n            rule.pattern,\n          ),\n        );\n      }\n    } else if (typeof rule.pattern === 'string') {\n      const _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(\n          format(\n            options.messages.pattern.mismatch,\n            rule.fullField,\n            value,\n            rule.pattern,\n          ),\n        );\n      }\n    }\n  }\n};\n\nexport default pattern;\n", "import required from './required';\nimport whitespace from './whitespace';\nimport type from './type';\nimport range from './range';\nimport enumRule from './enum';\nimport pattern from './pattern';\n\nexport default {\n  required,\n  whitespace,\n  type,\n  range,\n  enum: enumRule,\n  pattern,\n};\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst string: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'string');\n    if (!isEmptyValue(value, 'string')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n      rules.pattern(rule, value, source, errors, options);\n      if (rule.whitespace === true) {\n        rules.whitespace(rule, value, source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\n\nexport default string;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst method: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default method;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst number: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (value === '') {\n      value = undefined;\n    }\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default number;\n", "import { isEmptyValue } from '../util';\nimport rules from '../rule';\nimport { ExecuteValidator } from '../interface';\n\nconst boolean: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default boolean;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst regexp: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default regexp;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst integer: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default integer;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst floatFn: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default floatFn;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule/index';\n\nconst array: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if ((value === undefined || value === null) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (value !== undefined && value !== null) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default array;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst object: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default object;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst ENUM = 'enum' as const;\n\nconst enumerable: ExecuteValidator = (\n  rule,\n  value,\n  callback,\n  source,\n  options,\n) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules[ENUM](rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default enumerable;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst pattern: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'string')) {\n      rules.pattern(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default pattern;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst date: ExecuteValidator = (rule, value, callback, source, options) => {\n  // console.log('integer rule called %j', rule);\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  // console.log('validate on %s value', value);\n  if (validate) {\n    if (isEmptyValue(value, 'date') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'date')) {\n      let dateObject;\n\n      if (value instanceof Date) {\n        dateObject = value;\n      } else {\n        dateObject = new Date(value);\n      }\n\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\n\nexport default date;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\n\nconst required: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const type = Array.isArray(value) ? 'array' : typeof value;\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n};\n\nexport default required;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst type: ExecuteValidator = (rule, value, callback, source, options) => {\n  const ruleType = rule.type;\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, ruleType) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, ruleType);\n    if (!isEmptyValue(value, ruleType)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default type;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst any: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n  }\n  callback(errors);\n};\n\nexport default any;\n", "import string from './string';\nimport method from './method';\nimport number from './number';\nimport boolean from './boolean';\nimport regexp from './regexp';\nimport integer from './integer';\nimport float from './float';\nimport array from './array';\nimport object from './object';\nimport enumValidator from './enum';\nimport pattern from './pattern';\nimport date from './date';\nimport required from './required';\nimport type from './type';\nimport any from './any';\n\nexport default {\n  string,\n  method,\n  number,\n  boolean,\n  regexp,\n  integer,\n  float,\n  array,\n  object,\n  enum: enumValidator,\n  pattern,\n  date,\n  url: type,\n  hex: type,\n  email: type,\n  required,\n  any,\n};\n", "import { InternalValidateMessages } from './interface';\n\nexport function newMessages(): InternalValidateMessages {\n  return {\n    default: 'Validation error on field %s',\n    required: '%s is required',\n    enum: '%s must be one of %s',\n    whitespace: '%s cannot be empty',\n    date: {\n      format: '%s date %s is invalid for format %s',\n      parse: '%s date could not be parsed, %s is invalid ',\n      invalid: '%s date %s is invalid',\n    },\n    types: {\n      string: '%s is not a %s',\n      method: '%s is not a %s (function)',\n      array: '%s is not an %s',\n      object: '%s is not an %s',\n      number: '%s is not a %s',\n      date: '%s is not a %s',\n      boolean: '%s is not a %s',\n      integer: '%s is not an %s',\n      float: '%s is not a %s',\n      regexp: '%s is not a valid %s',\n      email: '%s is not a valid %s',\n      url: '%s is not a valid %s',\n      hex: '%s is not a valid %s',\n    },\n    string: {\n      len: '%s must be exactly %s characters',\n      min: '%s must be at least %s characters',\n      max: '%s cannot be longer than %s characters',\n      range: '%s must be between %s and %s characters',\n    },\n    number: {\n      len: '%s must equal %s',\n      min: '%s cannot be less than %s',\n      max: '%s cannot be greater than %s',\n      range: '%s must be between %s and %s',\n    },\n    array: {\n      len: '%s must be exactly %s in length',\n      min: '%s cannot be less than %s in length',\n      max: '%s cannot be greater than %s in length',\n      range: '%s must be between %s and %s in length',\n    },\n    pattern: {\n      mismatch: '%s value %s does not match pattern %s',\n    },\n    clone() {\n      const cloned = JSON.parse(JSON.stringify(this));\n      cloned.clone = this.clone;\n      return cloned;\n    },\n  };\n}\n\nexport const messages = newMessages();\n", "import {\n  format,\n  complementError,\n  asyncMap,\n  warning,\n  deepMerge,\n  convertFieldsError,\n} from './util';\nimport validators from './validator/index';\nimport { messages as defaultMessages, newMessages } from './messages';\nimport {\n  InternalRuleItem,\n  InternalValidateMessages,\n  Rule,\n  RuleItem,\n  Rules,\n  ValidateCallback,\n  ValidateMessages,\n  ValidateOption,\n  Values,\n  RuleValuePackage,\n  ValidateError,\n  ValidateFieldsError,\n  SyncErrorType,\n  ValidateResult,\n} from './interface';\n\nexport * from './interface';\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\nclass Schema {\n  // ========================= Static =========================\n  static register = function register(type: string, validator) {\n    if (typeof validator !== 'function') {\n      throw new Error(\n        'Cannot register a validator by type, validator is not a function',\n      );\n    }\n    validators[type] = validator;\n  };\n\n  static warning = warning;\n\n  static messages = defaultMessages;\n\n  static validators = validators;\n\n  // ======================== Instance ========================\n  rules: Record<string, RuleItem[]> = null;\n  _messages: InternalValidateMessages = defaultMessages;\n\n  constructor(descriptor: Rules) {\n    this.define(descriptor);\n  }\n\n  define(rules: Rules) {\n    if (!rules) {\n      throw new Error('Cannot configure a schema with no rules');\n    }\n    if (typeof rules !== 'object' || Array.isArray(rules)) {\n      throw new Error('Rules must be an object');\n    }\n    this.rules = {};\n\n    Object.keys(rules).forEach(name => {\n      const item: Rule = rules[name];\n      this.rules[name] = Array.isArray(item) ? item : [item];\n    });\n  }\n\n  messages(messages?: ValidateMessages) {\n    if (messages) {\n      this._messages = deepMerge(newMessages(), messages);\n    }\n    return this._messages;\n  }\n\n  validate(\n    source: Values,\n    option?: ValidateOption,\n    callback?: ValidateCallback,\n  ): Promise<Values>;\n  validate(source: Values, callback: ValidateCallback): Promise<Values>;\n  validate(source: Values): Promise<Values>;\n\n  validate(source_: Values, o: any = {}, oc: any = () => {}): Promise<Values> {\n    let source: Values = source_;\n    let options: ValidateOption = o;\n    let callback: ValidateCallback = oc;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!this.rules || Object.keys(this.rules).length === 0) {\n      if (callback) {\n        callback(null, source);\n      }\n      return Promise.resolve(source);\n    }\n\n    function complete(results: (ValidateError | ValidateError[])[]) {\n      let errors: ValidateError[] = [];\n      let fields: ValidateFieldsError = {};\n\n      function add(e: ValidateError | ValidateError[]) {\n        if (Array.isArray(e)) {\n          errors = errors.concat(...e);\n        } else {\n          errors.push(e);\n        }\n      }\n\n      for (let i = 0; i < results.length; i++) {\n        add(results[i]);\n      }\n      if (!errors.length) {\n        callback(null, source);\n      } else {\n        fields = convertFieldsError(errors);\n        (callback as (\n          errors: ValidateError[],\n          fields: ValidateFieldsError,\n        ) => void)(errors, fields);\n      }\n    }\n\n    if (options.messages) {\n      let messages = this.messages();\n      if (messages === defaultMessages) {\n        messages = newMessages();\n      }\n      deepMerge(messages, options.messages);\n      options.messages = messages;\n    } else {\n      options.messages = this.messages();\n    }\n\n    const series: Record<string, RuleValuePackage[]> = {};\n    const keys = options.keys || Object.keys(this.rules);\n    keys.forEach(z => {\n      const arr = this.rules[z];\n      let value = source[z];\n      arr.forEach(r => {\n        let rule: InternalRuleItem = r;\n        if (typeof rule.transform === 'function') {\n          if (source === source_) {\n            source = { ...source };\n          }\n          value = source[z] = rule.transform(value);\n        }\n        if (typeof rule === 'function') {\n          rule = {\n            validator: rule,\n          };\n        } else {\n          rule = { ...rule };\n        }\n\n        // Fill validator. Skip if nothing need to validate\n        rule.validator = this.getValidationMethod(rule);\n        if (!rule.validator) {\n          return;\n        }\n\n        rule.field = z;\n        rule.fullField = rule.fullField || z;\n        rule.type = this.getType(rule);\n        series[z] = series[z] || [];\n        series[z].push({\n          rule,\n          value,\n          source,\n          field: z,\n        });\n      });\n    });\n    const errorFields = {};\n    return asyncMap(\n      series,\n      options,\n      (data, doIt) => {\n        const rule = data.rule;\n        let deep =\n          (rule.type === 'object' || rule.type === 'array') &&\n          (typeof rule.fields === 'object' ||\n            typeof rule.defaultField === 'object');\n        deep = deep && (rule.required || (!rule.required && data.value));\n        rule.field = data.field;\n\n        function addFullField(key: string, schema: RuleItem) {\n          return {\n            ...schema,\n            fullField: `${rule.fullField}.${key}`,\n            fullFields: rule.fullFields ? [...rule.fullFields, key] : [key],\n          };\n        }\n\n        function cb(e: SyncErrorType | SyncErrorType[] = []) {\n          let errorList = Array.isArray(e) ? e : [e];\n          if (!options.suppressWarning && errorList.length) {\n            Schema.warning('async-validator:', errorList);\n          }\n          if (errorList.length && rule.message !== undefined) {\n            errorList = [].concat(rule.message);\n          }\n\n          // Fill error info\n          let filledErrors = errorList.map(complementError(rule, source));\n\n          if (options.first && filledErrors.length) {\n            errorFields[rule.field] = 1;\n            return doIt(filledErrors);\n          }\n          if (!deep) {\n            doIt(filledErrors);\n          } else {\n            // if rule is required but the target object\n            // does not exist fail at the rule level and don't\n            // go deeper\n            if (rule.required && !data.value) {\n              if (rule.message !== undefined) {\n                filledErrors = []\n                  .concat(rule.message)\n                  .map(complementError(rule, source));\n              } else if (options.error) {\n                filledErrors = [\n                  options.error(\n                    rule,\n                    format(options.messages.required, rule.field),\n                  ),\n                ];\n              }\n              return doIt(filledErrors);\n            }\n\n            let fieldsSchema: Record<string, Rule> = {};\n            if (rule.defaultField) {\n              Object.keys(data.value).map(key => {\n                fieldsSchema[key] = rule.defaultField;\n              });\n            }\n            fieldsSchema = {\n              ...fieldsSchema,\n              ...data.rule.fields,\n            };\n\n            const paredFieldsSchema: Record<string, RuleItem[]> = {};\n\n            Object.keys(fieldsSchema).forEach(field => {\n              const fieldSchema = fieldsSchema[field];\n              const fieldSchemaList = Array.isArray(fieldSchema)\n                ? fieldSchema\n                : [fieldSchema];\n              paredFieldsSchema[field] = fieldSchemaList.map(\n                addFullField.bind(null, field),\n              );\n            });\n            const schema = new Schema(paredFieldsSchema);\n            schema.messages(options.messages);\n            if (data.rule.options) {\n              data.rule.options.messages = options.messages;\n              data.rule.options.error = options.error;\n            }\n            schema.validate(data.value, data.rule.options || options, errs => {\n              const finalErrors = [];\n              if (filledErrors && filledErrors.length) {\n                finalErrors.push(...filledErrors);\n              }\n              if (errs && errs.length) {\n                finalErrors.push(...errs);\n              }\n              doIt(finalErrors.length ? finalErrors : null);\n            });\n          }\n        }\n\n        let res: ValidateResult;\n        if (rule.asyncValidator) {\n          res = rule.asyncValidator(rule, data.value, cb, data.source, options);\n        } else if (rule.validator) {\n          try {\n            res = rule.validator(rule, data.value, cb, data.source, options);\n          } catch (error) {\n            console.error?.(error);\n            // rethrow to report error\n            if (!options.suppressValidatorError) {\n              setTimeout(() => {\n                throw error;\n              }, 0);\n            }\n            cb(error.message);\n          }\n          if (res === true) {\n            cb();\n          } else if (res === false) {\n            cb(\n              typeof rule.message === 'function'\n                ? rule.message(rule.fullField || rule.field)\n                : rule.message || `${rule.fullField || rule.field} fails`,\n            );\n          } else if (res instanceof Array) {\n            cb(res);\n          } else if (res instanceof Error) {\n            cb(res.message);\n          }\n        }\n        if (res && (res as Promise<void>).then) {\n          (res as Promise<void>).then(\n            () => cb(),\n            e => cb(e),\n          );\n        }\n      },\n      results => {\n        complete(results);\n      },\n      source,\n    );\n  }\n\n  getType(rule: InternalRuleItem) {\n    if (rule.type === undefined && rule.pattern instanceof RegExp) {\n      rule.type = 'pattern';\n    }\n    if (\n      typeof rule.validator !== 'function' &&\n      rule.type &&\n      !validators.hasOwnProperty(rule.type)\n    ) {\n      throw new Error(format('Unknown rule type %s', rule.type));\n    }\n    return rule.type || 'string';\n  }\n\n  getValidationMethod(rule: InternalRuleItem) {\n    if (typeof rule.validator === 'function') {\n      return rule.validator;\n    }\n    const keys = Object.keys(rule);\n    const messageIndex = keys.indexOf('message');\n    if (messageIndex !== -1) {\n      keys.splice(messageIndex, 1);\n    }\n    if (keys.length === 1 && keys[0] === 'required') {\n      return validators.required;\n    }\n    return validators[this.getType(rule)] || undefined;\n  }\n}\n\nexport default Schema;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAaA,IAAMA,YAAY,GAAG,UAArB;AAIO,IAAIC,OAAwD,GAAG,SAAAA,QAAA,EAAM,EAArE;;AAGP,IACE,OAAOC,OAAP,KAAmB,WAAnB,IACAA,OAAO,CAACC,GADR,IAEAD,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAFzB,IAGA,OAAOC,MAAP,KAAkB,WAHlB,IAIA,OAAOC,QAAP,KAAoB,WALtB,EAME;EACAL,OAAO,GAAG,SAAAA,QAACM,IAAD,EAAOC,MAAP,EAAkB;IAC1B,IACE,OAAOC,OAAP,KAAmB,WAAnB,IACAA,OAAO,CAACC,IADR,IAEA,OAAOC,0BAAP,KAAsC,WAHxC,EAIE;MACA,IAAIH,MAAM,CAACI,KAAP,CAAa,UAAAC,CAAC;QAAA,OAAI,OAAOA,CAAP,KAAa,QAAjB;MAAA,CAAd,CAAJ,EAA8C;QAC5CJ,OAAO,CAACC,IAAR,CAAaH,IAAb,EAAmBC,MAAnB;MACD;IACF;GATH;AAWD;AAEM,SAASM,kBAATA,CACLN,MADK,EAE4B;EACjC,IAAI,CAACA,MAAD,IAAW,CAACA,MAAM,CAACO,MAAvB,EAA+B,OAAO,IAAP;EAC/B,IAAMC,MAAM,GAAG,EAAf;EACAR,MAAM,CAACS,OAAP,CAAe,UAAAC,KAAK,EAAI;IACtB,IAAMC,KAAK,GAAGD,KAAK,CAACC,KAApB;IACAH,MAAM,CAACG,KAAD,CAAN,GAAgBH,MAAM,CAACG,KAAD,CAAN,IAAiB,EAAjC;IACAH,MAAM,CAACG,KAAD,CAAN,CAAcC,IAAd,CAAmBF,KAAnB;GAHF;EAKA,OAAOF,MAAP;AACD;AAEM,SAASK,MAATA,CACLC,QADK,EAGG;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAT,MAAA,EADLU,IACK,OAAAC,KAAA,CAAAH,IAAA,OAAAA,IAAA,WAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;IADLF,IACK,CAAAE,IAAA,QAAAH,SAAA,CAAAG,IAAA;EAAA;EACR,IAAIC,CAAC,GAAG,CAAR;EACA,IAAMC,GAAG,GAAGJ,IAAI,CAACV,MAAjB;EACA,IAAI,OAAOO,QAAP,KAAoB,UAAxB,EAAoC;IAClC,OAAOA,QAAQ,CAACQ,KAAT,CAAe,IAAf,EAAqBL,IAArB,CAAP;EACD;EACD,IAAI,OAAOH,QAAP,KAAoB,QAAxB,EAAkC;IAChC,IAAIS,GAAG,GAAGT,QAAQ,CAACU,OAAT,CAAiBhC,YAAjB,EAA+B,UAAAiC,CAAC,EAAI;MAC5C,IAAIA,CAAC,KAAK,IAAV,EAAgB;QACd,OAAO,GAAP;MACD;MACD,IAAIL,CAAC,IAAIC,GAAT,EAAc;QACZ,OAAOI,CAAP;MACD;MACD,QAAQA,CAAR;QACE,KAAK,IAAL;UACE,OAAOC,MAAM,CAACT,IAAI,CAACG,CAAC,EAAF,CAAL,CAAb;QACF,KAAK,IAAL;UACE,OAAQO,MAAM,CAACV,IAAI,CAACG,CAAC,EAAF,CAAL,CAAd;QACF,KAAK,IAAL;UACE,IAAI;YACF,OAAOQ,IAAI,CAACC,SAAL,CAAeZ,IAAI,CAACG,CAAC,EAAF,CAAnB,CAAP;WADF,CAEE,OAAOU,CAAP,EAAU;YACV,OAAO,YAAP;UACD;UACD;QACF;UACE,OAAOL,CAAP;MAbJ;IAeD,CAtBS,CAAV;IAuBA,OAAOF,GAAP;EACD;EACD,OAAOT,QAAP;AACD;AAED,SAASiB,kBAATA,CAA4BhC,IAA5B,EAA0C;EACxC,OACEA,IAAI,KAAK,QAAT,IACAA,IAAI,KAAK,KADT,IAEAA,IAAI,KAAK,KAFT,IAGAA,IAAI,KAAK,OAHT,IAIAA,IAAI,KAAK,MAJT,IAKAA,IAAI,KAAK,SANX;AAQD;AAEM,SAASiC,YAATA,CAAsBC,KAAtB,EAAoClC,IAApC,EAAmD;EACxD,IAAIkC,KAAK,KAAKC,SAAV,IAAuBD,KAAK,KAAK,IAArC,EAA2C;IACzC,OAAO,IAAP;EACD;EACD,IAAIlC,IAAI,KAAK,OAAT,IAAoBmB,KAAK,CAACiB,OAAN,CAAcF,KAAd,CAApB,IAA4C,CAACA,KAAK,CAAC1B,MAAvD,EAA+D;IAC7D,OAAO,IAAP;EACD;EACD,IAAIwB,kBAAkB,CAAChC,IAAD,CAAlB,IAA4B,OAAOkC,KAAP,KAAiB,QAA7C,IAAyD,CAACA,KAA9D,EAAqE;IACnE,OAAO,IAAP;EACD;EACD,OAAO,KAAP;AACD;AAMD,SAASG,kBAATA,CACEC,GADF,EAEEC,IAFF,EAGEC,QAHF,EAIE;EACA,IAAMC,OAAwB,GAAG,EAAjC;EACA,IAAIC,KAAK,GAAG,CAAZ;EACA,IAAMC,SAAS,GAAGL,GAAG,CAAC9B,MAAtB;EAEA,SAASoC,KAATA,CAAe3C,MAAf,EAAwC;IACtCwC,OAAO,CAAC5B,IAAR,CAAAU,KAAA,CAAAkB,OAAO,EAAUxC,MAAM,IAAI,EAApB,CAAP;IACAyC,KAAK;IACL,IAAIA,KAAK,KAAKC,SAAd,EAAyB;MACvBH,QAAQ,CAACC,OAAD,CAAR;IACD;EACF;EAEDH,GAAG,CAAC5B,OAAJ,CAAY,UAAAmC,CAAC,EAAI;IACfN,IAAI,CAACM,CAAD,EAAID,KAAJ,CAAJ;GADF;AAGD;AAED,SAASE,gBAATA,CACER,GADF,EAEEC,IAFF,EAGEC,QAHF,EAIE;EACA,IAAIO,KAAK,GAAG,CAAZ;EACA,IAAMJ,SAAS,GAAGL,GAAG,CAAC9B,MAAtB;EAEA,SAASwC,IAATA,CAAc/C,MAAd,EAAuC;IACrC,IAAIA,MAAM,IAAIA,MAAM,CAACO,MAArB,EAA6B;MAC3BgC,QAAQ,CAACvC,MAAD,CAAR;MACA;IACD;IACD,IAAMgD,QAAQ,GAAGF,KAAjB;IACAA,KAAK,GAAGA,KAAK,GAAG,CAAhB;IACA,IAAIE,QAAQ,GAAGN,SAAf,EAA0B;MACxBJ,IAAI,CAACD,GAAG,CAACW,QAAD,CAAJ,EAAgBD,IAAhB,CAAJ;IACD,CAFD,MAEO;MACLR,QAAQ,CAAC,EAAD,CAAR;IACD;EACF;EAEDQ,IAAI,CAAC,EAAD,CAAJ;AACD;AAED,SAASE,aAATA,CAAuBC,MAAvB,EAAmE;EACjE,IAAMC,GAAuB,GAAG,EAAhC;EACAC,MAAM,CAACC,IAAP,CAAYH,MAAZ,EAAoBzC,OAApB,CAA4B,UAAA6C,CAAC,EAAI;IAC/BH,GAAG,CAACvC,IAAJ,CAAAU,KAAA,CAAA6B,GAAG,EAAUD,MAAM,CAACI,CAAD,CAAN,IAAa,EAAvB,CAAH;GADF;EAGA,OAAOH,GAAP;AACD;AAED,IAAaI,oBAAb,0BAAAC,MAAA;EAAAC,cAAA,CAAAF,oBAAA,EAAAC,MAAA;EAIE,SACED,qBAAAvD,MADF,EAEEQ,MAFF,EAGE;IAAA,IAAAkD,KAAA;IACAA,KAAA,GAAAF,MAAA,CAAAG,IAAA,OAAM,wBAAN;IACAD,KAAK,CAAA1D,MAAL,GAAcA,MAAd;IACA0D,KAAK,CAAAlD,MAAL,GAAcA,MAAd;IAHA,OAAAkD,KAAA;EAID;EAXH,OAAAH,oBAAA;AAAA,eAAAK,gBAAA,CAA0CC,KAA1C;AAmBO,SAASC,QAATA,CACLZ,MADK,EAELa,MAFK,EAGLzB,IAHK,EAILC,QAJK,EAKLyB,MALK,EAMY;EACjB,IAAID,MAAM,CAACE,KAAX,EAAkB;IAChB,IAAMC,QAAO,GAAG,IAAIC,OAAJ,CAAoB,UAACC,OAAD,EAAUC,MAAV,EAAqB;MACvD,IAAMtB,IAAI,GAAG,SAAPA,IAAOA,CAAC/C,MAAD,EAA6B;QACxCuC,QAAQ,CAACvC,MAAD,CAAR;QACA,OAAOA,MAAM,CAACO,MAAP,GACH8D,MAAM,CAAC,IAAId,oBAAJ,CAAyBvD,MAAzB,EAAiCM,kBAAkB,CAACN,MAAD,CAAnD,CAAD,CADH,GAEHoE,OAAO,CAACJ,MAAD,CAFX;OAFF;MAMA,IAAMM,UAAU,GAAGrB,aAAa,CAACC,MAAD,CAAhC;MACAL,gBAAgB,CAACyB,UAAD,EAAahC,IAAb,EAAmBS,IAAnB,CAAhB;IACD,CATe,CAAhB;IAUAmB,QAAO,SAAP,CAAc,UAAA7D,CAAC;MAAA,OAAIA,CAAJ;KAAf;IACA,OAAO6D,QAAP;EACD;EACD,IAAMK,WAAW,GACfR,MAAM,CAACQ,WAAP,KAAuB,IAAvB,GACInB,MAAM,CAACC,IAAP,CAAYH,MAAZ,CADJ,GAEIa,MAAM,CAACQ,WAAP,IAAsB,EAH5B;EAKA,IAAMC,UAAU,GAAGpB,MAAM,CAACC,IAAP,CAAYH,MAAZ,CAAnB;EACA,IAAMuB,YAAY,GAAGD,UAAU,CAACjE,MAAhC;EACA,IAAIkC,KAAK,GAAG,CAAZ;EACA,IAAMD,OAAwB,GAAG,EAAjC;EACA,IAAMkC,OAAO,GAAG,IAAIP,OAAJ,CAAoB,UAACC,OAAD,EAAUC,MAAV,EAAqB;IACvD,IAAMtB,IAAI,GAAG,SAAPA,IAAOA,CAAC/C,MAAD,EAA6B;MACxCwC,OAAO,CAAC5B,IAAR,CAAaU,KAAb,CAAmBkB,OAAnB,EAA4BxC,MAA5B;MACAyC,KAAK;MACL,IAAIA,KAAK,KAAKgC,YAAd,EAA4B;QAC1BlC,QAAQ,CAACC,OAAD,CAAR;QACA,OAAOA,OAAO,CAACjC,MAAR,GACH8D,MAAM,CACJ,IAAId,oBAAJ,CAAyBf,OAAzB,EAAkClC,kBAAkB,CAACkC,OAAD,CAApD,CADI,CADH,GAIH4B,OAAO,CAACJ,MAAD,CAJX;MAKD;KAVH;IAYA,IAAI,CAACQ,UAAU,CAACjE,MAAhB,EAAwB;MACtBgC,QAAQ,CAACC,OAAD,CAAR;MACA4B,OAAO,CAACJ,MAAD,CAAP;IACD;IACDQ,UAAU,CAAC/D,OAAX,CAAmB,UAAAkE,GAAG,EAAI;MACxB,IAAMtC,GAAG,GAAGa,MAAM,CAACyB,GAAD,CAAlB;MACA,IAAIJ,WAAW,CAACK,OAAZ,CAAoBD,GAApB,CAA6B,MAAC,CAAlC,EAAqC;QACnC9B,gBAAgB,CAACR,GAAD,EAAMC,IAAN,EAAYS,IAAZ,CAAhB;MACD,CAFD,MAEO;QACLX,kBAAkB,CAACC,GAAD,EAAMC,IAAN,EAAYS,IAAZ,CAAlB;MACD;KANH;EAQD,CAzBe,CAAhB;EA0BA2B,OAAO,SAAP,CAAc,UAAArE,CAAC;IAAA,OAAIA,CAAJ;GAAf;EACA,OAAOqE,OAAP;AACD;AAED,SAASG,UAATA,CACEC,GADF,EAEwB;EACtB,OAAO,CAAC,EAAEA,GAAG,IAAKA,GAAD,CAAuBC,OAAvB,KAAmC7C,SAA5C,CAAR;AACD;AAED,SAAS8C,QAATA,CAAkB/C,KAAlB,EAAiCgD,IAAjC,EAAiD;EAC/C,IAAIC,CAAC,GAAGjD,KAAR;EACA,KAAK,IAAIb,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6D,IAAI,CAAC1E,MAAzB,EAAiCa,CAAC,EAAlC,EAAsC;IACpC,IAAI8D,CAAC,IAAIhD,SAAT,EAAoB;MAClB,OAAOgD,CAAP;IACD;IACDA,CAAC,GAAGA,CAAC,CAACD,IAAI,CAAC7D,CAAD,CAAL,CAAL;EACD;EACD,OAAO8D,CAAP;AACD;AAEM,SAASC,eAATA,CAAyBC,IAAzB,EAAiDpB,MAAjD,EAAiE;EACtE,OAAO,UAACqB,EAAD,EAAgE;IACrE,IAAIC,UAAJ;IACA,IAAIF,IAAI,CAACG,UAAT,EAAqB;MACnBD,UAAU,GAAGN,QAAQ,CAAChB,MAAD,EAASoB,IAAI,CAACG,UAAd,CAArB;IACD,CAFD,MAEO;MACLD,UAAU,GAAGtB,MAAM,CAAEqB,EAAD,CAAY1E,KAAZ,IAAqByE,IAAI,CAACI,SAA3B,CAAnB;IACD;IACD,IAAIX,UAAU,CAACQ,EAAD,CAAd,EAAoB;MAClBA,EAAE,CAAC1E,KAAH,GAAW0E,EAAE,CAAC1E,KAAH,IAAYyE,IAAI,CAACI,SAA5B;MACAH,EAAE,CAACC,UAAH,GAAgBA,UAAhB;MACA,OAAOD,EAAP;IACD;IACD,OAAO;MACLN,OAAO,EAAE,OAAOM,EAAP,KAAc,UAAd,GAA2BA,EAAE,EAA7B,GAAkCA,EADtC;MAELC,UAAU,EAAVA,UAFK;MAGL3E,KAAK,EAAI0E,EAAF,CAAmC1E,KAAnC,IAA4CyE,IAAI,CAACI;KAH1D;GAZF;AAkBD;AAEM,SAASC,SAATA,CAAqCC,MAArC,EAAgD1B,MAAhD,EAAuE;EAC5E,IAAIA,MAAJ,EAAY;IACV,KAAK,IAAM2B,CAAX,IAAgB3B,MAAhB,EAAwB;MACtB,IAAIA,MAAM,CAAC4B,cAAP,CAAsBD,CAAtB,CAAJ,EAA8B;QAC5B,IAAM1D,KAAK,GAAG+B,MAAM,CAAC2B,CAAD,CAApB;QACA,IAAI,OAAO1D,KAAP,KAAiB,QAAjB,IAA6B,OAAOyD,MAAM,CAACC,CAAD,CAAb,KAAqB,QAAtD,EAAgE;UAC9DD,MAAM,CAACC,CAAD,CAAN,GAAAE,QAAA,KACKH,MAAM,CAACC,CAAD,CADX,EAEK1D,KAFL;QAID,CALD,MAKO;UACLyD,MAAM,CAACC,CAAD,CAAN,GAAY1D,KAAZ;QACD;MACF;IACF;EACF;EACD,OAAOyD,MAAP;AACD;ACjTD,IAAMI,UAAqB,GAAG,SAAxBC,QAAwBA,CAACX,IAAD,EAAOnD,KAAP,EAAc+B,MAAd,EAAsBhE,MAAtB,EAA8BgG,OAA9B,EAAuCjG,IAAvC,EAAgD;EAC5E,IACEqF,IAAI,CAACW,QAAL,KACC,CAAC/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CAAD,IACCqB,YAAY,CAACC,KAAD,EAAQlC,IAAI,IAAIqF,IAAI,CAACrF,IAArB,CAFd,CADF,EAIE;IACAC,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiBF,QAAlB,EAA4BX,IAAI,CAACI,SAAjC,CAAlB;EACD;AACF,CARD;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMU,UAAuB,GAAG,SAA1BA,UAA0BA,CAACd,IAAD,EAAOnD,KAAP,EAAc+B,MAAd,EAAsBhE,MAAtB,EAA8BgG,OAA9B,EAA0C;EACxE,IAAI,QAAQG,IAAR,CAAalE,KAAb,KAAuBA,KAAK,KAAK,EAArC,EAAyC;IACvCjC,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiBC,UAAlB,EAA8Bd,IAAI,CAACI,SAAnC,CAAlB;EACD;AACF,CAJD;;ACdA;AACA,IAAIY,MAAJ;AAEA,IAAAC,WAAA,GAAe,SAAfA,YAAA,EAAqB;EACnB,IAAID,MAAJ,EAAY;IACV,OAAOA,MAAP;EACD;EAED,IAAME,IAAI,GAAG,cAAb;EACA,IAAMC,CAAC,GAAG,SAAJA,CAAIA,CAAAP,OAAO;IAAA,OACfA,OAAO,IAAIA,OAAO,CAACQ,iBAAnB,wBACuBF,IADvB,cACoCA,IADpC,mBAEI,EAHW;GAAjB;EAKA,IAAMG,EAAE,GACN,gGADF;EAGA,IAAMC,KAAK,GAAG,kBAAd;EACA,IAAMC,EAAE,GAAG,CAER,eAAAD,KAFQ,gBAEQA,KAFR,wFAGRA,KAHQ,gBAGQD,EAHR,GAGe,OAAAC,KAHf,GAIR,oHAAAA,KAJQ,iBAISD,EAJT,aAImBC,KAJnB,mHAKRA,KALQ,oBAKYA,KALZ,eAK2BD,EAL3B,GAKqC,UAAAC,KALrC,GAMR,8FAAAA,KANQ,oBAMYA,KANZ,eAM2BD,EAN3B,aAMqCC,KANrC,GAOR,8FAAAA,KAPQ,GAOY,iBAAAA,KAPZ,GAO2B,YAAAD,EAP3B,GAOqC,UAAAC,KAPrC,iGAQRA,KARQ,oBAQYA,KARZ,eAQ2BD,EAR3B,GAQqC,UAAAC,KARrC,GASA,sGAAAA,KATA,eASeD,EATf,aASyBC,KATzB,yLAYRlF,OAZQ,CAYA,cAZA,EAYgB,EAZhB,CAaR,CAAAA,OAbQ,CAaA,KAbA,EAaO,EAbP,EAcRoF,IAdQ,EAAX,CAfmB;;EAgCnB,IAAMC,QAAQ,GAAG,IAAIC,MAAJ,UAAkBL,EAAlB,eAA8BE,EAA9B,GAAjB;EACA,IAAMI,OAAO,GAAG,IAAID,MAAJ,OAAeL,EAAf,GAAhB;EACA,IAAMO,OAAO,GAAG,IAAIF,MAAJ,OAAeH,EAAf,GAAhB;EAEA,IAAMM,EAAE,GAAG,SAALA,EAAKA,CAAAjB,OAAO;IAAA,OAChBA,OAAO,IAAIA,OAAO,CAACkB,KAAnB,GACIL,QADJ,GAEI,IAAIC,MAAJ,SACQP,CAAC,CAACP,OAAD,CADT,GACqBS,EADrB,GAC0BF,CAAC,CAACP,OAAD,CAD3B,aAC4CO,CAAC,CAACP,OAAD,CAD7C,GACyDW,EADzD,GAC8DJ,CAAC,CAC3DP,OAD2D,CAD/D,QAIE,GAJF,CAHY;GAAlB;EAUAiB,EAAE,CAACR,EAAH,GAAQ,UAACT,OAAD;IAAA,OACNA,OAAO,IAAIA,OAAO,CAACkB,KAAnB,GACIH,OADJ,GAEI,IAAID,MAAJ,CAAc,KAAAP,CAAC,CAACP,OAAD,CAAf,GAA2BS,EAA3B,GAAgCF,CAAC,CAACP,OAAD,CAAjC,EAA8C,GAA9C,CAHE;GAAR;EAIAiB,EAAE,CAACN,EAAH,GAAQ,UAACX,OAAD;IAAA,OACNA,OAAO,IAAIA,OAAO,CAACkB,KAAnB,GACIF,OADJ,GAEI,IAAIF,MAAJ,CAAc,KAAAP,CAAC,CAACP,OAAD,CAAf,GAA2BW,EAA3B,GAAgCJ,CAAC,CAACP,OAAD,CAAjC,EAA8C,GAA9C,CAHE;GAAR;EAKA,IAAMmB,QAAQ,GAAd;EACA,IAAMC,IAAI,GAAG,sBAAb;EACA,IAAMC,IAAI,GAAGJ,EAAE,CAACR,EAAH,GAAQzC,MAArB;EACA,IAAMsD,IAAI,GAAGL,EAAE,CAACN,EAAH,GAAQ3C,MAArB;EACA,IAAMuD,IAAI,GAAG,+DAAb;EACA,IAAMC,MAAM,GACV,gEADF;EAEA,IAAMC,GAAG,GAAT;EACA,IAAMC,IAAI,GAAG,gBAAb;EACA,IAAMzC,IAAI,GAAG,oBAAb;EACA,IAAM0C,KAAK,GAAS,QAAAR,QAAT,gBAA4BC,IAA5B,qBAAgDC,IAAhD,GAAwD,MAAAC,IAAxD,GAAgE,MAAAC,IAAhE,GAAuEC,MAAvE,GAAgFC,GAAhF,GAAuF,MAAAC,IAAvF,GAA8FzC,IAAzG;EACAmB,MAAM,GAAG,IAAIU,MAAJ,UAAkBa,KAAlB,SAA6B,GAA7B,CAAT;EACA,OAAOvB,MAAP;AACD,CApED;;ACCA;;AAEA,IAAMwB,SAAO,GAAG;EACd;EACAC,KAAK,EAAE,sOAFO;EAGd;EACA;EACA;EACA;EACAC,GAAG,EAAE;AAPS,CAAhB;AAUA,IAAMC,KAAK,GAAG;EACZC,OADY,WAAAA,QACJ/F,KADI,EACU;IACpB,OAAO8F,KAAK,CAACE,MAAN,CAAahG,KAAb,KAAuBiG,QAAQ,CAACjG,KAAD,EAAQ,EAAR,CAAR,KAAwBA,KAAtD;GAFU;EAAA,kBAAAkG,MAINlG,KAJM,EAIQ;IAClB,OAAO8F,KAAK,CAACE,MAAN,CAAahG,KAAb,KAAuB,CAAC8F,KAAK,CAACC,OAAN,CAAc/F,KAAd,CAA/B;GALU;EAOZmG,KAPY,WAAAA,MAONnG,KAPM,EAOQ;IAClB,OAAOf,KAAK,CAACiB,OAAN,CAAcF,KAAd,CAAP;GARU;EAUZoG,MAVY,WAAAA,OAULpG,KAVK,EAUS;IACnB,IAAIA,KAAK,YAAY6E,MAArB,EAA6B;MAC3B,OAAO,IAAP;IACD;IACD,IAAI;MACF,OAAO,CAAC,CAAC,IAAIA,MAAJ,CAAW7E,KAAX,CAAT;KADF,CAEE,OAAO5B,CAAP,EAAU;MACV,OAAO,KAAP;IACD;GAlBS;EAoBZiI,IApBY,WAAAA,KAoBPrG,KApBO,EAoBO;IACjB,OACE,OAAOA,KAAK,CAACsG,OAAb,KAAyB,UAAzB,IACA,OAAOtG,KAAK,CAACuG,QAAb,KAA0B,UAD1B,IAEA,OAAOvG,KAAK,CAACwG,OAAb,KAAyB,UAFzB,IAGA,CAACC,KAAK,CAACzG,KAAK,CAACsG,OAAN,EAAD,CAJR;GArBU;EA4BZN,MA5BY,WAAAA,OA4BLhG,KA5BK,EA4BS;IACnB,IAAIyG,KAAK,CAACzG,KAAD,CAAT,EAAkB;MAChB,OAAO,KAAP;IACD;IACD,OAAO,OAAOA,KAAP,KAAiB,QAAxB;GAhCU;EAkCZ0G,MAlCY,WAAAA,OAkCL1G,KAlCK,EAkCS;IACnB,OAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAAC8F,KAAK,CAACK,KAAN,CAAYnG,KAAZ,CAArC;GAnCU;EAqCZ2G,MArCY,WAAAA,OAqCL3G,KArCK,EAqCS;IACnB,OAAO,OAAOA,KAAP,KAAiB,UAAxB;GAtCU;EAwCZ4F,KAxCY,WAAAA,MAwCN5F,KAxCM,EAwCQ;IAClB,OACE,OAAOA,KAAP,KAAiB,QAAjB,IACAA,KAAK,CAAC1B,MAAN,IAAgB,GADhB,IAEA,CAAC,CAAC0B,KAAK,CAAC4G,KAAN,CAAYjB,SAAO,CAACC,KAApB,CAHJ;GAzCU;EA+CZiB,GA/CY,WAAAA,IA+CR7G,KA/CQ,EA+CM;IAChB,OACE,OAAOA,KAAP,KAAiB,QAAjB,IACAA,KAAK,CAAC1B,MAAN,IAAgB,IADhB,IAEA,CAAC,CAAC0B,KAAK,CAAC4G,KAAN,CAAYxC,WAAW,EAAvB,CAHJ;GAhDU;EAsDZyB,GAtDY,WAAAA,IAsDR7F,KAtDQ,EAsDM;IAChB,OAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAAC,CAACA,KAAK,CAAC4G,KAAN,CAAYjB,SAAO,CAACE,GAApB,CAAtC;EACD;AAxDW,CAAd;AA2DA,IAAMiB,MAAiB,GAAG,SAApBhJ,IAAoBA,CAACqF,IAAD,EAAOnD,KAAP,EAAc+B,MAAd,EAAsBhE,MAAtB,EAA8BgG,OAA9B,EAA0C;EAClE,IAAIZ,IAAI,CAACW,QAAL,IAAiB9D,KAAK,KAAKC,SAA/B,EAA0C;IACxC4D,UAAQ,CAACV,IAAD,EAAOnD,KAAP,EAAc+B,MAAd,EAAsBhE,MAAtB,EAA8BgG,OAA9B,CAAR;IACA;EACD;EACD,IAAMgD,MAAM,GAAG,CACb,SADa,EAEb,OAFa,EAGb,OAHa,EAIb,QAJa,EAKb,QALa,EAMb,QANa,EAOb,OAPa,EAQb,QARa,EASb,MATa,EAUb,KAVa,EAWb,KAXa,CAAf;EAaA,IAAMC,QAAQ,GAAG7D,IAAI,CAACrF,IAAtB;EACA,IAAIiJ,MAAM,CAACpE,OAAP,CAAeqE,QAAf,CAA2B,IAAC,CAAhC,EAAmC;IACjC,IAAI,CAAClB,KAAK,CAACkB,QAAD,CAAL,CAAgBhH,KAAhB,CAAL,EAA6B;MAC3BjC,MAAM,CAACY,IAAP,CACEC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiB8B,KAAjB,CAAuBkB,QAAvB,CAAD,EAAmC7D,IAAI,CAACI,SAAxC,EAAmDJ,IAAI,CAACrF,IAAxD,CADR;IAGD,CALgC;GAAnC,MAOO,IAAIkJ,QAAQ,IAAI,OAAOhH,KAAP,KAAiBmD,IAAI,CAACrF,IAAtC,EAA4C;IACjDC,MAAM,CAACY,IAAP,CACEC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiB8B,KAAjB,CAAuBkB,QAAvB,CAAD,EAAmC7D,IAAI,CAACI,SAAxC,EAAmDJ,IAAI,CAACrF,IAAxD,CADR;EAGD;AACF,CA/BD;ACxEA,IAAMmJ,KAAkB,GAAG,SAArBA,KAAqBA,CAAC9D,IAAD,EAAOnD,KAAP,EAAc+B,MAAd,EAAsBhE,MAAtB,EAA8BgG,OAA9B,EAA0C;EACnE,IAAM3E,GAAG,GAAG,OAAO+D,IAAI,CAAC/D,GAAZ,KAAoB,QAAhC;EACA,IAAM8H,GAAG,GAAG,OAAO/D,IAAI,CAAC+D,GAAZ,KAAoB,QAAhC;EACA,IAAMC,GAAG,GAAG,OAAOhE,IAAI,CAACgE,GAAZ,KAAoB,QAAhC,CAHmE;;EAKnE,IAAMC,QAAQ,GAAG,iCAAjB;EACA,IAAIC,GAAG,GAAGrH,KAAV;EACA,IAAI0C,GAAG,GAAG,IAAV;EACA,IAAM4E,GAAG,GAAG,OAAOtH,KAAP,KAAiB,QAA7B;EACA,IAAMV,GAAG,GAAG,OAAOU,KAAP,KAAiB,QAA7B;EACA,IAAMI,GAAG,GAAGnB,KAAK,CAACiB,OAAN,CAAcF,KAAd,CAAZ;EACA,IAAIsH,GAAJ,EAAS;IACP5E,GAAG,GAAG,QAAN;GADF,MAEO,IAAIpD,GAAJ,EAAS;IACdoD,GAAG,GAAG,QAAN;GADK,MAEA,IAAItC,GAAJ,EAAS;IACdsC,GAAG,GAAG,OAAN;EACD,CAjBkE;EAmBnE;EACA;;EACA,IAAI,CAACA,GAAL,EAAU;IACR,OAAO,KAAP;EACD;EACD,IAAItC,GAAJ,EAAS;IACPiH,GAAG,GAAGrH,KAAK,CAAC1B,MAAZ;EACD;EACD,IAAIgB,GAAJ,EAAS;IACP;IACA+H,GAAG,GAAGrH,KAAK,CAACT,OAAN,CAAc6H,QAAd,EAAwB,GAAxB,EAA6B9I,MAAnC;EACD;EACD,IAAIc,GAAJ,EAAS;IACP,IAAIiI,GAAG,KAAKlE,IAAI,CAAC/D,GAAjB,EAAsB;MACpBrB,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiBtB,GAAjB,EAAsBtD,GAAvB,EAA4B+D,IAAI,CAACI,SAAjC,EAA4CJ,IAAI,CAAC/D,GAAjD,CAAlB;IACD;EACF,CAJD,MAIO,IAAI8H,GAAG,IAAI,CAACC,GAAR,IAAeE,GAAG,GAAGlE,IAAI,CAAC+D,GAA9B,EAAmC;IACxCnJ,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiBtB,GAAjB,EAAsBwE,GAAvB,EAA4B/D,IAAI,CAACI,SAAjC,EAA4CJ,IAAI,CAAC+D,GAAjD,CAAlB;EACD,CAFM,MAEA,IAAIC,GAAG,IAAI,CAACD,GAAR,IAAeG,GAAG,GAAGlE,IAAI,CAACgE,GAA9B,EAAmC;IACxCpJ,MAAM,CAACY,IAAP,CAAYC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiBtB,GAAjB,EAAsByE,GAAvB,EAA4BhE,IAAI,CAACI,SAAjC,EAA4CJ,IAAI,CAACgE,GAAjD,CAAlB;EACD,CAFM,MAEA,IAAID,GAAG,IAAIC,GAAP,KAAeE,GAAG,GAAGlE,IAAI,CAAC+D,GAAX,IAAkBG,GAAG,GAAGlE,IAAI,CAACgE,GAA5C,CAAJ,EAAsD;IAC3DpJ,MAAM,CAACY,IAAP,CACEC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiBtB,GAAjB,CAAsB,CAAAuE,KAAvB,EAA8B9D,IAAI,CAACI,SAAnC,EAA8CJ,IAAI,CAAC+D,GAAnD,EAAwD/D,IAAI,CAACgE,GAA7D,CADR;EAGD;AACF,CA5CD;ACAA,IAAMI,MAAI,GAAG,MAAb;AAEA,IAAMC,YAAuB,GAAG,SAA1BC,UAA0BA,CAACtE,IAAD,EAAOnD,KAAP,EAAc+B,MAAd,EAAsBhE,MAAtB,EAA8BgG,OAA9B,EAA0C;EACxEZ,IAAI,CAACoE,MAAD,CAAJ,GAAatI,KAAK,CAACiB,OAAN,CAAciD,IAAI,CAACoE,MAAD,CAAlB,CAA4B,GAAApE,IAAI,CAACoE,MAAD,CAAhC,GAAyC,EAAtD;EACA,IAAIpE,IAAI,CAACoE,MAAD,CAAJ,CAAW5E,OAAX,CAAmB3C,KAAnB,MAA8B,CAAC,CAAnC,EAAsC;IACpCjC,MAAM,CAACY,IAAP,CACEC,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiBuD,MAAjB,CAAD,EAAyBpE,IAAI,CAACI,SAA9B,EAAyCJ,IAAI,CAACoE,MAAD,CAAJ,CAAWG,IAAX,CAAgB,IAAhB,CAAzC,CADR;EAGD;AACF,CAPD;ACFA,IAAMC,SAAoB,GAAG,SAAvBC,OAAuBA,CAACzE,IAAD,EAAOnD,KAAP,EAAc+B,MAAd,EAAsBhE,MAAtB,EAA8BgG,OAA9B,EAA0C;EACrE,IAAIZ,IAAI,CAACyE,OAAT,EAAkB;IAChB,IAAIzE,IAAI,CAACyE,OAAL,YAAwB/C,MAA5B,EAAoC;MAClC;MACA;MACA;MACA1B,IAAI,CAACyE,OAAL,CAAaC,SAAb,GAAyB,CAAzB;MACA,IAAI,CAAC1E,IAAI,CAACyE,OAAL,CAAa1D,IAAb,CAAkBlE,KAAlB,CAAL,EAA+B;QAC7BjC,MAAM,CAACY,IAAP,CACEC,MAAM,CACJmF,OAAO,CAACC,QAAR,CAAiB4D,OAAjB,CAAyBE,QADrB,EAEJ3E,IAAI,CAACI,SAFD,EAGJvD,KAHI,EAIJmD,IAAI,CAACyE,OAJD,CADR;MAQD;KAdH,MAeO,IAAI,OAAOzE,IAAI,CAACyE,OAAZ,KAAwB,QAA5B,EAAsC;MAC3C,IAAMG,QAAQ,GAAG,IAAIlD,MAAJ,CAAW1B,IAAI,CAACyE,OAAhB,CAAjB;MACA,IAAI,CAACG,QAAQ,CAAC7D,IAAT,CAAclE,KAAd,CAAL,EAA2B;QACzBjC,MAAM,CAACY,IAAP,CACEC,MAAM,CACJmF,OAAO,CAACC,QAAR,CAAiB4D,OAAjB,CAAyBE,QADrB,EAEJ3E,IAAI,CAACI,SAFD,EAGJvD,KAHI,EAIJmD,IAAI,CAACyE,OAJD,CADR;MAQD;IACF;EACF;AACF,CA/BD;ACIA,IAAAI,KAAA,GAAe;EACblE,QAAQ,EAARD,UADa;EAEbI,UAAU,EAAVA,UAFa;EAGbnG,IAAI,EAAJgJ,MAHa;EAIbG,KAAK,EAALA,KAJa;EAKb,QAAMO,YALO;EAMbI,OAAO,EAAPD;AANa,CAAf;ACHA,IAAMM,MAAwB,GAAG,SAA3BA,MAA2BA,CAAC9E,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC3E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,EAAQ,QAAR,CAAZ,IAAiC,CAACmD,IAAI,CAACW,QAA3C,EAAqD;MACnD,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C,EAAqD,QAArD;IACA,IAAI,CAAChE,YAAY,CAACC,KAAD,EAAQ,QAAR,CAAjB,EAAoC;MAClCgI,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;MACAiE,KAAK,CAACf,KAAN,CAAY9D,IAAZ,EAAkBnD,KAAlB,EAAyB+B,MAAzB,EAAiChE,MAAjC,EAAyCgG,OAAzC;MACAiE,KAAK,CAACJ,OAAN,CAAczE,IAAd,EAAoBnD,KAApB,EAA2B+B,MAA3B,EAAmChE,MAAnC,EAA2CgG,OAA3C;MACA,IAAIZ,IAAI,CAACc,UAAL,KAAoB,IAAxB,EAA8B;QAC5B+D,KAAK,CAAC/D,UAAN,CAAiBd,IAAjB,EAAuBnD,KAAvB,EAA8B+B,MAA9B,EAAsChE,MAAtC,EAA8CgG,OAA9C;MACD;IACF;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAnBD;ACAA,IAAM4I,MAAwB,GAAG,SAA3BA,MAA2BA,CAACxD,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC3E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI/D,KAAK,KAAKC,SAAd,EAAyB;MACvB+H,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAdD;ACAA,IAAMiI,MAAwB,GAAG,SAA3BA,MAA2BA,CAAC7C,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC3E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAIlI,KAAK,KAAK,EAAd,EAAkB;MAChBA,KAAK,GAAGC,SAAR;IACD;IACD,IAAIF,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI/D,KAAK,KAAKC,SAAd,EAAyB;MACvB+H,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;MACAiE,KAAK,CAACf,KAAN,CAAY9D,IAAZ,EAAkBnD,KAAlB,EAAyB+B,MAAzB,EAAiChE,MAAjC,EAAyCgG,OAAzC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAlBD;ACAA,IAAMoK,QAAyB,GAAG,SAA5BA,QAA4BC,CAACjF,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC5E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI/D,KAAK,KAAKC,SAAd,EAAyB;MACvB+H,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAdD;ACAA,IAAMqI,MAAwB,GAAG,SAA3BA,MAA2BA,CAACjD,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC3E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI,CAAChE,YAAY,CAACC,KAAD,CAAjB,EAA0B;MACxBgI,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAdD;ACAA,IAAMgI,OAAyB,GAAG,SAA5BA,OAA4BA,CAAC5C,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC5E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI/D,KAAK,KAAKC,SAAd,EAAyB;MACvB+H,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;MACAiE,KAAK,CAACf,KAAN,CAAY9D,IAAZ,EAAkBnD,KAAlB,EAAyB+B,MAAzB,EAAiChE,MAAjC,EAAyCgG,OAAzC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAfD;ACAA,IAAMsK,OAAyB,GAAG,SAA5BA,OAA4BA,CAAClF,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC5E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI/D,KAAK,KAAKC,SAAd,EAAyB;MACvB+H,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;MACAiE,KAAK,CAACf,KAAN,CAAY9D,IAAZ,EAAkBnD,KAAlB,EAAyB+B,MAAzB,EAAiChE,MAAjC,EAAyCgG,OAAzC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAfD;ACDA,IAAMoI,KAAuB,GAAG,SAA1BA,KAA0BA,CAAChD,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC1E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAI,CAAClI,KAAK,KAAKC,SAAV,IAAuBD,KAAK,KAAK,IAAlC,KAA2C,CAACmD,IAAI,CAACW,QAArD,EAA+D;MAC7D,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C,EAAqD,OAArD;IACA,IAAI/D,KAAK,KAAKC,SAAV,IAAuBD,KAAK,KAAK,IAArC,EAA2C;MACzCgI,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;MACAiE,KAAK,CAACf,KAAN,CAAY9D,IAAZ,EAAkBnD,KAAlB,EAAyB+B,MAAzB,EAAiChE,MAAjC,EAAyCgG,OAAzC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAfD;ACCA,IAAM2I,MAAwB,GAAG,SAA3BA,MAA2BA,CAACvD,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC3E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI/D,KAAK,KAAKC,SAAd,EAAyB;MACvB+H,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAdD;ACAA,IAAMuK,IAAI,GAAG,MAAb;AAEA,IAAMb,UAA4B,GAAG,SAA/BA,UAA+BA,CACnCtE,IADmC,EAEnCnD,KAFmC,EAGnCM,QAHmC,EAInCyB,MAJmC,EAKnCgC,OALmC,EAMhC;EACH,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI/D,KAAK,KAAKC,SAAd,EAAyB;MACvB+H,KAAK,CAACM,IAAD,CAAL,CAAYnF,IAAZ,EAAkBnD,KAAlB,EAAyB+B,MAAzB,EAAiChE,MAAjC,EAAyCgG,OAAzC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CApBD;ACFA,IAAM6J,OAAyB,GAAG,SAA5BA,OAA4BA,CAACzE,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC5E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,EAAQ,QAAR,CAAZ,IAAiC,CAACmD,IAAI,CAACW,QAA3C,EAAqD;MACnD,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI,CAAChE,YAAY,CAACC,KAAD,EAAQ,QAAR,CAAjB,EAAoC;MAClCgI,KAAK,CAACJ,OAAN,CAAczE,IAAd,EAAoBnD,KAApB,EAA2B+B,MAA3B,EAAmChE,MAAnC,EAA2CgG,OAA3C;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAdD;ACAA,IAAMsI,IAAsB,GAAG,SAAzBA,IAAyBA,CAAClD,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EACzE;EACA,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC,CAHyE;;EAMzE,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,EAAQ,MAAR,CAAZ,IAA+B,CAACmD,IAAI,CAACW,QAAzC,EAAmD;MACjD,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;IACA,IAAI,CAAChE,YAAY,CAACC,KAAD,EAAQ,MAAR,CAAjB,EAAkC;MAChC,IAAIuI,UAAJ;MAEA,IAAIvI,KAAK,YAAYwI,IAArB,EAA2B;QACzBD,UAAU,GAAGvI,KAAb;MACD,CAFD,MAEO;QACLuI,UAAU,GAAG,IAAIC,IAAJ,CAASxI,KAAT,CAAb;MACD;MAEDgI,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBoF,UAAjB,EAA6BxG,MAA7B,EAAqChE,MAArC,EAA6CgG,OAA7C;MACA,IAAIwE,UAAJ,EAAgB;QACdP,KAAK,CAACf,KAAN,CAAY9D,IAAZ,EAAkBoF,UAAU,CAACjC,OAAX,EAAlB,EAAwCvE,MAAxC,EAAgDhE,MAAhD,EAAwDgG,OAAxD;MACD;IACF;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CA3BD;ACDA,IAAM+F,QAA0B,GAAG,SAA7BA,QAA6BA,CAACX,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EAC7E,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMD,IAAI,GAAGmB,KAAK,CAACiB,OAAN,CAAcF,KAAd,CAAuB,UAAvB,GAAiC,OAAOA,KAArD;EACAgI,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C,EAAqDjG,IAArD;EACAwC,QAAQ,CAACvC,MAAD,CAAR;AACD,CALD;ACCA,IAAMD,IAAsB,GAAG,SAAzBA,IAAyBA,CAACqF,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EACzE,IAAMiD,QAAQ,GAAG7D,IAAI,CAACrF,IAAtB;EACA,IAAMC,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,EAAQgH,QAAR,CAAZ,IAAiC,CAAC7D,IAAI,CAACW,QAA3C,EAAqD;MACnD,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C,EAAqDiD,QAArD;IACA,IAAI,CAACjH,YAAY,CAACC,KAAD,EAAQgH,QAAR,CAAjB,EAAoC;MAClCgB,KAAK,CAAClK,IAAN,CAAWqF,IAAX,EAAiBnD,KAAjB,EAAwB+B,MAAxB,EAAgChE,MAAhC,EAAwCgG,OAAxC;IACD;EACF;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAfD;ACAA,IAAM0K,GAAqB,GAAG,SAAxBA,GAAwBA,CAACtF,IAAD,EAAOnD,KAAP,EAAcM,QAAd,EAAwByB,MAAxB,EAAgCgC,OAAhC,EAA4C;EACxE,IAAMhG,MAAgB,GAAG,EAAzB;EACA,IAAMmK,QAAQ,GACZ/E,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB/B,MAAM,CAAC4B,cAAP,CAAsBR,IAAI,CAACzE,KAA3B,CADtC;EAEA,IAAIwJ,QAAJ,EAAc;IACZ,IAAInI,YAAY,CAACC,KAAD,CAAZ,IAAuB,CAACmD,IAAI,CAACW,QAAjC,EAA2C;MACzC,OAAOxD,QAAQ,EAAf;IACD;IACD0H,KAAK,CAAClE,QAAN,CAAeX,IAAf,EAAqBnD,KAArB,EAA4B+B,MAA5B,EAAoChE,MAApC,EAA4CgG,OAA5C;EACD;EACDzD,QAAQ,CAACvC,MAAD,CAAR;AACD,CAXD;ACYA,IAAA2K,UAAA,GAAe;EACbT,MAAM,EAANA,MADa;EAEbtB,MAAM,EAANA,MAFa;EAGbX,MAAM,EAANA,MAHa;EAIb,WAAAmC,QAJa;EAKb/B,MAAM,EAANA,MALa;EAMbL,OAAO,EAAPA,OANa;EAOb,SAAAsC,OAPa;EAQblC,KAAK,EAALA,KARa;EASbO,MAAM,EAANA,MATa;EAUb,QAAMe,UAVO;EAWbG,OAAO,EAAPA,OAXa;EAYbvB,IAAI,EAAJA,IAZa;EAabQ,GAAG,EAAE/I,IAbQ;EAcb+H,GAAG,EAAE/H,IAdQ;EAeb8H,KAAK,EAAE9H,IAfM;EAgBbgG,QAAQ,EAARA,QAhBa;EAiBb2E,GAAG,EAAHA;AAjBa,CAAf;ACdO,SAASE,WAATA,CAAA,EAAiD;EACtD,OAAO;IACL,WAAS,8BADJ;IAEL7E,QAAQ,EAAE,gBAFL;IAGL,QAAM,sBAHD;IAILG,UAAU,EAAE,oBAJP;IAKLoC,IAAI,EAAE;MACJzH,MAAM,EAAE,qCADJ;MAEJgK,KAAK,EAAE,6CAFH;MAGJC,OAAO,EAAE;KARN;IAUL/C,KAAK,EAAE;MACLmC,MAAM,EAAE,gBADH;MAELtB,MAAM,EAAE,2BAFH;MAGLR,KAAK,EAAE,iBAHF;MAILO,MAAM,EAAE,iBAJH;MAKLV,MAAM,EAAE,gBALH;MAMLK,IAAI,EAAE,gBAND;MAOL,WAAS,gBAPJ;MAQLN,OAAO,EAAE,iBARJ;MASL,SAAO,gBATF;MAULK,MAAM,EAAE,sBAVH;MAWLR,KAAK,EAAE,sBAXF;MAYLiB,GAAG,EAAE,sBAZA;MAaLhB,GAAG,EAAE;KAvBF;IAyBLoC,MAAM,EAAE;MACN7I,GAAG,EAAE,kCADC;MAEN8H,GAAG,EAAE,mCAFC;MAGNC,GAAG,EAAE,wCAHC;MAINF,KAAK,EAAE;KA7BJ;IA+BLjB,MAAM,EAAE;MACN5G,GAAG,EAAE,kBADC;MAEN8H,GAAG,EAAE,2BAFC;MAGNC,GAAG,EAAE,8BAHC;MAINF,KAAK,EAAE;KAnCJ;IAqCLd,KAAK,EAAE;MACL/G,GAAG,EAAE,iCADA;MAEL8H,GAAG,EAAE,qCAFA;MAGLC,GAAG,EAAE,wCAHA;MAILF,KAAK,EAAE;KAzCJ;IA2CLW,OAAO,EAAE;MACPE,QAAQ,EAAE;KA5CP;IA8CLgB,KA9CK,EA8CG,SAAAA,MAAA;MACN,IAAMC,MAAM,GAAGpJ,IAAI,CAACiJ,KAAL,CAAWjJ,IAAI,CAACC,SAAL,CAAe,IAAf,CAAX,CAAf;MACAmJ,MAAM,CAACD,KAAP,GAAe,KAAKA,KAApB;MACA,OAAOC,MAAP;IACD;GAlDH;AAoDD;AAEM,IAAM/E,QAAQ,GAAG2E,WAAW,EAA5B;;AC5BP;AACA;AACA;AACA;AACA;AACA;;IACMK,MAAA;EACJ;EAgBA;EAIA,SAAAA,OAAYC,UAAZ,EAA+B;IAAA,IAH/B,CAAAjB,KAG+B,GAHK,IAGL;IAAA,IAF/B,CAAAkB,SAE+B,GAFOlF,QAEP;IAC7B,IAAK,CAAAmF,MAAL,CAAYF,UAAZ;EACD;;SAEDE,MAAA,YAAOA,OAAAnB,KAAP,EAAqB;IAAA,IAAAvG,KAAA;IACnB,IAAI,CAACuG,KAAL,EAAY;MACV,MAAM,IAAIpG,KAAJ,CAAU,yCAAV,CAAN;IACD;IACD,IAAI,OAAOoG,KAAP,KAAiB,QAAjB,IAA6B/I,KAAK,CAACiB,OAAN,CAAc8H,KAAd,CAAjC,EAAuD;MACrD,MAAM,IAAIpG,KAAJ,CAAU,yBAAV,CAAN;IACD;IACD,IAAK,CAAAoG,KAAL,GAAa,EAAb;IAEA7G,MAAM,CAACC,IAAP,CAAY4G,KAAZ,EAAmBxJ,OAAnB,CAA2B,UAAA4K,IAAI,EAAI;MACjC,IAAMC,IAAU,GAAGrB,KAAK,CAACoB,IAAD,CAAxB;MACA3H,KAAI,CAACuG,KAAL,CAAWoB,IAAX,IAAmBnK,KAAK,CAACiB,OAAN,CAAcmJ,IAAd,CAAsB,GAAAA,IAAtB,GAA6B,CAACA,IAAD,CAAhD;KAFF;;SAMFrF,QAAA,YAASA,SAAAkF,SAAT,EAAsC;IACpC,IAAIA,SAAJ,EAAc;MACZ,IAAK,CAAAA,SAAL,GAAiB1F,SAAS,CAACmF,WAAW,EAAZ,EAAgBO,SAAhB,CAA1B;IACD;IACD,OAAO,KAAKA,SAAZ;;EAWFI,MAAA,CAAApB,QAAA,YAASA,SAAAqB,OAAT,EAA0BC,CAA1B,EAAuCC,EAAvC,EAA4E;IAAA,IAAAC,MAAA;IAAA,IAAlDF,CAAkD;MAAlDA,CAAkD,GAAzC,EAAyC;IAAA;IAAA,IAArCC,EAAqC;MAArCA,EAAqC,GAA3B,SAAMA,GAAA,IAAqB;IAAA;IAC1E,IAAI1H,MAAc,GAAGwH,OAArB;IACA,IAAIxF,OAAuB,GAAGyF,CAA9B;IACA,IAAIlJ,QAA0B,GAAGmJ,EAAjC;IACA,IAAI,OAAO1F,OAAP,KAAmB,UAAvB,EAAmC;MACjCzD,QAAQ,GAAGyD,OAAX;MACAA,OAAO,GAAG,EAAV;IACD;IACD,IAAI,CAAC,KAAKiE,KAAN,IAAe7G,MAAM,CAACC,IAAP,CAAY,KAAK4G,KAAjB,EAAwB1J,MAAxB,KAAmC,CAAtD,EAAyD;MACvD,IAAIgC,QAAJ,EAAc;QACZA,QAAQ,CAAC,IAAD,EAAOyB,MAAP,CAAR;MACD;MACD,OAAOG,OAAO,CAACC,OAAR,CAAgBJ,MAAhB,CAAP;IACD;IAED,SAAS4H,QAATA,CAAkBpJ,OAAlB,EAAgE;MAC9D,IAAIxC,MAAuB,GAAG,EAA9B;MACA,IAAIQ,MAA2B,GAAG,EAAlC;MAEA,SAASqL,GAATA,CAAaxL,CAAb,EAAiD;QAC/C,IAAIa,KAAK,CAACiB,OAAN,CAAc9B,CAAd,CAAJ,EAAsB;UAAA,IAAAyL,OAAA;UACpB9L,MAAM,GAAG,CAAA8L,OAAA,GAAA9L,MAAM,EAAC+L,MAAP,CAAAzK,KAAA,CAAAwK,OAAA,EAAiBzL,CAAjB,CAAT;QACD,CAFD,MAEO;UACLL,MAAM,CAACY,IAAP,CAAYP,CAAZ;QACD;MACF;MAED,KAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoB,OAAO,CAACjC,MAA5B,EAAoCa,CAAC,EAArC,EAAyC;QACvCyK,GAAG,CAACrJ,OAAO,CAACpB,CAAD,CAAR,CAAH;MACD;MACD,IAAI,CAACpB,MAAM,CAACO,MAAZ,EAAoB;QAClBgC,QAAQ,CAAC,IAAD,EAAOyB,MAAP,CAAR;MACD,CAFD,MAEO;QACLxD,MAAM,GAAGF,kBAAkB,CAACN,MAAD,CAA3B;QACCuC,QAAD,CAGWvC,MAHX,EAGmBQ,MAHnB;MAID;IACF;IAED,IAAIwF,OAAO,CAACC,QAAZ,EAAsB;MACpB,IAAI+F,UAAQ,GAAG,IAAK,CAAA/F,QAAL,EAAf;MACA,IAAI+F,UAAQ,KAAK/F,QAAjB,EAAkC;QAChC+F,UAAQ,GAAGpB,WAAW,EAAtB;MACD;MACDnF,SAAS,CAACuG,UAAD,EAAWhG,OAAO,CAACC,QAAnB,CAAT;MACAD,OAAO,CAACC,QAAR,GAAmB+F,UAAnB;IACD,CAPD,MAOO;MACLhG,OAAO,CAACC,QAAR,GAAmB,KAAKA,QAAL,EAAnB;IACD;IAED,IAAMgG,MAA0C,GAAG,EAAnD;IACA,IAAM5I,IAAI,GAAG2C,OAAO,CAAC3C,IAAR,IAAgBD,MAAM,CAACC,IAAP,CAAY,IAAK,CAAA4G,KAAjB,CAA7B;IACA5G,IAAI,CAAC5C,OAAL,CAAa,UAAAyL,CAAC,EAAI;MAChB,IAAM7J,GAAG,GAAGsJ,MAAI,CAAC1B,KAAL,CAAWiC,CAAX,CAAZ;MACA,IAAIjK,KAAK,GAAG+B,MAAM,CAACkI,CAAD,CAAlB;MACA7J,GAAG,CAAC5B,OAAJ,CAAY,UAAA0L,CAAC,EAAI;QACf,IAAI/G,IAAsB,GAAG+G,CAA7B;QACA,IAAI,OAAO/G,IAAI,CAACgH,SAAZ,KAA0B,UAA9B,EAA0C;UACxC,IAAIpI,MAAM,KAAKwH,OAAf,EAAwB;YACtBxH,MAAM,GAAA6B,QAAA,KAAQ7B,MAAR,CAAN;UACD;UACD/B,KAAK,GAAG+B,MAAM,CAACkI,CAAD,CAAN,GAAY9G,IAAI,CAACgH,SAAL,CAAenK,KAAf,CAApB;QACD;QACD,IAAI,OAAOmD,IAAP,KAAgB,UAApB,EAAgC;UAC9BA,IAAI,GAAG;YACLiH,SAAS,EAAEjH;WADb;QAGD,CAJD,MAIO;UACLA,IAAI,GAAAS,QAAA,KAAQT,IAAR,CAAJ;QACD,CAdc;;QAiBfA,IAAI,CAACiH,SAAL,GAAiBV,MAAI,CAACW,mBAAL,CAAyBlH,IAAzB,CAAjB;QACA,IAAI,CAACA,IAAI,CAACiH,SAAV,EAAqB;UACnB;QACD;QAEDjH,IAAI,CAACzE,KAAL,GAAauL,CAAb;QACA9G,IAAI,CAACI,SAAL,GAAiBJ,IAAI,CAACI,SAAL,IAAkB0G,CAAnC;QACA9G,IAAI,CAACrF,IAAL,GAAY4L,MAAI,CAACY,OAAL,CAAanH,IAAb,CAAZ;QACA6G,MAAM,CAACC,CAAD,CAAN,GAAYD,MAAM,CAACC,CAAD,CAAN,IAAa,EAAzB;QACAD,MAAM,CAACC,CAAD,CAAN,CAAUtL,IAAV,CAAe;UACbwE,IAAI,EAAJA,IADa;UAEbnD,KAAK,EAALA,KAFa;UAGb+B,MAAM,EAANA,MAHa;UAIbrD,KAAK,EAAEuL;SAJT;OA1BF;KAHF;IAqCA,IAAMM,WAAW,GAAG,EAApB;IACA,OAAO1I,QAAQ,CACbmI,MADa,EAEbjG,OAFa,EAGb,UAACyG,IAAD,EAAOC,IAAP,EAAgB;MACd,IAAMtH,IAAI,GAAGqH,IAAI,CAACrH,IAAlB;MACA,IAAIuH,IAAI,GACN,CAACvH,IAAI,CAACrF,IAAL,KAAc,QAAd,IAA0BqF,IAAI,CAACrF,IAAL,KAAc,OAAzC,MACC,OAAOqF,IAAI,CAAC5E,MAAZ,KAAuB,QAAvB,IACC,OAAO4E,IAAI,CAACwH,YAAZ,KAA6B,QAF/B,CADF;MAIAD,IAAI,GAAGA,IAAI,KAAKvH,IAAI,CAACW,QAAL,IAAkB,CAACX,IAAI,CAACW,QAAN,IAAkB0G,IAAI,CAACxK,KAA9C,CAAX;MACAmD,IAAI,CAACzE,KAAL,GAAa8L,IAAI,CAAC9L,KAAlB;MAEA,SAASkM,YAATA,CAAsBlI,GAAtB,EAAmCmI,MAAnC,EAAqD;QACnD,OAAAjH,QAAA,KACKiH,MADL;UAEEtH,SAAS,EAAKJ,IAAI,CAACI,SAAV,SAAuBb,GAFlC;UAGEY,UAAU,EAAEH,IAAI,CAACG,UAAL,GAAsB,GAAAwG,MAAA,CAAA3G,IAAI,CAACG,UAA3B,EAAuC,CAAAZ,GAAvC,CAA8C,KAACA,GAAD;QAH5D;MAKD;MAED,SAASoI,EAATA,CAAY1M,CAAZ,EAAqD;QAAA,IAAzCA,CAAyC;UAAzCA,CAAyC,GAAJ,EAAI;QAAA;QACnD,IAAI2M,SAAS,GAAG9L,KAAK,CAACiB,OAAN,CAAc9B,CAAd,IAAmBA,CAAnB,GAAuB,CAACA,CAAD,CAAvC;QACA,IAAI,CAAC2F,OAAO,CAACiH,eAAT,IAA4BD,SAAS,CAACzM,MAA1C,EAAkD;UAChD0K,MAAM,CAACxL,OAAP,CAAe,kBAAf,EAAmCuN,SAAnC;QACD;QACD,IAAIA,SAAS,CAACzM,MAAV,IAAoB6E,IAAI,CAACL,OAAL,KAAiB7C,SAAzC,EAAoD;UAClD8K,SAAS,GAAG,EAAG,CAAAjB,MAAH,CAAU3G,IAAI,CAACL,OAAf,CAAZ;QACD,CAPkD;;QAUnD,IAAImI,YAAY,GAAGF,SAAS,CAACG,GAAV,CAAchI,eAAe,CAACC,IAAD,EAAOpB,MAAP,CAA7B,CAAnB;QAEA,IAAIgC,OAAO,CAAC/B,KAAR,IAAiBiJ,YAAY,CAAC3M,MAAlC,EAA0C;UACxCiM,WAAW,CAACpH,IAAI,CAACzE,KAAN,CAAX,GAA0B,CAA1B;UACA,OAAO+L,IAAI,CAACQ,YAAD,CAAX;QACD;QACD,IAAI,CAACP,IAAL,EAAW;UACTD,IAAI,CAACQ,YAAD,CAAJ;QACD,CAFD,MAEO;UACL;UACA;UACA;UACA,IAAI9H,IAAI,CAACW,QAAL,IAAiB,CAAC0G,IAAI,CAACxK,KAA3B,EAAkC;YAChC,IAAImD,IAAI,CAACL,OAAL,KAAiB7C,SAArB,EAAgC;cAC9BgL,YAAY,GAAG,EACZ,CAAAnB,MADY,CACL3G,IAAI,CAACL,OADA,EAEZoI,GAFY,CAERhI,eAAe,CAACC,IAAD,EAAOpB,MAAP,CAFP,CAAf;YAGD,CAJD,MAIO,IAAIgC,OAAO,CAACtF,KAAZ,EAAmB;cACxBwM,YAAY,GAAG,CACblH,OAAO,CAACtF,KAAR,CACE0E,IADF,EAEEvE,MAAM,CAACmF,OAAO,CAACC,QAAR,CAAiBF,QAAlB,EAA4BX,IAAI,CAACzE,KAAjC,CAFR,CADa,CAAf;YAMD;YACD,OAAO+L,IAAI,CAACQ,YAAD,CAAX;UACD;UAED,IAAIE,YAAkC,GAAG,EAAzC;UACA,IAAIhI,IAAI,CAACwH,YAAT,EAAuB;YACrBxJ,MAAM,CAACC,IAAP,CAAYoJ,IAAI,CAACxK,KAAjB,EAAwBkL,GAAxB,CAA4B,UAAAxI,GAAG,EAAI;cACjCyI,YAAY,CAACzI,GAAD,CAAZ,GAAoBS,IAAI,CAACwH,YAAzB;aADF;UAGD;UACDQ,YAAY,GAAAvH,QAAA,KACPuH,YADO,EAEPX,IAAI,CAACrH,IAAL,CAAU5E,MAFH,CAAZ;UAKA,IAAM6M,iBAA6C,GAAG,EAAtD;UAEAjK,MAAM,CAACC,IAAP,CAAY+J,YAAZ,EAA0B3M,OAA1B,CAAkC,UAAAE,KAAK,EAAI;YACzC,IAAM2M,WAAW,GAAGF,YAAY,CAACzM,KAAD,CAAhC;YACA,IAAM4M,eAAe,GAAGrM,KAAK,CAACiB,OAAN,CAAcmL,WAAd,IACpBA,WADoB,GAEpB,CAACA,WAAD,CAFJ;YAGAD,iBAAiB,CAAC1M,KAAD,CAAjB,GAA2B4M,eAAe,CAACJ,GAAhB,CACzBN,YAAY,CAACW,IAAb,CAAkB,IAAlB,EAAwB7M,KAAxB,CADyB,CAA3B;WALF;UASA,IAAMmM,MAAM,GAAG,IAAI7B,MAAJ,CAAWoC,iBAAX,CAAf;UACAP,MAAM,CAAC7G,QAAP,CAAgBD,OAAO,CAACC,QAAxB;UACA,IAAIwG,IAAI,CAACrH,IAAL,CAAUY,OAAd,EAAuB;YACrByG,IAAI,CAACrH,IAAL,CAAUY,OAAV,CAAkBC,QAAlB,GAA6BD,OAAO,CAACC,QAArC;YACAwG,IAAI,CAACrH,IAAL,CAAUY,OAAV,CAAkBtF,KAAlB,GAA0BsF,OAAO,CAACtF,KAAlC;UACD;UACDoM,MAAM,CAAC3C,QAAP,CAAgBsC,IAAI,CAACxK,KAArB,EAA4BwK,IAAI,CAACrH,IAAL,CAAUY,OAAV,IAAqBA,OAAjD,EAA0D,UAAAyH,IAAI,EAAI;YAChE,IAAMC,WAAW,GAAG,EAApB;YACA,IAAIR,YAAY,IAAIA,YAAY,CAAC3M,MAAjC,EAAyC;cACvCmN,WAAW,CAAC9M,IAAZ,CAAAU,KAAA,CAAAoM,WAAW,EAASR,YAAT,CAAX;YACD;YACD,IAAIO,IAAI,IAAIA,IAAI,CAAClN,MAAjB,EAAyB;cACvBmN,WAAW,CAAC9M,IAAZ,CAAAU,KAAA,CAAAoM,WAAW,EAASD,IAAT,CAAX;YACD;YACDf,IAAI,CAACgB,WAAW,CAACnN,MAAZ,GAAqBmN,WAArB,GAAmC,IAApC,CAAJ;WARF;QAUD;MACF;MAED,IAAIC,GAAJ;MACA,IAAIvI,IAAI,CAACwI,cAAT,EAAyB;QACvBD,GAAG,GAAGvI,IAAI,CAACwI,cAAL,CAAoBxI,IAApB,EAA0BqH,IAAI,CAACxK,KAA/B,EAAsC8K,EAAtC,EAA0CN,IAAI,CAACzI,MAA/C,EAAuDgC,OAAvD,CAAN;MACD,CAFD,MAEO,IAAIZ,IAAI,CAACiH,SAAT,EAAoB;QACzB,IAAI;UACFsB,GAAG,GAAGvI,IAAI,CAACiH,SAAL,CAAejH,IAAf,EAAqBqH,IAAI,CAACxK,KAA1B,EAAiC8K,EAAjC,EAAqCN,IAAI,CAACzI,MAA1C,EAAkDgC,OAAlD,CAAN;SADF,CAEE,OAAOtF,KAAP,EAAc;UACdT,OAAO,CAACS,KAAR,oBAAAT,OAAO,CAACS,KAAR,CAAgBA,KAAhB,EADc;;UAGd,IAAI,CAACsF,OAAO,CAAC6H,sBAAb,EAAqC;YACnCC,UAAU,CAAC,YAAM;cACf,MAAMpN,KAAN;aADQ,EAEP,CAFO,CAAV;UAGD;UACDqM,EAAE,CAACrM,KAAK,CAACqE,OAAP,CAAF;QACD;QACD,IAAI4I,GAAG,KAAK,IAAZ,EAAkB;UAChBZ,EAAE;QACH,CAFD,MAEO,IAAIY,GAAG,KAAK,KAAZ,EAAmB;UACxBZ,EAAE,CACA,OAAO3H,IAAI,CAACL,OAAZ,KAAwB,UAAxB,GACIK,IAAI,CAACL,OAAL,CAAaK,IAAI,CAACI,SAAL,IAAkBJ,IAAI,CAACzE,KAApC,CADJ,GAEIyE,IAAI,CAACL,OAAL,KAAmBK,IAAI,CAACI,SAAL,IAAkBJ,IAAI,CAACzE,KAA1C,YAHJ,CAAF;QAKD,CANM,MAMA,IAAIgN,GAAG,YAAYzM,KAAnB,EAA0B;UAC/B6L,EAAE,CAACY,GAAD,CAAF;QACD,CAFM,MAEA,IAAIA,GAAG,YAAY9J,KAAnB,EAA0B;UAC/BkJ,EAAE,CAACY,GAAG,CAAC5I,OAAL,CAAF;QACD;MACF;MACD,IAAI4I,GAAG,IAAKA,GAAD,CAAuBI,IAAlC,EAAwC;QACrCJ,GAAD,CAAuBI,IAAvB,CACE;UAAA,OAAMhB,EAAE,EAAR;SADF,EAEE,UAAA1M,CAAC;UAAA,OAAI0M,EAAE,CAAC1M,CAAD,CAAN;SAFH;MAID;KAtIU,EAwIb,UAAAmC,OAAO,EAAI;MACToJ,QAAQ,CAACpJ,OAAD,CAAR;KAzIW,EA2IbwB,MA3Ia,CAAf;;SA+IFuI,OAAA,YAAQA,QAAAnH,IAAR,EAAgC;IAC9B,IAAIA,IAAI,CAACrF,IAAL,KAAcmC,SAAd,IAA2BkD,IAAI,CAACyE,OAAL,YAAwB/C,MAAvD,EAA+D;MAC7D1B,IAAI,CAACrF,IAAL,GAAY,SAAZ;IACD;IACD,IACE,OAAOqF,IAAI,CAACiH,SAAZ,KAA0B,UAA1B,IACAjH,IAAI,CAACrF,IADL,IAEA,CAAC4K,UAAU,CAAC/E,cAAX,CAA0BR,IAAI,CAACrF,IAA/B,CAHH,EAIE;MACA,MAAM,IAAI8D,KAAJ,CAAUhD,MAAM,CAAC,sBAAD,EAAyBuE,IAAI,CAACrF,IAA9B,CAAhB,CAAN;IACD;IACD,OAAOqF,IAAI,CAACrF,IAAL,IAAa,QAApB;;SAGFuM,mBAAA,YAAoBA,oBAAAlH,IAApB,EAA4C;IAC1C,IAAI,OAAOA,IAAI,CAACiH,SAAZ,KAA0B,UAA9B,EAA0C;MACxC,OAAOjH,IAAI,CAACiH,SAAZ;IACD;IACD,IAAMhJ,IAAI,GAAGD,MAAM,CAACC,IAAP,CAAY+B,IAAZ,CAAb;IACA,IAAM4I,YAAY,GAAG3K,IAAI,CAACuB,OAAL,CAAa,SAAb,CAArB;IACA,IAAIoJ,YAAY,KAAK,CAAC,CAAtB,EAAyB;MACvB3K,IAAI,CAAC4K,MAAL,CAAYD,YAAZ,EAA0B,CAA1B;IACD;IACD,IAAI3K,IAAI,CAAC9C,MAAL,KAAgB,CAAhB,IAAqB8C,IAAI,CAAC,CAAD,CAAJ,KAAY,UAArC,EAAiD;MAC/C,OAAOsH,UAAU,CAAC5E,QAAlB;IACD;IACD,OAAO4E,UAAU,CAAC,IAAK,CAAA4B,OAAL,CAAanH,IAAb,CAAD,CAAV,IAAkClD,SAAzC;;;;AA5TE+I,MAAA,CAEGiD,QAAA,GAAW,SAASA,QAATA,CAAkBnO,IAAlB,EAAgCsM,SAAhC,EAA2C;EAC3D,IAAI,OAAOA,SAAP,KAAqB,UAAzB,EAAqC;IACnC,MAAM,IAAIxI,KAAJ,CACJ,kEADI,CAAN;EAGD;EACD8G,UAAU,CAAC5K,IAAD,CAAV,GAAmBsM,SAAnB;AACD;AATGpB,MAAA,CAWGxL,OAAA,GAAUA,OAAA;AAXbwL,MAAA,CAaGhF,QAAA,GAAWA,QAAA;AAbdgF,MAAA,CAeGN,UAAA,GAAaA,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}