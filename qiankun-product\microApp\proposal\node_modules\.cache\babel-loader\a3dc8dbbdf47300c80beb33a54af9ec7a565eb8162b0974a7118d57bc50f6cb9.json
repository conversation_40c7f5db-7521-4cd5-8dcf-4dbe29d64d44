{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport SuggestDocumentSubmit from './components/SuggestDocumentSubmit';\nimport { user } from 'common/js/system_var.js';\nvar __default__ = {\n  name: 'SuggestDocument'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var show = ref(false);\n    var id = ref('');\n    var _GlobalTable = GlobalTable({\n        tableApi: 'proposalClueTheme'\n      }),\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery,\n      handleSortChange = _GlobalTable.handleSortChange,\n      handleHeaderClass = _GlobalTable.handleHeaderClass,\n      handleTableSelect = _GlobalTable.handleTableSelect;\n    onActivated(function () {\n      handleQuery();\n      userLog();\n    });\n    var handleEdit = function handleEdit(item) {\n      id.value = item.id;\n      show.value = true;\n    };\n    var userLog = function userLog() {\n      console.log(user.value);\n    };\n    var callback = function callback() {\n      handleQuery();\n      show.value = false;\n    };\n    var __returned__ = {\n      show,\n      id,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      handleSortChange,\n      handleHeaderClass,\n      handleTableSelect,\n      handleEdit,\n      userLog,\n      callback,\n      ref,\n      onActivated,\n      get format() {\n        return format;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get SuggestDocumentSubmit() {\n        return SuggestDocumentSubmit;\n      },\n      get user() {\n        return user;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "format", "GlobalTable", "SuggestDocumentSubmit", "user", "__default__", "name", "show", "id", "_GlobalTable", "tableApi", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "handleSortChange", "handleHeaderClass", "handleTableSelect", "userLog", "handleEdit", "item", "value", "console", "log", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestClue/SuggestDocument/SuggestDocument.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestDocument\">\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\"\r\n                @sort-change=\"handleSortChange\"\r\n                :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column prop=\"title\"\r\n                         label=\"文案类标题\"\r\n                         width=\"280\" />\r\n        <el-table-column prop=\"content\"\r\n                         label=\"文案简介\"\r\n                         width=\"725\" />\r\n        <el-table-column prop=\"userName\"\r\n                         label=\"修改人\"\r\n                         width=\"180\" />\r\n        <el-table-column label=\"修改时间\"\r\n                         width=\"280\">\r\n          <template #default=\"scope\">{{ format(scope.row.createDate) }}</template>\r\n        </el-table-column>\r\n        <el-table-column width=\"120\"\r\n                         fixed=\"right\"\r\n                         class-name=\"globalTableCustom\">\r\n          <template #header>操作</template>\r\n          <template #default=\"scope\">\r\n            <el-button @click=\"handleEdit(scope.row)\"\r\n                       type=\"primary\"\r\n                       plain>编辑</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      :name=\"id ? '编辑文案' : '新增文案'\">\r\n      <SuggestDocumentSubmit :id=\"id\"\r\n                             @callback=\"callback\"></SuggestDocumentSubmit>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestDocument' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SuggestDocumentSubmit from './components/SuggestDocumentSubmit'\r\nimport { user } from 'common/js/system_var.js'\r\nconst show = ref(false)\r\nconst id = ref('')\r\nconst {\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n} = GlobalTable({ tableApi: 'proposalClueTheme', })\r\n\r\nonActivated(() => { handleQuery(); userLog() })\r\nconst handleEdit = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\n\r\nconst userLog = (() => {\r\n  console.log(user.value)\r\n})\r\n\r\nconst callback = () => {\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestDocument {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - 20px);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA6DA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,SAASC,IAAI,QAAQ,yBAAyB;AAP9C,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAkB,CAAC;;;;;IAQ1C,IAAMC,IAAI,GAAGR,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMS,EAAE,GAAGT,GAAG,CAAC,EAAE,CAAC;IAClB,IAAAU,YAAA,GAWIP,WAAW,CAAC;QAAEQ,QAAQ,EAAE;MAAqB,CAAC,CAAC;MAVjDC,QAAQ,GAAAF,YAAA,CAARE,QAAQ;MACRC,MAAM,GAAAH,YAAA,CAANG,MAAM;MACNC,MAAM,GAAAJ,YAAA,CAANI,MAAM;MACNC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MACRC,SAAS,GAAAN,YAAA,CAATM,SAAS;MACTC,SAAS,GAAAP,YAAA,CAATO,SAAS;MACTC,WAAW,GAAAR,YAAA,CAAXQ,WAAW;MACXC,gBAAgB,GAAAT,YAAA,CAAhBS,gBAAgB;MAChBC,iBAAiB,GAAAV,YAAA,CAAjBU,iBAAiB;MACjBC,iBAAiB,GAAAX,YAAA,CAAjBW,iBAAiB;IAGnBpB,WAAW,CAAC,YAAM;MAAEiB,WAAW,CAAC,CAAC;MAAEI,OAAO,CAAC,CAAC;IAAC,CAAC,CAAC;IAC/C,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3Bf,EAAE,CAACgB,KAAK,GAAGD,IAAI,CAACf,EAAE;MAClBD,IAAI,CAACiB,KAAK,GAAG,IAAI;IACnB,CAAC;IAED,IAAMH,OAAO,GAAI,SAAXA,OAAOA,CAAA,EAAU;MACrBI,OAAO,CAACC,GAAG,CAACtB,IAAI,CAACoB,KAAK,CAAC;IACzB,CAAE;IAEF,IAAMG,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBV,WAAW,CAAC,CAAC;MACbV,IAAI,CAACiB,KAAK,GAAG,KAAK;IACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}