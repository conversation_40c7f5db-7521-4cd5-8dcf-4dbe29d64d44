<template>
  <div class="SuggestRecommendUnit">
    <div class="SuggestRecommendUnitTitle">根据办理单位职能匹配、历史建议交办办理数据以及希望送交办单位三个维度综合分析，小助手建议您将本提案交由以下办理单位办理，点击他们就会对号入座哦~~</div>
    <div class="SuggestRecommendUnitName">办理单位：</div>
    <div class="SuggestRecommendUnitBody">
      <div class="SuggestRecommendUnitItem"
           v-for="item in unitData"
           @click="handleUnitClick(item)"
           :key="item.id">{{ item.name }}</div>
    </div>
  </div>
</template>
<script>
export default { name: 'SuggestRecommendUnit' }
</script>
<script setup>
import api from '@/api'
import { ref, watch } from 'vue'
const props = defineProps({ params: { type: Object, default: () => ({}) } })
const emit = defineEmits(['callback', 'select'])
const unitData = ref([])

const commonMethod = async () => {
  try {
    const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区
    const { data } = await api.commonUnit({ areaId: AreaId, dbName: 'thinktank', type: '1', ...props.params })
    unitData.value = data || []
    if (unitData.value.length) { emit('callback', true, true) } else { emit('callback', false, false) }
  } catch (err) {
    emit('callback', false, false)
  }
}
const handleUnitClick = (item) => { emit('select', item) }
watch(() => props.params, () => {
  if (props.params.content) { commonMethod() } else { emit('callback', false, false) }
}, { immediate: true })
</script>
<style lang="scss">
.SuggestRecommendUnit {
  width: 360px;
  padding: var(--zy-distance-two);
  padding-top: 0;

  .SuggestRecommendUnitTitle {
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    padding-bottom: var(--zy-font-name-distance-five);
    color: var(--zy-el-color-primary);
  }

  .SuggestRecommendUnitName {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding-bottom: var(--zy-font-name-distance-five);
  }

  .SuggestRecommendUnitBody {
    display: flex;
    flex-wrap: wrap;

    .SuggestRecommendUnitItem {
      height: var(--zy-height-routine);
      line-height: var(--zy-height-routine);
      font-size: var(--zy-text-font-size);
      background-color: var(--zy-el-color-info-light-9);
      padding: 0 var(--zy-distance-five);
      margin-right: var(--zy-distance-five);
      margin-bottom: var(--zy-distance-five);
      border-radius: var(--el-border-radius-small);
    }
  }
}
</style>
