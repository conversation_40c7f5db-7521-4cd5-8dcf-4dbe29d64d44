{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport http_stream from 'common/http/stream.js';\nimport { ref, onMounted, defineAsyncComponent } from 'vue';\nimport { useStore } from 'vuex';\nimport { format } from 'common/js/time.js';\nimport { size2Str } from 'common/js/utils.js';\nimport { globalFileLocation } from 'common/config/location.js';\nimport { setting, content_style, guid, svg } from '../../AiToolBox/AiToolBox.js';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'IntelligentManuscriptMerging'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var GlobalMarkdown = defineAsyncComponent(function () {\n      return import('common/components/global-markdown/global-markdown.vue');\n    });\n    var store = useStore();\n    var loading = ref(false);\n    var loadingText = ref('');\n    var fileData = ref([]);\n    var mergeId = ref('');\n    var mergeType = ref([]);\n    var content = ref('');\n    var elTime = ref('');\n    var elRef = ref();\n    var elPonderRef = ref();\n    var elData = ref({\n      content: '',\n      contentOld: ''\n    });\n    var elPonderData = ref({\n      content: '',\n      contentOld: ''\n    });\n    var currentRequest = null;\n    var isLoading = ref(false);\n    var isStreaming = ref(false);\n    var startTime = null;\n    var endTime = null;\n    var handleUpdate = function handleUpdate() {\n      var _elRef$value;\n      console.log((_elRef$value = elRef.value) === null || _elRef$value === void 0 ? void 0 : _elRef$value.elRef.innerHTML);\n    };\n    var fileIcon = function fileIcon(type) {\n      var IconClass = {\n        docx: 'globalFileWord',\n        doc: 'globalFileWord',\n        wps: 'globalFileWPS',\n        pdf: 'globalFilePDF',\n        txt: 'globalFileTXT'\n      };\n      return IconClass[type] || 'globalFileUnknown';\n    };\n    var formatDuring = function formatDuring(mss) {\n      var days = parseInt(mss / (1000 * 60 * 60 * 24));\n      var hours = parseInt(mss % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n      var minutes = parseInt(mss % (1000 * 60 * 60) / (1000 * 60));\n      var seconds = mss % (1000 * 60) / 1000;\n      var time = '';\n      if (days > 0) time += `${days} 天 `;\n      if (hours > 0) time += `${hours} 小时 `;\n      if (minutes > 0) time += `${minutes} 分钟 `;\n      if (seconds > 0) time += `${seconds} 秒 `;\n      return time;\n    };\n    var handleHttpStream = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var AiChatParam, data, _choice$, choice, details, _elPonderRef$value, executionTime, _elRef$value2, _elRef$value3, _elRef$value4, _elRef$value5, _elRef$value6;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              startTime = new Date();\n              isLoading.value = true;\n              isStreaming.value = true;\n              _context.prev = 3;\n              AiChatParam = {\n                chatBusinessScene: 'wenbenrunse',\n                chatId: `wenbenrunse_${guid()}`,\n                question: '智能合稿',\n                attachmentIds: fileData.value.map(function (v) {\n                  return v.id;\n                }).join(','),\n                tool: mergeId.value\n              };\n              currentRequest = http_stream('/aigpt/chatStream', {\n                body: JSON.stringify(AiChatParam),\n                onMessage(event) {\n                  // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\n                  isLoading.value = false;\n                  if (event.data !== '[DONE]') {\n                    data = JSON.parse(event.data);\n                    if (Array.isArray(data)) {\n                      console.log('[]', data);\n                    } else {\n                      // console.log('{}', data)\n                      choice = (data === null || data === void 0 ? void 0 : data.choices) || [{}];\n                      details = ((_choice$ = choice[0]) === null || _choice$ === void 0 ? void 0 : _choice$.delta) || {};\n                      if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\n                        (_elPonderRef$value = elPonderRef.value) === null || _elPonderRef$value === void 0 || _elPonderRef$value.enqueueRender(details.reasoning_content || '');\n                        if (elTime.value) {\n                          startTime = null;\n                          endTime = null;\n                        } else {\n                          endTime = new Date();\n                          executionTime = endTime - startTime;\n                          elTime.value = formatDuring(executionTime);\n                        }\n                      }\n                      if (Object.prototype.hasOwnProperty.call(details, 'content')) {\n                        loading.value = false;\n                        (_elRef$value2 = elRef.value) === null || _elRef$value2 === void 0 || _elRef$value2.enqueueRender(details.content || '');\n                        content.value = ((_elRef$value3 = elRef.value) === null || _elRef$value3 === void 0 || (_elRef$value3 = _elRef$value3.elRef) === null || _elRef$value3 === void 0 ? void 0 : _elRef$value3.innerHTML) || '';\n                      }\n                    }\n                  } else {\n                    // console.log(event.data)\n                    (_elRef$value4 = elRef.value) === null || _elRef$value4 === void 0 || _elRef$value4.enqueueRender('');\n                    isStreaming.value = false;\n                  }\n                },\n                onError(err) {\n                  console.log('流式接口错误:', err);\n                },\n                onClose() {\n                  isLoading.value = false;\n                  isStreaming.value = false;\n                  console.log('流式接口关闭');\n                }\n              });\n              _context.next = 8;\n              return currentRequest.promise;\n            case 8:\n              _context.next = 16;\n              break;\n            case 10:\n              _context.prev = 10;\n              _context.t0 = _context[\"catch\"](3);\n              isLoading.value = false;\n              isStreaming.value = false;\n              (_elRef$value5 = elRef.value) === null || _elRef$value5 === void 0 || _elRef$value5.enqueueRender('服务器繁忙，请稍后再试。');\n              console.error('启动流式接口失败:', _context.t0);\n            case 16:\n              _context.prev = 16;\n              // currentRequest = null\n              loading.value = false;\n              content.value = ((_elRef$value6 = elRef.value) === null || _elRef$value6 === void 0 || (_elRef$value6 = _elRef$value6.elRef) === null || _elRef$value6 === void 0 ? void 0 : _elRef$value6.innerHTML) || '';\n              return _context.finish(16);\n            case 20:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[3, 10, 16, 20]]);\n      }));\n      return function handleHttpStream() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleCloseMessage = function handleCloseMessage() {\n      currentRequest = null;\n      isLoading.value = false;\n      isStreaming.value = false;\n    };\n    var handleStopMessage = function handleStopMessage() {\n      if (currentRequest) {\n        currentRequest.abort();\n        isLoading.value = false;\n        isStreaming.value = false;\n        console.log('启动流式接口停止');\n      }\n      handleCloseMessage();\n    };\n    var handleStart = function handleStart() {\n      var _elRef$value7, _elPonderRef$value2;\n      if (!fileData.value.length) return ElMessage({\n        type: 'warning',\n        message: `请先上传附件!`\n      });\n      loading.value = true;\n      elTime.value = '';\n      elData.value = {\n        content: '',\n        contentOld: ''\n      };\n      elPonderData.value = {\n        content: '',\n        contentOld: ''\n      };\n      (_elRef$value7 = elRef.value) === null || _elRef$value7 === void 0 || _elRef$value7.clearContent();\n      (_elPonderRef$value2 = elPonderRef.value) === null || _elPonderRef$value2 === void 0 || _elPonderRef$value2.clearContent();\n      handleStopMessage();\n      handleHttpStream();\n    };\n    var handleExportWord = function handleExportWord() {\n      store.commit('setExportWordHtmlObj', {\n        code: 'exportWord',\n        name: '智能合稿 --- 文档导出',\n        key: 'content',\n        data: {\n          content: content.value\n        }\n      });\n    };\n    /**\r\n     * 限制上传附件的文件类型\r\n     */\n    var handleFile = function handleFile(file) {\n      var fileType = file.name.substring(file.name.lastIndexOf('.') + 1);\n      var isShow = ['doc', 'docx', 'pdf'].includes(fileType);\n      if (!isShow) ElMessage({\n        type: 'warning',\n        message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!`\n      });\n      return isShow;\n    };\n    var onUploadProgress = function onUploadProgress(progressEvent, uid) {\n      var _progressEvent$event;\n      if (progressEvent !== null && progressEvent !== void 0 && (_progressEvent$event = progressEvent.event) !== null && _progressEvent$event !== void 0 && _progressEvent$event.lengthComputable) {\n        var progress = (progressEvent.loaded / progressEvent.total * 100).toFixed(0);\n        fileData.value.forEach(function (item) {\n          if (item.uid === uid) {\n            item.progress = parseInt(progress);\n          }\n        });\n      }\n    };\n    /**\r\n     * 上传附件请求方法\r\n     */\n    var fileUpload = function fileUpload(_ref3) {\n      var file = _ref3.file;\n      var param = new FormData();\n      param.append('file', file);\n      var fileId = guid();\n      var fileInfo = {\n        uid: fileId,\n        fileName: file.name,\n        time: file.uid,\n        progress: 0\n      };\n      handleGlobalUpload(param, fileId, fileInfo);\n    };\n    var handleGlobalUpload = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(params, uid, fileInfo) {\n        var _yield$api$globalUplo, data, newFileData, _iterator, _step, item;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              fileData.value.push(fileInfo);\n              _context2.next = 4;\n              return api.globalUpload(params, onUploadProgress, uid);\n            case 4:\n              _yield$api$globalUplo = _context2.sent;\n              data = _yield$api$globalUplo.data;\n              newFileData = [];\n              _iterator = _createForOfIteratorHelper(fileData.value);\n              try {\n                for (_iterator.s(); !(_step = _iterator.n()).done;) {\n                  item = _step.value;\n                  if (item.uid === uid) {\n                    newFileData.push(_objectSpread(_objectSpread(_objectSpread({}, data), fileInfo), {}, {\n                      progress: 100\n                    }));\n                  } else {\n                    newFileData.push(item);\n                  }\n                }\n              } catch (err) {\n                _iterator.e(err);\n              } finally {\n                _iterator.f();\n              }\n              fileData.value = newFileData;\n              _context2.next = 15;\n              break;\n            case 12:\n              _context2.prev = 12;\n              _context2.t0 = _context2[\"catch\"](0);\n              fileData.value = fileData.value.filter(function (v) {\n                return v.uid !== uid;\n              });\n            case 15:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 12]]);\n      }));\n      return function handleGlobalUpload(_x, _x2, _x3) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var handleClick = function handleClick(row) {\n      globalFileLocation({\n        name: process.env.VUE_APP_NAME,\n        fileId: row.id,\n        fileType: row.extName,\n        fileName: row.originalFileName,\n        fileSize: row.fileSize\n      });\n    };\n    var handleDel = function handleDel(file) {\n      fileData.value = fileData.value.filter(function (v) {\n        return v.uid !== file.uid;\n      });\n    };\n    var aigptChatSceneDetail = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _yield$api$aigptChatS, data, newmergeType, index, _data$tools, item;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.aigptChatSceneDetail({\n                query: {\n                  chatSceneCode: 'Intelligent_collaboration'\n                }\n              });\n            case 2:\n              _yield$api$aigptChatS = _context3.sent;\n              data = _yield$api$aigptChatS.data;\n              newmergeType = [];\n              for (index = 0; index < (data === null || data === void 0 || (_data$tools = data.tools) === null || _data$tools === void 0 ? void 0 : _data$tools.length) || 0; index++) {\n                item = data === null || data === void 0 ? void 0 : data.tools[index];\n                if (item.isUsing) {\n                  newmergeType.push({\n                    id: item.id,\n                    code: item.chatToolCode,\n                    name: item.chatToolName\n                  });\n                  if (newmergeType.length === 1) {\n                    mergeId.value = item.id;\n                  }\n                }\n              }\n              console.log(newmergeType);\n              mergeType.value = newmergeType;\n            case 8:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function aigptChatSceneDetail() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    onMounted(function () {\n      aigptChatSceneDetail();\n    });\n    var __returned__ = {\n      GlobalMarkdown,\n      store,\n      loading,\n      loadingText,\n      fileData,\n      mergeId,\n      mergeType,\n      content,\n      elTime,\n      elRef,\n      elPonderRef,\n      elData,\n      elPonderData,\n      get currentRequest() {\n        return currentRequest;\n      },\n      set currentRequest(v) {\n        currentRequest = v;\n      },\n      isLoading,\n      isStreaming,\n      get startTime() {\n        return startTime;\n      },\n      set startTime(v) {\n        startTime = v;\n      },\n      get endTime() {\n        return endTime;\n      },\n      set endTime(v) {\n        endTime = v;\n      },\n      handleUpdate,\n      fileIcon,\n      formatDuring,\n      handleHttpStream,\n      handleCloseMessage,\n      handleStopMessage,\n      handleStart,\n      handleExportWord,\n      handleFile,\n      onUploadProgress,\n      fileUpload,\n      handleGlobalUpload,\n      handleClick,\n      handleDel,\n      aigptChatSceneDetail,\n      get api() {\n        return api;\n      },\n      get http_stream() {\n        return http_stream;\n      },\n      ref,\n      onMounted,\n      defineAsyncComponent,\n      get useStore() {\n        return useStore;\n      },\n      get format() {\n        return format;\n      },\n      get size2Str() {\n        return size2Str;\n      },\n      get globalFileLocation() {\n        return globalFileLocation;\n      },\n      get setting() {\n        return setting;\n      },\n      get content_style() {\n        return content_style;\n      },\n      get guid() {\n        return guid;\n      },\n      get svg() {\n        return svg;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "http_stream", "ref", "onMounted", "defineAsyncComponent", "useStore", "format", "size2Str", "globalFileLocation", "setting", "content_style", "guid", "svg", "ElMessage", "__default__", "GlobalMarkdown", "store", "loading", "loadingText", "fileData", "mergeId", "mergeType", "content", "elTime", "elRef", "elPonderRef", "elData", "contentOld", "elPonderData", "currentRequest", "isLoading", "isStreaming", "startTime", "endTime", "handleUpdate", "_elRef$value", "console", "log", "innerHTML", "fileIcon", "IconClass", "docx", "doc", "wps", "pdf", "txt", "formatDuring", "mss", "days", "parseInt", "hours", "minutes", "seconds", "time", "handleHttpStream", "_ref2", "_callee", "AiChatParam", "data", "_choice$", "choice", "details", "_elPonderRef$value", "executionTime", "_elRef$value2", "_elRef$value3", "_elRef$value4", "_elRef$value5", "_elRef$value6", "_callee$", "_context", "Date", "chatBusinessScene", "chatId", "question", "attachmentIds", "map", "id", "join", "tool", "body", "JSON", "stringify", "onMessage", "event", "parse", "Array", "isArray", "choices", "delta", "enqueueRender", "reasoning_content", "onError", "err", "onClose", "promise", "t0", "error", "handleCloseMessage", "handleStopMessage", "abort", "handleStart", "_elRef$value7", "_elPonderRef$value2", "message", "clearContent", "handleExportWord", "commit", "code", "key", "handleFile", "file", "fileType", "substring", "lastIndexOf", "isShow", "includes", "onUploadProgress", "progressEvent", "uid", "_progressEvent$event", "lengthComputable", "progress", "loaded", "total", "toFixed", "item", "fileUpload", "_ref3", "param", "FormData", "append", "fileId", "fileInfo", "fileName", "handleGlobalUpload", "_ref4", "_callee2", "params", "_yield$api$globalUplo", "newFileData", "_iterator", "_step", "_callee2$", "_context2", "globalUpload", "_createForOfIteratorHelper", "_objectSpread", "filter", "_x", "_x2", "_x3", "handleClick", "row", "process", "env", "VUE_APP_NAME", "extName", "originalFileName", "fileSize", "handleDel", "aigptChatSceneDetail", "_ref5", "_callee3", "_yield$api$aigptChatS", "newmergeType", "index", "_data$tools", "_callee3$", "_context3", "query", "chatSceneCode", "tools", "isUsing", "chatToolCode", "chatToolName"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiToolBoxFunction/IntelligentManuscriptMerging/IntelligentManuscriptMerging.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"IntelligentManuscriptMerging\"\r\n    v-loading=\"loading\"\r\n    :element-loading-spinner=\"svg\"\r\n    :lement-loading-text=\"loadingText\"\r\n    element-loading-svg-view-box=\"-10, -10, 50, 50\">\r\n    <div class=\"IntelligentManuscriptMergingHead\">\r\n      <div class=\"IntelligentManuscriptMergingButton\">\r\n        <div class=\"IntelligentManuscriptMergingButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleStart\">开始合稿</el-button>\r\n        </div>\r\n        <div class=\"IntelligentManuscriptMergingButtonItem\"></div>\r\n      </div>\r\n      <div class=\"IntelligentManuscriptMergingButton\">\r\n        <div class=\"IntelligentManuscriptMergingButtonItem\">\r\n          <div class=\"IntelligentManuscriptMergingButtonmerge\">\r\n            <div class=\"IntelligentManuscriptMergingButtonmergeTitle\">合并策略：</div>\r\n            <el-radio-group v-model=\"mergeId\">\r\n              <el-radio v-for=\"item in mergeType\" :key=\"item.id\" :value=\"item.id\">\r\n                {{ item.name }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n        <div class=\"IntelligentManuscriptMergingButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleExportWord\">导出</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"IntelligentManuscriptMergingBody\">\r\n      <div class=\"IntelligentManuscriptMergingBodyLeft\">\r\n        <div class=\"IntelligentManuscriptMergingUploadBody\">\r\n          <div class=\"IntelligentManuscriptMergingUpload\">\r\n            <el-upload\r\n              drag\r\n              action=\"/\"\r\n              :before-upload=\"handleFile\"\r\n              :http-request=\"fileUpload\"\r\n              :show-file-list=\"false\"\r\n              multiple>\r\n              <el-icon class=\"zy-el-icon--upload\">\r\n                <upload-filled />\r\n              </el-icon>\r\n              <div class=\"zy-el-upload__text\">\r\n                将附件拖拽至此区域，或\r\n                <em>点击上传</em>\r\n              </div>\r\n              <div class=\"zy-el-upload__tip\">仅支持{{ ['doc', 'docx', 'pdf'].join('、') }}格式</div>\r\n            </el-upload>\r\n          </div>\r\n          <el-scrollbar class=\"IntelligentManuscriptMergingFileScroll\">\r\n            <div class=\"IntelligentManuscriptMergingFileList\">\r\n              <div\r\n                class=\"IntelligentManuscriptMergingFileItem\"\r\n                v-for=\"item in fileData\"\r\n                :key=\"item.uid\"\r\n                @click=\"handleClick(item)\">\r\n                <div class=\"globalFileIcon\" :class=\"fileIcon(item.extName)\"></div>\r\n                <div class=\"IntelligentManuscriptMergingFileItemName ellipsis\">\r\n                  {{ item.fileName }}\r\n                  <div class=\"IntelligentManuscriptMergingFileItemDel\" @click.stop=\"handleDel(item)\" v-if=\"item.id\">\r\n                    <el-icon><Delete /></el-icon>\r\n                  </div>\r\n                </div>\r\n                <div class=\"IntelligentManuscriptMergingFileItemInfo\">\r\n                  <div class=\"IntelligentManuscriptMergingFileItemFileSize\">\r\n                    {{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}\r\n                  </div>\r\n                  <div class=\"IntelligentManuscriptMergingFileItemTime\">{{ format(item.createDate) }}</div>\r\n                </div>\r\n                <div\r\n                  class=\"IntelligentManuscriptMergingFileItemProgress\"\r\n                  :style=\"{ width: `${item.progress}%` }\"\r\n                  v-if=\"!item.id\"></div>\r\n              </div>\r\n            </div>\r\n          </el-scrollbar>\r\n        </div>\r\n      </div>\r\n      <div class=\"IntelligentManuscriptMergingBodyRight\">\r\n        <TinyMceEditor ref=\"wordRef\" v-model=\"content\" :setting=\"setting\" :content_style=\"content_style\" />\r\n      </div>\r\n    </div>\r\n    <div class=\"TextPolishingAI\">\r\n      <div class=\"TextPolishingAIPonderContent\" v-show=\"false\">\r\n        <GlobalMarkdown ref=\"elPonderRef\" v-model=\"elPonderData.content\" :content=\"elPonderData.contentOld\" />\r\n      </div>\r\n      <div class=\"TextPolishingAIContent\" v-show=\"false\">\r\n        <GlobalMarkdown ref=\"elRef\" v-model=\"elData.content\" :content=\"elData.contentOld\" @update=\"handleUpdate\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'IntelligentManuscriptMerging' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport http_stream from 'common/http/stream.js'\r\nimport { ref, onMounted, defineAsyncComponent } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nimport { size2Str } from 'common/js/utils.js'\r\nimport { globalFileLocation } from 'common/config/location.js'\r\nimport { setting, content_style, guid, svg } from '../../AiToolBox/AiToolBox.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst GlobalMarkdown = defineAsyncComponent(() => import('common/components/global-markdown/global-markdown.vue'))\r\nconst store = useStore()\r\n\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n\r\nconst fileData = ref([])\r\nconst mergeId = ref('')\r\nconst mergeType = ref([])\r\n\r\nconst content = ref('')\r\n\r\nconst elTime = ref('')\r\nconst elRef = ref()\r\nconst elPonderRef = ref()\r\nconst elData = ref({ content: '', contentOld: '' })\r\nconst elPonderData = ref({ content: '', contentOld: '' })\r\n\r\nlet currentRequest = null\r\nconst isLoading = ref(false)\r\nconst isStreaming = ref(false)\r\nlet startTime = null\r\nlet endTime = null\r\n\r\nconst handleUpdate = () => {\r\n  console.log(elRef.value?.elRef.innerHTML)\r\n}\r\n\r\nconst fileIcon = (type) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    pdf: 'globalFilePDF',\r\n    txt: 'globalFileTXT'\r\n  }\r\n  return IconClass[type] || 'globalFileUnknown'\r\n}\r\n\r\nconst formatDuring = (mss) => {\r\n  const days = parseInt(mss / (1000 * 60 * 60 * 24))\r\n  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))\r\n  const seconds = (mss % (1000 * 60)) / 1000\r\n  var time = ''\r\n  if (days > 0) time += `${days} 天 `\r\n  if (hours > 0) time += `${hours} 小时 `\r\n  if (minutes > 0) time += `${minutes} 分钟 `\r\n  if (seconds > 0) time += `${seconds} 秒 `\r\n  return time\r\n}\r\nconst handleHttpStream = async () => {\r\n  startTime = new Date()\r\n  isLoading.value = true\r\n  isStreaming.value = true\r\n  try {\r\n    const AiChatParam = {\r\n      chatBusinessScene: 'wenbenrunse',\r\n      chatId: `wenbenrunse_${guid()}`,\r\n      question: '智能合稿',\r\n      attachmentIds: fileData.value.map((v) => v.id).join(','),\r\n      tool: mergeId.value\r\n    }\r\n    currentRequest = http_stream('/aigpt/chatStream', {\r\n      body: JSON.stringify(AiChatParam),\r\n      onMessage(event) {\r\n        // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\r\n        isLoading.value = false\r\n        if (event.data !== '[DONE]') {\r\n          const data = JSON.parse(event.data)\r\n          if (Array.isArray(data)) {\r\n            console.log('[]', data)\r\n          } else {\r\n            // console.log('{}', data)\r\n            const choice = data?.choices || [{}]\r\n            const details = choice[0]?.delta || {}\r\n            if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\r\n              elPonderRef.value?.enqueueRender(details.reasoning_content || '')\r\n              if (elTime.value) {\r\n                startTime = null\r\n                endTime = null\r\n              } else {\r\n                endTime = new Date()\r\n                const executionTime = endTime - startTime\r\n                elTime.value = formatDuring(executionTime)\r\n              }\r\n            }\r\n            if (Object.prototype.hasOwnProperty.call(details, 'content')) {\r\n              loading.value = false\r\n              elRef.value?.enqueueRender(details.content || '')\r\n              content.value = elRef.value?.elRef?.innerHTML || ''\r\n            }\r\n          }\r\n        } else {\r\n          // console.log(event.data)\r\n          elRef.value?.enqueueRender('')\r\n          isStreaming.value = false\r\n        }\r\n      },\r\n      onError(err) {\r\n        console.log('流式接口错误:', err)\r\n      },\r\n      onClose() {\r\n        isLoading.value = false\r\n        isStreaming.value = false\r\n        console.log('流式接口关闭')\r\n      }\r\n    })\r\n    await currentRequest.promise\r\n  } catch (error) {\r\n    isLoading.value = false\r\n    isStreaming.value = false\r\n    elRef.value?.enqueueRender('服务器繁忙，请稍后再试。')\r\n    console.error('启动流式接口失败:', error)\r\n  } finally {\r\n    // currentRequest = null\r\n    loading.value = false\r\n    content.value = elRef.value?.elRef?.innerHTML || ''\r\n  }\r\n}\r\nconst handleCloseMessage = () => {\r\n  currentRequest = null\r\n  isLoading.value = false\r\n  isStreaming.value = false\r\n}\r\nconst handleStopMessage = () => {\r\n  if (currentRequest) {\r\n    currentRequest.abort()\r\n    isLoading.value = false\r\n    isStreaming.value = false\r\n    console.log('启动流式接口停止')\r\n  }\r\n  handleCloseMessage()\r\n}\r\nconst handleStart = () => {\r\n  if (!fileData.value.length) return ElMessage({ type: 'warning', message: `请先上传附件!` })\r\n  loading.value = true\r\n  elTime.value = ''\r\n  elData.value = { content: '', contentOld: '' }\r\n  elPonderData.value = { content: '', contentOld: '' }\r\n  elRef.value?.clearContent()\r\n  elPonderRef.value?.clearContent()\r\n  handleStopMessage()\r\n  handleHttpStream()\r\n}\r\n\r\nconst handleExportWord = () => {\r\n  store.commit('setExportWordHtmlObj', {\r\n    code: 'exportWord',\r\n    name: '智能合稿 --- 文档导出',\r\n    key: 'content',\r\n    data: { content: content.value }\r\n  })\r\n}\r\n/**\r\n * 限制上传附件的文件类型\r\n */\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  const isShow = ['doc', 'docx', 'pdf'].includes(fileType)\r\n  if (!isShow) ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!` })\r\n  return isShow\r\n}\r\n\r\nconst onUploadProgress = (progressEvent, uid) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    fileData.value.forEach((item) => {\r\n      if (item.uid === uid) {\r\n        item.progress = parseInt(progress)\r\n      }\r\n    })\r\n  }\r\n}\r\n/**\r\n * 上传附件请求方法\r\n */\r\nconst fileUpload = ({ file }) => {\r\n  const param = new FormData()\r\n  param.append('file', file)\r\n  const fileId = guid()\r\n  const fileInfo = { uid: fileId, fileName: file.name, time: file.uid, progress: 0 }\r\n  handleGlobalUpload(param, fileId, fileInfo)\r\n}\r\n\r\nconst handleGlobalUpload = async (params, uid, fileInfo) => {\r\n  try {\r\n    fileData.value.push(fileInfo)\r\n    const { data } = await api.globalUpload(params, onUploadProgress, uid)\r\n    const newFileData = []\r\n    for (const item of fileData.value) {\r\n      if (item.uid === uid) {\r\n        newFileData.push({ ...data, ...fileInfo, progress: 100 })\r\n      } else {\r\n        newFileData.push(item)\r\n      }\r\n    }\r\n    fileData.value = newFileData\r\n  } catch (err) {\r\n    fileData.value = fileData.value.filter((v) => v.uid !== uid)\r\n  }\r\n}\r\nconst handleClick = (row) => {\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nconst handleDel = (file) => {\r\n  fileData.value = fileData.value.filter((v) => v.uid !== file.uid)\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const { data } = await api.aigptChatSceneDetail({ query: { chatSceneCode: 'Intelligent_collaboration' } })\r\n  const newmergeType = []\r\n  for (let index = 0; index < data?.tools?.length || 0; index++) {\r\n    const item = data?.tools[index]\r\n    if (item.isUsing) {\r\n      newmergeType.push({\r\n        id: item.id,\r\n        code: item.chatToolCode,\r\n        name: item.chatToolName\r\n      })\r\n      if (newmergeType.length === 1) {\r\n        mergeId.value = item.id\r\n      }\r\n    }\r\n  }\r\n  console.log(newmergeType)\r\n  mergeType.value = newmergeType\r\n}\r\nonMounted(() => {\r\n  aigptChatSceneDetail()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.IntelligentManuscriptMerging {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .IntelligentManuscriptMergingHead {\r\n    width: 100%;\r\n    padding: var(--zy-distance-four) 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .IntelligentManuscriptMergingButton {\r\n      width: calc(100% - 832px);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      & + .IntelligentManuscriptMergingButton {\r\n        width: 812px;\r\n        padding-right: 16px;\r\n      }\r\n      .IntelligentManuscriptMergingButtonItem {\r\n        display: flex;\r\n        align-items: center;\r\n        .IntelligentManuscriptMergingButtonmerge {\r\n          display: flex;\r\n          align-items: center;\r\n          .IntelligentManuscriptMergingButtonmergeTitle {\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n            padding-right: 12px;\r\n          }\r\n          .zy-el-radio-group {\r\n            .zy-el-radio {\r\n              margin-right: 32px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .IntelligentManuscriptMergingBody {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2)));\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: var(--zy-distance-four);\r\n    .IntelligentManuscriptMergingBodyLeft {\r\n      width: calc(100% - 832px);\r\n      height: 100%;\r\n      .IntelligentManuscriptMergingUploadBody {\r\n        width: 100%;\r\n        height: 100%;\r\n        background: #fff;\r\n        .IntelligentManuscriptMergingUpload {\r\n          width: 100%;\r\n          padding: var(--zy-distance-two);\r\n          position: relative;\r\n\r\n          .zy-el-upload {\r\n            --zy-el-upload-dragger-padding-horizontal: 20px;\r\n            --zy-el-upload-dragger-padding-vertical: 10px;\r\n            .zy-el-upload-dragger {\r\n              height: 220px;\r\n              display: flex;\r\n              align-items: center;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n            }\r\n            .zy-el-icon {\r\n              font-size: 99px;\r\n            }\r\n            .zy-el-upload__text {\r\n              line-height: var(--zy-line-height);\r\n            }\r\n\r\n            .zy-el-upload__tip {\r\n              padding: 0 var(--zy-distance-one);\r\n              line-height: var(--zy-line-height);\r\n            }\r\n          }\r\n        }\r\n        .IntelligentManuscriptMergingFileScroll {\r\n          width: 100%;\r\n          height: calc(100% - 260px);\r\n        }\r\n        .IntelligentManuscriptMergingFileList {\r\n          width: 100%;\r\n          padding: 0 var(--zy-distance-two);\r\n        }\r\n        .IntelligentManuscriptMergingFileItem {\r\n          width: 100%;\r\n          position: relative;\r\n          padding-left: 40px;\r\n          padding-top: var(--zy-distance-five);\r\n          padding-bottom: var(--zy-distance-five);\r\n          border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n          cursor: pointer;\r\n\r\n          .globalFileIcon {\r\n            width: 32px;\r\n            height: 32px;\r\n            vertical-align: middle;\r\n            position: absolute;\r\n            top: 50%;\r\n            left: 0;\r\n            transform: translateY(-50%);\r\n          }\r\n\r\n          .globalFileUnknown {\r\n            background: url('../../img/file_type/unknown.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFilePDF {\r\n            background: url('../../img/file_type/PDF.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileWord {\r\n            background: url('../../img/file_type/Word.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileTXT {\r\n            background: url('../../img/file_type/TXT.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileWPS {\r\n            background: url('../../img/file_type/WPS.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .IntelligentManuscriptMergingFileItemName {\r\n            width: 100%;\r\n            font-size: var(--zy-name-font-size);\r\n            line-height: var(--zy-line-height);\r\n            padding-right: var(--zy-distance-two);\r\n            padding-bottom: var(--zy-font-text-distance-five);\r\n            position: relative;\r\n            .IntelligentManuscriptMergingFileItemDel {\r\n              width: var(--zy-distance-two);\r\n              height: 100%;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: flex-end;\r\n              position: absolute;\r\n              top: 0;\r\n              right: 0;\r\n              cursor: pointer;\r\n              padding-bottom: var(--zy-font-text-distance-five);\r\n              .zy-el-icon {\r\n                font-size: var(--zy-name-font-size);\r\n                &:hover {\r\n                  color: var(--zy-el-color-danger);\r\n                }\r\n              }\r\n            }\r\n          }\r\n          .IntelligentManuscriptMergingFileItemInfo {\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            .IntelligentManuscriptMergingFileItemFileSize {\r\n              font-size: var(--zy-text-font-size);\r\n              line-height: var(--zy-line-height);\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n            .IntelligentManuscriptMergingFileItemTime {\r\n              font-size: var(--zy-text-font-size);\r\n              line-height: var(--zy-line-height);\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n          }\r\n          .IntelligentManuscriptMergingFileItemProgress {\r\n            height: 100%;\r\n            background: rgba(0, 0, 0, 0.1);\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            z-index: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .IntelligentManuscriptMergingBodyRight {\r\n      width: 812px;\r\n      height: 100%;\r\n      .TinyMceEditor {\r\n        height: 100%;\r\n        .tox-tinymce {\r\n          border: none;\r\n        }\r\n        .tox-editor-header {\r\n          width: 796px;\r\n          border: 1px solid #ccc;\r\n          border-bottom: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;+CAoGA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,GAAG,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,KAAK;AAC1D,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,SAASC,OAAO,EAAEC,aAAa,EAAEC,IAAI,EAAEC,GAAG,QAAQ,8BAA8B;AAChF,SAASC,SAAS,QAAQ,cAAc;AAZxC,IAAAC,WAAA,GAAe;EAAEzC,IAAI,EAAE;AAA+B,CAAC;;;;;IAavD,IAAM0C,cAAc,GAAGX,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,uDAAuD,CAAC;IAAA,EAAC;IAClH,IAAMY,KAAK,GAAGX,QAAQ,CAAC,CAAC;IAExB,IAAMY,OAAO,GAAGf,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMgB,WAAW,GAAGhB,GAAG,CAAC,EAAE,CAAC;IAE3B,IAAMiB,QAAQ,GAAGjB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMkB,OAAO,GAAGlB,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMmB,SAAS,GAAGnB,GAAG,CAAC,EAAE,CAAC;IAEzB,IAAMoB,OAAO,GAAGpB,GAAG,CAAC,EAAE,CAAC;IAEvB,IAAMqB,MAAM,GAAGrB,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMsB,KAAK,GAAGtB,GAAG,CAAC,CAAC;IACnB,IAAMuB,WAAW,GAAGvB,GAAG,CAAC,CAAC;IACzB,IAAMwB,MAAM,GAAGxB,GAAG,CAAC;MAAEoB,OAAO,EAAE,EAAE;MAAEK,UAAU,EAAE;IAAG,CAAC,CAAC;IACnD,IAAMC,YAAY,GAAG1B,GAAG,CAAC;MAAEoB,OAAO,EAAE,EAAE;MAAEK,UAAU,EAAE;IAAG,CAAC,CAAC;IAEzD,IAAIE,cAAc,GAAG,IAAI;IACzB,IAAMC,SAAS,GAAG5B,GAAG,CAAC,KAAK,CAAC;IAC5B,IAAM6B,WAAW,GAAG7B,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAI8B,SAAS,GAAG,IAAI;IACpB,IAAIC,OAAO,GAAG,IAAI;IAElB,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MAAA,IAAAC,YAAA;MACzBC,OAAO,CAACC,GAAG,EAAAF,YAAA,GAACX,KAAK,CAAC5H,KAAK,cAAAuI,YAAA,uBAAXA,YAAA,CAAaX,KAAK,CAACc,SAAS,CAAC;IAC3C,CAAC;IAED,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIxH,IAAI,EAAK;MACzB,IAAMyH,SAAS,GAAG;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,GAAG,EAAE,gBAAgB;QACrBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE;MACP,CAAC;MACD,OAAOL,SAAS,CAACzH,IAAI,CAAC,IAAI,mBAAmB;IAC/C,CAAC;IAED,IAAM+H,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAK;MAC5B,IAAMC,IAAI,GAAGC,QAAQ,CAACF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAClD,IAAMG,KAAK,GAAGD,QAAQ,CAAEF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACxE,IAAMI,OAAO,GAAGF,QAAQ,CAAEF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;MAChE,IAAMK,OAAO,GAAIL,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI;MAC1C,IAAIM,IAAI,GAAG,EAAE;MACb,IAAIL,IAAI,GAAG,CAAC,EAAEK,IAAI,IAAI,GAAGL,IAAI,KAAK;MAClC,IAAIE,KAAK,GAAG,CAAC,EAAEG,IAAI,IAAI,GAAGH,KAAK,MAAM;MACrC,IAAIC,OAAO,GAAG,CAAC,EAAEE,IAAI,IAAI,GAAGF,OAAO,MAAM;MACzC,IAAIC,OAAO,GAAG,CAAC,EAAEC,IAAI,IAAI,GAAGD,OAAO,KAAK;MACxC,OAAOC,IAAI;IACb,CAAC;IACD,IAAMC,gBAAgB;MAAA,IAAAC,KAAA,GAAA5D,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkF,QAAA;QAAA,IAAAC,WAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,MAAA,EAAAC,OAAA,EAAAC,kBAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAAAlL,mBAAA,GAAAuB,IAAA,UAAA4J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAvF,IAAA,GAAAuF,QAAA,CAAAlH,IAAA;YAAA;cACvB4E,SAAS,GAAG,IAAIuC,IAAI,CAAC,CAAC;cACtBzC,SAAS,CAAClI,KAAK,GAAG,IAAI;cACtBmI,WAAW,CAACnI,KAAK,GAAG,IAAI;cAAA0K,QAAA,CAAAvF,IAAA;cAEhB0E,WAAW,GAAG;gBAClBe,iBAAiB,EAAE,aAAa;gBAChCC,MAAM,EAAE,eAAe9D,IAAI,CAAC,CAAC,EAAE;gBAC/B+D,QAAQ,EAAE,MAAM;gBAChBC,aAAa,EAAExD,QAAQ,CAACvH,KAAK,CAACgL,GAAG,CAAC,UAAChJ,CAAC;kBAAA,OAAKA,CAAC,CAACiJ,EAAE;gBAAA,EAAC,CAACC,IAAI,CAAC,GAAG,CAAC;gBACxDC,IAAI,EAAE3D,OAAO,CAACxH;cAChB,CAAC;cACDiI,cAAc,GAAG5B,WAAW,CAAC,mBAAmB,EAAE;gBAChD+E,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACzB,WAAW,CAAC;gBACjC0B,SAASA,CAACC,KAAK,EAAE;kBACf;kBACAtD,SAAS,CAAClI,KAAK,GAAG,KAAK;kBACvB,IAAIwL,KAAK,CAAC1B,IAAI,KAAK,QAAQ,EAAE;oBACrBA,IAAI,GAAGuB,IAAI,CAACI,KAAK,CAACD,KAAK,CAAC1B,IAAI,CAAC;oBACnC,IAAI4B,KAAK,CAACC,OAAO,CAAC7B,IAAI,CAAC,EAAE;sBACvBtB,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEqB,IAAI,CAAC;oBACzB,CAAC,MAAM;sBACL;sBACME,MAAM,GAAG,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,OAAO,KAAI,CAAC,CAAC,CAAC,CAAC;sBAC9B3B,OAAO,GAAG,EAAAF,QAAA,GAAAC,MAAM,CAAC,CAAC,CAAC,cAAAD,QAAA,uBAATA,QAAA,CAAW8B,KAAK,KAAI,CAAC,CAAC;sBACtC,IAAInM,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAAC4I,OAAO,EAAE,mBAAmB,CAAC,EAAE;wBACtE,CAAAC,kBAAA,GAAArC,WAAW,CAAC7H,KAAK,cAAAkK,kBAAA,eAAjBA,kBAAA,CAAmB4B,aAAa,CAAC7B,OAAO,CAAC8B,iBAAiB,IAAI,EAAE,CAAC;wBACjE,IAAIpE,MAAM,CAAC3H,KAAK,EAAE;0BAChBoI,SAAS,GAAG,IAAI;0BAChBC,OAAO,GAAG,IAAI;wBAChB,CAAC,MAAM;0BACLA,OAAO,GAAG,IAAIsC,IAAI,CAAC,CAAC;0BACdR,aAAa,GAAG9B,OAAO,GAAGD,SAAS;0BACzCT,MAAM,CAAC3H,KAAK,GAAGkJ,YAAY,CAACiB,aAAa,CAAC;wBAC5C;sBACF;sBACA,IAAIzK,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAAC4I,OAAO,EAAE,SAAS,CAAC,EAAE;wBAC5D5C,OAAO,CAACrH,KAAK,GAAG,KAAK;wBACrB,CAAAoK,aAAA,GAAAxC,KAAK,CAAC5H,KAAK,cAAAoK,aAAA,eAAXA,aAAA,CAAa0B,aAAa,CAAC7B,OAAO,CAACvC,OAAO,IAAI,EAAE,CAAC;wBACjDA,OAAO,CAAC1H,KAAK,GAAG,EAAAqK,aAAA,GAAAzC,KAAK,CAAC5H,KAAK,cAAAqK,aAAA,gBAAAA,aAAA,GAAXA,aAAA,CAAazC,KAAK,cAAAyC,aAAA,uBAAlBA,aAAA,CAAoB3B,SAAS,KAAI,EAAE;sBACrD;oBACF;kBACF,CAAC,MAAM;oBACL;oBACA,CAAA4B,aAAA,GAAA1C,KAAK,CAAC5H,KAAK,cAAAsK,aAAA,eAAXA,aAAA,CAAawB,aAAa,CAAC,EAAE,CAAC;oBAC9B3D,WAAW,CAACnI,KAAK,GAAG,KAAK;kBAC3B;gBACF,CAAC;gBACDgM,OAAOA,CAACC,GAAG,EAAE;kBACXzD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwD,GAAG,CAAC;gBAC7B,CAAC;gBACDC,OAAOA,CAAA,EAAG;kBACRhE,SAAS,CAAClI,KAAK,GAAG,KAAK;kBACvBmI,WAAW,CAACnI,KAAK,GAAG,KAAK;kBACzBwI,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;gBACvB;cACF,CAAC,CAAC;cAAAiC,QAAA,CAAAlH,IAAA;cAAA,OACIyE,cAAc,CAACkE,OAAO;YAAA;cAAAzB,QAAA,CAAAlH,IAAA;cAAA;YAAA;cAAAkH,QAAA,CAAAvF,IAAA;cAAAuF,QAAA,CAAA0B,EAAA,GAAA1B,QAAA;cAE5BxC,SAAS,CAAClI,KAAK,GAAG,KAAK;cACvBmI,WAAW,CAACnI,KAAK,GAAG,KAAK;cACzB,CAAAuK,aAAA,GAAA3C,KAAK,CAAC5H,KAAK,cAAAuK,aAAA,eAAXA,aAAA,CAAauB,aAAa,CAAC,cAAc,CAAC;cAC1CtD,OAAO,CAAC6D,KAAK,CAAC,WAAW,EAAA3B,QAAA,CAAA0B,EAAO,CAAC;YAAA;cAAA1B,QAAA,CAAAvF,IAAA;cAEjC;cACAkC,OAAO,CAACrH,KAAK,GAAG,KAAK;cACrB0H,OAAO,CAAC1H,KAAK,GAAG,EAAAwK,aAAA,GAAA5C,KAAK,CAAC5H,KAAK,cAAAwK,aAAA,gBAAAA,aAAA,GAAXA,aAAA,CAAa5C,KAAK,cAAA4C,aAAA,uBAAlBA,aAAA,CAAoB9B,SAAS,KAAI,EAAE;cAAA,OAAAgC,QAAA,CAAAhF,MAAA;YAAA;YAAA;cAAA,OAAAgF,QAAA,CAAApF,IAAA;UAAA;QAAA,GAAAsE,OAAA;MAAA,CAEtD;MAAA,gBApEKF,gBAAgBA,CAAA;QAAA,OAAAC,KAAA,CAAA1D,KAAA,OAAAD,SAAA;MAAA;IAAA,GAoErB;IACD,IAAMsG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/BrE,cAAc,GAAG,IAAI;MACrBC,SAAS,CAAClI,KAAK,GAAG,KAAK;MACvBmI,WAAW,CAACnI,KAAK,GAAG,KAAK;IAC3B,CAAC;IACD,IAAMuM,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B,IAAItE,cAAc,EAAE;QAClBA,cAAc,CAACuE,KAAK,CAAC,CAAC;QACtBtE,SAAS,CAAClI,KAAK,GAAG,KAAK;QACvBmI,WAAW,CAACnI,KAAK,GAAG,KAAK;QACzBwI,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB;MACA6D,kBAAkB,CAAC,CAAC;IACtB,CAAC;IACD,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MAAA,IAAAC,aAAA,EAAAC,mBAAA;MACxB,IAAI,CAACpF,QAAQ,CAACvH,KAAK,CAACqE,MAAM,EAAE,OAAO4C,SAAS,CAAC;QAAE9F,IAAI,EAAE,SAAS;QAAEyL,OAAO,EAAE;MAAU,CAAC,CAAC;MACrFvF,OAAO,CAACrH,KAAK,GAAG,IAAI;MACpB2H,MAAM,CAAC3H,KAAK,GAAG,EAAE;MACjB8H,MAAM,CAAC9H,KAAK,GAAG;QAAE0H,OAAO,EAAE,EAAE;QAAEK,UAAU,EAAE;MAAG,CAAC;MAC9CC,YAAY,CAAChI,KAAK,GAAG;QAAE0H,OAAO,EAAE,EAAE;QAAEK,UAAU,EAAE;MAAG,CAAC;MACpD,CAAA2E,aAAA,GAAA9E,KAAK,CAAC5H,KAAK,cAAA0M,aAAA,eAAXA,aAAA,CAAaG,YAAY,CAAC,CAAC;MAC3B,CAAAF,mBAAA,GAAA9E,WAAW,CAAC7H,KAAK,cAAA2M,mBAAA,eAAjBA,mBAAA,CAAmBE,YAAY,CAAC,CAAC;MACjCN,iBAAiB,CAAC,CAAC;MACnB7C,gBAAgB,CAAC,CAAC;IACpB,CAAC;IAED,IAAMoD,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B1F,KAAK,CAAC2F,MAAM,CAAC,sBAAsB,EAAE;QACnCC,IAAI,EAAE,YAAY;QAClBvI,IAAI,EAAE,eAAe;QACrBwI,GAAG,EAAE,SAAS;QACdnD,IAAI,EAAE;UAAEpC,OAAO,EAAEA,OAAO,CAAC1H;QAAM;MACjC,CAAC,CAAC;IACJ,CAAC;IACD;AACA;AACA;IACA,IAAMkN,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B,IAAMC,QAAQ,GAAGD,IAAI,CAAC1I,IAAI,CAAC4I,SAAS,CAACF,IAAI,CAAC1I,IAAI,CAAC6I,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACpE,IAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACJ,QAAQ,CAAC;MACxD,IAAI,CAACG,MAAM,EAAEtG,SAAS,CAAC;QAAE9F,IAAI,EAAE,SAAS;QAAEyL,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC1B,IAAI,CAAC,GAAG,CAAC;MAAM,CAAC,CAAC;MACjG,OAAOqC,MAAM;IACf,CAAC;IAED,IAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,aAAa,EAAEC,GAAG,EAAK;MAAA,IAAAC,oBAAA;MAC/C,IAAIF,aAAa,aAAbA,aAAa,gBAAAE,oBAAA,GAAbF,aAAa,CAAElC,KAAK,cAAAoC,oBAAA,eAApBA,oBAAA,CAAsBC,gBAAgB,EAAE;QAC1C,IAAMC,QAAQ,GAAG,CAAEJ,aAAa,CAACK,MAAM,GAAGL,aAAa,CAACM,KAAK,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;QAChF1G,QAAQ,CAACvH,KAAK,CAACoC,OAAO,CAAC,UAAC8L,IAAI,EAAK;UAC/B,IAAIA,IAAI,CAACP,GAAG,KAAKA,GAAG,EAAE;YACpBO,IAAI,CAACJ,QAAQ,GAAGzE,QAAQ,CAACyE,QAAQ,CAAC;UACpC;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACD;AACA;AACA;IACA,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAAC,KAAA,EAAiB;MAAA,IAAXjB,IAAI,GAAAiB,KAAA,CAAJjB,IAAI;MACxB,IAAMkB,KAAK,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC5BD,KAAK,CAACE,MAAM,CAAC,MAAM,EAAEpB,IAAI,CAAC;MAC1B,IAAMqB,MAAM,GAAGzH,IAAI,CAAC,CAAC;MACrB,IAAM0H,QAAQ,GAAG;QAAEd,GAAG,EAAEa,MAAM;QAAEE,QAAQ,EAAEvB,IAAI,CAAC1I,IAAI;QAAEgF,IAAI,EAAE0D,IAAI,CAACQ,GAAG;QAAEG,QAAQ,EAAE;MAAE,CAAC;MAClFa,kBAAkB,CAACN,KAAK,EAAEG,MAAM,EAAEC,QAAQ,CAAC;IAC7C,CAAC;IAED,IAAME,kBAAkB;MAAA,IAAAC,KAAA,GAAA7I,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAmK,SAAOC,MAAM,EAAEnB,GAAG,EAAEc,QAAQ;QAAA,IAAAM,qBAAA,EAAAjF,IAAA,EAAAkF,WAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAhB,IAAA;QAAA,OAAA5O,mBAAA,GAAAuB,IAAA,UAAAsO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjK,IAAA,GAAAiK,SAAA,CAAA5L,IAAA;YAAA;cAAA4L,SAAA,CAAAjK,IAAA;cAEnDoC,QAAQ,CAACvH,KAAK,CAACgE,IAAI,CAACyK,QAAQ,CAAC;cAAAW,SAAA,CAAA5L,IAAA;cAAA,OACN4C,GAAG,CAACiJ,YAAY,CAACP,MAAM,EAAErB,gBAAgB,EAAEE,GAAG,CAAC;YAAA;cAAAoB,qBAAA,GAAAK,SAAA,CAAAnM,IAAA;cAA9D6G,IAAI,GAAAiF,qBAAA,CAAJjF,IAAI;cACNkF,WAAW,GAAG,EAAE;cAAAC,SAAA,GAAAK,0BAAA,CACH/H,QAAQ,CAACvH,KAAK;cAAA;gBAAjC,KAAAiP,SAAA,CAAAxN,CAAA,MAAAyN,KAAA,GAAAD,SAAA,CAAArP,CAAA,IAAAiD,IAAA,GAAmC;kBAAxBqL,IAAI,GAAAgB,KAAA,CAAAlP,KAAA;kBACb,IAAIkO,IAAI,CAACP,GAAG,KAAKA,GAAG,EAAE;oBACpBqB,WAAW,CAAChL,IAAI,CAAAuL,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAMzF,IAAI,GAAK2E,QAAQ;sBAAEX,QAAQ,EAAE;oBAAG,EAAE,CAAC;kBAC3D,CAAC,MAAM;oBACLkB,WAAW,CAAChL,IAAI,CAACkK,IAAI,CAAC;kBACxB;gBACF;cAAC,SAAAjC,GAAA;gBAAAgD,SAAA,CAAA1P,CAAA,CAAA0M,GAAA;cAAA;gBAAAgD,SAAA,CAAAzN,CAAA;cAAA;cACD+F,QAAQ,CAACvH,KAAK,GAAGgP,WAAW;cAAAI,SAAA,CAAA5L,IAAA;cAAA;YAAA;cAAA4L,SAAA,CAAAjK,IAAA;cAAAiK,SAAA,CAAAhD,EAAA,GAAAgD,SAAA;cAE5B7H,QAAQ,CAACvH,KAAK,GAAGuH,QAAQ,CAACvH,KAAK,CAACwP,MAAM,CAAC,UAACxN,CAAC;gBAAA,OAAKA,CAAC,CAAC2L,GAAG,KAAKA,GAAG;cAAA,EAAC;YAAA;YAAA;cAAA,OAAAyB,SAAA,CAAA9J,IAAA;UAAA;QAAA,GAAAuJ,QAAA;MAAA,CAE/D;MAAA,gBAhBKF,kBAAkBA,CAAAc,EAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAf,KAAA,CAAA3I,KAAA,OAAAD,SAAA;MAAA;IAAA,GAgBvB;IACD,IAAM4J,WAAW,GAAG,SAAdA,WAAWA,CAAIC,GAAG,EAAK;MAC3BjJ,kBAAkB,CAAC;QACjBnC,IAAI,EAAEqL,OAAO,CAACC,GAAG,CAACC,YAAY;QAC9BxB,MAAM,EAAEqB,GAAG,CAAC5E,EAAE;QACdmC,QAAQ,EAAEyC,GAAG,CAACI,OAAO;QACrBvB,QAAQ,EAAEmB,GAAG,CAACK,gBAAgB;QAC9BC,QAAQ,EAAEN,GAAG,CAACM;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIjD,IAAI,EAAK;MAC1B5F,QAAQ,CAACvH,KAAK,GAAGuH,QAAQ,CAACvH,KAAK,CAACwP,MAAM,CAAC,UAACxN,CAAC;QAAA,OAAKA,CAAC,CAAC2L,GAAG,KAAKR,IAAI,CAACQ,GAAG;MAAA,EAAC;IACnE,CAAC;IACD,IAAM0C,oBAAoB;MAAA,IAAAC,KAAA,GAAAvK,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6L,SAAA;QAAA,IAAAC,qBAAA,EAAA1G,IAAA,EAAA2G,YAAA,EAAAC,KAAA,EAAAC,WAAA,EAAAzC,IAAA;QAAA,OAAA5O,mBAAA,GAAAuB,IAAA,UAAA+P,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1L,IAAA,GAAA0L,SAAA,CAAArN,IAAA;YAAA;cAAAqN,SAAA,CAAArN,IAAA;cAAA,OACJ4C,GAAG,CAACiK,oBAAoB,CAAC;gBAAES,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAA4B;cAAE,CAAC,CAAC;YAAA;cAAAP,qBAAA,GAAAK,SAAA,CAAA5N,IAAA;cAAlG6G,IAAI,GAAA0G,qBAAA,CAAJ1G,IAAI;cACN2G,YAAY,GAAG,EAAE;cACvB,KAASC,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAG5G,IAAI,aAAJA,IAAI,gBAAA6G,WAAA,GAAJ7G,IAAI,CAAEkH,KAAK,cAAAL,WAAA,uBAAXA,WAAA,CAAatM,MAAM,KAAI,CAAC,EAAEqM,KAAK,EAAE,EAAE;gBACvDxC,IAAI,GAAGpE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkH,KAAK,CAACN,KAAK,CAAC;gBAC/B,IAAIxC,IAAI,CAAC+C,OAAO,EAAE;kBAChBR,YAAY,CAACzM,IAAI,CAAC;oBAChBiH,EAAE,EAAEiD,IAAI,CAACjD,EAAE;oBACX+B,IAAI,EAAEkB,IAAI,CAACgD,YAAY;oBACvBzM,IAAI,EAAEyJ,IAAI,CAACiD;kBACb,CAAC,CAAC;kBACF,IAAIV,YAAY,CAACpM,MAAM,KAAK,CAAC,EAAE;oBAC7BmD,OAAO,CAACxH,KAAK,GAAGkO,IAAI,CAACjD,EAAE;kBACzB;gBACF;cACF;cACAzC,OAAO,CAACC,GAAG,CAACgI,YAAY,CAAC;cACzBhJ,SAAS,CAACzH,KAAK,GAAGyQ,YAAY;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAAvL,IAAA;UAAA;QAAA,GAAAiL,QAAA;MAAA,CAC/B;MAAA,gBAlBKF,oBAAoBA,CAAA;QAAA,OAAAC,KAAA,CAAArK,KAAA,OAAAD,SAAA;MAAA;IAAA,GAkBzB;IACDO,SAAS,CAAC,YAAM;MACd8J,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}