<template>
  <div class="SimilarityQuery">
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset">
      <template #search>
        <el-select v-model="termYearId" placeholder="请选择届次" clearable>
          <el-option v-for="item in termYearData" :key="item.termYearId" :label="item.name" :value="item.termYearId" />
        </el-select>
      </template>
    </xyl-search-button>
    <div class="globalTable" :class="{ isGlobalTable: !props.type }">
      <el-table :data="tableData">
        <el-table-column label="相似度" width="100" prop="similar" />
        <el-table-column label="提案标题" min-width="320" show-overflow-tooltip>
          <template #default="scope">
            <el-link @click="handleDetails(scope.row)" type="primary">{{ scope.row.title }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="提案者" min-width="120" prop="suggestionUserName" />
        <el-table-column label="界别" min-width="120">
          <template #default="scope">{{ scope.row.sectorType?.label }}</template>
        </el-table-column>
        <el-table-column label="状态" min-width="100" prop="currentStatus" />
        <el-table-column label="届" width="100" prop="circlesName" />
        <el-table-column label="次" width="90" prop="boutName" />
      </el-table>
    </div>
    <div class="SimilarityQueryButton" v-if="props.type">
      <el-button @click="resetForm" type="primary">返回修改提案</el-button>
      <el-button @click="submitForm" type="primary">直接提交</el-button>
    </div>
    <xyl-popup-window v-model="show" name="相似度详情">
      <SimilarityDetails :id="similarityId" :similarity="similarityNumber" :title="props.title"
        :content="props.content" />
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SimilarityQuery' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
// import { globalLocation } from 'common/config/location'
import { qiankunMicro } from 'common/config/MicroGlobal'
import SimilarityDetails from './SimilarityDetails.vue'
const props = defineProps({
  id: { type: String, default: '' },
  title: { type: String, default: '' },
  content: { type: String, default: '' },
  type: { type: Boolean, default: true }
})
const emit = defineEmits(['callback'])
const termYearId = ref('')
const termYearData = ref([])
const tableData = ref([])

const similarityId = ref('')
const similarityNumber = ref('')
const show = ref(false)

onMounted(() => {
  handleQuery()
  termYearTree()
})

const termYearTree = async () => {
  const res = await api.termYearTree({ termYearType: 'cppcc_member' })
  var { data } = res
  termYearData.value = []
  data.forEach((item) => {
    termYearData.value = [...termYearData.value, ...item.children]
  })
}
const handleQuery = () => {
  tableBodyData()
}
const handleReset = () => {
  termYearId.value = ''
  handleQuery()
}
const tableBodyData = async () => {
  const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区
  const { data } = await api.similarity({
    dirId: '60',
    index: '1_2_',
    id: props.id,
    areaCode: AreaId,
    content: props.content,
    termYearId: termYearId.value,
    status: '政协交办中,党委交办中,政府交办中,法院交办中,检察院交办中,两院交办中,办理中,已答复,满意度测评,重新办理,已办结'
  })
  tableData.value = data || []
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '相似文件详情', path: '/proposal/SuggestDetail', query: { id: item.id } }
  })
}
// const contrast = (item) => {
//   similarityId.value = item.id
//   similarityNumber.value = item.similar
//   show.value = true
//   // window.open(`${globalLocation()}MicroAppContainer?name=proposal&path=TextQueryTool&id=${props.id}&contrastId=${item.id}`, '_blank')
//   // window.open(`${globalLocation()}MicroAppContainer?name=proposal&path=SimilarityDetails&oneId=${props.id}&twoId=${item.id}`, '_blank')
// }
const submitForm = () => {
  emit('callback', true)
}
const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.SimilarityQuery {
  width: 1100px;
  height: calc(85vh - 52px);
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - ((var(--zy-height) * 2) + (var(--zy-distance-four) * 4)));
  }

  .globalTable.isGlobalTable {
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 3)));
  }

  .SimilarityQueryButton {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding: var(--zy-distance-four);

    .zy-el-button+.zy-el-button {
      margin-left: 120px;
    }
  }
}
</style>
