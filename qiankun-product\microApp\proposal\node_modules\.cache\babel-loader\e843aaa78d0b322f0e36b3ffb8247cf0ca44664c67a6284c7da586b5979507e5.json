{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"suggestPrint\",\n  ref: \"printRef\"\n};\nvar _hoisted_2 = {\n  class: \"suggestPrintTextNumber\"\n};\nvar _hoisted_3 = {\n  class: \"suggestPrintContainer\"\n};\nvar _hoisted_4 = {\n  class: \"suggestPrintContainerTitle\"\n};\nvar _hoisted_5 = {\n  class: \"suggestPrintContainerTitleContent\"\n};\nvar _hoisted_6 = {\n  class: \"suggestPrintContainerContent\"\n};\nvar _hoisted_7 = {\n  class: \"suggestPrintContainerContentContent\"\n};\nvar _hoisted_8 = {\n  class: \"suggestPrintContainerContentContentItem\"\n};\nvar _hoisted_9 = {\n  class: \"suggestPrintContainerContentContentItem\"\n};\nvar _hoisted_10 = {\n  class: \"suggestPrintContainerContentContentRight\"\n};\nvar _hoisted_11 = [\"src\"];\nvar _hoisted_12 = {\n  class: \"proposerBasicInformationTable\"\n};\nvar _hoisted_13 = {\n  class: \"cell\"\n};\nvar _hoisted_14 = {\n  class: \"cell\"\n};\nvar _hoisted_15 = {\n  class: \"cell col-span-3\"\n};\nvar _hoisted_16 = {\n  class: \"cell col-span-3\"\n};\nvar _hoisted_17 = {\n  class: \"cell\"\n};\nvar _hoisted_18 = {\n  class: \"cell\"\n};\nvar _hoisted_19 = {\n  class: \"cell\"\n};\nvar _hoisted_20 = {\n  class: \"cell\"\n};\nvar _hoisted_21 = {\n  class: \"cell col-span-3\"\n};\nvar _hoisted_22 = {\n  class: \"proposalTitle\"\n};\nvar _hoisted_23 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.printData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"suggestPrintBody\",\n      key: item.id\n    }, [_createCommentVNode(\" <div class=\\\"suggestPrintType\\\">\\r\\n        <div><span>案号：</span>{{ item.serialNumber }}</div>\\r\\n        <div><span>类别：</span>{{ item.bigThemeName }}</div>\\r\\n      </div> \"), _createCommentVNode(\" <div class=\\\"suggestPrintName\\\"\\r\\n           @click=\\\"handlePrint\\\">{{ item.redHeadTitle }}</div> \"), _createCommentVNode(\" <div class=\\\"suggestPrintItem\\\">\\r\\n        <span class=\\\"suggestPrintItemTitle\\\">提案者11</span>\\r\\n        <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n        <span class=\\\"suggestPrintItemContent\\\">{{ item.suggestUserName }}</span>\\r\\n      </div>\\r\\n      <div class=\\\"suggestPrintItemFlex\\\">\\r\\n        <div class=\\\"suggestPrintItem\\\">\\r\\n          <span class=\\\"suggestPrintItemTitle\\\">委员证号</span>\\r\\n          <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n          <span class=\\\"suggestPrintItemContent\\\">{{ item.cardNumberNpc }}</span>\\r\\n        </div>\\r\\n        <div class=\\\"suggestPrintItem\\\">\\r\\n          <span class=\\\"suggestPrintItemTitle\\\">界别</span>\\r\\n          <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n          <span class=\\\"suggestPrintItemContent\\\">{{ item.delegationName }}</span>\\r\\n        </div>\\r\\n      </div>\\r\\n      <div class=\\\"suggestPrintItem\\\">\\r\\n        <span class=\\\"suggestPrintItemTitle\\\">单位职务</span>\\r\\n        <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n        <span class=\\\"suggestPrintItemContent\\\">{{ item.position }}</span>\\r\\n      </div>\\r\\n      <div class=\\\"suggestPrintItem\\\">\\r\\n        <span class=\\\"suggestPrintItemTitle\\\">通讯地址</span>\\r\\n        <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n        <span class=\\\"suggestPrintItemContent\\\">{{ item.callAddress }}</span>\\r\\n      </div>\\r\\n      <div class=\\\"suggestPrintItemFlex suggestPrintItemInfo\\\">\\r\\n        <div class=\\\"suggestPrintItem\\\">\\r\\n          <span class=\\\"suggestPrintItemTitle\\\">邮政编码</span>\\r\\n          <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n          <span class=\\\"suggestPrintItemContent\\\">{{ item.postcode }}</span>\\r\\n        </div>\\r\\n        <div class=\\\"suggestPrintItem\\\">\\r\\n          <span class=\\\"suggestPrintItemTitle\\\">联系电话</span>\\r\\n          <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n          <span class=\\\"suggestPrintItemContent\\\">{{ item.mobile }}</span>\\r\\n        </div>\\r\\n      </div>\\r\\n      <div class=\\\"suggestPrintItem suggestPrintItemJoin\\\">\\r\\n        <span class=\\\"suggestPrintItemTitle\\\">联名人</span>\\r\\n        <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n        <span class=\\\"suggestPrintItemContent\\\">{{ item.joinUser }}</span>\\r\\n      </div>\\r\\n      <div class=\\\"suggestPrintItem\\\">\\r\\n        <span class=\\\"suggestPrintItemTitle\\\">提案标题</span>\\r\\n        <span class=\\\"suggestPrintItemColon\\\">：</span>\\r\\n        <span class=\\\"suggestPrintItemContent\\\">{{ item.titleone }}</span>\\r\\n      </div>\\r\\n      <div class=\\\"suggestPrintItemTwo\\\"\\r\\n           v-for=\\\"(text, index) in item.titletwoArr\\\"\\r\\n           :key=\\\"index\\\">{{ text }}</div>\\r\\n      <div class=\\\"suggestPrintItemTime\\\">提出时间：{{ item.submitDate }}</div>\\r\\n      <div style=\\\"page-break-after:always\\\"></div>\\r\\n      <div class=\\\"mainModulePageTableName\\\">其他相关信息</div>\\r\\n      <table>\\r\\n        <tbody>\\r\\n          <tr v-if=\\\"item.suggestSurveyTypeName\\\">\\r\\n            <td :rowspan=\\\"rowspan(item)\\\">相关情况</td>\\r\\n            <td>{{ item.suggestSurveyTypeName }}</td>\\r\\n            <td>{{ item.suggestSurveyTypeView }}</td>\\r\\n          </tr>\\r\\n          <tr v-if=\\\"item.notHandleTimeTypeName\\\">\\r\\n            <td :rowspan=\\\"rowspan(item)\\\" v-if=\\\"!item.suggestSurveyTypeName\\\">相关情况</td>\\r\\n            <td>{{ item.notHandleTimeTypeName }}</td>\\r\\n            <td>{{ item.notHandleTimeTypeView }}</td>\\r\\n          </tr>\\r\\n          <tr v-if=\\\"item.suggestOpenTypeName\\\">\\r\\n            <td :rowspan=\\\"rowspan(item)\\\" v-if=\\\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName\\\">相关情况</td>\\r\\n            <td>{{ item.suggestOpenTypeName }}</td>\\r\\n            <td>{{ item.suggestOpenTypeView }}</td>\\r\\n          </tr>\\r\\n          <tr v-if=\\\"item.isMakeMineJobName\\\">\\r\\n            <td :rowspan=\\\"rowspan(item)\\\"\\r\\n              v-if=\\\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName\\\">相关情况</td>\\r\\n            <td>{{ item.isMakeMineJobName }}</td>\\r\\n            <td>{{ item.isMakeMineJobView }}</td>\\r\\n          </tr>\\r\\n          <tr v-if=\\\"item.isHopeEnhanceTalkName\\\">\\r\\n            <td :rowspan=\\\"rowspan(item)\\\"\\r\\n              v-if=\\\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName\\\">\\r\\n              相关情况</td>\\r\\n            <td>{{ item.isHopeEnhanceTalkName }}</td>\\r\\n            <td>{{ item.isHopeEnhanceTalkView }}</td>\\r\\n          </tr>\\r\\n          <tr v-if=\\\"item.isNeedPaperAnswerName\\\">\\r\\n            <td\\r\\n              v-if=\\\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName && !item.isHopeEnhanceTalkName\\\">\\r\\n              相关情况</td>\\r\\n            <td>{{ item.isNeedPaperAnswerName }}</td>\\r\\n            <td>{{ item.isNeedPaperAnswerView }}</td>\\r\\n          </tr>\\r\\n          <tr>\\r\\n            <td>希望送交的承办单位（仅供参考）</td>\\r\\n            <td colspan=\\\"2\\\">{{ item.hopeHandleOfficeName }}</td>\\r\\n          </tr>\\r\\n        </tbody>\\r\\n      </table>\\r\\n      <div class=\\\"suggestPrintContent\\\"\\r\\n           v-html=\\\"item.content\\\"></div>\\r\\n      <div style=\\\"page-break-after:always\\\"></div>\\r\\n      <div class=\\\"mainModulePageTableName\\\">联名人信息表</div>\\r\\n      <table>\\r\\n        <tbody>\\r\\n          <tr>\\r\\n            <td>姓名</td>\\r\\n            <td>界别</td>\\r\\n            <td>委员证号</td>\\r\\n            <td>单位职务</td>\\r\\n            <td>联系方式</td>\\r\\n            <td>通讯地址</td>\\r\\n          </tr>\\r\\n          <tr v-for=\\\"row in item.joinUsers\\\" :key=\\\"row.userId\\\">\\r\\n            <td>{{ row.userName }}</td>\\r\\n            <td>{{ row.sector }}</td>\\r\\n            <td>{{ row.cardNumber }}</td>\\r\\n            <td>{{ row.position }}</td>\\r\\n            <td>{{ row.mobile }}</td>\\r\\n            <td>{{ row.callAddress }}</td>\\r\\n          </tr>\\r\\n        </tbody>\\r\\n      </table>\\r\\n      <div class=\\\"mainModulePageTableName\\\">提案联系人</div>\\r\\n      <table>\\r\\n        <tbody>\\r\\n          <tr>\\r\\n            <td colspan=\\\"2\\\">姓名</td>\\r\\n            <td colspan=\\\"2\\\">联系电话</td>\\r\\n            <td colspan=\\\"3\\\">通讯地址</td>\\r\\n          </tr>\\r\\n          <tr v-for=\\\"row in item.contacters\\\" :key=\\\"row.id\\\">\\r\\n            <td colspan=\\\"2\\\">{{ row.contacterName }}</td>\\r\\n            <td colspan=\\\"2\\\">{{ row.contacterMobile }}</td>\\r\\n            <td colspan=\\\"3\\\">{{ row.contacterAddress }}</td>\\r\\n          </tr>\\r\\n        </tbody>\\r\\n      </table>\\r\\n      <div class=\\\"mainModulePageTableName\\\">审查和办理信息表</div>\\r\\n      <table>\\r\\n        <tbody>\\r\\n          <tr>\\r\\n            <td>本提案目前状态</td>\\r\\n            <td colspan=\\\"2\\\">{{ item.currentStatus }}</td>\\r\\n          </tr>\\r\\n          <tr>\\r\\n            <td>审查情况</td>\\r\\n            <td colspan=\\\"2\\\">{{ item.verifyStatus }}</td>\\r\\n          </tr>\\r\\n          <tr v-if=\\\"item.mainHandleOffice\\\">\\r\\n            <td rowspan=\\\"2\\\">办理单位</td>\\r\\n            <td>主办</td>\\r\\n            <td>{{ item.mainHandleOffice }}</td>\\r\\n          </tr>\\r\\n          <tr v-if=\\\"item.mainHandleOffice\\\">\\r\\n            <td>协办</td>\\r\\n            <td>{{ item.assistHandleOffice }}</td>\\r\\n          </tr>\\r\\n          <tr v-if=\\\"item.publishHandleOffice\\\">\\r\\n            <td>办理单位</td>\\r\\n            <td>分办</td>\\r\\n            <td>{{ item.publishHandleOffice }}</td>\\r\\n          </tr>\\r\\n        </tbody>\\r\\n      </table>\\r\\n      <div style=\\\"page-break-after:always\\\"></div> \"), _createElementVNode(\"div\", {\n      class: \"suggestPrintName\",\n      onClick: $setup.handlePrint\n    }, \"中国人民政治协商会议\" + _toDisplayString($setup.areaName) + \"委员会\" + _toDisplayString(item.circlesType.name) + _toDisplayString(item.boutType.name) + \"会议\", 1 /* TEXT */), _cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n      class: \"suggestPrintText\"\n    }, \"提  案\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, \"提案编号：第\" + _toDisplayString(item.serialNumber) + \"号\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n      class: \"suggestPrintContainerTitleLabel\"\n    }, \"提案题目\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(item.title), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n      class: \"suggestPrintContainerContentLabel\"\n    }, \"交办意见\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, \"主办单位：\" + _toDisplayString(item.mainHandleOffice) + _toDisplayString(item.publishHandleOffice), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, \"协办单位：\" + _toDisplayString(item.assistHandleOffice), 1 /* TEXT */), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n      style: {\n        \"height\": \"80px\"\n      }\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.areaName) + \"政协提案委员会 \", 1 /* TEXT */), _createElementVNode(\"img\", {\n      src: $setup.areaLogo,\n      alt: \"Stamp\",\n      class: \"suggestPrintContainerContentContentStamp\"\n    }, null, 8 /* PROPS */, _hoisted_11), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n      class: \"suggestPrintContainerContentContentTime\"\n    }, \"年 月 日\", -1 /* HOISTED */))])])]), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n      class: \"proposerBasicInformationTitle\"\n    }, \"提案者基本情况\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createCommentVNode(\" 第一行 \"), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"第一提案者\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, _toDisplayString(item.suggestUserName), 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"提案者类型\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, _toDisplayString(item.teamOfficeTheme), 1 /* TEXT */), _createCommentVNode(\" 第二行 \"), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"工作单位及职务\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, _toDisplayString(item.position), 1 /* TEXT */), _createCommentVNode(\" 第三行 \"), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"通讯地址\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, _toDisplayString(item.callAddress), 1 /* TEXT */), _createCommentVNode(\" 第四行 \"), _cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"办公电话\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_17, _toDisplayString(item.officePhone), 1 /* TEXT */), _cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"手机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, _toDisplayString(item.mobile), 1 /* TEXT */), _createCommentVNode(\" 第五行 \"), _cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"界别\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_19, _toDisplayString(item.delegationName), 1 /* TEXT */), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"党派\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_20, _toDisplayString(item.party.name), 1 /* TEXT */), _createCommentVNode(\" 第六行 \"), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n      class: \"cell cell-title\"\n    }, \"联名提案者\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, _toDisplayString(item.joinUser), 1 /* TEXT */)]), _cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n      class: \"remarks\"\n    }, \" 附注：1、承办单位应在提案委员会交办提案之日起3个月内办结提案并答复提案者，特殊情况不得超过6个月。2、主办单位应在所主办提案全部答复提案者并填写完成《政协提案答复情况征询意见表》后10日内，整理答复意见文件和征询意见表，形成办理工作总结，一并报送市政协提案委和市委市政府督查室。 \", -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_22, _toDisplayString(item.title), 1 /* TEXT */), _createElementVNode(\"div\", {\n      class: \"proposalContent\",\n      innerHTML: item.content\n    }, null, 8 /* PROPS */, _hoisted_23)])]);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_Fragment", "_renderList", "$setup", "printData", "item", "key", "id", "_createCommentVNode", "_createElementVNode", "onClick", "handlePrint", "_toDisplayString", "areaName", "circlesType", "name", "boutType", "_hoisted_2", "serialNumber", "_hoisted_3", "_hoisted_4", "_hoisted_5", "title", "_hoisted_6", "_hoisted_7", "_hoisted_8", "mainHandleOffice", "publishHandleOffice", "_hoisted_9", "assistHandleOffice", "style", "_hoisted_10", "src", "areaLogo", "alt", "_hoisted_11", "_hoisted_12", "_hoisted_13", "suggestUserName", "_hoisted_14", "teamOfficeTheme", "_hoisted_15", "position", "_hoisted_16", "call<PERSON>dd<PERSON>", "_hoisted_17", "officePhone", "_hoisted_18", "mobile", "_hoisted_19", "delegation<PERSON>ame", "_hoisted_20", "party", "_hoisted_21", "joinUser", "_hoisted_22", "innerHTML", "content", "_hoisted_23"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggestPrint\\suggestPrint.vue"], "sourcesContent": ["<template>\r\n  <div class=\"suggestPrint\" ref=\"printRef\">\r\n    <div class=\"suggestPrintBody\" v-for=\"item in printData\" :key=\"item.id\">\r\n      <!-- <div class=\"suggestPrintType\">\r\n        <div><span>案号：</span>{{ item.serialNumber }}</div>\r\n        <div><span>类别：</span>{{ item.bigThemeName }}</div>\r\n      </div> -->\r\n      <!-- <div class=\"suggestPrintName\"\r\n           @click=\"handlePrint\">{{ item.redHeadTitle }}</div> -->\r\n      <!-- <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">提案者11</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.suggestUserName }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemFlex\">\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">委员证号</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.cardNumberNpc }}</span>\r\n        </div>\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">界别</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.delegationName }}</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">单位职务</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.position }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">通讯地址</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.callAddress }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemFlex suggestPrintItemInfo\">\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">邮政编码</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.postcode }}</span>\r\n        </div>\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">联系电话</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.mobile }}</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"suggestPrintItem suggestPrintItemJoin\">\r\n        <span class=\"suggestPrintItemTitle\">联名人</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.joinUser }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">提案标题</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.titleone }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemTwo\"\r\n           v-for=\"(text, index) in item.titletwoArr\"\r\n           :key=\"index\">{{ text }}</div>\r\n      <div class=\"suggestPrintItemTime\">提出时间：{{ item.submitDate }}</div>\r\n      <div style=\"page-break-after:always\"></div>\r\n      <div class=\"mainModulePageTableName\">其他相关信息</div>\r\n      <table>\r\n        <tbody>\r\n          <tr v-if=\"item.suggestSurveyTypeName\">\r\n            <td :rowspan=\"rowspan(item)\">相关情况</td>\r\n            <td>{{ item.suggestSurveyTypeName }}</td>\r\n            <td>{{ item.suggestSurveyTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.notHandleTimeTypeName\">\r\n            <td :rowspan=\"rowspan(item)\" v-if=\"!item.suggestSurveyTypeName\">相关情况</td>\r\n            <td>{{ item.notHandleTimeTypeName }}</td>\r\n            <td>{{ item.notHandleTimeTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.suggestOpenTypeName\">\r\n            <td :rowspan=\"rowspan(item)\" v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName\">相关情况</td>\r\n            <td>{{ item.suggestOpenTypeName }}</td>\r\n            <td>{{ item.suggestOpenTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isMakeMineJobName\">\r\n            <td :rowspan=\"rowspan(item)\"\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName\">相关情况</td>\r\n            <td>{{ item.isMakeMineJobName }}</td>\r\n            <td>{{ item.isMakeMineJobView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isHopeEnhanceTalkName\">\r\n            <td :rowspan=\"rowspan(item)\"\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName\">\r\n              相关情况</td>\r\n            <td>{{ item.isHopeEnhanceTalkName }}</td>\r\n            <td>{{ item.isHopeEnhanceTalkView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isNeedPaperAnswerName\">\r\n            <td\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName && !item.isHopeEnhanceTalkName\">\r\n              相关情况</td>\r\n            <td>{{ item.isNeedPaperAnswerName }}</td>\r\n            <td>{{ item.isNeedPaperAnswerView }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>希望送交的承办单位（仅供参考）</td>\r\n            <td colspan=\"2\">{{ item.hopeHandleOfficeName }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"suggestPrintContent\"\r\n           v-html=\"item.content\"></div>\r\n      <div style=\"page-break-after:always\"></div>\r\n      <div class=\"mainModulePageTableName\">联名人信息表</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td>姓名</td>\r\n            <td>界别</td>\r\n            <td>委员证号</td>\r\n            <td>单位职务</td>\r\n            <td>联系方式</td>\r\n            <td>通讯地址</td>\r\n          </tr>\r\n          <tr v-for=\"row in item.joinUsers\" :key=\"row.userId\">\r\n            <td>{{ row.userName }}</td>\r\n            <td>{{ row.sector }}</td>\r\n            <td>{{ row.cardNumber }}</td>\r\n            <td>{{ row.position }}</td>\r\n            <td>{{ row.mobile }}</td>\r\n            <td>{{ row.callAddress }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"mainModulePageTableName\">提案联系人</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td colspan=\"2\">姓名</td>\r\n            <td colspan=\"2\">联系电话</td>\r\n            <td colspan=\"3\">通讯地址</td>\r\n          </tr>\r\n          <tr v-for=\"row in item.contacters\" :key=\"row.id\">\r\n            <td colspan=\"2\">{{ row.contacterName }}</td>\r\n            <td colspan=\"2\">{{ row.contacterMobile }}</td>\r\n            <td colspan=\"3\">{{ row.contacterAddress }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"mainModulePageTableName\">审查和办理信息表</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td>本提案目前状态</td>\r\n            <td colspan=\"2\">{{ item.currentStatus }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>审查情况</td>\r\n            <td colspan=\"2\">{{ item.verifyStatus }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.mainHandleOffice\">\r\n            <td rowspan=\"2\">办理单位</td>\r\n            <td>主办</td>\r\n            <td>{{ item.mainHandleOffice }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.mainHandleOffice\">\r\n            <td>协办</td>\r\n            <td>{{ item.assistHandleOffice }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.publishHandleOffice\">\r\n            <td>办理单位</td>\r\n            <td>分办</td>\r\n            <td>{{ item.publishHandleOffice }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div style=\"page-break-after:always\"></div> -->\r\n\r\n      <div class=\"suggestPrintName\" @click=\"handlePrint\">中国人民政治协商会议{{ areaName }}委员会{{ item.circlesType.name\r\n      }}{{ item.boutType.name }}会议</div>\r\n      <div class=\"suggestPrintText\">提&nbsp;&nbsp;案</div>\r\n      <div class=\"suggestPrintTextNumber\">提案编号：第{{ item.serialNumber }}号</div>\r\n      <div class=\"suggestPrintContainer\">\r\n        <div class=\"suggestPrintContainerTitle\">\r\n          <div class=\"suggestPrintContainerTitleLabel\">提案题目</div>\r\n          <div class=\"suggestPrintContainerTitleContent\">{{ item.title }}</div>\r\n        </div>\r\n        <div class=\"suggestPrintContainerContent\">\r\n          <div class=\"suggestPrintContainerContentLabel\">交办意见</div>\r\n          <div class=\"suggestPrintContainerContentContent\">\r\n            <div class=\"suggestPrintContainerContentContentItem\">主办单位：{{ item.mainHandleOffice\r\n            }}{{ item.publishHandleOffice }}</div>\r\n            <div class=\"suggestPrintContainerContentContentItem\">协办单位：{{ item.assistHandleOffice }}</div>\r\n            <div style=\"height: 80px;\"></div>\r\n            <div class=\"suggestPrintContainerContentContentRight\">\r\n              {{ areaName }}政协提案委员会\r\n            </div>\r\n            <img :src=\"areaLogo\" alt=\"Stamp\" class=\"suggestPrintContainerContentContentStamp\">\r\n            <div class=\"suggestPrintContainerContentContentTime\">年 月 日</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposerBasicInformationTitle\">提案者基本情况</div>\r\n      <div class=\"proposerBasicInformationTable\">\r\n        <!-- 第一行 -->\r\n        <div class=\"cell cell-title\">第一提案者</div>\r\n        <div class=\"cell\">{{ item.suggestUserName }}</div>\r\n        <div class=\"cell cell-title\">提案者类型</div>\r\n        <div class=\"cell\">{{ item.teamOfficeTheme }}</div>\r\n\r\n        <!-- 第二行 -->\r\n        <div class=\"cell cell-title\">工作单位及职务</div>\r\n        <div class=\"cell col-span-3\">{{ item.position }}</div>\r\n\r\n        <!-- 第三行 -->\r\n        <div class=\"cell cell-title\">通讯地址</div>\r\n        <div class=\"cell col-span-3\">{{ item.callAddress }}</div>\r\n\r\n        <!-- 第四行 -->\r\n        <div class=\"cell cell-title\">办公电话</div>\r\n        <div class=\"cell\">{{ item.officePhone }}</div>\r\n        <div class=\"cell cell-title\">手机</div>\r\n        <div class=\"cell\">{{ item.mobile }}</div>\r\n\r\n        <!-- 第五行 -->\r\n        <div class=\"cell cell-title\">界别</div>\r\n        <div class=\"cell\">{{ item.delegationName }}</div>\r\n        <div class=\"cell cell-title\">党派</div>\r\n        <div class=\"cell\">{{ item.party.name }}</div>\r\n\r\n        <!-- 第六行 -->\r\n        <div class=\"cell cell-title\">联名提案者</div>\r\n        <div class=\"cell col-span-3\">{{ item.joinUser }}</div>\r\n      </div>\r\n      <div class=\"remarks\">\r\n        附注：1、承办单位应在提案委员会交办提案之日起3个月内办结提案并答复提案者，特殊情况不得超过6个月。2、主办单位应在所主办提案全部答复提案者并填写完成《政协提案答复情况征询意见表》后10日内，整理答复意见文件和征询意见表，形成办理工作总结，一并报送市政协提案委和市委市政府督查室。\r\n      </div>\r\n      <div>\r\n        <div class=\"proposalTitle\">{{ item.title }}</div>\r\n        <div class=\"proposalContent\" v-html=\"item.content\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'suggestPrint' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick } from 'vue'\r\nimport { filterTableData } from '@/assets/js/suggestExportWord'\r\nimport { Print } from 'common/js/print'\r\nimport { user } from 'common/js/system_var.js'\r\nconst props = defineProps({ params: { type: Object, default: () => ({}) } })\r\nconst emit = defineEmits(['callback'])\r\nconst printData = ref([])\r\nconst printRef = ref()\r\nconst areaName = ref('')\r\nconst areaLogo = ref('')\r\nconst handlePrint = () => {\r\n  Print.init(printRef.value)\r\n}\r\nonMounted(() => {\r\n  const areaId = user.value.areaId\r\n  if (areaId == '370500') {\r\n    areaName.value = '东营市'\r\n    areaLogo.value = require('../../assets/img/dongyingshizhang.png')\r\n  } else if (areaId == '370502') {\r\n    areaName.value = '东营区'\r\n    areaLogo.value = require('../../assets/img/daitianjia.png')\r\n  } else if (areaId == '370503') {\r\n    areaName.value = '河口区'\r\n    areaLogo.value = require('../../assets/img/daitianjia.png')\r\n  } else if (areaId == '370505') {\r\n    areaName.value = '垦利区'\r\n    areaLogo.value = require('../../assets/img/daitianjia.png')\r\n  } else if (areaId == '370522') {\r\n    areaName.value = '利津县'\r\n    areaLogo.value = require('../../assets/img/daitianjia.png')\r\n  } else if (areaId == '370523') {\r\n    areaName.value = '广饶县'\r\n    areaLogo.value = require('../../assets/img/guangraoxianzhang.png')\r\n  } else {\r\n    areaName.value = ''\r\n    areaLogo.value = ''\r\n  }\r\n\r\n  suggestionWord()\r\n})\r\n// const rowspan = (item) => {\r\n//   let rowspannum = 0\r\n//   if (item.suggestSurveyTypeName) { rowspannum += 1 }\r\n//   if (item.notHandleTimeTypeName) { rowspannum += 1 }\r\n//   if (item.suggestOpenTypeName) { rowspannum += 1 }\r\n//   if (item.isMakeMineJobName) { rowspannum += 1 }\r\n//   if (item.isHopeEnhanceTalkName) { rowspannum += 1 }\r\n//   if (item.isNeedPaperAnswerName) { rowspannum += 1 }\r\n//   return rowspannum\r\n// }\r\nconst suggestionWord = async () => {\r\n  const { data } = await api.suggestionWord(props.params)\r\n  if (data.length) {\r\n    printData.value = []\r\n    for (let index = 0; index < data.length; index++) {\r\n      printData.value.push(filterTableData(data[index]))\r\n    }\r\n    nextTick(() => {\r\n      handlePrint()\r\n      emit('callback')\r\n    })\r\n  }\r\n}\r\ndefineExpose({ print: handlePrint })\r\n</script>\r\n<style lang=\"scss\">\r\n@font-face {\r\n  font-family: \"FZXiaoBiaoSong\";\r\n  src: url(../../assets/img/FZXiaoBiaoSong-B05S.ttf);\r\n}\r\n\r\n.suggestPrint {\r\n  width: 100%;\r\n\r\n  .suggestPrintBody {\r\n    width: 100%;\r\n\r\n    // .suggestPrintType {\r\n    //   font-size: 16pt;\r\n    //   font-weight: bold;\r\n    //   padding-bottom: 40pt;\r\n    //   display: flex;\r\n    //   justify-content: space-between;\r\n\r\n    //   span {\r\n    //     color: red;\r\n    //   }\r\n    // }\r\n\r\n    // .suggestPrintName {\r\n    //   color: red;\r\n    //   font-size: 24pt;\r\n    //   line-height: 1.2;\r\n    //   font-weight: bold;\r\n    //   text-align: center;\r\n    //   padding-bottom: 60pt;\r\n    // }\r\n\r\n    // .suggestPrintItem {\r\n    //   width: 100%;\r\n    //   display: flex;\r\n    //   font-size: 16pt;\r\n    //   line-height: 1.5;\r\n    //   padding-bottom: 8pt;\r\n\r\n    //   span {\r\n    //     display: inline-block;\r\n    //   }\r\n\r\n    //   .suggestPrintItemTitle {\r\n    //     width: 92px;\r\n    //     color: red;\r\n    //     font-weight: bold;\r\n    //     text-align: justify;\r\n    //     text-align-last: justify;\r\n    //   }\r\n\r\n    //   .suggestPrintItemColon {\r\n    //     width: 16px;\r\n    //     color: red;\r\n    //   }\r\n\r\n    //   .suggestPrintItemContent {\r\n    //     width: calc(100% - 108px);\r\n    //     border-bottom: 1px solid #262626;\r\n    //   }\r\n    // }\r\n\r\n    // .suggestPrintItemInfo {\r\n    //   padding-bottom: 54pt;\r\n    // }\r\n\r\n    // .suggestPrintItemJoin {\r\n    //   padding-bottom: 32pt;\r\n    // }\r\n\r\n    // .suggestPrintItemTwo {\r\n    //   width: 100%;\r\n    //   font-size: 16pt;\r\n    //   line-height: 1.5;\r\n    //   min-height: calc(16pt * 1.5);\r\n    //   border-bottom: 1px solid #262626;\r\n    //   margin-bottom: 8pt;\r\n    // }\r\n\r\n    // .suggestPrintItemFlex {\r\n    //   width: 100%;\r\n    //   display: flex;\r\n    //   justify-content: space-between;\r\n\r\n    //   .suggestPrintItem {\r\n    //     width: 46%;\r\n    //   }\r\n    // }\r\n\r\n    // .suggestPrintItemTime {\r\n    //   color: red;\r\n    //   text-align: center;\r\n    //   font-size: 14pt;\r\n    //   line-height: 1.5;\r\n    //   padding-top: 80pt;\r\n    //   font-family: \"Times New Roman\";\r\n    // }\r\n\r\n    // .mainModulePageTableName {\r\n    //   font-size: 14pt;\r\n    //   line-height: 1.5;\r\n    //   text-align: center;\r\n    //   margin-bottom: 7pt;\r\n    // }\r\n\r\n    // table {\r\n    //   width: 100%;\r\n    //   table-layout: fixed;\r\n    //   word-break: break-all;\r\n    //   border-collapse: collapse;\r\n    //   margin-bottom: 32pt;\r\n\r\n    //   tr {\r\n    //     page-break-inside: avoid;\r\n\r\n    //     td {\r\n    //       text-align: center;\r\n    //       line-height: 1.5;\r\n    //       padding: 8px;\r\n    //       font-size: 12pt;\r\n    //       border: 1px solid #000;\r\n    //       font-family: \"宋体\";\r\n    //       display: table-cell;\r\n    //       vertical-align: middle;\r\n    //     }\r\n    //   }\r\n    // }\r\n\r\n    // .suggestPrintContent {\r\n    //   overflow: hidden;\r\n    //   line-height: var(--zy-line-height);\r\n\r\n    //   img,\r\n    //   video {\r\n    //     max-width: 100%;\r\n    //     height: auto !important;\r\n    //   }\r\n\r\n    //   table {\r\n    //     border-collapse: collapse;\r\n    //     border-spacing: 0;\r\n\r\n    //     tr {\r\n    //       page-break-inside: avoid;\r\n    //     }\r\n    //   }\r\n    // }\r\n\r\n    .suggestPrintName {\r\n      font-size: 16pt;\r\n      line-height: 1.2;\r\n      text-align: center;\r\n      padding-bottom: 20pt;\r\n      font-family: 宋体;\r\n    }\r\n\r\n    .suggestPrintText {\r\n      font-size: 42pt;\r\n      text-align: center;\r\n      padding-bottom: 10pt;\r\n      font-weight: bold;\r\n      color: #000;\r\n      font-family: 宋体;\r\n    }\r\n\r\n    .suggestPrintTextNumber {\r\n      font-size: 14pt;\r\n      text-align: center;\r\n      padding-bottom: 10pt;\r\n      font-family: 宋体;\r\n    }\r\n\r\n    .suggestPrintContainer {\r\n      width: 600px;\r\n      border: 1px solid black;\r\n      display: grid;\r\n      grid-template-rows: auto 1fr;\r\n      font-family: Arial, sans-serif;\r\n\r\n      .suggestPrintContainerTitle {\r\n        display: flex;\r\n        border-bottom: 1px solid black;\r\n\r\n        .suggestPrintContainerTitle:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .suggestPrintContainerTitleLabel,\r\n        .suggestPrintContainerTitleContent {\r\n          padding: 15px;\r\n        }\r\n\r\n        .suggestPrintContainerTitleLabel {\r\n          width: 120px;\r\n          border-right: 1px solid black;\r\n          text-align: center;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 15pt;\r\n          font-family: 黑体;\r\n        }\r\n\r\n        .suggestPrintContainerTitleContent {\r\n          flex: 1;\r\n          position: relative;\r\n          font-size: 14pt;\r\n          font-family: 宋体;\r\n        }\r\n      }\r\n\r\n      .suggestPrintContainerContent {\r\n        display: flex;\r\n\r\n        .suggestPrintContainerContentLabel,\r\n        .suggestPrintContainerContentContent {\r\n          padding: 15px;\r\n        }\r\n\r\n        .suggestPrintContainerContentLabel {\r\n          width: 120px;\r\n          border-right: 1px solid black;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 15pt;\r\n          font-family: 黑体;\r\n        }\r\n\r\n        .suggestPrintContainerContentContent {\r\n          flex: 1;\r\n          position: relative;\r\n\r\n          .suggestPrintContainerContentContentItem {\r\n            margin: 10px 0;\r\n            font-size: 14pt;\r\n            font-family: 宋体;\r\n          }\r\n\r\n          .suggestPrintContainerContentContentRight {\r\n            position: absolute;\r\n            right: 10px;\r\n            bottom: 25px;\r\n            font-size: 14pt;\r\n            font-family: 宋体;\r\n          }\r\n\r\n          .suggestPrintContainerContentContentStamp {\r\n            position: absolute;\r\n            right: 0px;\r\n            bottom: 10px;\r\n            width: 130px;\r\n            opacity: 0.6;\r\n          }\r\n\r\n          .suggestPrintContainerContentContentTime {\r\n            position: absolute;\r\n            right: 10px;\r\n            bottom: 0px;\r\n            font-size: 14pt;\r\n            font-family: 宋体;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .proposerBasicInformationTitle {\r\n      font-size: 18pt;\r\n      font-family: 黑体;\r\n      text-align: center;\r\n      margin: 10px 0;\r\n    }\r\n\r\n    .proposerBasicInformationTable {\r\n      display: grid;\r\n      width: 600px;\r\n      border: 1px solid black;\r\n      grid-template-columns: 160px 170px 120px 150px;\r\n      font-family: Arial, sans-serif;\r\n\r\n      .cell {\r\n        border: 1px solid black;\r\n        padding: 8px;\r\n        text-align: center;\r\n        font-size: 14pt;\r\n        font-family: 宋体;\r\n      }\r\n\r\n      table {\r\n        max-width: 100%;\r\n        border-collapse: collapse;\r\n        border-spacing: 0;\r\n\r\n        tr {\r\n          page-break-inside: avoid;\r\n        }\r\n      }\r\n\r\n      .col-span-3 {\r\n        grid-column: span 3;\r\n        text-align: left;\r\n      }\r\n\r\n      .col-span-2 {\r\n        grid-column: span 2;\r\n        text-align: left;\r\n      }\r\n    }\r\n\r\n    .remarks {\r\n      font-size: 12pt;\r\n      line-height: 1.6;\r\n      font-family: 楷体;\r\n    }\r\n\r\n    .proposalTitle {\r\n      text-align: center;\r\n      font-size: 22pt;\r\n      font-family: FZXiaoBiaoSong;\r\n      margin-top: 50px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,cAAc;EAACC,GAAG,EAAC;;;EAiLrBD,KAAK,EAAC;AAAwB;;EAC9BA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAA4B;;EAEhCA,KAAK,EAAC;AAAmC;;EAE3CA,KAAK,EAAC;AAA8B;;EAElCA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAyC;;EAE/CA,KAAK,EAAC;AAAyC;;EAE/CA,KAAK,EAAC;AAA0C;kBA/LjE;;EAwMWA,KAAK,EAAC;AAA+B;;EAGnCA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAIZA,KAAK,EAAC;AAAiB;;EAIvBA,KAAK,EAAC;AAAiB;;EAIvBA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAIZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAIZA,KAAK,EAAC;AAAiB;;EAMvBA,KAAK,EAAC;AAAe;kBA3OlC;;uBACEE,mBAAA,CA8OM,OA9ONC,UA8OM,I,kBA7OJD,mBAAA,CA4OME,SAAA,QA9OVC,WAAA,CAEiDC,MAAA,CAAAC,SAAS,EAF1D,UAEyCC,IAAI;yBAAzCN,mBAAA,CA4OM;MA5ODF,KAAK,EAAC,kBAAkB;MAA4BS,GAAG,EAAED,IAAI,CAACE;QACjEC,mBAAA,kLAGU,EACVA,mBAAA,wGAC2D,EAC3DA,mBAAA,gxOAoK+C,EAE/CC,mBAAA,CACkC;MAD7BZ,KAAK,EAAC,kBAAkB;MAAEa,OAAK,EAAEP,MAAA,CAAAQ;OAAa,YAAU,GAAAC,gBAAA,CAAGT,MAAA,CAAAU,QAAQ,IAAG,KAAG,GAAAD,gBAAA,CAAGP,IAAI,CAACS,WAAW,CAACC,IAAI,IAAAH,gBAAA,CACjGP,IAAI,CAACW,QAAQ,CAACD,IAAI,IAAG,IAAE,iB,4BAC5BN,mBAAA,CAAkD;MAA7CZ,KAAK,EAAC;IAAkB,GAAC,MAAc,sBAC5CY,mBAAA,CAAwE,OAAxEQ,UAAwE,EAApC,QAAM,GAAAL,gBAAA,CAAGP,IAAI,CAACa,YAAY,IAAG,GAAC,iBAClET,mBAAA,CAmBM,OAnBNU,UAmBM,GAlBJV,mBAAA,CAGM,OAHNW,UAGM,G,0BAFJX,mBAAA,CAAuD;MAAlDZ,KAAK,EAAC;IAAiC,GAAC,MAAI,sBACjDY,mBAAA,CAAqE,OAArEY,UAAqE,EAAAT,gBAAA,CAAnBP,IAAI,CAACiB,KAAK,iB,GAE9Db,mBAAA,CAaM,OAbNc,UAaM,G,0BAZJd,mBAAA,CAAyD;MAApDZ,KAAK,EAAC;IAAmC,GAAC,MAAI,sBACnDY,mBAAA,CAUM,OAVNe,UAUM,GATJf,mBAAA,CACsC,OADtCgB,UACsC,EADe,OAAK,GAAAb,gBAAA,CAAGP,IAAI,CAACqB,gBAAgB,IAAAd,gBAAA,CAC7EP,IAAI,CAACsB,mBAAmB,kBAC7BlB,mBAAA,CAA6F,OAA7FmB,UAA6F,EAAxC,OAAK,GAAAhB,gBAAA,CAAGP,IAAI,CAACwB,kBAAkB,kB,0BACpFpB,mBAAA,CAAiC;MAA5BqB,KAAqB,EAArB;QAAA;MAAA;IAAqB,6BAC1BrB,mBAAA,CAEM,OAFNsB,WAEM,EAAAnB,gBAAA,CADDT,MAAA,CAAAU,QAAQ,IAAG,UAChB,iBACAJ,mBAAA,CAAkF;MAA5EuB,GAAG,EAAE7B,MAAA,CAAA8B,QAAQ;MAAEC,GAAG,EAAC,OAAO;MAACrC,KAAK,EAAC;4BAlMnDsC,WAAA,G,0BAmMY1B,mBAAA,CAAgE;MAA3DZ,KAAK,EAAC;IAAyC,GAAC,OAAK,qB,mCAIhEY,mBAAA,CAAwD;MAAnDZ,KAAK,EAAC;IAA+B,GAAC,SAAO,sBAClDY,mBAAA,CA8BM,OA9BN2B,WA8BM,GA7BJ5B,mBAAA,SAAY,E,0BACZC,mBAAA,CAAwC;MAAnCZ,KAAK,EAAC;IAAiB,GAAC,OAAK,sBAClCY,mBAAA,CAAkD,OAAlD4B,WAAkD,EAAAzB,gBAAA,CAA7BP,IAAI,CAACiC,eAAe,kB,0BACzC7B,mBAAA,CAAwC;MAAnCZ,KAAK,EAAC;IAAiB,GAAC,OAAK,sBAClCY,mBAAA,CAAkD,OAAlD8B,WAAkD,EAAA3B,gBAAA,CAA7BP,IAAI,CAACmC,eAAe,kBAEzChC,mBAAA,SAAY,E,0BACZC,mBAAA,CAA0C;MAArCZ,KAAK,EAAC;IAAiB,GAAC,SAAO,sBACpCY,mBAAA,CAAsD,OAAtDgC,WAAsD,EAAA7B,gBAAA,CAAtBP,IAAI,CAACqC,QAAQ,kBAE7ClC,mBAAA,SAAY,E,0BACZC,mBAAA,CAAuC;MAAlCZ,KAAK,EAAC;IAAiB,GAAC,MAAI,sBACjCY,mBAAA,CAAyD,OAAzDkC,WAAyD,EAAA/B,gBAAA,CAAzBP,IAAI,CAACuC,WAAW,kBAEhDpC,mBAAA,SAAY,E,0BACZC,mBAAA,CAAuC;MAAlCZ,KAAK,EAAC;IAAiB,GAAC,MAAI,sBACjCY,mBAAA,CAA8C,OAA9CoC,WAA8C,EAAAjC,gBAAA,CAAzBP,IAAI,CAACyC,WAAW,kB,0BACrCrC,mBAAA,CAAqC;MAAhCZ,KAAK,EAAC;IAAiB,GAAC,IAAE,sBAC/BY,mBAAA,CAAyC,OAAzCsC,WAAyC,EAAAnC,gBAAA,CAApBP,IAAI,CAAC2C,MAAM,kBAEhCxC,mBAAA,SAAY,E,4BACZC,mBAAA,CAAqC;MAAhCZ,KAAK,EAAC;IAAiB,GAAC,IAAE,sBAC/BY,mBAAA,CAAiD,OAAjDwC,WAAiD,EAAArC,gBAAA,CAA5BP,IAAI,CAAC6C,cAAc,kB,4BACxCzC,mBAAA,CAAqC;MAAhCZ,KAAK,EAAC;IAAiB,GAAC,IAAE,sBAC/BY,mBAAA,CAA6C,OAA7C0C,WAA6C,EAAAvC,gBAAA,CAAxBP,IAAI,CAAC+C,KAAK,CAACrC,IAAI,kBAEpCP,mBAAA,SAAY,E,4BACZC,mBAAA,CAAwC;MAAnCZ,KAAK,EAAC;IAAiB,GAAC,OAAK,sBAClCY,mBAAA,CAAsD,OAAtD4C,WAAsD,EAAAzC,gBAAA,CAAtBP,IAAI,CAACiD,QAAQ,iB,+BAE/C7C,mBAAA,CAEM;MAFDZ,KAAK,EAAC;IAAS,GAAC,gJAErB,sBACAY,mBAAA,CAGM,cAFJA,mBAAA,CAAiD,OAAjD8C,WAAiD,EAAA3C,gBAAA,CAAnBP,IAAI,CAACiB,KAAK,kBACxCb,mBAAA,CAAyD;MAApDZ,KAAK,EAAC,iBAAiB;MAAC2D,SAAqB,EAAbnD,IAAI,CAACoD;4BA5OlDC,WAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}