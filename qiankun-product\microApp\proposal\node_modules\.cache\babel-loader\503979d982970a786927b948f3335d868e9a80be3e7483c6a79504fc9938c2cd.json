{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, computed, onActivated } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { format } from 'common/js/time.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { exportWordHtmlObj } from 'common/config/MicroGlobal';\nimport { filterTableData } from '@/assets/js/suggestExportWord';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport suggestPrint from '@/components/suggestPrint/suggestPrint';\nimport SuggestBasicInfo from './component/SuggestBasicInfo.vue'; // 提案基本信息\nimport SuggestAssignDetail from '@/views/SuggestAssign/component/SuggestAssignDetail.vue'; // 交办\nimport SuggestPostponeReview from '@/views/SuggestApplyForPostpone/component/SuggestPostponeReview.vue'; // 申请延期审查\nimport SuggestAdjustReview from '@/views/SuggestApplyForAdjust/component/SuggestAdjustReview.vue'; // 申请调整审查\nimport SuggestTrackTransactDetail from '@/views/SuggestTrackTransact/component/SuggestTrackTransactDetail.vue'; // 跟踪办理审查\nimport UnitSuggestDetail from '@/views/UnitSuggestDetail/UnitSuggestDetail.vue'; // 单位办理详情\nimport ApplyForAdjustRecords from './ApplyForAdjustRecords/ApplyForAdjustRecords.vue'; // 单位申请调整记录\nimport ApplyForAdjustResult from './ApplyForAdjustResult/ApplyForAdjustResult.vue'; // 单位申请调整结果\nimport TrackTransactApplyForRecords from './TrackTransactApplyForRecords/TrackTransactApplyForRecords.vue'; // 跟踪办理申请记录\nimport CommunicationSituation from './CommunicationSituation/CommunicationSituation.vue'; // 沟通情况\nimport SuggestReplyDetail from './SuggestReplyDetail/SuggestReplyDetail.vue'; // 答复件详情\nimport SegreeSatisfactionDetail from './SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue'; // 满意度测评\nimport { user } from 'common/js/system_var.js';\nimport UnitApplyForAnswerRecords from '@/views/UnitSuggestDetail/component/UnitApplyForAnswerRecords.vue';\nvar __default__ = {\n  name: 'SuggestDetail'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var activeValue = ref('aaaa');\n    var printParams = ref({});\n    var elPrintWhetherShow = ref(false);\n    var details = ref({});\n    var setExtResult = ref([]);\n    var transactObj = ref({}); // 办理方式和办理单位ID\n\n    var handlingMassing = ref({});\n    var isProcessActive = ref(false);\n    var isSatisfaction = ref(1);\n    var hasExecuteNodeIds = ref([]);\n    var reviewList = ref([]); // 审查信息数组\n    var assignList = ref([]); // 交办信息数组\n    var transactUnit = ref([]); // 办理单位列表\n    var maincoOrganizers = ref([]); // 协办单位列表\n    var coOrganizer = ref(''); // 分办里边的协办单位\n    var coOrganizerIds = ref([]); // 分办里边的协办单位id\n    var transactUnitObj = ref({}); // 当前办理单位数据\n\n    var isAdjustRecords = ref(false);\n    var isAdjustResult = ref(false);\n    var isTrackTransact = ref(false);\n    var replyList = ref([]); // 答复件列表\n    var replyId = ref(''); // 答复件ID\n    var replyDetailShow = ref(false);\n    var ifShow = ref(false);\n    var satisfactionsId = ref(''); // 满意度测评ID\n    var satisfactions = ref([]); // 满意度测评\n\n    var unitRecordsId = ref('');\n    var postponeShow = ref(false);\n    var show = ref(false);\n    var isPreAssign = ref(false); // 是否开启预交办\n    var isPersonal = computed(function () {\n      return route.query.logo === 'Personal';\n    });\n    var suggestionOfficeId = ref('');\n    var suggestionOfficeShow = ref(true);\n    onActivated(function () {\n      globalReadConfig();\n      if (route.query.id) {\n        suggestionInfo();\n        suggestionDetails();\n      }\n      // if (route.query.superviseInfoId) {\n      //   cppccSuperviseInfoInfo()\n      // }\n    });\n    // const cppccSuperviseInfoInfo = async () => {\n    //   const { data } = await api.globalJson('/cppccSuperviseInfo/info', {\n    //     detailId: route.query.superviseInfoId\n    //   })\n    //   superviseInfo.value = data\n    // }\n    var globalReadConfig = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$globalRead, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.globalReadConfig({\n                codes: ['proposal_enable_pre_assign']\n              });\n            case 2:\n              _yield$api$globalRead = _context.sent;\n              data = _yield$api$globalRead.data;\n              isPreAssign.value = (data === null || data === void 0 ? void 0 : data.proposal_enable_pre_assign) === 'true';\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function globalReadConfig() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleDetails = function handleDetails(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '提案详情',\n          path: '/proposal/SuggestDetail',\n          query: {\n            id: item.proposalId\n          }\n        }\n      });\n    };\n    var colorObj = function colorObj(state) {\n      var color = {\n        color: '#000'\n      };\n      if (state === 'has_answer') {\n        color.color = '#4fcc72';\n      } else if (state === 'handling') {\n        color.color = '#fbd536';\n      } else if (state === 'apply_adjust') {\n        color.color = '#ca6063';\n      }\n      return color;\n    };\n    var superviseInfo = ref({});\n    var hasSuperviseInfo = ref(false);\n    var suggestionInfo = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _yield$api$suggestion, data, extData;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.suggestionInfo({\n                detailId: route.query.id\n              });\n            case 2:\n              _yield$api$suggestion = _context2.sent;\n              data = _yield$api$suggestion.data;\n              extData = _yield$api$suggestion.extData;\n              data.content = data.content.replace(/<p>/g, '<p style=\"font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;\">');\n              details.value = data;\n              setExtResult.value = extData;\n              suggestUnitUserList();\n              if (data.superviseInfo) {\n                hasSuperviseInfo.value = true;\n                superviseInfo.value = data.superviseInfo;\n              }\n            case 10:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function suggestionInfo() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    // 获取当前登陆者是哪个提案办理单位\n    var suggestUnitUserList = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _data$;\n        var _yield$api$suggestUni, data, i;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.suggestUnitUserList({\n                isAnd: 1,\n                keyword: user.value.userName,\n                tableId: 'sys_npc_suuggestion_office_user',\n                pageNo: '1',\n                pageSize: '199'\n              });\n            case 2:\n              _yield$api$suggestUni = _context3.sent;\n              data = _yield$api$suggestUni.data;\n              suggestionOfficeId.value = (_data$ = data[0]) === null || _data$ === void 0 ? void 0 : _data$.suggestionOfficeId;\n              i = 0;\n            case 6:\n              if (!(i < setExtResult.value.length)) {\n                _context3.next = 13;\n                break;\n              }\n              if (!(setExtResult.value[i].handleOfficeId === suggestionOfficeId.value)) {\n                _context3.next = 10;\n                break;\n              }\n              // 找到匹配的 handleOfficeId\n              if (setExtResult.value[i].handleOfficeType === 'assist' || setExtResult.value[i].handleOfficeType === 'assistHandle') {\n                suggestionOfficeShow.value = false; // 如果是assist，设置为false\n              } else {\n                suggestionOfficeShow.value = true; // 否则设置为true\n              }\n              return _context3.abrupt(\"break\", 13);\n            case 10:\n              i++;\n              _context3.next = 6;\n              break;\n            case 13:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function suggestUnitUserList() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var delaysList = ref([]); // 延期记录\n    var isAnswerRecords = ref(false);\n    var flowHandleOfficeName = ref('');\n    var lookdelays = ref([]);\n    var handleLook = function handleLook(item) {\n      flowHandleOfficeName.value = item.flowHandleOfficeName;\n      lookdelays.value = item.delays;\n      isAnswerRecords.value = true;\n    };\n    var allhandleOfficeInfos = ref([]);\n    var suggestionDetails = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _data$handlingMassing2;\n        var _yield$api$suggestion2, data, extData, _data$handlingMassing, index, _data$streamVariables, item, nodeTtem, _item$streamVariable, _item$streamVariable2, _item$streamVariable3, _nodeTtem$streamVaria, _nodeTtem$streamVaria2, _nodeTtem$streamVaria3, _nodeTtem$streamVaria4, _nodeTtem$streamVaria5, _nodeTtem$streamVaria6, _nodeTtem$streamVaria7, _nodeTtem$streamVaria8, _nodeTtem$streamVaria9, _index, _data$handlingMassing3, _data$handlingMassing4, _item, _item$delays2, _item$adjusts, _item$answers, _item$delays3, _item$adjusts2;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.suggestionDetails({\n                suggestionId: route.query.id\n              });\n            case 2:\n              _yield$api$suggestion2 = _context4.sent;\n              data = _yield$api$suggestion2.data;\n              extData = _yield$api$suggestion2.extData;\n              hasExecuteNodeIds.value = data.hasExecuteNodeIds;\n              allhandleOfficeInfos.value = data.handleOfficeInfos;\n              delaysList.value = [];\n              if (route.query.type === 'postpone') {\n                data.handleOfficeInfos.forEach(function (item) {\n                  var _item$delays;\n                  if ((_item$delays = item.delays) !== null && _item$delays !== void 0 && _item$delays.length) {\n                    delaysList.value.push(// ...item.delays.map((v) => ({ ...v, flowHandleOfficeName: item.handlerOffice.flowHandleOfficeName }))\n                    _objectSpread(_objectSpread({}, item), {}, {\n                      flowHandleOfficeName: item.handlerOffice.flowHandleOfficeName\n                    }));\n                  }\n                });\n              }\n              if (!isPersonal.value) {\n                _context4.next = 12;\n                break;\n              }\n              if ((_data$handlingMassing = data.handlingMassing) !== null && _data$handlingMassing !== void 0 && _data$handlingMassing.answerStopDate) handingPortionAnswerList();\n              return _context4.abrupt(\"return\");\n            case 12:\n              reviewList.value = [];\n              assignList.value = [];\n              transactUnit.value = [];\n              maincoOrganizers.value = [];\n              coOrganizer.value = '';\n              transactObj.value = {\n                transactType: '',\n                mainHandleOfficeId: [],\n                handleOfficeIds: []\n              };\n              handlingMassing.value = data.handlingMassing;\n              isSatisfaction.value = data.isSatisfaction;\n              satisfactions.value = data.satisfactions || [];\n              coOrganizer.value = extData === null || extData === void 0 ? void 0 : extData.map(function (v) {\n                return v.flowHandleOfficeName;\n              }).join('、');\n              coOrganizerIds.value = extData === null || extData === void 0 ? void 0 : extData.map(function (v) {\n                return v.handleOfficeId;\n              });\n              console.log(coOrganizerIds.value);\n              if ((_data$handlingMassing2 = data.handlingMassing) !== null && _data$handlingMassing2 !== void 0 && _data$handlingMassing2.answerStopDate) {\n                handingPortionAnswerList();\n              }\n              for (index = 0; index < ((_data$streamVariables = data.streamVariables) === null || _data$streamVariables === void 0 ? void 0 : _data$streamVariables.length); index++) {\n                item = data === null || data === void 0 ? void 0 : data.streamVariables[index];\n                nodeTtem = data === null || data === void 0 ? void 0 : data.streamVariables[index + 1];\n                if (nodeTtem) {\n                  if (((_item$streamVariable = item.streamVariable) === null || _item$streamVariable === void 0 ? void 0 : _item$streamVariable.spareB) === 'verify') {\n                    reviewList.value.push({\n                      id: item.id,\n                      nodeName: item.nodeName,\n                      nodeResult: (_nodeTtem$streamVaria = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria === void 0 ? void 0 : _nodeTtem$streamVaria.spareC,\n                      UserName: (_nodeTtem$streamVaria2 = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria2 === void 0 ? void 0 : _nodeTtem$streamVaria2.handleUserName,\n                      handleTime: (_nodeTtem$streamVaria3 = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria3 === void 0 ? void 0 : _nodeTtem$streamVaria3.handleTime,\n                      handleContent: (_nodeTtem$streamVaria4 = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria4 === void 0 ? void 0 : _nodeTtem$streamVaria4.handleContent,\n                      isSpareDict: nodeTtem.nodeId === 'rejectReceive',\n                      spareDict: (_nodeTtem$streamVaria5 = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria5 === void 0 ? void 0 : _nodeTtem$streamVaria5.spareDict\n                    });\n                  }\n                  if (((_item$streamVariable2 = item.streamVariable) === null || _item$streamVariable2 === void 0 ? void 0 : _item$streamVariable2.spareB) === 'submitHandling' || ((_item$streamVariable3 = item.streamVariable) === null || _item$streamVariable3 === void 0 ? void 0 : _item$streamVariable3.spareB) === 'preSubmitHandling') {\n                    assignList.value.push({\n                      id: item.id,\n                      nodeName: item.nodeName,\n                      nodeResult: (_nodeTtem$streamVaria6 = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria6 === void 0 ? void 0 : _nodeTtem$streamVaria6.spareC,\n                      UserName: (_nodeTtem$streamVaria7 = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria7 === void 0 ? void 0 : _nodeTtem$streamVaria7.handleUserName,\n                      handleTime: (_nodeTtem$streamVaria8 = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria8 === void 0 ? void 0 : _nodeTtem$streamVaria8.handleTime,\n                      handleContent: (_nodeTtem$streamVaria9 = nodeTtem.streamVariable) === null || _nodeTtem$streamVaria9 === void 0 ? void 0 : _nodeTtem$streamVaria9.handleContent\n                    });\n                  }\n                }\n              }\n              for (_index = 0; _index < data.handleOfficeInfos.length; _index++) {\n                _item = data.handleOfficeInfos[_index];\n                if (_item.handlerOffice.handleOfficeType === 'main') {\n                  transactObj.value.transactType = 'main_assist';\n                  transactObj.value.mainHandleOfficeId.push(_item.handlerOffice.flowHandleOfficeId);\n                } else if (_item.handlerOffice.handleOfficeType === 'publish') {\n                  transactObj.value.transactType = 'publish';\n                  transactObj.value.handleOfficeIds.push(_item.handlerOffice.flowHandleOfficeId);\n                } else {\n                  transactObj.value.handleOfficeIds.push(_item.handlerOffice.flowHandleOfficeId);\n                }\n                if ((_data$handlingMassing3 = data.handlingMassing) !== null && _data$handlingMassing3 !== void 0 && _data$handlingMassing3.answerStopDate || (_data$handlingMassing4 = data.handlingMassing) !== null && _data$handlingMassing4 !== void 0 && _data$handlingMassing4.confirmStopDate) {\n                  if (_item.handlerOffice.handleOfficeType !== 'assist') {\n                    transactUnit.value.push({\n                      id: _item.handlerOffice.id,\n                      unitId: _item.handlerOffice.flowHandleOfficeId,\n                      unitName: _item.handlerOffice.flowHandleOfficeName,\n                      unitType: _item.handlerOffice.handleOfficeType === 'main' ? '主办' : _item.handlerOffice.handleOfficeType === 'assist' ? '协办' : '分办',\n                      hasRead: _item.handlerOffice.hasRead,\n                      // 是否已读\n                      firstReadTime: _item.handlerOffice.firstReadTime,\n                      // 阅读时间\n                      status: _item.handlerOffice.currentHandleStatus,\n                      // 办理状态\n                      statusName: _item.handlerOffice.currentHandleStatusName,\n                      // 办理状态\n                      isDelays: (_item$delays2 = _item.delays) !== null && _item$delays2 !== void 0 && (_item$delays2 = _item$delays2.filter(function (v) {\n                        return !v.verifyStatus;\n                      })) !== null && _item$delays2 !== void 0 && _item$delays2.length ? true : false || false,\n                      // 是否有待审查的延期申请\n                      isAdjusts: (_item$adjusts = _item.adjusts) !== null && _item$adjusts !== void 0 && (_item$adjusts = _item$adjusts.filter(function (v) {\n                        return !v.verifyStatus;\n                      })) !== null && _item$adjusts !== void 0 && _item$adjusts.length ? true : false || false,\n                      // 是否有待审查的调整申请\n                      answers: ((_item$answers = _item.answers) === null || _item$answers === void 0 ? void 0 : _item$answers.filter(function (v) {\n                        return v.submitAnswerType === 'direct';\n                      })[0]) || {},\n                      // 最终答复件\n                      hasConfirm: _item.handlerOffice.hasConfirm,\n                      confirmTime: _item.handlerOffice.confirmTime || ''\n                    });\n                  } else {\n                    maincoOrganizers.value.push(_item.handlerOffice);\n                    console.log('协办单位', maincoOrganizers.value);\n                  }\n                  if (_item.isCurrentLoginOfficeId && ['unit', 'unitTrackTransact', 'unitConclude', 'trackTransact', 'unitPreAssign'].includes(route.query.type)) {\n                    transactUnitObj.value = _objectSpread(_objectSpread({}, _item), {}, {\n                      isReply: data.handlingMassing.massingAnswerDate ? true : false || false,\n                      isDelays: (_item$delays3 = _item.delays) !== null && _item$delays3 !== void 0 && (_item$delays3 = _item$delays3.filter(function (r) {\n                        return !r.verifyStatus;\n                      })) !== null && _item$delays3 !== void 0 && _item$delays3.length ? true : false || false,\n                      isAdjusts: (_item$adjusts2 = _item.adjusts) !== null && _item$adjusts2 !== void 0 && (_item$adjusts2 = _item$adjusts2.filter(function (v) {\n                        return !v.verifyStatus;\n                      })) !== null && _item$adjusts2 !== void 0 && _item$adjusts2.length ? true : false || false\n                    });\n                  }\n                }\n              }\n            case 27:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function suggestionDetails() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handleExportWord = function handleExportWord() {\n      if (JSON.stringify(details.value) === '{}') return ElMessage({\n        type: 'warning',\n        message: '请等待提案详情加载完成再进行导出！'\n      });\n      suggestionWord({\n        ids: [route.query.id]\n      });\n    };\n    var handleSuggestPrint = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              if (!(JSON.stringify(details.value) === '{}')) {\n                _context5.next = 2;\n                break;\n              }\n              return _context5.abrupt(\"return\", ElMessage({\n                type: 'warning',\n                message: '请等待提案详情加载完成再进行打印！'\n              }));\n            case 2:\n              printParams.value = {\n                ids: [route.query.id]\n              };\n              elPrintWhetherShow.value = true;\n            case 4:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function handleSuggestPrint() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var suggestionWord = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6(params) {\n        var _yield$api$suggestion3, data, wordData, index;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.next = 2;\n              return api.suggestionWord(params);\n            case 2:\n              _yield$api$suggestion3 = _context6.sent;\n              data = _yield$api$suggestion3.data;\n              if (data.length) {\n                wordData = {};\n                for (index = 0; index < data.length; index++) {\n                  wordData = filterTableData(data[index]);\n                }\n                exportWordHtmlObj({\n                  code: 'proposalDetails',\n                  name: wordData.docName,\n                  key: 'content',\n                  data: wordData\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function suggestionWord(_x) {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var callback = function callback(type) {\n      postponeShow.value = false;\n      elPrintWhetherShow.value = false;\n      if (type) {\n        qiankunMicro.setGlobalState({\n          closeOpenRoute: {\n            openId: route.query.oldRouteId,\n            closeId: route.query.routeId\n          }\n        });\n      }\n    };\n    var refreshCallback = function refreshCallback() {\n      suggestionInfo();\n      suggestionDetails();\n    };\n    var closeCallback = function closeCallback() {\n      qiankunMicro.setGlobalState({\n        closeOpenRoute: {\n          openId: route.query.oldRouteId,\n          closeId: route.query.routeId\n        }\n      });\n    };\n    var handingPortionAnswerList = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var _yield$api$handingPor, data;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _context7.next = 2;\n              return api.handingPortionAnswerList({\n                query: {\n                  suggestionId: route.query.id\n                }\n              });\n            case 2:\n              _yield$api$handingPor = _context7.sent;\n              data = _yield$api$handingPor.data;\n              replyList.value = isPersonal.value ? (data === null || data === void 0 ? void 0 : data.filter(function (v) {\n                return v.isOpen;\n              })) || [] : data;\n            case 5:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7);\n      }));\n      return function handingPortionAnswerList() {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    var handleReply = function handleReply(item) {\n      replyId.value = item.id;\n      replyDetailShow.value = true;\n    };\n    var handleSatisfactions = function handleSatisfactions(item) {\n      satisfactionsId.value = item.id;\n      ifShow.value = true;\n    };\n    var handlePostpone = function handlePostpone(item) {\n      unitRecordsId.value = item.id;\n      postponeShow.value = true;\n    };\n    var handleConclude = function handleConclude() {\n      ElMessageBox.confirm('此操作将办结该提案, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        suggestionComplete('handleOver');\n      }).catch(function () {\n        ElMessage({\n          type: 'info',\n          message: '已取消重新办理'\n        });\n      });\n    };\n    var anewTransact = function anewTransact() {\n      ElMessageBox.confirm('此操作将重新办理该提案, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        suggestionComplete('suggestionHandling');\n      }).catch(function () {\n        ElMessage({\n          type: 'info',\n          message: '已取消重新办理'\n        });\n      });\n    };\n    var suggestionComplete = /*#__PURE__*/function () {\n      var _ref9 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8(nextNodeId) {\n        var _yield$api$suggestion4, code;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              _context8.next = 2;\n              return api.suggestionComplete({\n                suggestionId: route.query.id,\n                nextNodeId: nextNodeId\n              });\n            case 2:\n              _yield$api$suggestion4 = _context8.sent;\n              code = _yield$api$suggestion4.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '操作成功'\n                });\n                closeCallback();\n              }\n            case 5:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8);\n      }));\n      return function suggestionComplete(_x2) {\n        return _ref9.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      route,\n      activeValue,\n      printParams,\n      elPrintWhetherShow,\n      details,\n      setExtResult,\n      transactObj,\n      handlingMassing,\n      isProcessActive,\n      isSatisfaction,\n      hasExecuteNodeIds,\n      reviewList,\n      assignList,\n      transactUnit,\n      maincoOrganizers,\n      coOrganizer,\n      coOrganizerIds,\n      transactUnitObj,\n      isAdjustRecords,\n      isAdjustResult,\n      isTrackTransact,\n      replyList,\n      replyId,\n      replyDetailShow,\n      ifShow,\n      satisfactionsId,\n      satisfactions,\n      unitRecordsId,\n      postponeShow,\n      show,\n      isPreAssign,\n      isPersonal,\n      suggestionOfficeId,\n      suggestionOfficeShow,\n      globalReadConfig,\n      handleDetails,\n      colorObj,\n      superviseInfo,\n      hasSuperviseInfo,\n      suggestionInfo,\n      suggestUnitUserList,\n      delaysList,\n      isAnswerRecords,\n      flowHandleOfficeName,\n      lookdelays,\n      handleLook,\n      allhandleOfficeInfos,\n      suggestionDetails,\n      handleExportWord,\n      handleSuggestPrint,\n      suggestionWord,\n      callback,\n      refreshCallback,\n      closeCallback,\n      handingPortionAnswerList,\n      handleReply,\n      handleSatisfactions,\n      handlePostpone,\n      handleConclude,\n      anewTransact,\n      suggestionComplete,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      onActivated,\n      get useRoute() {\n        return useRoute;\n      },\n      get format() {\n        return format;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get exportWordHtmlObj() {\n        return exportWordHtmlObj;\n      },\n      get filterTableData() {\n        return filterTableData;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get suggestPrint() {\n        return suggestPrint;\n      },\n      SuggestBasicInfo,\n      SuggestAssignDetail,\n      SuggestPostponeReview,\n      SuggestAdjustReview,\n      SuggestTrackTransactDetail,\n      UnitSuggestDetail,\n      ApplyForAdjustRecords,\n      ApplyForAdjustResult,\n      TrackTransactApplyForRecords,\n      CommunicationSituation,\n      SuggestReplyDetail,\n      SegreeSatisfactionDetail,\n      get user() {\n        return user;\n      },\n      UnitApplyForAnswerRecords\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "computed", "onActivated", "useRoute", "format", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exportWordHtmlObj", "filterTableData", "ElMessage", "ElMessageBox", "suggest<PERSON><PERSON>t", "SuggestBasicInfo", "SuggestAssignDetail", "SuggestPostponeReview", "SuggestAdjustReview", "SuggestTrackTransactDetail", "UnitSuggestDetail", "ApplyForAdjustRecords", "ApplyForAdjustResult", "TrackTransactApplyForRecords", "CommunicationSituation", "SuggestReplyDetail", "SegreeSatisfactionDetail", "user", "UnitApplyForAnswerRecords", "__default__", "route", "activeValue", "printParams", "elPrintWhetherShow", "details", "setExtResult", "transactObj", "handlingMassing", "isProcessActive", "isSatisfaction", "hasExecuteNodeIds", "reviewList", "assignList", "transactUnit", "maincoOrganizers", "coOrganizer", "coOrganizerIds", "transactUnitObj", "isAdjustRecords", "isAdjustResult", "isTrackTransact", "replyList", "replyId", "replyDetailShow", "ifShow", "satisfactionsId", "satisfactions", "unitRecordsId", "postponeShow", "show", "isPreAssign", "isPersonal", "query", "logo", "suggestionOfficeId", "suggestionOfficeShow", "globalReadConfig", "id", "suggestionInfo", "suggestionDetails", "_ref2", "_callee", "_yield$api$globalRead", "data", "_callee$", "_context", "codes", "proposal_enable_pre_assign", "handleDetails", "item", "setGlobalState", "openRoute", "path", "proposalId", "colorObj", "state", "color", "superviseInfo", "hasSuperviseInfo", "_ref3", "_callee2", "_yield$api$suggestion", "extData", "_callee2$", "_context2", "detailId", "content", "replace", "suggestUnitUserList", "_ref4", "_callee3", "_data$", "_yield$api$suggestUni", "_callee3$", "_context3", "isAnd", "keyword", "userName", "tableId", "pageNo", "pageSize", "handleOfficeId", "handleOfficeType", "delaysList", "isAnswerRecords", "flowHandleOfficeName", "<PERSON><PERSON><PERSON>", "handleLook", "delays", "allhandleOfficeInfos", "_ref5", "_callee4", "_data$handlingMassing2", "_yield$api$suggestion2", "_data$handlingMassing", "index", "_data$streamVariables", "nodeTtem", "_item$streamVariable", "_item$streamVariable2", "_item$streamVariable3", "_nodeTtem$streamVaria", "_nodeTtem$streamVaria2", "_nodeTtem$streamVaria3", "_nodeTtem$streamVaria4", "_nodeTtem$streamVaria5", "_nodeTtem$streamVaria6", "_nodeTtem$streamVaria7", "_nodeTtem$streamVaria8", "_nodeTtem$streamVaria9", "_index", "_data$handlingMassing3", "_data$handlingMassing4", "_item", "_item$delays2", "_item$adjusts", "_item$answers", "_item$delays3", "_item$adjusts2", "_callee4$", "_context4", "suggestionId", "handleOfficeInfos", "_item$delays", "_objectSpread", "handlerOffice", "answerStopDate", "handingPortionAnswerList", "transactType", "mainHandleOfficeId", "handleOfficeIds", "map", "join", "console", "log", "streamVariables", "streamVariable", "spareB", "nodeName", "nodeResult", "spareC", "UserName", "handleUserName", "handleTime", "handleContent", "isSpareDict", "nodeId", "spareDict", "flowHandleOfficeId", "confirmStopDate", "unitId", "unitName", "unitType", "hasRead", "firstReadTime", "status", "currentHandleStatus", "statusName", "currentHandleStatusName", "is<PERSON><PERSON><PERSON>", "filter", "verifyStatus", "isAdjusts", "adjusts", "answers", "submitAnswerType", "hasConfirm", "confirmTime", "isCurrentLoginOfficeId", "includes", "isReply", "massingAnswerDate", "handleExportWord", "JSON", "stringify", "message", "<PERSON><PERSON><PERSON>", "ids", "handleSuggestPrint", "_ref6", "_callee5", "_callee5$", "_context5", "_ref7", "_callee6", "params", "_yield$api$suggestion3", "wordData", "_callee6$", "_context6", "code", "doc<PERSON>ame", "key", "_x", "callback", "closeOpenRoute", "openId", "oldRouteId", "closeId", "routeId", "refreshCallback", "closeCallback", "_ref8", "_callee7", "_yield$api$handingPor", "_callee7$", "_context7", "isOpen", "handleReply", "handleSatisfactions", "handlePostpone", "handleConclude", "confirm", "confirmButtonText", "cancelButtonText", "suggestionComplete", "anewTransact", "_ref9", "_callee8", "nextNodeId", "_yield$api$suggestion4", "_callee8$", "_context8", "_x2"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestDetail/SuggestDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestDetail\">\r\n    <anchor-location v-model=\"activeValue\">\r\n      <template #function>\r\n        <div class=\"detailsPrint\" title=\"打印提案\" @click=\"handleSuggestPrint\">打印提案</div>\r\n        <div class=\"detailsExportInfo\" title=\"导出提案信息\" @click=\"handleExportWord\">导出提案信息</div>\r\n      </template>\r\n      <div class=\"SuggestDetailProcessInfo\">\r\n        <div class=\"SuggestLabelName\">\r\n          提案流程\r\n          <span class=\"SuggestDetailBodyActive\" @click=\"isProcessActive = !isProcessActive\"\r\n            :class=\"{ 'is-ctive': isProcessActive }\">\r\n            {{ isProcessActive ? '收起' : '展开' }}\r\n            <el-icon>\r\n              <ArrowUp />\r\n            </el-icon>\r\n          </span>\r\n        </div>\r\n        <template v-if=\"!isPreAssign\">\r\n          <transition name=\"el-zoom-in-top\">\r\n            <div class=\"SuggestDetailProcessNodeBody\" v-show=\"isProcessActive\">\r\n              <div class=\"SuggestDetailProcessNodeMain\">\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNode\">提交</div>\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('submitSuggestion') }\">\r\n                    1\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNode\">审查</div>\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('prepareVerify') }\">\r\n                    2\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNode\">交办</div>\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('prepareSubmitHandle') }\">\r\n                    3\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNode\">办理</div>\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('suggestionHandling') }\">\r\n                    4\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNode\">答复</div>\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') }\">\r\n                    5\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNode\">满意度测评</div>\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') }\">\r\n                    6\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNode\">办结</div>\r\n                  <div class=\"SuggestDetailProcessNodeIcon\" :class=\"{\r\n                    'is-active': hasExecuteNodeIds.includes('handleOver'),\r\n                    'is-active-other': !isSatisfaction\r\n                  }\">\r\n                    8\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssist\">\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\" :class=\"{\r\n                    'is-active':\r\n                      hasExecuteNodeIds.includes('exchangeLetter') ||\r\n                      hasExecuteNodeIds.includes('exchangeSocial') ||\r\n                      hasExecuteNodeIds.includes('rejectReceive') ||\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') ||\r\n                      hasExecuteNodeIds.includes('returnSubmit')\r\n                  }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('exchangeLetter') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNode\">转来信</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') && !isSatisfaction }\">\r\n                    7\r\n                    <div class=\"SuggestDetailProcessNode\">不满意</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('handleOver') && !isSatisfaction }\"></div>\r\n                </div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssist\">\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\" :class=\"{\r\n                    'is-active':\r\n                      hasExecuteNodeIds.includes('exchangeSocial') ||\r\n                      hasExecuteNodeIds.includes('rejectReceive') ||\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') ||\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') ||\r\n                      hasExecuteNodeIds.includes('returnSubmit')\r\n                  }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('exchangeSocial') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNode\">转社情民意</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssist\">\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\" :class=\"{\r\n                    'is-active':\r\n                      hasExecuteNodeIds.includes('rejectReceive') ||\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') ||\r\n                      hasExecuteNodeIds.includes('returnSubmit')\r\n                  }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('rejectReceive') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNode\">不予立案</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssist\">\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\" :class=\"{\r\n                    'is-active':\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') || hasExecuteNodeIds.includes('returnSubmit')\r\n                  }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('cancelSuggestion') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNode\">撤案</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssist\">\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('returnSubmit') }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\">\r\n                  <div class=\"SuggestDetailProcessNodeIcon\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('returnSubmit') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNode\">退回</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfo\"></div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </template>\r\n        <!-- 建议流程图(包含预交办) -->\r\n        <template v-if=\"isPreAssign\">\r\n          <transition name=\"el-zoom-in-top\">\r\n            <div class=\"SuggestDetailProcessNodeBodyThree\" v-show=\"isProcessActive\">\r\n              <div class=\"SuggestDetailProcessNodeMainThree\">\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeThree\">提交</div>\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('submitSuggestion') }\">\r\n                    1\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeThree\">审查</div>\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('prepareVerify') }\">\r\n                    2\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeThree\">交办</div>\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('prepareSubmitHandle') }\">\r\n                    3\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeThree\">预交办</div>\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('preAssign') }\">\r\n                    4\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeThree\">办理</div>\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('suggestionHandling') }\">\r\n                    5\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeThree\">答复</div>\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') }\">\r\n                    6\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeThree\">满意度测评</div>\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') }\">\r\n                    7\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeThree\">办结</div>\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\" :class=\"{\r\n                    'is-active': hasExecuteNodeIds.includes('handleOver'),\r\n                    'is-active-other': !isSatisfaction\r\n                  }\">\r\n                    9\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssistThree\">\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\" :class=\"{\r\n                    'is-active':\r\n                      hasExecuteNodeIds.includes('exchangeLetter') ||\r\n                      hasExecuteNodeIds.includes('exchangeSocial') ||\r\n                      hasExecuteNodeIds.includes('rejectReceive') ||\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') ||\r\n                      hasExecuteNodeIds.includes('returnSubmit')\r\n                  }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('exchangeLetter') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNodeThree\">转来信</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('hasAnswerSuggestion') && !isSatisfaction }\">\r\n                    8\r\n                    <div class=\"SuggestDetailProcessNodeThree\">不满意</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('handleOver') && !isSatisfaction }\"></div>\r\n                </div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssistThree\">\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\" :class=\"{\r\n                    'is-active':\r\n                      hasExecuteNodeIds.includes('exchangeSocial') ||\r\n                      hasExecuteNodeIds.includes('rejectReceive') ||\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') ||\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') ||\r\n                      hasExecuteNodeIds.includes('returnSubmit')\r\n                  }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('exchangeSocial') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNodeThree\">转社情民意</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssistThree\">\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\" :class=\"{\r\n                    'is-active':\r\n                      hasExecuteNodeIds.includes('rejectReceive') ||\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') ||\r\n                      hasExecuteNodeIds.includes('returnSubmit')\r\n                  }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('rejectReceive') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNodeThree\">不予接收</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssistThree\">\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\" :class=\"{\r\n                    'is-active':\r\n                      hasExecuteNodeIds.includes('cancelSuggestion') || hasExecuteNodeIds.includes('returnSubmit')\r\n                  }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('cancelSuggestion') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNodeThree\">撤案</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n              </div>\r\n              <div class=\"SuggestDetailProcessNodeAssistThree\">\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('returnSubmit') }\"></div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\">\r\n                  <div class=\"SuggestDetailProcessNodeIconThree\"\r\n                    :class=\"{ 'is-active': hasExecuteNodeIds.includes('returnSubmit') }\">\r\n                    3\r\n                    <div class=\"SuggestDetailProcessNodeThree\">退回</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n                <div class=\"SuggestDetailProcessNodeInfoThree\"></div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </template>\r\n      </div>\r\n      <anchor-location-item v-if=\"reviewList.length\" value=\"aaaa\" label=\"提案审查信息\">\r\n        <div class=\"SuggestLabelName\">提案审查信息</div>\r\n        <global-info v-for=\"item in reviewList\" :key=\"item.id\">\r\n          <global-info-item label=\"审查结果\">{{ item.nodeResult }}</global-info-item>\r\n          <!-- <global-info-line>\r\n            <global-info-item label=\"审查类别\">{{ item.nodeName }}</global-info-item>\r\n            <global-info-item label=\"审查结果\">{{ item.nodeResult }}</global-info-item>\r\n          </global-info-line> -->\r\n          <global-info-line>\r\n            <global-info-item label=\"审查者\">{{ item.UserName }}</global-info-item>\r\n            <global-info-item label=\"审查时间\">{{ format(item.handleTime) }}</global-info-item>\r\n          </global-info-line>\r\n          <global-info-item v-if=\"item.isSpareDict\" :label=\"`${item.nodeResult}理由`\">\r\n            {{ item.spareDict?.label }}\r\n          </global-info-item>\r\n          <global-info-item label=\"审查意见\">{{ item.handleContent }}</global-info-item>\r\n        </global-info>\r\n      </anchor-location-item>\r\n      <anchor-location-item v-if=\"assignList.length\" value=\"bbbb\" label=\"提案交办信息\">\r\n        <div class=\"SuggestLabelName\">提案交办信息</div>\r\n        <global-info v-for=\"(item, index) in assignList\" :key=\"item.id\">\r\n          <global-info-item label=\"交办结果\">{{ item.nodeResult }}</global-info-item>\r\n          <!-- <global-info-line>\r\n            <global-info-item label=\"交办类别\">{{ item.nodeName }}</global-info-item>\r\n            <global-info-item label=\"交办结果\">{{ item.nodeResult }}</global-info-item>\r\n          </global-info-line> -->\r\n          <global-info-line>\r\n            <global-info-item label=\"交办者\">{{ item.UserName }}</global-info-item>\r\n            <global-info-item label=\"交办时间\">{{ format(item.handleTime) }}</global-info-item>\r\n          </global-info-line>\r\n          <global-info-item label=\"交办意见\">{{ item.handleContent }}</global-info-item>\r\n          <global-info-item v-if=\"\r\n            (index === assignList.length - 1 && !handlingMassing?.answerStopDate) ||\r\n            (index === assignList.length - 2 && handlingMassing?.confirmStopDate)\r\n          \" label=\"签收截止时间\">\r\n            {{ format(handlingMassing.confirmStopDate) }}\r\n          </global-info-item>\r\n          <global-info-item v-if=\"index === assignList.length - 1 && handlingMassing?.answerStopDate\" label=\"答复截止时间\">\r\n            {{ format(handlingMassing.answerStopDate) }}\r\n          </global-info-item>\r\n        </global-info>\r\n      </anchor-location-item>\r\n      <anchor-location-item v-if=\"hasSuperviseInfo\" value=\"oooo\" label=\"重点督办\">\r\n        <div class=\"SuggestLabelName\">重点督办</div>\r\n        <global-info>\r\n          <global-info-item :label=\"superviseInfo.superviseLeaderName ? '督办领导' : '督办单位'\">\r\n            {{ superviseInfo.superviseGroupName || superviseInfo.superviseLeaderName }}\r\n          </global-info-item>\r\n          <global-info-line>\r\n            <global-info-item label=\"牵头督办单位\">\r\n              {{ superviseInfo.superviseFirstGroupName || superviseInfo.superviseFirstGroup }}\r\n            </global-info-item>\r\n            <global-info-item label=\"其他协助督办单位\">\r\n              {{ superviseInfo.superviseOtherGroupName || superviseInfo.superviseOtherGroup }}\r\n            </global-info-item>\r\n          </global-info-line>\r\n          <global-info-item label=\"督办意见\">{{ superviseInfo.opinion }}</global-info-item>\r\n          <global-info-item label=\"相关文件\">\r\n            <xyl-global-file :fileData=\"superviseInfo.attachments\"></xyl-global-file>\r\n          </global-info-item>\r\n        </global-info>\r\n      </anchor-location-item>\r\n      <anchor-location-item v-if=\"transactUnit.length\" value=\"cccc\" label=\"提案办理单位\">\r\n        <div class=\"SuggestLabelName\">\r\n          提案办理单位\r\n          <div class=\"SuggestLabelNameButton\">\r\n            <el-button @click=\"isAdjustRecords = !isAdjustRecords\"\r\n              v-if=\"!['unit', 'unitTrackTransact', 'unitConclude', 'unitPreAssign'].includes(route.query.type)\"\r\n              type=\"primary\">\r\n              办理单位调整记录\r\n            </el-button>\r\n            <el-button @click=\"isAdjustResult = !isAdjustResult\" type=\"primary\">办理单位调整结果</el-button>\r\n          </div>\r\n        </div>\r\n        <global-info v-for=\"item in transactUnit\" :key=\"item.id\">\r\n          <global-info-line>\r\n            <global-info-item label=\"办理单位\">{{ item.unitName }}</global-info-item>\r\n            <global-info-item label=\"办理类型\">{{ item.unitType }}</global-info-item>\r\n          </global-info-line>\r\n          <global-info-line v-if=\"isPreAssign\">\r\n            <global-info-item label=\"是否签收\">\r\n              <div class=\"SuggestSign\" v-if=\"item.hasConfirm\">已签收</div>\r\n              <div class=\"SuggestUnSign\" v-if=\"!item.hasConfirm\">待签收</div>\r\n            </global-info-item>\r\n            <global-info-item label=\"签收时间\">{{ format(item.confirmTime) }}</global-info-item>\r\n          </global-info-line>\r\n          <global-info-line>\r\n            <global-info-item label=\"是否阅读\">\r\n              <div class=\"SuggestRead\" v-if=\"item.hasRead\">已阅读 {{ format(item.firstReadTime) }}</div>\r\n              <div class=\"SuggestUnRead\" v-if=\"!item.hasRead\">未阅读</div>\r\n            </global-info-item>\r\n            <global-info-item label=\"办理状态\">\r\n              <div class=\"SuggestReviewUnit\">\r\n                <span :style=\"colorObj(item.status)\">{{ item.statusName }}</span>\r\n                <el-link v-if=\"item.isDelays && route.query.type === 'postpone'\" @click=\"handlePostpone(item)\"\r\n                  type=\"primary\">\r\n                  申请延期审查\r\n                </el-link>\r\n              </div>\r\n            </global-info-item>\r\n          </global-info-line>\r\n          <global-info-line>\r\n            <global-info-item label=\"答复类型\">{{ item.answers?.suggestionAnswerType?.label }}</global-info-item>\r\n            <global-info-item label=\"答复时间\">{{ format(item.answers?.answerDate) }}</global-info-item>\r\n          </global-info-line>\r\n          <global-info-item label=\"答复意见\" v-if=\"item.status === 'has_answer'\">\r\n            <el-link @click=\"handleReply(item.answers)\" type=\"primary\">查看{{ item.unitName }}的答复信息</el-link>\r\n          </global-info-item>\r\n        </global-info>\r\n        <!-- 主办+协办的情况 -->\r\n        <global-info>\r\n          <global-info-item label=\"协办单位\">{{maincoOrganizers.map(b =>\r\n            b.flowHandleOfficeName).join('、')}}</global-info-item>\r\n        </global-info>\r\n        <!-- 协办+分办的情况 -->\r\n        <global-info v-if=\"coOrganizer\">\r\n          <global-info-item label=\"协办单位\">{{ coOrganizer }}</global-info-item>\r\n        </global-info>\r\n      </anchor-location-item>\r\n      <anchor-location-item v-if=\"delaysList.length && route.query.type === 'postpone'\" value=\"gggg\" label=\"申请延期记录\">\r\n        <div class=\"SuggestLabelName\">申请延期记录</div>\r\n        <template v-for=\"item in delaysList\" :key=\"item.id\">\r\n          <!-- <div class=\"SuggestLabelNameButton\">\r\n            <el-button v-if=\"item.delays.length > 1\" @click=\"handleLook(item)\" type=\"primary\">\r\n              查看更多延期记录\r\n            </el-button>\r\n          </div> -->\r\n          <global-info v-for=\"(child, index) in item.delays\" :key=\"child.id\" v-show=\"index === item.delays.length - 1\">\r\n            <global-info-line>\r\n              <global-info-item label=\"申请单位\">{{ item?.flowHandleOfficeName }}</global-info-item>\r\n              <global-info-item label=\"申请时间\">\r\n                <div style=\"width: 100%; display: flex; align-items: center; justify-content: space-between\">\r\n                  <div style=\"margin-right: 20px\">{{ format(child.createDate) }}</div>\r\n                  <el-button link text v-if=\"item.delays.length > 1\" @click=\"handleLook(item)\" type=\"primary\">\r\n                    查看更多记录\r\n                  </el-button>\r\n                </div>\r\n              </global-info-item>\r\n            </global-info-line>\r\n            <global-info-line>\r\n              <global-info-item label=\"答复截止时间\">{{ format(child.lastAnswerAdjustDate) }}</global-info-item>\r\n              <global-info-item label=\"申请答复截止时间\">{{ format(child.lastApplyAdjustDate) }}</global-info-item>\r\n            </global-info-line>\r\n            <global-info-item label=\"申请延期理由\">\r\n              <pre>{{ child.delayReason }}</pre>\r\n            </global-info-item>\r\n            <global-info-item label=\"是否同意延期申请\">\r\n              {{ child.verifyStatus ? (child.verifyStatus === 1 ? '同意申请' : '驳回') : '待审查' }}\r\n            </global-info-item>\r\n            <global-info-item v-if=\"child.verifyStatus === 2\" label=\"驳回理由\">\r\n              <pre>{{ child.noPassReason }}</pre>\r\n            </global-info-item>\r\n          </global-info>\r\n        </template>\r\n      </anchor-location-item>\r\n      <anchor-location-item v-if=\"\r\n        handlingMassing?.answerStopDate && !['unit', 'unitTrackTransact', 'unitConclude'].includes(route.query.type)\r\n      \" value=\"dddd\" label=\"提案办理信息\">\r\n        <div class=\"SuggestLabelName\">\r\n          提案办理及答复信息\r\n          <div class=\"SuggestLabelNameButton\">\r\n            <el-button @click=\"isTrackTransact = !isTrackTransact\" type=\"primary\">跟踪办理申请记录</el-button>\r\n          </div>\r\n        </div>\r\n        <global-info>\r\n          <global-info-item label=\"答复时间\">{{ format(handlingMassing.massingAnswerDate) }}</global-info-item>\r\n          <global-info-item label=\"沟通情况\">\r\n            <el-link @click=\"show = !show\" type=\"primary\">查看办理单位与委员沟通情况</el-link>\r\n          </global-info-item>\r\n          <global-info-item label=\"答复意见\">\r\n            <div v-for=\"item in replyList\" :key=\"item.id\">\r\n              <el-link @click=\"handleReply(item)\" type=\"primary\">\r\n                查看{{ item.handleOfficeName }}的答复信息\r\n                {{ format(item.answerDate) }}\r\n                <span v-if=\"item.submitAnswerType === 'history'\">（历史答复）</span>\r\n                <span v-if=\"item.submitAnswerType === 'trace'\">（跟踪办理答复）</span>\r\n                <span v-if=\"item.submitAnswerType === 'direct'\">（最终答复）</span>\r\n                {{ item.suggestionAnswerType?.label }}\r\n              </el-link>\r\n            </div>\r\n          </global-info-item>\r\n          <global-info-item label=\"满意度测评\">\r\n            <div v-if=\"!satisfactions.length\">\r\n              <el-link @click=\"handleSatisfactions({ id: '' })\" type=\"primary\">查看满意度测评</el-link>\r\n            </div>\r\n            <div v-for=\"item in satisfactions\" :key=\"item.id\">\r\n              <el-link @click=\"handleSatisfactions(item)\" type=\"primary\">\r\n                {{ item.handleResultName }}{{ item.isHistoryTest ? '（历史测评）' : '（最终测评）' }}\r\n              </el-link>\r\n            </div>\r\n          </global-info-item>\r\n        </global-info>\r\n      </anchor-location-item>\r\n      <anchor-location-item v-if=\"isPersonal && replyList.length\" value=\"zzzz\" label=\"提案办理信息\">\r\n        <div class=\"SuggestLabelName\">提案办理及答复信息</div>\r\n        <global-info>\r\n          <global-info-item label=\"答复意见\">\r\n            <div v-for=\"item in replyList\" :key=\"item.id\">\r\n              <el-link @click=\"handleReply(item)\" type=\"primary\">\r\n                查看{{ item.handleOfficeName }}的答复信息\r\n                {{ format(item.answerDate) }}\r\n                <span v-if=\"item.submitAnswerType === 'history'\">（历史答复）</span>\r\n                <span v-if=\"item.submitAnswerType === 'trace'\">（跟踪办理答复）</span>\r\n                <span v-if=\"item.submitAnswerType === 'direct'\">（最终答复）</span>\r\n                {{ item.suggestionAnswerType?.label }}\r\n              </el-link>\r\n            </div>\r\n          </global-info-item>\r\n        </global-info>\r\n      </anchor-location-item>\r\n      <anchor-location-item v-if=\"details.isMergeProposal && !isPersonal\" value=\"eeee\" label=\"提案并案信息\">\r\n        <div class=\"SuggestLabelName\">{{ details.isMainMergeProposal ? '并入提案信息' : '主并提案信息' }}</div>\r\n        <div class=\"SuggestDetailTable\" v-if=\"details.mergeProposals?.length\">\r\n          <div class=\"SuggestDetailTableHead\">\r\n            <div class=\"SuggestDetailTableItem row2\">流水号</div>\r\n            <div class=\"SuggestDetailTableItem row2\">案号</div>\r\n            <div class=\"SuggestDetailTableItem row5\">案题</div>\r\n            <div class=\"SuggestDetailTableItem row2\">提案者</div>\r\n            <div class=\"SuggestDetailTableItem row3\">提交时间</div>\r\n          </div>\r\n          <div class=\"SuggestDetailTableBody\" v-for=\"item in details.mergeProposals\" :key=\"item.proposalId\">\r\n            <div class=\"SuggestDetailTableItem row2\">{{ item.streamNumber }}</div>\r\n            <div class=\"SuggestDetailTableItem row2\">{{ item.serialNumber }}</div>\r\n            <div class=\"SuggestDetailTableItem row5\">\r\n              <el-link @click=\"handleDetails(item)\" type=\"primary\">{{ item.title }}</el-link>\r\n            </div>\r\n            <div class=\"SuggestDetailTableItem row2\">{{ item.suggestionUserName }}</div>\r\n            <div class=\"SuggestDetailTableItem row3\">{{ format(item.createDate) }}</div>\r\n          </div>\r\n        </div>\r\n      </anchor-location-item>\r\n      <!-- 交办 -->\r\n      <keep-alive>\r\n        <SuggestAssignDetail :id=\"route.query.id\" :details=\"details\" :transactObj=\"transactObj\"\r\n          :coOrganizerIds=\"coOrganizerIds\" :name=\"route.query.moduleName\" :isPreAssign=\"isPreAssign\"\r\n          v-if=\"route.query.type === 'assign'\" @callback=\"closeCallback\"></SuggestAssignDetail>\r\n      </keep-alive>\r\n      <!-- 申请调整审查 -->\r\n      <keep-alive>\r\n        <SuggestAdjustReview :id=\"route.query.id\" :transactObj=\"transactObj\" v-if=\"route.query.type === 'adjust'\"\r\n          @callback=\"closeCallback\"></SuggestAdjustReview>\r\n      </keep-alive>\r\n      <!-- 跟踪办理审查 -->\r\n      <keep-alive>\r\n        <SuggestTrackTransactDetail :id=\"route.query.id\" :transactUnitObj=\"transactUnitObj\"\r\n          v-if=\"route.query.type === 'trackTransact'\" @refresh=\"refreshCallback\" @callback=\"closeCallback\">\r\n        </SuggestTrackTransactDetail>\r\n      </keep-alive>\r\n      <keep-alive>\r\n        <UnitSuggestDetail :id=\"route.query.id\" :type=\"route.query.type\" :details=\"details\"\r\n          :allhandleOfficeInfos=\"allhandleOfficeInfos\" :transactUnitObj=\"transactUnitObj\" :satisfactions=\"satisfactions\"\r\n          :suggestionOfficeShow=\"suggestionOfficeShow\"\r\n          v-if=\"['unit', 'unitTrackTransact', 'unitConclude', 'unitPreAssign'].includes(route.query.type)\"\r\n          @refresh=\"refreshCallback\" @callback=\"closeCallback\"></UnitSuggestDetail>\r\n      </keep-alive>\r\n      <div v-if=\"route.query.type === 'reply'\" class=\"SuggestReplyButton\">\r\n        <el-button :disabled=\"!satisfactions.map((v) => !v.isHistoryTest).length\" @click=\"handleConclude\"\r\n          type=\"primary\">\r\n          办结\r\n        </el-button>\r\n        <el-button @click=\"anewTransact\" type=\"primary\">重新办理</el-button>\r\n        <el-button @click=\"closeCallback\">取消</el-button>\r\n      </div>\r\n      <!-- v-show=\"reviewList.length || assignList.length || transactUnit.length || details.isMergeProposal\" -->\r\n      <div class=\"SuggestSegmentation\"></div>\r\n      <anchor-location-item value=\"ffff\" label=\"提案基本信息\">\r\n        <!-- 提案基本信息 -->\r\n        <keep-alive>\r\n          <SuggestBasicInfo :id=\"route.query.id\" :details=\"details\"></SuggestBasicInfo>\r\n        </keep-alive>\r\n      </anchor-location-item>\r\n    </anchor-location>\r\n    <suggestPrint v-if=\"elPrintWhetherShow\" :params=\"printParams\" @callback=\"callback\"></suggestPrint>\r\n  </div>\r\n  <xyl-popup-window v-model=\"postponeShow\" name=\"延期审查\">\r\n    <SuggestPostponeReview :id=\"unitRecordsId\" @callback=\"callback\"></SuggestPostponeReview>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isAdjustRecords\" name=\"办理单位调整记录\">\r\n    <ApplyForAdjustRecords :id=\"route.query.id\"></ApplyForAdjustRecords>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isAdjustResult\" name=\"办理单位调整结果\">\r\n    <ApplyForAdjustResult :id=\"route.query.id\"></ApplyForAdjustResult>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"isTrackTransact\" name=\"跟踪办理申请记录\">\r\n    <TrackTransactApplyForRecords :id=\"route.query.id\"></TrackTransactApplyForRecords>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"show\" name=\"办理单位与委员沟通情况\">\r\n    <CommunicationSituation :id=\"route.query.id\" :type=\"['transact', 'reply', 'conclude'].includes(route.query.type)\">\r\n    </CommunicationSituation>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"replyDetailShow\" name=\"答复文件详情\">\r\n    <SuggestReplyDetail :id=\"replyId\"></SuggestReplyDetail>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"ifShow\" name=\"满意度测评\">\r\n    <SegreeSatisfactionDetail :id=\"satisfactionsId\" :suggestId=\"route.query.id\"></SegreeSatisfactionDetail>\r\n  </xyl-popup-window>\r\n\r\n  <xyl-popup-window v-model=\"isAnswerRecords\" name=\"申请延期记录\">\r\n    <UnitApplyForAnswerRecords :name=\"flowHandleOfficeName\" :data=\"lookdelays\"></UnitApplyForAnswerRecords>\r\n  </xyl-popup-window>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestDetail' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { exportWordHtmlObj } from 'common/config/MicroGlobal'\r\nimport { filterTableData } from '@/assets/js/suggestExportWord'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport suggestPrint from '@/components/suggestPrint/suggestPrint'\r\nimport SuggestBasicInfo from './component/SuggestBasicInfo.vue' // 提案基本信息\r\nimport SuggestAssignDetail from '@/views/SuggestAssign/component/SuggestAssignDetail.vue' // 交办\r\nimport SuggestPostponeReview from '@/views/SuggestApplyForPostpone/component/SuggestPostponeReview.vue' // 申请延期审查\r\nimport SuggestAdjustReview from '@/views/SuggestApplyForAdjust/component/SuggestAdjustReview.vue' // 申请调整审查\r\nimport SuggestTrackTransactDetail from '@/views/SuggestTrackTransact/component/SuggestTrackTransactDetail.vue' // 跟踪办理审查\r\nimport UnitSuggestDetail from '@/views/UnitSuggestDetail/UnitSuggestDetail.vue' // 单位办理详情\r\nimport ApplyForAdjustRecords from './ApplyForAdjustRecords/ApplyForAdjustRecords.vue' // 单位申请调整记录\r\nimport ApplyForAdjustResult from './ApplyForAdjustResult/ApplyForAdjustResult.vue' // 单位申请调整结果\r\nimport TrackTransactApplyForRecords from './TrackTransactApplyForRecords/TrackTransactApplyForRecords.vue' // 跟踪办理申请记录\r\nimport CommunicationSituation from './CommunicationSituation/CommunicationSituation.vue' // 沟通情况\r\nimport SuggestReplyDetail from './SuggestReplyDetail/SuggestReplyDetail.vue' // 答复件详情\r\nimport SegreeSatisfactionDetail from './SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue' // 满意度测评\r\nimport { user } from 'common/js/system_var.js'\r\nimport UnitApplyForAnswerRecords from '@/views/UnitSuggestDetail/component/UnitApplyForAnswerRecords.vue'\r\nconst route = useRoute()\r\nconst activeValue = ref('aaaa')\r\n\r\nconst printParams = ref({})\r\nconst elPrintWhetherShow = ref(false)\r\n\r\nconst details = ref({})\r\nconst setExtResult = ref([])\r\n\r\nconst transactObj = ref({}) // 办理方式和办理单位ID\r\n\r\nconst handlingMassing = ref({})\r\nconst isProcessActive = ref(false)\r\nconst isSatisfaction = ref(1)\r\nconst hasExecuteNodeIds = ref([])\r\nconst reviewList = ref([]) // 审查信息数组\r\nconst assignList = ref([]) // 交办信息数组\r\nconst transactUnit = ref([]) // 办理单位列表\r\nconst maincoOrganizers = ref([]) // 协办单位列表\r\nconst coOrganizer = ref('') // 分办里边的协办单位\r\nconst coOrganizerIds = ref([]) // 分办里边的协办单位id\r\nconst transactUnitObj = ref({}) // 当前办理单位数据\r\n\r\nconst isAdjustRecords = ref(false)\r\nconst isAdjustResult = ref(false)\r\nconst isTrackTransact = ref(false)\r\n\r\nconst replyList = ref([]) // 答复件列表\r\nconst replyId = ref('') // 答复件ID\r\nconst replyDetailShow = ref(false)\r\n\r\nconst ifShow = ref(false)\r\nconst satisfactionsId = ref('') // 满意度测评ID\r\nconst satisfactions = ref([]) // 满意度测评\r\n\r\nconst unitRecordsId = ref('')\r\nconst postponeShow = ref(false)\r\nconst show = ref(false)\r\nconst isPreAssign = ref(false) // 是否开启预交办\r\nconst isPersonal = computed(() => route.query.logo === 'Personal')\r\nconst suggestionOfficeId = ref('')\r\nconst suggestionOfficeShow = ref(true)\r\n\r\nonActivated(() => {\r\n  globalReadConfig()\r\n  if (route.query.id) {\r\n    suggestionInfo()\r\n    suggestionDetails()\r\n  }\r\n  // if (route.query.superviseInfoId) {\r\n  //   cppccSuperviseInfoInfo()\r\n  // }\r\n})\r\n// const cppccSuperviseInfoInfo = async () => {\r\n//   const { data } = await api.globalJson('/cppccSuperviseInfo/info', {\r\n//     detailId: route.query.superviseInfoId\r\n//   })\r\n//   superviseInfo.value = data\r\n// }\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({\r\n    codes: ['proposal_enable_pre_assign']\r\n  })\r\n  isPreAssign.value = data?.proposal_enable_pre_assign === 'true'\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.proposalId } }\r\n  })\r\n}\r\nconst colorObj = (state) => {\r\n  var color = { color: '#000' }\r\n  if (state === 'has_answer') {\r\n    color.color = '#4fcc72'\r\n  } else if (state === 'handling') {\r\n    color.color = '#fbd536'\r\n  } else if (state === 'apply_adjust') {\r\n    color.color = '#ca6063'\r\n  }\r\n  return color\r\n}\r\nconst superviseInfo = ref({})\r\nconst hasSuperviseInfo = ref(false)\r\nconst suggestionInfo = async () => {\r\n  const { data, extData } = await api.suggestionInfo({ detailId: route.query.id })\r\n  data.content = data.content.replace(/<p>/g, '<p style=\"font-family: 仿宋_GB2312; text-indent: 32pt; line-height: 28pt; font-size: 16pt;\">');\r\n  details.value = data\r\n  setExtResult.value = extData\r\n  suggestUnitUserList()\r\n  if (data.superviseInfo) {\r\n    hasSuperviseInfo.value = true\r\n    superviseInfo.value = data.superviseInfo\r\n  }\r\n}\r\n// 获取当前登陆者是哪个提案办理单位\r\nconst suggestUnitUserList = async () => {\r\n  const { data } = await api.suggestUnitUserList({\r\n    isAnd: 1,\r\n    keyword: user.value.userName,\r\n    tableId: 'sys_npc_suuggestion_office_user',\r\n    pageNo: '1',\r\n    pageSize: '199'\r\n  })\r\n  suggestionOfficeId.value = data[0]?.suggestionOfficeId\r\n  for (let i = 0; i < setExtResult.value.length; i++) {\r\n    if (setExtResult.value[i].handleOfficeId === suggestionOfficeId.value) {\r\n      // 找到匹配的 handleOfficeId\r\n      if (setExtResult.value[i].handleOfficeType === 'assist' || setExtResult.value[i].handleOfficeType === 'assistHandle') {\r\n        suggestionOfficeShow.value = false;  // 如果是assist，设置为false\r\n      } else {\r\n        suggestionOfficeShow.value = true;   // 否则设置为true\r\n      }\r\n      break;  // 找到匹配项后可以跳出循环\r\n    }\r\n  }\r\n}\r\nconst delaysList = ref([]) // 延期记录\r\nconst isAnswerRecords = ref(false)\r\nconst flowHandleOfficeName = ref('')\r\nconst lookdelays = ref([])\r\nconst handleLook = (item) => {\r\n  flowHandleOfficeName.value = item.flowHandleOfficeName\r\n  lookdelays.value = item.delays\r\n  isAnswerRecords.value = true\r\n}\r\nconst allhandleOfficeInfos = ref([])\r\nconst suggestionDetails = async () => {\r\n  const { data, extData } = await api.suggestionDetails({ suggestionId: route.query.id })\r\n  hasExecuteNodeIds.value = data.hasExecuteNodeIds\r\n  allhandleOfficeInfos.value = data.handleOfficeInfos\r\n  delaysList.value = []\r\n  if (route.query.type === 'postpone') {\r\n    data.handleOfficeInfos.forEach((item) => {\r\n      if (item.delays?.length) {\r\n        delaysList.value.push(\r\n          // ...item.delays.map((v) => ({ ...v, flowHandleOfficeName: item.handlerOffice.flowHandleOfficeName }))\r\n          { ...item, flowHandleOfficeName: item.handlerOffice.flowHandleOfficeName }\r\n        )\r\n      }\r\n    })\r\n  }\r\n  if (isPersonal.value) {\r\n    if (data.handlingMassing?.answerStopDate) handingPortionAnswerList()\r\n    return\r\n  }\r\n  reviewList.value = []\r\n  assignList.value = []\r\n  transactUnit.value = []\r\n  maincoOrganizers.value = []\r\n  coOrganizer.value = ''\r\n  transactObj.value = { transactType: '', mainHandleOfficeId: [], handleOfficeIds: [] }\r\n  handlingMassing.value = data.handlingMassing\r\n  isSatisfaction.value = data.isSatisfaction\r\n  satisfactions.value = data.satisfactions || []\r\n  coOrganizer.value = extData?.map(v => v.flowHandleOfficeName).join('、')\r\n  coOrganizerIds.value = extData?.map(v => v.handleOfficeId)\r\n  console.log(coOrganizerIds.value)\r\n  if (data.handlingMassing?.answerStopDate) { handingPortionAnswerList() }\r\n  for (let index = 0; index < data.streamVariables?.length; index++) {\r\n    const item = data?.streamVariables[index]\r\n    const nodeTtem = data?.streamVariables[index + 1]\r\n    if (nodeTtem) {\r\n      if (item.streamVariable?.spareB === 'verify') {\r\n        reviewList.value.push({\r\n          id: item.id,\r\n          nodeName: item.nodeName,\r\n          nodeResult: nodeTtem.streamVariable?.spareC,\r\n          UserName: nodeTtem.streamVariable?.handleUserName,\r\n          handleTime: nodeTtem.streamVariable?.handleTime,\r\n          handleContent: nodeTtem.streamVariable?.handleContent,\r\n          isSpareDict: nodeTtem.nodeId === 'rejectReceive',\r\n          spareDict: nodeTtem.streamVariable?.spareDict\r\n        })\r\n      }\r\n      if (item.streamVariable?.spareB === 'submitHandling' || item.streamVariable?.spareB === 'preSubmitHandling') {\r\n        assignList.value.push({\r\n          id: item.id,\r\n          nodeName: item.nodeName,\r\n          nodeResult: nodeTtem.streamVariable?.spareC,\r\n          UserName: nodeTtem.streamVariable?.handleUserName,\r\n          handleTime: nodeTtem.streamVariable?.handleTime,\r\n          handleContent: nodeTtem.streamVariable?.handleContent\r\n        })\r\n      }\r\n    }\r\n  }\r\n  for (let index = 0; index < data.handleOfficeInfos.length; index++) {\r\n    const item = data.handleOfficeInfos[index]\r\n    if (item.handlerOffice.handleOfficeType === 'main') {\r\n      transactObj.value.transactType = 'main_assist'\r\n      transactObj.value.mainHandleOfficeId.push(item.handlerOffice.flowHandleOfficeId)\r\n    } else if (item.handlerOffice.handleOfficeType === 'publish') {\r\n      transactObj.value.transactType = 'publish'\r\n      transactObj.value.handleOfficeIds.push(item.handlerOffice.flowHandleOfficeId)\r\n    } else {\r\n      transactObj.value.handleOfficeIds.push(item.handlerOffice.flowHandleOfficeId)\r\n    }\r\n    if (data.handlingMassing?.answerStopDate || data.handlingMassing?.confirmStopDate) {\r\n      if (item.handlerOffice.handleOfficeType !== 'assist') {\r\n        transactUnit.value.push({\r\n          id: item.handlerOffice.id,\r\n          unitId: item.handlerOffice.flowHandleOfficeId,\r\n          unitName: item.handlerOffice.flowHandleOfficeName,\r\n          unitType:\r\n            item.handlerOffice.handleOfficeType === 'main'\r\n              ? '主办'\r\n              : item.handlerOffice.handleOfficeType === 'assist'\r\n                ? '协办'\r\n                : '分办',\r\n          hasRead: item.handlerOffice.hasRead, // 是否已读\r\n          firstReadTime: item.handlerOffice.firstReadTime, // 阅读时间\r\n          status: item.handlerOffice.currentHandleStatus, // 办理状态\r\n          statusName: item.handlerOffice.currentHandleStatusName, // 办理状态\r\n          isDelays: item.delays?.filter((v) => !v.verifyStatus)?.length ? true : false || false, // 是否有待审查的延期申请\r\n          isAdjusts: item.adjusts?.filter((v) => !v.verifyStatus)?.length ? true : false || false, // 是否有待审查的调整申请\r\n          answers: item.answers?.filter((v) => v.submitAnswerType === 'direct')[0] || {}, // 最终答复件\r\n          hasConfirm: item.handlerOffice.hasConfirm,\r\n          confirmTime: item.handlerOffice.confirmTime || ''\r\n        })\r\n      } else {\r\n        maincoOrganizers.value.push(item.handlerOffice)\r\n        console.log('协办单位', maincoOrganizers.value)\r\n      }\r\n      if (\r\n        item.isCurrentLoginOfficeId &&\r\n        ['unit', 'unitTrackTransact', 'unitConclude', 'trackTransact', 'unitPreAssign'].includes(route.query.type)\r\n      ) {\r\n        transactUnitObj.value = {\r\n          ...item,\r\n          isReply: data.handlingMassing.massingAnswerDate ? true : false || false,\r\n          isDelays: item.delays?.filter((r) => !r.verifyStatus)?.length ? true : false || false,\r\n          isAdjusts: item.adjusts?.filter((v) => !v.verifyStatus)?.length ? true : false || false\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\nconst handleExportWord = () => {\r\n  if (JSON.stringify(details.value) === '{}')\r\n    return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行导出！' })\r\n  suggestionWord({ ids: [route.query.id] })\r\n}\r\nconst handleSuggestPrint = async () => {\r\n  if (JSON.stringify(details.value) === '{}')\r\n    return ElMessage({ type: 'warning', message: '请等待提案详情加载完成再进行打印！' })\r\n  printParams.value = { ids: [route.query.id] }\r\n  elPrintWhetherShow.value = true\r\n}\r\nconst suggestionWord = async (params) => {\r\n  const { data } = await api.suggestionWord(params)\r\n  if (data.length) {\r\n    var wordData = {}\r\n    for (let index = 0; index < data.length; index++) {\r\n      wordData = filterTableData(data[index])\r\n    }\r\n    exportWordHtmlObj({ code: 'proposalDetails', name: wordData.docName, key: 'content', data: wordData })\r\n  }\r\n}\r\nconst callback = (type) => {\r\n  postponeShow.value = false\r\n  elPrintWhetherShow.value = false\r\n  if (type) {\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  }\r\n}\r\nconst refreshCallback = () => {\r\n  suggestionInfo()\r\n  suggestionDetails()\r\n}\r\nconst closeCallback = () => {\r\n  qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n}\r\n\r\nconst handingPortionAnswerList = async () => {\r\n  const { data } = await api.handingPortionAnswerList({ query: { suggestionId: route.query.id } })\r\n  replyList.value = isPersonal.value ? data?.filter((v) => v.isOpen) || [] : data\r\n}\r\nconst handleReply = (item) => {\r\n  replyId.value = item.id\r\n  replyDetailShow.value = true\r\n}\r\nconst handleSatisfactions = (item) => {\r\n  satisfactionsId.value = item.id\r\n  ifShow.value = true\r\n}\r\nconst handlePostpone = (item) => {\r\n  unitRecordsId.value = item.id\r\n  postponeShow.value = true\r\n}\r\nconst handleConclude = () => {\r\n  ElMessageBox.confirm('此操作将办结该提案, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  })\r\n    .then(() => {\r\n      suggestionComplete('handleOver')\r\n    })\r\n    .catch(() => {\r\n      ElMessage({ type: 'info', message: '已取消重新办理' })\r\n    })\r\n}\r\nconst anewTransact = () => {\r\n  ElMessageBox.confirm('此操作将重新办理该提案, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  })\r\n    .then(() => {\r\n      suggestionComplete('suggestionHandling')\r\n    })\r\n    .catch(() => {\r\n      ElMessage({ type: 'info', message: '已取消重新办理' })\r\n    })\r\n}\r\nconst suggestionComplete = async (nextNodeId) => {\r\n  const { code } = await api.suggestionComplete({ suggestionId: route.query.id, nextNodeId: nextNodeId })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '操作成功' })\r\n    closeCallback()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './SuggestDetail.scss';\r\n\r\n.SuggestDetail {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .suggestPrint {\r\n    width: 790px;\r\n    position: fixed;\r\n    top: -100%;\r\n    left: -100%;\r\n  }\r\n\r\n  .detailsPrint,\r\n  .detailsExportInfo {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding-left: 30px;\r\n    margin-bottom: 20px;\r\n    position: relative;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .detailsPrint {\r\n    background: url('../../assets/img/suggest_details_print.png') no-repeat;\r\n    background-size: 20px 20px;\r\n    background-position: left center;\r\n  }\r\n\r\n  .detailsExportInfo {\r\n    background: url('../../assets/img/suggest_details_export_info.png') no-repeat;\r\n    background-size: 20px 20px;\r\n    background-position: left center;\r\n  }\r\n\r\n  .anchor-location-item {\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n  }\r\n\r\n  .SuggestDetailProcessInfo {\r\n    padding: var(--zy-distance-one);\r\n    padding-bottom: 0;\r\n  }\r\n\r\n  .SuggestLabelName {\r\n    width: 100%;\r\n    font-weight: bold;\r\n    position: relative;\r\n    padding: 20px 28px;\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      width: 20px;\r\n      height: 20px;\r\n      transform: translateY(-50%);\r\n      background: url('../../assets/img/suggest_details_icon.png') no-repeat;\r\n      background-color: var(--zy-el-color-primary);\r\n      background-size: 20px 20px;\r\n      background-position: center center;\r\n    }\r\n\r\n    .SuggestLabelNameButton {\r\n      position: absolute;\r\n      top: 50%;\r\n      right: 0;\r\n      transform: translateY(-50%);\r\n\r\n      .zy-el-button {\r\n        --zy-el-button-size: var(--zy-height-secondary);\r\n      }\r\n    }\r\n\r\n    .SuggestDetailBodyActive {\r\n      font-weight: normal;\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: 1;\r\n      cursor: pointer;\r\n      margin-left: 12px;\r\n      display: inline-flex;\r\n      align-items: center;\r\n\r\n      .zy-el-icon {\r\n        font-size: var(--zy-name-font-size);\r\n        transform: rotate(180deg);\r\n        transition: transform 0.5s ease;\r\n        margin-left: 2px;\r\n      }\r\n    }\r\n\r\n    .SuggestDetailBodyActive.is-ctive {\r\n      .zy-el-icon {\r\n        transform: rotate(0deg);\r\n        transition: transform 0.5s ease;\r\n      }\r\n    }\r\n  }\r\n\r\n  .SuggestDetailProcessNodeBody {\r\n    width: 100%;\r\n    padding-bottom: 20px;\r\n\r\n    .SuggestDetailProcessNodeMain {\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .SuggestDetailProcessNodeInfo+.SuggestDetailProcessNodeInfo {\r\n        .SuggestDetailProcessNodeIcon {\r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 50%;\r\n            left: -99px;\r\n            width: 99px;\r\n            transform: translateY(-50%);\r\n            border-top: 1px dashed var(--zy-el-text-color-regular);\r\n          }\r\n        }\r\n      }\r\n\r\n      .SuggestDetailProcessNodeInfo {\r\n        flex: 1;\r\n\r\n        .SuggestDetailProcessNode {\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          text-align: center;\r\n        }\r\n\r\n        .SuggestDetailProcessNodeIcon {\r\n          position: relative;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          width: 30px;\r\n          height: 30px;\r\n          border-radius: 50%;\r\n          border: 1px solid var(--zy-el-text-color-regular);\r\n          color: var(--zy-el-text-color-regular);\r\n          font-size: var(--zy-name-font-size);\r\n          margin: auto;\r\n          z-index: 9;\r\n        }\r\n\r\n        &:nth-child(7) {\r\n          .SuggestDetailProcessNodeIcon {\r\n            &::before {\r\n              content: '';\r\n              position: absolute;\r\n              top: 100%;\r\n              left: 50%;\r\n              height: 45px;\r\n              transform: translateX(-50%);\r\n              border-left: 1px dashed var(--zy-el-text-color-regular);\r\n            }\r\n          }\r\n\r\n          .SuggestDetailProcessNodeIcon.is-active.is-active-other {\r\n            &::after {\r\n              border-color: var(--zy-el-text-color-regular);\r\n            }\r\n\r\n            &::before {\r\n              border-color: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n\r\n        .SuggestDetailProcessNodeIcon.is-active {\r\n          color: #fff;\r\n          background-color: var(--zy-el-color-primary);\r\n          border: 1px solid var(--zy-el-color-primary);\r\n\r\n          &::after {\r\n            border-color: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .SuggestDetailProcessNodeAssist {\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: 30px;\r\n\r\n      .SuggestDetailProcessNodeInfo {\r\n        flex: 1;\r\n\r\n        .SuggestDetailProcessNode {\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          text-align: center;\r\n        }\r\n\r\n        .SuggestDetailProcessNodeIcon {\r\n          position: relative;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          width: 30px;\r\n          height: 30px;\r\n          border-radius: 50%;\r\n          border: 1px solid var(--zy-el-text-color-regular);\r\n          color: var(--zy-el-text-color-regular);\r\n          font-size: var(--zy-name-font-size);\r\n          margin: auto;\r\n          z-index: 2;\r\n        }\r\n\r\n        &:nth-child(2) {\r\n          .SuggestDetailProcessNodeIcon {\r\n            border: 0;\r\n\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              bottom: 50%;\r\n              left: 50%;\r\n              height: 60px;\r\n              transform: translateX(-50%);\r\n              border-left: 1px dashed var(--zy-el-text-color-regular);\r\n            }\r\n          }\r\n\r\n          .SuggestDetailProcessNodeIcon.is-active {\r\n            background-color: transparent;\r\n          }\r\n        }\r\n\r\n        &:nth-child(3) {\r\n          .SuggestDetailProcessNodeIcon {\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              top: 50%;\r\n              left: -115px;\r\n              width: 115px;\r\n              transform: translateY(-50%);\r\n              border-top: 1px dashed var(--zy-el-text-color-regular);\r\n            }\r\n\r\n            .SuggestDetailProcessNode {\r\n              position: absolute;\r\n              top: 50%;\r\n              left: 100%;\r\n              transform: translateY(-50%);\r\n              white-space: nowrap;\r\n              color: var(--zy-el-text-color-regular);\r\n              padding-left: 10px;\r\n            }\r\n          }\r\n        }\r\n\r\n        &:nth-child(4) {\r\n          .SuggestDetailProcessNodeIcon {\r\n            border: 0;\r\n\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              bottom: 50%;\r\n              left: 50%;\r\n              height: 60px;\r\n              transform: translateX(-50%);\r\n              border-left: 1px dashed var(--zy-el-text-color-regular);\r\n            }\r\n          }\r\n        }\r\n\r\n        &:nth-child(5) {\r\n          .SuggestDetailProcessNodeIcon {\r\n            border: 0;\r\n\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              top: 50%;\r\n              left: -115px;\r\n              width: 245px;\r\n              transform: translateY(-50%);\r\n              border-top: 1px dashed var(--zy-el-text-color-regular);\r\n            }\r\n          }\r\n        }\r\n\r\n        &:nth-child(6) {\r\n          .SuggestDetailProcessNodeIcon {\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              bottom: 100%;\r\n              left: 50%;\r\n              height: 30px;\r\n              transform: translateX(-50%);\r\n              border-left: 1px dashed var(--zy-el-text-color-regular);\r\n            }\r\n\r\n            .SuggestDetailProcessNode {\r\n              position: absolute;\r\n              top: 100%;\r\n              left: 50%;\r\n              transform: translateX(-50%);\r\n              white-space: nowrap;\r\n              color: var(--zy-el-text-color-regular);\r\n            }\r\n          }\r\n        }\r\n\r\n        &:nth-child(7) {\r\n          .SuggestDetailProcessNodeIcon {\r\n            border: 0;\r\n\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              top: 50%;\r\n              left: -100px;\r\n              width: 115px;\r\n              transform: translateY(-50%);\r\n              border-top: 1px dashed var(--zy-el-text-color-regular);\r\n            }\r\n          }\r\n\r\n          .SuggestDetailProcessNodeIcon.is-active {\r\n            background-color: transparent;\r\n            border: 0;\r\n          }\r\n        }\r\n\r\n        .SuggestDetailProcessNodeIcon.is-active {\r\n          color: #fff;\r\n          background-color: var(--zy-el-color-primary);\r\n          border: 1px solid var(--zy-el-color-primary);\r\n\r\n          &::after {\r\n            border-color: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .global-info {\r\n    padding-bottom: 12px;\r\n\r\n    .global-info-item {\r\n      .global-info-label {\r\n        width: 160px;\r\n      }\r\n\r\n      .global-info-content {\r\n        width: calc(100% - 160px);\r\n      }\r\n    }\r\n  }\r\n\r\n  .SuggestRead {\r\n    padding-left: 26px;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    color: var(--zy-el-text-color-regular);\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      transform: translateY(-50%);\r\n      width: 20px;\r\n      height: 20px;\r\n      background: url(\"../../assets/img/suggest_details_read.png\") no-repeat;\r\n      background-color: var(--zy-el-color-info);\r\n      background-size: 100% 100%;\r\n    }\r\n  }\r\n\r\n  .SuggestUnRead {\r\n    padding-left: 26px;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      transform: translateY(-50%);\r\n      width: 20px;\r\n      height: 20px;\r\n      background: url(\"../../assets/img/suggest_details_unread.png\") no-repeat;\r\n      background-color: var(--zy-el-color-danger);\r\n      background-size: 100% 100%;\r\n    }\r\n  }\r\n\r\n  .SuggestReviewUnit {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .zy-el-link {\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n\r\n  .SuggestDetailTable {\r\n    width: 100%;\r\n    margin-bottom: 20px;\r\n    border-top: 1px solid var(--zy-el-border-color-lighter);\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .SuggestDetailTableHead,\r\n    .SuggestDetailTableBody {\r\n      width: 100%;\r\n      display: flex;\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    }\r\n\r\n    .SuggestDetailTableHead {\r\n      background-color: var(--zy-el-color-info-light-9);\r\n    }\r\n\r\n    .SuggestDetailTableBody {\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    }\r\n\r\n    .SuggestDetailTableItem {\r\n      text-align: center;\r\n      border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: 10px;\r\n      overflow: hidden;\r\n      white-space: nowrap;\r\n    }\r\n\r\n    .row2 {\r\n      flex: 2;\r\n    }\r\n\r\n    .row3 {\r\n      flex: 3;\r\n    }\r\n\r\n    .row5 {\r\n      flex: 5;\r\n    }\r\n  }\r\n\r\n  .SuggestReplyButton {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: var(--zy-distance-two) var(--zy-distance-one);\r\n  }\r\n\r\n  .SuggestDetailProcessInfo+.SuggestDetailProcessInfo {\r\n    padding-top: 0;\r\n  }\r\n\r\n  .SuggestSegmentation {\r\n    width: 100%;\r\n    height: 10px;\r\n    background-color: var(--zy-el-color-info-light-9);\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1580px) {\r\n  .SuggestDetail {\r\n\r\n    .detailsPrint,\r\n    .detailsExportInfo {\r\n      color: transparent;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;+CA0rBA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,KAAK;AAChD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,gBAAgB,MAAM,kCAAkC,EAAC;AAChE,OAAOC,mBAAmB,MAAM,yDAAyD,EAAC;AAC1F,OAAOC,qBAAqB,MAAM,qEAAqE,EAAC;AACxG,OAAOC,mBAAmB,MAAM,iEAAiE,EAAC;AAClG,OAAOC,0BAA0B,MAAM,uEAAuE,EAAC;AAC/G,OAAOC,iBAAiB,MAAM,iDAAiD,EAAC;AAChF,OAAOC,qBAAqB,MAAM,mDAAmD,EAAC;AACtF,OAAOC,oBAAoB,MAAM,iDAAiD,EAAC;AACnF,OAAOC,4BAA4B,MAAM,iEAAiE,EAAC;AAC3G,OAAOC,sBAAsB,MAAM,qDAAqD,EAAC;AACzF,OAAOC,kBAAkB,MAAM,6CAA6C,EAAC;AAC7E,OAAOC,wBAAwB,MAAM,yDAAyD,EAAC;AAC/F,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,OAAOC,yBAAyB,MAAM,mEAAmE;AAzBzG,IAAAC,WAAA,GAAe;EAAErD,IAAI,EAAE;AAAgB,CAAC;;;;;IA0BxC,IAAMsD,KAAK,GAAGvB,QAAQ,CAAC,CAAC;IACxB,IAAMwB,WAAW,GAAG3B,GAAG,CAAC,MAAM,CAAC;IAE/B,IAAM4B,WAAW,GAAG5B,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAM6B,kBAAkB,GAAG7B,GAAG,CAAC,KAAK,CAAC;IAErC,IAAM8B,OAAO,GAAG9B,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAM+B,YAAY,GAAG/B,GAAG,CAAC,EAAE,CAAC;IAE5B,IAAMgC,WAAW,GAAGhC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC;;IAE5B,IAAMiC,eAAe,GAAGjC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAMkC,eAAe,GAAGlC,GAAG,CAAC,KAAK,CAAC;IAClC,IAAMmC,cAAc,GAAGnC,GAAG,CAAC,CAAC,CAAC;IAC7B,IAAMoC,iBAAiB,GAAGpC,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMqC,UAAU,GAAGrC,GAAG,CAAC,EAAE,CAAC,EAAC;IAC3B,IAAMsC,UAAU,GAAGtC,GAAG,CAAC,EAAE,CAAC,EAAC;IAC3B,IAAMuC,YAAY,GAAGvC,GAAG,CAAC,EAAE,CAAC,EAAC;IAC7B,IAAMwC,gBAAgB,GAAGxC,GAAG,CAAC,EAAE,CAAC,EAAC;IACjC,IAAMyC,WAAW,GAAGzC,GAAG,CAAC,EAAE,CAAC,EAAC;IAC5B,IAAM0C,cAAc,GAAG1C,GAAG,CAAC,EAAE,CAAC,EAAC;IAC/B,IAAM2C,eAAe,GAAG3C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC;;IAEhC,IAAM4C,eAAe,GAAG5C,GAAG,CAAC,KAAK,CAAC;IAClC,IAAM6C,cAAc,GAAG7C,GAAG,CAAC,KAAK,CAAC;IACjC,IAAM8C,eAAe,GAAG9C,GAAG,CAAC,KAAK,CAAC;IAElC,IAAM+C,SAAS,GAAG/C,GAAG,CAAC,EAAE,CAAC,EAAC;IAC1B,IAAMgD,OAAO,GAAGhD,GAAG,CAAC,EAAE,CAAC,EAAC;IACxB,IAAMiD,eAAe,GAAGjD,GAAG,CAAC,KAAK,CAAC;IAElC,IAAMkD,MAAM,GAAGlD,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMmD,eAAe,GAAGnD,GAAG,CAAC,EAAE,CAAC,EAAC;IAChC,IAAMoD,aAAa,GAAGpD,GAAG,CAAC,EAAE,CAAC,EAAC;;IAE9B,IAAMqD,aAAa,GAAGrD,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMsD,YAAY,GAAGtD,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMuD,IAAI,GAAGvD,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMwD,WAAW,GAAGxD,GAAG,CAAC,KAAK,CAAC,EAAC;IAC/B,IAAMyD,UAAU,GAAGxD,QAAQ,CAAC;MAAA,OAAMyB,KAAK,CAACgC,KAAK,CAACC,IAAI,KAAK,UAAU;IAAA,EAAC;IAClE,IAAMC,kBAAkB,GAAG5D,GAAG,CAAC,EAAE,CAAC;IAClC,IAAM6D,oBAAoB,GAAG7D,GAAG,CAAC,IAAI,CAAC;IAEtCE,WAAW,CAAC,YAAM;MAChB4D,gBAAgB,CAAC,CAAC;MAClB,IAAIpC,KAAK,CAACgC,KAAK,CAACK,EAAE,EAAE;QAClBC,cAAc,CAAC,CAAC;QAChBC,iBAAiB,CAAC,CAAC;MACrB;MACA;MACA;MACA;IACF,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA,IAAMH,gBAAgB;MAAA,IAAAI,KAAA,GAAAxE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8F,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAApL,mBAAA,GAAAuB,IAAA,UAAA8J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAzF,IAAA,GAAAyF,QAAA,CAAApH,IAAA;YAAA;cAAAoH,QAAA,CAAApH,IAAA;cAAA,OACA4C,GAAG,CAAC+D,gBAAgB,CAAC;gBAC1CU,KAAK,EAAE,CAAC,4BAA4B;cACtC,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAG,QAAA,CAAA3H,IAAA;cAFMyH,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAGZb,WAAW,CAAC7J,KAAK,GAAG,CAAA0K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,0BAA0B,MAAK,MAAM;YAAA;YAAA;cAAA,OAAAF,QAAA,CAAAtF,IAAA;UAAA;QAAA,GAAAkF,OAAA;MAAA,CAChE;MAAA,gBALKL,gBAAgBA,CAAA;QAAA,OAAAI,KAAA,CAAAtE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKrB;IACD,IAAM+E,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9BtE,YAAY,CAACuE,cAAc,CAAC;QAC1BC,SAAS,EAAE;UAAEzG,IAAI,EAAE,MAAM;UAAE0G,IAAI,EAAE,yBAAyB;UAAEpB,KAAK,EAAE;YAAEK,EAAE,EAAEY,IAAI,CAACI;UAAW;QAAE;MAC7F,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,KAAK,EAAK;MAC1B,IAAIC,KAAK,GAAG;QAAEA,KAAK,EAAE;MAAO,CAAC;MAC7B,IAAID,KAAK,KAAK,YAAY,EAAE;QAC1BC,KAAK,CAACA,KAAK,GAAG,SAAS;MACzB,CAAC,MAAM,IAAID,KAAK,KAAK,UAAU,EAAE;QAC/BC,KAAK,CAACA,KAAK,GAAG,SAAS;MACzB,CAAC,MAAM,IAAID,KAAK,KAAK,cAAc,EAAE;QACnCC,KAAK,CAACA,KAAK,GAAG,SAAS;MACzB;MACA,OAAOA,KAAK;IACd,CAAC;IACD,IAAMC,aAAa,GAAGnF,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAMoF,gBAAgB,GAAGpF,GAAG,CAAC,KAAK,CAAC;IACnC,IAAMgE,cAAc;MAAA,IAAAqB,KAAA,GAAA3F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiH,SAAA;QAAA,IAAAC,qBAAA,EAAAlB,IAAA,EAAAmB,OAAA;QAAA,OAAAvM,mBAAA,GAAAuB,IAAA,UAAAiL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,IAAA,GAAA4G,SAAA,CAAAvI,IAAA;YAAA;cAAAuI,SAAA,CAAAvI,IAAA;cAAA,OACW4C,GAAG,CAACiE,cAAc,CAAC;gBAAE2B,QAAQ,EAAEjE,KAAK,CAACgC,KAAK,CAACK;cAAG,CAAC,CAAC;YAAA;cAAAwB,qBAAA,GAAAG,SAAA,CAAA9I,IAAA;cAAxEyH,IAAI,GAAAkB,qBAAA,CAAJlB,IAAI;cAAEmB,OAAO,GAAAD,qBAAA,CAAPC,OAAO;cACrBnB,IAAI,CAACuB,OAAO,GAAGvB,IAAI,CAACuB,OAAO,CAACC,OAAO,CAAC,MAAM,EAAE,4FAA4F,CAAC;cACzI/D,OAAO,CAACnI,KAAK,GAAG0K,IAAI;cACpBtC,YAAY,CAACpI,KAAK,GAAG6L,OAAO;cAC5BM,mBAAmB,CAAC,CAAC;cACrB,IAAIzB,IAAI,CAACc,aAAa,EAAE;gBACtBC,gBAAgB,CAACzL,KAAK,GAAG,IAAI;gBAC7BwL,aAAa,CAACxL,KAAK,GAAG0K,IAAI,CAACc,aAAa;cAC1C;YAAC;YAAA;cAAA,OAAAO,SAAA,CAAAzG,IAAA;UAAA;QAAA,GAAAqG,QAAA;MAAA,CACF;MAAA,gBAVKtB,cAAcA,CAAA;QAAA,OAAAqB,KAAA,CAAAzF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAUnB;IACD;IACA,IAAMmG,mBAAmB;MAAA,IAAAC,KAAA,GAAArG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2H,SAAA;QAAA,IAAAC,MAAA;QAAA,IAAAC,qBAAA,EAAA7B,IAAA,EAAAzK,CAAA;QAAA,OAAAX,mBAAA,GAAAuB,IAAA,UAAA2L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtH,IAAA,GAAAsH,SAAA,CAAAjJ,IAAA;YAAA;cAAAiJ,SAAA,CAAAjJ,IAAA;cAAA,OACH4C,GAAG,CAAC+F,mBAAmB,CAAC;gBAC7CO,KAAK,EAAE,CAAC;gBACRC,OAAO,EAAE/E,IAAI,CAAC5H,KAAK,CAAC4M,QAAQ;gBAC5BC,OAAO,EAAE,iCAAiC;gBAC1CC,MAAM,EAAE,GAAG;gBACXC,QAAQ,EAAE;cACZ,CAAC,CAAC;YAAA;cAAAR,qBAAA,GAAAE,SAAA,CAAAxJ,IAAA;cANMyH,IAAI,GAAA6B,qBAAA,CAAJ7B,IAAI;cAOZT,kBAAkB,CAACjK,KAAK,IAAAsM,MAAA,GAAG5B,IAAI,CAAC,CAAC,CAAC,cAAA4B,MAAA,uBAAPA,MAAA,CAASrC,kBAAkB;cAC7ChK,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAGmI,YAAY,CAACpI,KAAK,CAACqE,MAAM;gBAAAoI,SAAA,CAAAjJ,IAAA;gBAAA;cAAA;cAAA,MACvC4E,YAAY,CAACpI,KAAK,CAACC,CAAC,CAAC,CAAC+M,cAAc,KAAK/C,kBAAkB,CAACjK,KAAK;gBAAAyM,SAAA,CAAAjJ,IAAA;gBAAA;cAAA;cACnE;cACA,IAAI4E,YAAY,CAACpI,KAAK,CAACC,CAAC,CAAC,CAACgN,gBAAgB,KAAK,QAAQ,IAAI7E,YAAY,CAACpI,KAAK,CAACC,CAAC,CAAC,CAACgN,gBAAgB,KAAK,cAAc,EAAE;gBACpH/C,oBAAoB,CAAClK,KAAK,GAAG,KAAK,CAAC,CAAE;cACvC,CAAC,MAAM;gBACLkK,oBAAoB,CAAClK,KAAK,GAAG,IAAI,CAAC,CAAG;cACvC;cAAC,OAAAyM,SAAA,CAAArJ,MAAA;YAAA;cAP0CnD,CAAC,EAAE;cAAAwM,SAAA,CAAAjJ,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAiJ,SAAA,CAAAnH,IAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA,CAWnD;MAAA,gBApBKF,mBAAmBA,CAAA;QAAA,OAAAC,KAAA,CAAAnG,KAAA,OAAAD,SAAA;MAAA;IAAA,GAoBxB;IACD,IAAMkH,UAAU,GAAG7G,GAAG,CAAC,EAAE,CAAC,EAAC;IAC3B,IAAM8G,eAAe,GAAG9G,GAAG,CAAC,KAAK,CAAC;IAClC,IAAM+G,oBAAoB,GAAG/G,GAAG,CAAC,EAAE,CAAC;IACpC,IAAMgH,UAAU,GAAGhH,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMiH,UAAU,GAAG,SAAbA,UAAUA,CAAItC,IAAI,EAAK;MAC3BoC,oBAAoB,CAACpN,KAAK,GAAGgL,IAAI,CAACoC,oBAAoB;MACtDC,UAAU,CAACrN,KAAK,GAAGgL,IAAI,CAACuC,MAAM;MAC9BJ,eAAe,CAACnN,KAAK,GAAG,IAAI;IAC9B,CAAC;IACD,IAAMwN,oBAAoB,GAAGnH,GAAG,CAAC,EAAE,CAAC;IACpC,IAAMiE,iBAAiB;MAAA,IAAAmD,KAAA,GAAA1H,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgJ,SAAA;QAAA,IAAAC,sBAAA;QAAA,IAAAC,sBAAA,EAAAlD,IAAA,EAAAmB,OAAA,EAAAgC,qBAAA,EAAAC,KAAA,EAAAC,qBAAA,EAAA/C,IAAA,EAAAgD,QAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,MAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,KAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,cAAA;QAAA,OAAA/P,mBAAA,GAAAuB,IAAA,UAAAyO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApK,IAAA,GAAAoK,SAAA,CAAA/L,IAAA;YAAA;cAAA+L,SAAA,CAAA/L,IAAA;cAAA,OACQ4C,GAAG,CAACkE,iBAAiB,CAAC;gBAAEkF,YAAY,EAAEzH,KAAK,CAACgC,KAAK,CAACK;cAAG,CAAC,CAAC;YAAA;cAAAwD,sBAAA,GAAA2B,SAAA,CAAAtM,IAAA;cAA/EyH,IAAI,GAAAkD,sBAAA,CAAJlD,IAAI;cAAEmB,OAAO,GAAA+B,sBAAA,CAAP/B,OAAO;cACrBpD,iBAAiB,CAACzI,KAAK,GAAG0K,IAAI,CAACjC,iBAAiB;cAChD+E,oBAAoB,CAACxN,KAAK,GAAG0K,IAAI,CAAC+E,iBAAiB;cACnDvC,UAAU,CAAClN,KAAK,GAAG,EAAE;cACrB,IAAI+H,KAAK,CAACgC,KAAK,CAAC5I,IAAI,KAAK,UAAU,EAAE;gBACnCuJ,IAAI,CAAC+E,iBAAiB,CAACrN,OAAO,CAAC,UAAC4I,IAAI,EAAK;kBAAA,IAAA0E,YAAA;kBACvC,KAAAA,YAAA,GAAI1E,IAAI,CAACuC,MAAM,cAAAmC,YAAA,eAAXA,YAAA,CAAarL,MAAM,EAAE;oBACvB6I,UAAU,CAAClN,KAAK,CAACgE,IAAI,CACnB;oBAAA2L,aAAA,CAAAA,aAAA,KACK3E,IAAI;sBAAEoC,oBAAoB,EAAEpC,IAAI,CAAC4E,aAAa,CAACxC;oBAAoB,EAC1E,CAAC;kBACH;gBACF,CAAC,CAAC;cACJ;cAAC,KACGtD,UAAU,CAAC9J,KAAK;gBAAAuP,SAAA,CAAA/L,IAAA;gBAAA;cAAA;cAClB,KAAAqK,qBAAA,GAAInD,IAAI,CAACpC,eAAe,cAAAuF,qBAAA,eAApBA,qBAAA,CAAsBgC,cAAc,EAAEC,wBAAwB,CAAC,CAAC;cAAA,OAAAP,SAAA,CAAAnM,MAAA;YAAA;cAGtEsF,UAAU,CAAC1I,KAAK,GAAG,EAAE;cACrB2I,UAAU,CAAC3I,KAAK,GAAG,EAAE;cACrB4I,YAAY,CAAC5I,KAAK,GAAG,EAAE;cACvB6I,gBAAgB,CAAC7I,KAAK,GAAG,EAAE;cAC3B8I,WAAW,CAAC9I,KAAK,GAAG,EAAE;cACtBqI,WAAW,CAACrI,KAAK,GAAG;gBAAE+P,YAAY,EAAE,EAAE;gBAAEC,kBAAkB,EAAE,EAAE;gBAAEC,eAAe,EAAE;cAAG,CAAC;cACrF3H,eAAe,CAACtI,KAAK,GAAG0K,IAAI,CAACpC,eAAe;cAC5CE,cAAc,CAACxI,KAAK,GAAG0K,IAAI,CAAClC,cAAc;cAC1CiB,aAAa,CAACzJ,KAAK,GAAG0K,IAAI,CAACjB,aAAa,IAAI,EAAE;cAC9CX,WAAW,CAAC9I,KAAK,GAAG6L,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqE,GAAG,CAAC,UAAAlO,CAAC;gBAAA,OAAIA,CAAC,CAACoL,oBAAoB;cAAA,EAAC,CAAC+C,IAAI,CAAC,GAAG,CAAC;cACvEpH,cAAc,CAAC/I,KAAK,GAAG6L,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqE,GAAG,CAAC,UAAAlO,CAAC;gBAAA,OAAIA,CAAC,CAACgL,cAAc;cAAA,EAAC;cAC1DoD,OAAO,CAACC,GAAG,CAACtH,cAAc,CAAC/I,KAAK,CAAC;cACjC,KAAA2N,sBAAA,GAAIjD,IAAI,CAACpC,eAAe,cAAAqF,sBAAA,eAApBA,sBAAA,CAAsBkC,cAAc,EAAE;gBAAEC,wBAAwB,CAAC,CAAC;cAAC;cACvE,KAAShC,KAAK,GAAG,CAAC,EAAEA,KAAK,KAAAC,qBAAA,GAAGrD,IAAI,CAAC4F,eAAe,cAAAvC,qBAAA,uBAApBA,qBAAA,CAAsB1J,MAAM,GAAEyJ,KAAK,EAAE,EAAE;gBAC3D9C,IAAI,GAAGN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4F,eAAe,CAACxC,KAAK,CAAC;gBACnCE,QAAQ,GAAGtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4F,eAAe,CAACxC,KAAK,GAAG,CAAC,CAAC;gBACjD,IAAIE,QAAQ,EAAE;kBACZ,IAAI,EAAAC,oBAAA,GAAAjD,IAAI,CAACuF,cAAc,cAAAtC,oBAAA,uBAAnBA,oBAAA,CAAqBuC,MAAM,MAAK,QAAQ,EAAE;oBAC5C9H,UAAU,CAAC1I,KAAK,CAACgE,IAAI,CAAC;sBACpBoG,EAAE,EAAEY,IAAI,CAACZ,EAAE;sBACXqG,QAAQ,EAAEzF,IAAI,CAACyF,QAAQ;sBACvBC,UAAU,GAAAtC,qBAAA,GAAEJ,QAAQ,CAACuC,cAAc,cAAAnC,qBAAA,uBAAvBA,qBAAA,CAAyBuC,MAAM;sBAC3CC,QAAQ,GAAAvC,sBAAA,GAAEL,QAAQ,CAACuC,cAAc,cAAAlC,sBAAA,uBAAvBA,sBAAA,CAAyBwC,cAAc;sBACjDC,UAAU,GAAAxC,sBAAA,GAAEN,QAAQ,CAACuC,cAAc,cAAAjC,sBAAA,uBAAvBA,sBAAA,CAAyBwC,UAAU;sBAC/CC,aAAa,GAAAxC,sBAAA,GAAEP,QAAQ,CAACuC,cAAc,cAAAhC,sBAAA,uBAAvBA,sBAAA,CAAyBwC,aAAa;sBACrDC,WAAW,EAAEhD,QAAQ,CAACiD,MAAM,KAAK,eAAe;sBAChDC,SAAS,GAAA1C,sBAAA,GAAER,QAAQ,CAACuC,cAAc,cAAA/B,sBAAA,uBAAvBA,sBAAA,CAAyB0C;oBACtC,CAAC,CAAC;kBACJ;kBACA,IAAI,EAAAhD,qBAAA,GAAAlD,IAAI,CAACuF,cAAc,cAAArC,qBAAA,uBAAnBA,qBAAA,CAAqBsC,MAAM,MAAK,gBAAgB,IAAI,EAAArC,qBAAA,GAAAnD,IAAI,CAACuF,cAAc,cAAApC,qBAAA,uBAAnBA,qBAAA,CAAqBqC,MAAM,MAAK,mBAAmB,EAAE;oBAC3G7H,UAAU,CAAC3I,KAAK,CAACgE,IAAI,CAAC;sBACpBoG,EAAE,EAAEY,IAAI,CAACZ,EAAE;sBACXqG,QAAQ,EAAEzF,IAAI,CAACyF,QAAQ;sBACvBC,UAAU,GAAAjC,sBAAA,GAAET,QAAQ,CAACuC,cAAc,cAAA9B,sBAAA,uBAAvBA,sBAAA,CAAyBkC,MAAM;sBAC3CC,QAAQ,GAAAlC,sBAAA,GAAEV,QAAQ,CAACuC,cAAc,cAAA7B,sBAAA,uBAAvBA,sBAAA,CAAyBmC,cAAc;sBACjDC,UAAU,GAAAnC,sBAAA,GAAEX,QAAQ,CAACuC,cAAc,cAAA5B,sBAAA,uBAAvBA,sBAAA,CAAyBmC,UAAU;sBAC/CC,aAAa,GAAAnC,sBAAA,GAAEZ,QAAQ,CAACuC,cAAc,cAAA3B,sBAAA,uBAAvBA,sBAAA,CAAyBmC;oBAC1C,CAAC,CAAC;kBACJ;gBACF;cACF;cACA,KAASjD,MAAK,GAAG,CAAC,EAAEA,MAAK,GAAGpD,IAAI,CAAC+E,iBAAiB,CAACpL,MAAM,EAAEyJ,MAAK,EAAE,EAAE;gBAC5D9C,KAAI,GAAGN,IAAI,CAAC+E,iBAAiB,CAAC3B,MAAK,CAAC;gBAC1C,IAAI9C,KAAI,CAAC4E,aAAa,CAAC3C,gBAAgB,KAAK,MAAM,EAAE;kBAClD5E,WAAW,CAACrI,KAAK,CAAC+P,YAAY,GAAG,aAAa;kBAC9C1H,WAAW,CAACrI,KAAK,CAACgQ,kBAAkB,CAAChM,IAAI,CAACgH,KAAI,CAAC4E,aAAa,CAACuB,kBAAkB,CAAC;gBAClF,CAAC,MAAM,IAAInG,KAAI,CAAC4E,aAAa,CAAC3C,gBAAgB,KAAK,SAAS,EAAE;kBAC5D5E,WAAW,CAACrI,KAAK,CAAC+P,YAAY,GAAG,SAAS;kBAC1C1H,WAAW,CAACrI,KAAK,CAACiQ,eAAe,CAACjM,IAAI,CAACgH,KAAI,CAAC4E,aAAa,CAACuB,kBAAkB,CAAC;gBAC/E,CAAC,MAAM;kBACL9I,WAAW,CAACrI,KAAK,CAACiQ,eAAe,CAACjM,IAAI,CAACgH,KAAI,CAAC4E,aAAa,CAACuB,kBAAkB,CAAC;gBAC/E;gBACA,IAAI,CAAArC,sBAAA,GAAApE,IAAI,CAACpC,eAAe,cAAAwG,sBAAA,eAApBA,sBAAA,CAAsBe,cAAc,KAAAd,sBAAA,GAAIrE,IAAI,CAACpC,eAAe,cAAAyG,sBAAA,eAApBA,sBAAA,CAAsBqC,eAAe,EAAE;kBACjF,IAAIpG,KAAI,CAAC4E,aAAa,CAAC3C,gBAAgB,KAAK,QAAQ,EAAE;oBACpDrE,YAAY,CAAC5I,KAAK,CAACgE,IAAI,CAAC;sBACtBoG,EAAE,EAAEY,KAAI,CAAC4E,aAAa,CAACxF,EAAE;sBACzBiH,MAAM,EAAErG,KAAI,CAAC4E,aAAa,CAACuB,kBAAkB;sBAC7CG,QAAQ,EAAEtG,KAAI,CAAC4E,aAAa,CAACxC,oBAAoB;sBACjDmE,QAAQ,EACNvG,KAAI,CAAC4E,aAAa,CAAC3C,gBAAgB,KAAK,MAAM,GAC1C,IAAI,GACJjC,KAAI,CAAC4E,aAAa,CAAC3C,gBAAgB,KAAK,QAAQ,GAC9C,IAAI,GACJ,IAAI;sBACZuE,OAAO,EAAExG,KAAI,CAAC4E,aAAa,CAAC4B,OAAO;sBAAE;sBACrCC,aAAa,EAAEzG,KAAI,CAAC4E,aAAa,CAAC6B,aAAa;sBAAE;sBACjDC,MAAM,EAAE1G,KAAI,CAAC4E,aAAa,CAAC+B,mBAAmB;sBAAE;sBAChDC,UAAU,EAAE5G,KAAI,CAAC4E,aAAa,CAACiC,uBAAuB;sBAAE;sBACxDC,QAAQ,EAAE,CAAA7C,aAAA,GAAAjE,KAAI,CAACuC,MAAM,cAAA0B,aAAA,gBAAAA,aAAA,GAAXA,aAAA,CAAa8C,MAAM,CAAC,UAAC/P,CAAC;wBAAA,OAAK,CAACA,CAAC,CAACgQ,YAAY;sBAAA,EAAC,cAAA/C,aAAA,eAA3CA,aAAA,CAA6C5K,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK;sBAAE;sBACvF4N,SAAS,EAAE,CAAA/C,aAAA,GAAAlE,KAAI,CAACkH,OAAO,cAAAhD,aAAA,gBAAAA,aAAA,GAAZA,aAAA,CAAc6C,MAAM,CAAC,UAAC/P,CAAC;wBAAA,OAAK,CAACA,CAAC,CAACgQ,YAAY;sBAAA,EAAC,cAAA9C,aAAA,eAA5CA,aAAA,CAA8C7K,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK;sBAAE;sBACzF8N,OAAO,EAAE,EAAAhD,aAAA,GAAAnE,KAAI,CAACmH,OAAO,cAAAhD,aAAA,uBAAZA,aAAA,CAAc4C,MAAM,CAAC,UAAC/P,CAAC;wBAAA,OAAKA,CAAC,CAACoQ,gBAAgB,KAAK,QAAQ;sBAAA,EAAC,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC;sBAAE;sBAChFC,UAAU,EAAErH,KAAI,CAAC4E,aAAa,CAACyC,UAAU;sBACzCC,WAAW,EAAEtH,KAAI,CAAC4E,aAAa,CAAC0C,WAAW,IAAI;oBACjD,CAAC,CAAC;kBACJ,CAAC,MAAM;oBACLzJ,gBAAgB,CAAC7I,KAAK,CAACgE,IAAI,CAACgH,KAAI,CAAC4E,aAAa,CAAC;oBAC/CQ,OAAO,CAACC,GAAG,CAAC,MAAM,EAAExH,gBAAgB,CAAC7I,KAAK,CAAC;kBAC7C;kBACA,IACEgL,KAAI,CAACuH,sBAAsB,IAC3B,CAAC,MAAM,EAAE,mBAAmB,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,CAAC,CAACC,QAAQ,CAACzK,KAAK,CAACgC,KAAK,CAAC5I,IAAI,CAAC,EAC1G;oBACA6H,eAAe,CAAChJ,KAAK,GAAA2P,aAAA,CAAAA,aAAA,KAChB3E,KAAI;sBACPyH,OAAO,EAAE/H,IAAI,CAACpC,eAAe,CAACoK,iBAAiB,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK;sBACvEZ,QAAQ,EAAE,CAAA1C,aAAA,GAAApE,KAAI,CAACuC,MAAM,cAAA6B,aAAA,gBAAAA,aAAA,GAAXA,aAAA,CAAa2C,MAAM,CAAC,UAACtS,CAAC;wBAAA,OAAK,CAACA,CAAC,CAACuS,YAAY;sBAAA,EAAC,cAAA5C,aAAA,eAA3CA,aAAA,CAA6C/K,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK;sBACrF4N,SAAS,EAAE,CAAA5C,cAAA,GAAArE,KAAI,CAACkH,OAAO,cAAA7C,cAAA,gBAAAA,cAAA,GAAZA,cAAA,CAAc0C,MAAM,CAAC,UAAC/P,CAAC;wBAAA,OAAK,CAACA,CAAC,CAACgQ,YAAY;sBAAA,EAAC,cAAA3C,cAAA,eAA5CA,cAAA,CAA8ChL,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI;oBAAK,EACxF;kBACH;gBACF;cACF;YAAC;YAAA;cAAA,OAAAkL,SAAA,CAAAjK,IAAA;UAAA;QAAA,GAAAoI,QAAA;MAAA,CACF;MAAA,gBA9GKpD,iBAAiBA,CAAA;QAAA,OAAAmD,KAAA,CAAAxH,KAAA,OAAAD,SAAA;MAAA;IAAA,GA8GtB;IACD,IAAM2M,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B,IAAIC,IAAI,CAACC,SAAS,CAAC1K,OAAO,CAACnI,KAAK,CAAC,KAAK,IAAI,EACxC,OAAO6G,SAAS,CAAC;QAAE1F,IAAI,EAAE,SAAS;QAAE2R,OAAO,EAAE;MAAoB,CAAC,CAAC;MACrEC,cAAc,CAAC;QAAEC,GAAG,EAAE,CAACjL,KAAK,CAACgC,KAAK,CAACK,EAAE;MAAE,CAAC,CAAC;IAC3C,CAAC;IACD,IAAM6I,kBAAkB;MAAA,IAAAC,KAAA,GAAAnN,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAyO,SAAA;QAAA,OAAA7T,mBAAA,GAAAuB,IAAA,UAAAuS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlO,IAAA,GAAAkO,SAAA,CAAA7P,IAAA;YAAA;cAAA,MACrBoP,IAAI,CAACC,SAAS,CAAC1K,OAAO,CAACnI,KAAK,CAAC,KAAK,IAAI;gBAAAqT,SAAA,CAAA7P,IAAA;gBAAA;cAAA;cAAA,OAAA6P,SAAA,CAAAjQ,MAAA,WACjCyD,SAAS,CAAC;gBAAE1F,IAAI,EAAE,SAAS;gBAAE2R,OAAO,EAAE;cAAoB,CAAC,CAAC;YAAA;cACrE7K,WAAW,CAACjI,KAAK,GAAG;gBAAEgT,GAAG,EAAE,CAACjL,KAAK,CAACgC,KAAK,CAACK,EAAE;cAAE,CAAC;cAC7ClC,kBAAkB,CAAClI,KAAK,GAAG,IAAI;YAAA;YAAA;cAAA,OAAAqT,SAAA,CAAA/N,IAAA;UAAA;QAAA,GAAA6N,QAAA;MAAA,CAChC;MAAA,gBALKF,kBAAkBA,CAAA;QAAA,OAAAC,KAAA,CAAAjN,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKvB;IACD,IAAM+M,cAAc;MAAA,IAAAO,KAAA,GAAAvN,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6O,SAAOC,MAAM;QAAA,IAAAC,sBAAA,EAAA/I,IAAA,EAAAgJ,QAAA,EAAA5F,KAAA;QAAA,OAAAxO,mBAAA,GAAAuB,IAAA,UAAA8S,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzO,IAAA,GAAAyO,SAAA,CAAApQ,IAAA;YAAA;cAAAoQ,SAAA,CAAApQ,IAAA;cAAA,OACX4C,GAAG,CAAC2M,cAAc,CAACS,MAAM,CAAC;YAAA;cAAAC,sBAAA,GAAAG,SAAA,CAAA3Q,IAAA;cAAzCyH,IAAI,GAAA+I,sBAAA,CAAJ/I,IAAI;cACZ,IAAIA,IAAI,CAACrG,MAAM,EAAE;gBACXqP,QAAQ,GAAG,CAAC,CAAC;gBACjB,KAAS5F,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGpD,IAAI,CAACrG,MAAM,EAAEyJ,KAAK,EAAE,EAAE;kBAChD4F,QAAQ,GAAG9M,eAAe,CAAC8D,IAAI,CAACoD,KAAK,CAAC,CAAC;gBACzC;gBACAnH,iBAAiB,CAAC;kBAAEkN,IAAI,EAAE,iBAAiB;kBAAEpP,IAAI,EAAEiP,QAAQ,CAACI,OAAO;kBAAEC,GAAG,EAAE,SAAS;kBAAErJ,IAAI,EAAEgJ;gBAAS,CAAC,CAAC;cACxG;YAAC;YAAA;cAAA,OAAAE,SAAA,CAAAtO,IAAA;UAAA;QAAA,GAAAiO,QAAA;MAAA,CACF;MAAA,gBATKR,cAAcA,CAAAiB,EAAA;QAAA,OAAAV,KAAA,CAAArN,KAAA,OAAAD,SAAA;MAAA;IAAA,GASnB;IACD,IAAMiO,QAAQ,GAAG,SAAXA,QAAQA,CAAI9S,IAAI,EAAK;MACzBwI,YAAY,CAAC3J,KAAK,GAAG,KAAK;MAC1BkI,kBAAkB,CAAClI,KAAK,GAAG,KAAK;MAChC,IAAImB,IAAI,EAAE;QACRuF,YAAY,CAACuE,cAAc,CAAC;UAAEiJ,cAAc,EAAE;YAAEC,MAAM,EAAEpM,KAAK,CAACgC,KAAK,CAACqK,UAAU;YAAEC,OAAO,EAAEtM,KAAK,CAACgC,KAAK,CAACuK;UAAQ;QAAE,CAAC,CAAC;MACnH;IACF,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5BlK,cAAc,CAAC,CAAC;MAChBC,iBAAiB,CAAC,CAAC;IACrB,CAAC;IACD,IAAMkK,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B9N,YAAY,CAACuE,cAAc,CAAC;QAAEiJ,cAAc,EAAE;UAAEC,MAAM,EAAEpM,KAAK,CAACgC,KAAK,CAACqK,UAAU;UAAEC,OAAO,EAAEtM,KAAK,CAACgC,KAAK,CAACuK;QAAQ;MAAE,CAAC,CAAC;IACnH,CAAC;IAED,IAAMxE,wBAAwB;MAAA,IAAA2E,KAAA,GAAA1O,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgQ,SAAA;QAAA,IAAAC,qBAAA,EAAAjK,IAAA;QAAA,OAAApL,mBAAA,GAAAuB,IAAA,UAAA+T,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1P,IAAA,GAAA0P,SAAA,CAAArR,IAAA;YAAA;cAAAqR,SAAA,CAAArR,IAAA;cAAA,OACR4C,GAAG,CAAC0J,wBAAwB,CAAC;gBAAE/F,KAAK,EAAE;kBAAEyF,YAAY,EAAEzH,KAAK,CAACgC,KAAK,CAACK;gBAAG;cAAE,CAAC,CAAC;YAAA;cAAAuK,qBAAA,GAAAE,SAAA,CAAA5R,IAAA;cAAxFyH,IAAI,GAAAiK,qBAAA,CAAJjK,IAAI;cACZtB,SAAS,CAACpJ,KAAK,GAAG8J,UAAU,CAAC9J,KAAK,GAAG,CAAA0K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqH,MAAM,CAAC,UAAC/P,CAAC;gBAAA,OAAKA,CAAC,CAAC8S,MAAM;cAAA,EAAC,KAAI,EAAE,GAAGpK,IAAI;YAAA;YAAA;cAAA,OAAAmK,SAAA,CAAAvP,IAAA;UAAA;QAAA,GAAAoP,QAAA;MAAA,CAChF;MAAA,gBAHK5E,wBAAwBA,CAAA;QAAA,OAAA2E,KAAA,CAAAxO,KAAA,OAAAD,SAAA;MAAA;IAAA,GAG7B;IACD,IAAM+O,WAAW,GAAG,SAAdA,WAAWA,CAAI/J,IAAI,EAAK;MAC5B3B,OAAO,CAACrJ,KAAK,GAAGgL,IAAI,CAACZ,EAAE;MACvBd,eAAe,CAACtJ,KAAK,GAAG,IAAI;IAC9B,CAAC;IACD,IAAMgV,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIhK,IAAI,EAAK;MACpCxB,eAAe,CAACxJ,KAAK,GAAGgL,IAAI,CAACZ,EAAE;MAC/Bb,MAAM,CAACvJ,KAAK,GAAG,IAAI;IACrB,CAAC;IACD,IAAMiV,cAAc,GAAG,SAAjBA,cAAcA,CAAIjK,IAAI,EAAK;MAC/BtB,aAAa,CAAC1J,KAAK,GAAGgL,IAAI,CAACZ,EAAE;MAC7BT,YAAY,CAAC3J,KAAK,GAAG,IAAI;IAC3B,CAAC;IACD,IAAMkV,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3BpO,YAAY,CAACqO,OAAO,CAAC,kBAAkB,EAAE,IAAI,EAAE;QAC7CC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBlU,IAAI,EAAE;MACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;QACV4S,kBAAkB,CAAC,YAAY,CAAC;MAClC,CAAC,CAAC,CACD3P,KAAK,CAAC,YAAM;QACXkB,SAAS,CAAC;UAAE1F,IAAI,EAAE,MAAM;UAAE2R,OAAO,EAAE;QAAU,CAAC,CAAC;MACjD,CAAC,CAAC;IACN,CAAC;IACD,IAAMyC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBzO,YAAY,CAACqO,OAAO,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAC/CC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBlU,IAAI,EAAE;MACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;QACV4S,kBAAkB,CAAC,oBAAoB,CAAC;MAC1C,CAAC,CAAC,CACD3P,KAAK,CAAC,YAAM;QACXkB,SAAS,CAAC;UAAE1F,IAAI,EAAE,MAAM;UAAE2R,OAAO,EAAE;QAAU,CAAC,CAAC;MACjD,CAAC,CAAC;IACN,CAAC;IACD,IAAMwC,kBAAkB;MAAA,IAAAE,KAAA,GAAAzP,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+Q,SAAOC,UAAU;QAAA,IAAAC,sBAAA,EAAA9B,IAAA;QAAA,OAAAvU,mBAAA,GAAAuB,IAAA,UAAA+U,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1Q,IAAA,GAAA0Q,SAAA,CAAArS,IAAA;YAAA;cAAAqS,SAAA,CAAArS,IAAA;cAAA,OACnB4C,GAAG,CAACkP,kBAAkB,CAAC;gBAAE9F,YAAY,EAAEzH,KAAK,CAACgC,KAAK,CAACK,EAAE;gBAAEsL,UAAU,EAAEA;cAAW,CAAC,CAAC;YAAA;cAAAC,sBAAA,GAAAE,SAAA,CAAA5S,IAAA;cAA/F4Q,IAAI,GAAA8B,sBAAA,CAAJ9B,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBhN,SAAS,CAAC;kBAAE1F,IAAI,EAAE,SAAS;kBAAE2R,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/C0B,aAAa,CAAC,CAAC;cACjB;YAAC;YAAA;cAAA,OAAAqB,SAAA,CAAAvQ,IAAA;UAAA;QAAA,GAAAmQ,QAAA;MAAA,CACF;MAAA,gBANKH,kBAAkBA,CAAAQ,GAAA;QAAA,OAAAN,KAAA,CAAAvP,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}