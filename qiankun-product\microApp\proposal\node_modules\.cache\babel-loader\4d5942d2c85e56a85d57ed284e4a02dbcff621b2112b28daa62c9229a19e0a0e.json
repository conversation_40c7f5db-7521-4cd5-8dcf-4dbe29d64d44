{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport SuggestNumberingSubmit from './component/SuggestNumberingSubmit';\nvar __default__ = {\n  name: 'SuggestNumbering'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [{\n      id: 'new',\n      name: '新增',\n      type: 'primary',\n      has: 'new'\n    }\n    // { id: 'del', name: '删除', type: '', has: 'del' }\n    ];\n    var tableButtonList = [{\n      id: 'edit',\n      name: '编辑',\n      width: 100,\n      has: 'edit'\n    }];\n    var id = ref('');\n    var show = ref(false);\n    var _GlobalTable = GlobalTable({\n        tableApi: 'suggestionTermYearList',\n        delApi: 'suggestionTermYearDel',\n        valId: 'termYearId'\n      }),\n      keyword = _GlobalTable.keyword,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      handleDel = _GlobalTable.handleDel,\n      tableRefReset = _GlobalTable.tableRefReset;\n    onActivated(function () {\n      handleQuery();\n    });\n    var handleButton = function handleButton(id) {\n      switch (id) {\n        case 'new':\n          handleNew();\n          break;\n        case 'del':\n          handleDel('届次编号');\n          break;\n        default:\n          break;\n      }\n    };\n    var handleCommand = function handleCommand(row, isType) {\n      switch (isType) {\n        case 'edit':\n          handleEdit(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleNew = function handleNew() {\n      id.value = '';\n      show.value = true;\n    };\n    var handleEdit = function handleEdit(item) {\n      id.value = item.termYearId;\n      show.value = true;\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      show.value = false;\n    };\n    var __returned__ = {\n      buttonList,\n      tableButtonList,\n      id,\n      show,\n      keyword,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      handleTableSelect,\n      handleDel,\n      tableRefReset,\n      handleButton,\n      handleCommand,\n      handleReset,\n      handleNew,\n      handleEdit,\n      callback,\n      ref,\n      onActivated,\n      get format() {\n        return format;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get SuggestNumberingSubmit() {\n        return SuggestNumberingSubmit;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "format", "GlobalTable", "SuggestNumberingSubmit", "__default__", "name", "buttonList", "id", "type", "has", "tableButtonList", "width", "show", "_GlobalTable", "tableApi", "del<PERSON><PERSON>", "valId", "keyword", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "handleTableSelect", "handleDel", "tableRefReset", "handleButton", "handleNew", "handleCommand", "row", "isType", "handleEdit", "handleReset", "value", "item", "termYearId", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestConfig/SuggestNumbering/SuggestNumbering.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestNumbering\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"termYearId\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column label=\"届次\"\r\n                         min-width=\"160\"\r\n                         prop=\"companyUser\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">{{ scope.row.termYear?.circlesType?.label }}{{ scope.row.termYear?.boutType?.label }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"大会编号前缀\"\r\n                         min-width=\"160\"\r\n                         prop=\"meetingNumberPrefix\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"大会起始编号\"\r\n                         min-width=\"160\"\r\n                         prop=\"meetingStartNumber\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"平时编号前缀\"\r\n                         min-width=\"160\"\r\n                         prop=\"usualNumberPrefix\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"平时起始编号\"\r\n                         min-width=\"160\"\r\n                         prop=\"usualStartNumber\"\r\n                         show-overflow-tooltip />\r\n        <el-table-column label=\"大会时间\"\r\n                         min-width=\"360\"\r\n                         prop=\"companyUser\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            {{ format(scope.row.meetingStartDate) }} - {{ format(scope.row.meetingEndDate) }}\r\n          </template>\r\n        </el-table-column>\r\n        <xyl-global-table-button :data=\"tableButtonList\"\r\n                                 @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      :name=\"id ? '编辑' : '新增'\">\r\n      <SuggestNumberingSubmit :id=\"id\"\r\n                              @callback=\"callback\"></SuggestNumberingSubmit>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestNumbering' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SuggestNumberingSubmit from './component/SuggestNumberingSubmit'\r\nconst buttonList = [\r\n  { id: 'new', name: '新增', type: 'primary', has: 'new' }\r\n  // { id: 'del', name: '删除', type: '', has: 'del' }\r\n]\r\nconst tableButtonList = [{ id: 'edit', name: '编辑', width: 100, has: 'edit' }]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset\r\n} = GlobalTable({ tableApi: 'suggestionTermYearList', delApi: 'suggestionTermYearDel', valId: 'termYearId' })\r\n\r\nonActivated(() => { handleQuery() })\r\n\r\nconst handleButton = (id) => {\r\n  switch (id) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'del':\r\n      handleDel('届次编号')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleNew = () => {\r\n  id.value = ''\r\n  show.value = true\r\n}\r\nconst handleEdit = (item) => {\r\n  id.value = item.termYearId\r\n  show.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestNumbering {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA+EA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,sBAAsB,MAAM,oCAAoC;AANvE,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAmB,CAAC;;;;;IAO3C,IAAMC,UAAU,GAAG,CACjB;MAAEC,EAAE,EAAE,KAAK;MAAEF,IAAI,EAAE,IAAI;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAM;IACrD;IAAA,CACD;IACD,IAAMC,eAAe,GAAG,CAAC;MAAEH,EAAE,EAAE,MAAM;MAAEF,IAAI,EAAE,IAAI;MAAEM,KAAK,EAAE,GAAG;MAAEF,GAAG,EAAE;IAAO,CAAC,CAAC;IAC7E,IAAMF,EAAE,GAAGR,GAAG,CAAC,EAAE,CAAC;IAClB,IAAMa,IAAI,GAAGb,GAAG,CAAC,KAAK,CAAC;IACvB,IAAAc,YAAA,GAYIX,WAAW,CAAC;QAAEY,QAAQ,EAAE,wBAAwB;QAAEC,MAAM,EAAE,uBAAuB;QAAEC,KAAK,EAAE;MAAa,CAAC,CAAC;MAX3GC,OAAO,GAAAJ,YAAA,CAAPI,OAAO;MACPC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MACRC,MAAM,GAAAN,YAAA,CAANM,MAAM;MACNC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,QAAQ,GAAAR,YAAA,CAARQ,QAAQ;MACRC,SAAS,GAAAT,YAAA,CAATS,SAAS;MACTC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,WAAW,GAAAX,YAAA,CAAXW,WAAW;MACXC,iBAAiB,GAAAZ,YAAA,CAAjBY,iBAAiB;MACjBC,SAAS,GAAAb,YAAA,CAATa,SAAS;MACTC,aAAa,GAAAd,YAAA,CAAbc,aAAa;IAGf3B,WAAW,CAAC,YAAM;MAAEwB,WAAW,CAAC,CAAC;IAAC,CAAC,CAAC;IAEpC,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAIrB,EAAE,EAAK;MAC3B,QAAQA,EAAE;QACR,KAAK,KAAK;UACRsB,SAAS,CAAC,CAAC;UACX;QACF,KAAK,KAAK;UACRH,SAAS,CAAC,MAAM,CAAC;UACjB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMI,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAEC,MAAM,EAAK;MACrC,QAAQA,MAAM;QACZ,KAAK,MAAM;UACTC,UAAU,CAACF,GAAG,CAAC;UACf;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBjB,OAAO,CAACkB,KAAK,GAAG,EAAE;MAClBX,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMK,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtBtB,EAAE,CAAC4B,KAAK,GAAG,EAAE;MACbvB,IAAI,CAACuB,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMF,UAAU,GAAG,SAAbA,UAAUA,CAAIG,IAAI,EAAK;MAC3B7B,EAAE,CAAC4B,KAAK,GAAGC,IAAI,CAACC,UAAU;MAC1BzB,IAAI,CAACuB,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMG,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBX,aAAa,CAAC,CAAC;MACfH,WAAW,CAAC,CAAC;MACbZ,IAAI,CAACuB,KAAK,GAAG,KAAK;IACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}