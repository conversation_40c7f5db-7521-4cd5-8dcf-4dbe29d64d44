<template>
  <div class="suggestPop" :class="{ show: isVisible || show }" v-loading="loading">
    <div class="suggestPopHead" :class="{ showHead: show }">
      <el-icon v-if="show">
        <RefreshRight @click="RefreshRightclick" />
      </el-icon>
      <el-icon v-if="show" @click="closePop">
        <Close />
      </el-icon>
      <el-icon @click="closePop" v-else>
        <More />
      </el-icon>
    </div>
    <div class="content" v-if="delayedShow">
      <div class="suggestPopContentHeader">{{ user.userName }}，{{ getgreetings() }}</div>
      <div class="suggestPopContentChooseRole" v-if="roles.length > 1">
        <div>选择您的身份以查看更多待办</div>
        <el-select v-model="role" size="small" style="width: 120px" @change="getCompositeData">
          <el-option v-for="item in rulesoptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="suggestPopContentBody">
        <div>{{ tableData.termYear }}</div>
        <template v-if="role == 'npc_contact_committee'">
          <div>
            <span @click="goSuggestList('AllSuggest', 'meetList')">{{ tableData.meetList?.amount || 0 }}</span>
            件大会建议，
            <span @click="goSuggestList('AllSuggest', 'usualList')">{{ tableData.usualList?.amount || 0 }}</span>
            件闭会建议，
            <span
              @click="goSuggestList('SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议', 'importantList')">
              {{ tableData.importantList?.amount || 0 }}
            </span>
            件重点督办建议
          </div>
          <div class="mb20">
            <span
              @click="goSuggestList('SuggestReview?tableId=id_sgsn_suggestion_prepareVerify&moduleName=待审查建议', 'auditList')">
              {{ tableData.auditList?.amount || 0 }}
            </span>
            件待审查，
            <template v-if="suggestionEnablePreAssign">
              <span @click="
                goSuggestList('SuggestAdvanceAssign?tableId=id_sgsn_suggestion_preAssignSuggestion&moduleName=预交办', 'preAssignList')
                ">
                {{ tableData.preAssignList?.amount || 0 }}
              </span>
              件预交办，
            </template>
            <span @click="
              goSuggestList('SuggestAssign?tableId=id_sgsn_suggestion_prepareSubmitHandle&moduleName=人大交办中', 'prepareSubmitHandleList')
              ">
              {{ tableData.prepareSubmitHandleList?.amount || 0 }}
            </span>
            件人大交办中
          </div>
          <div class="hasColorBox">
            <span @click="goSuggestList('SuggestTransact', 'handleList')">{{ tableData.handleList?.amount || 0 }}</span>
            件办理中，其中
            <span class="red"></span>
            <span class="nocolorSpan">{{ tableData.redAnswerDate?.amount || 0 }}</span>
            件，
            <span class="yellow"></span>
            <span class="nocolorSpan">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>
            件，
            <span class="green"></span>
            <span class="nocolorSpan">{{ tableData.greenAnswerDate?.amount || 0 }}</span>
            件
          </div>
          <div class="mb20">
            <span @click="goSuggestList('SuggestApplyForAdjust', 'adjustList')">{{ tableData.adjustList?.amount || 0
              }}</span>
            件调整申请待审核，
            <span @click="goSuggestList('SuggestApplyForPostpone', 'delayList')">{{ tableData.delayList?.amount || 0
              }}</span>
            件延期申请待审核
          </div>
          <div>
            <span @click="goSuggestList('SuggestReply', 'answerList')">{{ tableData.answerList?.amount || 0 }}</span>
            件已答复
          </div>
          <div>
            <span @click="goSuggestList('AllSuggest', 'satisfactionList')">{{ tableData.satisfactionList?.amount || 0
              }}</span>
            件已答复待代表满意度测评
          </div>
          <div>
            <span @click="goSuggestList('SuggestConclude', 'finishList')">{{ tableData.finishList?.amount || 0 }}</span>
            件已办结
          </div>
        </template>
        <template v-if="role === 'proposal_committee'">
          <div>
            <span @click="goSuggestList('AllSuggest', 'meetList')">{{ tableData.meetList?.amount || 0 }}</span>
            件大会提案，
            <span @click="goSuggestList('AllSuggest', 'usualList')">{{ tableData.usualList?.amount || 0 }}</span>
            件闭会提案，
            <span
              @click="goSuggestList('SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案', 'importantList')">
              {{ tableData.importantList?.amount || 0 }}
            </span>
            件重点督办提案
          </div>
          <div class="mb20">
            <span
              @click="goSuggestList('SuggestReview?tableId=id_prop_proposal_prepareVerify&moduleName=待审查提案', 'auditList')">
              {{ tableData.auditList?.amount || 0 }}
            </span>
            件待审查，
            <template v-if="suggestionEnablePreAssign">
              <span @click="
                goSuggestList('SuggestAdvanceAssign?tableId=id_prop_proposal_preAssignPropoasl&moduleName=预交办', 'preAssignList')
                ">
                {{ tableData.preAssignList?.amount || 0 }}
              </span>
              件预交办，
            </template>
            <span @click="
              goSuggestList('SuggestAssign?tableId=id_prop_proposal_prepareSubmitHandle&moduleName=政协交办中', 'prepareSubmitHandleList')
              ">
              {{ tableData.prepareSubmitHandleList?.amount || 0 }}
            </span>
            件政协交办中
          </div>
          <div class="hasColorBox">
            <span @click="goSuggestList('SuggestTransact', 'handleList')">{{ tableData.handleList?.amount || 0 }}</span>
            件办理中，其中
            <span class="red"></span>
            <span class="nocolorSpan">{{ tableData.redAnswerDate?.amount || 0 }}</span>
            件，
            <span class="yellow"></span>
            <span class="nocolorSpan">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>
            件，
            <span class="green"></span>
            <span class="nocolorSpan">{{ tableData.greenAnswerDate?.amount || 0 }}</span>
            件
          </div>
          <div class="mb20">
            <span @click="goSuggestList('SuggestApplyForAdjust', 'adjustList')">{{ tableData.adjustList?.amount || 0
              }}</span>
            件调整申请待审核，
            <span @click="goSuggestList('SuggestApplyForPostpone', 'delayList')">{{ tableData.delayList?.amount || 0
              }}</span>
            件延期申请待审核
          </div>
          <div>
            <span @click="goSuggestList('SuggestReply', 'answerList')">{{ tableData.answerList?.amount || 0 }}</span>
            件已答复
          </div>
          <div>
            <span @click="goSuggestList('SuggestSatisfaction', 'satisfactionList')">{{
              tableData.satisfactionList?.amount || 0
              }}</span>
            件已答复待委员满意度测评
          </div>
          <div>
            <span @click="goSuggestList('SuggestConclude', 'finishList')">{{ tableData.finishList?.amount || 0 }}</span>
            件已办结
          </div>
        </template>
        <template v-if="role == 'suggestion_office_user'">
          <div>
            共
            <span @click="goSuggestList('UnitSuggestTransact', 'handleList')">{{ tableData.handleList?.amount || 0
              }}</span>
            件办理中，
            <span @click="
              goSuggestList(
                systemPlatform == 'CPPCC'
                  ? 'SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案'
                  : 'SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议',
                'importantList'
              )
              ">
              {{ tableData.importantList?.amount || 0 }}
            </span>
            件重点督办{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}
          </div>
          <div class="hasColorBox">
            其中
            <span class="red"></span>
            <span class="nocolorSpan">{{ tableData.redAnswerDate?.amount || 0 }}</span>
            件，
            <span class="yellow"></span>
            <span class="nocolorSpan">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>
            件，
            <span class="green"></span>
            <span class="nocolorSpan">{{ tableData.greenAnswerDate?.amount || 0 }}</span>
            件
          </div>
          <div>
            <span class="nocolorSpan">{{ tableData.adjustList?.amount || 0 }}</span>
            件调整申请待审核，
          </div>
          <div class="mb20">
            <span class="nocolorSpan">{{ tableData.delayList?.amount || 0 }}</span>
            件申请延期待审核，
          </div>
          <div>
            <span @click="goSuggestList('UnitSuggestReply', 'answerList')">{{ tableData.answerList?.amount || 0
              }}</span>
            件已答复
          </div>
          <!-- <div>
            <span @click="goSuggestList('satisfactionList')">{{ tableData.satisfactionList?.amount || 0 }}</span>
            件已答复待代表满意度测评
          </div> -->
          <div>
            <span @click="goSuggestList('UnitSuggestConclude', 'finishList')">{{ tableData.finishList?.amount || 0
              }}</span>
            件已办结
          </div>
        </template>
        <template v-if="role == 'delegation_manager'">
          <div>
            本代表团
            <span @click="goSuggestList('AllSuggest', 'memberList')">{{ tableData.memberList?.amount || 0 }}</span>
            件代表建议，
            <span @click="goSuggestList('AllSuggest', 'teamList')">{{ tableData.teamList?.amount || 0 }}</span>
            件全团建议
          </div>
          <div>
            <span @click="
              goSuggestList(
                'SuggestReview?tableId=id_sgsn_suggestion_base_delegation_verify&moduleName=代表团审查&nextNode=prepareVerify',
                'delegationAuditList'
              )
              ">
              {{ tableData.delegationAuditList?.amount || 0 }}
            </span>
            件待代表团审查建议
          </div>
        </template>
        <template v-if="role == 'npc_member' || role == 'cppcc_member'">
          <div>
            您已提交
            <span @click="goSuggestList('MyLedSuggest', 'normalList')">
              {{ tableData.normalList?.amount || 0 }}
            </span>
            件{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}，
            <span class="nocolorSpan">{{ tableData.importantList?.amount || 0 }}</span>
            件形成重点督办{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}
          </div>
          <div>
            <span @click="goSuggestList('MyJointSuggest', 'needJoinList')">
              {{ tableData.needJoinList?.amount || 0 }}
            </span>
            件需要确认是否{{ systemPlatform == 'CPPCC' ? '联名' : '附议' }}，
            <span @click="goSuggestList('MyLedSuggest', 'backList')">
              {{ tableData.backList?.amount || 0 }}
            </span>
            件被退回，
            <span @click="
              goSuggestList(
                systemPlatform == 'CPPCC'
                  ? 'SuggestDraftBox?nextNode=prepareVerify'
                  : 'SuggestDraftBox?nextNode=prepareVerify',
                'draftsList'
              )
              ">
              {{ tableData.draftsList?.amount || 0 }}
            </span>
            件在草稿箱
          </div>
          <div>
            <span @click="goSuggestList('MyLedSuggest', 'satisfactionList')">
              {{ tableData.satisfactionList?.amount || 0 }}
            </span>
            件待满意度测评
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'suggestPop'
}
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, computed, inject } from 'vue'
import { openConfig } from 'common/js/system_var.js'
const systemPlatform = computed(() => openConfig.value?.systemPlatform || '')
const props = defineProps({
  isVisible: { type: Boolean, default: false },
  routePth: { type: String, default: '' }
})

const openPage = inject('openPage')
const getgreetings = () => {
  const hour = new Date().getHours()
  if (hour < 12) {
    return '早上好'
  } else if (hour < 18) {
    return '下午好'
  } else {
    return '晚上好'
  }
}
// npc_contact_committee  npc_member  delegation_manager  suggestion_office_user

const show = ref(false)
const delayedShow = ref(false)
const closePop = () => {
  if (delayedShow.value) {
    delayedShow.value = !delayedShow.value
  } else {
    setTimeout(() => {
      delayedShow.value = !delayedShow.value
    }, 300)
  }
  show.value = !show.value
}

const user = ref({})
const canChooseRoles = ref(
  systemPlatform.value == 'CPPCC'
    ? ['proposal_committee', 'suggestion_office_user', 'cppcc_member']
    : ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member']
)

const role = ref('')
const rulesoptions = ref([])
const roles = ref([])
const suggestionEnablePreAssign = ref(false) // 是否开启预交办
const getLoginHintConfig = async () => {
  const { data } = await api.globalReadOpenConfig({
    codes: ['suggestion_enable_pre_assign', 'proposal_enable_pre_assign']
  })
  if (data.suggestion_enable_pre_assign && systemPlatform.value != 'CPPCC') {
    suggestionEnablePreAssign.value = data.suggestion_enable_pre_assign == 'true'
  }
  if (data.proposal_enable_pre_assign && systemPlatform.value == 'CPPCC') {
    suggestionEnablePreAssign.value = data.proposal_enable_pre_assign == 'true'
  }
}
onMounted(() => {
  getLoginHintConfig()
  user.value = JSON.parse(sessionStorage.getItem('user'))
  roles.value = user.value.specialRoleKeys.filter((item) => canChooseRoles.value.includes(item))
  if (roles.value.includes('npc_contact_committee'))
    rulesoptions.value.push({ value: 'npc_contact_committee', label: '联工委', param: 'remind_admin' })
  if (roles.value.includes('proposal_committee'))
    rulesoptions.value.push({ value: 'proposal_committee', label: '提案委', param: 'remind_admin' })
  if (roles.value.includes('suggestion_office_user'))
    rulesoptions.value.push({ value: 'suggestion_office_user', label: '办理单位', param: 'remind_office' })
  if (roles.value.includes('delegation_manager'))
    rulesoptions.value.push({ value: 'delegation_manager', label: '代表团管理员', param: 'remind_delegation' })
  if (roles.value.includes('npc_member'))
    rulesoptions.value.push({ value: 'npc_member', label: '人大代表', param: 'remind_npc_member' })
  if (roles.value.includes('cppcc_member'))
    rulesoptions.value.push({ value: 'cppcc_member', label: '政协委员', param: 'remind_member' })
  role.value = rulesoptions.value[0].value
  getCompositeData()
  setTimeout(() => {
    show.value = true
    setTimeout(() => {
      delayedShow.value = true
    }, 100)
  }, 300)
})
const tableData = ref({})
const loading = ref(true)
const getCompositeData = async () => {
  const url = systemPlatform.value === 'CPPCC' ? '/proposalStatistics/composite' : '/suggestionStatistics/composite'
  const res = await api.globalJson(url, {
    countView: rulesoptions.value.filter((v) => v.value === role.value)[0].param
  })
  tableData.value = res.data.tableData.length ? res.data.tableData[0] : {}
  loading.value = false
}
const RefreshRightclick = () => {
  loading.value = true
  getCompositeData()
}
const goSuggestList = (type, key) => {
  const count = tableData.value[key]?.amount || 0;
  if (count === 0) {
    ElMessage({
      type: 'info',
      message: `暂无${key === 'draftsList' ? '草稿' : '相关'}数据`
    });
    return;
  }
  const suggestIds = tableData.value[key]?.suggestionIds || tableData.value[key]?.proposalIds || []
  console.log(suggestIds)

  if (suggestIds.length) {
    sessionStorage.setItem('suggestIds', JSON.stringify(suggestIds))
  } else {
    sessionStorage.removeItem('suggestIds')
  }
  const url = systemPlatform.value == 'CPPCC' ? 'proposal' : 'suggest'
  // if (type) return
  openPage({ key: 'routePath', value: `/${url}/` + type })
}
</script>
<style lang="scss">
.suggestPop {
  position: absolute;
  right: 16px;
  bottom: 0;
  width: 50px;
  background: #fff;
  z-index: 999;
  transform: translateY(18);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px 8px 0 0;
  height: 18px;

  &.show {
    height: auto;
    width: 500px;
    height: 400px;
    transform: translateY(0);
  }

  .suggestPopHead {
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 16px;
    background: var(--zy-el-color-primary);
    color: #fff;
    font-size: 20px;

    .zy-el-icon {
      margin-left: 10px;
      cursor: pointer;
    }

    &.showHead {
      height: 36px;
    }
  }

  .content {
    height: calc(100% - 36px);
    padding: 16px;
    overflow-y: auto;

    .suggestPopContentHeader {
      border-bottom: 1px solid #e5e5e5;
      padding-bottom: 16px;
      margin-bottom: 10px;
    }

    .suggestPopContentChooseRole {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-size: 14px;
      margin-bottom: 20px;

      .zy-el-select {
        margin-left: 16px;
      }
    }

    .suggestPopContentBody {
      padding-left: 10px;
      line-height: 26px;

      span {
        color: var(--zy-el-color-primary);
        cursor: pointer;
      }

      .nocolorSpan {
        color: var(--zy-el-text-color-primary);
      }

      .hasColorBox {
        display: flex;
        align-items: center;

        .red {
          width: 20px;
          height: 20px;
          background: #f56c6c;
          border-radius: 50%;
          display: inline-block;
        }

        .yellow {
          width: 20px;
          height: 20px;
          background: rgb(246, 185, 47);
          border-radius: 50%;
          display: inline-block;
        }

        .green {
          background: rgb(51, 203, 116);
          width: 20px;
          height: 20px;
          border-radius: 50%;
          display: inline-block;
        }
      }

      .mb20 {
        margin-bottom: 14px;
      }
    }
  }
}
</style>
