{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted, nextTick } from 'vue';\nimport { filterTableData } from '@/assets/js/suggestExportWord';\nimport { Print } from 'common/js/print';\nimport { user } from 'common/js/system_var.js';\nvar __default__ = {\n  name: 'suggestPrint'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    params: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    var props = __props;\n    var emit = __emit;\n    var printData = ref([]);\n    var printRef = ref();\n    var areaName = ref('');\n    var areaLogo = ref('');\n    var handlePrint = function handlePrint() {\n      Print.init(printRef.value);\n    };\n    onMounted(function () {\n      var areaId = user.value.areaId;\n      if (areaId == '370500') {\n        areaName.value = '东营市';\n        areaLogo.value = require('../../assets/img/dongyingshizhang.png');\n      } else if (areaId == '370502') {\n        areaName.value = '东营区';\n        areaLogo.value = require('../../assets/img/daitianjia.png');\n      } else if (areaId == '370503') {\n        areaName.value = '河口区';\n        areaLogo.value = require('../../assets/img/daitianjia.png');\n      } else if (areaId == '370505') {\n        areaName.value = '垦利区';\n        areaLogo.value = require('../../assets/img/daitianjia.png');\n      } else if (areaId == '370522') {\n        areaName.value = '利津县';\n        areaLogo.value = require('../../assets/img/daitianjia.png');\n      } else if (areaId == '370523') {\n        areaName.value = '广饶县';\n        areaLogo.value = require('../../assets/img/guangraoxianzhang.png');\n      } else {\n        areaName.value = '';\n        areaLogo.value = '';\n      }\n      suggestionWord();\n    });\n    // const rowspan = (item) => {\n    //   let rowspannum = 0\n    //   if (item.suggestSurveyTypeName) { rowspannum += 1 }\n    //   if (item.notHandleTimeTypeName) { rowspannum += 1 }\n    //   if (item.suggestOpenTypeName) { rowspannum += 1 }\n    //   if (item.isMakeMineJobName) { rowspannum += 1 }\n    //   if (item.isHopeEnhanceTalkName) { rowspannum += 1 }\n    //   if (item.isNeedPaperAnswerName) { rowspannum += 1 }\n    //   return rowspannum\n    // }\n    var suggestionWord = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$suggestion, data, index;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.suggestionWord(props.params);\n            case 2:\n              _yield$api$suggestion = _context.sent;\n              data = _yield$api$suggestion.data;\n              if (data.length) {\n                printData.value = [];\n                for (index = 0; index < data.length; index++) {\n                  printData.value.push(filterTableData(data[index]));\n                }\n                nextTick(function () {\n                  handlePrint();\n                  emit('callback');\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function suggestionWord() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    __expose({\n      print: handlePrint\n    });\n    var __returned__ = {\n      props,\n      emit,\n      printData,\n      printRef,\n      areaName,\n      areaLogo,\n      handlePrint,\n      suggestionWord,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      nextTick,\n      get filterTableData() {\n        return filterTableData;\n      },\n      get Print() {\n        return Print;\n      },\n      get user() {\n        return user;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "nextTick", "filterTableData", "Print", "user", "__default__", "props", "__props", "emit", "__emit", "printData", "printRef", "areaName", "areaLogo", "handlePrint", "init", "areaId", "require", "<PERSON><PERSON><PERSON>", "_ref2", "_callee", "_yield$api$suggestion", "data", "index", "_callee$", "_context", "params", "__expose", "print"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/components/suggestPrint/suggestPrint.vue"], "sourcesContent": ["<template>\r\n  <div class=\"suggestPrint\" ref=\"printRef\">\r\n    <div class=\"suggestPrintBody\" v-for=\"item in printData\" :key=\"item.id\">\r\n      <!-- <div class=\"suggestPrintType\">\r\n        <div><span>案号：</span>{{ item.serialNumber }}</div>\r\n        <div><span>类别：</span>{{ item.bigThemeName }}</div>\r\n      </div> -->\r\n      <!-- <div class=\"suggestPrintName\"\r\n           @click=\"handlePrint\">{{ item.redHeadTitle }}</div> -->\r\n      <!-- <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">提案者11</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.suggestUserName }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemFlex\">\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">委员证号</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.cardNumberNpc }}</span>\r\n        </div>\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">界别</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.delegationName }}</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">单位职务</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.position }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">通讯地址</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.callAddress }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemFlex suggestPrintItemInfo\">\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">邮政编码</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.postcode }}</span>\r\n        </div>\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">联系电话</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.mobile }}</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"suggestPrintItem suggestPrintItemJoin\">\r\n        <span class=\"suggestPrintItemTitle\">联名人</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.joinUser }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">提案标题</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.titleone }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemTwo\"\r\n           v-for=\"(text, index) in item.titletwoArr\"\r\n           :key=\"index\">{{ text }}</div>\r\n      <div class=\"suggestPrintItemTime\">提出时间：{{ item.submitDate }}</div>\r\n      <div style=\"page-break-after:always\"></div>\r\n      <div class=\"mainModulePageTableName\">其他相关信息</div>\r\n      <table>\r\n        <tbody>\r\n          <tr v-if=\"item.suggestSurveyTypeName\">\r\n            <td :rowspan=\"rowspan(item)\">相关情况</td>\r\n            <td>{{ item.suggestSurveyTypeName }}</td>\r\n            <td>{{ item.suggestSurveyTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.notHandleTimeTypeName\">\r\n            <td :rowspan=\"rowspan(item)\" v-if=\"!item.suggestSurveyTypeName\">相关情况</td>\r\n            <td>{{ item.notHandleTimeTypeName }}</td>\r\n            <td>{{ item.notHandleTimeTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.suggestOpenTypeName\">\r\n            <td :rowspan=\"rowspan(item)\" v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName\">相关情况</td>\r\n            <td>{{ item.suggestOpenTypeName }}</td>\r\n            <td>{{ item.suggestOpenTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isMakeMineJobName\">\r\n            <td :rowspan=\"rowspan(item)\"\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName\">相关情况</td>\r\n            <td>{{ item.isMakeMineJobName }}</td>\r\n            <td>{{ item.isMakeMineJobView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isHopeEnhanceTalkName\">\r\n            <td :rowspan=\"rowspan(item)\"\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName\">\r\n              相关情况</td>\r\n            <td>{{ item.isHopeEnhanceTalkName }}</td>\r\n            <td>{{ item.isHopeEnhanceTalkView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isNeedPaperAnswerName\">\r\n            <td\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName && !item.isHopeEnhanceTalkName\">\r\n              相关情况</td>\r\n            <td>{{ item.isNeedPaperAnswerName }}</td>\r\n            <td>{{ item.isNeedPaperAnswerView }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>希望送交的承办单位（仅供参考）</td>\r\n            <td colspan=\"2\">{{ item.hopeHandleOfficeName }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"suggestPrintContent\"\r\n           v-html=\"item.content\"></div>\r\n      <div style=\"page-break-after:always\"></div>\r\n      <div class=\"mainModulePageTableName\">联名人信息表</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td>姓名</td>\r\n            <td>界别</td>\r\n            <td>委员证号</td>\r\n            <td>单位职务</td>\r\n            <td>联系方式</td>\r\n            <td>通讯地址</td>\r\n          </tr>\r\n          <tr v-for=\"row in item.joinUsers\" :key=\"row.userId\">\r\n            <td>{{ row.userName }}</td>\r\n            <td>{{ row.sector }}</td>\r\n            <td>{{ row.cardNumber }}</td>\r\n            <td>{{ row.position }}</td>\r\n            <td>{{ row.mobile }}</td>\r\n            <td>{{ row.callAddress }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"mainModulePageTableName\">提案联系人</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td colspan=\"2\">姓名</td>\r\n            <td colspan=\"2\">联系电话</td>\r\n            <td colspan=\"3\">通讯地址</td>\r\n          </tr>\r\n          <tr v-for=\"row in item.contacters\" :key=\"row.id\">\r\n            <td colspan=\"2\">{{ row.contacterName }}</td>\r\n            <td colspan=\"2\">{{ row.contacterMobile }}</td>\r\n            <td colspan=\"3\">{{ row.contacterAddress }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"mainModulePageTableName\">审查和办理信息表</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td>本提案目前状态</td>\r\n            <td colspan=\"2\">{{ item.currentStatus }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>审查情况</td>\r\n            <td colspan=\"2\">{{ item.verifyStatus }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.mainHandleOffice\">\r\n            <td rowspan=\"2\">办理单位</td>\r\n            <td>主办</td>\r\n            <td>{{ item.mainHandleOffice }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.mainHandleOffice\">\r\n            <td>协办</td>\r\n            <td>{{ item.assistHandleOffice }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.publishHandleOffice\">\r\n            <td>办理单位</td>\r\n            <td>分办</td>\r\n            <td>{{ item.publishHandleOffice }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div style=\"page-break-after:always\"></div> -->\r\n\r\n      <div class=\"suggestPrintName\" @click=\"handlePrint\">中国人民政治协商会议{{ areaName }}委员会{{ item.circlesType.name\r\n      }}{{ item.boutType.name }}会议</div>\r\n      <div class=\"suggestPrintText\">提&nbsp;&nbsp;案</div>\r\n      <div class=\"suggestPrintTextNumber\">提案编号：第{{ item.serialNumber }}号</div>\r\n      <div class=\"suggestPrintContainer\">\r\n        <div class=\"suggestPrintContainerTitle\">\r\n          <div class=\"suggestPrintContainerTitleLabel\">提案题目</div>\r\n          <div class=\"suggestPrintContainerTitleContent\">{{ item.title }}</div>\r\n        </div>\r\n        <div class=\"suggestPrintContainerContent\">\r\n          <div class=\"suggestPrintContainerContentLabel\">交办意见</div>\r\n          <div class=\"suggestPrintContainerContentContent\">\r\n            <div class=\"suggestPrintContainerContentContentItem\">主办单位：{{ item.mainHandleOffice\r\n            }}{{ item.publishHandleOffice }}</div>\r\n            <div class=\"suggestPrintContainerContentContentItem\">协办单位：{{ item.assistHandleOffice }}</div>\r\n            <div style=\"height: 80px;\"></div>\r\n            <div class=\"suggestPrintContainerContentContentRight\">\r\n              {{ areaName }}政协提案委员会\r\n            </div>\r\n            <img :src=\"areaLogo\" alt=\"Stamp\" class=\"suggestPrintContainerContentContentStamp\">\r\n            <div class=\"suggestPrintContainerContentContentTime\">年 月 日</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposerBasicInformationTitle\">提案者基本情况</div>\r\n      <div class=\"proposerBasicInformationTable\">\r\n        <!-- 第一行 -->\r\n        <div class=\"cell cell-title\">第一提案者</div>\r\n        <div class=\"cell\">{{ item.suggestUserName }}</div>\r\n        <div class=\"cell cell-title\">提案者类型</div>\r\n        <div class=\"cell\">{{ item.teamOfficeTheme }}</div>\r\n\r\n        <!-- 第二行 -->\r\n        <div class=\"cell cell-title\">工作单位及职务</div>\r\n        <div class=\"cell col-span-3\">{{ item.position }}</div>\r\n\r\n        <!-- 第三行 -->\r\n        <div class=\"cell cell-title\">通讯地址</div>\r\n        <div class=\"cell col-span-3\">{{ item.callAddress }}</div>\r\n\r\n        <!-- 第四行 -->\r\n        <div class=\"cell cell-title\">办公电话</div>\r\n        <div class=\"cell\">{{ item.officePhone }}</div>\r\n        <div class=\"cell cell-title\">手机</div>\r\n        <div class=\"cell\">{{ item.mobile }}</div>\r\n\r\n        <!-- 第五行 -->\r\n        <div class=\"cell cell-title\">界别</div>\r\n        <div class=\"cell\">{{ item.delegationName }}</div>\r\n        <div class=\"cell cell-title\">党派</div>\r\n        <div class=\"cell\">{{ item.party.name }}</div>\r\n\r\n        <!-- 第六行 -->\r\n        <div class=\"cell cell-title\">联名提案者</div>\r\n        <div class=\"cell col-span-3\">{{ item.joinUser }}</div>\r\n      </div>\r\n      <div class=\"remarks\">\r\n        附注：1、承办单位应在提案委员会交办提案之日起3个月内办结提案并答复提案者，特殊情况不得超过6个月。2、主办单位应在所主办提案全部答复提案者并填写完成《政协提案答复情况征询意见表》后10日内，整理答复意见文件和征询意见表，形成办理工作总结，一并报送市政协提案委和市委市政府督查室。\r\n      </div>\r\n      <div>\r\n        <div class=\"proposalTitle\">{{ item.title }}</div>\r\n        <div class=\"proposalContent\" v-html=\"item.content\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'suggestPrint' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick } from 'vue'\r\nimport { filterTableData } from '@/assets/js/suggestExportWord'\r\nimport { Print } from 'common/js/print'\r\nimport { user } from 'common/js/system_var.js'\r\nconst props = defineProps({ params: { type: Object, default: () => ({}) } })\r\nconst emit = defineEmits(['callback'])\r\nconst printData = ref([])\r\nconst printRef = ref()\r\nconst areaName = ref('')\r\nconst areaLogo = ref('')\r\nconst handlePrint = () => {\r\n  Print.init(printRef.value)\r\n}\r\nonMounted(() => {\r\n  const areaId = user.value.areaId\r\n  if (areaId == '370500') {\r\n    areaName.value = '东营市'\r\n    areaLogo.value = require('../../assets/img/dongyingshizhang.png')\r\n  } else if (areaId == '370502') {\r\n    areaName.value = '东营区'\r\n    areaLogo.value = require('../../assets/img/daitianjia.png')\r\n  } else if (areaId == '370503') {\r\n    areaName.value = '河口区'\r\n    areaLogo.value = require('../../assets/img/daitianjia.png')\r\n  } else if (areaId == '370505') {\r\n    areaName.value = '垦利区'\r\n    areaLogo.value = require('../../assets/img/daitianjia.png')\r\n  } else if (areaId == '370522') {\r\n    areaName.value = '利津县'\r\n    areaLogo.value = require('../../assets/img/daitianjia.png')\r\n  } else if (areaId == '370523') {\r\n    areaName.value = '广饶县'\r\n    areaLogo.value = require('../../assets/img/guangraoxianzhang.png')\r\n  } else {\r\n    areaName.value = ''\r\n    areaLogo.value = ''\r\n  }\r\n\r\n  suggestionWord()\r\n})\r\n// const rowspan = (item) => {\r\n//   let rowspannum = 0\r\n//   if (item.suggestSurveyTypeName) { rowspannum += 1 }\r\n//   if (item.notHandleTimeTypeName) { rowspannum += 1 }\r\n//   if (item.suggestOpenTypeName) { rowspannum += 1 }\r\n//   if (item.isMakeMineJobName) { rowspannum += 1 }\r\n//   if (item.isHopeEnhanceTalkName) { rowspannum += 1 }\r\n//   if (item.isNeedPaperAnswerName) { rowspannum += 1 }\r\n//   return rowspannum\r\n// }\r\nconst suggestionWord = async () => {\r\n  const { data } = await api.suggestionWord(props.params)\r\n  if (data.length) {\r\n    printData.value = []\r\n    for (let index = 0; index < data.length; index++) {\r\n      printData.value.push(filterTableData(data[index]))\r\n    }\r\n    nextTick(() => {\r\n      handlePrint()\r\n      emit('callback')\r\n    })\r\n  }\r\n}\r\ndefineExpose({ print: handlePrint })\r\n</script>\r\n<style lang=\"scss\">\r\n@font-face {\r\n  font-family: \"FZXiaoBiaoSong\";\r\n  src: url(../../assets/img/FZXiaoBiaoSong-B05S.ttf);\r\n}\r\n\r\n.suggestPrint {\r\n  width: 100%;\r\n\r\n  .suggestPrintBody {\r\n    width: 100%;\r\n\r\n    // .suggestPrintType {\r\n    //   font-size: 16pt;\r\n    //   font-weight: bold;\r\n    //   padding-bottom: 40pt;\r\n    //   display: flex;\r\n    //   justify-content: space-between;\r\n\r\n    //   span {\r\n    //     color: red;\r\n    //   }\r\n    // }\r\n\r\n    // .suggestPrintName {\r\n    //   color: red;\r\n    //   font-size: 24pt;\r\n    //   line-height: 1.2;\r\n    //   font-weight: bold;\r\n    //   text-align: center;\r\n    //   padding-bottom: 60pt;\r\n    // }\r\n\r\n    // .suggestPrintItem {\r\n    //   width: 100%;\r\n    //   display: flex;\r\n    //   font-size: 16pt;\r\n    //   line-height: 1.5;\r\n    //   padding-bottom: 8pt;\r\n\r\n    //   span {\r\n    //     display: inline-block;\r\n    //   }\r\n\r\n    //   .suggestPrintItemTitle {\r\n    //     width: 92px;\r\n    //     color: red;\r\n    //     font-weight: bold;\r\n    //     text-align: justify;\r\n    //     text-align-last: justify;\r\n    //   }\r\n\r\n    //   .suggestPrintItemColon {\r\n    //     width: 16px;\r\n    //     color: red;\r\n    //   }\r\n\r\n    //   .suggestPrintItemContent {\r\n    //     width: calc(100% - 108px);\r\n    //     border-bottom: 1px solid #262626;\r\n    //   }\r\n    // }\r\n\r\n    // .suggestPrintItemInfo {\r\n    //   padding-bottom: 54pt;\r\n    // }\r\n\r\n    // .suggestPrintItemJoin {\r\n    //   padding-bottom: 32pt;\r\n    // }\r\n\r\n    // .suggestPrintItemTwo {\r\n    //   width: 100%;\r\n    //   font-size: 16pt;\r\n    //   line-height: 1.5;\r\n    //   min-height: calc(16pt * 1.5);\r\n    //   border-bottom: 1px solid #262626;\r\n    //   margin-bottom: 8pt;\r\n    // }\r\n\r\n    // .suggestPrintItemFlex {\r\n    //   width: 100%;\r\n    //   display: flex;\r\n    //   justify-content: space-between;\r\n\r\n    //   .suggestPrintItem {\r\n    //     width: 46%;\r\n    //   }\r\n    // }\r\n\r\n    // .suggestPrintItemTime {\r\n    //   color: red;\r\n    //   text-align: center;\r\n    //   font-size: 14pt;\r\n    //   line-height: 1.5;\r\n    //   padding-top: 80pt;\r\n    //   font-family: \"Times New Roman\";\r\n    // }\r\n\r\n    // .mainModulePageTableName {\r\n    //   font-size: 14pt;\r\n    //   line-height: 1.5;\r\n    //   text-align: center;\r\n    //   margin-bottom: 7pt;\r\n    // }\r\n\r\n    // table {\r\n    //   width: 100%;\r\n    //   table-layout: fixed;\r\n    //   word-break: break-all;\r\n    //   border-collapse: collapse;\r\n    //   margin-bottom: 32pt;\r\n\r\n    //   tr {\r\n    //     page-break-inside: avoid;\r\n\r\n    //     td {\r\n    //       text-align: center;\r\n    //       line-height: 1.5;\r\n    //       padding: 8px;\r\n    //       font-size: 12pt;\r\n    //       border: 1px solid #000;\r\n    //       font-family: \"宋体\";\r\n    //       display: table-cell;\r\n    //       vertical-align: middle;\r\n    //     }\r\n    //   }\r\n    // }\r\n\r\n    // .suggestPrintContent {\r\n    //   overflow: hidden;\r\n    //   line-height: var(--zy-line-height);\r\n\r\n    //   img,\r\n    //   video {\r\n    //     max-width: 100%;\r\n    //     height: auto !important;\r\n    //   }\r\n\r\n    //   table {\r\n    //     border-collapse: collapse;\r\n    //     border-spacing: 0;\r\n\r\n    //     tr {\r\n    //       page-break-inside: avoid;\r\n    //     }\r\n    //   }\r\n    // }\r\n\r\n    .suggestPrintName {\r\n      font-size: 16pt;\r\n      line-height: 1.2;\r\n      text-align: center;\r\n      padding-bottom: 20pt;\r\n      font-family: 宋体;\r\n    }\r\n\r\n    .suggestPrintText {\r\n      font-size: 42pt;\r\n      text-align: center;\r\n      padding-bottom: 10pt;\r\n      font-weight: bold;\r\n      color: #000;\r\n      font-family: 宋体;\r\n    }\r\n\r\n    .suggestPrintTextNumber {\r\n      font-size: 14pt;\r\n      text-align: center;\r\n      padding-bottom: 10pt;\r\n      font-family: 宋体;\r\n    }\r\n\r\n    .suggestPrintContainer {\r\n      width: 600px;\r\n      border: 1px solid black;\r\n      display: grid;\r\n      grid-template-rows: auto 1fr;\r\n      font-family: Arial, sans-serif;\r\n\r\n      .suggestPrintContainerTitle {\r\n        display: flex;\r\n        border-bottom: 1px solid black;\r\n\r\n        .suggestPrintContainerTitle:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .suggestPrintContainerTitleLabel,\r\n        .suggestPrintContainerTitleContent {\r\n          padding: 15px;\r\n        }\r\n\r\n        .suggestPrintContainerTitleLabel {\r\n          width: 120px;\r\n          border-right: 1px solid black;\r\n          text-align: center;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 15pt;\r\n          font-family: 黑体;\r\n        }\r\n\r\n        .suggestPrintContainerTitleContent {\r\n          flex: 1;\r\n          position: relative;\r\n          font-size: 14pt;\r\n          font-family: 宋体;\r\n        }\r\n      }\r\n\r\n      .suggestPrintContainerContent {\r\n        display: flex;\r\n\r\n        .suggestPrintContainerContentLabel,\r\n        .suggestPrintContainerContentContent {\r\n          padding: 15px;\r\n        }\r\n\r\n        .suggestPrintContainerContentLabel {\r\n          width: 120px;\r\n          border-right: 1px solid black;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 15pt;\r\n          font-family: 黑体;\r\n        }\r\n\r\n        .suggestPrintContainerContentContent {\r\n          flex: 1;\r\n          position: relative;\r\n\r\n          .suggestPrintContainerContentContentItem {\r\n            margin: 10px 0;\r\n            font-size: 14pt;\r\n            font-family: 宋体;\r\n          }\r\n\r\n          .suggestPrintContainerContentContentRight {\r\n            position: absolute;\r\n            right: 10px;\r\n            bottom: 25px;\r\n            font-size: 14pt;\r\n            font-family: 宋体;\r\n          }\r\n\r\n          .suggestPrintContainerContentContentStamp {\r\n            position: absolute;\r\n            right: 0px;\r\n            bottom: 10px;\r\n            width: 130px;\r\n            opacity: 0.6;\r\n          }\r\n\r\n          .suggestPrintContainerContentContentTime {\r\n            position: absolute;\r\n            right: 10px;\r\n            bottom: 0px;\r\n            font-size: 14pt;\r\n            font-family: 宋体;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .proposerBasicInformationTitle {\r\n      font-size: 18pt;\r\n      font-family: 黑体;\r\n      text-align: center;\r\n      margin: 10px 0;\r\n    }\r\n\r\n    .proposerBasicInformationTable {\r\n      display: grid;\r\n      width: 600px;\r\n      border: 1px solid black;\r\n      grid-template-columns: 160px 170px 120px 150px;\r\n      font-family: Arial, sans-serif;\r\n\r\n      .cell {\r\n        border: 1px solid black;\r\n        padding: 8px;\r\n        text-align: center;\r\n        font-size: 14pt;\r\n        font-family: 宋体;\r\n      }\r\n\r\n      table {\r\n        max-width: 100%;\r\n        border-collapse: collapse;\r\n        border-spacing: 0;\r\n\r\n        tr {\r\n          page-break-inside: avoid;\r\n        }\r\n      }\r\n\r\n      .col-span-3 {\r\n        grid-column: span 3;\r\n        text-align: left;\r\n      }\r\n\r\n      .col-span-2 {\r\n        grid-column: span 2;\r\n        text-align: left;\r\n      }\r\n    }\r\n\r\n    .remarks {\r\n      font-size: 12pt;\r\n      line-height: 1.6;\r\n      font-family: 楷体;\r\n    }\r\n\r\n    .proposalTitle {\r\n      text-align: center;\r\n      font-size: 22pt;\r\n      font-family: FZXiaoBiaoSong;\r\n      margin-top: 50px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAsPA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,IAAI,QAAQ,yBAAyB;AAP9C,IAAAC,WAAA,GAAe;EAAElC,IAAI,EAAE;AAAe,CAAC;;;;;;;;;;;;;;IAQvC,IAAMmC,KAAK,GAAGC,OAA8D;IAC5E,IAAMC,IAAI,GAAGC,MAAyB;IACtC,IAAMC,SAAS,GAAGX,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMY,QAAQ,GAAGZ,GAAG,CAAC,CAAC;IACtB,IAAMa,QAAQ,GAAGb,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMc,QAAQ,GAAGd,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMe,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBX,KAAK,CAACY,IAAI,CAACJ,QAAQ,CAACjH,KAAK,CAAC;IAC5B,CAAC;IACDsG,SAAS,CAAC,YAAM;MACd,IAAMgB,MAAM,GAAGZ,IAAI,CAAC1G,KAAK,CAACsH,MAAM;MAChC,IAAIA,MAAM,IAAI,QAAQ,EAAE;QACtBJ,QAAQ,CAAClH,KAAK,GAAG,KAAK;QACtBmH,QAAQ,CAACnH,KAAK,GAAGuH,OAAO,CAAC,uCAAuC,CAAC;MACnE,CAAC,MAAM,IAAID,MAAM,IAAI,QAAQ,EAAE;QAC7BJ,QAAQ,CAAClH,KAAK,GAAG,KAAK;QACtBmH,QAAQ,CAACnH,KAAK,GAAGuH,OAAO,CAAC,iCAAiC,CAAC;MAC7D,CAAC,MAAM,IAAID,MAAM,IAAI,QAAQ,EAAE;QAC7BJ,QAAQ,CAAClH,KAAK,GAAG,KAAK;QACtBmH,QAAQ,CAACnH,KAAK,GAAGuH,OAAO,CAAC,iCAAiC,CAAC;MAC7D,CAAC,MAAM,IAAID,MAAM,IAAI,QAAQ,EAAE;QAC7BJ,QAAQ,CAAClH,KAAK,GAAG,KAAK;QACtBmH,QAAQ,CAACnH,KAAK,GAAGuH,OAAO,CAAC,iCAAiC,CAAC;MAC7D,CAAC,MAAM,IAAID,MAAM,IAAI,QAAQ,EAAE;QAC7BJ,QAAQ,CAAClH,KAAK,GAAG,KAAK;QACtBmH,QAAQ,CAACnH,KAAK,GAAGuH,OAAO,CAAC,iCAAiC,CAAC;MAC7D,CAAC,MAAM,IAAID,MAAM,IAAI,QAAQ,EAAE;QAC7BJ,QAAQ,CAAClH,KAAK,GAAG,KAAK;QACtBmH,QAAQ,CAACnH,KAAK,GAAGuH,OAAO,CAAC,wCAAwC,CAAC;MACpE,CAAC,MAAM;QACLL,QAAQ,CAAClH,KAAK,GAAG,EAAE;QACnBmH,QAAQ,CAACnH,KAAK,GAAG,EAAE;MACrB;MAEAwH,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAMA,cAAc;MAAA,IAAAC,KAAA,GAAA1B,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgD,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA,EAAAC,KAAA;QAAA,OAAAvI,mBAAA,GAAAuB,IAAA,UAAAiH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA5C,IAAA,GAAA4C,QAAA,CAAAvE,IAAA;YAAA;cAAAuE,QAAA,CAAAvE,IAAA;cAAA,OACE4C,GAAG,CAACoB,cAAc,CAACZ,KAAK,CAACoB,MAAM,CAAC;YAAA;cAAAL,qBAAA,GAAAI,QAAA,CAAA9E,IAAA;cAA/C2E,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZ,IAAIA,IAAI,CAACvD,MAAM,EAAE;gBACf2C,SAAS,CAAChH,KAAK,GAAG,EAAE;gBACpB,KAAS6H,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,IAAI,CAACvD,MAAM,EAAEwD,KAAK,EAAE,EAAE;kBAChDb,SAAS,CAAChH,KAAK,CAACgE,IAAI,CAACwC,eAAe,CAACoB,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC;gBACpD;gBACAtB,QAAQ,CAAC,YAAM;kBACba,WAAW,CAAC,CAAC;kBACbN,IAAI,CAAC,UAAU,CAAC;gBAClB,CAAC,CAAC;cACJ;YAAC;YAAA;cAAA,OAAAiB,QAAA,CAAAzC,IAAA;UAAA;QAAA,GAAAoC,OAAA;MAAA,CACF;MAAA,gBAZKF,cAAcA,CAAA;QAAA,OAAAC,KAAA,CAAAxB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAYnB;IACDiC,QAAY,CAAC;MAAEC,KAAK,EAAEd;IAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}