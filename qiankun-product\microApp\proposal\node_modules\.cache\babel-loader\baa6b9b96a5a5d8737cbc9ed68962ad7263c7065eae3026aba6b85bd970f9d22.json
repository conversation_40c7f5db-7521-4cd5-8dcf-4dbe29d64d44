{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SubmitCollectiveProposalUnit\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"单位名称\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.name,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.name = $event;\n            }),\n            placeholder: \"请输入单位名称\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"序号\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.sort,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.sort = $event;\n            }),\n            maxlength: \"10\",\n            \"show-word-limit\": \"\",\n            onInput: _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.sort = $setup.validNum($setup.form.sort);\n            }),\n            placeholder: \"请输入序号\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"联系人\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.contactUser,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.contactUser = $event;\n            }),\n            placeholder: \"请输入联系人\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"联系电话\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.contactPhone,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.contactPhone = $event;\n            }),\n            placeholder: \"请输入联系电话\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"类型\",\n        prop: \"teamOfficeTheme\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.teamOfficeTheme,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.form.teamOfficeTheme = $event;\n            }),\n            placeholder: \"请选择类型\",\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.teamOfficeThemeData, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.key,\n                  label: item.name,\n                  value: item.key\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[6] || (_cache[6] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[7] || (_cache[7] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[8] || (_cache[8] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "name", "_cache", "$event", "placeholder", "clearable", "_", "sort", "maxlength", "onInput", "validNum", "contactUser", "contactPhone", "_component_el_select", "teamOfficeTheme", "_Fragment", "_renderList", "teamOfficeThemeData", "item", "_createBlock", "_component_el_option", "key", "value", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\component\\SubmitCollectiveProposalUnit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SubmitCollectiveProposalUnit\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item label=\"单位名称\"\r\n                    prop=\"name\">\r\n        <el-input v-model=\"form.name\"\r\n                  placeholder=\"请输入单位名称\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"序号\">\r\n        <el-input v-model=\"form.sort\"\r\n                  maxlength=\"10\"\r\n                  show-word-limit\r\n                  @input=\"form.sort = validNum(form.sort)\"\r\n                  placeholder=\"请输入序号\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\">\r\n        <el-input v-model=\"form.contactUser\"\r\n                  placeholder=\"请输入联系人\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"联系电话\">\r\n        <el-input v-model=\"form.contactPhone\"\r\n                  placeholder=\"请输入联系电话\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"类型\"\r\n                    prop=\"teamOfficeTheme\"\r\n                    class=\"globalFormTitle\">\r\n        <el-select v-model=\"form.teamOfficeTheme\"\r\n                   placeholder=\"请选择类型\"\r\n                   clearable>\r\n          <el-option v-for=\"item in teamOfficeThemeData\"\r\n                     :key=\"item.key\"\r\n                     :label=\"item.name\"\r\n                     :value=\"item.key\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitCollectiveProposalUnit' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { validNum } from 'common/js/utils.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  name: '',\r\n  sort: '',\r\n  contactUser: '',\r\n  contactPhone: '',\r\n  teamOfficeTheme: ''\r\n})\r\nconst teamOfficeThemeData = ref([])\r\nconst rules = reactive({ name: [{ required: true, message: '请输入单位名称', trigger: ['blur', 'change'] }] })\r\n\r\nonMounted(() => {\r\n  dictionaryData()\r\n  if (props.id) { teamOfficeInfo() }\r\n})\r\n\r\n/**\r\n * @description: 获取字典数据，用于填充模板类型下拉选项\r\n * @return {void}\r\n */\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['team_office_theme'] })\r\n  var { data } = res\r\n  teamOfficeThemeData.value = data.team_office_theme\r\n}\r\nconst teamOfficeInfo = async () => {\r\n  const res = await api.teamOfficeInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.name = data.name\r\n  form.sort = data.sort\r\n  form.contactUser = data.contactUser\r\n  form.contactPhone = data.contactPhone\r\n  form.teamOfficeTheme = data.teamOfficeTheme.value\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/teamOffice/edit' : '/teamOffice/add', {\r\n    form: { id: props.id, name: form.name, sort: form.sort, contactUser: form.contactUser, contactPhone: form.contactPhone, teamOfficeTheme: form.teamOfficeTheme }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitCollectiveProposalUnit {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA8B;;EA4ChCA,KAAK,EAAC;AAAkB;;;;;;;;uBA5CjCC,mBAAA,CAkDM,OAlDNC,UAkDM,GAjDJC,YAAA,CAgDUC,kBAAA;IAhDDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAKe,CALfT,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC;;QATzBJ,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YAZ9BC,UAAA,EAU2BV,MAAA,CAAAC,IAAI,CAACU,IAAI;YAVpC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAU2Bb,MAAA,CAAAC,IAAI,CAACU,IAAI,GAAAE,MAAA;YAAA;YAClBC,WAAW,EAAC,SAAS;YACrBC,SAAS,EAAT;;;QAZlBC,CAAA;UAcMpB,YAAA,CAOeU,uBAAA;QAPDC,KAAK,EAAC;MAAI;QAd9BH,OAAA,EAAAC,QAAA,CAeQ;UAAA,OAKsB,CALtBT,YAAA,CAKsBa,mBAAA;YApB9BC,UAAA,EAe2BV,MAAA,CAAAC,IAAI,CAACgB,IAAI;YAfpC,uBAAAL,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAe2Bb,MAAA,CAAAC,IAAI,CAACgB,IAAI,GAAAJ,MAAA;YAAA;YAClBK,SAAS,EAAC,IAAI;YACd,iBAAe,EAAf,EAAe;YACdC,OAAK,EAAAP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEb,MAAA,CAAAC,IAAI,CAACgB,IAAI,GAAGjB,MAAA,CAAAoB,QAAQ,CAACpB,MAAA,CAAAC,IAAI,CAACgB,IAAI;YAAA;YACtCH,WAAW,EAAC,OAAO;YACnBC,SAAS,EAAT;;;QApBlBC,CAAA;UAsBMpB,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC;MAAK;QAtB/BH,OAAA,EAAAC,QAAA,CAuBQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YAzB9BC,UAAA,EAuB2BV,MAAA,CAAAC,IAAI,CAACoB,WAAW;YAvB3C,uBAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAuB2Bb,MAAA,CAAAC,IAAI,CAACoB,WAAW,GAAAR,MAAA;YAAA;YACzBC,WAAW,EAAC,QAAQ;YACpBC,SAAS,EAAT;;;QAzBlBC,CAAA;UA2BMpB,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC;MAAM;QA3BhCH,OAAA,EAAAC,QAAA,CA4BQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBa,mBAAA;YA9B9BC,UAAA,EA4B2BV,MAAA,CAAAC,IAAI,CAACqB,YAAY;YA5B5C,uBAAAV,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA4B2Bb,MAAA,CAAAC,IAAI,CAACqB,YAAY,GAAAT,MAAA;YAAA;YAC1BC,WAAW,EAAC,SAAS;YACrBC,SAAS,EAAT;;;QA9BlBC,CAAA;UAgCMpB,YAAA,CAYeU,uBAAA;QAZDC,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,iBAAiB;QACtBf,KAAK,EAAC;;QAlC1BW,OAAA,EAAAC,QAAA,CAmCQ;UAAA,OAQY,CARZT,YAAA,CAQY2B,oBAAA;YA3CpBb,UAAA,EAmC4BV,MAAA,CAAAC,IAAI,CAACuB,eAAe;YAnChD,uBAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAmC4Bb,MAAA,CAAAC,IAAI,CAACuB,eAAe,GAAAX,MAAA;YAAA;YAC7BC,WAAW,EAAC,OAAO;YACnBC,SAAS,EAAT;;YArCnBX,OAAA,EAAAC,QAAA,CAsCqB;cAAA,OAAmC,E,kBAA9CX,mBAAA,CAIY+B,SAAA,QA1CtBC,WAAA,CAsCoC1B,MAAA,CAAA2B,mBAAmB,EAtCvD,UAsC4BC,IAAI;qCAAtBC,YAAA,CAIYC,oBAAA;kBAHAC,GAAG,EAAEH,IAAI,CAACG,GAAG;kBACbxB,KAAK,EAAEqB,IAAI,CAACjB,IAAI;kBAChBqB,KAAK,EAAEJ,IAAI,CAACG;;;;YAzClCf,CAAA;;;QAAAA,CAAA;UA6CMiB,mBAAA,CAIM,OAJNC,UAIM,GAHJtC,YAAA,CACsDuC,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAzB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAsC,UAAU,CAACtC,MAAA,CAAAuC,OAAO;QAAA;;QA/C7CnC,OAAA,EAAAC,QAAA,CA+CgD;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA/ClD4B,gBAAA,CA+CgD,IAAE,E;;QA/ClDxB,CAAA;UAgDQpB,YAAA,CAA4CuC,oBAAA;QAAhCE,OAAK,EAAErC,MAAA,CAAAyC;MAAS;QAhDpCrC,OAAA,EAAAC,QAAA,CAgDsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAhDxC4B,gBAAA,CAgDsC,IAAE,E;;QAhDxCxB,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}