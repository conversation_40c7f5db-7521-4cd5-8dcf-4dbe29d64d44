{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, computed, onMounted, onUnmounted, watch, nextTick, defineAsyncComponent } from 'vue';\nimport * as RongIMLib from '@rongcloud/imlib-next';\nimport { format } from 'common/js/time.js';\nimport utils, { size2Str } from 'common/js/utils.js';\nimport { globalFileLocation } from 'common/config/location';\nimport { user, appOnlyHeader } from 'common/js/system_var.js';\n// import { emotion } from '../js/emotion.js' emoteIcon, folderIcon, lineFeedIcon, voteIcon,\nimport { chatGroupMemberList } from '../js/ChatMethod.js';\nimport { fileIcon, handleTimeFormat, handleHistoryMessages, getUniqueFileName } from '../js/ChatViewMethod.js';\nimport { notificationIcon, initGroupChatIcon, announcementIcon, clearAwayIcon, voteListIcon, voteBgIcon } from '../js/icon.js';\nimport { Search, Picture } from '@element-plus/icons-vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nvar __default__ = {\n  name: 'GlobalChatView'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: [String, Number],\n    chatList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  emits: ['update:modelValue', 'time', 'refresh', 'send'],\n  setup(__props, _ref) {\n    var _window$electron;\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    var GlobalChatEditor = defineAsyncComponent(function () {\n      return import('./GlobalChatEditor.vue');\n    });\n    var ChatPopupWindow = defineAsyncComponent(function () {\n      return import('./chat-popup-window/chat-popup-window.vue');\n    });\n    var SettingPopupWindow = defineAsyncComponent(function () {\n      return import('./setting-popup-window/setting-popup-window.vue');\n    });\n    var ChatSendImg = defineAsyncComponent(function () {\n      return import('./ChatSendImg/ChatSendImg.vue');\n    });\n    var ChatSendFile = defineAsyncComponent(function () {\n      return import('./ChatSendFile/ChatSendFile.vue');\n    });\n    var GlobalCreateGroup = defineAsyncComponent(function () {\n      return import('./GlobalCreateGroup/GlobalCreateGroup.vue');\n    });\n    var GlobalGroupAddUser = defineAsyncComponent(function () {\n      return import('./GlobalGroupAddUser/GlobalGroupAddUser.vue');\n    });\n    var GlobalGroupDelUser = defineAsyncComponent(function () {\n      return import('./GlobalGroupDelUser/GlobalGroupDelUser.vue');\n    });\n    var GlobalGroupName = defineAsyncComponent(function () {\n      return import('./GlobalGroupName/GlobalGroupName.vue');\n    });\n    var GlobalGroupQr = defineAsyncComponent(function () {\n      return import('./GlobalGroupQr/GlobalGroupQr.vue');\n    });\n    var GlobalGroupAnnouncement = defineAsyncComponent(function () {\n      return import('./GlobalGroupAnnouncement/GlobalGroupAnnouncement.vue');\n    });\n    var GlobalGroupTransfer = defineAsyncComponent(function () {\n      return import('./GlobalGroupTransfer/GlobalGroupTransfer.vue');\n    });\n    var GlobalChatViewWindow = defineAsyncComponent(function () {\n      return import('./GlobalChatViewWindow/GlobalChatViewWindow.vue');\n    });\n    var GlobalGroupVote = defineAsyncComponent(function () {\n      return import('./GlobalGroupVote/GlobalGroupVote.vue');\n    });\n    var GlobalCreateVote = defineAsyncComponent(function () {\n      return import('./GlobalGroupVote/GlobalCreateVote.vue');\n    });\n    var GlobalVoteDetails = defineAsyncComponent(function () {\n      return import('./GlobalGroupVote/GlobalVoteDetails.vue');\n    });\n    var props = __props;\n    var emit = __emit;\n    var isMac = (_window$electron = window.electron) === null || _window$electron === void 0 ? void 0 : _window$electron.isMac;\n    var isElectron = window.electron ? true : false;\n    var keyword = ref('');\n    var chatId = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(value) {\n        emit('update:modelValue', value);\n      }\n    });\n    var chatType = ['RC:TxtMsg', 'RC:ImgTextMsg'];\n    var chatList = computed(function () {\n      return props.chatList;\n    });\n    var isChat = ref(true);\n    var chatInfo = ref({});\n    var groupUser = ref([]);\n    var chatInfoImg = ref([]);\n    var chatInfoAudio = ref({});\n    var chatInfoAudioObj = ref({});\n    var electronFile = ref([]);\n    var isElectronFile = ref([]);\n    var isElectronFileObj = ref({});\n    var electronRecordObj = ref({});\n    var electronRecordData = ref([]);\n    var chatInfoUser = ref({});\n    var chatMenuTop = ref(0);\n    var chatMenuLeft = ref(0);\n    var chatMenuItem = ref({});\n    var chatMenuShow = ref(false);\n    var isChatMenuShow = ref(false);\n    var messagesMenuTop = ref(0);\n    var messagesMenuLeft = ref(0);\n    var messagesMenuItem = ref({});\n    var messagesMenuShow = ref(false);\n    var isMessagesMenuShow = ref(false);\n    var fileList = ref([]);\n    var fileShow = ref(false);\n    var fileImg = ref({});\n    var imgShow = ref(false);\n    var userId = ref([]);\n    var createGroupShow = ref(false);\n    var infoId = ref('');\n    var addShow = ref(false);\n    var delShow = ref(false);\n    var nameShow = ref(false);\n    var qrShow = ref(false);\n    var isGroupOwner = ref(false);\n    var announcementShow = ref(false);\n    var transferShow = ref(false);\n    var settingShow = ref(false);\n    var chatGroupAnnouncement = ref('');\n    var isChatGroupAnnouncement = ref(false);\n    var voteId = ref('');\n    var voteRefresh = ref('');\n    var voteShow = ref(false);\n    var createVoteShow = ref(false);\n    var voteDetailsShow = ref(false);\n    // 图片地址拼接组合\n    var imgUrl = function imgUrl(url) {\n      return url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg');\n    };\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var scrollRef = ref();\n    var editorRef = ref();\n    var scrollHeight = ref(0);\n    var scrollTopNum = ref(0);\n    var scrollShow = ref(true);\n    var chatInfoMessages = ref([]);\n    var chatInfoMessagesData = ref([]);\n    onMounted(function () {});\n    var handleElClick = function handleElClick(e) {\n      chatMenuShow.value = false;\n      messagesMenuShow.value = false;\n      e.preventDefault();\n    };\n    var handleElContextmenu = function handleElContextmenu(e) {\n      if (isChatMenuShow.value) {\n        isChatMenuShow.value = false;\n      } else {\n        chatMenuShow.value = false;\n      }\n      if (isMessagesMenuShow.value) {\n        isMessagesMenuShow.value = false;\n      } else {\n        messagesMenuShow.value = false;\n      }\n      e.preventDefault();\n    };\n    var handleCustom = function handleCustom(item) {\n      if (item.customType === 'file') isElectron ? handleElectronFile(item.uid, item.file, false) : handlePreview(item.file);\n      if (item.customType === 'vote') handleVoteDetails(item.vote);\n    };\n    var handleElectronFile = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(uid, file) {\n        var _isElectronFile$value;\n        var type,\n          fileName,\n          fileFolderPath,\n          result,\n          _args = arguments;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              type = _args.length > 2 && _args[2] !== undefined ? _args[2] : false;\n              if (!((_isElectronFile$value = isElectronFile.value) !== null && _isElectronFile$value !== void 0 && _isElectronFile$value.includes(uid))) {\n                _context.next = 3;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 3:\n              fileName = file.originalFileName;\n              if (electronRecordObj.value[uid]) {\n                fileName = electronRecordObj.value[uid];\n              } else {\n                fileName = getUniqueFileName(fileName, electronRecordData.value);\n                electronRecordObj.value[uid] = fileName;\n                electronRecordData.value.push(fileName);\n              }\n              fileFolderPath = chatId.value + '_' + user.value.accountId + '_file';\n              _context.next = 8;\n              return window.electron.fileExists(fileFolderPath, fileName);\n            case 8:\n              result = _context.sent;\n              if (result) type ? handleOpenFolderSelectFile(fileName) : handleElectronOpenFile(fileName);\n              if (!result) globalElectronFileDownload(uid, file, fileName, type);\n            case 11:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function handleElectronFile(_x, _x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var onDownloadProgress = function onDownloadProgress(progressEvent, id) {\n      var _progressEvent$event;\n      if (progressEvent !== null && progressEvent !== void 0 && (_progressEvent$event = progressEvent.event) !== null && _progressEvent$event !== void 0 && _progressEvent$event.lengthComputable) {\n        var progress = (progressEvent.loaded / progressEvent.total * 100).toFixed(0);\n        isElectronFileObj.value[id] = 100 - parseInt(progress) + '%';\n      }\n    };\n    var globalElectronFileDownload = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(uid, file, fileName) {\n        var type,\n          res,\n          fileFolderPath,\n          saveRes,\n          _args2 = arguments;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              type = _args2.length > 3 && _args2[3] !== undefined ? _args2[3] : false;\n              isElectronFile.value.push(uid);\n              isElectronFileObj.value[uid] = '100%';\n              _context2.next = 5;\n              return api.globalElectronFileDownload(uid, file.id, {}, onDownloadProgress);\n            case 5:\n              res = _context2.sent;\n              isElectronFile.value = isElectronFile.value.filter(function (v) {\n                return v !== uid;\n              });\n              delete isElectronFileObj.value[uid];\n              fileFolderPath = chatId.value + '_' + user.value.accountId + '_file';\n              _context2.next = 11;\n              return window.electron.saveFile(fileFolderPath, fileName, res);\n            case 11:\n              saveRes = _context2.sent;\n              if (saveRes.type === 'success') type ? handleOpenFolderSelectFile(fileName) : handleElectronOpenFile(fileName);\n              if (saveRes.type === 'error') ElMessage({\n                type: 'error',\n                message: saveRes.message\n              });\n            case 14:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function globalElectronFileDownload(_x3, _x4, _x5) {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var handleElectronOpenFile = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(fileName) {\n        var fileFolderPath, openRes;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              fileFolderPath = chatId.value + '_' + user.value.accountId + '_file';\n              _context3.next = 3;\n              return window.electron.openFile(fileFolderPath, fileName);\n            case 3:\n              openRes = _context3.sent;\n              if (openRes.type === 'error') ElMessage({\n                type: 'error',\n                message: openRes.message\n              });\n            case 5:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function handleElectronOpenFile(_x6) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var handleOpenFolderSelectFile = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(fileName) {\n        var fileFolderPath, openRes;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              fileFolderPath = chatId.value + '_' + user.value.accountId + '_file';\n              _context4.next = 3;\n              return window.electron.openFolderSelectFile(`chat_files/${fileFolderPath}`, fileName);\n            case 3:\n              openRes = _context4.sent;\n              if (openRes.type === 'error') ElMessage({\n                type: 'error',\n                message: openRes.message\n              });\n            case 5:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function handleOpenFolderSelectFile(_x7) {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handleFolderSelectFile = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var _messagesMenuItem$val, _messagesMenuItem$val2;\n        var uid, file;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              uid = (_messagesMenuItem$val = messagesMenuItem.value) === null || _messagesMenuItem$val === void 0 ? void 0 : _messagesMenuItem$val.uid;\n              file = (_messagesMenuItem$val2 = messagesMenuItem.value) === null || _messagesMenuItem$val2 === void 0 ? void 0 : _messagesMenuItem$val2.file;\n              handleElectronFile(uid, file, true);\n            case 3:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function handleFolderSelectFile() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var globalElectronSaveRecord = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6(recordId, data) {\n        var fileName, fileContent, res;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              fileName = recordId + '_' + user.value.accountId + '_record.txt';\n              fileContent = utils.gm_encrypt(data, 'zysoft2017-08-11', 'zysoft2017-08-11');\n              _context6.next = 4;\n              return window.electron.saveRecordFile('chat_record', fileName, fileContent);\n            case 4:\n              res = _context6.sent;\n              console.log(res);\n            case 6:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function globalElectronSaveRecord(_x8, _x9) {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var globalElectronReadRecord = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7(recordId) {\n        var fileName, res, recordObj, recordData, key, value;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              fileName = recordId + '_' + user.value.accountId + '_record.txt';\n              _context7.next = 3;\n              return window.electron.readRecordFile('chat_record', fileName);\n            case 3:\n              res = _context7.sent;\n              if (res.type === 'success') {\n                recordObj = JSON.parse(utils.gm_decrypt(res.data, 'zysoft2017-08-11', 'zysoft2017-08-11'));\n                recordData = [];\n                for (key in recordObj) {\n                  if (Object.prototype.hasOwnProperty.call(recordObj, key)) {\n                    value = recordObj[key];\n                    recordData.push(value);\n                  }\n                }\n                electronRecordObj.value = recordObj;\n                electronRecordData.value = recordData;\n              }\n            case 5:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7);\n      }));\n      return function globalElectronReadRecord(_x10) {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    var handlePreview = function handlePreview(row) {\n      if (!row) return;\n      globalFileLocation({\n        name: process.env.VUE_APP_NAME,\n        fileId: row.id,\n        fileType: row.extName,\n        fileName: row.originalFileName,\n        fileSize: row.fileSize\n      });\n    };\n    var querySearch = function querySearch(queryString, cb) {\n      var results = queryString ? chatList.value.filter(function (v) {\n        var _v$chatObjectInfo;\n        return (_v$chatObjectInfo = v.chatObjectInfo) === null || _v$chatObjectInfo === void 0 || (_v$chatObjectInfo = _v$chatObjectInfo.name) === null || _v$chatObjectInfo === void 0 ? void 0 : _v$chatObjectInfo.toLowerCase().includes(queryString === null || queryString === void 0 ? void 0 : queryString.toLowerCase());\n      }) : [];\n      cb(results);\n    };\n    var scrollDown = function scrollDown() {\n      scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight;\n    };\n    var scrollElHeight = function scrollElHeight() {\n      scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - scrollHeight.value;\n    };\n    var handleMessagesScroll = function handleMessagesScroll(_ref9) {\n      var scrollTop = _ref9.scrollTop;\n      scrollTopNum.value = scrollTop;\n      if (scrollTop === 0) {\n        var _chatInfoMessages$val;\n        scrollHeight.value = scrollRef.value.wrapRef.scrollHeight;\n        getHistoryMessages(((_chatInfoMessages$val = chatInfoMessages.value[0]) === null || _chatInfoMessages$val === void 0 ? void 0 : _chatInfoMessages$val.sentTime) || 0);\n      }\n    };\n    var handleClick = /*#__PURE__*/function () {\n      var _ref10 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8(item) {\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              if (!(chatId.value === item.id)) {\n                _context8.next = 2;\n                break;\n              }\n              return _context8.abrupt(\"return\");\n            case 2:\n              isChat.value = true;\n              chatId.value = item.id;\n            case 4:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8);\n      }));\n      return function handleClick(_x11) {\n        return _ref10.apply(this, arguments);\n      };\n    }();\n    var handleChatClick = /*#__PURE__*/function () {\n      var _ref11 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9(item, type) {\n        var _editorRef$value;\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1) switch (_context9.prev = _context9.next) {\n            case 0:\n              settingShow.value = false;\n              chatInfo.value = item;\n              scrollHeight.value = 0;\n              chatInfoMessages.value = [];\n              electronFile.value = [];\n              isElectronFile.value = [];\n              isElectronFileObj.value = {};\n              if (!type) {\n                (_editorRef$value = editorRef.value) === null || _editorRef$value === void 0 || _editorRef$value.clearMessage();\n                chatGroupAnnouncement.value = '';\n                isChatGroupAnnouncement.value = false;\n              }\n              getHistoryMessages();\n              if (!item.isTemporary) clearMessagesUnreadStatus();\n            case 10:\n            case \"end\":\n              return _context9.stop();\n          }\n        }, _callee9);\n      }));\n      return function handleChatClick(_x12, _x13) {\n        return _ref11.apply(this, arguments);\n      };\n    }();\n    var handleClearAway = function handleClearAway() {\n      ElMessageBox.confirm('此操作将清除所有消息的未读状态, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        clearAllMessagesUnreadStatus();\n      }).catch(function () {\n        ElMessage({\n          type: 'info',\n          message: '已取消清除'\n        });\n      });\n    };\n    var clearAllMessagesUnreadStatus = /*#__PURE__*/function () {\n      var _ref12 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee10() {\n        var _yield$RongIMLib$clea, code, msg;\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1) switch (_context10.prev = _context10.next) {\n            case 0:\n              _context10.next = 2;\n              return RongIMLib.clearAllMessagesUnreadStatus();\n            case 2:\n              _yield$RongIMLib$clea = _context10.sent;\n              code = _yield$RongIMLib$clea.code;\n              msg = _yield$RongIMLib$clea.msg;\n              if (code === 0) {\n                handleRefresh();\n              } else {\n                console.log(code, msg);\n              }\n            case 6:\n            case \"end\":\n              return _context10.stop();\n          }\n        }, _callee10);\n      }));\n      return function clearAllMessagesUnreadStatus() {\n        return _ref12.apply(this, arguments);\n      };\n    }();\n    var clearMessagesUnreadStatus = /*#__PURE__*/function () {\n      var _ref13 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee11(type) {\n        var _yield$RongIMLib$clea2, code, msg;\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              _context11.next = 2;\n              return RongIMLib.clearMessagesUnreadStatus({\n                conversationType: chatInfo.value.type,\n                targetId: chatInfo.value.targetId\n              });\n            case 2:\n              _yield$RongIMLib$clea2 = _context11.sent;\n              code = _yield$RongIMLib$clea2.code;\n              msg = _yield$RongIMLib$clea2.msg;\n              if (code === 0) {\n                if (!type) handleRefresh();\n              } else {\n                console.log(code, msg);\n              }\n            case 6:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee11);\n      }));\n      return function clearMessagesUnreadStatus(_x14) {\n        return _ref13.apply(this, arguments);\n      };\n    }();\n    var getHistoryMessages = /*#__PURE__*/function () {\n      var _ref14 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee12() {\n        var timestamp,\n          option,\n          conversation,\n          _yield$RongIMLib$getH,\n          code,\n          data,\n          msg,\n          _args12 = arguments;\n        return _regeneratorRuntime().wrap(function _callee12$(_context12) {\n          while (1) switch (_context12.prev = _context12.next) {\n            case 0:\n              timestamp = _args12.length > 0 && _args12[0] !== undefined ? _args12[0] : 0;\n              option = {\n                timestamp,\n                count: 20,\n                order: 0\n              };\n              conversation = {\n                conversationType: chatInfo.value.type,\n                targetId: chatInfo.value.targetId\n              };\n              _context12.next = 5;\n              return RongIMLib.getHistoryMessages(conversation, option);\n            case 5:\n              _yield$RongIMLib$getH = _context12.sent;\n              code = _yield$RongIMLib$getH.code;\n              data = _yield$RongIMLib$getH.data;\n              msg = _yield$RongIMLib$getH.msg;\n              if (!(code === 0)) {\n                _context12.next = 16;\n                break;\n              }\n              _context12.next = 12;\n              return handleUser();\n            case 12:\n              scrollShow.value = timestamp === 0;\n              handleMessages(data.list, timestamp === 0);\n              _context12.next = 17;\n              break;\n            case 16:\n              console.log(code, msg);\n            case 17:\n            case \"end\":\n              return _context12.stop();\n          }\n        }, _callee12);\n      }));\n      return function getHistoryMessages() {\n        return _ref14.apply(this, arguments);\n      };\n    }();\n    var getNewestMessages = /*#__PURE__*/function () {\n      var _ref15 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee13() {\n        var option, conversation, _yield$RongIMLib$getH2, code, data, msg;\n        return _regeneratorRuntime().wrap(function _callee13$(_context13) {\n          while (1) switch (_context13.prev = _context13.next) {\n            case 0:\n              option = {\n                timestamp: chatInfo.value.sentTime,\n                count: 20,\n                order: 1\n              };\n              conversation = {\n                conversationType: chatInfo.value.type,\n                targetId: chatInfo.value.targetId\n              };\n              _context13.next = 4;\n              return RongIMLib.getHistoryMessages(conversation, option);\n            case 4:\n              _yield$RongIMLib$getH2 = _context13.sent;\n              code = _yield$RongIMLib$getH2.code;\n              data = _yield$RongIMLib$getH2.data;\n              msg = _yield$RongIMLib$getH2.msg;\n              if (code === 0) {\n                scrollShow.value = true;\n                clearMessagesUnreadStatus(true);\n                handleMessages(data.list, true);\n              } else {\n                console.log(code, msg);\n              }\n            case 9:\n            case \"end\":\n              return _context13.stop();\n          }\n        }, _callee13);\n      }));\n      return function getNewestMessages() {\n        return _ref15.apply(this, arguments);\n      };\n    }();\n    var chatGroupInfo = /*#__PURE__*/function () {\n      var _ref16 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee14(id) {\n        var _yield$api$chatGroupI, data, groupAnnouncement, groupAnnouncementItem;\n        return _regeneratorRuntime().wrap(function _callee14$(_context14) {\n          while (1) switch (_context14.prev = _context14.next) {\n            case 0:\n              _context14.next = 2;\n              return api.chatGroupInfo({\n                detailId: id.slice(appOnlyHeader.value.length)\n              });\n            case 2:\n              _yield$api$chatGroupI = _context14.sent;\n              data = _yield$api$chatGroupI.data;\n              groupAnnouncement = JSON.parse(localStorage.getItem('isChatGroupAnnouncement')) || {};\n              groupAnnouncementItem = groupAnnouncement.hasOwnProperty(id) ? groupAnnouncement[id] : {\n                show: true,\n                callBoard: ''\n              };\n              if (data.callBoard && groupAnnouncementItem.callBoard !== data.callBoard) {\n                chatGroupAnnouncement.value = data.callBoard;\n                isChatGroupAnnouncement.value = true;\n              }\n              return _context14.abrupt(\"return\", data);\n            case 8:\n            case \"end\":\n              return _context14.stop();\n          }\n        }, _callee14);\n      }));\n      return function chatGroupInfo(_x15) {\n        return _ref16.apply(this, arguments);\n      };\n    }();\n    var handleChatGroupAnnouncement = function handleChatGroupAnnouncement() {\n      var groupAnnouncement = JSON.parse(localStorage.getItem('isChatGroupAnnouncement')) || {};\n      var newGroupAnnouncement = _objectSpread({}, groupAnnouncement);\n      newGroupAnnouncement[chatInfo.value.targetId] = {\n        show: false,\n        callBoard: chatGroupAnnouncement.value\n      };\n      localStorage.setItem('isChatGroupAnnouncement', JSON.stringify(newGroupAnnouncement));\n      chatGroupAnnouncement.value = '';\n      isChatGroupAnnouncement.value = false;\n    };\n    var handleAreArraysEqual = function handleAreArraysEqual(arr1, arr2) {\n      // 如果数组长度不相等，直接返回 false\n      if (arr1.length !== arr2.length) return false;\n      // 将两个数组排序后比较\n      var sortedArr1 = _toConsumableArray(arr1).sort();\n      var sortedArr2 = _toConsumableArray(arr2).sort();\n      // 遍历比较每个元素\n      for (var i = 0; i < sortedArr1.length; i++) {\n        if (sortedArr1[i] !== sortedArr2[i]) {\n          return false;\n        }\n      }\n      return true;\n    };\n    var handleUser = /*#__PURE__*/function () {\n      var _ref17 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee15() {\n        var newUserObj, newGroupInfo, isGroupUser, isChatGroup, index, item, _chatInfo$value, _chatInfo$value2;\n        return _regeneratorRuntime().wrap(function _callee15$(_context15) {\n          while (1) switch (_context15.prev = _context15.next) {\n            case 0:\n              newUserObj = {};\n              if (!(chatInfo.value.type === 3)) {\n                _context15.next = 16;\n                break;\n              }\n              _context15.next = 4;\n              return chatGroupInfo(chatInfo.value.targetId);\n            case 4:\n              newGroupInfo = _context15.sent;\n              if (!newGroupInfo.id) {\n                ElMessageBox.alert('当前群组已解散！', '提示', {\n                  confirmButtonText: '确定',\n                  callback: function callback() {\n                    scrollHeight.value = 0;\n                    chatInfoMessages.value = [];\n                    chatInfoMessagesData.value = [];\n                    chatMenuItem.value = chatInfo.value;\n                    handleDelChat();\n                  }\n                });\n              }\n              isGroupUser = handleAreArraysEqual(newGroupInfo.memberUserIds, groupUser.value.map(function (v) {\n                return v.accountId;\n              }));\n              if (isGroupUser) {\n                _context15.next = 11;\n                break;\n              }\n              _context15.next = 10;\n              return chatGroupMemberList(chatInfo.value.targetId.slice(appOnlyHeader.value.length));\n            case 10:\n              groupUser.value = _context15.sent;\n            case 11:\n              isChatGroup = false;\n              for (index = 0; index < groupUser.value.length; index++) {\n                item = groupUser.value[index];\n                if (item.accountId === user.value.accountId) isChatGroup = true;\n                newUserObj[appOnlyHeader.value + item.accountId] = {\n                  uid: appOnlyHeader.value + item.accountId,\n                  id: item.accountId,\n                  name: item.userName,\n                  img: item.photo || item.headImg,\n                  userInfo: {\n                    userId: item.id,\n                    userName: item.userName,\n                    photo: item.photo,\n                    headImg: item.headImg\n                  }\n                };\n              }\n              isChat.value = newGroupInfo.id ? isChatGroup : true;\n              _context15.next = 20;\n              break;\n            case 16:\n              isChat.value = true;\n              groupUser.value = [];\n              newUserObj[appOnlyHeader.value + user.value.accountId] = {\n                uid: appOnlyHeader.value + user.value.accountId,\n                id: user.value.accountId,\n                name: user.value.userName,\n                img: user.value.photo || user.value.headImg,\n                userInfo: {\n                  userId: user.value.id,\n                  userName: user.value.userName,\n                  photo: user.value.photo,\n                  headImg: user.value.headImg\n                }\n              };\n              newUserObj[(_chatInfo$value = chatInfo.value) === null || _chatInfo$value === void 0 ? void 0 : _chatInfo$value.chatObjectInfo.uid] = (_chatInfo$value2 = chatInfo.value) === null || _chatInfo$value2 === void 0 ? void 0 : _chatInfo$value2.chatObjectInfo;\n            case 20:\n              chatInfoUser.value = newUserObj;\n            case 21:\n            case \"end\":\n              return _context15.stop();\n          }\n        }, _callee15);\n      }));\n      return function handleUser() {\n        return _ref17.apply(this, arguments);\n      };\n    }();\n    var handleMessages = /*#__PURE__*/function () {\n      var _ref18 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee16(data, type) {\n        var _yield$handleHistoryM, newMessages, withdrawId;\n        return _regeneratorRuntime().wrap(function _callee16$(_context16) {\n          while (1) switch (_context16.prev = _context16.next) {\n            case 0:\n              _context16.next = 2;\n              return handleHistoryMessages(data, chatInfoUser.value);\n            case 2:\n              _yield$handleHistoryM = _context16.sent;\n              newMessages = _yield$handleHistoryM.newMessages;\n              withdrawId = _yield$handleHistoryM.withdrawId;\n              console.log(newMessages);\n              if (type) {\n                chatInfoMessages.value = [].concat(_toConsumableArray(chatInfoMessages.value), _toConsumableArray(newMessages)).filter(function (v) {\n                  return !(withdrawId !== null && withdrawId !== void 0 && withdrawId.includes(v.uid));\n                });\n                handleRenderMessages(withdrawId);\n              } else {\n                chatInfoMessages.value = [].concat(_toConsumableArray(newMessages), _toConsumableArray(chatInfoMessages.value)).filter(function (v) {\n                  return !(withdrawId !== null && withdrawId !== void 0 && withdrawId.includes(v.uid));\n                });\n                handleRenderMessages(withdrawId);\n              }\n            case 7:\n            case \"end\":\n              return _context16.stop();\n          }\n        }, _callee16);\n      }));\n      return function handleMessages(_x16, _x17) {\n        return _ref18.apply(this, arguments);\n      };\n    }();\n    var handleRenderMessages = /*#__PURE__*/function () {\n      var _ref19 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee17(withdrawId, type) {\n        var timeData, newMessages, newChatInfoImg, newMessagesId, isGroupAnnouncement, index, _item$content, item, _timeData;\n        return _regeneratorRuntime().wrap(function _callee17$(_context17) {\n          while (1) switch (_context17.prev = _context17.next) {\n            case 0:\n              timeData = [];\n              newMessages = [];\n              newChatInfoImg = [];\n              newMessagesId = [];\n              isGroupAnnouncement = false;\n              for (index = 0; index < chatInfoMessages.value.length; index++) {\n                item = chatInfoMessages.value[index];\n                if ((_item$content = item.content) !== null && _item$content !== void 0 && (_item$content = _item$content.content) !== null && _item$content !== void 0 && _item$content.includes('群公告：\\n')) isGroupAnnouncement = true;\n                if (!(newMessagesId !== null && newMessagesId !== void 0 && newMessagesId.includes(item.uid)) && !(withdrawId !== null && withdrawId !== void 0 && withdrawId.includes(item.uid))) {\n                  newMessagesId.push(item.uid);\n                  if (!((_timeData = timeData) !== null && _timeData !== void 0 && _timeData.includes(format(item.sentTime)))) {\n                    timeData = [format(item.sentTime), format(item.sentTime + 60000)];\n                    newMessages.push({\n                      id: item.sentTime,\n                      type: 'time',\n                      className: 'GlobalChatMessagesTime',\n                      content: format(item.sentTime)\n                    });\n                  }\n                  if (item.type === 'RC:ImgMsg') {\n                    newMessages.push(_objectSpread(_objectSpread({}, item), {}, {\n                      imgIndex: newChatInfoImg.length\n                    }));\n                    newChatInfoImg.push(item.content.imageUri);\n                  } else if (item.type === 'RC:HQVCMsg') {\n                    newMessages.push(_objectSpread(_objectSpread({}, item), {}, {\n                      audio: new Audio(item.content.remoteUrl)\n                    }));\n                  } else if (item.type === 'RC:ImgTextMsg') {\n                    newMessages.push(item);\n                    electronFile.value.push(item === null || item === void 0 ? void 0 : item.uid);\n                  } else {\n                    newMessages.push(item);\n                  }\n                }\n              }\n              if (isGroupAnnouncement) chatGroupInfo(chatInfo.value.targetId);\n              chatInfoImg.value = newChatInfoImg;\n              chatInfoMessagesData.value = newMessages;\n              if (type) {\n                nextTick(function () {\n                  scrollRef.value.wrapRef.scrollTop = scrollTopNum.value;\n                });\n              } else {\n                nextTick(function () {\n                  scrollShow.value ? scrollDown() : scrollElHeight();\n                });\n              }\n            case 10:\n            case \"end\":\n              return _context17.stop();\n          }\n        }, _callee17);\n      }));\n      return function handleRenderMessages(_x18, _x19) {\n        return _ref19.apply(this, arguments);\n      };\n    }();\n    var handleChatMenu = function handleChatMenu(e, item) {\n      chatMenuItem.value = item;\n      chatMenuTop.value = e.pageY;\n      chatMenuLeft.value = e.pageX;\n      chatMenuShow.value = true;\n      isChatMenuShow.value = true;\n    };\n    var handleMessagesMenu = function handleMessagesMenu(e, item) {\n      var _item$content2;\n      var selection = window.getSelection();\n      var copyContent = selection.toString() || (item === null || item === void 0 || (_item$content2 = item.content) === null || _item$content2 === void 0 ? void 0 : _item$content2.content);\n      var now = new Date().getTime();\n      var timeDifference = now - item.sentTime;\n      var isWithdraw = timeDifference < 180000;\n      messagesMenuItem.value = _objectSpread(_objectSpread({}, item), {}, {\n        copyContent,\n        isWithdraw\n      });\n      messagesMenuTop.value = e.pageY;\n      messagesMenuLeft.value = e.pageX;\n      messagesMenuShow.value = true;\n      isMessagesMenuShow.value = true;\n    };\n    var handleAudio = function handleAudio(data) {\n      if (chatInfoAudio.value.id) {\n        if (chatInfoAudio.value.id === data.id) {\n          chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime;\n          handlePauseAudio();\n        } else {\n          chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime;\n          handlePauseAudio();\n          chatInfoAudio.value = {\n            id: data.id,\n            audio: data.audio\n          };\n          chatInfoAudio.value.audio.currentTime = 0;\n          handlePlayAudio();\n        }\n      } else {\n        chatInfoAudio.value = {\n          id: data.id,\n          audio: data.audio\n        };\n        chatInfoAudio.value.audio.currentTime = 0;\n        handlePlayAudio();\n      }\n    };\n    var handleGoAudio = function handleGoAudio(data) {\n      if (chatInfoAudio.value.id) {\n        chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime;\n        handlePauseAudio();\n      }\n      chatInfoAudio.value = {\n        id: data.id,\n        audio: data.audio\n      };\n      chatInfoAudio.value.audio.currentTime = chatInfoAudioObj.value[chatInfoAudio.value.id] || 0;\n      handlePlayAudio();\n    };\n    var handlePlayAudio = function handlePlayAudio() {\n      chatInfoAudio.value.audio.play();\n      chatInfoAudio.value.audio.addEventListener('ended', function () {\n        chatInfoAudioObj.value[chatInfoAudio.value.id] = 0;\n        chatInfoAudio.value = {};\n      });\n    };\n    var handlePauseAudio = function handlePauseAudio() {\n      chatInfoAudio.value.audio.pause();\n      chatInfoAudio.value = {};\n    };\n    var handleImgLoad = function handleImgLoad() {\n      if (scrollShow.value) scrollDown();\n    };\n    var handleDrop = function handleDrop(event) {\n      event.preventDefault();\n      var files = event.dataTransfer.files;\n      if (files.length) {\n        var mewFileList = [];\n        for (var index = 0; index < files.length; index++) {\n          var file = files.item(index);\n          var extName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();\n          mewFileList.push({\n            id: guid(),\n            name: file.name,\n            extName,\n            size: file.size,\n            file: file\n          });\n        }\n        fileList.value = mewFileList;\n        fileShow.value = true;\n      }\n    };\n    var fileCallback = function fileCallback(data) {\n      if (data) {\n        for (var index = 0; index < data.length; index++) {\n          var item = data[index];\n          fileUpload(item);\n        }\n      }\n      fileList.value = [];\n      fileShow.value = false;\n    };\n    var handlePasteImg = function handlePasteImg(file) {\n      fileImg.value = file;\n      imgShow.value = true;\n    };\n    var imgCallback = function imgCallback(type) {\n      if (type) fileUpload(fileImg.value);\n      fileImg.value = {};\n      imgShow.value = false;\n    };\n    var fileUpload = /*#__PURE__*/function () {\n      var _ref20 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee18(file) {\n        var _ref21;\n        var params, _yield$api$globalUplo, data;\n        return _regeneratorRuntime().wrap(function _callee18$(_context18) {\n          while (1) switch (_context18.prev = _context18.next) {\n            case 0:\n              params = new FormData();\n              params.append('file', file.file);\n              params.append('isKeepAlive', true);\n              params.append('uid', file.id || file.uid || guid());\n              _context18.next = 6;\n              return api.globalUpload(params, function () {});\n            case 6:\n              _yield$api$globalUplo = _context18.sent;\n              data = _yield$api$globalUplo.data;\n              if ((_ref21 = ['png', 'jpg', 'jpeg']) !== null && _ref21 !== void 0 && _ref21.includes(data.extName)) {\n                handleSendImgMessage(api.openImgURL(data.newFileName));\n              } else {\n                localStorage.setItem(data.id, JSON.stringify(data));\n                handleSendFileMessage(data.id);\n                if (chatInfo.value.type === 3) submitChatGroupFile(data.id);\n              }\n            case 9:\n            case \"end\":\n              return _context18.stop();\n          }\n        }, _callee18);\n      }));\n      return function fileUpload(_x20) {\n        return _ref20.apply(this, arguments);\n      };\n    }();\n    var submitChatGroupFile = /*#__PURE__*/function () {\n      var _ref22 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee19(fileId) {\n        return _regeneratorRuntime().wrap(function _callee19$(_context19) {\n          while (1) switch (_context19.prev = _context19.next) {\n            case 0:\n              _context19.next = 2;\n              return api.chatGroupFileAdd({\n                form: {\n                  chatGroupId: chatInfo.value.targetId.slice(appOnlyHeader.value.length),\n                  fileId\n                }\n              });\n            case 2:\n            case \"end\":\n              return _context19.stop();\n          }\n        }, _callee19);\n      }));\n      return function submitChatGroupFile(_x21) {\n        return _ref22.apply(this, arguments);\n      };\n    }();\n    var isMacText = function isMacText() {\n      var userAgent = navigator.userAgent.toLowerCase();\n      return (userAgent === null || userAgent === void 0 ? void 0 : userAgent.includes('macintosh')) || (userAgent === null || userAgent === void 0 ? void 0 : userAgent.includes('mac os x'));\n    };\n    var handleSetting = function handleSetting() {\n      if (!isChat.value) return;\n      settingShow.value = !settingShow.value;\n    };\n    var handleKeyCode = function handleKeyCode(data) {\n      var _data$mentions;\n      var userIdData = data === null || data === void 0 || (_data$mentions = data.mentions) === null || _data$mentions === void 0 ? void 0 : _data$mentions.map(function (v) {\n        return appOnlyHeader.value + v.userInfo.accountId;\n      });\n      var mentionData = userIdData.length ? {\n        mentionedContent: '',\n        type: 2,\n        userIdList: Array.from(new Set(userIdData))\n      } : {};\n      handleSendTextMessage(data.content, mentionData);\n    };\n    var handleSendTextMessage = function handleSendTextMessage(contentText) {\n      var mentionData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (!contentText.replace(/^\\s+|\\s+$/g, '')) return;\n      var message = new RongIMLib.TextMessage({\n        content: contentText,\n        mentionedInfo: mentionData\n      });\n      var conversation = {\n        conversationType: chatInfo.value.type,\n        targetId: chatInfo.value.targetId\n      };\n      var options = {\n        onSendBefore: function onSendBefore(message) {\n          scrollShow.value = true;\n          handleMessages([_objectSpread(_objectSpread({}, message), {}, {\n            sentTime: Date.parse(new Date())\n          })], true);\n        }\n      };\n      handleSendMessage(conversation, message, options, function (code, msg, data) {\n        if (code === 0) {\n          var newChatInfoMessages = [];\n          for (var index = 0; index < chatInfoMessages.value.length; index++) {\n            var item = chatInfoMessages.value[index];\n            newChatInfoMessages.push(item.id === data.messageId ? _objectSpread(_objectSpread({}, item), {}, {\n              uid: data.messageUId,\n              sentTime: data.sentTime\n            }) : item);\n          }\n          chatInfoMessages.value = newChatInfoMessages;\n          handleRenderMessages([], true);\n          console.log('消息发送成功：', data);\n        } else {\n          console.log('消息发送失败：', code, msg);\n        }\n      });\n    };\n    var handleSendImgMessage = function handleSendImgMessage(url) {\n      var message = new RongIMLib.ImageMessage({\n        content: '',\n        imageUri: url\n      });\n      var conversation = {\n        conversationType: chatInfo.value.type,\n        targetId: chatInfo.value.targetId\n      };\n      var options = {\n        onSendBefore: function onSendBefore(message) {\n          scrollShow.value = true;\n          handleMessages([_objectSpread(_objectSpread({}, message), {}, {\n            sentTime: Date.parse(new Date())\n          })], true);\n        }\n      };\n      handleSendMessage(conversation, message, options, function (code, msg, data) {\n        if (code === 0) {\n          var newChatInfoMessages = [];\n          for (var index = 0; index < chatInfoMessages.value.length; index++) {\n            var item = chatInfoMessages.value[index];\n            newChatInfoMessages.push(item.id === data.messageId ? _objectSpread(_objectSpread({}, item), {}, {\n              uid: data.messageUId,\n              sentTime: data.sentTime\n            }) : item);\n          }\n          chatInfoMessages.value = newChatInfoMessages;\n          handleRenderMessages([], true);\n          console.log('消息发送成功：', data);\n        } else {\n          console.log('消息发送失败：', code, msg);\n        }\n      });\n    };\n    var handleSendFileMessage = function handleSendFileMessage(fileId) {\n      var PersonMessage = RongIMLib.registerMessageType('RC:ImgTextMsg', true, true, [], false);\n      var message = new PersonMessage({\n        content: `[文件],${fileId}`,\n        title: '[文件]'\n      });\n      var conversation = {\n        conversationType: chatInfo.value.type,\n        targetId: chatInfo.value.targetId\n      };\n      var options = {\n        onSendBefore: function onSendBefore(message) {\n          scrollShow.value = true;\n          handleMessages([_objectSpread(_objectSpread({}, message), {}, {\n            sentTime: Date.parse(new Date())\n          })], true);\n        }\n      };\n      handleSendMessage(conversation, message, options, function (code, msg, data) {\n        if (code === 0) {\n          var newChatInfoMessages = [];\n          for (var index = 0; index < chatInfoMessages.value.length; index++) {\n            var item = chatInfoMessages.value[index];\n            newChatInfoMessages.push(item.id === data.messageId ? _objectSpread(_objectSpread({}, item), {}, {\n              uid: data.messageUId,\n              sentTime: data.sentTime\n            }) : item);\n          }\n          chatInfoMessages.value = newChatInfoMessages;\n          handleRenderMessages([], true);\n          console.log('消息发送成功：', data);\n        } else {\n          console.log('消息发送失败：', code, msg);\n        }\n      });\n    };\n    var handleSendCustomMessage = function handleSendCustomMessage(params) {\n      var PersonMessage = RongIMLib.registerMessageType('RC:CmdNtf', true, true, [], false);\n      var message = new PersonMessage(params);\n      var conversation = {\n        conversationType: chatInfo.value.type,\n        targetId: chatInfo.value.targetId\n      };\n      var options = {\n        onSendBefore: function onSendBefore(message) {\n          scrollShow.value = true;\n          handleMessages([_objectSpread(_objectSpread({}, message), {}, {\n            sentTime: Date.parse(new Date())\n          })], true);\n        }\n      };\n      handleSendMessage(conversation, message, options, function (code, msg, data) {\n        if (code === 0) {\n          var newChatInfoMessages = [];\n          for (var index = 0; index < chatInfoMessages.value.length; index++) {\n            var item = chatInfoMessages.value[index];\n            newChatInfoMessages.push(item.id === data.messageId ? _objectSpread(_objectSpread({}, item), {}, {\n              uid: data.messageUId,\n              sentTime: data.sentTime\n            }) : item);\n          }\n          chatInfoMessages.value = newChatInfoMessages;\n          handleRenderMessages([], true);\n          console.log('消息发送成功：', data);\n        } else {\n          console.log('消息发送失败：', code, msg);\n        }\n      });\n    };\n    var handleSendMessage = /*#__PURE__*/function () {\n      var _ref23 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee20(conversation, message, options, callback) {\n        var _yield$RongIMLib$send, code, msg, data;\n        return _regeneratorRuntime().wrap(function _callee20$(_context20) {\n          while (1) switch (_context20.prev = _context20.next) {\n            case 0:\n              _context20.next = 2;\n              return RongIMLib.sendMessage(conversation, message, options);\n            case 2:\n              _yield$RongIMLib$send = _context20.sent;\n              code = _yield$RongIMLib$send.code;\n              msg = _yield$RongIMLib$send.msg;\n              data = _yield$RongIMLib$send.data;\n              if (code === 0) handleRefresh();\n              callback(code, msg, data);\n            case 8:\n            case \"end\":\n              return _context20.stop();\n          }\n        }, _callee20);\n      }));\n      return function handleSendMessage(_x22, _x23, _x24, _x25) {\n        return _ref23.apply(this, arguments);\n      };\n    }();\n    var handleNotificationClick = /*#__PURE__*/function () {\n      var _ref24 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee21(notification) {\n        var _yield$RongIMLib$setC, code;\n        return _regeneratorRuntime().wrap(function _callee21$(_context21) {\n          while (1) switch (_context21.prev = _context21.next) {\n            case 0:\n              _context21.next = 2;\n              return RongIMLib.setConversationNotificationStatus({\n                conversationType: chatMenuItem.value.type,\n                targetId: chatMenuItem.value.id\n              }, notification);\n            case 2:\n              _yield$RongIMLib$setC = _context21.sent;\n              code = _yield$RongIMLib$setC.code;\n              if (!code) handleRefresh();\n            case 5:\n            case \"end\":\n              return _context21.stop();\n          }\n        }, _callee21);\n      }));\n      return function handleNotificationClick(_x26) {\n        return _ref24.apply(this, arguments);\n      };\n    }();\n    var handleIsTopClick = /*#__PURE__*/function () {\n      var _ref25 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee22(isTop) {\n        var _yield$RongIMLib$setC2, code;\n        return _regeneratorRuntime().wrap(function _callee22$(_context22) {\n          while (1) switch (_context22.prev = _context22.next) {\n            case 0:\n              _context22.next = 2;\n              return RongIMLib.setConversationToTop({\n                conversationType: chatMenuItem.value.type,\n                targetId: chatMenuItem.value.id\n              }, isTop);\n            case 2:\n              _yield$RongIMLib$setC2 = _context22.sent;\n              code = _yield$RongIMLib$setC2.code;\n              if (!code) handleRefresh();\n            case 5:\n            case \"end\":\n              return _context22.stop();\n          }\n        }, _callee22);\n      }));\n      return function handleIsTopClick(_x27) {\n        return _ref25.apply(this, arguments);\n      };\n    }();\n    var handleDelChat = /*#__PURE__*/function () {\n      var _ref26 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee23() {\n        var _yield$RongIMLib$remo, code, msg;\n        return _regeneratorRuntime().wrap(function _callee23$(_context23) {\n          while (1) switch (_context23.prev = _context23.next) {\n            case 0:\n              _context23.next = 2;\n              return RongIMLib.removeConversation({\n                conversationType: chatMenuItem.value.type,\n                targetId: chatMenuItem.value.id\n              });\n            case 2:\n              _yield$RongIMLib$remo = _context23.sent;\n              code = _yield$RongIMLib$remo.code;\n              msg = _yield$RongIMLib$remo.msg;\n              if (code === 0) {\n                console.log('消息删除成功');\n                handleRefresh('del', chatMenuItem.value);\n              } else {\n                console.log('消息删除失败：', code, msg);\n              }\n            case 6:\n            case \"end\":\n              return _context23.stop();\n          }\n        }, _callee23);\n      }));\n      return function handleDelChat() {\n        return _ref26.apply(this, arguments);\n      };\n    }();\n    var handleDelMessage = /*#__PURE__*/function () {\n      var _ref27 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee24() {\n        var conversation, messagesData, _yield$RongIMLib$dele, code, msg;\n        return _regeneratorRuntime().wrap(function _callee24$(_context24) {\n          while (1) switch (_context24.prev = _context24.next) {\n            case 0:\n              conversation = {\n                conversationType: chatInfo.value.type,\n                targetId: chatInfo.value.targetId\n              };\n              messagesData = [{\n                messageUId: messagesMenuItem.value.uid,\n                sentTime: messagesMenuItem.value.sentTime,\n                messageDirection: messagesMenuItem.value.direction\n              }];\n              _context24.next = 4;\n              return RongIMLib.deleteMessages(conversation, messagesData);\n            case 4:\n              _yield$RongIMLib$dele = _context24.sent;\n              code = _yield$RongIMLib$dele.code;\n              msg = _yield$RongIMLib$dele.msg;\n              if (code === 0) {\n                console.log('消息删除成功');\n                chatInfoMessages.value = chatInfoMessages.value.filter(function (item) {\n                  return item.uid !== messagesMenuItem.value.uid;\n                });\n                handleRenderMessages([], true);\n              } else {\n                console.log('消息删除失败：', code, msg);\n              }\n            case 8:\n            case \"end\":\n              return _context24.stop();\n          }\n        }, _callee24);\n      }));\n      return function handleDelMessage() {\n        return _ref27.apply(this, arguments);\n      };\n    }();\n    var handleWithdrawMessage = /*#__PURE__*/function () {\n      var _ref28 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee25() {\n        var conversation, messagesData, _yield$RongIMLib$reca, code, msg;\n        return _regeneratorRuntime().wrap(function _callee25$(_context25) {\n          while (1) switch (_context25.prev = _context25.next) {\n            case 0:\n              conversation = {\n                conversationType: chatInfo.value.type,\n                targetId: chatInfo.value.targetId\n              };\n              messagesData = {\n                messageUId: messagesMenuItem.value.uid,\n                sentTime: messagesMenuItem.value.sentTime,\n                disableNotification: true\n              };\n              _context25.next = 4;\n              return RongIMLib.recallMessage(conversation, messagesData);\n            case 4:\n              _yield$RongIMLib$reca = _context25.sent;\n              code = _yield$RongIMLib$reca.code;\n              msg = _yield$RongIMLib$reca.msg;\n              if (code === 0) {\n                handleChatClick(chatInfo.value, true);\n                console.log('消息撤回成功');\n              } else {\n                console.log('消息撤回失败：', code, msg);\n              }\n            case 8:\n            case \"end\":\n              return _context25.stop();\n          }\n        }, _callee25);\n      }));\n      return function handleWithdrawMessage() {\n        return _ref28.apply(this, arguments);\n      };\n    }();\n    var handleGroup = /*#__PURE__*/function () {\n      var _ref29 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee26(type, data, isOwner) {\n        var _data$chatObjectInfo, _data$chatObjectInfo2, _data$chatObjectInfo3, _data$chatObjectInfo4, _data$chatObjectInfo5, _data$chatObjectInfo6, _data$chatObjectInfo7;\n        var PersonMessage, message, conversation, options;\n        return _regeneratorRuntime().wrap(function _callee26$(_context26) {\n          while (1) switch (_context26.prev = _context26.next) {\n            case 0:\n              if (type === 'create') handleCreateGroup(data === null || data === void 0 || (_data$chatObjectInfo = data.chatObjectInfo) === null || _data$chatObjectInfo === void 0 ? void 0 : _data$chatObjectInfo.id);\n              if (type === 'add') handleGroupAddUser(data === null || data === void 0 || (_data$chatObjectInfo2 = data.chatObjectInfo) === null || _data$chatObjectInfo2 === void 0 ? void 0 : _data$chatObjectInfo2.id);\n              if (type === 'del') handleGroupDelUser(data === null || data === void 0 || (_data$chatObjectInfo3 = data.chatObjectInfo) === null || _data$chatObjectInfo3 === void 0 ? void 0 : _data$chatObjectInfo3.id);\n              if (type === 'name') handleGroupName(data === null || data === void 0 || (_data$chatObjectInfo4 = data.chatObjectInfo) === null || _data$chatObjectInfo4 === void 0 ? void 0 : _data$chatObjectInfo4.id);\n              if (type === 'qr') handleGroupQr(data === null || data === void 0 || (_data$chatObjectInfo5 = data.chatObjectInfo) === null || _data$chatObjectInfo5 === void 0 ? void 0 : _data$chatObjectInfo5.id);\n              if (type === 'announcement') handleGroupAnnouncement(data === null || data === void 0 || (_data$chatObjectInfo6 = data.chatObjectInfo) === null || _data$chatObjectInfo6 === void 0 ? void 0 : _data$chatObjectInfo6.id, isOwner);\n              if (type === 'transfer') handleGroupTransfer(data === null || data === void 0 || (_data$chatObjectInfo7 = data.chatObjectInfo) === null || _data$chatObjectInfo7 === void 0 ? void 0 : _data$chatObjectInfo7.id);\n              if (!(type === 'quit')) {\n                _context26.next = 15;\n                break;\n              }\n              PersonMessage = RongIMLib.registerMessageType('RC:CmdNtf', true, true, [], false);\n              message = new PersonMessage(data);\n              conversation = {\n                conversationType: chatInfo.value.type,\n                targetId: chatInfo.value.targetId\n              };\n              options = {\n                onSendBefore: function onSendBefore() {}\n              };\n              _context26.next = 14;\n              return RongIMLib.sendMessage(conversation, message, options);\n            case 14:\n              handleQuitDelChat();\n            case 15:\n            case \"end\":\n              return _context26.stop();\n          }\n        }, _callee26);\n      }));\n      return function handleGroup(_x28, _x29, _x30) {\n        return _ref29.apply(this, arguments);\n      };\n    }();\n    var handleQuitDelChat = /*#__PURE__*/function () {\n      var _ref30 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee27() {\n        var _yield$RongIMLib$remo2, code, msg;\n        return _regeneratorRuntime().wrap(function _callee27$(_context27) {\n          while (1) switch (_context27.prev = _context27.next) {\n            case 0:\n              _context27.next = 2;\n              return RongIMLib.removeConversation({\n                conversationType: chatInfo.value.type,\n                targetId: chatInfo.value.targetId\n              });\n            case 2:\n              _yield$RongIMLib$remo2 = _context27.sent;\n              code = _yield$RongIMLib$remo2.code;\n              msg = _yield$RongIMLib$remo2.msg;\n              if (code === 0) {\n                console.log('消息删除成功');\n                handleRefresh();\n              } else {\n                console.log('消息删除失败：', code, msg);\n              }\n            case 6:\n            case \"end\":\n              return _context27.stop();\n          }\n        }, _callee27);\n      }));\n      return function handleQuitDelChat() {\n        return _ref30.apply(this, arguments);\n      };\n    }();\n    var handleCreateGroup = function handleCreateGroup(id) {\n      var _user$value, _user$value2;\n      userId.value = id ? [(_user$value = user.value) === null || _user$value === void 0 ? void 0 : _user$value.accountId, id] : [(_user$value2 = user.value) === null || _user$value2 === void 0 ? void 0 : _user$value2.accountId];\n      createGroupShow.value = true;\n    };\n    var handleGroupAddUser = function handleGroupAddUser(id) {\n      infoId.value = id;\n      addShow.value = true;\n    };\n    var handleGroupDelUser = function handleGroupDelUser(id) {\n      infoId.value = id;\n      delShow.value = true;\n    };\n    var handleGroupName = function handleGroupName(id) {\n      infoId.value = id;\n      nameShow.value = true;\n    };\n    var handleGroupQr = function handleGroupQr(id) {\n      infoId.value = id;\n      qrShow.value = true;\n    };\n    var handleGroupAnnouncement = function handleGroupAnnouncement(id, isOwner) {\n      infoId.value = id;\n      isGroupOwner.value = isOwner;\n      announcementShow.value = true;\n    };\n    var handleGroupTransfer = function handleGroupTransfer(id) {\n      infoId.value = id;\n      transferShow.value = true;\n    };\n    var handleVote = function handleVote() {\n      infoId.value = chatInfo.value.targetId.slice(appOnlyHeader.value.length);\n      voteShow.value = true;\n    };\n    var handleVoteDetails = function handleVoteDetails(row) {\n      voteId.value = row.id;\n      voteDetailsShow.value = true;\n    };\n    var createCallback = function createCallback(data) {\n      settingShow.value = false;\n      if (data) emit('send', data);\n      createGroupShow.value = false;\n    };\n    var addCallback = /*#__PURE__*/function () {\n      var _ref31 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee28(type, data) {\n        return _regeneratorRuntime().wrap(function _callee28$(_context28) {\n          while (1) switch (_context28.prev = _context28.next) {\n            case 0:\n              settingShow.value = false;\n              if (!type) {\n                _context28.next = 5;\n                break;\n              }\n              _context28.next = 4;\n              return handleUser();\n            case 4:\n              handleSendCustomMessage(data);\n            case 5:\n              addShow.value = false;\n            case 6:\n            case \"end\":\n              return _context28.stop();\n          }\n        }, _callee28);\n      }));\n      return function addCallback(_x31, _x32) {\n        return _ref31.apply(this, arguments);\n      };\n    }();\n    var delCallback = /*#__PURE__*/function () {\n      var _ref32 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee29(type, data) {\n        return _regeneratorRuntime().wrap(function _callee29$(_context29) {\n          while (1) switch (_context29.prev = _context29.next) {\n            case 0:\n              settingShow.value = false;\n              if (!type) {\n                _context29.next = 5;\n                break;\n              }\n              _context29.next = 4;\n              return handleUser();\n            case 4:\n              handleSendCustomMessage(data);\n            case 5:\n              delShow.value = false;\n            case 6:\n            case \"end\":\n              return _context29.stop();\n          }\n        }, _callee29);\n      }));\n      return function delCallback(_x33, _x34) {\n        return _ref32.apply(this, arguments);\n      };\n    }();\n    var nameCallback = /*#__PURE__*/function () {\n      var _ref33 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee30(type, data) {\n        return _regeneratorRuntime().wrap(function _callee30$(_context30) {\n          while (1) switch (_context30.prev = _context30.next) {\n            case 0:\n              settingShow.value = false;\n              if (!type) {\n                _context30.next = 6;\n                break;\n              }\n              _context30.next = 4;\n              return handleUser();\n            case 4:\n              handleTime();\n              handleSendCustomMessage(data);\n            case 6:\n              nameShow.value = false;\n            case 7:\n            case \"end\":\n              return _context30.stop();\n          }\n        }, _callee30);\n      }));\n      return function nameCallback(_x35, _x36) {\n        return _ref33.apply(this, arguments);\n      };\n    }();\n    var announcementCallback = /*#__PURE__*/function () {\n      var _ref34 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee31(type, data) {\n        return _regeneratorRuntime().wrap(function _callee31$(_context31) {\n          while (1) switch (_context31.prev = _context31.next) {\n            case 0:\n              settingShow.value = false;\n              if (type) {\n                handleSendTextMessage(data);\n              }\n              announcementShow.value = false;\n            case 3:\n            case \"end\":\n              return _context31.stop();\n          }\n        }, _callee31);\n      }));\n      return function announcementCallback(_x37, _x38) {\n        return _ref34.apply(this, arguments);\n      };\n    }();\n    var transferCallback = /*#__PURE__*/function () {\n      var _ref35 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee32(type, data) {\n        return _regeneratorRuntime().wrap(function _callee32$(_context32) {\n          while (1) switch (_context32.prev = _context32.next) {\n            case 0:\n              settingShow.value = false;\n              if (!type) {\n                _context32.next = 5;\n                break;\n              }\n              _context32.next = 4;\n              return handleUser();\n            case 4:\n              handleSendCustomMessage(data);\n            case 5:\n              transferShow.value = false;\n            case 6:\n            case \"end\":\n              return _context32.stop();\n          }\n        }, _callee32);\n      }));\n      return function transferCallback(_x39, _x40) {\n        return _ref35.apply(this, arguments);\n      };\n    }();\n    var voteCallback = function voteCallback() {\n      createVoteShow.value = true;\n    };\n    var handleVoteCallback = function handleVoteCallback() {\n      voteRefresh.value = guid();\n      createVoteShow.value = false;\n      voteDetailsShow.value = false;\n    };\n    var handleTime = function handleTime() {\n      emit('time');\n    };\n    var handleRefresh = function handleRefresh(type, data) {\n      emit('refresh', type, data);\n    };\n    onUnmounted(function () {\n      if (isElectron) {\n        var fileContent = JSON.stringify(electronRecordObj.value);\n        if (chatId.value) globalElectronSaveRecord(chatId.value, fileContent);\n      }\n    });\n    watch(function () {\n      return chatId.value;\n    }, function (newValue, oldValue) {\n      if (isElectron) {\n        var fileContent = JSON.stringify(electronRecordObj.value);\n        electronRecordObj.value = {};\n        electronRecordData.value = [];\n        if (oldValue) globalElectronSaveRecord(oldValue, fileContent);\n        if (newValue) globalElectronReadRecord(newValue);\n      }\n      if (chatId.value) {\n        for (var index = 0; index < props.chatList.length; index++) {\n          var item = props.chatList[index];\n          if (chatId.value === item.id) handleChatClick(item);\n        }\n      }\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return chatList.value;\n    }, function () {\n      if (chatId.value) {\n        var isShow = true;\n        for (var index = 0; index < props.chatList.length; index++) {\n          var item = props.chatList[index];\n          if (chatId.value === item.id) {\n            isShow = false;\n            chatInfo.value = item;\n          }\n        }\n        if (isShow) {\n          chatId.value = '';\n          chatInfo.value = {};\n        }\n      }\n    }, {\n      immediate: true\n    });\n    __expose({\n      getNewestMessages\n    });\n    var __returned__ = {\n      GlobalChatEditor,\n      ChatPopupWindow,\n      SettingPopupWindow,\n      ChatSendImg,\n      ChatSendFile,\n      GlobalCreateGroup,\n      GlobalGroupAddUser,\n      GlobalGroupDelUser,\n      GlobalGroupName,\n      GlobalGroupQr,\n      GlobalGroupAnnouncement,\n      GlobalGroupTransfer,\n      GlobalChatViewWindow,\n      GlobalGroupVote,\n      GlobalCreateVote,\n      GlobalVoteDetails,\n      props,\n      emit,\n      isMac,\n      isElectron,\n      keyword,\n      chatId,\n      chatType,\n      chatList,\n      isChat,\n      chatInfo,\n      groupUser,\n      chatInfoImg,\n      chatInfoAudio,\n      chatInfoAudioObj,\n      electronFile,\n      isElectronFile,\n      isElectronFileObj,\n      electronRecordObj,\n      electronRecordData,\n      chatInfoUser,\n      chatMenuTop,\n      chatMenuLeft,\n      chatMenuItem,\n      chatMenuShow,\n      isChatMenuShow,\n      messagesMenuTop,\n      messagesMenuLeft,\n      messagesMenuItem,\n      messagesMenuShow,\n      isMessagesMenuShow,\n      fileList,\n      fileShow,\n      fileImg,\n      imgShow,\n      userId,\n      createGroupShow,\n      infoId,\n      addShow,\n      delShow,\n      nameShow,\n      qrShow,\n      isGroupOwner,\n      announcementShow,\n      transferShow,\n      settingShow,\n      chatGroupAnnouncement,\n      isChatGroupAnnouncement,\n      voteId,\n      voteRefresh,\n      voteShow,\n      createVoteShow,\n      voteDetailsShow,\n      imgUrl,\n      guid,\n      scrollRef,\n      editorRef,\n      scrollHeight,\n      scrollTopNum,\n      scrollShow,\n      chatInfoMessages,\n      chatInfoMessagesData,\n      handleElClick,\n      handleElContextmenu,\n      handleCustom,\n      handleElectronFile,\n      onDownloadProgress,\n      globalElectronFileDownload,\n      handleElectronOpenFile,\n      handleOpenFolderSelectFile,\n      handleFolderSelectFile,\n      globalElectronSaveRecord,\n      globalElectronReadRecord,\n      handlePreview,\n      querySearch,\n      scrollDown,\n      scrollElHeight,\n      handleMessagesScroll,\n      handleClick,\n      handleChatClick,\n      handleClearAway,\n      clearAllMessagesUnreadStatus,\n      clearMessagesUnreadStatus,\n      getHistoryMessages,\n      getNewestMessages,\n      chatGroupInfo,\n      handleChatGroupAnnouncement,\n      handleAreArraysEqual,\n      handleUser,\n      handleMessages,\n      handleRenderMessages,\n      handleChatMenu,\n      handleMessagesMenu,\n      handleAudio,\n      handleGoAudio,\n      handlePlayAudio,\n      handlePauseAudio,\n      handleImgLoad,\n      handleDrop,\n      fileCallback,\n      handlePasteImg,\n      imgCallback,\n      fileUpload,\n      submitChatGroupFile,\n      isMacText,\n      handleSetting,\n      handleKeyCode,\n      handleSendTextMessage,\n      handleSendImgMessage,\n      handleSendFileMessage,\n      handleSendCustomMessage,\n      handleSendMessage,\n      handleNotificationClick,\n      handleIsTopClick,\n      handleDelChat,\n      handleDelMessage,\n      handleWithdrawMessage,\n      handleGroup,\n      handleQuitDelChat,\n      handleCreateGroup,\n      handleGroupAddUser,\n      handleGroupDelUser,\n      handleGroupName,\n      handleGroupQr,\n      handleGroupAnnouncement,\n      handleGroupTransfer,\n      handleVote,\n      handleVoteDetails,\n      createCallback,\n      addCallback,\n      delCallback,\n      nameCallback,\n      announcementCallback,\n      transferCallback,\n      voteCallback,\n      handleVoteCallback,\n      handleTime,\n      handleRefresh,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      onMounted,\n      onUnmounted,\n      watch,\n      nextTick,\n      defineAsyncComponent,\n      get RongIMLib() {\n        return RongIMLib;\n      },\n      get format() {\n        return format;\n      },\n      get utils() {\n        return utils;\n      },\n      get size2Str() {\n        return size2Str;\n      },\n      get globalFileLocation() {\n        return globalFileLocation;\n      },\n      get user() {\n        return user;\n      },\n      get appOnlyHeader() {\n        return appOnlyHeader;\n      },\n      get chatGroupMemberList() {\n        return chatGroupMemberList;\n      },\n      get fileIcon() {\n        return fileIcon;\n      },\n      get handleTimeFormat() {\n        return handleTimeFormat;\n      },\n      get handleHistoryMessages() {\n        return handleHistoryMessages;\n      },\n      get getUniqueFileName() {\n        return getUniqueFileName;\n      },\n      get notificationIcon() {\n        return notificationIcon;\n      },\n      get initGroupChatIcon() {\n        return initGroupChatIcon;\n      },\n      get announcementIcon() {\n        return announcementIcon;\n      },\n      get clearAwayIcon() {\n        return clearAwayIcon;\n      },\n      get voteListIcon() {\n        return voteListIcon;\n      },\n      get voteBgIcon() {\n        return voteBgIcon;\n      },\n      get Search() {\n        return Search;\n      },\n      get Picture() {\n        return Picture;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "computed", "onMounted", "onUnmounted", "watch", "nextTick", "defineAsyncComponent", "RongIMLib", "format", "utils", "size2Str", "globalFileLocation", "user", "appOnly<PERSON>eader", "chatGroupMemberList", "fileIcon", "handleTimeFormat", "handleHistoryMessages", "getUniqueFileName", "notificationIcon", "initGroupChatIcon", "announcementIcon", "clearAwayIcon", "voteListIcon", "voteBgIcon", "Search", "Picture", "ElMessage", "ElMessageBox", "__default__", "GlobalChatEditor", "ChatPopupWindow", "SettingPopupWindow", "ChatSendImg", "ChatSendFile", "GlobalCreateGroup", "GlobalGroupAddUser", "GlobalGroupDelUser", "GlobalGroupName", "GlobalGroupQr", "GlobalGroupAnnouncement", "GlobalGroupTransfer", "GlobalChatViewWindow", "GlobalGroupVote", "GlobalCreateVote", "GlobalVoteDetails", "props", "__props", "emit", "__emit", "isMac", "_window$electron", "window", "electron", "isElectron", "keyword", "chatId", "get", "modelValue", "set", "chatType", "chatList", "isChat", "chatInfo", "groupUser", "chatInfoImg", "chatInfoAudio", "chatInfoAudioObj", "electronFile", "isElectronFile", "isElectronFileObj", "electronRecordObj", "electronRecordData", "chatInfoUser", "chatMenuTop", "chatMenuLeft", "chatMenuItem", "chatMenuShow", "isChatMenuShow", "messagesMenuTop", "messagesMenuLeft", "messagesMenuItem", "messagesMenuShow", "isMessagesMenuShow", "fileList", "fileShow", "fileImg", "imgShow", "userId", "createGroupShow", "infoId", "addShow", "delShow", "nameShow", "qrShow", "isGroupOwner", "announcementShow", "transferShow", "settingShow", "chatGroupAnnouncement", "isChatGroupAnnouncement", "voteId", "voteRefresh", "voteShow", "createVoteShow", "voteDetailsShow", "imgUrl", "url", "fileURL", "defaultImgURL", "guid", "replace", "Math", "random", "toString", "scrollRef", "editor<PERSON><PERSON>", "scrollHeight", "scrollTopNum", "scrollShow", "chatInfoMessages", "chatInfoMessagesData", "handleElClick", "preventDefault", "handleElContextmenu", "handleCustom", "item", "customType", "handleElectronFile", "uid", "file", "handlePreview", "handleVoteDetails", "vote", "_ref2", "_callee", "_isElectronFile$value", "fileName", "fileFolderPath", "result", "_args", "_callee$", "_context", "undefined", "includes", "originalFileName", "accountId", "fileExists", "handleOpenFolderSelectFile", "handleElectronOpenFile", "globalElectronFileDownload", "_x", "_x2", "onDownloadProgress", "progressEvent", "id", "_progressEvent$event", "event", "lengthComputable", "progress", "loaded", "total", "toFixed", "parseInt", "_ref3", "_callee2", "res", "saveRes", "_args2", "_callee2$", "_context2", "filter", "saveFile", "message", "_x3", "_x4", "_x5", "_ref4", "_callee3", "openRes", "_callee3$", "_context3", "openFile", "_x6", "_ref5", "_callee4", "_callee4$", "_context4", "openFolderSelectFile", "_x7", "handleFolderSelectFile", "_ref6", "_callee5", "_messagesMenuItem$val", "_messagesMenuItem$val2", "_callee5$", "_context5", "globalElectronSaveRecord", "_ref7", "_callee6", "recordId", "data", "fileContent", "_callee6$", "_context6", "gm_encrypt", "saveRecordFile", "console", "log", "_x8", "_x9", "globalElectronReadRecord", "_ref8", "_callee7", "recordObj", "recordData", "key", "_callee7$", "_context7", "readRecordFile", "JSON", "parse", "gm_decrypt", "_x10", "row", "process", "env", "VUE_APP_NAME", "fileId", "fileType", "extName", "fileSize", "querySearch", "queryString", "cb", "results", "_v$chatObjectInfo", "chatObjectInfo", "toLowerCase", "scrollDown", "wrapRef", "scrollTop", "scrollElHeight", "handleMessagesScroll", "_ref9", "_chatInfoMessages$val", "getHistoryMessages", "sentTime", "handleClick", "_ref10", "_callee8", "_callee8$", "_context8", "_x11", "handleChatClick", "_ref11", "_callee9", "_editorRef$value", "_callee9$", "_context9", "clearMessage", "isTemporary", "clearMessagesUnreadStatus", "_x12", "_x13", "handleClearAway", "confirm", "confirmButtonText", "cancelButtonText", "clearAllMessagesUnreadStatus", "_ref12", "_callee10", "_yield$RongIMLib$clea", "code", "msg", "_callee10$", "_context10", "handleRefresh", "_ref13", "_callee11", "_yield$RongIMLib$clea2", "_callee11$", "_context11", "conversationType", "targetId", "_x14", "_ref14", "_callee12", "timestamp", "option", "conversation", "_yield$RongIMLib$getH", "_args12", "_callee12$", "_context12", "count", "order", "handleUser", "handleMessages", "list", "getNewestMessages", "_ref15", "_callee13", "_yield$RongIMLib$getH2", "_callee13$", "_context13", "chatGroupInfo", "_ref16", "_callee14", "_yield$api$chatGroupI", "groupAnnouncement", "groupAnnouncementItem", "_callee14$", "_context14", "detailId", "localStorage", "getItem", "show", "callBoard", "_x15", "handleChatGroupAnnouncement", "newGroupAnnouncement", "_objectSpread", "setItem", "stringify", "handleAreArraysEqual", "arr1", "arr2", "sortedArr1", "_toConsumableArray", "sort", "sortedArr2", "_ref17", "_callee15", "newUserObj", "newGroupInfo", "isGroupUser", "isChatGroup", "index", "_chatInfo$value", "_chatInfo$value2", "_callee15$", "_context15", "alert", "callback", "handleDelChat", "memberUserIds", "map", "userName", "img", "photo", "headImg", "userInfo", "_ref18", "_callee16", "_yield$handleHistoryM", "newMessages", "withdrawId", "_callee16$", "_context16", "concat", "handleRenderMessages", "_x16", "_x17", "_ref19", "_callee17", "timeData", "newChatInfoImg", "newMessagesId", "isGroupAnnouncement", "_item$content", "_timeData", "_callee17$", "_context17", "content", "className", "imgIndex", "imageUri", "audio", "Audio", "remoteUrl", "_x18", "_x19", "handleChatMenu", "pageY", "pageX", "handleMessagesMenu", "_item$content2", "selection", "getSelection", "copyContent", "now", "Date", "getTime", "timeDifference", "isWithdraw", "handleAudio", "currentTime", "handlePauseAudio", "handlePlayAudio", "handleGoAudio", "play", "addEventListener", "pause", "handleImgLoad", "handleDrop", "files", "dataTransfer", "mewFileList", "substring", "lastIndexOf", "size", "fileCallback", "fileUpload", "handlePasteImg", "imgCallback", "_ref20", "_callee18", "_ref21", "params", "_yield$api$globalUplo", "_callee18$", "_context18", "FormData", "append", "globalUpload", "handleSendImgMessage", "openImgURL", "newFileName", "handleSendFileMessage", "submitChatGroupFile", "_x20", "_ref22", "_callee19", "_callee19$", "_context19", "chatGroupFileAdd", "form", "chatGroupId", "_x21", "isMacText", "userAgent", "navigator", "handleSetting", "handleKeyCode", "_data$mentions", "userIdData", "mentions", "mentionData", "mentioned<PERSON><PERSON><PERSON>", "userIdList", "Array", "from", "Set", "handleSendTextMessage", "contentText", "TextMessage", "mentionedInfo", "options", "onSendBefore", "handleSendMessage", "newChatInfoMessages", "messageId", "messageUId", "ImageMessage", "PersonMessage", "registerMessageType", "title", "handleSendCustomMessage", "_ref23", "_callee20", "_yield$RongIMLib$send", "_callee20$", "_context20", "sendMessage", "_x22", "_x23", "_x24", "_x25", "handleNotificationClick", "_ref24", "_callee21", "notification", "_yield$RongIMLib$setC", "_callee21$", "_context21", "setConversationNotificationStatus", "_x26", "handleIsTopClick", "_ref25", "_callee22", "isTop", "_yield$RongIMLib$setC2", "_callee22$", "_context22", "setConversationToTop", "_x27", "_ref26", "_callee23", "_yield$RongIMLib$remo", "_callee23$", "_context23", "removeConversation", "handleDelMessage", "_ref27", "_callee24", "messagesData", "_yield$RongIMLib$dele", "_callee24$", "_context24", "messageDirection", "direction", "deleteMessages", "handleWithdrawMessage", "_ref28", "_callee25", "_yield$RongIMLib$reca", "_callee25$", "_context25", "disableNotification", "recallMessage", "handleGroup", "_ref29", "_callee26", "isOwner", "_data$chatObjectInfo", "_data$chatObjectInfo2", "_data$chatObjectInfo3", "_data$chatObjectInfo4", "_data$chatObjectInfo5", "_data$chatObjectInfo6", "_data$chatObjectInfo7", "_callee26$", "_context26", "handleCreateGroup", "handleGroupAddUser", "handleGroupDelUser", "handleGroupName", "handleGroupQr", "handleGroupAnnouncement", "handleGroupTransfer", "handleQuitDelChat", "_x28", "_x29", "_x30", "_ref30", "_callee27", "_yield$RongIMLib$remo2", "_callee27$", "_context27", "_user$value", "_user$value2", "handleVote", "createCallback", "addCallback", "_ref31", "_callee28", "_callee28$", "_context28", "_x31", "_x32", "del<PERSON><PERSON><PERSON>", "_ref32", "_callee29", "_callee29$", "_context29", "_x33", "_x34", "nameCallback", "_ref33", "_callee30", "_callee30$", "_context30", "handleTime", "_x35", "_x36", "announcementCallback", "_ref34", "_callee31", "_callee31$", "_context31", "_x37", "_x38", "transferCallback", "_ref35", "_callee32", "_callee32$", "_context32", "_x39", "_x40", "voteCallback", "handleVoteCallback", "newValue", "oldValue", "immediate", "isShow", "__expose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/components/GlobalChatView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatView\" :class=\"{ GlobalChatMacView: isMac }\" @click.prevent=\"handleElClick\"\r\n    @contextmenu.prevent=\"handleElContextmenu\">\r\n    <div class=\"GlobalChatViewList forbidSelect\">\r\n      <div class=\"GlobalChatViewListHead\">\r\n        <el-autocomplete v-model=\"keyword\" :prefix-icon=\"Search\" :fetch-suggestions=\"querySearch\" placeholder=\"搜索\"\r\n          popper-class=\"GlobalChatViewAutocomplete\" clearable @select=\"handleClick\">\r\n          <template #default=\"{ item }\">\r\n            <div class=\"GlobalChatViewMessagesItem forbidSelect\">\r\n              <el-badge :value=\"item.count\" :hidden=\"!item.count\" :is-dot=\"item.isNotInform === 1\">\r\n                <el-image :src=\"imgUrl(item.chatObjectInfo?.img)\" fit=\"cover\" draggable=\"false\" />\r\n              </el-badge>\r\n              <div class=\"GlobalChatViewMessagesInfo\">\r\n                <div class=\"GlobalChatViewMessagesName\">\r\n                  <div class=\"ellipsis\">{{ item.chatObjectInfo?.name }}</div>\r\n                  <span>{{ handleTimeFormat(item?.sentTime) }}</span>\r\n                </div>\r\n                <div class=\"GlobalChatViewNotInform\" v-html=\"notificationIcon\" v-if=\"item.isNotInform === 1\"></div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-if=\"item.messageType === 'RC:ImgTextMsg'\">\r\n                  {{ item?.content?.title }}\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:ImgMsg'\">\r\n                  [图片]\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:HQVCMsg'\">\r\n                  [语音] {{ item?.content?.duration }}\"\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:CmdNtf'\">\r\n                  {{ item?.content?.name }}\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:LBSMsg'\">\r\n                  [不支持的消息，请在移动端进行查看]\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:RcCmd'\">\r\n                  {{ item.revocationMessage }}\r\n                </div>\r\n                <div class=\"GlobalChatViewMessagesText ellipsis\" v-else>{{ item?.content?.content }}</div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-autocomplete>\r\n        <div class=\"GlobalChatViewListHeadIcon\" v-html=\"initGroupChatIcon\" @click=\"handleCreateGroup('')\"></div>\r\n      </div>\r\n      <el-scrollbar class=\"GlobalChatViewMessagesList\">\r\n        <div :class=\"['GlobalChatViewMessagesItem', { 'is-top': item.isTop }, { 'is-active': item.id === chatId }]\"\r\n          v-for=\"item in chatList\" :key=\"item.id\" @click=\"handleClick(item)\"\r\n          @contextmenu.prevent=\"handleChatMenu($event, item)\">\r\n          <el-badge :value=\"item.count\" :hidden=\"!item.count\" :is-dot=\"item.isNotInform === 1\">\r\n            <el-image :src=\"imgUrl(item.chatObjectInfo?.img)\" fit=\"cover\" draggable=\"false\" />\r\n          </el-badge>\r\n          <div class=\"GlobalChatViewMessagesInfo\">\r\n            <div class=\"GlobalChatViewMessagesName\">\r\n              <div class=\"ellipsis\" v-if=\"!item.chatObjectInfo?.chatGroupType\">{{ item.chatObjectInfo?.name }}</div>\r\n              <div class=\"GlobalChatViewMessagesNameGroup ellipsis\"\r\n                v-if=\"item.type === 3 && item.chatObjectInfo.chatGroupType\">\r\n                {{ item.chatObjectInfo?.name }}\r\n                <span>{{ item.chatObjectInfo.chatGroupType }}</span>\r\n              </div>\r\n              <span>{{ handleTimeFormat(item?.sentTime) }}</span>\r\n            </div>\r\n            <div class=\"GlobalChatViewNotInform\" v-html=\"notificationIcon\" v-if=\"item.isNotInform === 1\"></div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-if=\"item.messageType === 'RC:ImgTextMsg'\">\r\n              {{ item?.content?.title }}\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:ImgMsg'\">[图片]</div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:HQVCMsg'\">\r\n              [语音] {{ item?.content?.duration }}\"\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:CmdNtf'\">\r\n              {{ item?.content?.name }}\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:LBSMsg'\">\r\n              [不支持的消息，请在移动端进行查看]\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else-if=\"item.messageType === 'RC:RcCmd'\">\r\n              {{ item.revocationMessage }}\r\n            </div>\r\n            <div class=\"GlobalChatViewMessagesText ellipsis\" v-else>{{ item?.content?.content }}</div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <div class=\"GlobalChatClearAway\" @click=\"handleClearAway\">\r\n        <div v-html=\"clearAwayIcon\"></div>\r\n        清除未读\r\n      </div>\r\n      <div class=\"GlobalChatViewMenu\" :style=\"{ left: chatMenuLeft + 'px', top: chatMenuTop + 'px' }\"\r\n        v-show=\"chatMenuShow\">\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleIsTopClick(true)\" v-if=\"!chatMenuItem.isTop\">置顶</div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleIsTopClick(false)\" v-if=\"chatMenuItem.isTop\">取消置顶</div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleNotificationClick(1)\" v-if=\"chatMenuItem.isNotInform === 2\">\r\n          消息免打扰\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleNotificationClick(2)\" v-if=\"chatMenuItem.isNotInform === 1\">\r\n          允许消息通知\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuLine\"></div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleDelChat\">删除</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"GlobalChatWindow\" @dragover.prevent @drop=\"handleDrop\" v-if=\"chatId\">\r\n      <div class=\"GlobalChatWindowTitle forbidSelect\">\r\n        <div class=\"ellipsis\" @click=\"handleSetting\">\r\n          {{ chatInfo.chatObjectInfo?.name }}\r\n          <span v-if=\"chatInfo.type === 3 && groupUser.length\">（{{ groupUser.length }}）</span>\r\n        </div>\r\n        <div class=\"GlobalChatWindowMore\" @click=\"handleSetting\">\r\n          <el-icon>\r\n            <MoreFilled />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n      <el-scrollbar ref=\"scrollRef\" always class=\"GlobalChatWindowScroll\" :class=\"{ GlobalChatWindowNoChat: !isChat }\"\r\n        @scroll=\"handleMessagesScroll\">\r\n        <div class=\"GlobalChatGroupAnnouncement\" v-if=\"isChatGroupAnnouncement\">\r\n          <div class=\"GlobalChatGroupAnnouncementTitle\">\r\n            <div>\r\n              <span v-html=\"announcementIcon\"></span>\r\n              群公告\r\n            </div>\r\n            <el-icon @click=\"handleChatGroupAnnouncement\">\r\n              <CircleCloseFilled />\r\n            </el-icon>\r\n          </div>\r\n          <div class=\"GlobalChatGroupAnnouncementContent\">{{ chatGroupAnnouncement }}</div>\r\n        </div>\r\n        <div class=\"GlobalChatWindowBody\">\r\n          <div v-for=\"item in chatInfoMessagesData\" :key=\"item.id || item.uid\" :class=\"item.className\">\r\n            <template v-if=\"item.type === 'time'\">{{ item?.content }}</template>\r\n            <template v-else-if=\"item.type === 'RC:CmdNtf'\">{{ item?.content?.name }}</template>\r\n            <template v-else-if=\"item.type === 'RC:RcCmd'\">\r\n              {{ item.chatObjectInfoType ? '你' : item.userName }}撤回了一条消息\r\n            </template>\r\n            <template v-else>\r\n              <div class=\"GlobalChatWindowUserImg\">\r\n                <el-image :src=\"imgUrl(item.chatObjectInfo?.img)\" fit=\"cover\" draggable=\"false\" />\r\n              </div>\r\n              <div class=\"GlobalChatMessagesInfo\">\r\n                <div class=\"GlobalChatMessagesName ellipsis\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\"\r\n                  v-if=\"!item.chatObjectInfoType\">\r\n                  {{ item.chatObjectInfo?.name }}\r\n                </div>\r\n                <div class=\"GlobalChatMessagesText\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\"\r\n                  v-if=\"item.type === 'RC:TxtMsg'\">\r\n                  <span></span>\r\n                  <div class=\"GlobalChatEmotion\" v-html=\"item?.content?.htmlContent\"></div>\r\n                </div>\r\n                <div class=\"GlobalChatMessagesText\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\"\r\n                  @click=\"handleAudio(item)\" v-if=\"item.type === 'RC:HQVCMsg'\">\r\n                  <span></span>\r\n                  <div :class=\"['GlobalChatVoice', { 'is-active': chatInfoAudio.id === item.id }]\"\r\n                    :style=\"{ width: `${88 + (item?.content?.duration || 0)}px` }\">\r\n                    {{ item?.content?.duration }}\"\r\n                  </div>\r\n                  <div class=\"GlobalChatVoiceContinue\" @click.stop=\"handleGoAudio(item)\"\r\n                    v-if=\"chatInfoAudio.id !== item.id && chatInfoAudioObj[item.id]\">\r\n                    继续播放\r\n                  </div>\r\n                </div>\r\n                <el-image :src=\"item?.content?.imageUri\" fit=\"cover\" :preview-src-list=\"chatInfoImg\"\r\n                  :initial-index=\"item.imgIndex\" @load=\"handleImgLoad\"\r\n                  @contextmenu.prevent=\"handleMessagesMenu($event, item)\" v-if=\"item.type === 'RC:ImgMsg'\">\r\n                  <template #error>\r\n                    <div class=\"GlobalChatMessagesImgSlot\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\">\r\n                      <el-icon>\r\n                        <Picture />\r\n                      </el-icon>\r\n                    </div>\r\n                  </template>\r\n                </el-image>\r\n                <div :class=\"['GlobalChatMessagesCustom', item.customType]\"\r\n                  @contextmenu.prevent=\"handleMessagesMenu($event, item)\" @click=\"handleCustom(item)\"\r\n                  v-if=\"['RC:ImgTextMsg', 'RC:LBSMsg']?.includes(item.type)\">\r\n                  <span></span>\r\n                  <template v-if=\"item.customType === 'file'\">\r\n                    <div class=\"GlobalChatMessagesFileDownload\" :style=\"{ width: isElectronFileObj[item.file.id] }\"\r\n                      v-if=\"isElectronFile?.includes(item.file.id)\"></div>\r\n                    <div class=\"globalFileIcon\" :class=\"fileIcon(item.file?.extName)\"></div>\r\n                    <div class=\"GlobalChatMessagesCustomName\">{{ item.file?.originalFileName || '未知文件' }}</div>\r\n                    <div class=\"GlobalChatMessagesCustomText\">\r\n                      {{ item.file?.fileSize ? size2Str(item.file.fileSize) : '0KB' }}\r\n                    </div>\r\n                  </template>\r\n                  <template v-if=\"item.customType === 'vote'\">\r\n                    <div class=\"GlobalChatMessagesVoteTitleBody\">\r\n                      <div class=\"GlobalChatMessagesVoteTitle\">\r\n                        <div class=\"GlobalChatMessagesVoteIcon\" v-html=\"voteListIcon\"></div>\r\n                        投票\r\n                      </div>\r\n                      <div class=\"GlobalChatMessagesVoteTime\">{{ format(item.vote?.createDate) }}</div>\r\n                    </div>\r\n                    <div class=\"GlobalChatMessagesVoteInfo\">\r\n                      <div class=\"GlobalChatMessagesVoteInfoIcon\" v-html=\"voteBgIcon\"></div>\r\n                      <div class=\"GlobalChatMessagesVoteName\">{{ item.vote?.topic }}</div>\r\n                    </div>\r\n                  </template>\r\n                  <template v-if=\"item.customType === 'unknown'\">\r\n                    <div class=\"GlobalChatMessagesCustomUnknown\">[不支持的消息，请在移动端进行查看]</div>\r\n                  </template>\r\n                </div>\r\n                <div class=\"GlobalChatMessagesFile\" @contextmenu.prevent=\"handleMessagesMenu($event, item)\"\r\n                  v-if=\"item.type === 'RC:FileMsg'\">\r\n                  <span></span>\r\n                  <div class=\"globalFileIcon\" :class=\"fileIcon(item?.content?.type)\"></div>\r\n                  <div class=\"GlobalChatMessagesFileName\">{{ item?.content?.name || '未知文件' }}</div>\r\n                  <div class=\"GlobalChatMessagesFileSize\">\r\n                    {{ item?.content?.size ? size2Str(item?.content?.size) : '0KB' }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <div class=\"GlobalChatViewMenu\" :style=\"{ left: messagesMenuLeft + 'px', top: messagesMenuTop + 'px' }\"\r\n        v-show=\"messagesMenuShow\">\r\n        <!-- <div class=\"GlobalChatViewMenuItem\" v-if=\"chatType?.includes(messagesMenuItem.type)\">转发</div> -->\r\n        <!-- <div class=\"GlobalChatViewMenuItem\">收藏</div> -->\r\n        <div class=\"GlobalChatViewMenuItem\" v-copy=\"messagesMenuItem?.copyContent\"\r\n          v-if=\"messagesMenuItem.type === 'RC:TxtMsg'\">\r\n          复制\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleFolderSelectFile\"\r\n          v-if=\"isElectron && messagesMenuItem?.customType === 'file'\">\r\n          {{ isMacText() ? '在 Finder 中显示' : '在资源管理器中显示' }}\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuLine\" v-if=\"chatType?.includes(messagesMenuItem.type)\"></div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleDelMessage\"\r\n          v-if=\"!messagesMenuItem.chatObjectInfoType || !messagesMenuItem.isWithdraw\">\r\n          删除\r\n        </div>\r\n        <div class=\"GlobalChatViewMenuItem\" @click=\"handleWithdrawMessage\"\r\n          v-if=\"messagesMenuItem.chatObjectInfoType && messagesMenuItem.isWithdraw\">\r\n          撤回\r\n        </div>\r\n      </div>\r\n      <GlobalChatEditor ref=\"editorRef\" :isVote=\"chatInfo.type === 3\" :userData=\"groupUser\" @handleFile=\"fileUpload\"\r\n        @handleVote=\"handleVote\" @handlePasteImg=\"handlePasteImg\" @handleSendMessage=\"handleKeyCode\" v-show=\"isChat\">\r\n      </GlobalChatEditor>\r\n      <div class=\"GlobalChatViewNoMessage\" v-show=\"!isChat\">\r\n        <el-icon>\r\n          <Warning />\r\n        </el-icon>\r\n        无法在已退出的群聊中接收和发送消息\r\n      </div>\r\n      <setting-popup-window v-model=\"settingShow\">\r\n        <GlobalChatViewWindow :chatInfo=\"chatInfo\" :groupUser=\"groupUser\" @refresh=\"handleRefresh\"\r\n          @callback=\"handleGroup\" />\r\n      </setting-popup-window>\r\n    </div>\r\n    <div class=\"GlobalChatViewDrag\" v-if=\"!chatId\"></div>\r\n    <chat-popup-window v-model=\"fileShow\">\r\n      <ChatSendFile :chatInfo=\"chatInfo\" :fileList=\"fileList\" @callback=\"fileCallback\"></ChatSendFile>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"imgShow\">\r\n      <ChatSendImg :chatInfo=\"chatInfo\" :fileImg=\"fileImg\" @callback=\"imgCallback\"></ChatSendImg>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"createGroupShow\">\r\n      <GlobalCreateGroup :userId=\"userId\" @callback=\"createCallback\"></GlobalCreateGroup>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"addShow\">\r\n      <GlobalGroupAddUser :infoId=\"infoId\" @callback=\"addCallback\"></GlobalGroupAddUser>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"delShow\">\r\n      <GlobalGroupDelUser :infoId=\"infoId\" @callback=\"delCallback\"></GlobalGroupDelUser>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"nameShow\" class=\"GlobalGroupNamePopupWindow\">\r\n      <GlobalGroupName :infoId=\"infoId\" @callback=\"nameCallback\"></GlobalGroupName>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"qrShow\" class=\"GlobalGroupQrPopupWindow\">\r\n      <GlobalGroupQr :infoId=\"infoId\"></GlobalGroupQr>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"announcementShow\" class=\"GlobalGroupAnnouncementPopupWindow\">\r\n      <GlobalGroupAnnouncement :infoId=\"infoId\" :isOwner=\"isGroupOwner\" @callback=\"announcementCallback\">\r\n      </GlobalGroupAnnouncement>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"transferShow\">\r\n      <GlobalGroupTransfer :infoId=\"infoId\" @callback=\"transferCallback\"></GlobalGroupTransfer>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"voteShow\" class=\"GlobalGroupVotePopupWindow\">\r\n      <GlobalGroupVote :id=\"infoId\" :refresh=\"voteRefresh\" @callback=\"voteCallback\"\r\n        @sendMessage=\"handleSendCustomMessage\">\r\n      </GlobalGroupVote>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"createVoteShow\">\r\n      <GlobalCreateVote :dataId=\"infoId\" @callback=\"handleVoteCallback\"></GlobalCreateVote>\r\n    </chat-popup-window>\r\n    <chat-popup-window v-model=\"voteDetailsShow\" class=\"GlobalGroupVotePopupWindow\">\r\n      <GlobalVoteDetails :id=\"voteId\" @callback=\"handleVoteCallback\" @sendMessage=\"handleSendCustomMessage\">\r\n      </GlobalVoteDetails>\r\n    </chat-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatView' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, onUnmounted, watch, nextTick, defineAsyncComponent } from 'vue'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { format } from 'common/js/time.js'\r\nimport utils, { size2Str } from 'common/js/utils.js'\r\nimport { globalFileLocation } from 'common/config/location'\r\nimport { user, appOnlyHeader } from 'common/js/system_var.js'\r\n// import { emotion } from '../js/emotion.js' emoteIcon, folderIcon, lineFeedIcon, voteIcon,\r\nimport { chatGroupMemberList } from '../js/ChatMethod.js'\r\nimport { fileIcon, handleTimeFormat, handleHistoryMessages, getUniqueFileName } from '../js/ChatViewMethod.js'\r\nimport {\r\n  notificationIcon,\r\n  initGroupChatIcon,\r\n  announcementIcon,\r\n  clearAwayIcon,\r\n  voteListIcon,\r\n  voteBgIcon\r\n} from '../js/icon.js'\r\nimport { Search, Picture } from '@element-plus/icons-vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nconst GlobalChatEditor = defineAsyncComponent(() => import('./GlobalChatEditor.vue'))\r\nconst ChatPopupWindow = defineAsyncComponent(() => import('./chat-popup-window/chat-popup-window.vue'))\r\nconst SettingPopupWindow = defineAsyncComponent(() => import('./setting-popup-window/setting-popup-window.vue'))\r\nconst ChatSendImg = defineAsyncComponent(() => import('./ChatSendImg/ChatSendImg.vue'))\r\nconst ChatSendFile = defineAsyncComponent(() => import('./ChatSendFile/ChatSendFile.vue'))\r\nconst GlobalCreateGroup = defineAsyncComponent(() => import('./GlobalCreateGroup/GlobalCreateGroup.vue'))\r\nconst GlobalGroupAddUser = defineAsyncComponent(() => import('./GlobalGroupAddUser/GlobalGroupAddUser.vue'))\r\nconst GlobalGroupDelUser = defineAsyncComponent(() => import('./GlobalGroupDelUser/GlobalGroupDelUser.vue'))\r\nconst GlobalGroupName = defineAsyncComponent(() => import('./GlobalGroupName/GlobalGroupName.vue'))\r\nconst GlobalGroupQr = defineAsyncComponent(() => import('./GlobalGroupQr/GlobalGroupQr.vue'))\r\nconst GlobalGroupAnnouncement = defineAsyncComponent(() =>\r\n  import('./GlobalGroupAnnouncement/GlobalGroupAnnouncement.vue')\r\n)\r\nconst GlobalGroupTransfer = defineAsyncComponent(() => import('./GlobalGroupTransfer/GlobalGroupTransfer.vue'))\r\nconst GlobalChatViewWindow = defineAsyncComponent(() => import('./GlobalChatViewWindow/GlobalChatViewWindow.vue'))\r\nconst GlobalGroupVote = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalGroupVote.vue'))\r\nconst GlobalCreateVote = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalCreateVote.vue'))\r\nconst GlobalVoteDetails = defineAsyncComponent(() => import('./GlobalGroupVote/GlobalVoteDetails.vue'))\r\nconst props = defineProps({ modelValue: [String, Number], chatList: { type: Array, default: () => [] } })\r\nconst emit = defineEmits(['update:modelValue', 'time', 'refresh', 'send'])\r\nconst isMac = window.electron?.isMac\r\nconst isElectron = window.electron ? true : false\r\nconst keyword = ref('')\r\nconst chatId = computed({\r\n  get () {\r\n    return props.modelValue\r\n  },\r\n  set (value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst chatType = ['RC:TxtMsg', 'RC:ImgTextMsg']\r\nconst chatList = computed(() => props.chatList)\r\nconst isChat = ref(true)\r\nconst chatInfo = ref({})\r\nconst groupUser = ref([])\r\nconst chatInfoImg = ref([])\r\nconst chatInfoAudio = ref({})\r\nconst chatInfoAudioObj = ref({})\r\nconst electronFile = ref([])\r\nconst isElectronFile = ref([])\r\nconst isElectronFileObj = ref({})\r\nconst electronRecordObj = ref({})\r\nconst electronRecordData = ref([])\r\nconst chatInfoUser = ref({})\r\nconst chatMenuTop = ref(0)\r\nconst chatMenuLeft = ref(0)\r\nconst chatMenuItem = ref({})\r\nconst chatMenuShow = ref(false)\r\nconst isChatMenuShow = ref(false)\r\nconst messagesMenuTop = ref(0)\r\nconst messagesMenuLeft = ref(0)\r\nconst messagesMenuItem = ref({})\r\nconst messagesMenuShow = ref(false)\r\nconst isMessagesMenuShow = ref(false)\r\nconst fileList = ref([])\r\nconst fileShow = ref(false)\r\nconst fileImg = ref({})\r\nconst imgShow = ref(false)\r\nconst userId = ref([])\r\nconst createGroupShow = ref(false)\r\nconst infoId = ref('')\r\nconst addShow = ref(false)\r\nconst delShow = ref(false)\r\nconst nameShow = ref(false)\r\nconst qrShow = ref(false)\r\nconst isGroupOwner = ref(false)\r\nconst announcementShow = ref(false)\r\nconst transferShow = ref(false)\r\nconst settingShow = ref(false)\r\nconst chatGroupAnnouncement = ref('')\r\nconst isChatGroupAnnouncement = ref(false)\r\nconst voteId = ref('')\r\nconst voteRefresh = ref('')\r\nconst voteShow = ref(false)\r\nconst createVoteShow = ref(false)\r\nconst voteDetailsShow = ref(false)\r\n// 图片地址拼接组合\r\nconst imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst scrollRef = ref()\r\nconst editorRef = ref()\r\nconst scrollHeight = ref(0)\r\nconst scrollTopNum = ref(0)\r\nconst scrollShow = ref(true)\r\nconst chatInfoMessages = ref([])\r\nconst chatInfoMessagesData = ref([])\r\nonMounted(() => { })\r\nconst handleElClick = (e) => {\r\n  chatMenuShow.value = false\r\n  messagesMenuShow.value = false\r\n  e.preventDefault()\r\n}\r\nconst handleElContextmenu = (e) => {\r\n  if (isChatMenuShow.value) {\r\n    isChatMenuShow.value = false\r\n  } else {\r\n    chatMenuShow.value = false\r\n  }\r\n  if (isMessagesMenuShow.value) {\r\n    isMessagesMenuShow.value = false\r\n  } else {\r\n    messagesMenuShow.value = false\r\n  }\r\n  e.preventDefault()\r\n}\r\nconst handleCustom = (item) => {\r\n  if (item.customType === 'file') isElectron ? handleElectronFile(item.uid, item.file, false) : handlePreview(item.file)\r\n  if (item.customType === 'vote') handleVoteDetails(item.vote)\r\n}\r\nconst handleElectronFile = async (uid, file, type = false) => {\r\n  if (isElectronFile.value?.includes(uid)) return\r\n  let fileName = file.originalFileName\r\n  if (electronRecordObj.value[uid]) {\r\n    fileName = electronRecordObj.value[uid]\r\n  } else {\r\n    fileName = getUniqueFileName(fileName, electronRecordData.value)\r\n    electronRecordObj.value[uid] = fileName\r\n    electronRecordData.value.push(fileName)\r\n  }\r\n  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'\r\n  const result = await window.electron.fileExists(fileFolderPath, fileName)\r\n  if (result) type ? handleOpenFolderSelectFile(fileName) : handleElectronOpenFile(fileName)\r\n  if (!result) globalElectronFileDownload(uid, file, fileName, type)\r\n}\r\nconst onDownloadProgress = (progressEvent, id) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    isElectronFileObj.value[id] = 100 - parseInt(progress) + '%'\r\n  }\r\n}\r\nconst globalElectronFileDownload = async (uid, file, fileName, type = false) => {\r\n  isElectronFile.value.push(uid)\r\n  isElectronFileObj.value[uid] = '100%'\r\n  const res = await api.globalElectronFileDownload(uid, file.id, {}, onDownloadProgress)\r\n  isElectronFile.value = isElectronFile.value.filter((v) => v !== uid)\r\n  delete isElectronFileObj.value[uid]\r\n  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'\r\n  const saveRes = await window.electron.saveFile(fileFolderPath, fileName, res)\r\n  if (saveRes.type === 'success') type ? handleOpenFolderSelectFile(fileName) : handleElectronOpenFile(fileName)\r\n  if (saveRes.type === 'error') ElMessage({ type: 'error', message: saveRes.message })\r\n}\r\nconst handleElectronOpenFile = async (fileName) => {\r\n  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'\r\n  const openRes = await window.electron.openFile(fileFolderPath, fileName)\r\n  if (openRes.type === 'error') ElMessage({ type: 'error', message: openRes.message })\r\n}\r\nconst handleOpenFolderSelectFile = async (fileName) => {\r\n  const fileFolderPath = chatId.value + '_' + user.value.accountId + '_file'\r\n  const openRes = await window.electron.openFolderSelectFile(`chat_files/${fileFolderPath}`, fileName)\r\n  if (openRes.type === 'error') ElMessage({ type: 'error', message: openRes.message })\r\n}\r\nconst handleFolderSelectFile = async () => {\r\n  const uid = messagesMenuItem.value?.uid\r\n  const file = messagesMenuItem.value?.file\r\n  handleElectronFile(uid, file, true)\r\n}\r\nconst globalElectronSaveRecord = async (recordId, data) => {\r\n  const fileName = recordId + '_' + user.value.accountId + '_record.txt'\r\n  const fileContent = utils.gm_encrypt(data, 'zysoft2017-08-11', 'zysoft2017-08-11')\r\n  const res = await window.electron.saveRecordFile('chat_record', fileName, fileContent)\r\n  console.log(res)\r\n}\r\nconst globalElectronReadRecord = async (recordId) => {\r\n  const fileName = recordId + '_' + user.value.accountId + '_record.txt'\r\n  const res = await window.electron.readRecordFile('chat_record', fileName)\r\n  if (res.type === 'success') {\r\n    const recordObj = JSON.parse(utils.gm_decrypt(res.data, 'zysoft2017-08-11', 'zysoft2017-08-11'))\r\n    const recordData = []\r\n    for (const key in recordObj) {\r\n      if (Object.prototype.hasOwnProperty.call(recordObj, key)) {\r\n        const value = recordObj[key]\r\n        recordData.push(value)\r\n      }\r\n    }\r\n    electronRecordObj.value = recordObj\r\n    electronRecordData.value = recordData\r\n  }\r\n}\r\nconst handlePreview = (row) => {\r\n  if (!row) return\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nconst querySearch = (queryString, cb) => {\r\n  const results = queryString\r\n    ? chatList.value.filter((v) => v.chatObjectInfo?.name?.toLowerCase().includes(queryString?.toLowerCase()))\r\n    : []\r\n  cb(results)\r\n}\r\nconst scrollDown = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight\r\n}\r\nconst scrollElHeight = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - scrollHeight.value\r\n}\r\nconst handleMessagesScroll = ({ scrollTop }) => {\r\n  scrollTopNum.value = scrollTop\r\n  if (scrollTop === 0) {\r\n    scrollHeight.value = scrollRef.value.wrapRef.scrollHeight\r\n    getHistoryMessages(chatInfoMessages.value[0]?.sentTime || 0)\r\n  }\r\n}\r\nconst handleClick = async (item) => {\r\n  if (chatId.value === item.id) return\r\n  isChat.value = true\r\n  chatId.value = item.id\r\n}\r\nconst handleChatClick = async (item, type) => {\r\n  settingShow.value = false\r\n  chatInfo.value = item\r\n  scrollHeight.value = 0\r\n  chatInfoMessages.value = []\r\n  electronFile.value = []\r\n  isElectronFile.value = []\r\n  isElectronFileObj.value = {}\r\n  if (!type) {\r\n    editorRef.value?.clearMessage()\r\n    chatGroupAnnouncement.value = ''\r\n    isChatGroupAnnouncement.value = false\r\n  }\r\n  getHistoryMessages()\r\n  if (!item.isTemporary) clearMessagesUnreadStatus()\r\n}\r\nconst handleClearAway = () => {\r\n  ElMessageBox.confirm('此操作将清除所有消息的未读状态, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  })\r\n    .then(() => {\r\n      clearAllMessagesUnreadStatus()\r\n    })\r\n    .catch(() => {\r\n      ElMessage({ type: 'info', message: '已取消清除' })\r\n    })\r\n}\r\nconst clearAllMessagesUnreadStatus = async () => {\r\n  const { code, msg } = await RongIMLib.clearAllMessagesUnreadStatus()\r\n  if (code === 0) {\r\n    handleRefresh()\r\n  } else {\r\n    console.log(code, msg)\r\n  }\r\n}\r\nconst clearMessagesUnreadStatus = async (type) => {\r\n  const { code, msg } = await RongIMLib.clearMessagesUnreadStatus({\r\n    conversationType: chatInfo.value.type,\r\n    targetId: chatInfo.value.targetId\r\n  })\r\n  if (code === 0) {\r\n    if (!type) handleRefresh()\r\n  } else {\r\n    console.log(code, msg)\r\n  }\r\n}\r\nconst getHistoryMessages = async (timestamp = 0) => {\r\n  const option = { timestamp, count: 20, order: 0 }\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const { code, data, msg } = await RongIMLib.getHistoryMessages(conversation, option)\r\n  if (code === 0) {\r\n    await handleUser()\r\n    scrollShow.value = timestamp === 0\r\n    handleMessages(data.list, timestamp === 0)\r\n  } else {\r\n    console.log(code, msg)\r\n  }\r\n}\r\nconst getNewestMessages = async () => {\r\n  const option = { timestamp: chatInfo.value.sentTime, count: 20, order: 1 }\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const { code, data, msg } = await RongIMLib.getHistoryMessages(conversation, option)\r\n  if (code === 0) {\r\n    scrollShow.value = true\r\n    clearMessagesUnreadStatus(true)\r\n    handleMessages(data.list, true)\r\n  } else {\r\n    console.log(code, msg)\r\n  }\r\n}\r\nconst chatGroupInfo = async (id) => {\r\n  const { data } = await api.chatGroupInfo({ detailId: id.slice(appOnlyHeader.value.length) })\r\n  const groupAnnouncement = JSON.parse(localStorage.getItem('isChatGroupAnnouncement')) || {}\r\n  const groupAnnouncementItem = groupAnnouncement.hasOwnProperty(id)\r\n    ? groupAnnouncement[id]\r\n    : { show: true, callBoard: '' }\r\n  if (data.callBoard && groupAnnouncementItem.callBoard !== data.callBoard) {\r\n    chatGroupAnnouncement.value = data.callBoard\r\n    isChatGroupAnnouncement.value = true\r\n  }\r\n  return data\r\n}\r\nconst handleChatGroupAnnouncement = () => {\r\n  const groupAnnouncement = JSON.parse(localStorage.getItem('isChatGroupAnnouncement')) || {}\r\n  let newGroupAnnouncement = { ...groupAnnouncement }\r\n  newGroupAnnouncement[chatInfo.value.targetId] = { show: false, callBoard: chatGroupAnnouncement.value }\r\n  localStorage.setItem('isChatGroupAnnouncement', JSON.stringify(newGroupAnnouncement))\r\n  chatGroupAnnouncement.value = ''\r\n  isChatGroupAnnouncement.value = false\r\n}\r\nconst handleAreArraysEqual = (arr1, arr2) => {\r\n  // 如果数组长度不相等，直接返回 false\r\n  if (arr1.length !== arr2.length) return false\r\n  // 将两个数组排序后比较\r\n  const sortedArr1 = [...arr1].sort()\r\n  const sortedArr2 = [...arr2].sort()\r\n  // 遍历比较每个元素\r\n  for (let i = 0; i < sortedArr1.length; i++) {\r\n    if (sortedArr1[i] !== sortedArr2[i]) {\r\n      return false\r\n    }\r\n  }\r\n  return true\r\n}\r\n\r\nconst handleUser = async () => {\r\n  let newUserObj = {}\r\n  if (chatInfo.value.type === 3) {\r\n    const newGroupInfo = await chatGroupInfo(chatInfo.value.targetId)\r\n    if (!newGroupInfo.id) {\r\n      ElMessageBox.alert('当前群组已解散！', '提示', {\r\n        confirmButtonText: '确定',\r\n        callback: () => {\r\n          scrollHeight.value = 0\r\n          chatInfoMessages.value = []\r\n          chatInfoMessagesData.value = []\r\n          chatMenuItem.value = chatInfo.value\r\n          handleDelChat()\r\n        }\r\n      })\r\n    }\r\n    const isGroupUser = handleAreArraysEqual(\r\n      newGroupInfo.memberUserIds,\r\n      groupUser.value.map((v) => v.accountId)\r\n    )\r\n    if (!isGroupUser)\r\n      groupUser.value = await chatGroupMemberList(chatInfo.value.targetId.slice(appOnlyHeader.value.length))\r\n    let isChatGroup = false\r\n    for (let index = 0; index < groupUser.value.length; index++) {\r\n      const item = groupUser.value[index]\r\n      if (item.accountId === user.value.accountId) isChatGroup = true\r\n      newUserObj[appOnlyHeader.value + item.accountId] = {\r\n        uid: appOnlyHeader.value + item.accountId,\r\n        id: item.accountId,\r\n        name: item.userName,\r\n        img: item.photo || item.headImg,\r\n        userInfo: { userId: item.id, userName: item.userName, photo: item.photo, headImg: item.headImg }\r\n      }\r\n    }\r\n    isChat.value = newGroupInfo.id ? isChatGroup : true\r\n  } else {\r\n    isChat.value = true\r\n    groupUser.value = []\r\n    newUserObj[appOnlyHeader.value + user.value.accountId] = {\r\n      uid: appOnlyHeader.value + user.value.accountId,\r\n      id: user.value.accountId,\r\n      name: user.value.userName,\r\n      img: user.value.photo || user.value.headImg,\r\n      userInfo: {\r\n        userId: user.value.id,\r\n        userName: user.value.userName,\r\n        photo: user.value.photo,\r\n        headImg: user.value.headImg\r\n      }\r\n    }\r\n    newUserObj[chatInfo.value?.chatObjectInfo.uid] = chatInfo.value?.chatObjectInfo\r\n  }\r\n  chatInfoUser.value = newUserObj\r\n}\r\nconst handleMessages = async (data, type) => {\r\n  const { newMessages, withdrawId } = await handleHistoryMessages(data, chatInfoUser.value)\r\n  console.log(newMessages)\r\n  if (type) {\r\n    chatInfoMessages.value = [...chatInfoMessages.value, ...newMessages].filter((v) => !withdrawId?.includes(v.uid))\r\n    handleRenderMessages(withdrawId)\r\n  } else {\r\n    chatInfoMessages.value = [...newMessages, ...chatInfoMessages.value].filter((v) => !withdrawId?.includes(v.uid))\r\n    handleRenderMessages(withdrawId)\r\n  }\r\n}\r\nconst handleRenderMessages = async (withdrawId, type) => {\r\n  let timeData = []\r\n  let newMessages = []\r\n  let newChatInfoImg = []\r\n  let newMessagesId = []\r\n  let isGroupAnnouncement = false\r\n  for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n    const item = chatInfoMessages.value[index]\r\n    if (item.content?.content?.includes('群公告：\\n')) isGroupAnnouncement = true\r\n    if (!newMessagesId?.includes(item.uid) && !withdrawId?.includes(item.uid)) {\r\n      newMessagesId.push(item.uid)\r\n      if (!timeData?.includes(format(item.sentTime))) {\r\n        timeData = [format(item.sentTime), format(item.sentTime + 60000)]\r\n        newMessages.push({\r\n          id: item.sentTime,\r\n          type: 'time',\r\n          className: 'GlobalChatMessagesTime',\r\n          content: format(item.sentTime)\r\n        })\r\n      }\r\n      if (item.type === 'RC:ImgMsg') {\r\n        newMessages.push({ ...item, imgIndex: newChatInfoImg.length })\r\n        newChatInfoImg.push(item.content.imageUri)\r\n      } else if (item.type === 'RC:HQVCMsg') {\r\n        newMessages.push({ ...item, audio: new Audio(item.content.remoteUrl) })\r\n      } else if (item.type === 'RC:ImgTextMsg') {\r\n        newMessages.push(item)\r\n        electronFile.value.push(item?.uid)\r\n      } else {\r\n        newMessages.push(item)\r\n      }\r\n    }\r\n  }\r\n  if (isGroupAnnouncement) chatGroupInfo(chatInfo.value.targetId)\r\n  chatInfoImg.value = newChatInfoImg\r\n  chatInfoMessagesData.value = newMessages\r\n  if (type) {\r\n    nextTick(() => {\r\n      scrollRef.value.wrapRef.scrollTop = scrollTopNum.value\r\n    })\r\n  } else {\r\n    nextTick(() => {\r\n      scrollShow.value ? scrollDown() : scrollElHeight()\r\n    })\r\n  }\r\n}\r\nconst handleChatMenu = (e, item) => {\r\n  chatMenuItem.value = item\r\n  chatMenuTop.value = e.pageY\r\n  chatMenuLeft.value = e.pageX\r\n  chatMenuShow.value = true\r\n  isChatMenuShow.value = true\r\n}\r\nconst handleMessagesMenu = (e, item) => {\r\n  const selection = window.getSelection()\r\n  const copyContent = selection.toString() || item?.content?.content\r\n  const now = new Date().getTime()\r\n  const timeDifference = now - item.sentTime\r\n  const isWithdraw = timeDifference < 180000\r\n  messagesMenuItem.value = { ...item, copyContent, isWithdraw }\r\n  messagesMenuTop.value = e.pageY\r\n  messagesMenuLeft.value = e.pageX\r\n  messagesMenuShow.value = true\r\n  isMessagesMenuShow.value = true\r\n}\r\nconst handleAudio = (data) => {\r\n  if (chatInfoAudio.value.id) {\r\n    if (chatInfoAudio.value.id === data.id) {\r\n      chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime\r\n      handlePauseAudio()\r\n    } else {\r\n      chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime\r\n      handlePauseAudio()\r\n      chatInfoAudio.value = { id: data.id, audio: data.audio }\r\n      chatInfoAudio.value.audio.currentTime = 0\r\n      handlePlayAudio()\r\n    }\r\n  } else {\r\n    chatInfoAudio.value = { id: data.id, audio: data.audio }\r\n    chatInfoAudio.value.audio.currentTime = 0\r\n    handlePlayAudio()\r\n  }\r\n}\r\nconst handleGoAudio = (data) => {\r\n  if (chatInfoAudio.value.id) {\r\n    chatInfoAudioObj.value[chatInfoAudio.value.id] = chatInfoAudio.value.audio.currentTime\r\n    handlePauseAudio()\r\n  }\r\n  chatInfoAudio.value = { id: data.id, audio: data.audio }\r\n  chatInfoAudio.value.audio.currentTime = chatInfoAudioObj.value[chatInfoAudio.value.id] || 0\r\n  handlePlayAudio()\r\n}\r\nconst handlePlayAudio = () => {\r\n  chatInfoAudio.value.audio.play()\r\n  chatInfoAudio.value.audio.addEventListener('ended', () => {\r\n    chatInfoAudioObj.value[chatInfoAudio.value.id] = 0\r\n    chatInfoAudio.value = {}\r\n  })\r\n}\r\nconst handlePauseAudio = () => {\r\n  chatInfoAudio.value.audio.pause()\r\n  chatInfoAudio.value = {}\r\n}\r\nconst handleImgLoad = () => {\r\n  if (scrollShow.value) scrollDown()\r\n}\r\nconst handleDrop = (event) => {\r\n  event.preventDefault()\r\n  const files = event.dataTransfer.files\r\n  if (files.length) {\r\n    const mewFileList = []\r\n    for (let index = 0; index < files.length; index++) {\r\n      const file = files.item(index)\r\n      const extName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()\r\n      mewFileList.push({ id: guid(), name: file.name, extName, size: file.size, file: file })\r\n    }\r\n    fileList.value = mewFileList\r\n    fileShow.value = true\r\n  }\r\n}\r\nconst fileCallback = (data) => {\r\n  if (data) {\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      fileUpload(item)\r\n    }\r\n  }\r\n  fileList.value = []\r\n  fileShow.value = false\r\n}\r\nconst handlePasteImg = (file) => {\r\n  fileImg.value = file\r\n  imgShow.value = true\r\n}\r\nconst imgCallback = (type) => {\r\n  if (type) fileUpload(fileImg.value)\r\n  fileImg.value = {}\r\n  imgShow.value = false\r\n}\r\nconst fileUpload = async (file) => {\r\n  const params = new FormData()\r\n  params.append('file', file.file)\r\n  params.append('isKeepAlive', true)\r\n  params.append('uid', file.id || file.uid || guid())\r\n  const { data } = await api.globalUpload(params, () => { })\r\n  if (['png', 'jpg', 'jpeg']?.includes(data.extName)) {\r\n    handleSendImgMessage(api.openImgURL(data.newFileName))\r\n  } else {\r\n    localStorage.setItem(data.id, JSON.stringify(data))\r\n    handleSendFileMessage(data.id)\r\n    if (chatInfo.value.type === 3) submitChatGroupFile(data.id)\r\n  }\r\n}\r\nconst submitChatGroupFile = async (fileId) => {\r\n  await api.chatGroupFileAdd({\r\n    form: { chatGroupId: chatInfo.value.targetId.slice(appOnlyHeader.value.length), fileId }\r\n  })\r\n}\r\nconst isMacText = () => {\r\n  const userAgent = navigator.userAgent.toLowerCase()\r\n  return userAgent?.includes('macintosh') || userAgent?.includes('mac os x')\r\n}\r\nconst handleSetting = () => {\r\n  if (!isChat.value) return\r\n  settingShow.value = !settingShow.value\r\n}\r\nconst handleKeyCode = (data) => {\r\n  const userIdData = data?.mentions?.map((v) => appOnlyHeader.value + v.userInfo.accountId)\r\n  const mentionData = userIdData.length\r\n    ? { mentionedContent: '', type: 2, userIdList: Array.from(new Set(userIdData)) }\r\n    : {}\r\n  handleSendTextMessage(data.content, mentionData)\r\n}\r\nconst handleSendTextMessage = (contentText, mentionData = {}) => {\r\n  if (!contentText.replace(/^\\s+|\\s+$/g, '')) return\r\n  const message = new RongIMLib.TextMessage({ content: contentText, mentionedInfo: mentionData })\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const options = {\r\n    onSendBefore: (message) => {\r\n      scrollShow.value = true\r\n      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)\r\n    }\r\n  }\r\n  handleSendMessage(conversation, message, options, (code, msg, data) => {\r\n    if (code === 0) {\r\n      let newChatInfoMessages = []\r\n      for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n        const item = chatInfoMessages.value[index]\r\n        newChatInfoMessages.push(\r\n          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item\r\n        )\r\n      }\r\n      chatInfoMessages.value = newChatInfoMessages\r\n      handleRenderMessages([], true)\r\n      console.log('消息发送成功：', data)\r\n    } else {\r\n      console.log('消息发送失败：', code, msg)\r\n    }\r\n  })\r\n}\r\nconst handleSendImgMessage = (url) => {\r\n  const message = new RongIMLib.ImageMessage({ content: '', imageUri: url })\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const options = {\r\n    onSendBefore: (message) => {\r\n      scrollShow.value = true\r\n      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)\r\n    }\r\n  }\r\n  handleSendMessage(conversation, message, options, (code, msg, data) => {\r\n    if (code === 0) {\r\n      let newChatInfoMessages = []\r\n      for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n        const item = chatInfoMessages.value[index]\r\n        newChatInfoMessages.push(\r\n          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item\r\n        )\r\n      }\r\n      chatInfoMessages.value = newChatInfoMessages\r\n      handleRenderMessages([], true)\r\n      console.log('消息发送成功：', data)\r\n    } else {\r\n      console.log('消息发送失败：', code, msg)\r\n    }\r\n  })\r\n}\r\nconst handleSendFileMessage = (fileId) => {\r\n  const PersonMessage = RongIMLib.registerMessageType('RC:ImgTextMsg', true, true, [], false)\r\n  const message = new PersonMessage({ content: `[文件],${fileId}`, title: '[文件]' })\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const options = {\r\n    onSendBefore: (message) => {\r\n      scrollShow.value = true\r\n      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)\r\n    }\r\n  }\r\n  handleSendMessage(conversation, message, options, (code, msg, data) => {\r\n    if (code === 0) {\r\n      let newChatInfoMessages = []\r\n      for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n        const item = chatInfoMessages.value[index]\r\n        newChatInfoMessages.push(\r\n          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item\r\n        )\r\n      }\r\n      chatInfoMessages.value = newChatInfoMessages\r\n      handleRenderMessages([], true)\r\n      console.log('消息发送成功：', data)\r\n    } else {\r\n      console.log('消息发送失败：', code, msg)\r\n    }\r\n  })\r\n}\r\nconst handleSendCustomMessage = (params) => {\r\n  const PersonMessage = RongIMLib.registerMessageType('RC:CmdNtf', true, true, [], false)\r\n  const message = new PersonMessage(params)\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const options = {\r\n    onSendBefore: (message) => {\r\n      scrollShow.value = true\r\n      handleMessages([{ ...message, sentTime: Date.parse(new Date()) }], true)\r\n    }\r\n  }\r\n  handleSendMessage(conversation, message, options, (code, msg, data) => {\r\n    if (code === 0) {\r\n      let newChatInfoMessages = []\r\n      for (let index = 0; index < chatInfoMessages.value.length; index++) {\r\n        const item = chatInfoMessages.value[index]\r\n        newChatInfoMessages.push(\r\n          item.id === data.messageId ? { ...item, uid: data.messageUId, sentTime: data.sentTime } : item\r\n        )\r\n      }\r\n      chatInfoMessages.value = newChatInfoMessages\r\n      handleRenderMessages([], true)\r\n      console.log('消息发送成功：', data)\r\n    } else {\r\n      console.log('消息发送失败：', code, msg)\r\n    }\r\n  })\r\n}\r\nconst handleSendMessage = async (conversation, message, options, callback) => {\r\n  const { code, msg, data } = await RongIMLib.sendMessage(conversation, message, options)\r\n  if (code === 0) handleRefresh()\r\n  callback(code, msg, data)\r\n}\r\nconst handleNotificationClick = async (notification) => {\r\n  const { code } = await RongIMLib.setConversationNotificationStatus(\r\n    { conversationType: chatMenuItem.value.type, targetId: chatMenuItem.value.id },\r\n    notification\r\n  )\r\n  if (!code) handleRefresh()\r\n}\r\nconst handleIsTopClick = async (isTop) => {\r\n  const { code } = await RongIMLib.setConversationToTop(\r\n    { conversationType: chatMenuItem.value.type, targetId: chatMenuItem.value.id },\r\n    isTop\r\n  )\r\n  if (!code) handleRefresh()\r\n}\r\nconst handleDelChat = async () => {\r\n  const { code, msg } = await RongIMLib.removeConversation({\r\n    conversationType: chatMenuItem.value.type,\r\n    targetId: chatMenuItem.value.id\r\n  })\r\n  if (code === 0) {\r\n    console.log('消息删除成功')\r\n    handleRefresh('del', chatMenuItem.value)\r\n  } else {\r\n    console.log('消息删除失败：', code, msg)\r\n  }\r\n}\r\nconst handleDelMessage = async () => {\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const messagesData = [\r\n    {\r\n      messageUId: messagesMenuItem.value.uid,\r\n      sentTime: messagesMenuItem.value.sentTime,\r\n      messageDirection: messagesMenuItem.value.direction\r\n    }\r\n  ]\r\n  const { code, msg } = await RongIMLib.deleteMessages(conversation, messagesData)\r\n  if (code === 0) {\r\n    console.log('消息删除成功')\r\n    chatInfoMessages.value = chatInfoMessages.value.filter((item) => item.uid !== messagesMenuItem.value.uid)\r\n    handleRenderMessages([], true)\r\n  } else {\r\n    console.log('消息删除失败：', code, msg)\r\n  }\r\n}\r\nconst handleWithdrawMessage = async () => {\r\n  const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n  const messagesData = {\r\n    messageUId: messagesMenuItem.value.uid,\r\n    sentTime: messagesMenuItem.value.sentTime,\r\n    disableNotification: true\r\n  }\r\n  const { code, msg } = await RongIMLib.recallMessage(conversation, messagesData)\r\n  if (code === 0) {\r\n    handleChatClick(chatInfo.value, true)\r\n    console.log('消息撤回成功')\r\n  } else {\r\n    console.log('消息撤回失败：', code, msg)\r\n  }\r\n}\r\nconst handleGroup = async (type, data, isOwner) => {\r\n  if (type === 'create') handleCreateGroup(data?.chatObjectInfo?.id)\r\n  if (type === 'add') handleGroupAddUser(data?.chatObjectInfo?.id)\r\n  if (type === 'del') handleGroupDelUser(data?.chatObjectInfo?.id)\r\n  if (type === 'name') handleGroupName(data?.chatObjectInfo?.id)\r\n  if (type === 'qr') handleGroupQr(data?.chatObjectInfo?.id)\r\n  if (type === 'announcement') handleGroupAnnouncement(data?.chatObjectInfo?.id, isOwner)\r\n  if (type === 'transfer') handleGroupTransfer(data?.chatObjectInfo?.id)\r\n  if (type === 'quit') {\r\n    const PersonMessage = RongIMLib.registerMessageType('RC:CmdNtf', true, true, [], false)\r\n    const message = new PersonMessage(data)\r\n    const conversation = { conversationType: chatInfo.value.type, targetId: chatInfo.value.targetId }\r\n    const options = { onSendBefore: () => { } }\r\n    await RongIMLib.sendMessage(conversation, message, options)\r\n    handleQuitDelChat()\r\n  }\r\n}\r\nconst handleQuitDelChat = async () => {\r\n  const { code, msg } = await RongIMLib.removeConversation({\r\n    conversationType: chatInfo.value.type,\r\n    targetId: chatInfo.value.targetId\r\n  })\r\n  if (code === 0) {\r\n    console.log('消息删除成功')\r\n    handleRefresh()\r\n  } else {\r\n    console.log('消息删除失败：', code, msg)\r\n  }\r\n}\r\nconst handleCreateGroup = (id) => {\r\n  userId.value = id ? [user.value?.accountId, id] : [user.value?.accountId]\r\n  createGroupShow.value = true\r\n}\r\nconst handleGroupAddUser = (id) => {\r\n  infoId.value = id\r\n  addShow.value = true\r\n}\r\nconst handleGroupDelUser = (id) => {\r\n  infoId.value = id\r\n  delShow.value = true\r\n}\r\nconst handleGroupName = (id) => {\r\n  infoId.value = id\r\n  nameShow.value = true\r\n}\r\nconst handleGroupQr = (id) => {\r\n  infoId.value = id\r\n  qrShow.value = true\r\n}\r\nconst handleGroupAnnouncement = (id, isOwner) => {\r\n  infoId.value = id\r\n  isGroupOwner.value = isOwner\r\n  announcementShow.value = true\r\n}\r\nconst handleGroupTransfer = (id) => {\r\n  infoId.value = id\r\n  transferShow.value = true\r\n}\r\nconst handleVote = () => {\r\n  infoId.value = chatInfo.value.targetId.slice(appOnlyHeader.value.length)\r\n  voteShow.value = true\r\n}\r\nconst handleVoteDetails = (row) => {\r\n  voteId.value = row.id\r\n  voteDetailsShow.value = true\r\n}\r\nconst createCallback = (data) => {\r\n  settingShow.value = false\r\n  if (data) emit('send', data)\r\n  createGroupShow.value = false\r\n}\r\nconst addCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    await handleUser()\r\n    handleSendCustomMessage(data)\r\n  }\r\n  addShow.value = false\r\n}\r\nconst delCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    await handleUser()\r\n    handleSendCustomMessage(data)\r\n  }\r\n  delShow.value = false\r\n}\r\nconst nameCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    await handleUser()\r\n    handleTime()\r\n    handleSendCustomMessage(data)\r\n  }\r\n  nameShow.value = false\r\n}\r\nconst announcementCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    handleSendTextMessage(data)\r\n  }\r\n  announcementShow.value = false\r\n}\r\nconst transferCallback = async (type, data) => {\r\n  settingShow.value = false\r\n  if (type) {\r\n    await handleUser()\r\n    handleSendCustomMessage(data)\r\n  }\r\n  transferShow.value = false\r\n}\r\nconst voteCallback = () => {\r\n  createVoteShow.value = true\r\n}\r\nconst handleVoteCallback = () => {\r\n  voteRefresh.value = guid()\r\n  createVoteShow.value = false\r\n  voteDetailsShow.value = false\r\n}\r\nconst handleTime = () => {\r\n  emit('time')\r\n}\r\nconst handleRefresh = (type, data) => {\r\n  emit('refresh', type, data)\r\n}\r\nonUnmounted(() => {\r\n  if (isElectron) {\r\n    const fileContent = JSON.stringify(electronRecordObj.value)\r\n    if (chatId.value) globalElectronSaveRecord(chatId.value, fileContent)\r\n  }\r\n})\r\nwatch(\r\n  () => chatId.value,\r\n  (newValue, oldValue) => {\r\n    if (isElectron) {\r\n      const fileContent = JSON.stringify(electronRecordObj.value)\r\n      electronRecordObj.value = {}\r\n      electronRecordData.value = []\r\n      if (oldValue) globalElectronSaveRecord(oldValue, fileContent)\r\n      if (newValue) globalElectronReadRecord(newValue)\r\n    }\r\n    if (chatId.value) {\r\n      for (let index = 0; index < props.chatList.length; index++) {\r\n        const item = props.chatList[index]\r\n        if (chatId.value === item.id) handleChatClick(item)\r\n      }\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => chatList.value,\r\n  () => {\r\n    if (chatId.value) {\r\n      let isShow = true\r\n      for (let index = 0; index < props.chatList.length; index++) {\r\n        const item = props.chatList[index]\r\n        if (chatId.value === item.id) {\r\n          isShow = false\r\n          chatInfo.value = item\r\n        }\r\n      }\r\n      if (isShow) {\r\n        chatId.value = ''\r\n        chatInfo.value = {}\r\n      }\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\ndefineExpose({ getNewestMessages })\r\n</script>\r\n<style lang=\"scss\">\r\n@import url('../scss/emotion.scss');\r\n\r\n.GlobalChatView {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n\r\n  &.GlobalChatMacView {\r\n    .GlobalChatViewList {\r\n      .GlobalChatViewListHead {\r\n        height: 56px;\r\n      }\r\n\r\n      .GlobalChatViewMessagesList {\r\n        height: calc(100% - 92px);\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewDrag {\r\n      height: 56px;\r\n    }\r\n\r\n    .GlobalChatWindow {\r\n      .GlobalChatWindowTitle {\r\n        height: 56px;\r\n\r\n        .GlobalChatWindowMore {\r\n          margin: 0;\r\n        }\r\n      }\r\n\r\n      .GlobalChatWindowScroll {\r\n        height: calc(100% - 222px);\r\n\r\n        &.GlobalChatWindowNoChat {\r\n          height: calc(100% - (56px + var(--zy-height)));\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatViewList {\r\n    width: 280px;\r\n    height: 100%;\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalChatViewListHead {\r\n      width: 100%;\r\n      height: 66px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n      -webkit-app-region: drag;\r\n\r\n      .zy-el-autocomplete {\r\n        width: 199px;\r\n        height: var(--zy-height-routine);\r\n        -webkit-app-region: no-drag;\r\n\r\n        .zy-el-input {\r\n          width: 199px;\r\n          height: var(--zy-height-routine);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewListHeadIcon {\r\n        width: 32px;\r\n        height: 32px;\r\n        cursor: pointer;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewMessagesList {\r\n      width: 100%;\r\n      height: calc(100% - 102px);\r\n\r\n      .GlobalChatViewMessagesItem {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 10px 20px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          background: #f9f9fa;\r\n        }\r\n\r\n        &.is-top {\r\n          background: #f6f6f6;\r\n        }\r\n\r\n        &.is-active {\r\n          background: #f2f2f2;\r\n        }\r\n\r\n        .zy-el-badge {\r\n          width: 42px;\r\n          height: 42px;\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 42px;\r\n          height: 42px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .GlobalChatViewMessagesInfo {\r\n          width: calc(100% - 56px);\r\n          height: 42px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: space-between;\r\n          position: relative;\r\n\r\n          .GlobalChatViewNotInform {\r\n            width: 16px;\r\n            height: 16px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            position: absolute;\r\n            right: 0;\r\n            bottom: 0;\r\n\r\n            path {\r\n              fill: var(--zy-el-text-color-secondary);\r\n            }\r\n          }\r\n\r\n          .GlobalChatViewMessagesName {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            div {\r\n              flex: 1;\r\n              font-size: 14px;\r\n            }\r\n\r\n            .GlobalChatViewMessagesNameGroup {\r\n              position: relative;\r\n              padding-right: 39px;\r\n\r\n              span {\r\n                padding: 2px 6px;\r\n                font-size: 12px;\r\n                border-radius: 3px;\r\n                position: absolute;\r\n                top: 50%;\r\n                right: 3px;\r\n                transform: translateY(-50%);\r\n                text-align: center;\r\n                color: var(--zy-el-color-success);\r\n                background: var(--zy-el-color-success-light-9);\r\n              }\r\n            }\r\n\r\n            span {\r\n              font-size: 12px;\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n          }\r\n\r\n          .GlobalChatViewNotInform+.GlobalChatViewMessagesText {\r\n            padding-right: 18px;\r\n          }\r\n\r\n          .GlobalChatViewMessagesText {\r\n            font-size: 12px;\r\n            color: var(--zy-el-text-color-secondary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatClearAway {\r\n      width: 100%;\r\n      height: 36px;\r\n      display: flex;\r\n      align-items: center;\r\n      color: var(--zy-el-text-color-secondary);\r\n      border-top: 1px solid var(--zy-el-border-color-lighter);\r\n      font-size: 14px;\r\n      padding-top: 4px;\r\n      padding-left: 46px;\r\n      position: relative;\r\n      cursor: pointer;\r\n\r\n      div {\r\n        width: 22px;\r\n        height: 22px;\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 20px;\r\n        transform: translateY(-50%);\r\n\r\n        .icon {\r\n          width: 22px;\r\n          height: 22px;\r\n\r\n          path {\r\n            fill: var(--zy-el-text-color-secondary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatViewDrag {\r\n    width: calc(100% - 280px);\r\n    height: 66px;\r\n    position: relative;\r\n    -webkit-app-region: drag;\r\n\r\n    &::before {\r\n      content: '';\r\n      width: 96px;\r\n      height: 28px;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      background: transparent;\r\n      -webkit-app-region: no-drag;\r\n    }\r\n  }\r\n\r\n  .GlobalChatWindow {\r\n    width: calc(100% - 280px);\r\n    height: 100%;\r\n    background: #f9f9fa;\r\n    position: relative;\r\n\r\n    .GlobalChatWindowTitle {\r\n      width: 100%;\r\n      height: 66px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n      background: #fff;\r\n      position: relative;\r\n      -webkit-app-region: drag;\r\n\r\n      &::after {\r\n        content: '';\r\n        width: 100%;\r\n        height: 1px;\r\n        position: absolute;\r\n        left: 0;\r\n        bottom: 0;\r\n        background: var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        width: 96px;\r\n        height: 28px;\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        background: transparent;\r\n        -webkit-app-region: no-drag;\r\n        z-index: 8;\r\n      }\r\n\r\n      .ellipsis {\r\n        max-width: calc(100% - 52px);\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .GlobalChatWindowMore {\r\n        width: 32px;\r\n        height: 32px;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        -webkit-app-region: no-drag;\r\n        margin-top: 16px;\r\n\r\n        .zy-el-icon {\r\n          font-size: 20px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewNoMessage {\r\n      width: 100%;\r\n      height: var(--zy-height);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background: rgba(0, 0, 0, 0.6);\r\n      color: #fff;\r\n      font-size: 12px;\r\n\r\n      .zy-el-icon {\r\n        font-size: 16px;\r\n        margin-right: 2px;\r\n      }\r\n    }\r\n\r\n    .GlobalChatWindowScroll {\r\n      width: 100%;\r\n      height: calc(100% - 232px);\r\n\r\n      &.GlobalChatWindowNoChat {\r\n        height: calc(100% - (66px + var(--zy-height)));\r\n      }\r\n\r\n      .GlobalChatGroupAnnouncement {\r\n        width: calc(100% - 40px);\r\n        position: absolute;\r\n        top: 10px;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        box-shadow: var(--zy-el-box-shadow-light);\r\n        border-radius: 4px;\r\n        padding: 10px 20px;\r\n        background: #fff;\r\n        z-index: 6;\r\n\r\n        .GlobalChatGroupAnnouncementTitle {\r\n          width: 100%;\r\n          height: 32px;\r\n          display: flex;\r\n          align-items: flex-end;\r\n          justify-content: space-between;\r\n          padding-bottom: 9px;\r\n\r\n          div {\r\n            display: flex;\r\n            align-items: flex-end;\r\n            justify-content: center;\r\n            font-size: 14px;\r\n            line-height: 14px;\r\n            font-weight: bold;\r\n            padding-left: 2px;\r\n\r\n            span {\r\n              width: 22px;\r\n              height: 22px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              margin-right: 6px;\r\n\r\n              .icon {\r\n                width: 22px;\r\n                height: 22px;\r\n\r\n                path {\r\n                  fill: var(--zy-el-color-primary);\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .zy-el-icon {\r\n            color: #ccc;\r\n            font-size: 18px;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n\r\n        .GlobalChatGroupAnnouncementContent {\r\n          font-size: 14px;\r\n          line-height: 1.6;\r\n        }\r\n      }\r\n\r\n      .GlobalChatWindowBody {\r\n        padding: var(--zy-distance-five) 0;\r\n\r\n        .GlobalChatMessagesTime {\r\n          width: 100%;\r\n          font-size: 14px;\r\n          text-align: center;\r\n          color: var(--zy-el-text-color-secondary);\r\n          padding: var(--zy-distance-five) var(--zy-distance-two);\r\n        }\r\n\r\n        .GlobalChatMessagesInform {\r\n          width: 100%;\r\n          font-size: 12px;\r\n          text-align: center;\r\n          color: var(--zy-el-text-color-secondary);\r\n          padding: var(--zy-distance-two);\r\n        }\r\n\r\n        .GlobalChatMessages,\r\n        .GlobalChatSelfMessages {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: var(--zy-distance-five) var(--zy-distance-two);\r\n\r\n          .GlobalChatWindowUserImg {\r\n            width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n\r\n            .zy-el-image {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 50%;\r\n            }\r\n          }\r\n\r\n          .GlobalChatMessagesInfo {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            width: calc(100% - ((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n            position: relative;\r\n\r\n            .GlobalChatMessagesImgSlot {\r\n              width: 168px;\r\n              height: 68px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              font-size: 28px;\r\n              background: #fff;\r\n              color: var(--zy-el-text-color-regular);\r\n            }\r\n\r\n            .zy-el-image {\r\n              width: 168px;\r\n              height: auto;\r\n              border-radius: 8px;\r\n              overflow: hidden;\r\n            }\r\n\r\n            .GlobalChatMessagesName {\r\n              width: 100%;\r\n              font-size: 12px;\r\n              line-height: 12px;\r\n              padding-bottom: 6px;\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n\r\n            .GlobalChatMessagesText {\r\n              position: relative;\r\n              display: inline-block;\r\n              max-width: 100%;\r\n              padding: 10px var(--zy-distance-five);\r\n              font-size: var(--zy-text-font-size);\r\n              line-height: var(--zy-line-height);\r\n              background: #fff;\r\n              border-radius: var(--el-border-radius-base);\r\n              border: 1px solid var(--zy-el-border-color-light);\r\n              word-wrap: break-word;\r\n              white-space: pre-wrap;\r\n              z-index: 2;\r\n\r\n              span {\r\n                position: absolute;\r\n                width: 10px;\r\n                height: 10px;\r\n                z-index: 2;\r\n                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);\r\n\r\n                &::after {\r\n                  content: '';\r\n                  position: absolute;\r\n                  width: 10px;\r\n                  height: 10px;\r\n                  transform: rotate(45deg);\r\n                  background: #fff;\r\n                  border: 1px solid var(--zy-el-border-color-light);\r\n                  box-sizing: border-box;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesCustom {\r\n              width: 320px;\r\n              padding: 12px 16px;\r\n              display: flex;\r\n              flex-direction: column;\r\n              background: #fff;\r\n              position: relative;\r\n              border-radius: var(--el-border-radius-base);\r\n              border: 1px solid var(--zy-el-border-color-light);\r\n              word-wrap: break-word;\r\n              white-space: pre-wrap;\r\n              cursor: pointer;\r\n\r\n              &.file {\r\n                padding-right: 58px;\r\n              }\r\n\r\n              &.unknown {\r\n                width: auto;\r\n              }\r\n\r\n              span {\r\n                position: absolute;\r\n                width: 10px;\r\n                height: 10px;\r\n                z-index: 2;\r\n                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);\r\n\r\n                &::after {\r\n                  content: '';\r\n                  position: absolute;\r\n                  width: 10px;\r\n                  height: 10px;\r\n                  transform: rotate(45deg);\r\n                  background: #fff;\r\n                  border: 1px solid var(--zy-el-border-color-light);\r\n                  box-sizing: border-box;\r\n                }\r\n              }\r\n\r\n              .GlobalChatMessagesCustomName {\r\n                font-size: var(--zy-text-font-size);\r\n                line-height: var(--zy-line-height);\r\n                padding-bottom: var(--zy-font-text-distance-five);\r\n                word-break: break-all;\r\n              }\r\n\r\n              .GlobalChatMessagesCustomText {\r\n                color: var(--zy-el-text-color-secondary);\r\n                font-size: calc(var(--zy-text-font-size) - 2px);\r\n              }\r\n\r\n              .GlobalChatMessagesFileDownload {\r\n                width: 100%;\r\n                height: 100%;\r\n                background: rgba(0, 0, 0, 0.2);\r\n                position: absolute;\r\n                top: 0;\r\n                right: 0;\r\n                z-index: 9;\r\n              }\r\n\r\n              .globalFileIcon {\r\n                width: 40px;\r\n                height: 40px;\r\n                vertical-align: middle;\r\n                position: absolute;\r\n                top: 12px;\r\n                right: 12px;\r\n\r\n                &.globalFileUnknown {\r\n                  background: url('../img/unknown.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePDF {\r\n                  background: url('../img/PDF.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileWord {\r\n                  background: url('../img/Word.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileExcel {\r\n                  background: url('../img/Excel.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePicture {\r\n                  background: url('../img/picture.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileVideo {\r\n                  background: url('../img/video.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileTXT {\r\n                  background: url('../img/TXT.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileCompress {\r\n                  background: url('../img/compress.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileWPS {\r\n                  background: url('../img/WPS.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePPT {\r\n                  background: url('../img/PPT.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n              }\r\n\r\n              .GlobalChatMessagesVoteTitleBody {\r\n                width: 100%;\r\n                display: flex;\r\n                align-items: flex-end;\r\n                justify-content: space-between;\r\n                padding: 0 2px 8px 2px;\r\n\r\n                .GlobalChatMessagesVoteTitle {\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  font-weight: bold;\r\n                  font-size: var(--zy-name-font-size);\r\n\r\n                  .GlobalChatMessagesVoteIcon {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    margin-right: 6px;\r\n\r\n                    svg {\r\n                      width: 16px;\r\n                      height: 16px;\r\n\r\n                      path {\r\n                        fill: var(--zy-el-color-warning);\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n\r\n                .GlobalChatMessagesVoteTime {\r\n                  font-size: var(--zy-text-font-size);\r\n                  color: var(--zy-el-text-color-secondary);\r\n                }\r\n              }\r\n\r\n              .GlobalChatMessagesVoteInfo {\r\n                width: 100%;\r\n                height: 92px;\r\n                display: flex;\r\n                align-items: center;\r\n                position: relative;\r\n                padding-top: 2px;\r\n\r\n                .GlobalChatMessagesVoteInfoIcon {\r\n                  width: 100%;\r\n                  height: 100%;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  position: absolute;\r\n                  top: 0;\r\n                  left: 0;\r\n                  z-index: 1;\r\n                }\r\n\r\n                .GlobalChatMessagesVoteName {\r\n                  width: 100%;\r\n                  height: calc((var(--zy-text-font-size) * var(--zy-line-height)) * 3);\r\n                  color: #fff;\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 3;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  font-size: var(--zy-text-font-size);\r\n                  line-height: var(--zy-line-height);\r\n                  padding: 0 72px 0 16px;\r\n                  overflow: hidden;\r\n                  position: relative;\r\n                  z-index: 2;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesFile {\r\n              width: 320px;\r\n              padding: 12px 16px;\r\n              display: flex;\r\n              flex-direction: column;\r\n              background: #fff;\r\n              position: relative;\r\n              padding-right: 58px;\r\n              border-radius: var(--el-border-radius-base);\r\n              border: 1px solid var(--zy-el-border-color-light);\r\n              word-wrap: break-word;\r\n              white-space: pre-wrap;\r\n              cursor: pointer;\r\n\r\n              span {\r\n                position: absolute;\r\n                width: 10px;\r\n                height: 10px;\r\n                z-index: 2;\r\n                top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);\r\n\r\n                &::after {\r\n                  content: '';\r\n                  position: absolute;\r\n                  width: 10px;\r\n                  height: 10px;\r\n                  transform: rotate(45deg);\r\n                  background: #fff;\r\n                  border: 1px solid var(--zy-el-border-color-light);\r\n                  box-sizing: border-box;\r\n                }\r\n              }\r\n\r\n              .GlobalChatMessagesFileName {\r\n                font-size: var(--zy-text-font-size);\r\n                line-height: var(--zy-line-height);\r\n                padding-bottom: var(--zy-font-text-distance-five);\r\n                word-break: break-all;\r\n              }\r\n\r\n              .GlobalChatMessagesFileSize {\r\n                color: var(--zy-el-text-color-secondary);\r\n                font-size: calc(var(--zy-text-font-size) - 2px);\r\n              }\r\n\r\n              .globalFileIcon {\r\n                width: 40px;\r\n                height: 40px;\r\n                vertical-align: middle;\r\n                position: absolute;\r\n                top: 12px;\r\n                right: 12px;\r\n\r\n                &.globalFileUnknown {\r\n                  background: url('../img/unknown.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePDF {\r\n                  background: url('../img/PDF.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileWord {\r\n                  background: url('../img/Word.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileExcel {\r\n                  background: url('../img/Excel.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePicture {\r\n                  background: url('../img/picture.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileVideo {\r\n                  background: url('../img/video.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileTXT {\r\n                  background: url('../img/TXT.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileCompress {\r\n                  background: url('../img/compress.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFileWPS {\r\n                  background: url('../img/WPS.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n\r\n                &.globalFilePPT {\r\n                  background: url('../img/PPT.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesCustomUnknown {\r\n              font-size: var(--zy-text-font-size);\r\n              line-height: var(--zy-line-height);\r\n            }\r\n          }\r\n        }\r\n\r\n        .GlobalChatMessages {\r\n          .GlobalChatMessagesInfo {\r\n            padding-right: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n\r\n            .GlobalChatMessagesText {\r\n              span {\r\n                left: 0;\r\n                transform: translate(-50%, -50%);\r\n\r\n                &::after {\r\n                  border-top-color: transparent !important;\r\n                  border-right-color: transparent !important;\r\n                }\r\n              }\r\n\r\n              .GlobalChatVoice {\r\n                padding: 0 26px;\r\n                position: relative;\r\n                cursor: pointer;\r\n\r\n                &::after {\r\n                  content: '';\r\n                  width: 26px;\r\n                  height: 19px;\r\n                  position: absolute;\r\n                  top: 50%;\r\n                  left: 0;\r\n                  transform: translateY(-50%);\r\n                  background: url('../img/record_receive.png') no-repeat;\r\n                  background-size: auto 19px;\r\n                  background-position: center left;\r\n                }\r\n\r\n                &.is-active {\r\n                  &::after {\r\n                    background: url('../img/record_receive_gif.gif') no-repeat;\r\n                    background-size: auto 19px;\r\n                    background-position: center left;\r\n                  }\r\n                }\r\n              }\r\n\r\n              .GlobalChatVoiceContinue {\r\n                width: 68px;\r\n                height: 20px;\r\n                font-size: 10px;\r\n                line-height: 20px;\r\n                position: absolute;\r\n                top: 50%;\r\n                left: 110%;\r\n                transform: translateY(-50%);\r\n                color: var(--zy-el-text-color-regular);\r\n                background: #e6e6e6;\r\n                text-align: center;\r\n                border-radius: 10px;\r\n                cursor: pointer;\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesCustom {\r\n              span {\r\n                left: 0;\r\n                transform: translate(-50%, -50%);\r\n\r\n                &::after {\r\n                  border-top-color: transparent !important;\r\n                  border-right-color: transparent !important;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesFile {\r\n              span {\r\n                left: 0;\r\n                transform: translate(-50%, -50%);\r\n\r\n                &::after {\r\n                  border-top-color: transparent !important;\r\n                  border-right-color: transparent !important;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .GlobalChatSelfMessages {\r\n          flex-direction: row-reverse;\r\n\r\n          .GlobalChatMessagesInfo {\r\n            justify-content: flex-end;\r\n            padding-left: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n\r\n            .GlobalChatMessagesText {\r\n              span {\r\n                right: 0;\r\n                transform: translate(50%, -50%);\r\n\r\n                &::after {\r\n                  border-left-color: transparent !important;\r\n                  border-bottom-color: transparent !important;\r\n                }\r\n              }\r\n\r\n              .GlobalChatVoice {\r\n                padding: 0 26px;\r\n                position: relative;\r\n                cursor: pointer;\r\n                text-align: right;\r\n\r\n                &::after {\r\n                  content: '';\r\n                  width: 26px;\r\n                  height: 19px;\r\n                  position: absolute;\r\n                  top: 50%;\r\n                  right: 0;\r\n                  transform: translateY(-50%) rotate(180deg);\r\n                  background: url('../img/record_receive.png') no-repeat;\r\n                  background-size: auto 19px;\r\n                  background-position: center left;\r\n                }\r\n\r\n                &.is-active {\r\n                  &::after {\r\n                    background: url('../img/record_receive_gif.gif') no-repeat;\r\n                    background-size: auto 19px;\r\n                    background-position: center left;\r\n                  }\r\n                }\r\n              }\r\n\r\n              .GlobalChatVoiceContinue {\r\n                width: 68px;\r\n                height: 20px;\r\n                font-size: 10px;\r\n                line-height: 20px;\r\n                position: absolute;\r\n                top: 50%;\r\n                right: 110%;\r\n                transform: translateY(-50%);\r\n                color: var(--zy-el-text-color-regular);\r\n                background: #e6e6e6;\r\n                text-align: center;\r\n                border-radius: 10px;\r\n                cursor: pointer;\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesCustom {\r\n              span {\r\n                right: 0;\r\n                transform: translate(50%, -50%);\r\n\r\n                &::after {\r\n                  border-left-color: transparent !important;\r\n                  border-bottom-color: transparent !important;\r\n                }\r\n              }\r\n            }\r\n\r\n            .GlobalChatMessagesFile {\r\n              span {\r\n                right: 0;\r\n                transform: translate(50%, -50%);\r\n\r\n                &::after {\r\n                  border-left-color: transparent !important;\r\n                  border-bottom-color: transparent !important;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatViewMenu {\r\n    position: fixed;\r\n    background: #fff;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border-radius: var(--el-border-radius-base);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n    padding: 6px;\r\n    z-index: 9;\r\n\r\n    .GlobalChatViewMenuItem {\r\n      min-width: 52px;\r\n      font-size: 12px;\r\n      padding: 3px 9px;\r\n      border-radius: 4px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background: #f8f8fa;\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewMenuLine {\r\n      width: 100%;\r\n      height: 7px;\r\n      position: relative;\r\n\r\n      &::after {\r\n        content: '';\r\n        width: calc(100% - 16px);\r\n        border-top: 1px solid var(--zy-el-border-color-light);\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalGroupNamePopupWindow {\r\n    .chat-popup-window-body {\r\n      height: 168px;\r\n    }\r\n  }\r\n\r\n  .GlobalGroupQrPopupWindow {\r\n    .chat-popup-window-body {\r\n      height: auto;\r\n    }\r\n  }\r\n\r\n  .GlobalGroupAnnouncementPopupWindow {\r\n    .chat-popup-window-body {\r\n      height: auto;\r\n    }\r\n  }\r\n\r\n  .GlobalGroupVotePopupWindow {\r\n    .chat-popup-window-body {\r\n      height: 82%;\r\n      max-height: 680px;\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatViewAutocomplete {\r\n  .GlobalChatViewMessagesItem {\r\n    width: 218px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 10px 0;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      background: #f9f9fa;\r\n    }\r\n\r\n    &.is-top {\r\n      background: #f6f6f6;\r\n    }\r\n\r\n    &.is-active {\r\n      background: #f2f2f2;\r\n    }\r\n\r\n    .zy-el-badge {\r\n      width: 42px;\r\n      height: 42px;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 42px;\r\n      height: 42px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .GlobalChatViewMessagesInfo {\r\n      width: calc(100% - 56px);\r\n      height: 42px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      line-height: normal;\r\n      position: relative;\r\n\r\n      .GlobalChatViewNotInform {\r\n        width: 16px;\r\n        height: 16px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        position: absolute;\r\n        right: 0;\r\n        bottom: 0;\r\n\r\n        path {\r\n          fill: var(--zy-el-text-color-secondary);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewMessagesName {\r\n        display: flex;\r\n\r\n        div {\r\n          flex: 1;\r\n          font-size: 14px;\r\n        }\r\n\r\n        span {\r\n          font-size: 12px;\r\n          color: var(--zy-el-text-color-secondary);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewNotInform+.GlobalChatViewMessagesText {\r\n        padding-right: 18px;\r\n      }\r\n\r\n      .GlobalChatViewMessagesText {\r\n        font-size: 12px;\r\n        color: var(--zy-el-text-color-secondary);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;+CAySA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,KAAK;AAClG,OAAO,KAAKC,SAAS,MAAM,uBAAuB;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,oBAAoB;AACpD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,IAAI,EAAEC,aAAa,QAAQ,yBAAyB;AAC7D;AACA,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,QAAQ,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,iBAAiB,QAAQ,yBAAyB;AAC9G,SACEC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,YAAY,EACZC,UAAU,QACL,eAAe;AACtB,SAASC,MAAM,EAAEC,OAAO,QAAQ,yBAAyB;AACzD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AAtBtD,IAAAC,WAAA,GAAe;EAAEzD,IAAI,EAAE;AAAiB,CAAC;;;;;;;;;;;;;;;;IAuBzC,IAAM0D,gBAAgB,GAAGxB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,wBAAwB,CAAC;IAAA,EAAC;IACrF,IAAMyB,eAAe,GAAGzB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;IAAA,EAAC;IACvG,IAAM0B,kBAAkB,GAAG1B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,iDAAiD,CAAC;IAAA,EAAC;IAChH,IAAM2B,WAAW,GAAG3B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,+BAA+B,CAAC;IAAA,EAAC;IACvF,IAAM4B,YAAY,GAAG5B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,iCAAiC,CAAC;IAAA,EAAC;IAC1F,IAAM6B,iBAAiB,GAAG7B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;IAAA,EAAC;IACzG,IAAM8B,kBAAkB,GAAG9B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,6CAA6C,CAAC;IAAA,EAAC;IAC5G,IAAM+B,kBAAkB,GAAG/B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,6CAA6C,CAAC;IAAA,EAAC;IAC5G,IAAMgC,eAAe,GAAGhC,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,uCAAuC,CAAC;IAAA,EAAC;IACnG,IAAMiC,aAAa,GAAGjC,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,mCAAmC,CAAC;IAAA,EAAC;IAC7F,IAAMkC,uBAAuB,GAAGlC,oBAAoB,CAAC;MAAA,OACnD,MAAM,CAAC,uDAAuD,CAAC;IAAA,CACjE,CAAC;IACD,IAAMmC,mBAAmB,GAAGnC,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,+CAA+C,CAAC;IAAA,EAAC;IAC/G,IAAMoC,oBAAoB,GAAGpC,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,iDAAiD,CAAC;IAAA,EAAC;IAClH,IAAMqC,eAAe,GAAGrC,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,uCAAuC,CAAC;IAAA,EAAC;IACnG,IAAMsC,gBAAgB,GAAGtC,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,wCAAwC,CAAC;IAAA,EAAC;IACrG,IAAMuC,iBAAiB,GAAGvC,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,yCAAyC,CAAC;IAAA,EAAC;IACvG,IAAMwC,KAAK,GAAGC,OAA2F;IACzG,IAAMC,IAAI,GAAGC,MAA6D;IAC1E,IAAMC,KAAK,IAAAC,gBAAA,GAAGC,MAAM,CAACC,QAAQ,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBD,KAAK;IACpC,IAAMI,UAAU,GAAGF,MAAM,CAACC,QAAQ,GAAG,IAAI,GAAG,KAAK;IACjD,IAAME,OAAO,GAAGvD,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMwD,MAAM,GAAGvD,QAAQ,CAAC;MACtBwD,GAAGA,CAAA,EAAI;QACL,OAAOX,KAAK,CAACY,UAAU;MACzB,CAAC;MACDC,GAAGA,CAAEhK,KAAK,EAAE;QACVqJ,IAAI,CAAC,mBAAmB,EAAErJ,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;IACF,IAAMiK,QAAQ,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC;IAC/C,IAAMC,QAAQ,GAAG5D,QAAQ,CAAC;MAAA,OAAM6C,KAAK,CAACe,QAAQ;IAAA,EAAC;IAC/C,IAAMC,MAAM,GAAG9D,GAAG,CAAC,IAAI,CAAC;IACxB,IAAM+D,QAAQ,GAAG/D,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAMgE,SAAS,GAAGhE,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMiE,WAAW,GAAGjE,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMkE,aAAa,GAAGlE,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAMmE,gBAAgB,GAAGnE,GAAG,CAAC,CAAC,CAAC,CAAC;IAChC,IAAMoE,YAAY,GAAGpE,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMqE,cAAc,GAAGrE,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMsE,iBAAiB,GAAGtE,GAAG,CAAC,CAAC,CAAC,CAAC;IACjC,IAAMuE,iBAAiB,GAAGvE,GAAG,CAAC,CAAC,CAAC,CAAC;IACjC,IAAMwE,kBAAkB,GAAGxE,GAAG,CAAC,EAAE,CAAC;IAClC,IAAMyE,YAAY,GAAGzE,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM0E,WAAW,GAAG1E,GAAG,CAAC,CAAC,CAAC;IAC1B,IAAM2E,YAAY,GAAG3E,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAM4E,YAAY,GAAG5E,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAM6E,YAAY,GAAG7E,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAM8E,cAAc,GAAG9E,GAAG,CAAC,KAAK,CAAC;IACjC,IAAM+E,eAAe,GAAG/E,GAAG,CAAC,CAAC,CAAC;IAC9B,IAAMgF,gBAAgB,GAAGhF,GAAG,CAAC,CAAC,CAAC;IAC/B,IAAMiF,gBAAgB,GAAGjF,GAAG,CAAC,CAAC,CAAC,CAAC;IAChC,IAAMkF,gBAAgB,GAAGlF,GAAG,CAAC,KAAK,CAAC;IACnC,IAAMmF,kBAAkB,GAAGnF,GAAG,CAAC,KAAK,CAAC;IACrC,IAAMoF,QAAQ,GAAGpF,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMqF,QAAQ,GAAGrF,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMsF,OAAO,GAAGtF,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMuF,OAAO,GAAGvF,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMwF,MAAM,GAAGxF,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMyF,eAAe,GAAGzF,GAAG,CAAC,KAAK,CAAC;IAClC,IAAM0F,MAAM,GAAG1F,GAAG,CAAC,EAAE,CAAC;IACtB,IAAM2F,OAAO,GAAG3F,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAM4F,OAAO,GAAG5F,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAM6F,QAAQ,GAAG7F,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAM8F,MAAM,GAAG9F,GAAG,CAAC,KAAK,CAAC;IACzB,IAAM+F,YAAY,GAAG/F,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMgG,gBAAgB,GAAGhG,GAAG,CAAC,KAAK,CAAC;IACnC,IAAMiG,YAAY,GAAGjG,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMkG,WAAW,GAAGlG,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAMmG,qBAAqB,GAAGnG,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMoG,uBAAuB,GAAGpG,GAAG,CAAC,KAAK,CAAC;IAC1C,IAAMqG,MAAM,GAAGrG,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMsG,WAAW,GAAGtG,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMuG,QAAQ,GAAGvG,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMwG,cAAc,GAAGxG,GAAG,CAAC,KAAK,CAAC;IACjC,IAAMyG,eAAe,GAAGzG,GAAG,CAAC,KAAK,CAAC;IAClC;IACA,IAAM0G,MAAM,GAAG,SAATA,MAAMA,CAAIC,GAAG;MAAA,OAAMA,GAAG,GAAG5G,GAAG,CAAC6G,OAAO,CAACD,GAAG,CAAC,GAAG5G,GAAG,CAAC8G,aAAa,CAAC,uBAAuB,CAAC;IAAA,CAAC;IAC7F,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAC/M,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAI4N,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9BtL,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAACuL,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,SAAS,GAAGnH,GAAG,CAAC,CAAC;IACvB,IAAMoH,SAAS,GAAGpH,GAAG,CAAC,CAAC;IACvB,IAAMqH,YAAY,GAAGrH,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAMsH,YAAY,GAAGtH,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAMuH,UAAU,GAAGvH,GAAG,CAAC,IAAI,CAAC;IAC5B,IAAMwH,gBAAgB,GAAGxH,GAAG,CAAC,EAAE,CAAC;IAChC,IAAMyH,oBAAoB,GAAGzH,GAAG,CAAC,EAAE,CAAC;IACpCE,SAAS,CAAC,YAAM,CAAE,CAAC,CAAC;IACpB,IAAMwH,aAAa,GAAG,SAAhBA,aAAaA,CAAIxO,CAAC,EAAK;MAC3B2L,YAAY,CAAClL,KAAK,GAAG,KAAK;MAC1BuL,gBAAgB,CAACvL,KAAK,GAAG,KAAK;MAC9BT,CAAC,CAACyO,cAAc,CAAC,CAAC;IACpB,CAAC;IACD,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAI1O,CAAC,EAAK;MACjC,IAAI4L,cAAc,CAACnL,KAAK,EAAE;QACxBmL,cAAc,CAACnL,KAAK,GAAG,KAAK;MAC9B,CAAC,MAAM;QACLkL,YAAY,CAAClL,KAAK,GAAG,KAAK;MAC5B;MACA,IAAIwL,kBAAkB,CAACxL,KAAK,EAAE;QAC5BwL,kBAAkB,CAACxL,KAAK,GAAG,KAAK;MAClC,CAAC,MAAM;QACLuL,gBAAgB,CAACvL,KAAK,GAAG,KAAK;MAChC;MACAT,CAAC,CAACyO,cAAc,CAAC,CAAC;IACpB,CAAC;IACD,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAIC,IAAI,EAAK;MAC7B,IAAIA,IAAI,CAACC,UAAU,KAAK,MAAM,EAAEzE,UAAU,GAAG0E,kBAAkB,CAACF,IAAI,CAACG,GAAG,EAAEH,IAAI,CAACI,IAAI,EAAE,KAAK,CAAC,GAAGC,aAAa,CAACL,IAAI,CAACI,IAAI,CAAC;MACtH,IAAIJ,IAAI,CAACC,UAAU,KAAK,MAAM,EAAEK,iBAAiB,CAACN,IAAI,CAACO,IAAI,CAAC;IAC9D,CAAC;IACD,IAAML,kBAAkB;MAAA,IAAAM,KAAA,GAAA5I,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkK,QAAON,GAAG,EAAEC,IAAI;QAAA,IAAAM,qBAAA;QAAA,IAAA1N,IAAA;UAAA2N,QAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,KAAA,GAAAjJ,SAAA;QAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAAqO,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAhK,IAAA,GAAAgK,QAAA,CAAA3L,IAAA;YAAA;cAAErC,IAAI,GAAA8N,KAAA,CAAA5K,MAAA,QAAA4K,KAAA,QAAAG,SAAA,GAAAH,KAAA,MAAG,KAAK;cAAA,OAAAJ,qBAAA,GACnDnE,cAAc,CAAC1K,KAAK,cAAA6O,qBAAA,eAApBA,qBAAA,CAAsBQ,QAAQ,CAACf,GAAG,CAAC;gBAAAa,QAAA,CAAA3L,IAAA;gBAAA;cAAA;cAAA,OAAA2L,QAAA,CAAA/L,MAAA;YAAA;cACnC0L,QAAQ,GAAGP,IAAI,CAACe,gBAAgB;cACpC,IAAI1E,iBAAiB,CAAC5K,KAAK,CAACsO,GAAG,CAAC,EAAE;gBAChCQ,QAAQ,GAAGlE,iBAAiB,CAAC5K,KAAK,CAACsO,GAAG,CAAC;cACzC,CAAC,MAAM;gBACLQ,QAAQ,GAAGvH,iBAAiB,CAACuH,QAAQ,EAAEjE,kBAAkB,CAAC7K,KAAK,CAAC;gBAChE4K,iBAAiB,CAAC5K,KAAK,CAACsO,GAAG,CAAC,GAAGQ,QAAQ;gBACvCjE,kBAAkB,CAAC7K,KAAK,CAACgE,IAAI,CAAC8K,QAAQ,CAAC;cACzC;cACMC,cAAc,GAAGlF,MAAM,CAAC7J,KAAK,GAAG,GAAG,GAAGiH,IAAI,CAACjH,KAAK,CAACuP,SAAS,GAAG,OAAO;cAAAJ,QAAA,CAAA3L,IAAA;cAAA,OACrDiG,MAAM,CAACC,QAAQ,CAAC8F,UAAU,CAACT,cAAc,EAAED,QAAQ,CAAC;YAAA;cAAnEE,MAAM,GAAAG,QAAA,CAAAlM,IAAA;cACZ,IAAI+L,MAAM,EAAE7N,IAAI,GAAGsO,0BAA0B,CAACX,QAAQ,CAAC,GAAGY,sBAAsB,CAACZ,QAAQ,CAAC;cAC1F,IAAI,CAACE,MAAM,EAAEW,0BAA0B,CAACrB,GAAG,EAAEC,IAAI,EAAEO,QAAQ,EAAE3N,IAAI,CAAC;YAAA;YAAA;cAAA,OAAAgO,QAAA,CAAA7J,IAAA;UAAA;QAAA,GAAAsJ,OAAA;MAAA,CACnE;MAAA,gBAdKP,kBAAkBA,CAAAuB,EAAA,EAAAC,GAAA;QAAA,OAAAlB,KAAA,CAAA1I,KAAA,OAAAD,SAAA;MAAA;IAAA,GAcvB;IACD,IAAM8J,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,aAAa,EAAEC,EAAE,EAAK;MAAA,IAAAC,oBAAA;MAChD,IAAIF,aAAa,aAAbA,aAAa,gBAAAE,oBAAA,GAAbF,aAAa,CAAEG,KAAK,cAAAD,oBAAA,eAApBA,oBAAA,CAAsBE,gBAAgB,EAAE;QAC1C,IAAMC,QAAQ,GAAG,CAAEL,aAAa,CAACM,MAAM,GAAGN,aAAa,CAACO,KAAK,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;QAChF5F,iBAAiB,CAAC3K,KAAK,CAACgQ,EAAE,CAAC,GAAG,GAAG,GAAGQ,QAAQ,CAACJ,QAAQ,CAAC,GAAG,GAAG;MAC9D;IACF,CAAC;IACD,IAAMT,0BAA0B;MAAA,IAAAc,KAAA,GAAA1K,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgM,SAAOpC,GAAG,EAAEC,IAAI,EAAEO,QAAQ;QAAA,IAAA3N,IAAA;UAAAwP,GAAA;UAAA5B,cAAA;UAAA6B,OAAA;UAAAC,MAAA,GAAA7K,SAAA;QAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAAiQ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5L,IAAA,GAAA4L,SAAA,CAAAvN,IAAA;YAAA;cAAErC,IAAI,GAAA0P,MAAA,CAAAxM,MAAA,QAAAwM,MAAA,QAAAzB,SAAA,GAAAyB,MAAA,MAAG,KAAK;cACzEnG,cAAc,CAAC1K,KAAK,CAACgE,IAAI,CAACsK,GAAG,CAAC;cAC9B3D,iBAAiB,CAAC3K,KAAK,CAACsO,GAAG,CAAC,GAAG,MAAM;cAAAyC,SAAA,CAAAvN,IAAA;cAAA,OACnB4C,GAAG,CAACuJ,0BAA0B,CAACrB,GAAG,EAAEC,IAAI,CAACyB,EAAE,EAAE,CAAC,CAAC,EAAEF,kBAAkB,CAAC;YAAA;cAAhFa,GAAG,GAAAI,SAAA,CAAA9N,IAAA;cACTyH,cAAc,CAAC1K,KAAK,GAAG0K,cAAc,CAAC1K,KAAK,CAACgR,MAAM,CAAC,UAAChP,CAAC;gBAAA,OAAKA,CAAC,KAAKsM,GAAG;cAAA,EAAC;cACpE,OAAO3D,iBAAiB,CAAC3K,KAAK,CAACsO,GAAG,CAAC;cAC7BS,cAAc,GAAGlF,MAAM,CAAC7J,KAAK,GAAG,GAAG,GAAGiH,IAAI,CAACjH,KAAK,CAACuP,SAAS,GAAG,OAAO;cAAAwB,SAAA,CAAAvN,IAAA;cAAA,OACpDiG,MAAM,CAACC,QAAQ,CAACuH,QAAQ,CAAClC,cAAc,EAAED,QAAQ,EAAE6B,GAAG,CAAC;YAAA;cAAvEC,OAAO,GAAAG,SAAA,CAAA9N,IAAA;cACb,IAAI2N,OAAO,CAACzP,IAAI,KAAK,SAAS,EAAEA,IAAI,GAAGsO,0BAA0B,CAACX,QAAQ,CAAC,GAAGY,sBAAsB,CAACZ,QAAQ,CAAC;cAC9G,IAAI8B,OAAO,CAACzP,IAAI,KAAK,OAAO,EAAE6G,SAAS,CAAC;gBAAE7G,IAAI,EAAE,OAAO;gBAAE+P,OAAO,EAAEN,OAAO,CAACM;cAAQ,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAzL,IAAA;UAAA;QAAA,GAAAoL,QAAA;MAAA,CACrF;MAAA,gBAVKf,0BAA0BA,CAAAwB,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAZ,KAAA,CAAAxK,KAAA,OAAAD,SAAA;MAAA;IAAA,GAU/B;IACD,IAAM0J,sBAAsB;MAAA,IAAA4B,KAAA,GAAAvL,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6M,SAAOzC,QAAQ;QAAA,IAAAC,cAAA,EAAAyC,OAAA;QAAA,OAAAlS,mBAAA,GAAAuB,IAAA,UAAA4Q,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvM,IAAA,GAAAuM,SAAA,CAAAlO,IAAA;YAAA;cACtCuL,cAAc,GAAGlF,MAAM,CAAC7J,KAAK,GAAG,GAAG,GAAGiH,IAAI,CAACjH,KAAK,CAACuP,SAAS,GAAG,OAAO;cAAAmC,SAAA,CAAAlO,IAAA;cAAA,OACpDiG,MAAM,CAACC,QAAQ,CAACiI,QAAQ,CAAC5C,cAAc,EAAED,QAAQ,CAAC;YAAA;cAAlE0C,OAAO,GAAAE,SAAA,CAAAzO,IAAA;cACb,IAAIuO,OAAO,CAACrQ,IAAI,KAAK,OAAO,EAAE6G,SAAS,CAAC;gBAAE7G,IAAI,EAAE,OAAO;gBAAE+P,OAAO,EAAEM,OAAO,CAACN;cAAQ,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAQ,SAAA,CAAApM,IAAA;UAAA;QAAA,GAAAiM,QAAA;MAAA,CACrF;MAAA,gBAJK7B,sBAAsBA,CAAAkC,GAAA;QAAA,OAAAN,KAAA,CAAArL,KAAA,OAAAD,SAAA;MAAA;IAAA,GAI3B;IACD,IAAMyJ,0BAA0B;MAAA,IAAAoC,KAAA,GAAA9L,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAoN,SAAOhD,QAAQ;QAAA,IAAAC,cAAA,EAAAyC,OAAA;QAAA,OAAAlS,mBAAA,GAAAuB,IAAA,UAAAkR,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7M,IAAA,GAAA6M,SAAA,CAAAxO,IAAA;YAAA;cAC1CuL,cAAc,GAAGlF,MAAM,CAAC7J,KAAK,GAAG,GAAG,GAAGiH,IAAI,CAACjH,KAAK,CAACuP,SAAS,GAAG,OAAO;cAAAyC,SAAA,CAAAxO,IAAA;cAAA,OACpDiG,MAAM,CAACC,QAAQ,CAACuI,oBAAoB,CAAC,cAAclD,cAAc,EAAE,EAAED,QAAQ,CAAC;YAAA;cAA9F0C,OAAO,GAAAQ,SAAA,CAAA/O,IAAA;cACb,IAAIuO,OAAO,CAACrQ,IAAI,KAAK,OAAO,EAAE6G,SAAS,CAAC;gBAAE7G,IAAI,EAAE,OAAO;gBAAE+P,OAAO,EAAEM,OAAO,CAACN;cAAQ,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAc,SAAA,CAAA1M,IAAA;UAAA;QAAA,GAAAwM,QAAA;MAAA,CACrF;MAAA,gBAJKrC,0BAA0BA,CAAAyC,GAAA;QAAA,OAAAL,KAAA,CAAA5L,KAAA,OAAAD,SAAA;MAAA;IAAA,GAI/B;IACD,IAAMmM,sBAAsB;MAAA,IAAAC,KAAA,GAAArM,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2N,SAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAAA,IAAAjE,GAAA,EAAAC,IAAA;QAAA,OAAAjP,mBAAA,GAAAuB,IAAA,UAAA2R,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtN,IAAA,GAAAsN,SAAA,CAAAjP,IAAA;YAAA;cACvB8K,GAAG,IAAAgE,qBAAA,GAAGhH,gBAAgB,CAACtL,KAAK,cAAAsS,qBAAA,uBAAtBA,qBAAA,CAAwBhE,GAAG;cACjCC,IAAI,IAAAgE,sBAAA,GAAGjH,gBAAgB,CAACtL,KAAK,cAAAuS,sBAAA,uBAAtBA,sBAAA,CAAwBhE,IAAI;cACzCF,kBAAkB,CAACC,GAAG,EAAEC,IAAI,EAAE,IAAI,CAAC;YAAA;YAAA;cAAA,OAAAkE,SAAA,CAAAnN,IAAA;UAAA;QAAA,GAAA+M,QAAA;MAAA,CACpC;MAAA,gBAJKF,sBAAsBA,CAAA;QAAA,OAAAC,KAAA,CAAAnM,KAAA,OAAAD,SAAA;MAAA;IAAA,GAI3B;IACD,IAAM0M,wBAAwB;MAAA,IAAAC,KAAA,GAAA5M,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkO,SAAOC,QAAQ,EAAEC,IAAI;QAAA,IAAAhE,QAAA,EAAAiE,WAAA,EAAApC,GAAA;QAAA,OAAArR,mBAAA,GAAAuB,IAAA,UAAAmS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9N,IAAA,GAAA8N,SAAA,CAAAzP,IAAA;YAAA;cAC9CsL,QAAQ,GAAG+D,QAAQ,GAAG,GAAG,GAAG5L,IAAI,CAACjH,KAAK,CAACuP,SAAS,GAAG,aAAa;cAChEwD,WAAW,GAAGjM,KAAK,CAACoM,UAAU,CAACJ,IAAI,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;cAAAG,SAAA,CAAAzP,IAAA;cAAA,OAChEiG,MAAM,CAACC,QAAQ,CAACyJ,cAAc,CAAC,aAAa,EAAErE,QAAQ,EAAEiE,WAAW,CAAC;YAAA;cAAhFpC,GAAG,GAAAsC,SAAA,CAAAhQ,IAAA;cACTmQ,OAAO,CAACC,GAAG,CAAC1C,GAAG,CAAC;YAAA;YAAA;cAAA,OAAAsC,SAAA,CAAA3N,IAAA;UAAA;QAAA,GAAAsN,QAAA;MAAA,CACjB;MAAA,gBALKF,wBAAwBA,CAAAY,GAAA,EAAAC,GAAA;QAAA,OAAAZ,KAAA,CAAA1M,KAAA,OAAAD,SAAA;MAAA;IAAA,GAK7B;IACD,IAAMwN,wBAAwB;MAAA,IAAAC,KAAA,GAAA1N,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgP,SAAOb,QAAQ;QAAA,IAAA/D,QAAA,EAAA6B,GAAA,EAAAgD,SAAA,EAAAC,UAAA,EAAAC,GAAA,EAAA7T,KAAA;QAAA,OAAAV,mBAAA,GAAAuB,IAAA,UAAAiT,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5O,IAAA,GAAA4O,SAAA,CAAAvQ,IAAA;YAAA;cACxCsL,QAAQ,GAAG+D,QAAQ,GAAG,GAAG,GAAG5L,IAAI,CAACjH,KAAK,CAACuP,SAAS,GAAG,aAAa;cAAAwE,SAAA,CAAAvQ,IAAA;cAAA,OACpDiG,MAAM,CAACC,QAAQ,CAACsK,cAAc,CAAC,aAAa,EAAElF,QAAQ,CAAC;YAAA;cAAnE6B,GAAG,GAAAoD,SAAA,CAAA9Q,IAAA;cACT,IAAI0N,GAAG,CAACxP,IAAI,KAAK,SAAS,EAAE;gBACpBwS,SAAS,GAAGM,IAAI,CAACC,KAAK,CAACpN,KAAK,CAACqN,UAAU,CAACxD,GAAG,CAACmC,IAAI,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;gBAC1Fc,UAAU,GAAG,EAAE;gBACrB,KAAWC,GAAG,IAAIF,SAAS,EAAE;kBAC3B,IAAIjU,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAACsS,SAAS,EAAEE,GAAG,CAAC,EAAE;oBAClD7T,KAAK,GAAG2T,SAAS,CAACE,GAAG,CAAC;oBAC5BD,UAAU,CAAC5P,IAAI,CAAChE,KAAK,CAAC;kBACxB;gBACF;gBACA4K,iBAAiB,CAAC5K,KAAK,GAAG2T,SAAS;gBACnC9I,kBAAkB,CAAC7K,KAAK,GAAG4T,UAAU;cACvC;YAAC;YAAA;cAAA,OAAAG,SAAA,CAAAzO,IAAA;UAAA;QAAA,GAAAoO,QAAA;MAAA,CACF;MAAA,gBAfKF,wBAAwBA,CAAAY,IAAA;QAAA,OAAAX,KAAA,CAAAxN,KAAA,OAAAD,SAAA;MAAA;IAAA,GAe7B;IACD,IAAMwI,aAAa,GAAG,SAAhBA,aAAaA,CAAI6F,GAAG,EAAK;MAC7B,IAAI,CAACA,GAAG,EAAE;MACVrN,kBAAkB,CAAC;QACjBvC,IAAI,EAAE6P,OAAO,CAACC,GAAG,CAACC,YAAY;QAC9BC,MAAM,EAAEJ,GAAG,CAACrE,EAAE;QACd0E,QAAQ,EAAEL,GAAG,CAACM,OAAO;QACrB7F,QAAQ,EAAEuF,GAAG,CAAC/E,gBAAgB;QAC9BsF,QAAQ,EAAEP,GAAG,CAACO;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,WAAW,EAAEC,EAAE,EAAK;MACvC,IAAMC,OAAO,GAAGF,WAAW,GACvB5K,QAAQ,CAAClK,KAAK,CAACgR,MAAM,CAAC,UAAChP,CAAC;QAAA,IAAAiT,iBAAA;QAAA,QAAAA,iBAAA,GAAKjT,CAAC,CAACkT,cAAc,cAAAD,iBAAA,gBAAAA,iBAAA,GAAhBA,iBAAA,CAAkBxQ,IAAI,cAAAwQ,iBAAA,uBAAtBA,iBAAA,CAAwBE,WAAW,CAAC,CAAC,CAAC9F,QAAQ,CAACyF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,WAAW,CAAC,CAAC,CAAC;MAAA,EAAC,GACxG,EAAE;MACNJ,EAAE,CAACC,OAAO,CAAC;IACb,CAAC;IACD,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB5H,SAAS,CAACxN,KAAK,CAACqV,OAAO,CAACC,SAAS,GAAG9H,SAAS,CAACxN,KAAK,CAACqV,OAAO,CAAC3H,YAAY;IAC1E,CAAC;IACD,IAAM6H,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B/H,SAAS,CAACxN,KAAK,CAACqV,OAAO,CAACC,SAAS,GAAG9H,SAAS,CAACxN,KAAK,CAACqV,OAAO,CAAC3H,YAAY,GAAGA,YAAY,CAAC1N,KAAK;IAC/F,CAAC;IACD,IAAMwV,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAAC,KAAA,EAAsB;MAAA,IAAhBH,SAAS,GAAAG,KAAA,CAATH,SAAS;MACvC3H,YAAY,CAAC3N,KAAK,GAAGsV,SAAS;MAC9B,IAAIA,SAAS,KAAK,CAAC,EAAE;QAAA,IAAAI,qBAAA;QACnBhI,YAAY,CAAC1N,KAAK,GAAGwN,SAAS,CAACxN,KAAK,CAACqV,OAAO,CAAC3H,YAAY;QACzDiI,kBAAkB,CAAC,EAAAD,qBAAA,GAAA7H,gBAAgB,CAAC7N,KAAK,CAAC,CAAC,CAAC,cAAA0V,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,KAAI,CAAC,CAAC;MAC9D;IACF,CAAC;IACD,IAAMC,WAAW;MAAA,IAAAC,MAAA,GAAA/P,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqR,SAAO5H,IAAI;QAAA,OAAA7O,mBAAA,GAAAuB,IAAA,UAAAmV,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9Q,IAAA,GAAA8Q,SAAA,CAAAzS,IAAA;YAAA;cAAA,MACzBqG,MAAM,CAAC7J,KAAK,KAAKmO,IAAI,CAAC6B,EAAE;gBAAAiG,SAAA,CAAAzS,IAAA;gBAAA;cAAA;cAAA,OAAAyS,SAAA,CAAA7S,MAAA;YAAA;cAC5B+G,MAAM,CAACnK,KAAK,GAAG,IAAI;cACnB6J,MAAM,CAAC7J,KAAK,GAAGmO,IAAI,CAAC6B,EAAE;YAAA;YAAA;cAAA,OAAAiG,SAAA,CAAA3Q,IAAA;UAAA;QAAA,GAAAyQ,QAAA;MAAA,CACvB;MAAA,gBAJKF,WAAWA,CAAAK,IAAA;QAAA,OAAAJ,MAAA,CAAA7P,KAAA,OAAAD,SAAA;MAAA;IAAA,GAIhB;IACD,IAAMmQ,eAAe;MAAA,IAAAC,MAAA,GAAArQ,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2R,SAAOlI,IAAI,EAAEhN,IAAI;QAAA,IAAAmV,gBAAA;QAAA,OAAAhX,mBAAA,GAAAuB,IAAA,UAAA0V,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArR,IAAA,GAAAqR,SAAA,CAAAhT,IAAA;YAAA;cACvC+I,WAAW,CAACvM,KAAK,GAAG,KAAK;cACzBoK,QAAQ,CAACpK,KAAK,GAAGmO,IAAI;cACrBT,YAAY,CAAC1N,KAAK,GAAG,CAAC;cACtB6N,gBAAgB,CAAC7N,KAAK,GAAG,EAAE;cAC3ByK,YAAY,CAACzK,KAAK,GAAG,EAAE;cACvB0K,cAAc,CAAC1K,KAAK,GAAG,EAAE;cACzB2K,iBAAiB,CAAC3K,KAAK,GAAG,CAAC,CAAC;cAC5B,IAAI,CAACmB,IAAI,EAAE;gBACT,CAAAmV,gBAAA,GAAA7I,SAAS,CAACzN,KAAK,cAAAsW,gBAAA,eAAfA,gBAAA,CAAiBG,YAAY,CAAC,CAAC;gBAC/BjK,qBAAqB,CAACxM,KAAK,GAAG,EAAE;gBAChCyM,uBAAuB,CAACzM,KAAK,GAAG,KAAK;cACvC;cACA2V,kBAAkB,CAAC,CAAC;cACpB,IAAI,CAACxH,IAAI,CAACuI,WAAW,EAAEC,yBAAyB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAlR,IAAA;UAAA;QAAA,GAAA+Q,QAAA;MAAA,CACnD;MAAA,gBAfKF,eAAeA,CAAAS,IAAA,EAAAC,IAAA;QAAA,OAAAT,MAAA,CAAAnQ,KAAA,OAAAD,SAAA;MAAA;IAAA,GAepB;IACD,IAAM8Q,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B7O,YAAY,CAAC8O,OAAO,CAAC,wBAAwB,EAAE,IAAI,EAAE;QACnDC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtB9V,IAAI,EAAE;MACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;QACVwU,4BAA4B,CAAC,CAAC;MAChC,CAAC,CAAC,CACDvR,KAAK,CAAC,YAAM;QACXqC,SAAS,CAAC;UAAE7G,IAAI,EAAE,MAAM;UAAE+P,OAAO,EAAE;QAAQ,CAAC,CAAC;MAC/C,CAAC,CAAC;IACN,CAAC;IACD,IAAMgG,4BAA4B;MAAA,IAAAC,MAAA,GAAApR,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0S,UAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA,EAAAC,GAAA;QAAA,OAAAjY,mBAAA,GAAAuB,IAAA,UAAA2W,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtS,IAAA,GAAAsS,UAAA,CAAAjU,IAAA;YAAA;cAAAiU,UAAA,CAAAjU,IAAA;cAAA,OACPoD,SAAS,CAACsQ,4BAA4B,CAAC,CAAC;YAAA;cAAAG,qBAAA,GAAAI,UAAA,CAAAxU,IAAA;cAA5DqU,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAAEC,GAAG,GAAAF,qBAAA,CAAHE,GAAG;cACjB,IAAID,IAAI,KAAK,CAAC,EAAE;gBACdI,aAAa,CAAC,CAAC;cACjB,CAAC,MAAM;gBACLtE,OAAO,CAACC,GAAG,CAACiE,IAAI,EAAEC,GAAG,CAAC;cACxB;YAAC;YAAA;cAAA,OAAAE,UAAA,CAAAnS,IAAA;UAAA;QAAA,GAAA8R,SAAA;MAAA,CACF;MAAA,gBAPKF,4BAA4BA,CAAA;QAAA,OAAAC,MAAA,CAAAlR,KAAA,OAAAD,SAAA;MAAA;IAAA,GAOjC;IACD,IAAM2Q,yBAAyB;MAAA,IAAAgB,MAAA,GAAA5R,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkT,UAAOzW,IAAI;QAAA,IAAA0W,sBAAA,EAAAP,IAAA,EAAAC,GAAA;QAAA,OAAAjY,mBAAA,GAAAuB,IAAA,UAAAiX,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5S,IAAA,GAAA4S,UAAA,CAAAvU,IAAA;YAAA;cAAAuU,UAAA,CAAAvU,IAAA;cAAA,OACfoD,SAAS,CAAC+P,yBAAyB,CAAC;gBAC9DqB,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;gBACrC8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;cAC3B,CAAC,CAAC;YAAA;cAAAJ,sBAAA,GAAAE,UAAA,CAAA9U,IAAA;cAHMqU,IAAI,GAAAO,sBAAA,CAAJP,IAAI;cAAEC,GAAG,GAAAM,sBAAA,CAAHN,GAAG;cAIjB,IAAID,IAAI,KAAK,CAAC,EAAE;gBACd,IAAI,CAACnW,IAAI,EAAEuW,aAAa,CAAC,CAAC;cAC5B,CAAC,MAAM;gBACLtE,OAAO,CAACC,GAAG,CAACiE,IAAI,EAAEC,GAAG,CAAC;cACxB;YAAC;YAAA;cAAA,OAAAQ,UAAA,CAAAzS,IAAA;UAAA;QAAA,GAAAsS,SAAA;MAAA,CACF;MAAA,gBAVKjB,yBAAyBA,CAAAuB,IAAA;QAAA,OAAAP,MAAA,CAAA1R,KAAA,OAAAD,SAAA;MAAA;IAAA,GAU9B;IACD,IAAM2P,kBAAkB;MAAA,IAAAwC,MAAA,GAAApS,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0T,UAAA;QAAA,IAAAC,SAAA;UAAAC,MAAA;UAAAC,YAAA;UAAAC,qBAAA;UAAAlB,IAAA;UAAAxE,IAAA;UAAAyE,GAAA;UAAAkB,OAAA,GAAAzS,SAAA;QAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAA6X,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxT,IAAA,GAAAwT,UAAA,CAAAnV,IAAA;YAAA;cAAO6U,SAAS,GAAAI,OAAA,CAAApU,MAAA,QAAAoU,OAAA,QAAArJ,SAAA,GAAAqJ,OAAA,MAAG,CAAC;cACvCH,MAAM,GAAG;gBAAED,SAAS;gBAAEO,KAAK,EAAE,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAC;cAC3CN,YAAY,GAAG;gBAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;gBAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;cAAS,CAAC;cAAAU,UAAA,CAAAnV,IAAA;cAAA,OAC/DoD,SAAS,CAAC+O,kBAAkB,CAAC4C,YAAY,EAAED,MAAM,CAAC;YAAA;cAAAE,qBAAA,GAAAG,UAAA,CAAA1V,IAAA;cAA5EqU,IAAI,GAAAkB,qBAAA,CAAJlB,IAAI;cAAExE,IAAI,GAAA0F,qBAAA,CAAJ1F,IAAI;cAAEyE,GAAG,GAAAiB,qBAAA,CAAHjB,GAAG;cAAA,MACnBD,IAAI,KAAK,CAAC;gBAAAqB,UAAA,CAAAnV,IAAA;gBAAA;cAAA;cAAAmV,UAAA,CAAAnV,IAAA;cAAA,OACNsV,UAAU,CAAC,CAAC;YAAA;cAClBlL,UAAU,CAAC5N,KAAK,GAAGqY,SAAS,KAAK,CAAC;cAClCU,cAAc,CAACjG,IAAI,CAACkG,IAAI,EAAEX,SAAS,KAAK,CAAC,CAAC;cAAAM,UAAA,CAAAnV,IAAA;cAAA;YAAA;cAE1C4P,OAAO,CAACC,GAAG,CAACiE,IAAI,EAAEC,GAAG,CAAC;YAAA;YAAA;cAAA,OAAAoB,UAAA,CAAArT,IAAA;UAAA;QAAA,GAAA8S,SAAA;MAAA,CAEzB;MAAA,gBAXKzC,kBAAkBA,CAAA;QAAA,OAAAwC,MAAA,CAAAlS,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWvB;IACD,IAAMiT,iBAAiB;MAAA,IAAAC,MAAA,GAAAnT,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAyU,UAAA;QAAA,IAAAb,MAAA,EAAAC,YAAA,EAAAa,sBAAA,EAAA9B,IAAA,EAAAxE,IAAA,EAAAyE,GAAA;QAAA,OAAAjY,mBAAA,GAAAuB,IAAA,UAAAwY,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnU,IAAA,GAAAmU,UAAA,CAAA9V,IAAA;YAAA;cAClB8U,MAAM,GAAG;gBAAED,SAAS,EAAEjO,QAAQ,CAACpK,KAAK,CAAC4V,QAAQ;gBAAEgD,KAAK,EAAE,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAC;cACpEN,YAAY,GAAG;gBAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;gBAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;cAAS,CAAC;cAAAqB,UAAA,CAAA9V,IAAA;cAAA,OAC/DoD,SAAS,CAAC+O,kBAAkB,CAAC4C,YAAY,EAAED,MAAM,CAAC;YAAA;cAAAc,sBAAA,GAAAE,UAAA,CAAArW,IAAA;cAA5EqU,IAAI,GAAA8B,sBAAA,CAAJ9B,IAAI;cAAExE,IAAI,GAAAsG,sBAAA,CAAJtG,IAAI;cAAEyE,GAAG,GAAA6B,sBAAA,CAAH7B,GAAG;cACvB,IAAID,IAAI,KAAK,CAAC,EAAE;gBACd1J,UAAU,CAAC5N,KAAK,GAAG,IAAI;gBACvB2W,yBAAyB,CAAC,IAAI,CAAC;gBAC/BoC,cAAc,CAACjG,IAAI,CAACkG,IAAI,EAAE,IAAI,CAAC;cACjC,CAAC,MAAM;gBACL5F,OAAO,CAACC,GAAG,CAACiE,IAAI,EAAEC,GAAG,CAAC;cACxB;YAAC;YAAA;cAAA,OAAA+B,UAAA,CAAAhU,IAAA;UAAA;QAAA,GAAA6T,SAAA;MAAA,CACF;MAAA,gBAXKF,iBAAiBA,CAAA;QAAA,OAAAC,MAAA,CAAAjT,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWtB;IACD,IAAMuT,aAAa;MAAA,IAAAC,MAAA,GAAAzT,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+U,UAAOzJ,EAAE;QAAA,IAAA0J,qBAAA,EAAA5G,IAAA,EAAA6G,iBAAA,EAAAC,qBAAA;QAAA,OAAAta,mBAAA,GAAAuB,IAAA,UAAAgZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3U,IAAA,GAAA2U,UAAA,CAAAtW,IAAA;YAAA;cAAAsW,UAAA,CAAAtW,IAAA;cAAA,OACN4C,GAAG,CAACmT,aAAa,CAAC;gBAAEQ,QAAQ,EAAE/J,EAAE,CAAC3K,KAAK,CAAC6B,aAAa,CAAClH,KAAK,CAACqE,MAAM;cAAE,CAAC,CAAC;YAAA;cAAAqV,qBAAA,GAAAI,UAAA,CAAA7W,IAAA;cAApF6P,IAAI,GAAA4G,qBAAA,CAAJ5G,IAAI;cACN6G,iBAAiB,GAAG1F,IAAI,CAACC,KAAK,CAAC8F,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,CAAC;cACrFL,qBAAqB,GAAGD,iBAAiB,CAAC9Z,cAAc,CAACmQ,EAAE,CAAC,GAC9D2J,iBAAiB,CAAC3J,EAAE,CAAC,GACrB;gBAAEkK,IAAI,EAAE,IAAI;gBAAEC,SAAS,EAAE;cAAG,CAAC;cACjC,IAAIrH,IAAI,CAACqH,SAAS,IAAIP,qBAAqB,CAACO,SAAS,KAAKrH,IAAI,CAACqH,SAAS,EAAE;gBACxE3N,qBAAqB,CAACxM,KAAK,GAAG8S,IAAI,CAACqH,SAAS;gBAC5C1N,uBAAuB,CAACzM,KAAK,GAAG,IAAI;cACtC;cAAC,OAAA8Z,UAAA,CAAA1W,MAAA,WACM0P,IAAI;YAAA;YAAA;cAAA,OAAAgH,UAAA,CAAAxU,IAAA;UAAA;QAAA,GAAAmU,SAAA;MAAA,CACZ;MAAA,gBAXKF,aAAaA,CAAAa,IAAA;QAAA,OAAAZ,MAAA,CAAAvT,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWlB;IACD,IAAMqU,2BAA2B,GAAG,SAA9BA,2BAA2BA,CAAA,EAAS;MACxC,IAAMV,iBAAiB,GAAG1F,IAAI,CAACC,KAAK,CAAC8F,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,CAAC;MAC3F,IAAIK,oBAAoB,GAAAC,aAAA,KAAQZ,iBAAiB,CAAE;MACnDW,oBAAoB,CAAClQ,QAAQ,CAACpK,KAAK,CAACiY,QAAQ,CAAC,GAAG;QAAEiC,IAAI,EAAE,KAAK;QAAEC,SAAS,EAAE3N,qBAAqB,CAACxM;MAAM,CAAC;MACvGga,YAAY,CAACQ,OAAO,CAAC,yBAAyB,EAAEvG,IAAI,CAACwG,SAAS,CAACH,oBAAoB,CAAC,CAAC;MACrF9N,qBAAqB,CAACxM,KAAK,GAAG,EAAE;MAChCyM,uBAAuB,CAACzM,KAAK,GAAG,KAAK;IACvC,CAAC;IACD,IAAM0a,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,IAAI,EAAEC,IAAI,EAAK;MAC3C;MACA,IAAID,IAAI,CAACtW,MAAM,KAAKuW,IAAI,CAACvW,MAAM,EAAE,OAAO,KAAK;MAC7C;MACA,IAAMwW,UAAU,GAAGC,kBAAA,CAAIH,IAAI,EAAEI,IAAI,CAAC,CAAC;MACnC,IAAMC,UAAU,GAAGF,kBAAA,CAAIF,IAAI,EAAEG,IAAI,CAAC,CAAC;MACnC;MACA,KAAK,IAAI9a,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4a,UAAU,CAACxW,MAAM,EAAEpE,CAAC,EAAE,EAAE;QAC1C,IAAI4a,UAAU,CAAC5a,CAAC,CAAC,KAAK+a,UAAU,CAAC/a,CAAC,CAAC,EAAE;UACnC,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC;IAED,IAAM6Y,UAAU;MAAA,IAAAmC,MAAA,GAAAlV,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwW,UAAA;QAAA,IAAAC,UAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,KAAA,EAAApN,IAAA,EAAAqN,eAAA,EAAAC,gBAAA;QAAA,OAAAnc,mBAAA,GAAAuB,IAAA,UAAA6a,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxW,IAAA,GAAAwW,UAAA,CAAAnY,IAAA;YAAA;cACb2X,UAAU,GAAG,CAAC,CAAC;cAAA,MACf/Q,QAAQ,CAACpK,KAAK,CAACmB,IAAI,KAAK,CAAC;gBAAAwa,UAAA,CAAAnY,IAAA;gBAAA;cAAA;cAAAmY,UAAA,CAAAnY,IAAA;cAAA,OACA+V,aAAa,CAACnP,QAAQ,CAACpK,KAAK,CAACiY,QAAQ,CAAC;YAAA;cAA3DmD,YAAY,GAAAO,UAAA,CAAA1Y,IAAA;cAClB,IAAI,CAACmY,YAAY,CAACpL,EAAE,EAAE;gBACpB/H,YAAY,CAAC2T,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE;kBACnC5E,iBAAiB,EAAE,IAAI;kBACvB6E,QAAQ,EAAE,SAAVA,QAAQA,CAAA,EAAQ;oBACdnO,YAAY,CAAC1N,KAAK,GAAG,CAAC;oBACtB6N,gBAAgB,CAAC7N,KAAK,GAAG,EAAE;oBAC3B8N,oBAAoB,CAAC9N,KAAK,GAAG,EAAE;oBAC/BiL,YAAY,CAACjL,KAAK,GAAGoK,QAAQ,CAACpK,KAAK;oBACnC8b,aAAa,CAAC,CAAC;kBACjB;gBACF,CAAC,CAAC;cACJ;cACMT,WAAW,GAAGX,oBAAoB,CACtCU,YAAY,CAACW,aAAa,EAC1B1R,SAAS,CAACrK,KAAK,CAACgc,GAAG,CAAC,UAACha,CAAC;gBAAA,OAAKA,CAAC,CAACuN,SAAS;cAAA,EACxC,CAAC;cAAA,IACI8L,WAAW;gBAAAM,UAAA,CAAAnY,IAAA;gBAAA;cAAA;cAAAmY,UAAA,CAAAnY,IAAA;cAAA,OACU2D,mBAAmB,CAACiD,QAAQ,CAACpK,KAAK,CAACiY,QAAQ,CAAC5S,KAAK,CAAC6B,aAAa,CAAClH,KAAK,CAACqE,MAAM,CAAC,CAAC;YAAA;cAAtGgG,SAAS,CAACrK,KAAK,GAAA2b,UAAA,CAAA1Y,IAAA;YAAA;cACbqY,WAAW,GAAG,KAAK;cACvB,KAASC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGlR,SAAS,CAACrK,KAAK,CAACqE,MAAM,EAAEkX,KAAK,EAAE,EAAE;gBACrDpN,IAAI,GAAG9D,SAAS,CAACrK,KAAK,CAACub,KAAK,CAAC;gBACnC,IAAIpN,IAAI,CAACoB,SAAS,KAAKtI,IAAI,CAACjH,KAAK,CAACuP,SAAS,EAAE+L,WAAW,GAAG,IAAI;gBAC/DH,UAAU,CAACjU,aAAa,CAAClH,KAAK,GAAGmO,IAAI,CAACoB,SAAS,CAAC,GAAG;kBACjDjB,GAAG,EAAEpH,aAAa,CAAClH,KAAK,GAAGmO,IAAI,CAACoB,SAAS;kBACzCS,EAAE,EAAE7B,IAAI,CAACoB,SAAS;kBAClB9K,IAAI,EAAE0J,IAAI,CAAC8N,QAAQ;kBACnBC,GAAG,EAAE/N,IAAI,CAACgO,KAAK,IAAIhO,IAAI,CAACiO,OAAO;kBAC/BC,QAAQ,EAAE;oBAAExQ,MAAM,EAAEsC,IAAI,CAAC6B,EAAE;oBAAEiM,QAAQ,EAAE9N,IAAI,CAAC8N,QAAQ;oBAAEE,KAAK,EAAEhO,IAAI,CAACgO,KAAK;oBAAEC,OAAO,EAAEjO,IAAI,CAACiO;kBAAQ;gBACjG,CAAC;cACH;cACAjS,MAAM,CAACnK,KAAK,GAAGob,YAAY,CAACpL,EAAE,GAAGsL,WAAW,GAAG,IAAI;cAAAK,UAAA,CAAAnY,IAAA;cAAA;YAAA;cAEnD2G,MAAM,CAACnK,KAAK,GAAG,IAAI;cACnBqK,SAAS,CAACrK,KAAK,GAAG,EAAE;cACpBmb,UAAU,CAACjU,aAAa,CAAClH,KAAK,GAAGiH,IAAI,CAACjH,KAAK,CAACuP,SAAS,CAAC,GAAG;gBACvDjB,GAAG,EAAEpH,aAAa,CAAClH,KAAK,GAAGiH,IAAI,CAACjH,KAAK,CAACuP,SAAS;gBAC/CS,EAAE,EAAE/I,IAAI,CAACjH,KAAK,CAACuP,SAAS;gBACxB9K,IAAI,EAAEwC,IAAI,CAACjH,KAAK,CAACic,QAAQ;gBACzBC,GAAG,EAAEjV,IAAI,CAACjH,KAAK,CAACmc,KAAK,IAAIlV,IAAI,CAACjH,KAAK,CAACoc,OAAO;gBAC3CC,QAAQ,EAAE;kBACRxQ,MAAM,EAAE5E,IAAI,CAACjH,KAAK,CAACgQ,EAAE;kBACrBiM,QAAQ,EAAEhV,IAAI,CAACjH,KAAK,CAACic,QAAQ;kBAC7BE,KAAK,EAAElV,IAAI,CAACjH,KAAK,CAACmc,KAAK;kBACvBC,OAAO,EAAEnV,IAAI,CAACjH,KAAK,CAACoc;gBACtB;cACF,CAAC;cACDjB,UAAU,EAAAK,eAAA,GAACpR,QAAQ,CAACpK,KAAK,cAAAwb,eAAA,uBAAdA,eAAA,CAAgBtG,cAAc,CAAC5G,GAAG,CAAC,IAAAmN,gBAAA,GAAGrR,QAAQ,CAACpK,KAAK,cAAAyb,gBAAA,uBAAdA,gBAAA,CAAgBvG,cAAc;YAAA;cAEjFpK,YAAY,CAAC9K,KAAK,GAAGmb,UAAU;YAAA;YAAA;cAAA,OAAAQ,UAAA,CAAArW,IAAA;UAAA;QAAA,GAAA4V,SAAA;MAAA,CAChC;MAAA,gBArDKpC,UAAUA,CAAA;QAAA,OAAAmC,MAAA,CAAAhV,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqDf;IACD,IAAM+S,cAAc;MAAA,IAAAuD,MAAA,GAAAvW,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6X,UAAOzJ,IAAI,EAAE3R,IAAI;QAAA,IAAAqb,qBAAA,EAAAC,WAAA,EAAAC,UAAA;QAAA,OAAApd,mBAAA,GAAAuB,IAAA,UAAA8b,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAzX,IAAA,GAAAyX,UAAA,CAAApZ,IAAA;YAAA;cAAAoZ,UAAA,CAAApZ,IAAA;cAAA,OACI8D,qBAAqB,CAACwL,IAAI,EAAEhI,YAAY,CAAC9K,KAAK,CAAC;YAAA;cAAAwc,qBAAA,GAAAI,UAAA,CAAA3Z,IAAA;cAAjFwZ,WAAW,GAAAD,qBAAA,CAAXC,WAAW;cAAEC,UAAU,GAAAF,qBAAA,CAAVE,UAAU;cAC/BtJ,OAAO,CAACC,GAAG,CAACoJ,WAAW,CAAC;cACxB,IAAItb,IAAI,EAAE;gBACR0M,gBAAgB,CAAC7N,KAAK,GAAG,GAAA6c,MAAA,CAAA/B,kBAAA,CAAIjN,gBAAgB,CAAC7N,KAAK,GAAA8a,kBAAA,CAAK2B,WAAW,GAAEzL,MAAM,CAAC,UAAChP,CAAC;kBAAA,OAAK,EAAC0a,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAErN,QAAQ,CAACrN,CAAC,CAACsM,GAAG,CAAC;gBAAA,EAAC;gBAChHwO,oBAAoB,CAACJ,UAAU,CAAC;cAClC,CAAC,MAAM;gBACL7O,gBAAgB,CAAC7N,KAAK,GAAG,GAAA6c,MAAA,CAAA/B,kBAAA,CAAI2B,WAAW,GAAA3B,kBAAA,CAAKjN,gBAAgB,CAAC7N,KAAK,GAAEgR,MAAM,CAAC,UAAChP,CAAC;kBAAA,OAAK,EAAC0a,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAErN,QAAQ,CAACrN,CAAC,CAACsM,GAAG,CAAC;gBAAA,EAAC;gBAChHwO,oBAAoB,CAACJ,UAAU,CAAC;cAClC;YAAC;YAAA;cAAA,OAAAE,UAAA,CAAAtX,IAAA;UAAA;QAAA,GAAAiX,SAAA;MAAA,CACF;MAAA,gBAVKxD,cAAcA,CAAAgE,IAAA,EAAAC,IAAA;QAAA,OAAAV,MAAA,CAAArW,KAAA,OAAAD,SAAA;MAAA;IAAA,GAUnB;IACD,IAAM8W,oBAAoB;MAAA,IAAAG,MAAA,GAAAlX,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwY,UAAOR,UAAU,EAAEvb,IAAI;QAAA,IAAAgc,QAAA,EAAAV,WAAA,EAAAW,cAAA,EAAAC,aAAA,EAAAC,mBAAA,EAAA/B,KAAA,EAAAgC,aAAA,EAAApP,IAAA,EAAAqP,SAAA;QAAA,OAAAle,mBAAA,GAAAuB,IAAA,UAAA4c,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvY,IAAA,GAAAuY,UAAA,CAAAla,IAAA;YAAA;cAC9C2Z,QAAQ,GAAG,EAAE;cACbV,WAAW,GAAG,EAAE;cAChBW,cAAc,GAAG,EAAE;cACnBC,aAAa,GAAG,EAAE;cAClBC,mBAAmB,GAAG,KAAK;cAC/B,KAAS/B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1N,gBAAgB,CAAC7N,KAAK,CAACqE,MAAM,EAAEkX,KAAK,EAAE,EAAE;gBAC5DpN,IAAI,GAAGN,gBAAgB,CAAC7N,KAAK,CAACub,KAAK,CAAC;gBAC1C,KAAAgC,aAAA,GAAIpP,IAAI,CAACwP,OAAO,cAAAJ,aAAA,gBAAAA,aAAA,GAAZA,aAAA,CAAcI,OAAO,cAAAJ,aAAA,eAArBA,aAAA,CAAuBlO,QAAQ,CAAC,QAAQ,CAAC,EAAEiO,mBAAmB,GAAG,IAAI;gBACzE,IAAI,EAACD,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEhO,QAAQ,CAAClB,IAAI,CAACG,GAAG,CAAC,KAAI,EAACoO,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAErN,QAAQ,CAAClB,IAAI,CAACG,GAAG,CAAC,GAAE;kBACzE+O,aAAa,CAACrZ,IAAI,CAACmK,IAAI,CAACG,GAAG,CAAC;kBAC5B,IAAI,GAAAkP,SAAA,GAACL,QAAQ,cAAAK,SAAA,eAARA,SAAA,CAAUnO,QAAQ,CAACxI,MAAM,CAACsH,IAAI,CAACyH,QAAQ,CAAC,CAAC,GAAE;oBAC9CuH,QAAQ,GAAG,CAACtW,MAAM,CAACsH,IAAI,CAACyH,QAAQ,CAAC,EAAE/O,MAAM,CAACsH,IAAI,CAACyH,QAAQ,GAAG,KAAK,CAAC,CAAC;oBACjE6G,WAAW,CAACzY,IAAI,CAAC;sBACfgM,EAAE,EAAE7B,IAAI,CAACyH,QAAQ;sBACjBzU,IAAI,EAAE,MAAM;sBACZyc,SAAS,EAAE,wBAAwB;sBACnCD,OAAO,EAAE9W,MAAM,CAACsH,IAAI,CAACyH,QAAQ;oBAC/B,CAAC,CAAC;kBACJ;kBACA,IAAIzH,IAAI,CAAChN,IAAI,KAAK,WAAW,EAAE;oBAC7Bsb,WAAW,CAACzY,IAAI,CAAAuW,aAAA,CAAAA,aAAA,KAAMpM,IAAI;sBAAE0P,QAAQ,EAAET,cAAc,CAAC/Y;oBAAM,EAAE,CAAC;oBAC9D+Y,cAAc,CAACpZ,IAAI,CAACmK,IAAI,CAACwP,OAAO,CAACG,QAAQ,CAAC;kBAC5C,CAAC,MAAM,IAAI3P,IAAI,CAAChN,IAAI,KAAK,YAAY,EAAE;oBACrCsb,WAAW,CAACzY,IAAI,CAAAuW,aAAA,CAAAA,aAAA,KAAMpM,IAAI;sBAAE4P,KAAK,EAAE,IAAIC,KAAK,CAAC7P,IAAI,CAACwP,OAAO,CAACM,SAAS;oBAAC,EAAE,CAAC;kBACzE,CAAC,MAAM,IAAI9P,IAAI,CAAChN,IAAI,KAAK,eAAe,EAAE;oBACxCsb,WAAW,CAACzY,IAAI,CAACmK,IAAI,CAAC;oBACtB1D,YAAY,CAACzK,KAAK,CAACgE,IAAI,CAACmK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,GAAG,CAAC;kBACpC,CAAC,MAAM;oBACLmO,WAAW,CAACzY,IAAI,CAACmK,IAAI,CAAC;kBACxB;gBACF;cACF;cACA,IAAImP,mBAAmB,EAAE/D,aAAa,CAACnP,QAAQ,CAACpK,KAAK,CAACiY,QAAQ,CAAC;cAC/D3N,WAAW,CAACtK,KAAK,GAAGod,cAAc;cAClCtP,oBAAoB,CAAC9N,KAAK,GAAGyc,WAAW;cACxC,IAAItb,IAAI,EAAE;gBACRuF,QAAQ,CAAC,YAAM;kBACb8G,SAAS,CAACxN,KAAK,CAACqV,OAAO,CAACC,SAAS,GAAG3H,YAAY,CAAC3N,KAAK;gBACxD,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL0G,QAAQ,CAAC,YAAM;kBACbkH,UAAU,CAAC5N,KAAK,GAAGoV,UAAU,CAAC,CAAC,GAAGG,cAAc,CAAC,CAAC;gBACpD,CAAC,CAAC;cACJ;YAAC;YAAA;cAAA,OAAAmI,UAAA,CAAApY,IAAA;UAAA;QAAA,GAAA4X,SAAA;MAAA,CACF;MAAA,gBA7CKJ,oBAAoBA,CAAAoB,IAAA,EAAAC,IAAA;QAAA,OAAAlB,MAAA,CAAAhX,KAAA,OAAAD,SAAA;MAAA;IAAA,GA6CzB;IACD,IAAMoY,cAAc,GAAG,SAAjBA,cAAcA,CAAI7e,CAAC,EAAE4O,IAAI,EAAK;MAClClD,YAAY,CAACjL,KAAK,GAAGmO,IAAI;MACzBpD,WAAW,CAAC/K,KAAK,GAAGT,CAAC,CAAC8e,KAAK;MAC3BrT,YAAY,CAAChL,KAAK,GAAGT,CAAC,CAAC+e,KAAK;MAC5BpT,YAAY,CAAClL,KAAK,GAAG,IAAI;MACzBmL,cAAc,CAACnL,KAAK,GAAG,IAAI;IAC7B,CAAC;IACD,IAAMue,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIhf,CAAC,EAAE4O,IAAI,EAAK;MAAA,IAAAqQ,cAAA;MACtC,IAAMC,SAAS,GAAGhV,MAAM,CAACiV,YAAY,CAAC,CAAC;MACvC,IAAMC,WAAW,GAAGF,SAAS,CAAClR,QAAQ,CAAC,CAAC,KAAIY,IAAI,aAAJA,IAAI,gBAAAqQ,cAAA,GAAJrQ,IAAI,CAAEwP,OAAO,cAAAa,cAAA,uBAAbA,cAAA,CAAeb,OAAO;MAClE,IAAMiB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAChC,IAAMC,cAAc,GAAGH,GAAG,GAAGzQ,IAAI,CAACyH,QAAQ;MAC1C,IAAMoJ,UAAU,GAAGD,cAAc,GAAG,MAAM;MAC1CzT,gBAAgB,CAACtL,KAAK,GAAAua,aAAA,CAAAA,aAAA,KAAQpM,IAAI;QAAEwQ,WAAW;QAAEK;MAAU,EAAE;MAC7D5T,eAAe,CAACpL,KAAK,GAAGT,CAAC,CAAC8e,KAAK;MAC/BhT,gBAAgB,CAACrL,KAAK,GAAGT,CAAC,CAAC+e,KAAK;MAChC/S,gBAAgB,CAACvL,KAAK,GAAG,IAAI;MAC7BwL,kBAAkB,CAACxL,KAAK,GAAG,IAAI;IACjC,CAAC;IACD,IAAMif,WAAW,GAAG,SAAdA,WAAWA,CAAInM,IAAI,EAAK;MAC5B,IAAIvI,aAAa,CAACvK,KAAK,CAACgQ,EAAE,EAAE;QAC1B,IAAIzF,aAAa,CAACvK,KAAK,CAACgQ,EAAE,KAAK8C,IAAI,CAAC9C,EAAE,EAAE;UACtCxF,gBAAgB,CAACxK,KAAK,CAACuK,aAAa,CAACvK,KAAK,CAACgQ,EAAE,CAAC,GAAGzF,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACmB,WAAW;UACtFC,gBAAgB,CAAC,CAAC;QACpB,CAAC,MAAM;UACL3U,gBAAgB,CAACxK,KAAK,CAACuK,aAAa,CAACvK,KAAK,CAACgQ,EAAE,CAAC,GAAGzF,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACmB,WAAW;UACtFC,gBAAgB,CAAC,CAAC;UAClB5U,aAAa,CAACvK,KAAK,GAAG;YAAEgQ,EAAE,EAAE8C,IAAI,CAAC9C,EAAE;YAAE+N,KAAK,EAAEjL,IAAI,CAACiL;UAAM,CAAC;UACxDxT,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACmB,WAAW,GAAG,CAAC;UACzCE,eAAe,CAAC,CAAC;QACnB;MACF,CAAC,MAAM;QACL7U,aAAa,CAACvK,KAAK,GAAG;UAAEgQ,EAAE,EAAE8C,IAAI,CAAC9C,EAAE;UAAE+N,KAAK,EAAEjL,IAAI,CAACiL;QAAM,CAAC;QACxDxT,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACmB,WAAW,GAAG,CAAC;QACzCE,eAAe,CAAC,CAAC;MACnB;IACF,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIvM,IAAI,EAAK;MAC9B,IAAIvI,aAAa,CAACvK,KAAK,CAACgQ,EAAE,EAAE;QAC1BxF,gBAAgB,CAACxK,KAAK,CAACuK,aAAa,CAACvK,KAAK,CAACgQ,EAAE,CAAC,GAAGzF,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACmB,WAAW;QACtFC,gBAAgB,CAAC,CAAC;MACpB;MACA5U,aAAa,CAACvK,KAAK,GAAG;QAAEgQ,EAAE,EAAE8C,IAAI,CAAC9C,EAAE;QAAE+N,KAAK,EAAEjL,IAAI,CAACiL;MAAM,CAAC;MACxDxT,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACmB,WAAW,GAAG1U,gBAAgB,CAACxK,KAAK,CAACuK,aAAa,CAACvK,KAAK,CAACgQ,EAAE,CAAC,IAAI,CAAC;MAC3FoP,eAAe,CAAC,CAAC;IACnB,CAAC;IACD,IAAMA,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B7U,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACuB,IAAI,CAAC,CAAC;MAChC/U,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACwB,gBAAgB,CAAC,OAAO,EAAE,YAAM;QACxD/U,gBAAgB,CAACxK,KAAK,CAACuK,aAAa,CAACvK,KAAK,CAACgQ,EAAE,CAAC,GAAG,CAAC;QAClDzF,aAAa,CAACvK,KAAK,GAAG,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC;IACD,IAAMmf,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B5U,aAAa,CAACvK,KAAK,CAAC+d,KAAK,CAACyB,KAAK,CAAC,CAAC;MACjCjV,aAAa,CAACvK,KAAK,GAAG,CAAC,CAAC;IAC1B,CAAC;IACD,IAAMyf,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAI7R,UAAU,CAAC5N,KAAK,EAAEoV,UAAU,CAAC,CAAC;IACpC,CAAC;IACD,IAAMsK,UAAU,GAAG,SAAbA,UAAUA,CAAIxP,KAAK,EAAK;MAC5BA,KAAK,CAAClC,cAAc,CAAC,CAAC;MACtB,IAAM2R,KAAK,GAAGzP,KAAK,CAAC0P,YAAY,CAACD,KAAK;MACtC,IAAIA,KAAK,CAACtb,MAAM,EAAE;QAChB,IAAMwb,WAAW,GAAG,EAAE;QACtB,KAAK,IAAItE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGoE,KAAK,CAACtb,MAAM,EAAEkX,KAAK,EAAE,EAAE;UACjD,IAAMhN,IAAI,GAAGoR,KAAK,CAACxR,IAAI,CAACoN,KAAK,CAAC;UAC9B,IAAM5G,OAAO,GAAGpG,IAAI,CAAC9J,IAAI,CAACqb,SAAS,CAACvR,IAAI,CAAC9J,IAAI,CAACsb,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC5K,WAAW,CAAC,CAAC;UACjF0K,WAAW,CAAC7b,IAAI,CAAC;YAAEgM,EAAE,EAAE7C,IAAI,CAAC,CAAC;YAAE1I,IAAI,EAAE8J,IAAI,CAAC9J,IAAI;YAAEkQ,OAAO;YAAEqL,IAAI,EAAEzR,IAAI,CAACyR,IAAI;YAAEzR,IAAI,EAAEA;UAAK,CAAC,CAAC;QACzF;QACA9C,QAAQ,CAACzL,KAAK,GAAG6f,WAAW;QAC5BnU,QAAQ,CAAC1L,KAAK,GAAG,IAAI;MACvB;IACF,CAAC;IACD,IAAMigB,YAAY,GAAG,SAAfA,YAAYA,CAAInN,IAAI,EAAK;MAC7B,IAAIA,IAAI,EAAE;QACR,KAAK,IAAIyI,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGzI,IAAI,CAACzO,MAAM,EAAEkX,KAAK,EAAE,EAAE;UAChD,IAAMpN,IAAI,GAAG2E,IAAI,CAACyI,KAAK,CAAC;UACxB2E,UAAU,CAAC/R,IAAI,CAAC;QAClB;MACF;MACA1C,QAAQ,CAACzL,KAAK,GAAG,EAAE;MACnB0L,QAAQ,CAAC1L,KAAK,GAAG,KAAK;IACxB,CAAC;IACD,IAAMmgB,cAAc,GAAG,SAAjBA,cAAcA,CAAI5R,IAAI,EAAK;MAC/B5C,OAAO,CAAC3L,KAAK,GAAGuO,IAAI;MACpB3C,OAAO,CAAC5L,KAAK,GAAG,IAAI;IACtB,CAAC;IACD,IAAMogB,WAAW,GAAG,SAAdA,WAAWA,CAAIjf,IAAI,EAAK;MAC5B,IAAIA,IAAI,EAAE+e,UAAU,CAACvU,OAAO,CAAC3L,KAAK,CAAC;MACnC2L,OAAO,CAAC3L,KAAK,GAAG,CAAC,CAAC;MAClB4L,OAAO,CAAC5L,KAAK,GAAG,KAAK;IACvB,CAAC;IACD,IAAMkgB,UAAU;MAAA,IAAAG,MAAA,GAAAta,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4b,UAAO/R,IAAI;QAAA,IAAAgS,MAAA;QAAA,IAAAC,MAAA,EAAAC,qBAAA,EAAA3N,IAAA;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAA6f,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxb,IAAA,GAAAwb,UAAA,CAAAnd,IAAA;YAAA;cACtBgd,MAAM,GAAG,IAAII,QAAQ,CAAC,CAAC;cAC7BJ,MAAM,CAACK,MAAM,CAAC,MAAM,EAAEtS,IAAI,CAACA,IAAI,CAAC;cAChCiS,MAAM,CAACK,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC;cAClCL,MAAM,CAACK,MAAM,CAAC,KAAK,EAAEtS,IAAI,CAACyB,EAAE,IAAIzB,IAAI,CAACD,GAAG,IAAInB,IAAI,CAAC,CAAC,CAAC;cAAAwT,UAAA,CAAAnd,IAAA;cAAA,OAC5B4C,GAAG,CAAC0a,YAAY,CAACN,MAAM,EAAE,YAAM,CAAE,CAAC,CAAC;YAAA;cAAAC,qBAAA,GAAAE,UAAA,CAAA1d,IAAA;cAAlD6P,IAAI,GAAA2N,qBAAA,CAAJ3N,IAAI;cACZ,KAAAyN,MAAA,GAAI,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,cAAAA,MAAA,eAAtBA,MAAA,CAAwBlR,QAAQ,CAACyD,IAAI,CAAC6B,OAAO,CAAC,EAAE;gBAClDoM,oBAAoB,CAAC3a,GAAG,CAAC4a,UAAU,CAAClO,IAAI,CAACmO,WAAW,CAAC,CAAC;cACxD,CAAC,MAAM;gBACLjH,YAAY,CAACQ,OAAO,CAAC1H,IAAI,CAAC9C,EAAE,EAAEiE,IAAI,CAACwG,SAAS,CAAC3H,IAAI,CAAC,CAAC;gBACnDoO,qBAAqB,CAACpO,IAAI,CAAC9C,EAAE,CAAC;gBAC9B,IAAI5F,QAAQ,CAACpK,KAAK,CAACmB,IAAI,KAAK,CAAC,EAAEggB,mBAAmB,CAACrO,IAAI,CAAC9C,EAAE,CAAC;cAC7D;YAAC;YAAA;cAAA,OAAA2Q,UAAA,CAAArb,IAAA;UAAA;QAAA,GAAAgb,SAAA;MAAA,CACF;MAAA,gBAbKJ,UAAUA,CAAAkB,IAAA;QAAA,OAAAf,MAAA,CAAApa,KAAA,OAAAD,SAAA;MAAA;IAAA,GAaf;IACD,IAAMmb,mBAAmB;MAAA,IAAAE,MAAA,GAAAtb,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4c,UAAO7M,MAAM;QAAA,OAAAnV,mBAAA,GAAAuB,IAAA,UAAA0gB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArc,IAAA,GAAAqc,UAAA,CAAAhe,IAAA;YAAA;cAAAge,UAAA,CAAAhe,IAAA;cAAA,OACjC4C,GAAG,CAACqb,gBAAgB,CAAC;gBACzBC,IAAI,EAAE;kBAAEC,WAAW,EAAEvX,QAAQ,CAACpK,KAAK,CAACiY,QAAQ,CAAC5S,KAAK,CAAC6B,aAAa,CAAClH,KAAK,CAACqE,MAAM,CAAC;kBAAEoQ;gBAAO;cACzF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAA+M,UAAA,CAAAlc,IAAA;UAAA;QAAA,GAAAgc,SAAA;MAAA,CACH;MAAA,gBAJKH,mBAAmBA,CAAAS,IAAA;QAAA,OAAAP,MAAA,CAAApb,KAAA,OAAAD,SAAA;MAAA;IAAA,GAIxB;IACD,IAAM6b,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAMC,SAAS,GAAGC,SAAS,CAACD,SAAS,CAAC3M,WAAW,CAAC,CAAC;MACnD,OAAO,CAAA2M,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEzS,QAAQ,CAAC,WAAW,CAAC,MAAIyS,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEzS,QAAQ,CAAC,UAAU,CAAC;IAC5E,CAAC;IACD,IAAM2S,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAI,CAAC7X,MAAM,CAACnK,KAAK,EAAE;MACnBuM,WAAW,CAACvM,KAAK,GAAG,CAACuM,WAAW,CAACvM,KAAK;IACxC,CAAC;IACD,IAAMiiB,aAAa,GAAG,SAAhBA,aAAaA,CAAInP,IAAI,EAAK;MAAA,IAAAoP,cAAA;MAC9B,IAAMC,UAAU,GAAGrP,IAAI,aAAJA,IAAI,gBAAAoP,cAAA,GAAJpP,IAAI,CAAEsP,QAAQ,cAAAF,cAAA,uBAAdA,cAAA,CAAgBlG,GAAG,CAAC,UAACha,CAAC;QAAA,OAAKkF,aAAa,CAAClH,KAAK,GAAGgC,CAAC,CAACqa,QAAQ,CAAC9M,SAAS;MAAA,EAAC;MACzF,IAAM8S,WAAW,GAAGF,UAAU,CAAC9d,MAAM,GACjC;QAAEie,gBAAgB,EAAE,EAAE;QAAEnhB,IAAI,EAAE,CAAC;QAAEohB,UAAU,EAAEC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACP,UAAU,CAAC;MAAE,CAAC,GAC9E,CAAC,CAAC;MACNQ,qBAAqB,CAAC7P,IAAI,CAAC6K,OAAO,EAAE0E,WAAW,CAAC;IAClD,CAAC;IACD,IAAMM,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,WAAW,EAAuB;MAAA,IAArBP,WAAW,GAAArc,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,CAAC,CAAC;MAC1D,IAAI,CAAC4c,WAAW,CAACxV,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE;MAC5C,IAAM8D,OAAO,GAAG,IAAItK,SAAS,CAACic,WAAW,CAAC;QAAElF,OAAO,EAAEiF,WAAW;QAAEE,aAAa,EAAET;MAAY,CAAC,CAAC;MAC/F,IAAM9J,YAAY,GAAG;QAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;QAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;MAAS,CAAC;MACjG,IAAM8K,OAAO,GAAG;QACdC,YAAY,EAAE,SAAdA,YAAYA,CAAG9R,OAAO,EAAK;UACzBtD,UAAU,CAAC5N,KAAK,GAAG,IAAI;UACvB+Y,cAAc,CAAC,CAAAwB,aAAA,CAAAA,aAAA,KAAMrJ,OAAO;YAAE0E,QAAQ,EAAEiJ,IAAI,CAAC3K,KAAK,CAAC,IAAI2K,IAAI,CAAC,CAAC;UAAC,GAAG,EAAE,IAAI,CAAC;QAC1E;MACF,CAAC;MACDoE,iBAAiB,CAAC1K,YAAY,EAAErH,OAAO,EAAE6R,OAAO,EAAE,UAACzL,IAAI,EAAEC,GAAG,EAAEzE,IAAI,EAAK;QACrE,IAAIwE,IAAI,KAAK,CAAC,EAAE;UACd,IAAI4L,mBAAmB,GAAG,EAAE;UAC5B,KAAK,IAAI3H,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1N,gBAAgB,CAAC7N,KAAK,CAACqE,MAAM,EAAEkX,KAAK,EAAE,EAAE;YAClE,IAAMpN,IAAI,GAAGN,gBAAgB,CAAC7N,KAAK,CAACub,KAAK,CAAC;YAC1C2H,mBAAmB,CAAClf,IAAI,CACtBmK,IAAI,CAAC6B,EAAE,KAAK8C,IAAI,CAACqQ,SAAS,GAAA5I,aAAA,CAAAA,aAAA,KAAQpM,IAAI;cAAEG,GAAG,EAAEwE,IAAI,CAACsQ,UAAU;cAAExN,QAAQ,EAAE9C,IAAI,CAAC8C;YAAQ,KAAKzH,IAC5F,CAAC;UACH;UACAN,gBAAgB,CAAC7N,KAAK,GAAGkjB,mBAAmB;UAC5CpG,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC;UAC9B1J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEP,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLM,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiE,IAAI,EAAEC,GAAG,CAAC;QACnC;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAMwJ,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAI/T,GAAG,EAAK;MACpC,IAAMkE,OAAO,GAAG,IAAItK,SAAS,CAACyc,YAAY,CAAC;QAAE1F,OAAO,EAAE,EAAE;QAAEG,QAAQ,EAAE9Q;MAAI,CAAC,CAAC;MAC1E,IAAMuL,YAAY,GAAG;QAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;QAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;MAAS,CAAC;MACjG,IAAM8K,OAAO,GAAG;QACdC,YAAY,EAAE,SAAdA,YAAYA,CAAG9R,OAAO,EAAK;UACzBtD,UAAU,CAAC5N,KAAK,GAAG,IAAI;UACvB+Y,cAAc,CAAC,CAAAwB,aAAA,CAAAA,aAAA,KAAMrJ,OAAO;YAAE0E,QAAQ,EAAEiJ,IAAI,CAAC3K,KAAK,CAAC,IAAI2K,IAAI,CAAC,CAAC;UAAC,GAAG,EAAE,IAAI,CAAC;QAC1E;MACF,CAAC;MACDoE,iBAAiB,CAAC1K,YAAY,EAAErH,OAAO,EAAE6R,OAAO,EAAE,UAACzL,IAAI,EAAEC,GAAG,EAAEzE,IAAI,EAAK;QACrE,IAAIwE,IAAI,KAAK,CAAC,EAAE;UACd,IAAI4L,mBAAmB,GAAG,EAAE;UAC5B,KAAK,IAAI3H,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1N,gBAAgB,CAAC7N,KAAK,CAACqE,MAAM,EAAEkX,KAAK,EAAE,EAAE;YAClE,IAAMpN,IAAI,GAAGN,gBAAgB,CAAC7N,KAAK,CAACub,KAAK,CAAC;YAC1C2H,mBAAmB,CAAClf,IAAI,CACtBmK,IAAI,CAAC6B,EAAE,KAAK8C,IAAI,CAACqQ,SAAS,GAAA5I,aAAA,CAAAA,aAAA,KAAQpM,IAAI;cAAEG,GAAG,EAAEwE,IAAI,CAACsQ,UAAU;cAAExN,QAAQ,EAAE9C,IAAI,CAAC8C;YAAQ,KAAKzH,IAC5F,CAAC;UACH;UACAN,gBAAgB,CAAC7N,KAAK,GAAGkjB,mBAAmB;UAC5CpG,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC;UAC9B1J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEP,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLM,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiE,IAAI,EAAEC,GAAG,CAAC;QACnC;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAM2J,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIzM,MAAM,EAAK;MACxC,IAAM6O,aAAa,GAAG1c,SAAS,CAAC2c,mBAAmB,CAAC,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC;MAC3F,IAAMrS,OAAO,GAAG,IAAIoS,aAAa,CAAC;QAAE3F,OAAO,EAAE,QAAQlJ,MAAM,EAAE;QAAE+O,KAAK,EAAE;MAAO,CAAC,CAAC;MAC/E,IAAMjL,YAAY,GAAG;QAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;QAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;MAAS,CAAC;MACjG,IAAM8K,OAAO,GAAG;QACdC,YAAY,EAAE,SAAdA,YAAYA,CAAG9R,OAAO,EAAK;UACzBtD,UAAU,CAAC5N,KAAK,GAAG,IAAI;UACvB+Y,cAAc,CAAC,CAAAwB,aAAA,CAAAA,aAAA,KAAMrJ,OAAO;YAAE0E,QAAQ,EAAEiJ,IAAI,CAAC3K,KAAK,CAAC,IAAI2K,IAAI,CAAC,CAAC;UAAC,GAAG,EAAE,IAAI,CAAC;QAC1E;MACF,CAAC;MACDoE,iBAAiB,CAAC1K,YAAY,EAAErH,OAAO,EAAE6R,OAAO,EAAE,UAACzL,IAAI,EAAEC,GAAG,EAAEzE,IAAI,EAAK;QACrE,IAAIwE,IAAI,KAAK,CAAC,EAAE;UACd,IAAI4L,mBAAmB,GAAG,EAAE;UAC5B,KAAK,IAAI3H,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1N,gBAAgB,CAAC7N,KAAK,CAACqE,MAAM,EAAEkX,KAAK,EAAE,EAAE;YAClE,IAAMpN,IAAI,GAAGN,gBAAgB,CAAC7N,KAAK,CAACub,KAAK,CAAC;YAC1C2H,mBAAmB,CAAClf,IAAI,CACtBmK,IAAI,CAAC6B,EAAE,KAAK8C,IAAI,CAACqQ,SAAS,GAAA5I,aAAA,CAAAA,aAAA,KAAQpM,IAAI;cAAEG,GAAG,EAAEwE,IAAI,CAACsQ,UAAU;cAAExN,QAAQ,EAAE9C,IAAI,CAAC8C;YAAQ,KAAKzH,IAC5F,CAAC;UACH;UACAN,gBAAgB,CAAC7N,KAAK,GAAGkjB,mBAAmB;UAC5CpG,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC;UAC9B1J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEP,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLM,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiE,IAAI,EAAEC,GAAG,CAAC;QACnC;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAMkM,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIjD,MAAM,EAAK;MAC1C,IAAM8C,aAAa,GAAG1c,SAAS,CAAC2c,mBAAmB,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC;MACvF,IAAMrS,OAAO,GAAG,IAAIoS,aAAa,CAAC9C,MAAM,CAAC;MACzC,IAAMjI,YAAY,GAAG;QAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;QAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;MAAS,CAAC;MACjG,IAAM8K,OAAO,GAAG;QACdC,YAAY,EAAE,SAAdA,YAAYA,CAAG9R,OAAO,EAAK;UACzBtD,UAAU,CAAC5N,KAAK,GAAG,IAAI;UACvB+Y,cAAc,CAAC,CAAAwB,aAAA,CAAAA,aAAA,KAAMrJ,OAAO;YAAE0E,QAAQ,EAAEiJ,IAAI,CAAC3K,KAAK,CAAC,IAAI2K,IAAI,CAAC,CAAC;UAAC,GAAG,EAAE,IAAI,CAAC;QAC1E;MACF,CAAC;MACDoE,iBAAiB,CAAC1K,YAAY,EAAErH,OAAO,EAAE6R,OAAO,EAAE,UAACzL,IAAI,EAAEC,GAAG,EAAEzE,IAAI,EAAK;QACrE,IAAIwE,IAAI,KAAK,CAAC,EAAE;UACd,IAAI4L,mBAAmB,GAAG,EAAE;UAC5B,KAAK,IAAI3H,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1N,gBAAgB,CAAC7N,KAAK,CAACqE,MAAM,EAAEkX,KAAK,EAAE,EAAE;YAClE,IAAMpN,IAAI,GAAGN,gBAAgB,CAAC7N,KAAK,CAACub,KAAK,CAAC;YAC1C2H,mBAAmB,CAAClf,IAAI,CACtBmK,IAAI,CAAC6B,EAAE,KAAK8C,IAAI,CAACqQ,SAAS,GAAA5I,aAAA,CAAAA,aAAA,KAAQpM,IAAI;cAAEG,GAAG,EAAEwE,IAAI,CAACsQ,UAAU;cAAExN,QAAQ,EAAE9C,IAAI,CAAC8C;YAAQ,KAAKzH,IAC5F,CAAC;UACH;UACAN,gBAAgB,CAAC7N,KAAK,GAAGkjB,mBAAmB;UAC5CpG,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC;UAC9B1J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEP,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLM,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiE,IAAI,EAAEC,GAAG,CAAC;QACnC;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAM0L,iBAAiB;MAAA,IAAAS,MAAA,GAAA3d,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAif,UAAOpL,YAAY,EAAErH,OAAO,EAAE6R,OAAO,EAAElH,QAAQ;QAAA,IAAA+H,qBAAA,EAAAtM,IAAA,EAAAC,GAAA,EAAAzE,IAAA;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAAgjB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3e,IAAA,GAAA2e,UAAA,CAAAtgB,IAAA;YAAA;cAAAsgB,UAAA,CAAAtgB,IAAA;cAAA,OACrCoD,SAAS,CAACmd,WAAW,CAACxL,YAAY,EAAErH,OAAO,EAAE6R,OAAO,CAAC;YAAA;cAAAa,qBAAA,GAAAE,UAAA,CAAA7gB,IAAA;cAA/EqU,IAAI,GAAAsM,qBAAA,CAAJtM,IAAI;cAAEC,GAAG,GAAAqM,qBAAA,CAAHrM,GAAG;cAAEzE,IAAI,GAAA8Q,qBAAA,CAAJ9Q,IAAI;cACvB,IAAIwE,IAAI,KAAK,CAAC,EAAEI,aAAa,CAAC,CAAC;cAC/BmE,QAAQ,CAACvE,IAAI,EAAEC,GAAG,EAAEzE,IAAI,CAAC;YAAA;YAAA;cAAA,OAAAgR,UAAA,CAAAxe,IAAA;UAAA;QAAA,GAAAqe,SAAA;MAAA,CAC1B;MAAA,gBAJKV,iBAAiBA,CAAAe,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAT,MAAA,CAAAzd,KAAA,OAAAD,SAAA;MAAA;IAAA,GAItB;IACD,IAAMoe,uBAAuB;MAAA,IAAAC,MAAA,GAAAte,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4f,UAAOC,YAAY;QAAA,IAAAC,qBAAA,EAAAlN,IAAA;QAAA,OAAAhY,mBAAA,GAAAuB,IAAA,UAAA4jB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvf,IAAA,GAAAuf,UAAA,CAAAlhB,IAAA;YAAA;cAAAkhB,UAAA,CAAAlhB,IAAA;cAAA,OAC1BoD,SAAS,CAAC+d,iCAAiC,CAChE;gBAAE3M,gBAAgB,EAAE/M,YAAY,CAACjL,KAAK,CAACmB,IAAI;gBAAE8W,QAAQ,EAAEhN,YAAY,CAACjL,KAAK,CAACgQ;cAAG,CAAC,EAC9EuU,YACF,CAAC;YAAA;cAAAC,qBAAA,GAAAE,UAAA,CAAAzhB,IAAA;cAHOqU,IAAI,GAAAkN,qBAAA,CAAJlN,IAAI;cAIZ,IAAI,CAACA,IAAI,EAAEI,aAAa,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAgN,UAAA,CAAApf,IAAA;UAAA;QAAA,GAAAgf,SAAA;MAAA,CAC3B;MAAA,gBANKF,uBAAuBA,CAAAQ,IAAA;QAAA,OAAAP,MAAA,CAAApe,KAAA,OAAAD,SAAA;MAAA;IAAA,GAM5B;IACD,IAAM6e,gBAAgB;MAAA,IAAAC,MAAA,GAAA/e,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqgB,UAAOC,KAAK;QAAA,IAAAC,sBAAA,EAAA3N,IAAA;QAAA,OAAAhY,mBAAA,GAAAuB,IAAA,UAAAqkB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhgB,IAAA,GAAAggB,UAAA,CAAA3hB,IAAA;YAAA;cAAA2hB,UAAA,CAAA3hB,IAAA;cAAA,OACZoD,SAAS,CAACwe,oBAAoB,CACnD;gBAAEpN,gBAAgB,EAAE/M,YAAY,CAACjL,KAAK,CAACmB,IAAI;gBAAE8W,QAAQ,EAAEhN,YAAY,CAACjL,KAAK,CAACgQ;cAAG,CAAC,EAC9EgV,KACF,CAAC;YAAA;cAAAC,sBAAA,GAAAE,UAAA,CAAAliB,IAAA;cAHOqU,IAAI,GAAA2N,sBAAA,CAAJ3N,IAAI;cAIZ,IAAI,CAACA,IAAI,EAAEI,aAAa,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAyN,UAAA,CAAA7f,IAAA;UAAA;QAAA,GAAAyf,SAAA;MAAA,CAC3B;MAAA,gBANKF,gBAAgBA,CAAAQ,IAAA;QAAA,OAAAP,MAAA,CAAA7e,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMrB;IACD,IAAM8V,aAAa;MAAA,IAAAwJ,MAAA,GAAAvf,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6gB,UAAA;QAAA,IAAAC,qBAAA,EAAAlO,IAAA,EAAAC,GAAA;QAAA,OAAAjY,mBAAA,GAAAuB,IAAA,UAAA4kB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvgB,IAAA,GAAAugB,UAAA,CAAAliB,IAAA;YAAA;cAAAkiB,UAAA,CAAAliB,IAAA;cAAA,OACQoD,SAAS,CAAC+e,kBAAkB,CAAC;gBACvD3N,gBAAgB,EAAE/M,YAAY,CAACjL,KAAK,CAACmB,IAAI;gBACzC8W,QAAQ,EAAEhN,YAAY,CAACjL,KAAK,CAACgQ;cAC/B,CAAC,CAAC;YAAA;cAAAwV,qBAAA,GAAAE,UAAA,CAAAziB,IAAA;cAHMqU,IAAI,GAAAkO,qBAAA,CAAJlO,IAAI;cAAEC,GAAG,GAAAiO,qBAAA,CAAHjO,GAAG;cAIjB,IAAID,IAAI,KAAK,CAAC,EAAE;gBACdlE,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;gBACrBqE,aAAa,CAAC,KAAK,EAAEzM,YAAY,CAACjL,KAAK,CAAC;cAC1C,CAAC,MAAM;gBACLoT,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiE,IAAI,EAAEC,GAAG,CAAC;cACnC;YAAC;YAAA;cAAA,OAAAmO,UAAA,CAAApgB,IAAA;UAAA;QAAA,GAAAigB,SAAA;MAAA,CACF;MAAA,gBAXKzJ,aAAaA,CAAA;QAAA,OAAAwJ,MAAA,CAAArf,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWlB;IACD,IAAM4f,gBAAgB;MAAA,IAAAC,MAAA,GAAA9f,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAohB,UAAA;QAAA,IAAAvN,YAAA,EAAAwN,YAAA,EAAAC,qBAAA,EAAA1O,IAAA,EAAAC,GAAA;QAAA,OAAAjY,mBAAA,GAAAuB,IAAA,UAAAolB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/gB,IAAA,GAAA+gB,UAAA,CAAA1iB,IAAA;YAAA;cACjB+U,YAAY,GAAG;gBAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;gBAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;cAAS,CAAC;cAC3F8N,YAAY,GAAG,CACnB;gBACE3C,UAAU,EAAE9X,gBAAgB,CAACtL,KAAK,CAACsO,GAAG;gBACtCsH,QAAQ,EAAEtK,gBAAgB,CAACtL,KAAK,CAAC4V,QAAQ;gBACzCuQ,gBAAgB,EAAE7a,gBAAgB,CAACtL,KAAK,CAAComB;cAC3C,CAAC,CACF;cAAAF,UAAA,CAAA1iB,IAAA;cAAA,OAC2BoD,SAAS,CAACyf,cAAc,CAAC9N,YAAY,EAAEwN,YAAY,CAAC;YAAA;cAAAC,qBAAA,GAAAE,UAAA,CAAAjjB,IAAA;cAAxEqU,IAAI,GAAA0O,qBAAA,CAAJ1O,IAAI;cAAEC,GAAG,GAAAyO,qBAAA,CAAHzO,GAAG;cACjB,IAAID,IAAI,KAAK,CAAC,EAAE;gBACdlE,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;gBACrBxF,gBAAgB,CAAC7N,KAAK,GAAG6N,gBAAgB,CAAC7N,KAAK,CAACgR,MAAM,CAAC,UAAC7C,IAAI;kBAAA,OAAKA,IAAI,CAACG,GAAG,KAAKhD,gBAAgB,CAACtL,KAAK,CAACsO,GAAG;gBAAA,EAAC;gBACzGwO,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC;cAChC,CAAC,MAAM;gBACL1J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiE,IAAI,EAAEC,GAAG,CAAC;cACnC;YAAC;YAAA;cAAA,OAAA2O,UAAA,CAAA5gB,IAAA;UAAA;QAAA,GAAAwgB,SAAA;MAAA,CACF;MAAA,gBAjBKF,gBAAgBA,CAAA;QAAA,OAAAC,MAAA,CAAA5f,KAAA,OAAAD,SAAA;MAAA;IAAA,GAiBrB;IACD,IAAMsgB,qBAAqB;MAAA,IAAAC,MAAA,GAAAxgB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8hB,UAAA;QAAA,IAAAjO,YAAA,EAAAwN,YAAA,EAAAU,qBAAA,EAAAnP,IAAA,EAAAC,GAAA;QAAA,OAAAjY,mBAAA,GAAAuB,IAAA,UAAA6lB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxhB,IAAA,GAAAwhB,UAAA,CAAAnjB,IAAA;YAAA;cACtB+U,YAAY,GAAG;gBAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;gBAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;cAAS,CAAC;cAC3F8N,YAAY,GAAG;gBACnB3C,UAAU,EAAE9X,gBAAgB,CAACtL,KAAK,CAACsO,GAAG;gBACtCsH,QAAQ,EAAEtK,gBAAgB,CAACtL,KAAK,CAAC4V,QAAQ;gBACzCgR,mBAAmB,EAAE;cACvB,CAAC;cAAAD,UAAA,CAAAnjB,IAAA;cAAA,OAC2BoD,SAAS,CAACigB,aAAa,CAACtO,YAAY,EAAEwN,YAAY,CAAC;YAAA;cAAAU,qBAAA,GAAAE,UAAA,CAAA1jB,IAAA;cAAvEqU,IAAI,GAAAmP,qBAAA,CAAJnP,IAAI;cAAEC,GAAG,GAAAkP,qBAAA,CAAHlP,GAAG;cACjB,IAAID,IAAI,KAAK,CAAC,EAAE;gBACdnB,eAAe,CAAC/L,QAAQ,CAACpK,KAAK,EAAE,IAAI,CAAC;gBACrCoT,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;cACvB,CAAC,MAAM;gBACLD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiE,IAAI,EAAEC,GAAG,CAAC;cACnC;YAAC;YAAA;cAAA,OAAAoP,UAAA,CAAArhB,IAAA;UAAA;QAAA,GAAAkhB,SAAA;MAAA,CACF;MAAA,gBAdKF,qBAAqBA,CAAA;QAAA,OAAAC,MAAA,CAAAtgB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAc1B;IACD,IAAM8gB,WAAW;MAAA,IAAAC,MAAA,GAAAhhB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsiB,UAAO7lB,IAAI,EAAE2R,IAAI,EAAEmU,OAAO;QAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAAA,IAAAlE,aAAA,EAAApS,OAAA,EAAAqH,YAAA,EAAAwK,OAAA;QAAA,OAAAzjB,mBAAA,GAAAuB,IAAA,UAAA4mB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAviB,IAAA,GAAAuiB,UAAA,CAAAlkB,IAAA;YAAA;cAC5C,IAAIrC,IAAI,KAAK,QAAQ,EAAEwmB,iBAAiB,CAAC7U,IAAI,aAAJA,IAAI,gBAAAoU,oBAAA,GAAJpU,IAAI,CAAEoC,cAAc,cAAAgS,oBAAA,uBAApBA,oBAAA,CAAsBlX,EAAE,CAAC;cAClE,IAAI7O,IAAI,KAAK,KAAK,EAAEymB,kBAAkB,CAAC9U,IAAI,aAAJA,IAAI,gBAAAqU,qBAAA,GAAJrU,IAAI,CAAEoC,cAAc,cAAAiS,qBAAA,uBAApBA,qBAAA,CAAsBnX,EAAE,CAAC;cAChE,IAAI7O,IAAI,KAAK,KAAK,EAAE0mB,kBAAkB,CAAC/U,IAAI,aAAJA,IAAI,gBAAAsU,qBAAA,GAAJtU,IAAI,CAAEoC,cAAc,cAAAkS,qBAAA,uBAApBA,qBAAA,CAAsBpX,EAAE,CAAC;cAChE,IAAI7O,IAAI,KAAK,MAAM,EAAE2mB,eAAe,CAAChV,IAAI,aAAJA,IAAI,gBAAAuU,qBAAA,GAAJvU,IAAI,CAAEoC,cAAc,cAAAmS,qBAAA,uBAApBA,qBAAA,CAAsBrX,EAAE,CAAC;cAC9D,IAAI7O,IAAI,KAAK,IAAI,EAAE4mB,aAAa,CAACjV,IAAI,aAAJA,IAAI,gBAAAwU,qBAAA,GAAJxU,IAAI,CAAEoC,cAAc,cAAAoS,qBAAA,uBAApBA,qBAAA,CAAsBtX,EAAE,CAAC;cAC1D,IAAI7O,IAAI,KAAK,cAAc,EAAE6mB,uBAAuB,CAAClV,IAAI,aAAJA,IAAI,gBAAAyU,qBAAA,GAAJzU,IAAI,CAAEoC,cAAc,cAAAqS,qBAAA,uBAApBA,qBAAA,CAAsBvX,EAAE,EAAEiX,OAAO,CAAC;cACvF,IAAI9lB,IAAI,KAAK,UAAU,EAAE8mB,mBAAmB,CAACnV,IAAI,aAAJA,IAAI,gBAAA0U,qBAAA,GAAJ1U,IAAI,CAAEoC,cAAc,cAAAsS,qBAAA,uBAApBA,qBAAA,CAAsBxX,EAAE,CAAC;cAAA,MAClE7O,IAAI,KAAK,MAAM;gBAAAumB,UAAA,CAAAlkB,IAAA;gBAAA;cAAA;cACX8f,aAAa,GAAG1c,SAAS,CAAC2c,mBAAmB,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC;cACjFrS,OAAO,GAAG,IAAIoS,aAAa,CAACxQ,IAAI,CAAC;cACjCyF,YAAY,GAAG;gBAAEP,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;gBAAE8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;cAAS,CAAC;cAC3F8K,OAAO,GAAG;gBAAEC,YAAY,EAAE,SAAdA,YAAYA,CAAA,EAAQ,CAAE;cAAE,CAAC;cAAA0E,UAAA,CAAAlkB,IAAA;cAAA,OACrCoD,SAAS,CAACmd,WAAW,CAACxL,YAAY,EAAErH,OAAO,EAAE6R,OAAO,CAAC;YAAA;cAC3DmF,iBAAiB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAR,UAAA,CAAApiB,IAAA;UAAA;QAAA,GAAA0hB,SAAA;MAAA,CAEtB;MAAA,gBAhBKF,WAAWA,CAAAqB,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAtB,MAAA,CAAA9gB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAgBhB;IACD,IAAMkiB,iBAAiB;MAAA,IAAAI,MAAA,GAAAviB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6jB,UAAA;QAAA,IAAAC,sBAAA,EAAAlR,IAAA,EAAAC,GAAA;QAAA,OAAAjY,mBAAA,GAAAuB,IAAA,UAAA4nB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvjB,IAAA,GAAAujB,UAAA,CAAAllB,IAAA;YAAA;cAAAklB,UAAA,CAAAllB,IAAA;cAAA,OACIoD,SAAS,CAAC+e,kBAAkB,CAAC;gBACvD3N,gBAAgB,EAAE5N,QAAQ,CAACpK,KAAK,CAACmB,IAAI;gBACrC8W,QAAQ,EAAE7N,QAAQ,CAACpK,KAAK,CAACiY;cAC3B,CAAC,CAAC;YAAA;cAAAuQ,sBAAA,GAAAE,UAAA,CAAAzlB,IAAA;cAHMqU,IAAI,GAAAkR,sBAAA,CAAJlR,IAAI;cAAEC,GAAG,GAAAiR,sBAAA,CAAHjR,GAAG;cAIjB,IAAID,IAAI,KAAK,CAAC,EAAE;gBACdlE,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;gBACrBqE,aAAa,CAAC,CAAC;cACjB,CAAC,MAAM;gBACLtE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiE,IAAI,EAAEC,GAAG,CAAC;cACnC;YAAC;YAAA;cAAA,OAAAmR,UAAA,CAAApjB,IAAA;UAAA;QAAA,GAAAijB,SAAA;MAAA,CACF;MAAA,gBAXKL,iBAAiBA,CAAA;QAAA,OAAAI,MAAA,CAAAriB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWtB;IACD,IAAM2hB,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAI3X,EAAE,EAAK;MAAA,IAAA2Y,WAAA,EAAAC,YAAA;MAChC/c,MAAM,CAAC7L,KAAK,GAAGgQ,EAAE,GAAG,EAAA2Y,WAAA,GAAC1hB,IAAI,CAACjH,KAAK,cAAA2oB,WAAA,uBAAVA,WAAA,CAAYpZ,SAAS,EAAES,EAAE,CAAC,GAAG,EAAA4Y,YAAA,GAAC3hB,IAAI,CAACjH,KAAK,cAAA4oB,YAAA,uBAAVA,YAAA,CAAYrZ,SAAS,CAAC;MACzEzD,eAAe,CAAC9L,KAAK,GAAG,IAAI;IAC9B,CAAC;IACD,IAAM4nB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI5X,EAAE,EAAK;MACjCjE,MAAM,CAAC/L,KAAK,GAAGgQ,EAAE;MACjBhE,OAAO,CAAChM,KAAK,GAAG,IAAI;IACtB,CAAC;IACD,IAAM6nB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI7X,EAAE,EAAK;MACjCjE,MAAM,CAAC/L,KAAK,GAAGgQ,EAAE;MACjB/D,OAAO,CAACjM,KAAK,GAAG,IAAI;IACtB,CAAC;IACD,IAAM8nB,eAAe,GAAG,SAAlBA,eAAeA,CAAI9X,EAAE,EAAK;MAC9BjE,MAAM,CAAC/L,KAAK,GAAGgQ,EAAE;MACjB9D,QAAQ,CAAClM,KAAK,GAAG,IAAI;IACvB,CAAC;IACD,IAAM+nB,aAAa,GAAG,SAAhBA,aAAaA,CAAI/X,EAAE,EAAK;MAC5BjE,MAAM,CAAC/L,KAAK,GAAGgQ,EAAE;MACjB7D,MAAM,CAACnM,KAAK,GAAG,IAAI;IACrB,CAAC;IACD,IAAMgoB,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIhY,EAAE,EAAEiX,OAAO,EAAK;MAC/Clb,MAAM,CAAC/L,KAAK,GAAGgQ,EAAE;MACjB5D,YAAY,CAACpM,KAAK,GAAGinB,OAAO;MAC5B5a,gBAAgB,CAACrM,KAAK,GAAG,IAAI;IAC/B,CAAC;IACD,IAAMioB,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIjY,EAAE,EAAK;MAClCjE,MAAM,CAAC/L,KAAK,GAAGgQ,EAAE;MACjB1D,YAAY,CAACtM,KAAK,GAAG,IAAI;IAC3B,CAAC;IACD,IAAM6oB,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB9c,MAAM,CAAC/L,KAAK,GAAGoK,QAAQ,CAACpK,KAAK,CAACiY,QAAQ,CAAC5S,KAAK,CAAC6B,aAAa,CAAClH,KAAK,CAACqE,MAAM,CAAC;MACxEuI,QAAQ,CAAC5M,KAAK,GAAG,IAAI;IACvB,CAAC;IACD,IAAMyO,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAI4F,GAAG,EAAK;MACjC3H,MAAM,CAAC1M,KAAK,GAAGqU,GAAG,CAACrE,EAAE;MACrBlD,eAAe,CAAC9M,KAAK,GAAG,IAAI;IAC9B,CAAC;IACD,IAAM8oB,cAAc,GAAG,SAAjBA,cAAcA,CAAIhW,IAAI,EAAK;MAC/BvG,WAAW,CAACvM,KAAK,GAAG,KAAK;MACzB,IAAI8S,IAAI,EAAEzJ,IAAI,CAAC,MAAM,EAAEyJ,IAAI,CAAC;MAC5BhH,eAAe,CAAC9L,KAAK,GAAG,KAAK;IAC/B,CAAC;IACD,IAAM+oB,WAAW;MAAA,IAAAC,MAAA,GAAAjjB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAukB,UAAO9nB,IAAI,EAAE2R,IAAI;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAAqoB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhkB,IAAA,GAAAgkB,UAAA,CAAA3lB,IAAA;YAAA;cACnC+I,WAAW,CAACvM,KAAK,GAAG,KAAK;cAAA,KACrBmB,IAAI;gBAAAgoB,UAAA,CAAA3lB,IAAA;gBAAA;cAAA;cAAA2lB,UAAA,CAAA3lB,IAAA;cAAA,OACAsV,UAAU,CAAC,CAAC;YAAA;cAClB2K,uBAAuB,CAAC3Q,IAAI,CAAC;YAAA;cAE/B9G,OAAO,CAAChM,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAmpB,UAAA,CAAA7jB,IAAA;UAAA;QAAA,GAAA2jB,SAAA;MAAA,CACtB;MAAA,gBAPKF,WAAWA,CAAAK,IAAA,EAAAC,IAAA;QAAA,OAAAL,MAAA,CAAA/iB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAOhB;IACD,IAAMsjB,WAAW;MAAA,IAAAC,MAAA,GAAAxjB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8kB,UAAOroB,IAAI,EAAE2R,IAAI;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAA4oB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvkB,IAAA,GAAAukB,UAAA,CAAAlmB,IAAA;YAAA;cACnC+I,WAAW,CAACvM,KAAK,GAAG,KAAK;cAAA,KACrBmB,IAAI;gBAAAuoB,UAAA,CAAAlmB,IAAA;gBAAA;cAAA;cAAAkmB,UAAA,CAAAlmB,IAAA;cAAA,OACAsV,UAAU,CAAC,CAAC;YAAA;cAClB2K,uBAAuB,CAAC3Q,IAAI,CAAC;YAAA;cAE/B7G,OAAO,CAACjM,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAA0pB,UAAA,CAAApkB,IAAA;UAAA;QAAA,GAAAkkB,SAAA;MAAA,CACtB;MAAA,gBAPKF,WAAWA,CAAAK,IAAA,EAAAC,IAAA;QAAA,OAAAL,MAAA,CAAAtjB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAOhB;IACD,IAAM6jB,YAAY;MAAA,IAAAC,MAAA,GAAA/jB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqlB,UAAO5oB,IAAI,EAAE2R,IAAI;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAAmpB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9kB,IAAA,GAAA8kB,UAAA,CAAAzmB,IAAA;YAAA;cACpC+I,WAAW,CAACvM,KAAK,GAAG,KAAK;cAAA,KACrBmB,IAAI;gBAAA8oB,UAAA,CAAAzmB,IAAA;gBAAA;cAAA;cAAAymB,UAAA,CAAAzmB,IAAA;cAAA,OACAsV,UAAU,CAAC,CAAC;YAAA;cAClBoR,UAAU,CAAC,CAAC;cACZzG,uBAAuB,CAAC3Q,IAAI,CAAC;YAAA;cAE/B5G,QAAQ,CAAClM,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAiqB,UAAA,CAAA3kB,IAAA;UAAA;QAAA,GAAAykB,SAAA;MAAA,CACvB;MAAA,gBARKF,YAAYA,CAAAM,IAAA,EAAAC,IAAA;QAAA,OAAAN,MAAA,CAAA7jB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQjB;IACD,IAAMqkB,oBAAoB;MAAA,IAAAC,MAAA,GAAAvkB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6lB,UAAOppB,IAAI,EAAE2R,IAAI;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAA2pB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtlB,IAAA,GAAAslB,UAAA,CAAAjnB,IAAA;YAAA;cAC5C+I,WAAW,CAACvM,KAAK,GAAG,KAAK;cACzB,IAAImB,IAAI,EAAE;gBACRwhB,qBAAqB,CAAC7P,IAAI,CAAC;cAC7B;cACAzG,gBAAgB,CAACrM,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAyqB,UAAA,CAAAnlB,IAAA;UAAA;QAAA,GAAAilB,SAAA;MAAA,CAC/B;MAAA,gBANKF,oBAAoBA,CAAAK,IAAA,EAAAC,IAAA;QAAA,OAAAL,MAAA,CAAArkB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMzB;IACD,IAAM4kB,gBAAgB;MAAA,IAAAC,MAAA,GAAA9kB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAomB,UAAO3pB,IAAI,EAAE2R,IAAI;QAAA,OAAAxT,mBAAA,GAAAuB,IAAA,UAAAkqB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7lB,IAAA,GAAA6lB,UAAA,CAAAxnB,IAAA;YAAA;cACxC+I,WAAW,CAACvM,KAAK,GAAG,KAAK;cAAA,KACrBmB,IAAI;gBAAA6pB,UAAA,CAAAxnB,IAAA;gBAAA;cAAA;cAAAwnB,UAAA,CAAAxnB,IAAA;cAAA,OACAsV,UAAU,CAAC,CAAC;YAAA;cAClB2K,uBAAuB,CAAC3Q,IAAI,CAAC;YAAA;cAE/BxG,YAAY,CAACtM,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAgrB,UAAA,CAAA1lB,IAAA;UAAA;QAAA,GAAAwlB,SAAA;MAAA,CAC3B;MAAA,gBAPKF,gBAAgBA,CAAAK,IAAA,EAAAC,IAAA;QAAA,OAAAL,MAAA,CAAA5kB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAOrB;IACD,IAAMmlB,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBte,cAAc,CAAC7M,KAAK,GAAG,IAAI;IAC7B,CAAC;IACD,IAAMorB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/Bze,WAAW,CAAC3M,KAAK,GAAGmN,IAAI,CAAC,CAAC;MAC1BN,cAAc,CAAC7M,KAAK,GAAG,KAAK;MAC5B8M,eAAe,CAAC9M,KAAK,GAAG,KAAK;IAC/B,CAAC;IACD,IAAMkqB,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB7gB,IAAI,CAAC,MAAM,CAAC;IACd,CAAC;IACD,IAAMqO,aAAa,GAAG,SAAhBA,aAAaA,CAAIvW,IAAI,EAAE2R,IAAI,EAAK;MACpCzJ,IAAI,CAAC,SAAS,EAAElI,IAAI,EAAE2R,IAAI,CAAC;IAC7B,CAAC;IACDtM,WAAW,CAAC,YAAM;MAChB,IAAImD,UAAU,EAAE;QACd,IAAMoJ,WAAW,GAAGkB,IAAI,CAACwG,SAAS,CAAC7P,iBAAiB,CAAC5K,KAAK,CAAC;QAC3D,IAAI6J,MAAM,CAAC7J,KAAK,EAAE0S,wBAAwB,CAAC7I,MAAM,CAAC7J,KAAK,EAAE+S,WAAW,CAAC;MACvE;IACF,CAAC,CAAC;IACFtM,KAAK,CACH;MAAA,OAAMoD,MAAM,CAAC7J,KAAK;IAAA,GAClB,UAACqrB,QAAQ,EAAEC,QAAQ,EAAK;MACtB,IAAI3hB,UAAU,EAAE;QACd,IAAMoJ,WAAW,GAAGkB,IAAI,CAACwG,SAAS,CAAC7P,iBAAiB,CAAC5K,KAAK,CAAC;QAC3D4K,iBAAiB,CAAC5K,KAAK,GAAG,CAAC,CAAC;QAC5B6K,kBAAkB,CAAC7K,KAAK,GAAG,EAAE;QAC7B,IAAIsrB,QAAQ,EAAE5Y,wBAAwB,CAAC4Y,QAAQ,EAAEvY,WAAW,CAAC;QAC7D,IAAIsY,QAAQ,EAAE7X,wBAAwB,CAAC6X,QAAQ,CAAC;MAClD;MACA,IAAIxhB,MAAM,CAAC7J,KAAK,EAAE;QAChB,KAAK,IAAIub,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGpS,KAAK,CAACe,QAAQ,CAAC7F,MAAM,EAAEkX,KAAK,EAAE,EAAE;UAC1D,IAAMpN,IAAI,GAAGhF,KAAK,CAACe,QAAQ,CAACqR,KAAK,CAAC;UAClC,IAAI1R,MAAM,CAAC7J,KAAK,KAAKmO,IAAI,CAAC6B,EAAE,EAAEmG,eAAe,CAAChI,IAAI,CAAC;QACrD;MACF;IACF,CAAC,EACD;MAAEod,SAAS,EAAE;IAAK,CACpB,CAAC;IACD9kB,KAAK,CACH;MAAA,OAAMyD,QAAQ,CAAClK,KAAK;IAAA,GACpB,YAAM;MACJ,IAAI6J,MAAM,CAAC7J,KAAK,EAAE;QAChB,IAAIwrB,MAAM,GAAG,IAAI;QACjB,KAAK,IAAIjQ,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGpS,KAAK,CAACe,QAAQ,CAAC7F,MAAM,EAAEkX,KAAK,EAAE,EAAE;UAC1D,IAAMpN,IAAI,GAAGhF,KAAK,CAACe,QAAQ,CAACqR,KAAK,CAAC;UAClC,IAAI1R,MAAM,CAAC7J,KAAK,KAAKmO,IAAI,CAAC6B,EAAE,EAAE;YAC5Bwb,MAAM,GAAG,KAAK;YACdphB,QAAQ,CAACpK,KAAK,GAAGmO,IAAI;UACvB;QACF;QACA,IAAIqd,MAAM,EAAE;UACV3hB,MAAM,CAAC7J,KAAK,GAAG,EAAE;UACjBoK,QAAQ,CAACpK,KAAK,GAAG,CAAC,CAAC;QACrB;MACF;IACF,CAAC,EACD;MAAEurB,SAAS,EAAE;IAAK,CACpB,CAAC;IACDE,QAAY,CAAC;MAAExS;IAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}