<!--
 * @FileDescription: 首页展示
 * @Author: 耿培宣
 * @Date: 2025/05/29
 * @LastEditors: 耿培宣
 * @LastEditTime: 2025/05/29
 -->
<template style="overflow: hidden;">
  <div class="homePageNavigationBar">
    <template v-for="(item, index) in subMenus" :key="index">
      <span class="nav-item" :class="{ active: activeNavItem === index }" @click="handleWorkBench(item, index)">{{
        item.name
      }}</span>
      <span v-if="index < subMenus.length - 1" class="separator">|</span>
    </template>
  </div>
  <el-scrollbar class="homePage">
    <section class="zy-el-container">
      <div class="top-section">
        <div class="carousel-container">
          <el-carousel :interval="5000" indicator-position="none" @change="handleCarouselChange" ref="carouselRef">
            <el-carousel-item v-for="item in newsData" :key="item.id" @click="newsHandle(item)">
              <img v-if="item.infoPic" :src="imgUrl(item.infoPic)" class="carousel-img" />
              <div v-else class="carousel-img placeholder-img"></div>
              <div class="carousel-overlay">
                <div class="overlay-title">{{ item.infoTitle }}</div>
                <div class="overlay-indicator">
                  <span v-for="(dot, index) in newsData.length" :key="index" class="indicator-dot"
                    :class="{ active: index === currentCarouselIndex }" @click="handleIndicatorClick(index)"></span>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <div class="news-tabs-container">
          <div class="news-tabs-header">
            <div class="tabs-scroll-container">
              <div class="news-tab-item" v-for="(item, index) in newColumnData"
                :class="{ active: activeNewsTabIndex === index }" @click="clickNewColumn(item, index)">
                <span>{{ item.name }}</span>
              </div>
            </div>
            <div class="more-link" @click="openWinNews(informationList[activeNewsTabIndex].id, 2)">更多 ></div>
          </div>
          <div class="news-tabs-content">
            <div class="news-list" v-loading="loadingInformationList">
              <template v-if="informationList.length === 0">
                <div class="list-item"
                  style="display: flex;align-items: center;justify-content: center;color: #999;width:100%;">暂无数据</div>
              </template>
              <template v-else>
                <div v-for="item in informationList" :key="item.id" class="news-item" @click="newsHandle(item)">
                  <span class="dot">•</span>
                  <span class="title">{{ item.infoTitle }}</span>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-section">
        <!-- 通知公告 -->
        <div class="committee-work-container">
          <div class="committee_title_box">
            <div class="committee_title">通知公告</div>
            <div class="more-link" @click="openWinNotice">更多 ></div>
          </div>
          <div class="list-container" v-loading="loadingNoticeList">
            <template v-if="noticeData.length === 0">
              <div class="list-item"
                style="display: flex;align-items: center;justify-content: center;color: #999;width:100%;">暂无数据</div>
            </template>
            <template v-else>
              <div v-for="item in noticeData" :key="item.id" class="list-item" @click="noticeInfo(item)">
                <span class="dot">•</span>
                <div>
                  <div class="title">{{ item.theme }}</div>
                  <div class="date">{{ formatTime(item.createDate) }}</div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <!-- 委员风采 -->
        <div class="committee-work-container">
          <div class="committee_title_box">
            <div class="committee_title">委员风采</div>
            <div class="more-link" @click="openWinNews(null, 3)">更多 ></div>
          </div>
          <div class="list-container" v-loading="loadingMemberStyle">
            <template v-if="memberStyleData.length === 0">
              <div class="list-item"
                style="display: flex;align-items: center;justify-content: center;color: #999;width:100%;">暂无数据</div>
            </template>
            <template v-else>
              <div v-for="item in memberStyleData" :key="item.id" class="list-item" @click="newsHandle(item)">
                <span class="dot">•</span>
                <div>
                  <div class="title" :title="item.infoTitle">{{ item.infoTitle }}</div>
                  <div class="date">{{ formatTime(item.createDate) }}</div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <!-- 委员眼中的西安 -->
        <div class="committee-work-container">
          <div class="committee_title_box">
            <div class="committee_title">委员眼中的西安</div>
            <div class="more-link" @click="openWinNews(null, 4)">更多 ></div>
          </div>
          <div class="list-container" v-loading="loadingCommitteeMembersList">
            <template v-if="committeeMembersData.length === 0">
              <div class="list-item"
                style="display: flex;align-items: center;justify-content: center;color: #999;width:100%;">暂无数据</div>
            </template>
            <template v-else>
              <div v-for="item in committeeMembersData" :key="item.id" class="list-item" @click="newsHandle(item)">
                <span class="dot">•</span>
                <div>
                  <div class="title">{{ item.infoTitle }}</div>
                  <div class="date">{{ formatTime(item.createDate) }}</div>
                </div>
              </div>
            </template>

          </div>
        </div>
      </div>
    </section>
  </el-scrollbar>
  <xyl-popup-window v-model="newsShow" name="详情">
    <AllInformationDetail :id="id" @callback="callback"></AllInformationDetail>
  </xyl-popup-window>
  <xyl-popup-window v-model="noticeShow" name="通知公告详情">
    <NoticeAnnouncementDetails :id="id" @callback="callback"></NoticeAnnouncementDetails>
  </xyl-popup-window>
</template>
<script>
export default { name: 'homePage' }
</script>
<script setup>
import api from '@/api'
import { onMounted, ref, inject, computed } from 'vue'
import AllInformationDetail from './components/AllInformationDetail'
import NoticeAnnouncementDetails from './components/NoticeAnnouncementDetails'
const openPage = inject('openPage')
const leftMenuData = inject('leftMenuData')
const menuListData = inject('WorkBenchList')
const activeNavItem = ref(0)
const imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')
const subMenus = computed(() => {
  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')
  const newArr = filtered.filter(item => item.name == '综合应用')
  const result = newArr[0]?.children.filter(child => child.name !== '系统运维' && child.name !== '我的')
  return result
})
const handleWorkBench = (item, index) => {
  activeNavItem.value = index
  leftMenuData(item)
}
// 资讯轮播图
const newsData = ref([])
const currentCarouselIndex = ref(0)
const carouselRef = ref(null)
// 资讯栏目
const activeNewsTabIndex = ref(0)
const newColumnData = ref([])
// 资讯列表
const informationList = ref([])
const loadingInformationList = ref(false)
// 通知公告
const loadingNoticeList = ref(false)
const noticeData = ref([])
// 委员风采
const loadingMemberStyle = ref(false)
const memberStyleData = ref([])
// 委员眼中的西安
const loadingCommitteeMembersList = ref(false)
const committeeMembersData = ref([])

const id = ref('')
const noticeShow = ref(false)
const newsShow = ref(false)

// 获取资讯轮播图
const getNewsData = async () => {
  let params = {
    isAnd: 1,
    objectParam: {},
    orderBys: [],
    pageNo: 1,
    pageSize: 10,
    query: { columnId: '1887325961586761729', moduleId: '1', passFlag: '' },
    tableId: 'zy_news_content_1',
    wheres: [
      { columnId: 'zy_news_content_1_is_top', queryType: 'EQ', value: '1' }
    ]
  }
  const res = await api.newsContentList(params)
  var { data } = res
  newsData.value = data || []
  getSpecialCommitteeData() // 通知公告
  if (window.location.host === 'localhost:2000' || window.location.host === 'xazx.cszysoft.com:8131') {
    getSuggestionData('1735594801618776066') // 假设这是委员风采栏目ID  测试
    getCommitteeMembersData('1721347440629542913')
  } else {
    getSuggestionData('1928270222481985537') // 假设这是委员风采栏目ID  正式
    getCommitteeMembersData('1928269626215534594')
  }
}

// 处理轮播图指示器点击事件
const handleIndicatorClick = (index) => {
  if (carouselRef.value) {
    carouselRef.value.setActiveItem(index);
  }
}

// 处理轮播图切换事件
const handleCarouselChange = (index) => {
  currentCarouselIndex.value = index;
}

// 获取资讯栏目
const getNewsColumnData = async () => {
  let params = {
    pageNo: 1,
    pageSize: 10,
    query: { moduleId: '1' }
  }
  const res = await api.newsColumnList(params)
  var { data } = res
  console.log('获取资讯栏目数据', data)
  newColumnData.value = data || []
  getInformationData(data[0].id) // 默认获取第一个栏目下的数据
}

// 点击资讯栏目
const clickNewColumn = (item, index) => {
  activeNewsTabIndex.value = index
  getInformationData(item.id)
}

// 获取资讯栏目下的数据
const getInformationData = async (id) => {
  loadingInformationList.value = true
  let params = {
    isAnd: 1,
    objectParam: {},
    orderBys: [],
    pageNo: 1,
    pageSize: 10,
    query: { columnId: id, moduleId: '1', passFlag: '' },
    tableId: 'zy_news_content_1',
    wheres: []
  }
  const res = await api.newsContentList(params)
  var { data } = res
  informationList.value = data || []
  loadingInformationList.value = false
}

// 获取通知公告
const getSpecialCommitteeData = async (id) => {
  loadingNoticeList.value = true
  let params = {
    isAnd: 1,
    isSelectForManager: 1,
    orderBys: [],
    pageNo: 1,
    pageSize: 5,
    query: { isDraft: 0, channelId: null },
    tableId: 'id_message_notification',
    wheres: []
  }
  const res = await api.noticeHomePage(params)
  var { data } = res
  noticeData.value = data || []
  loadingNoticeList.value = false
}

// 获取委员风采栏目下的数据
const getSuggestionData = async (id) => {
  loadingMemberStyle.value = true
  let params = {
    isAnd: 1,
    objectParam: {},
    orderBys: [],
    pageNo: 1,
    pageSize: 10,
    query: { columnId: id, moduleId: '7', passFlag: '' },
    tableId: 'content_information_7',
    wheres: []
  }
  const res = await api.newsContentList(params)
  var { data } = res
  memberStyleData.value = data || []
  loadingMemberStyle.value = false
}

// 获取委员眼中的西安栏目下的数据
const getCommitteeMembersData = async (id) => {
  loadingCommitteeMembersList.value = true
  let params = {
    isAnd: 1,
    objectParam: {},
    orderBys: [],
    pageNo: 1,
    pageSize: 10,
    query: { columnId: id, moduleId: '7', passFlag: '' },
    tableId: 'content_information_7',
    wheres: []
  }
  const res = await api.newsContentList(params)
  var { data } = res
  committeeMembersData.value = data || []
  loadingCommitteeMembersList.value = false
}

// 时间转换
const formatTime = (time) => {
  time = Number(time)
  return new Date(time).toLocaleString()
}

// 资讯详情处理
const newsHandle = (item) => {
  id.value = item.id
  newsShow.value = true
}

// 通知公告详情
const noticeInfo = (item) => {
  id.value = item.id
  noticeShow.value = true
}

// 打开资讯列表
const openWinNews = async (_item, _type) => {
  let id = ''
  if (_type == 2) {
    openPage({ key: 'routePath', value: '/information/AllInformationPublicList?moduleId=1' })
  } else if (_type == 3) {
    if (window.location.host === 'localhost:2000' || window.location.host === 'xazx.cszysoft.com:8131') {
      id = '1735594801618776066'
    } else {
      id = '1928270222481985537'
    }
    openPage({ key: 'routePath', value: '/information/MemberStyleList?moduleId=7&columnId=' + id })
  } else if (_type == 4) {
    if (window.location.host === 'localhost:2000' || window.location.host === 'xazx.cszysoft.com:8131') {
      id = '1721347440629542913'
    } else {
      id = '1928269626215534594'
    }
    openPage({ key: 'routePath', value: '/information/CommitteeEyesXiAn?moduleId=7&columnId=' + id })
  }
}

// 打开通知公告更多
const openWinNotice = () => {
  openPage({ key: 'routePath', value: '/interaction/NoticeAnnouncementList' })
}

// 弹窗回调
const callback = () => {
  id.value = ""
  newsShow.value = false
  noticeShow.value = false
}

onMounted(() => {
  getNewsData()
  getNewsColumnData()
})
</script>

<style lang="scss" scoped>
body {
  margin: 0;
  padding: 0;
}

.homePageNavigationBar {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;

  .nav-item {
    color: #4f4f4f;
    font-size: 18px;
    cursor: pointer;
    padding: 0 10px;
    white-space: nowrap;

    &:hover {
      color: #0056b3;
    }

    &.active {
      color: #007bff;
      font-weight: bold;
    }
  }

  .separator {
    color: #d3d3d3;
    margin: 0 5px;
  }
}

.homePage {
  width: calc(100% - 500px);
  margin: auto;
  height: calc(100% - 64px);
  background: #fff;
  padding: 20px;
  box-sizing: border-box;

  .zy-el-container {
    display: flex;
    flex-direction: column;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 0;
    overflow: hidden;

    .top-section,
    .bottom-section {
      display: flex;
      gap: 35px;
      padding: 30px 40px;
      background-color: #fff;
    }

    .top-section {
      border-bottom: 1px solid #eee;
      height: 450px;

      .carousel-container {
        width: 40%;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        .zy-el-carousel {
          width: 100%;
          height: 100%;
        }

        .carousel-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .placeholder-img {
          width: 100%;
          height: 100%;
          background-color: #ccc;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #666;
          font-size: 18px;
        }

        .carousel-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background-color: rgba(0, 0, 0, 0.5);
          color: white;
          padding: 15px 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          z-index: 1;

          .overlay-title {
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex-grow: 1;
            margin-right: 10px;
          }

          .overlay-indicator {
            display: flex;
            gap: 5px;
            z-index: 10;

            .indicator-dot {
              width: 8px;
              height: 8px;
              background-color: rgba(255, 255, 255, 0.5);
              border-radius: 50%;
              cursor: pointer;
            }

            .indicator-dot.active {
              background-color: white;
            }
          }
        }
      }

      .carousel-container :deep(.zy-el-carousel__container) {
        height: 100% !important;
      }

      .carousel-container :deep(.zy-el-carousel__indicators) {
        display: none;
      }

      .news-tabs-container {
        width: 60%;
        background: rgb(248, 249, 253);
        border-radius: 8px;
        padding: 20px 0;
        position: relative;

        .news-tabs-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 20px;
          position: relative;

          .tabs-scroll-container {
            display: flex;
            align-items: center;
            overflow-x: auto;
            overflow-y: hidden;
            max-width: calc(100% - 80px);
            white-space: nowrap;
            padding-bottom: 8px;
          }

          .news-tab-item {
            font-size: 20px;
            color: #606266;
            padding: 0 15px;
            height: 40px;
            line-height: 40px;
            cursor: pointer;
            background-color: #fefefe;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-right: 10px;
            flex-shrink: 0;

            &.active {
              color: #fff;
              background-color: #007bff;
              font-weight: bold;
              position: relative;

              &::after {
                content: '';
                position: absolute;
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 10px solid #007bff;
              }
            }
          }
        }

        .news-tabs-content {
          height: 100%;

          .news-list {
            margin-top: 10px;
            padding: 0 20px;
            height: calc(100% - 50px);
            overflow: hidden;

            .news-item {
              margin-bottom: 20px;
              font-size: 18px;
              color: #333;
              display: flex;
              align-items: flex-start;
              cursor: pointer;

              .dot {
                margin-right: 5px;
                color: #8a8a8a;
                flex-shrink: 0;
                margin-right: 10px;
              }

              .title {
                flex-grow: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
    }

    .bottom-section {
      padding: 15px 40px;
      height: 370px;
      width: 100%;

      .committee-work-container {
        width: 33.33%;
      }

      .committee-work-container,
      .suggestions-container {
        background-color: #ffffff;
        border-radius: 8px;
        position: relative;
        height: 100%;

        .committee_title_box {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-bottom: 10px;

          .committee_title {
            font-size: 20px;
            color: #333;
            font-weight: bold;
          }
        }

        .list-container {
          height: calc(100% - 35px);

          .list-item {
            margin-bottom: 15px;
            font-size: 18px;
            color: #333;
            display: flex;
            align-items: flex-start;
            cursor: pointer;

            .dot {
              margin-right: 5px;
              color: #007bff;
              flex-shrink: 0;
            }

            .title {
              flex-grow: 1;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              margin-right: 10px;
              line-height: 26px;
            }

            .date {
              font-size: 14px;
              color: #999;
              flex-shrink: 0;
              margin-top: 6px;
            }
          }
        }

        .suggestions_title_box {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-bottom: 10px;

          .suggestions_title {
            font-size: 20px;
            color: #333;
            font-weight: bold;
          }
        }
      }
    }
  }
}

/* 确保滚动条样式生效的全局样式 */
.news-tabs-container .tabs-scroll-container::-webkit-scrollbar {
  height: 8px !important;
}

.news-tabs-container .tabs-scroll-container::-webkit-scrollbar-track {
  background: #f5f5f5 !important;
  border-radius: 10px !important;
  margin: 0 4px !important;
}

.news-tabs-container .tabs-scroll-container::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #d0d0d0, #b8b8b8, #d0d0d0) !important;
  border-radius: 10px !important;
  border: 1px solid #e0e0e0 !important;
  transition: all 0.3s ease !important;
}

.news-tabs-container .tabs-scroll-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #b8b8b8, #9a9a9a, #b8b8b8) !important;
  border-color: #c0c0c0 !important;
}

.news-tabs-container .tabs-scroll-container::-webkit-scrollbar-thumb:active {
  background: linear-gradient(90deg, #a0a0a0, #808080, #a0a0a0) !important;
}

@media screen and (max-width: 1280px) {
  .news-header {
    display: flex;
    margin-bottom: 30px;
    height: 100%;
    cursor: pointer;
    flex-direction: column;

    img {
      max-width: 100%;
      height: 172px;
      margin-right: 20px;
    }
  }
}

.more-link {
  cursor: pointer;
  color: #989898;
}
</style>
