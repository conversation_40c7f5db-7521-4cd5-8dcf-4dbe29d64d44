<template>
  <div class="UnitSuggestTransact">
    <xyl-label v-model="labelId" @labelClick="handleLabel">
      <xyl-label-item v-for="item in labelList" :key="item.itemCode" :value="item.itemCode">
        {{ item.itemName }}（{{ item.count }}）
      </xyl-label-item>
    </xyl-label>
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #mainHandleOffices="scope">
            <template v-if="scope.row.mainHandleOffices && scope.row.mainHandleOffices.length > 0">
              <el-popover placement="top-start" v-for="item in scope.row.mainHandleOffices" :key="item.id"
                :disabled="item.users == null" popper-class="SuggestUnitPopper">
                <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                  {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
                </div>
                <div class="SuggestUnitPopperText">经办人：
                  <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">{{ index == 0 ? '' :
                    '，' }}{{ items.userName }}（{{ items.mobile }}）</span>
                </div>
                <template #reference>
                  <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                    {{ item.flowHandleOfficeName }}</span>
                </template>
              </el-popover>
            </template>
            <template v-if="scope.row.publishHandleOffices && scope.row.publishHandleOffices.length > 0">
              <el-popover placement="top-start" v-for="(item, i) in scope.row.publishHandleOffices" :key="item.id"
                :disabled="item.users == null" popper-class="SuggestUnitPopper">
                <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                  {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
                </div>
                <div class="SuggestUnitPopperText">经办人：
                  <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">{{ index == 0 ? '' :
                    '，' }}{{ items.userName }}（{{ items.mobile }}）</span>
                </div>
                <template #reference>
                  <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                    {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}</span>
                </template>
              </el-popover>
            </template>
          </template>
          <template #assistHandleOffices="scope">
            <template v-if="scope.row.assistHandleOffices && scope.row.assistHandleOffices.length > 0">
              <el-popover placement="top-start" v-for="(item, i) in scope.row.assistHandleOffices" :key="item.id"
                :disabled="item.users == null" popper-class="SuggestUnitPopper">
                <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                  {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
                </div>
                <div class="SuggestUnitPopperText">经办人：
                  <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">{{ index == 0 ? '' :
                    '，' }}{{ items.userName }}（{{ items.mobile }}）</span>
                </div>
                <template #reference>
                  <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                    {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}</span>
                </template>
              </el-popover>
            </template>
            <template v-else>
              <el-popover placement="top-start" v-for="(item, i) in scope.row.assistHandleVoList" :key="item.id"
                :disabled="item.users == null" popper-class="SuggestUnitPopper">
                <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                  {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
                </div>
                <div class="SuggestUnitPopperText">经办人：
                  <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">{{ index == 0 ? '' :
                    '，' }}{{ items.userName }}（{{ items.mobile }}）</span>
                </div>
                <template #reference>
                  <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                    {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}</span>
                </template>
              </el-popover>
            </template>

          </template>
          <!-- <template #publishHandleOffices="scope">
            <el-popover placement="top-start" v-for="(item, i) in scope.row.publishHandleOffices" :key="item.id"
              :disabled="item.users == null" popper-class="SuggestUnitPopper">
              <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
              </div>
              <div class="SuggestUnitPopperText">
                经办人：
                <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">
                  {{ index == 0 ? '' : '，' }}{{ items.userName }}（{{ items.mobile }}）
                </span>
              </div>
              <template #reference>
                <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                  {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}
                </span>
              </template>
            </el-popover>
          </template> -->
        </xyl-global-table>
        <xyl-global-table-button label="" :editCustomTableHead="handleEditorCustom">
          <template #default="scope">
            <el-tooltip effect="dark" :content="timeText(scope.row.handleOfficeAnswerStopDate)" placement="top-start">
              <div :style="timeColor(scope.row.handleOfficeAnswerStopDate)" class="SuggestTimeIcon"></div>
            </el-tooltip>
          </template>
        </xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="办理中提案" :exportId="exportId" :params="exportParams" module="proposalExportExcel"
        tableId="id_prop_proposal_company_handling" @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'UnitSuggestTransact' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
const buttonList = [
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
]
const labelId = ref('')
const labelList = ref([])
const isOpenAutoRead = ref(true)
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: 'id_prop_proposal_company_handling', tableApi: 'suggestionList' })

onActivated(() => {
  globalReadConfig()
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    suggestionCountSelector()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    suggestionCountSelector()
  }
})
const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: ['proposal_office_download_read'] })
  if (data.proposal_office_download_read) {
    isOpenAutoRead.value = data.proposal_office_download_read === 'true' ? true : false
  } else {
    isOpenAutoRead.value = true
  }
}

const suggestionCountSelector = async () => {
  const { data } = await api.suggestionCountSelector({ countItemType: 'handling' })
  labelId.value = data[0].itemCode
  labelList.value = data
  handleLabel()
}
const handleLabel = () => {
  tableQuery.value = { countItemCode: labelId.value || null }
  handleQuery()
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'exportWord':
      suggestExportWord(handleGetParams(), isOpenAutoRead.value)
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id, type: 'unit' } }
  })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
}
const colorObj = (state, type) => {
  var color = { color: '#000' }
  if (state === 'has_answer') {
    color.color = '#4fcc72'
  } else if (state === 'handling') {
    color.color = '#fbd536'
  } else if (state === 'apply_adjust') {
    color.color = '#ca6063'
  } else if (state === 'suggestionHandling') {
    color.color = '#fbd536'
  }
  if (type) {
    color = { color: '#000' }
  }

  return color
}
const timeColor = (time) => {
  var color = { backgroundColor: 'red' }
  if (time > Date.now() + 3600 * 1000 * 24 * 30) {
    color.backgroundColor = 'yellow'
  }
  if (time > Date.now() + 3600 * 1000 * 24 * 60) {
    color.backgroundColor = 'green'
  }
  return color
}
const timeText = (time) => {
  var text = '答复期限小于30天'
  if (time > Date.now() + 3600 * 1000 * 24 * 30) {
    text = '答复期限大于等于30天小于60天'
  }
  if (time > Date.now() + 3600 * 1000 * 24 * 60) {
    text = '答复期限大于60天'
  }
  return text
}
</script>
<style lang="scss">
.UnitSuggestTransact {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - ((var(--zy-height) * 2) + (var(--zy-distance-four) * 4) + 42px));
  }

  .SuggestTimeIcon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: auto;
  }
}

.SuggestUnitPopper {
  width: 500px !important;

  .SuggestUnitPopperName {
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    padding-bottom: var(--zy-font-name-distance-five);
  }

  .SuggestUnitPopperText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
  }
}
</style>
