{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport api from '@/api';\nimport { ref, computed, watch } from 'vue';\nimport { downloadFile } from '../../config/MicroGlobal';\nimport { globalFileLocation } from '../../config/location';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'XylUploadFile'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    max: {\n      type: Number,\n      default: 0\n    },\n    apiName: {\n      type: String,\n      default: 'globalUpload'\n    },\n    // 上传的api\n    fileData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    fileType: {\n      type: Array,\n      default: function _default() {\n        return ['jpg', 'png', 'gif', 'jpeg', 'txt', 'doc', 'docx', 'wps', 'ppt', 'pptx', 'pdf', 'ofd', 'xls', 'xlsx', 'zip', 'rar', 'amr', 'mp4', 'avi', 'wav'];\n      }\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['click', 'fileUpload', 'isAllSucceed'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var emit = __emit;\n    var props = __props;\n    var disabled = computed(function () {\n      return props.disabled;\n    });\n    var fileSucceed = ref([]);\n    var fileArrAy = ref([]);\n    var defaultFile = function defaultFile() {\n      fileSucceed.value = props.fileData.map(function (v) {\n        return _objectSpread(_objectSpread({}, v), {}, {\n          uid: guid(),\n          fileName: v.originalFileName,\n          fileURL: api.fileURL(v.newFileName),\n          progress: 100\n        });\n      });\n    };\n    var handleClick = function handleClick(e) {\n      if (disabled.value) return;\n      emit('click', e);\n    };\n    var handleFile = function handleFile(file) {\n      var fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();\n      // const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\n      var isShow = props.fileType.includes(fileType);\n      if (!isShow) {\n        ElMessage({\n          type: 'warning',\n          message: `仅支持${props.fileType.join('、')}格式!`\n        });\n      }\n      return isShow;\n    };\n    var fileUpload = function fileUpload(file) {\n      if (props.max && fileSucceed.value.length + fileArrAy.value.length >= props.max) {\n        ElMessage({\n          type: 'warning',\n          message: `最多只能上传${props.max}个文件！`\n        });\n        return false;\n      }\n      var param = new FormData();\n      param.append('file', file.file);\n      globalUpload(param, guid(), file.file.name, file.file.uid);\n    };\n    var globalUpload = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(params, uid, name, time) {\n        var res, data, newData, newSortData, newSucceedData, index, item;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              fileArrAy.value.push({\n                uid,\n                fileName: name,\n                progress: 0\n              });\n              emit('isAllSucceed', !fileArrAy.value.length);\n              _context.next = 5;\n              return api[props.apiName](params, onUploadProgress, uid);\n            case 5:\n              res = _context.sent;\n              data = res.data;\n              fileArrAy.value = fileArrAy.value.filter(function (item) {\n                return item.uid !== uid;\n              });\n              newData = [];\n              newSortData = [];\n              newSucceedData = [].concat(_toConsumableArray(fileSucceed.value), [_objectSpread(_objectSpread({}, data), {}, {\n                uid: uid,\n                time: time,\n                fileName: data.originalFileName,\n                fileURL: api.fileURL(data.newFileName),\n                progress: 100\n              })]);\n              for (index = 0; index < newSucceedData.length; index++) {\n                item = newSucceedData[index];\n                if (item.time) {\n                  newSortData.push(item);\n                } else {\n                  newData.push(item);\n                }\n              }\n              emit('fileUpload', [].concat(newData, _toConsumableArray(newSortData.sort(function (a, b) {\n                return a.time - b.time;\n              }))));\n              emit('isAllSucceed', !fileArrAy.value.length);\n              _context.next = 19;\n              break;\n            case 16:\n              _context.prev = 16;\n              _context.t0 = _context[\"catch\"](0);\n              fileArrAy.value = fileArrAy.value.filter(function (item) {\n                return item.uid !== uid;\n              });\n            case 19:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 16]]);\n      }));\n      return function globalUpload(_x, _x2, _x3, _x4) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var onUploadProgress = function onUploadProgress(progressEvent, uid) {\n      var _progressEvent$event;\n      if (progressEvent !== null && progressEvent !== void 0 && (_progressEvent$event = progressEvent.event) !== null && _progressEvent$event !== void 0 && _progressEvent$event.lengthComputable) {\n        var progress = (progressEvent.loaded / progressEvent.total * 100).toFixed(0);\n        fileArrAy.value.forEach(function (item) {\n          if (item.uid === uid) {\n            item.progress = parseInt(progress);\n          }\n        });\n      }\n    };\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var handlePreview = function handlePreview(row) {\n      globalFileLocation({\n        name: process.env.VUE_APP_NAME,\n        fileId: row.id,\n        fileType: row.extName,\n        fileName: row.originalFileName,\n        fileSize: row.fileSize\n      });\n    };\n    var handleDownload = function handleDownload(row) {\n      if (props.public) {\n        if (window.__POWERED_BY_QIANKUN__) {\n          extendDownloadFile({\n            url: `/in_system/file/download/${row.id}`,\n            params: {},\n            fileType: row.extName,\n            fileName: row.originalFileName,\n            fileSize: row.fileSize\n          });\n        } else {\n          store.commit('setExtendDownloadFile', {\n            url: `/in_system/file/download/${row.id}`,\n            params: {},\n            fileType: row.extName,\n            fileName: row.originalFileName,\n            fileSize: row.fileSize\n          });\n        }\n      } else {\n        if (window.__POWERED_BY_QIANKUN__) {\n          downloadFile({\n            fileId: row.id,\n            fileType: row.extName,\n            fileName: row.originalFileName,\n            fileSize: row.fileSize\n          });\n        } else {\n          store.commit('setDownloadFile', {\n            fileId: row.id,\n            fileType: row.extName,\n            fileName: row.originalFileName,\n            fileSize: row.fileSize\n          });\n        }\n      }\n    };\n    var handleDel = function handleDel(file) {\n      emit('fileUpload', fileSucceed.value.filter(function (item) {\n        return item.uid !== file.uid;\n      }));\n    };\n    watch(function () {\n      return props.fileData;\n    }, function (val) {\n      if (val.length) {\n        defaultFile();\n      } else {\n        fileSucceed.value = [];\n      }\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      emit,\n      props,\n      disabled,\n      fileSucceed,\n      fileArrAy,\n      defaultFile,\n      handleClick,\n      handleFile,\n      fileUpload,\n      globalUpload,\n      onUploadProgress,\n      guid,\n      handlePreview,\n      handleDownload,\n      handleDel,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      watch,\n      get downloadFile() {\n        return downloadFile;\n      },\n      get globalFileLocation() {\n        return globalFileLocation;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "api", "ref", "computed", "watch", "downloadFile", "globalFileLocation", "ElMessage", "__default__", "emit", "__emit", "props", "__props", "disabled", "fileSucceed", "fileArrAy", "defaultFile", "fileData", "map", "uid", "guid", "fileName", "originalFileName", "fileURL", "newFileName", "progress", "handleClick", "handleFile", "file", "fileType", "substring", "lastIndexOf", "toLowerCase", "isShow", "includes", "message", "join", "fileUpload", "max", "param", "FormData", "append", "globalUpload", "_ref2", "_callee", "params", "time", "res", "data", "newData", "newSortData", "newSucceedData", "index", "item", "_callee$", "_context", "apiName", "onUploadProgress", "concat", "sort", "b", "t0", "_x", "_x2", "_x3", "_x4", "progressEvent", "_progressEvent$event", "event", "lengthComputable", "loaded", "total", "toFixed", "parseInt", "replace", "Math", "random", "handlePreview", "row", "process", "env", "VUE_APP_NAME", "fileId", "id", "extName", "fileSize", "handleDownload", "public", "window", "__POWERED_BY_QIANKUN__", "extendDownloadFile", "url", "store", "commit", "handleDel", "val", "immediate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/xyl-upload-file/xyl-upload-file.vue"], "sourcesContent": ["<template>\r\n  <div class=\"xyl-upload-file\">\r\n    <el-upload\r\n      drag\r\n      action=\"/\"\r\n      :class=\"{ 'zy-is-upload-disabled': disabled }\"\r\n      :before-upload=\"handleFile\"\r\n      :http-request=\"fileUpload\"\r\n      :show-file-list=\"false\"\r\n      :disabled=\"disabled\"\r\n      multiple\r\n      @click=\"handleClick\">\r\n      <el-icon class=\"zy-el-icon--upload\">\r\n        <upload-filled />\r\n      </el-icon>\r\n      <div class=\"zy-el-upload__text\">\r\n        将附件拖拽至此区域，或\r\n        <em>点击上传</em>\r\n      </div>\r\n      <div class=\"zy-el-upload__tip\">仅支持{{ props.fileType.join('、') }}格式</div>\r\n    </el-upload>\r\n    <div class=\"xyl-upload-file-list\">\r\n      <div\r\n        class=\"xyl-upload-file-item ellipsis\"\r\n        :class=\"{ 'xyl-upload-file-item-disabled': disabled }\"\r\n        v-for=\"item in fileSucceed\"\r\n        :key=\"item.uid\">\r\n        <div class=\"xyl-upload-file-icon\">\r\n          <el-icon>\r\n            <Document />\r\n          </el-icon>\r\n        </div>\r\n        {{ item.fileName }}\r\n        <slot :row=\"item\"></slot>\r\n        <div class=\"xyl-upload-file-succes\" v-if=\"item.fileURL\">\r\n          <el-icon>\r\n            <CircleCheck />\r\n          </el-icon>\r\n        </div>\r\n        <div class=\"xyl-upload-file-preview\" @click=\"handlePreview(item)\" v-if=\"item.fileURL\">\r\n          <el-icon>\r\n            <View />\r\n          </el-icon>\r\n        </div>\r\n        <div class=\"xyl-upload-file-download\" @click=\"handleDownload(item)\" v-if=\"item.fileURL\">\r\n          <el-icon>\r\n            <Download />\r\n          </el-icon>\r\n        </div>\r\n        <div class=\"xyl-upload-file-del\" @click=\"handleDel(item)\" v-if=\"item.fileURL && !disabled\">\r\n          <el-icon>\r\n            <Close />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"xyl-upload-file-item ellipsis\" v-for=\"item in fileArrAy\" :key=\"item.uid\">\r\n        <div class=\"xyl-upload-file-icon\">\r\n          <el-icon>\r\n            <Document />\r\n          </el-icon>\r\n        </div>\r\n        {{ item.fileName }}\r\n        <el-progress :percentage=\"item.progress\" :show-text=\"false\" v-if=\"!item.fileURL\" :stroke-width=\"2\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'XylUploadFile' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, watch } from 'vue'\r\nimport { downloadFile } from '../../config/MicroGlobal'\r\nimport { globalFileLocation } from '../../config/location'\r\nimport { ElMessage } from 'element-plus'\r\nconst emit = defineEmits(['click', 'fileUpload', 'isAllSucceed'])\r\nconst props = defineProps({\r\n  max: { type: Number, default: 0 },\r\n  apiName: { type: String, default: 'globalUpload' }, // 上传的api\r\n  fileData: { type: Array, default: () => [] },\r\n  fileType: {\r\n    type: Array,\r\n    default: () => [\r\n      'jpg',\r\n      'png',\r\n      'gif',\r\n      'jpeg',\r\n      'txt',\r\n      'doc',\r\n      'docx',\r\n      'wps',\r\n      'ppt',\r\n      'pptx',\r\n      'pdf',\r\n      'ofd',\r\n      'xls',\r\n      'xlsx',\r\n      'zip',\r\n      'rar',\r\n      'amr',\r\n      'mp4',\r\n      'avi',\r\n      'wav'\r\n    ]\r\n  },\r\n  disabled: { type: Boolean, default: false }\r\n})\r\n\r\nconst disabled = computed(() => props.disabled)\r\nconst fileSucceed = ref([])\r\nconst fileArrAy = ref([])\r\n\r\nconst defaultFile = () => {\r\n  fileSucceed.value = props.fileData.map((v) => ({\r\n    ...v,\r\n    uid: guid(),\r\n    fileName: v.originalFileName,\r\n    fileURL: api.fileURL(v.newFileName),\r\n    progress: 100\r\n  }))\r\n}\r\nconst handleClick = (e) => {\r\n  if (disabled.value) return\r\n  emit('click', e)\r\n}\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()\r\n  // const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  const isShow = props.fileType.includes(fileType)\r\n  if (!isShow) {\r\n    ElMessage({ type: 'warning', message: `仅支持${props.fileType.join('、')}格式!` })\r\n  }\r\n  return isShow\r\n}\r\nconst fileUpload = (file) => {\r\n  if (props.max && fileSucceed.value.length + fileArrAy.value.length >= props.max) {\r\n    ElMessage({ type: 'warning', message: `最多只能上传${props.max}个文件！` })\r\n    return false\r\n  }\r\n  const param = new FormData()\r\n  param.append('file', file.file)\r\n  globalUpload(param, guid(), file.file.name, file.file.uid)\r\n}\r\nconst globalUpload = async (params, uid, name, time) => {\r\n  try {\r\n    fileArrAy.value.push({ uid, fileName: name, progress: 0 })\r\n    emit('isAllSucceed', !fileArrAy.value.length)\r\n    const res = await api[props.apiName](params, onUploadProgress, uid)\r\n    var { data } = res\r\n    fileArrAy.value = fileArrAy.value.filter((item) => item.uid !== uid)\r\n\r\n    const newData = []\r\n    const newSortData = []\r\n    const newSucceedData = [\r\n      ...fileSucceed.value,\r\n      {\r\n        ...data,\r\n        uid: uid,\r\n        time: time,\r\n        fileName: data.originalFileName,\r\n        fileURL: api.fileURL(data.newFileName),\r\n        progress: 100\r\n      }\r\n    ]\r\n    for (let index = 0; index < newSucceedData.length; index++) {\r\n      const item = newSucceedData[index]\r\n      if (item.time) {\r\n        newSortData.push(item)\r\n      } else {\r\n        newData.push(item)\r\n      }\r\n    }\r\n    emit('fileUpload', [...newData, ...newSortData.sort((a, b) => a.time - b.time)])\r\n    emit('isAllSucceed', !fileArrAy.value.length)\r\n  } catch (err) {\r\n    fileArrAy.value = fileArrAy.value.filter((item) => item.uid !== uid)\r\n  }\r\n}\r\nconst onUploadProgress = (progressEvent, uid) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    fileArrAy.value.forEach((item) => {\r\n      if (item.uid === uid) {\r\n        item.progress = parseInt(progress)\r\n      }\r\n    })\r\n  }\r\n}\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst handlePreview = (row) => {\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nconst handleDownload = (row) => {\r\n  if (props.public) {\r\n    if (window.__POWERED_BY_QIANKUN__) {\r\n      extendDownloadFile({\r\n        url: `/in_system/file/download/${row.id}`,\r\n        params: {},\r\n        fileType: row.extName,\r\n        fileName: row.originalFileName,\r\n        fileSize: row.fileSize\r\n      })\r\n    } else {\r\n      store.commit('setExtendDownloadFile', {\r\n        url: `/in_system/file/download/${row.id}`,\r\n        params: {},\r\n        fileType: row.extName,\r\n        fileName: row.originalFileName,\r\n        fileSize: row.fileSize\r\n      })\r\n    }\r\n  } else {\r\n    if (window.__POWERED_BY_QIANKUN__) {\r\n      downloadFile({ fileId: row.id, fileType: row.extName, fileName: row.originalFileName, fileSize: row.fileSize })\r\n    } else {\r\n      store.commit('setDownloadFile', {\r\n        fileId: row.id,\r\n        fileType: row.extName,\r\n        fileName: row.originalFileName,\r\n        fileSize: row.fileSize\r\n      })\r\n    }\r\n  }\r\n}\r\nconst handleDel = (file) => {\r\n  emit(\r\n    'fileUpload',\r\n    fileSucceed.value.filter((item) => item.uid !== file.uid)\r\n  )\r\n}\r\nwatch(\r\n  () => props.fileData,\r\n  (val) => {\r\n    if (val.length) {\r\n      defaultFile()\r\n    } else {\r\n      fileSucceed.value = []\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.xyl-upload-file {\r\n  width: 100%;\r\n\r\n  .zy-el-upload {\r\n    --zy-el-upload-dragger-padding-horizontal: 20px;\r\n    --zy-el-upload-dragger-padding-vertical: 10px;\r\n\r\n    .zy-el-upload__text {\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .zy-el-upload__tip {\r\n      padding: 0 var(--zy-distance-one);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .xyl-upload-file-list {\r\n    width: 100%;\r\n    padding-top: 12px;\r\n\r\n    .xyl-upload-file-item {\r\n      width: 100%;\r\n      position: relative;\r\n      padding: var(--zy-font-text-distance-five) var(--zy-distance-one);\r\n      padding-right: calc(var(--zy-distance-one) * 3);\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-text-font-size);\r\n      cursor: pointer;\r\n\r\n      .zy-el-progress {\r\n        position: absolute;\r\n        left: 50%;\r\n        bottom: 0;\r\n        width: 100%;\r\n        transform: translateX(-50%);\r\n      }\r\n\r\n      .xyl-upload-file-icon {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: var(--zy-distance-two);\r\n        transform: translate(-50%, -50%);\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n      }\r\n\r\n      .xyl-upload-file-preview,\r\n      .xyl-upload-file-download,\r\n      .xyl-upload-file-succes,\r\n      .xyl-upload-file-del {\r\n        position: absolute;\r\n        top: 50%;\r\n        right: var(--zy-distance-two);\r\n        transform: translate(50%, -50%);\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n\r\n      .xyl-upload-file-preview {\r\n        right: calc(var(--zy-distance-two) * 5);\r\n      }\r\n\r\n      .xyl-upload-file-download {\r\n        right: calc(var(--zy-distance-two) * 3);\r\n      }\r\n\r\n      .xyl-upload-file-succes {\r\n        color: var(--zy-el-color-success);\r\n      }\r\n\r\n      .xyl-upload-file-del {\r\n        display: none;\r\n      }\r\n\r\n      &:hover {\r\n        .xyl-upload-file-succes {\r\n          display: none;\r\n        }\r\n\r\n        .xyl-upload-file-del {\r\n          display: flex;\r\n        }\r\n      }\r\n    }\r\n\r\n    .xyl-upload-file-item-disabled {\r\n      &:hover {\r\n        .xyl-upload-file-succes {\r\n          display: flex;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .zy-is-upload-disabled {\r\n    cursor: not-allowed;\r\n\r\n    .zy-el-upload-dragger {\r\n      cursor: not-allowed;\r\n      background-color: var(--el-disabled-bg-color);\r\n      border: 1px dashed var(--zy-el-border-color-darker);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAwEA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAArG,CAAA,WAAAsG,kBAAA,CAAAtG,CAAA,KAAAuG,gBAAA,CAAAvG,CAAA,KAAAwG,2BAAA,CAAAxG,CAAA,KAAAyG,kBAAA;AAAA,SAAAA,mBAAA,cAAA5C,SAAA;AAAA,SAAA2C,4BAAAxG,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAA0G,iBAAA,CAAA1G,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAA4G,QAAA,CAAA/E,IAAA,CAAA5B,CAAA,EAAA4F,KAAA,6BAAA7F,CAAA,IAAAC,CAAA,CAAA+E,WAAA,KAAAhF,CAAA,GAAAC,CAAA,CAAA+E,WAAA,CAAAC,IAAA,aAAAjF,CAAA,cAAAA,CAAA,GAAA6G,KAAA,CAAAC,IAAA,CAAA7G,CAAA,oBAAAD,CAAA,+CAAA+G,IAAA,CAAA/G,CAAA,IAAA2G,iBAAA,CAAA1G,CAAA,EAAAU,CAAA;AAAA,SAAA6F,iBAAAvG,CAAA,8BAAAS,MAAA,YAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,aAAAX,CAAA,uBAAA4G,KAAA,CAAAC,IAAA,CAAA7G,CAAA;AAAA,SAAAsG,mBAAAtG,CAAA,QAAA4G,KAAA,CAAAG,OAAA,CAAA/G,CAAA,UAAA0G,iBAAA,CAAA1G,CAAA;AAAA,SAAA0G,kBAAA1G,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA4E,MAAA,MAAAlE,CAAA,GAAAV,CAAA,CAAA4E,MAAA,YAAA9E,CAAA,MAAAK,CAAA,GAAAyG,KAAA,CAAAlG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AAAA,SAAA6G,mBAAA7G,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAA4G,kBAAA9G,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAoH,SAAA,aAAA5B,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAgH,KAAA,CAAApH,CAAA,EAAAD,CAAA,YAAAsH,MAAAjH,CAAA,IAAA6G,kBAAA,CAAAtG,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAA+G,KAAA,EAAAC,MAAA,UAAAlH,CAAA,cAAAkH,OAAAlH,CAAA,IAAA6G,kBAAA,CAAAtG,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAA+G,KAAA,EAAAC,MAAA,WAAAlH,CAAA,KAAAiH,KAAA;AAAA,SAAAE,QAAAxH,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAsH,qBAAA,QAAAlH,CAAA,GAAAJ,MAAA,CAAAsH,qBAAA,CAAAzH,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAmH,MAAA,WAAAxH,CAAA,WAAAC,MAAA,CAAAwH,wBAAA,CAAA3H,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAA4C,KAAA,CAAApH,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2H,cAAA5H,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAAkH,SAAA,CAAAtC,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAAmH,SAAA,CAAAlH,CAAA,IAAAkH,SAAA,CAAAlH,CAAA,QAAAA,CAAA,OAAAsH,OAAA,CAAArH,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA2H,eAAA,CAAA7H,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA2H,yBAAA,GAAA3H,MAAA,CAAA4H,gBAAA,CAAA/H,CAAA,EAAAG,MAAA,CAAA2H,yBAAA,CAAA7H,CAAA,KAAAuH,OAAA,CAAArH,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAwH,wBAAA,CAAA1H,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA6H,gBAAA7H,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA8H,cAAA,CAAA9H,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAgI,eAAA/H,CAAA,QAAAS,CAAA,GAAAuH,YAAA,CAAAhI,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAuH,aAAAhI,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAuH,WAAA,kBAAAlI,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAiI,MAAA,GAAAC,MAAA,EAAAnI,CAAA;AADA,OAAOoI,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,KAAK;AAC1C,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,SAAS,QAAQ,cAAc;AAPxC,IAAAC,WAAA,GAAe;EAAE1D,IAAI,EAAE;AAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQxC,IAAM2D,IAAI,GAAGC,MAAoD;IACjE,IAAMC,KAAK,GAAGC,OA8BZ;IAEF,IAAMC,QAAQ,GAAGV,QAAQ,CAAC;MAAA,OAAMQ,KAAK,CAACE,QAAQ;IAAA,EAAC;IAC/C,IAAMC,WAAW,GAAGZ,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMa,SAAS,GAAGb,GAAG,CAAC,EAAE,CAAC;IAEzB,IAAMc,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBF,WAAW,CAACzI,KAAK,GAAGsI,KAAK,CAACM,QAAQ,CAACC,GAAG,CAAC,UAAC7G,CAAC;QAAA,OAAAmF,aAAA,CAAAA,aAAA,KACpCnF,CAAC;UACJ8G,GAAG,EAAEC,IAAI,CAAC,CAAC;UACXC,QAAQ,EAAEhH,CAAC,CAACiH,gBAAgB;UAC5BC,OAAO,EAAEtB,GAAG,CAACsB,OAAO,CAAClH,CAAC,CAACmH,WAAW,CAAC;UACnCC,QAAQ,EAAE;QAAG;MAAA,CACb,CAAC;IACL,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAI9J,CAAC,EAAK;MACzB,IAAIiJ,QAAQ,CAACxI,KAAK,EAAE;MACpBoI,IAAI,CAAC,OAAO,EAAE7I,CAAC,CAAC;IAClB,CAAC;IACD,IAAM+J,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B,IAAMC,QAAQ,GAAGD,IAAI,CAAC9E,IAAI,CAACgF,SAAS,CAACF,IAAI,CAAC9E,IAAI,CAACiF,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAClF;MACA,IAAMC,MAAM,GAAGtB,KAAK,CAACkB,QAAQ,CAACK,QAAQ,CAACL,QAAQ,CAAC;MAChD,IAAI,CAACI,MAAM,EAAE;QACX1B,SAAS,CAAC;UAAE/G,IAAI,EAAE,SAAS;UAAE2I,OAAO,EAAE,MAAMxB,KAAK,CAACkB,QAAQ,CAACO,IAAI,CAAC,GAAG,CAAC;QAAM,CAAC,CAAC;MAC9E;MACA,OAAOH,MAAM;IACf,CAAC;IACD,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CAAIT,IAAI,EAAK;MAC3B,IAAIjB,KAAK,CAAC2B,GAAG,IAAIxB,WAAW,CAACzI,KAAK,CAACqE,MAAM,GAAGqE,SAAS,CAAC1I,KAAK,CAACqE,MAAM,IAAIiE,KAAK,CAAC2B,GAAG,EAAE;QAC/E/B,SAAS,CAAC;UAAE/G,IAAI,EAAE,SAAS;UAAE2I,OAAO,EAAE,SAASxB,KAAK,CAAC2B,GAAG;QAAO,CAAC,CAAC;QACjE,OAAO,KAAK;MACd;MACA,IAAMC,KAAK,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC5BD,KAAK,CAACE,MAAM,CAAC,MAAM,EAAEb,IAAI,CAACA,IAAI,CAAC;MAC/Bc,YAAY,CAACH,KAAK,EAAEnB,IAAI,CAAC,CAAC,EAAEQ,IAAI,CAACA,IAAI,CAAC9E,IAAI,EAAE8E,IAAI,CAACA,IAAI,CAACT,GAAG,CAAC;IAC5D,CAAC;IACD,IAAMuB,YAAY;MAAA,IAAAC,KAAA,GAAA5D,iBAAA,cAAApH,mBAAA,GAAAoF,IAAA,CAAG,SAAA6F,QAAOC,MAAM,EAAE1B,GAAG,EAAErE,IAAI,EAAEgG,IAAI;QAAA,IAAAC,GAAA,EAAAC,IAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,cAAA,EAAAC,KAAA,EAAAC,IAAA;QAAA,OAAA1L,mBAAA,GAAAuB,IAAA,UAAAoK,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA/F,IAAA,GAAA+F,QAAA,CAAA1H,IAAA;YAAA;cAAA0H,QAAA,CAAA/F,IAAA;cAE/CuD,SAAS,CAAC1I,KAAK,CAACgE,IAAI,CAAC;gBAAE8E,GAAG;gBAAEE,QAAQ,EAAEvE,IAAI;gBAAE2E,QAAQ,EAAE;cAAE,CAAC,CAAC;cAC1DhB,IAAI,CAAC,cAAc,EAAE,CAACM,SAAS,CAAC1I,KAAK,CAACqE,MAAM,CAAC;cAAA6G,QAAA,CAAA1H,IAAA;cAAA,OAC3BoE,GAAG,CAACU,KAAK,CAAC6C,OAAO,CAAC,CAACX,MAAM,EAAEY,gBAAgB,EAAEtC,GAAG,CAAC;YAAA;cAA7D4B,GAAG,GAAAQ,QAAA,CAAAjI,IAAA;cACH0H,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVjC,SAAS,CAAC1I,KAAK,GAAG0I,SAAS,CAAC1I,KAAK,CAACiH,MAAM,CAAC,UAAC+D,IAAI;gBAAA,OAAKA,IAAI,CAAClC,GAAG,KAAKA,GAAG;cAAA,EAAC;cAE9D8B,OAAO,GAAG,EAAE;cACZC,WAAW,GAAG,EAAE;cAChBC,cAAc,MAAAO,MAAA,CAAAvF,kBAAA,CACf2C,WAAW,CAACzI,KAAK,IAAAmH,aAAA,CAAAA,aAAA,KAEfwD,IAAI;gBACP7B,GAAG,EAAEA,GAAG;gBACR2B,IAAI,EAAEA,IAAI;gBACVzB,QAAQ,EAAE2B,IAAI,CAAC1B,gBAAgB;gBAC/BC,OAAO,EAAEtB,GAAG,CAACsB,OAAO,CAACyB,IAAI,CAACxB,WAAW,CAAC;gBACtCC,QAAQ,EAAE;cAAG;cAGjB,KAAS2B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,cAAc,CAACzG,MAAM,EAAE0G,KAAK,EAAE,EAAE;gBACpDC,IAAI,GAAGF,cAAc,CAACC,KAAK,CAAC;gBAClC,IAAIC,IAAI,CAACP,IAAI,EAAE;kBACbI,WAAW,CAAC7G,IAAI,CAACgH,IAAI,CAAC;gBACxB,CAAC,MAAM;kBACLJ,OAAO,CAAC5G,IAAI,CAACgH,IAAI,CAAC;gBACpB;cACF;cACA5C,IAAI,CAAC,YAAY,KAAAiD,MAAA,CAAMT,OAAO,EAAA9E,kBAAA,CAAK+E,WAAW,CAACS,IAAI,CAAC,UAACnL,CAAC,EAAEoL,CAAC;gBAAA,OAAKpL,CAAC,CAACsK,IAAI,GAAGc,CAAC,CAACd,IAAI;cAAA,EAAC,EAAC,CAAC;cAChFrC,IAAI,CAAC,cAAc,EAAE,CAACM,SAAS,CAAC1I,KAAK,CAACqE,MAAM,CAAC;cAAA6G,QAAA,CAAA1H,IAAA;cAAA;YAAA;cAAA0H,QAAA,CAAA/F,IAAA;cAAA+F,QAAA,CAAAM,EAAA,GAAAN,QAAA;cAE7CxC,SAAS,CAAC1I,KAAK,GAAG0I,SAAS,CAAC1I,KAAK,CAACiH,MAAM,CAAC,UAAC+D,IAAI;gBAAA,OAAKA,IAAI,CAAClC,GAAG,KAAKA,GAAG;cAAA,EAAC;YAAA;YAAA;cAAA,OAAAoC,QAAA,CAAA5F,IAAA;UAAA;QAAA,GAAAiF,OAAA;MAAA,CAEvE;MAAA,gBAlCKF,YAAYA,CAAAoB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAtB,KAAA,CAAA1D,KAAA,OAAAD,SAAA;MAAA;IAAA,GAkCjB;IACD,IAAMyE,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIS,aAAa,EAAE/C,GAAG,EAAK;MAAA,IAAAgD,oBAAA;MAC/C,IAAID,aAAa,aAAbA,aAAa,gBAAAC,oBAAA,GAAbD,aAAa,CAAEE,KAAK,cAAAD,oBAAA,eAApBA,oBAAA,CAAsBE,gBAAgB,EAAE;QAC1C,IAAM5C,QAAQ,GAAG,CAAEyC,aAAa,CAACI,MAAM,GAAGJ,aAAa,CAACK,KAAK,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;QAChFzD,SAAS,CAAC1I,KAAK,CAACoC,OAAO,CAAC,UAAC4I,IAAI,EAAK;UAChC,IAAIA,IAAI,CAAClC,GAAG,KAAKA,GAAG,EAAE;YACpBkC,IAAI,CAAC5B,QAAQ,GAAGgD,QAAQ,CAAChD,QAAQ,CAAC;UACpC;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAML,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACsD,OAAO,CAAC,OAAO,EAAE,UAAChM,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAI6M,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9BvK,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAACoE,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMoG,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAK;MAC7BxE,kBAAkB,CAAC;QACjBxD,IAAI,EAAEiI,OAAO,CAACC,GAAG,CAACC,YAAY;QAC9BC,MAAM,EAAEJ,GAAG,CAACK,EAAE;QACdtD,QAAQ,EAAEiD,GAAG,CAACM,OAAO;QACrB/D,QAAQ,EAAEyD,GAAG,CAACxD,gBAAgB;QAC9B+D,QAAQ,EAAEP,GAAG,CAACO;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIR,GAAG,EAAK;MAC9B,IAAInE,KAAK,CAAC4E,MAAM,EAAE;QAChB,IAAIC,MAAM,CAACC,sBAAsB,EAAE;UACjCC,kBAAkB,CAAC;YACjBC,GAAG,EAAE,4BAA4Bb,GAAG,CAACK,EAAE,EAAE;YACzCtC,MAAM,EAAE,CAAC,CAAC;YACVhB,QAAQ,EAAEiD,GAAG,CAACM,OAAO;YACrB/D,QAAQ,EAAEyD,GAAG,CAACxD,gBAAgB;YAC9B+D,QAAQ,EAAEP,GAAG,CAACO;UAChB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLO,KAAK,CAACC,MAAM,CAAC,uBAAuB,EAAE;YACpCF,GAAG,EAAE,4BAA4Bb,GAAG,CAACK,EAAE,EAAE;YACzCtC,MAAM,EAAE,CAAC,CAAC;YACVhB,QAAQ,EAAEiD,GAAG,CAACM,OAAO;YACrB/D,QAAQ,EAAEyD,GAAG,CAACxD,gBAAgB;YAC9B+D,QAAQ,EAAEP,GAAG,CAACO;UAChB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAIG,MAAM,CAACC,sBAAsB,EAAE;UACjCpF,YAAY,CAAC;YAAE6E,MAAM,EAAEJ,GAAG,CAACK,EAAE;YAAEtD,QAAQ,EAAEiD,GAAG,CAACM,OAAO;YAAE/D,QAAQ,EAAEyD,GAAG,CAACxD,gBAAgB;YAAE+D,QAAQ,EAAEP,GAAG,CAACO;UAAS,CAAC,CAAC;QACjH,CAAC,MAAM;UACLO,KAAK,CAACC,MAAM,CAAC,iBAAiB,EAAE;YAC9BX,MAAM,EAAEJ,GAAG,CAACK,EAAE;YACdtD,QAAQ,EAAEiD,GAAG,CAACM,OAAO;YACrB/D,QAAQ,EAAEyD,GAAG,CAACxD,gBAAgB;YAC9B+D,QAAQ,EAAEP,GAAG,CAACO;UAChB,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IACD,IAAMS,SAAS,GAAG,SAAZA,SAASA,CAAIlE,IAAI,EAAK;MAC1BnB,IAAI,CACF,YAAY,EACZK,WAAW,CAACzI,KAAK,CAACiH,MAAM,CAAC,UAAC+D,IAAI;QAAA,OAAKA,IAAI,CAAClC,GAAG,KAAKS,IAAI,CAACT,GAAG;MAAA,EAC1D,CAAC;IACH,CAAC;IACDf,KAAK,CACH;MAAA,OAAMO,KAAK,CAACM,QAAQ;IAAA,GACpB,UAAC8E,GAAG,EAAK;MACP,IAAIA,GAAG,CAACrJ,MAAM,EAAE;QACdsE,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLF,WAAW,CAACzI,KAAK,GAAG,EAAE;MACxB;IACF,CAAC,EACD;MAAE2N,SAAS,EAAE;IAAK,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}