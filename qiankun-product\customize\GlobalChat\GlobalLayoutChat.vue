<template>
  <div class="GlobalLayoutChat" @contextmenu.prevent>
    <div class="GlobalLayoutChatButton" v-if="!isMac">
      <div class="GlobalLayoutChatMinimize" title="最小化" @click="handleMinimize"></div>
      <div class="GlobalLayoutChatMaximize" title="最大化" @click="handleMaximize" v-if="!ifMax"></div>
      <div class="GlobalLayoutChatUnmaximize" title="向下还原" @click="handleUnmaximize" v-if="ifMax"></div>
      <div class="GlobalLayoutChatClose" title="关闭" @click="handleClose"></div>
    </div>
    <GlobalChatNav v-model="navId" :chatTotal="chatTotal" exit @change="handleChange"></GlobalChatNav>
    <div class="GlobalChatBody" v-show="navId === '1'">
      <GlobalChatView ref="chatViewRef" v-model="chatId" :chatList="chatList" @time="handleTime"
        @refresh="handleRefresh" @send="handleSend"></GlobalChatView>
    </div>
    <div class="GlobalChatBody" v-show="navId === '2'">
      <GlobalChatAddressBook ref="addressBookRef" @send="handleSend"></GlobalChatAddressBook>
    </div>
    <div class="GlobalChatBody" v-show="navId === '3'">
      <GlobalChatGroup ref="groupRef" @send="handleSend"></GlobalChatGroup>
    </div>
  </div>
</template>
<script>
export default { name: 'GlobalLayoutChat' }
</script>
<script setup>
import { ref, computed, watch, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'
import { format } from 'common/js/time.js'
import * as RongIMLib from '@rongcloud/imlib-next'
import { handleChatId, handleChatList } from './js/ChatMethod.js'
const GlobalChatNav = defineAsyncComponent(() => import('./components/GlobalChatNav.vue'))
const GlobalChatView = defineAsyncComponent(() => import('./components/GlobalChatView.vue'))
const GlobalChatAddressBook = defineAsyncComponent(() => import('./components/GlobalChatAddressBook.vue'))
const GlobalChatGroup = defineAsyncComponent(() => import('./components/GlobalChatGroup.vue'))
const store = useStore()
const isMac = window.electron?.isMac
const ifMax = ref(false)
const rongCloudToken = computed(() => store.getters.getRongCloudToken)
const navId = ref('1')
const chatId = ref('')
const chatList = ref([])
const chatTotal = computed(() => {
  let total = 0
  for (let index = 0; index < chatList.value.length; index++) {
    const item = chatList.value[index]
    total += item.count
  }
  if (window.electron) window.electron.sendMessage({ key: 'chat-message', value: total ? total + '' : '' })
  return total
})
const chatViewRef = ref()
const addressBookRef = ref()
const groupRef = ref()
const refreshTime = ref('')
const chatObjectInfo = ref([])
onMounted(() => {
  if (window.electron)
    window.electron.setConfig({
      resizable: true,
      maximizable: true,
      minimumSize: [880, 680],
      size: [880, 680],
      center: true
    })
  if (window.electron)
    window.electron.onMaximize(() => {
      ifMax.value = true
    })
  if (window.electron)
    window.electron.onUnmaximize(() => {
      ifMax.value = false
    })
})
const handleMinimize = () => {
  if (window.electron) window.electron.minimize()
}
const handleMaximize = () => {
  if (window.electron) window.electron.maximize()
}
const handleUnmaximize = () => {
  if (window.electron) window.electron.unmaximize()
}
const handleClose = () => {
  if (window.electron) window.electron.close()
}
const rongCloudLink = async (token) => {
  const Events = RongIMLib.Events
  RongIMLib.addEventListener(Events.CONNECTED, () => {
    console.log('链接成功')
    handleEventListener()
    getRongCloudSessionList()
  })
  await RongIMLib.connect(token)
}
// const handleMessages = async (conversationType, targetId) => {
//   const res = await RongIMLib.getConversation({ conversationType, targetId })
//   return res
// }
const handleEventListener = async () => {
  const Events = RongIMLib.Events
  RongIMLib.addEventListener(Events.MESSAGES, async (evt) => {
    console.log('新消息来了', evt.messages)
    const newData = []
    const newDataId = []
    for (let index = 0; index < evt.messages.length; index++) {
      const item = evt.messages[index]
      if (!newDataId?.includes(item.targetId)) {
        newDataId.push(item.targetId)
        // const { code, data } = await handleMessages(item.conversationType, item.targetId)
        const { code, data } = await RongIMLib.getConversation({
          conversationType: item.conversationType,
          targetId: item.targetId
        })
        if (data?.targetId === chatId.value) {
          chatViewRef.value?.getNewestMessages()
          if (!code) newData.push({ ...data, unreadMessageCount: 0 })
        } else {
          if (!code) newData.push(data)
        }
      }
    }
    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')
    chatObjectInfo.value = await handleChatId(newData, isRefresh, chatObjectInfo.value)
    chatList.value = await handleChatList(newData, [], chatList.value, chatObjectInfo.value)
    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')
  })
}
const handleTime = (type) => {
  refreshTime.value = type ? format(new Date(), 'YYYY-MM-DD HH') : ''
}
const handleChange = (id) => {
  if (id === '2') addressBookRef.value?.refresh()
  if (id === '3') groupRef.value?.refresh()
}
const handleRefresh = (type, data) => {
  if (type === 'del') chatList.value = chatList.value.filter((v) => v.id !== data.id)
  getRongCloudSessionList()
}
const getRongCloudSessionList = async () => {
  const { code, data, msg } = await RongIMLib.getConversationList()
  if (code === 0) {
    // console.log('获取会话列表成功', data)
    const newTemporary = []
    for (let index = 0; index < chatList.value.length; index++) {
      const item = chatList.value[index]
      if (item.isTemporary) newTemporary.push(item)
    }
    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')
    chatObjectInfo.value = await handleChatId(data, isRefresh, chatObjectInfo.value)
    chatList.value = await handleChatList(data, newTemporary, [], chatObjectInfo.value)
    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')
  } else {
    console.log('获取会话列表失败: ', code, msg)
  }
}
const handleSend = (data) => {
  const idList = chatList.value.map((v) => v.id)
  if (idList?.includes(data.id)) {
    chatId.value = data.id
    navId.value = '1'
  } else {
    chatId.value = data.id
    chatList.value = [data, ...chatList.value]
    navId.value = '1'
  }
}
onUnmounted(() => {
  const Events = RongIMLib.Events
  RongIMLib.removeEventListeners(Events.MESSAGES)
  RongIMLib.removeEventListeners(Events.CONNECTED)
  RongIMLib.disconnect().then(() => {
    console.log('成功断开')
  })
})
watch(
  () => rongCloudToken.value,
  () => {
    if (rongCloudToken.value) rongCloudLink(rongCloudToken.value)
  },
  { immediate: true }
)
</script>
<style lang="scss">
.GlobalLayoutChat {
  width: 100%;
  height: 100%;
  display: flex;
  border: 1px solid #ccc;
  position: relative;

  .zy-el-image {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .GlobalLayoutChatButton {
    width: 96px;
    height: 28px;
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 99;
    pointer-events: auto;
    -webkit-app-region: no-drag;

    .GlobalLayoutChatMinimize {
      width: 32px;
      height: 28px;
      background: url('./img/minimize.png') no-repeat;
      background-size: 16px 16px;
      background-position: center center;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background-color: rgba($color: #999, $alpha: 0.6);
      }
    }

    .GlobalLayoutChatMaximize {
      width: 32px;
      height: 28px;
      background: url('./img/max.png') no-repeat;
      background-size: 14px 14px;
      background-position: center center;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background-color: rgba($color: #999, $alpha: 0.6);
      }
    }

    .GlobalLayoutChatUnmaximize {
      width: 32px;
      height: 28px;
      background: url('./img/min.png') no-repeat;
      background-size: 14px 14px;
      background-position: center center;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background-color: rgba($color: #999, $alpha: 0.6);
      }
    }

    .GlobalLayoutChatClose {
      width: 32px;
      height: 28px;
      background: url('./img/close.png') no-repeat;
      background-size: 16px 16px;
      background-position: center center;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background-color: rgba($color: red, $alpha: 0.6);
      }
    }
  }

  .GlobalChatBody {
    width: calc(100% - 72px);
    height: 100%;
  }
}
</style>
