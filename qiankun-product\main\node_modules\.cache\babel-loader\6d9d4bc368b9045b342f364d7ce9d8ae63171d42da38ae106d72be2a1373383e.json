{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { ref, computed, watch, onUnmounted, defineAsyncComponent } from 'vue';\nimport { useStore } from 'vuex';\nimport { format } from 'common/js/time.js';\nimport * as RongIMLib from '@rongcloud/imlib-next';\nimport { handleChatId, handleChatList } from './js/ChatMethod.js';\nvar __default__ = {\n  name: 'GlobalChat'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var GlobalChatNav = defineAsyncComponent(function () {\n      return import('./components/GlobalChatNav.vue');\n    });\n    var GlobalChatView = defineAsyncComponent(function () {\n      return import('./components/GlobalChatView.vue');\n    });\n    var GlobalChatAddressBook = defineAsyncComponent(function () {\n      return import('./components/GlobalChatAddressBook.vue');\n    });\n    var GlobalChatGroup = defineAsyncComponent(function () {\n      return import('./components/GlobalChatGroup.vue');\n    });\n    var store = useStore();\n    var emit = __emit;\n    var rongCloudToken = computed(function () {\n      return store.getters.getRongCloudToken;\n    });\n    var navId = ref('1');\n    var chatId = ref('');\n    var chatList = ref([]);\n    var chatTotal = computed(function () {\n      var total = 0;\n      for (var index = 0; index < chatList.value.length; index++) {\n        var item = chatList.value[index];\n        if (item.isNotInform !== 1) total += item.count;\n      }\n      emit('callback', total);\n      return total;\n    });\n    var chatViewRef = ref();\n    var addressBookRef = ref();\n    var groupRef = ref();\n    var refreshTime = ref('');\n    var chatObjectInfo = ref([]);\n    var rongCloudLink = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(token) {\n        var Events;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              Events = RongIMLib.Events;\n              RongIMLib.addEventListener(Events.CONNECTED, function () {\n                console.log('链接成功');\n                handleEventListener();\n                getRongCloudSessionList();\n              });\n              _context.next = 4;\n              return RongIMLib.connect(token);\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function rongCloudLink(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    // const handleMessages = async (conversationType, targetId) => {\n    //   const res = await RongIMLib.getConversation({ conversationType, targetId })\n    //   return res\n    // }\n    var handleEventListener = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var Events;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              Events = RongIMLib.Events;\n              RongIMLib.addEventListener(Events.MESSAGES, /*#__PURE__*/function () {\n                var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(evt) {\n                  var newData, newDataId, index, item, _yield$RongIMLib$getC, code, data, _chatViewRef$value, isRefresh;\n                  return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n                    while (1) switch (_context2.prev = _context2.next) {\n                      case 0:\n                        console.log('新消息来了', evt.messages);\n                        newData = [];\n                        newDataId = [];\n                        index = 0;\n                      case 4:\n                        if (!(index < evt.messages.length)) {\n                          _context2.next = 17;\n                          break;\n                        }\n                        item = evt.messages[index];\n                        if (newDataId !== null && newDataId !== void 0 && newDataId.includes(item.targetId)) {\n                          _context2.next = 14;\n                          break;\n                        }\n                        newDataId.push(item.targetId);\n                        // const { code, data } = await handleMessages(item.conversationType, item.targetId)\n                        _context2.next = 10;\n                        return RongIMLib.getConversation({\n                          conversationType: item.conversationType,\n                          targetId: item.targetId\n                        });\n                      case 10:\n                        _yield$RongIMLib$getC = _context2.sent;\n                        code = _yield$RongIMLib$getC.code;\n                        data = _yield$RongIMLib$getC.data;\n                        if ((data === null || data === void 0 ? void 0 : data.targetId) === chatId.value) {\n                          (_chatViewRef$value = chatViewRef.value) === null || _chatViewRef$value === void 0 || _chatViewRef$value.getNewestMessages();\n                          if (!code) newData.push(_objectSpread(_objectSpread({}, data), {}, {\n                            unreadMessageCount: 0\n                          }));\n                        } else {\n                          if (!code) newData.push(data);\n                        }\n                      case 14:\n                        index++;\n                        _context2.next = 4;\n                        break;\n                      case 17:\n                        isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH');\n                        _context2.next = 20;\n                        return handleChatId(newData, isRefresh, chatObjectInfo.value);\n                      case 20:\n                        chatObjectInfo.value = _context2.sent;\n                        _context2.next = 23;\n                        return handleChatList(newData, [], chatList.value, chatObjectInfo.value);\n                      case 23:\n                        chatList.value = _context2.sent;\n                        refreshTime.value = format(new Date(), 'YYYY-MM-DD HH');\n                      case 25:\n                      case \"end\":\n                        return _context2.stop();\n                    }\n                  }, _callee2);\n                }));\n                return function (_x2) {\n                  return _ref4.apply(this, arguments);\n                };\n              }());\n            case 2:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function handleEventListener() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var handleTime = function handleTime(type) {\n      refreshTime.value = type ? format(new Date(), 'YYYY-MM-DD HH') : '';\n    };\n    var handleChange = function handleChange(id) {\n      var _addressBookRef$value, _groupRef$value;\n      if (id === '2') (_addressBookRef$value = addressBookRef.value) === null || _addressBookRef$value === void 0 || _addressBookRef$value.refresh();\n      if (id === '3') (_groupRef$value = groupRef.value) === null || _groupRef$value === void 0 || _groupRef$value.refresh();\n    };\n    var handleRefresh = function handleRefresh(type, data) {\n      if (type === 'del') chatList.value = chatList.value.filter(function (v) {\n        return v.id !== data.id;\n      });\n      getRongCloudSessionList();\n    };\n    var getRongCloudSessionList = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _yield$RongIMLib$getC2, code, data, msg, newTemporary, index, item, isRefresh;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return RongIMLib.getConversationList();\n            case 2:\n              _yield$RongIMLib$getC2 = _context4.sent;\n              code = _yield$RongIMLib$getC2.code;\n              data = _yield$RongIMLib$getC2.data;\n              msg = _yield$RongIMLib$getC2.msg;\n              if (!(code === 0)) {\n                _context4.next = 20;\n                break;\n              }\n              console.log('获取会话列表成功', data);\n              newTemporary = [];\n              for (index = 0; index < chatList.value.length; index++) {\n                item = chatList.value[index];\n                if (item.isTemporary) newTemporary.push(item);\n              }\n              isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH');\n              _context4.next = 13;\n              return handleChatId(data, isRefresh, chatObjectInfo.value);\n            case 13:\n              chatObjectInfo.value = _context4.sent;\n              _context4.next = 16;\n              return handleChatList(data, newTemporary, [], chatObjectInfo.value);\n            case 16:\n              chatList.value = _context4.sent;\n              refreshTime.value = format(new Date(), 'YYYY-MM-DD HH');\n              _context4.next = 21;\n              break;\n            case 20:\n              console.log('获取会话列表失败: ', code, msg);\n            case 21:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function getRongCloudSessionList() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handleSend = function handleSend(data) {\n      var idList = chatList.value.map(function (v) {\n        return v.id;\n      });\n      if (idList !== null && idList !== void 0 && idList.includes(data.id)) {\n        chatId.value = data.id;\n        navId.value = '1';\n      } else {\n        chatId.value = data.id;\n        chatList.value = [data].concat(_toConsumableArray(chatList.value));\n        navId.value = '1';\n      }\n    };\n    onUnmounted(function () {\n      var Events = RongIMLib.Events;\n      RongIMLib.removeEventListeners(Events.MESSAGES);\n      RongIMLib.removeEventListeners(Events.CONNECTED);\n      RongIMLib.disconnect().then(function () {\n        console.log('成功断开');\n      });\n    });\n    watch(function () {\n      return rongCloudToken.value;\n    }, function () {\n      if (rongCloudToken.value) rongCloudLink(rongCloudToken.value);\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      GlobalChatNav,\n      GlobalChatView,\n      GlobalChatAddressBook,\n      GlobalChatGroup,\n      store,\n      emit,\n      rongCloudToken,\n      navId,\n      chatId,\n      chatList,\n      chatTotal,\n      chatViewRef,\n      addressBookRef,\n      groupRef,\n      refreshTime,\n      chatObjectInfo,\n      rongCloudLink,\n      handleEventListener,\n      handleTime,\n      handleChange,\n      handleRefresh,\n      getRongCloudSessionList,\n      handleSend,\n      ref,\n      computed,\n      watch,\n      onUnmounted,\n      defineAsyncComponent,\n      get useStore() {\n        return useStore;\n      },\n      get format() {\n        return format;\n      },\n      get RongIMLib() {\n        return RongIMLib;\n      },\n      get handleChatId() {\n        return handleChatId;\n      },\n      get handleChatList() {\n        return handleChatList;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ref", "computed", "watch", "onUnmounted", "defineAsyncComponent", "useStore", "format", "RongIMLib", "handleChatId", "handleChatList", "__default__", "GlobalChatNav", "GlobalChatView", "GlobalChatAddressBook", "GlobalChatGroup", "store", "emit", "__emit", "rongCloudToken", "getters", "getRongCloudToken", "navId", "chatId", "chatList", "chatTotal", "total", "index", "item", "isNotInform", "count", "chatViewRef", "addressBookRef", "groupRef", "refreshTime", "chatObjectInfo", "rongCloudLink", "_ref2", "_callee", "token", "Events", "_callee$", "_context", "addEventListener", "CONNECTED", "console", "log", "handleEventListener", "getRongCloudSessionList", "connect", "_x", "_ref3", "_callee3", "_callee3$", "_context3", "MESSAGES", "_ref4", "_callee2", "evt", "newData", "newDataId", "_yield$RongIMLib$getC", "code", "data", "_chatViewRef$value", "isRefresh", "_callee2$", "_context2", "messages", "includes", "targetId", "getConversation", "conversationType", "getNewestMessages", "_objectSpread", "unreadMessageCount", "Date", "_x2", "handleTime", "handleChange", "id", "_addressBookRef$value", "_groupRef$value", "refresh", "handleRefresh", "filter", "_ref5", "_callee4", "_yield$RongIMLib$getC2", "msg", "newTemporary", "_callee4$", "_context4", "getConversationList", "isTemporary", "handleSend", "idList", "map", "concat", "_toConsumableArray", "removeEventListeners", "disconnect", "immediate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/GlobalChat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatBox\" @contextmenu.prevent>\r\n    <div class=\"GlobalChat\">\r\n      <GlobalChatNav v-model=\"navId\" :chatTotal=\"chatTotal\" @change=\"handleChange\"></GlobalChatNav>\r\n      <div class=\"GlobalChatBody\" v-show=\"navId === '1'\">\r\n        <GlobalChatView ref=\"chatViewRef\" v-model=\"chatId\" :chatList=\"chatList\" @time=\"handleTime\"\r\n          @refresh=\"handleRefresh\" @send=\"handleSend\"></GlobalChatView>\r\n      </div>\r\n      <div class=\"GlobalChatBody\" v-show=\"navId === '2'\">\r\n        <GlobalChatAddressBook ref=\"addressBookRef\" @send=\"handleSend\"></GlobalChatAddressBook>\r\n      </div>\r\n      <div class=\"GlobalChatBody\" v-show=\"navId === '3'\">\r\n        <GlobalChatGroup ref=\"groupRef\" @send=\"handleSend\"></GlobalChatGroup>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChat' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, watch, onUnmounted, defineAsyncComponent } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { handleChatId, handleChatList } from './js/ChatMethod.js'\r\nconst GlobalChatNav = defineAsyncComponent(() => import('./components/GlobalChatNav.vue'))\r\nconst GlobalChatView = defineAsyncComponent(() => import('./components/GlobalChatView.vue'))\r\nconst GlobalChatAddressBook = defineAsyncComponent(() => import('./components/GlobalChatAddressBook.vue'))\r\nconst GlobalChatGroup = defineAsyncComponent(() => import('./components/GlobalChatGroup.vue'))\r\nconst store = useStore()\r\nconst emit = defineEmits(['callback'])\r\nconst rongCloudToken = computed(() => store.getters.getRongCloudToken)\r\nconst navId = ref('1')\r\nconst chatId = ref('')\r\nconst chatList = ref([])\r\nconst chatTotal = computed(() => {\r\n  let total = 0\r\n  for (let index = 0; index < chatList.value.length; index++) {\r\n    const item = chatList.value[index]\r\n    if (item.isNotInform !== 1) total += item.count\r\n  }\r\n  emit('callback', total)\r\n  return total\r\n})\r\nconst chatViewRef = ref()\r\nconst addressBookRef = ref()\r\nconst groupRef = ref()\r\nconst refreshTime = ref('')\r\nconst chatObjectInfo = ref([])\r\nconst rongCloudLink = async (token) => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.addEventListener(Events.CONNECTED, () => {\r\n    console.log('链接成功')\r\n    handleEventListener()\r\n    getRongCloudSessionList()\r\n  })\r\n  await RongIMLib.connect(token)\r\n}\r\n// const handleMessages = async (conversationType, targetId) => {\r\n//   const res = await RongIMLib.getConversation({ conversationType, targetId })\r\n//   return res\r\n// }\r\nconst handleEventListener = async () => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.addEventListener(Events.MESSAGES, async (evt) => {\r\n    console.log('新消息来了', evt.messages)\r\n    const newData = []\r\n    const newDataId = []\r\n    for (let index = 0; index < evt.messages.length; index++) {\r\n      const item = evt.messages[index]\r\n      if (!newDataId?.includes(item.targetId)) {\r\n        newDataId.push(item.targetId)\r\n        // const { code, data } = await handleMessages(item.conversationType, item.targetId)\r\n        const { code, data } = await RongIMLib.getConversation({\r\n          conversationType: item.conversationType,\r\n          targetId: item.targetId\r\n        })\r\n        if (data?.targetId === chatId.value) {\r\n          chatViewRef.value?.getNewestMessages()\r\n          if (!code) newData.push({ ...data, unreadMessageCount: 0 })\r\n        } else {\r\n          if (!code) newData.push(data)\r\n        }\r\n      }\r\n    }\r\n    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')\r\n    chatObjectInfo.value = await handleChatId(newData, isRefresh, chatObjectInfo.value)\r\n    chatList.value = await handleChatList(newData, [], chatList.value, chatObjectInfo.value)\r\n    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')\r\n  })\r\n}\r\nconst handleTime = (type) => {\r\n  refreshTime.value = type ? format(new Date(), 'YYYY-MM-DD HH') : ''\r\n}\r\nconst handleChange = (id) => {\r\n  if (id === '2') addressBookRef.value?.refresh()\r\n  if (id === '3') groupRef.value?.refresh()\r\n}\r\nconst handleRefresh = (type, data) => {\r\n  if (type === 'del') chatList.value = chatList.value.filter((v) => v.id !== data.id)\r\n  getRongCloudSessionList()\r\n}\r\nconst getRongCloudSessionList = async () => {\r\n  const { code, data, msg } = await RongIMLib.getConversationList()\r\n  if (code === 0) {\r\n    console.log('获取会话列表成功', data)\r\n    const newTemporary = []\r\n    for (let index = 0; index < chatList.value.length; index++) {\r\n      const item = chatList.value[index]\r\n      if (item.isTemporary) newTemporary.push(item)\r\n    }\r\n    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')\r\n    chatObjectInfo.value = await handleChatId(data, isRefresh, chatObjectInfo.value)\r\n    chatList.value = await handleChatList(data, newTemporary, [], chatObjectInfo.value)\r\n    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')\r\n  } else {\r\n    console.log('获取会话列表失败: ', code, msg)\r\n  }\r\n}\r\nconst handleSend = (data) => {\r\n  const idList = chatList.value.map((v) => v.id)\r\n  if (idList?.includes(data.id)) {\r\n    chatId.value = data.id\r\n    navId.value = '1'\r\n  } else {\r\n    chatId.value = data.id\r\n    chatList.value = [data, ...chatList.value]\r\n    navId.value = '1'\r\n  }\r\n}\r\nonUnmounted(() => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.removeEventListeners(Events.MESSAGES)\r\n  RongIMLib.removeEventListeners(Events.CONNECTED)\r\n  RongIMLib.disconnect().then(() => {\r\n    console.log('成功断开')\r\n  })\r\n})\r\nwatch(\r\n  () => rongCloudToken.value,\r\n  () => {\r\n    if (rongCloudToken.value) rongCloudLink(rongCloudToken.value)\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalChatBox {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .GlobalChat {\r\n    width: 980px;\r\n    min-height: 560px;\r\n    max-height: 720px;\r\n  }\r\n}\r\n\r\n.GlobalChat {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  position: relative;\r\n\r\n  .zy-el-image {\r\n    -webkit-touch-callout: none;\r\n    -webkit-user-select: none;\r\n    -khtml-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n  }\r\n\r\n  .GlobalChatBody {\r\n    width: calc(100% - 72px);\r\n    height: 100%;\r\n\r\n    .GlobalChatView {\r\n      .GlobalChatViewList {\r\n        .GlobalChatViewListHead {\r\n          height: 56px;\r\n        }\r\n\r\n        .GlobalChatViewMessagesList {\r\n          height: calc(100% - 92px);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewDrag {\r\n        height: 56px;\r\n      }\r\n\r\n      .GlobalChatWindow {\r\n        .GlobalChatWindowTitle {\r\n          height: 56px;\r\n\r\n          .GlobalChatWindowMore {\r\n            margin: 0;\r\n          }\r\n        }\r\n\r\n        .GlobalChatWindowScroll {\r\n          height: calc(100% - 222px);\r\n\r\n          &.GlobalChatWindowNoChat {\r\n            height: calc(100% - (56px + var(--zy-height)));\r\n          }\r\n        }\r\n\r\n        .setting-popup-window {\r\n          height: calc(100% - 56px);\r\n          top: 56px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatAddressBook {\r\n      .GlobalChatAddressBookList {\r\n        .GlobalChatAddressBookInput {\r\n          height: 56px;\r\n        }\r\n\r\n        .GlobalChatAddressBookScrollbar {\r\n          height: calc(100% - 56px);\r\n        }\r\n      }\r\n\r\n      .GlobalChatAddressBookDrag {\r\n        height: 56px;\r\n      }\r\n    }\r\n\r\n    .GlobalChatGroup {\r\n      .GlobalChatGroupList {\r\n        .GlobalChatGroupInput {\r\n          height: 56px;\r\n        }\r\n\r\n        .GlobalChatGroupScrollbar {\r\n          height: calc(100% - 56px);\r\n        }\r\n      }\r\n\r\n      .GlobalChatGroupDrag {\r\n        height: 56px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;+CAsBA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,SAASE,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAEC,oBAAoB,QAAQ,KAAK;AAC7E,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,SAAS,MAAM,uBAAuB;AAClD,SAASC,YAAY,EAAEC,cAAc,QAAQ,oBAAoB;AAPjE,IAAAC,WAAA,GAAe;EAAErC,IAAI,EAAE;AAAa,CAAC;;;;;;;IAQrC,IAAMsC,aAAa,GAAGP,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,gCAAgC,CAAC;IAAA,EAAC;IAC1F,IAAMQ,cAAc,GAAGR,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,iCAAiC,CAAC;IAAA,EAAC;IAC5F,IAAMS,qBAAqB,GAAGT,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,wCAAwC,CAAC;IAAA,EAAC;IAC1G,IAAMU,eAAe,GAAGV,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,kCAAkC,CAAC;IAAA,EAAC;IAC9F,IAAMW,KAAK,GAAGV,QAAQ,CAAC,CAAC;IACxB,IAAMW,IAAI,GAAGC,MAAyB;IACtC,IAAMC,cAAc,GAAGjB,QAAQ,CAAC;MAAA,OAAMc,KAAK,CAACI,OAAO,CAACC,iBAAiB;IAAA,EAAC;IACtE,IAAMC,KAAK,GAAGrB,GAAG,CAAC,GAAG,CAAC;IACtB,IAAMsB,MAAM,GAAGtB,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMuB,QAAQ,GAAGvB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMwB,SAAS,GAAGvB,QAAQ,CAAC,YAAM;MAC/B,IAAIwB,KAAK,GAAG,CAAC;MACb,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,QAAQ,CAAC3H,KAAK,CAACqE,MAAM,EAAEyD,KAAK,EAAE,EAAE;QAC1D,IAAMC,IAAI,GAAGJ,QAAQ,CAAC3H,KAAK,CAAC8H,KAAK,CAAC;QAClC,IAAIC,IAAI,CAACC,WAAW,KAAK,CAAC,EAAEH,KAAK,IAAIE,IAAI,CAACE,KAAK;MACjD;MACAb,IAAI,CAAC,UAAU,EAAES,KAAK,CAAC;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACF,IAAMK,WAAW,GAAG9B,GAAG,CAAC,CAAC;IACzB,IAAM+B,cAAc,GAAG/B,GAAG,CAAC,CAAC;IAC5B,IAAMgC,QAAQ,GAAGhC,GAAG,CAAC,CAAC;IACtB,IAAMiC,WAAW,GAAGjC,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMkC,cAAc,GAAGlC,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMmC,aAAa;MAAA,IAAAC,KAAA,GAAAzC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+D,QAAOC,KAAK;QAAA,IAAAC,MAAA;QAAA,OAAArJ,mBAAA,GAAAuB,IAAA,UAAA+H,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA1D,IAAA,GAAA0D,QAAA,CAAArF,IAAA;YAAA;cAC1BmF,MAAM,GAAGhC,SAAS,CAACgC,MAAM;cAC/BhC,SAAS,CAACmC,gBAAgB,CAACH,MAAM,CAACI,SAAS,EAAE,YAAM;gBACjDC,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;gBACnBC,mBAAmB,CAAC,CAAC;gBACrBC,uBAAuB,CAAC,CAAC;cAC3B,CAAC,CAAC;cAAAN,QAAA,CAAArF,IAAA;cAAA,OACImD,SAAS,CAACyC,OAAO,CAACV,KAAK,CAAC;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAvD,IAAA;UAAA;QAAA,GAAAmD,OAAA;MAAA,CAC/B;MAAA,gBARKF,aAAaA,CAAAc,EAAA;QAAA,OAAAb,KAAA,CAAAvC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQlB;IACD;IACA;IACA;IACA;IACA,IAAMkD,mBAAmB;MAAA,IAAAI,KAAA,GAAAvD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6E,SAAA;QAAA,IAAAZ,MAAA;QAAA,OAAArJ,mBAAA,GAAAuB,IAAA,UAAA2I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtE,IAAA,GAAAsE,SAAA,CAAAjG,IAAA;YAAA;cACpBmF,MAAM,GAAGhC,SAAS,CAACgC,MAAM;cAC/BhC,SAAS,CAACmC,gBAAgB,CAACH,MAAM,CAACe,QAAQ;gBAAA,IAAAC,KAAA,GAAA5D,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAE,SAAAkF,SAAOC,GAAG;kBAAA,IAAAC,OAAA,EAAAC,SAAA,EAAAjC,KAAA,EAAAC,IAAA,EAAAiC,qBAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,kBAAA,EAAAC,SAAA;kBAAA,OAAA9K,mBAAA,GAAAuB,IAAA,UAAAwJ,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAnF,IAAA,GAAAmF,SAAA,CAAA9G,IAAA;sBAAA;wBACpDwF,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEY,GAAG,CAACU,QAAQ,CAAC;wBAC5BT,OAAO,GAAG,EAAE;wBACZC,SAAS,GAAG,EAAE;wBACXjC,KAAK,GAAG,CAAC;sBAAA;wBAAA,MAAEA,KAAK,GAAG+B,GAAG,CAACU,QAAQ,CAAClG,MAAM;0BAAAiG,SAAA,CAAA9G,IAAA;0BAAA;wBAAA;wBACvCuE,IAAI,GAAG8B,GAAG,CAACU,QAAQ,CAACzC,KAAK,CAAC;wBAAA,IAC3BiC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAES,QAAQ,CAACzC,IAAI,CAAC0C,QAAQ,CAAC;0BAAAH,SAAA,CAAA9G,IAAA;0BAAA;wBAAA;wBACrCuG,SAAS,CAAC/F,IAAI,CAAC+D,IAAI,CAAC0C,QAAQ,CAAC;wBAC7B;wBAAAH,SAAA,CAAA9G,IAAA;wBAAA,OAC6BmD,SAAS,CAAC+D,eAAe,CAAC;0BACrDC,gBAAgB,EAAE5C,IAAI,CAAC4C,gBAAgB;0BACvCF,QAAQ,EAAE1C,IAAI,CAAC0C;wBACjB,CAAC,CAAC;sBAAA;wBAAAT,qBAAA,GAAAM,SAAA,CAAArH,IAAA;wBAHMgH,IAAI,GAAAD,qBAAA,CAAJC,IAAI;wBAAEC,IAAI,GAAAF,qBAAA,CAAJE,IAAI;wBAIlB,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,QAAQ,MAAK/C,MAAM,CAAC1H,KAAK,EAAE;0BACnC,CAAAmK,kBAAA,GAAAjC,WAAW,CAAClI,KAAK,cAAAmK,kBAAA,eAAjBA,kBAAA,CAAmBS,iBAAiB,CAAC,CAAC;0BACtC,IAAI,CAACX,IAAI,EAAEH,OAAO,CAAC9F,IAAI,CAAA6G,aAAA,CAAAA,aAAA,KAAMX,IAAI;4BAAEY,kBAAkB,EAAE;0BAAC,EAAE,CAAC;wBAC7D,CAAC,MAAM;0BACL,IAAI,CAACb,IAAI,EAAEH,OAAO,CAAC9F,IAAI,CAACkG,IAAI,CAAC;wBAC/B;sBAAC;wBAd4CpC,KAAK,EAAE;wBAAAwC,SAAA,CAAA9G,IAAA;wBAAA;sBAAA;wBAiBlD4G,SAAS,GAAG/B,WAAW,CAACrI,KAAK,KAAK0G,MAAM,CAAC,IAAIqE,IAAI,CAAC,CAAC,EAAE,eAAe,CAAC;wBAAAT,SAAA,CAAA9G,IAAA;wBAAA,OAC9CoD,YAAY,CAACkD,OAAO,EAAEM,SAAS,EAAE9B,cAAc,CAACtI,KAAK,CAAC;sBAAA;wBAAnFsI,cAAc,CAACtI,KAAK,GAAAsK,SAAA,CAAArH,IAAA;wBAAAqH,SAAA,CAAA9G,IAAA;wBAAA,OACGqD,cAAc,CAACiD,OAAO,EAAE,EAAE,EAAEnC,QAAQ,CAAC3H,KAAK,EAAEsI,cAAc,CAACtI,KAAK,CAAC;sBAAA;wBAAxF2H,QAAQ,CAAC3H,KAAK,GAAAsK,SAAA,CAAArH,IAAA;wBACdoF,WAAW,CAACrI,KAAK,GAAG0G,MAAM,CAAC,IAAIqE,IAAI,CAAC,CAAC,EAAE,eAAe,CAAC;sBAAA;sBAAA;wBAAA,OAAAT,SAAA,CAAAhF,IAAA;oBAAA;kBAAA,GAAAsE,QAAA;gBAAA,CACxD;gBAAA,iBAAAoB,GAAA;kBAAA,OAAArB,KAAA,CAAA1D,KAAA,OAAAD,SAAA;gBAAA;cAAA,IAAC;YAAA;YAAA;cAAA,OAAAyD,SAAA,CAAAnE,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA,CACH;MAAA,gBA5BKL,mBAAmBA,CAAA;QAAA,OAAAI,KAAA,CAAArD,KAAA,OAAAD,SAAA;MAAA;IAAA,GA4BxB;IACD,IAAMiF,UAAU,GAAG,SAAbA,UAAUA,CAAI9J,IAAI,EAAK;MAC3BkH,WAAW,CAACrI,KAAK,GAAGmB,IAAI,GAAGuF,MAAM,CAAC,IAAIqE,IAAI,CAAC,CAAC,EAAE,eAAe,CAAC,GAAG,EAAE;IACrE,CAAC;IACD,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAIC,EAAE,EAAK;MAAA,IAAAC,qBAAA,EAAAC,eAAA;MAC3B,IAAIF,EAAE,KAAK,GAAG,EAAE,CAAAC,qBAAA,GAAAjD,cAAc,CAACnI,KAAK,cAAAoL,qBAAA,eAApBA,qBAAA,CAAsBE,OAAO,CAAC,CAAC;MAC/C,IAAIH,EAAE,KAAK,GAAG,EAAE,CAAAE,eAAA,GAAAjD,QAAQ,CAACpI,KAAK,cAAAqL,eAAA,eAAdA,eAAA,CAAgBC,OAAO,CAAC,CAAC;IAC3C,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIpK,IAAI,EAAE+I,IAAI,EAAK;MACpC,IAAI/I,IAAI,KAAK,KAAK,EAAEwG,QAAQ,CAAC3H,KAAK,GAAG2H,QAAQ,CAAC3H,KAAK,CAACwL,MAAM,CAAC,UAACxJ,CAAC;QAAA,OAAKA,CAAC,CAACmJ,EAAE,KAAKjB,IAAI,CAACiB,EAAE;MAAA,EAAC;MACnFhC,uBAAuB,CAAC,CAAC;IAC3B,CAAC;IACD,IAAMA,uBAAuB;MAAA,IAAAsC,KAAA,GAAA1F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgH,SAAA;QAAA,IAAAC,sBAAA,EAAA1B,IAAA,EAAAC,IAAA,EAAA0B,GAAA,EAAAC,YAAA,EAAA/D,KAAA,EAAAC,IAAA,EAAAqC,SAAA;QAAA,OAAA9K,mBAAA,GAAAuB,IAAA,UAAAiL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,IAAA,GAAA4G,SAAA,CAAAvI,IAAA;YAAA;cAAAuI,SAAA,CAAAvI,IAAA;cAAA,OACImD,SAAS,CAACqF,mBAAmB,CAAC,CAAC;YAAA;cAAAL,sBAAA,GAAAI,SAAA,CAAA9I,IAAA;cAAzDgH,IAAI,GAAA0B,sBAAA,CAAJ1B,IAAI;cAAEC,IAAI,GAAAyB,sBAAA,CAAJzB,IAAI;cAAE0B,GAAG,GAAAD,sBAAA,CAAHC,GAAG;cAAA,MACnB3B,IAAI,KAAK,CAAC;gBAAA8B,SAAA,CAAAvI,IAAA;gBAAA;cAAA;cACZwF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEiB,IAAI,CAAC;cACvB2B,YAAY,GAAG,EAAE;cACvB,KAAS/D,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,QAAQ,CAAC3H,KAAK,CAACqE,MAAM,EAAEyD,KAAK,EAAE,EAAE;gBACpDC,IAAI,GAAGJ,QAAQ,CAAC3H,KAAK,CAAC8H,KAAK,CAAC;gBAClC,IAAIC,IAAI,CAACkE,WAAW,EAAEJ,YAAY,CAAC7H,IAAI,CAAC+D,IAAI,CAAC;cAC/C;cACMqC,SAAS,GAAG/B,WAAW,CAACrI,KAAK,KAAK0G,MAAM,CAAC,IAAIqE,IAAI,CAAC,CAAC,EAAE,eAAe,CAAC;cAAAgB,SAAA,CAAAvI,IAAA;cAAA,OAC9CoD,YAAY,CAACsD,IAAI,EAAEE,SAAS,EAAE9B,cAAc,CAACtI,KAAK,CAAC;YAAA;cAAhFsI,cAAc,CAACtI,KAAK,GAAA+L,SAAA,CAAA9I,IAAA;cAAA8I,SAAA,CAAAvI,IAAA;cAAA,OACGqD,cAAc,CAACqD,IAAI,EAAE2B,YAAY,EAAE,EAAE,EAAEvD,cAAc,CAACtI,KAAK,CAAC;YAAA;cAAnF2H,QAAQ,CAAC3H,KAAK,GAAA+L,SAAA,CAAA9I,IAAA;cACdoF,WAAW,CAACrI,KAAK,GAAG0G,MAAM,CAAC,IAAIqE,IAAI,CAAC,CAAC,EAAE,eAAe,CAAC;cAAAgB,SAAA,CAAAvI,IAAA;cAAA;YAAA;cAEvDwF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgB,IAAI,EAAE2B,GAAG,CAAC;YAAA;YAAA;cAAA,OAAAG,SAAA,CAAAzG,IAAA;UAAA;QAAA,GAAAoG,QAAA;MAAA,CAEvC;MAAA,gBAhBKvC,uBAAuBA,CAAA;QAAA,OAAAsC,KAAA,CAAAxF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAgB5B;IACD,IAAMkG,UAAU,GAAG,SAAbA,UAAUA,CAAIhC,IAAI,EAAK;MAC3B,IAAMiC,MAAM,GAAGxE,QAAQ,CAAC3H,KAAK,CAACoM,GAAG,CAAC,UAACpK,CAAC;QAAA,OAAKA,CAAC,CAACmJ,EAAE;MAAA,EAAC;MAC9C,IAAIgB,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE3B,QAAQ,CAACN,IAAI,CAACiB,EAAE,CAAC,EAAE;QAC7BzD,MAAM,CAAC1H,KAAK,GAAGkK,IAAI,CAACiB,EAAE;QACtB1D,KAAK,CAACzH,KAAK,GAAG,GAAG;MACnB,CAAC,MAAM;QACL0H,MAAM,CAAC1H,KAAK,GAAGkK,IAAI,CAACiB,EAAE;QACtBxD,QAAQ,CAAC3H,KAAK,IAAIkK,IAAI,EAAAmC,MAAA,CAAAC,kBAAA,CAAK3E,QAAQ,CAAC3H,KAAK,EAAC;QAC1CyH,KAAK,CAACzH,KAAK,GAAG,GAAG;MACnB;IACF,CAAC;IACDuG,WAAW,CAAC,YAAM;MAChB,IAAMoC,MAAM,GAAGhC,SAAS,CAACgC,MAAM;MAC/BhC,SAAS,CAAC4F,oBAAoB,CAAC5D,MAAM,CAACe,QAAQ,CAAC;MAC/C/C,SAAS,CAAC4F,oBAAoB,CAAC5D,MAAM,CAACI,SAAS,CAAC;MAChDpC,SAAS,CAAC6F,UAAU,CAAC,CAAC,CAAC9J,IAAI,CAAC,YAAM;QAChCsG,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF3C,KAAK,CACH;MAAA,OAAMgB,cAAc,CAACtH,KAAK;IAAA,GAC1B,YAAM;MACJ,IAAIsH,cAAc,CAACtH,KAAK,EAAEuI,aAAa,CAACjB,cAAc,CAACtH,KAAK,CAAC;IAC/D,CAAC,EACD;MAAEyM,SAAS,EAAE;IAAK,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}