<template>
  <div class="SuggestAdvanceAssign">
    <xyl-label v-model="labelId" @labelClick="handleLabel">
      <xyl-label-item v-for="item in labelList" :key="item.itemCode" :value="item.itemCode">
        {{ item.itemName }}（{{ item.count }}）
      </xyl-label-item>
    </xyl-label>
    <xyl-search-button
      @queryClick="handleQuery"
      @resetClick="handleReset"
      @handleButton="handleButton"
      :buttonList="buttonList"
      :data="tableHead"
      ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table
        ref="tableRef"
        row-key="id"
        :data="tableData"
        @select="handleTableSelect"
        @select-all="handleTableSelect"
        @sort-change="handleSortChange"
        :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table
          :tableHead="tableHead"
          @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #mainHandleOffices="scope">
            <el-popover
              placement="top-start"
              v-for="item in scope.row.mainHandleOffices"
              :key="item.id"
              :disabled="item.users == null"
              popper-class="SuggestUnitPopper">
              <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
              </div>
              <div class="SuggestUnitPopperText">
                经办人：
                <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">
                  {{ index == 0 ? '' : '，' }}{{ items.userName }}（{{ items.mobile }}）
                </span>
              </div>
              <template #reference>
                <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                  {{ item.flowHandleOfficeName }}
                </span>
              </template>
            </el-popover>
          </template>
          <template #assistHandleOffices="scope">
            <el-popover
              placement="top-start"
              v-for="(item, i) in scope.row.assistHandleOffices"
              :key="item.id"
              :disabled="item.users == null"
              popper-class="SuggestUnitPopper">
              <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
              </div>
              <div class="SuggestUnitPopperText">
                经办人：
                <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">
                  {{ index == 0 ? '' : '，' }}{{ items.userName }}（{{ items.mobile }}）
                </span>
              </div>
              <template #reference>
                <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                  {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}
                </span>
              </template>
            </el-popover>
          </template>
          <template #publishHandleOffices="scope">
            <el-popover
              placement="top-start"
              v-for="(item, i) in scope.row.publishHandleOffices"
              :key="item.id"
              :disabled="item.users == null"
              popper-class="SuggestUnitPopper">
              <div :style="colorObj(item.suggestionHandleStatus, item.users == null)" class="SuggestUnitPopperName">
                {{ item.flowHandleOfficeName }}【{{ item.suggestionHandleStatusName }}】
              </div>
              <div class="SuggestUnitPopperText">
                经办人：
                <span v-for="(items, index) in item.users" v-copy="items.mobile" :key="items.id">
                  {{ index == 0 ? '' : '，' }}{{ items.userName }}（{{ items.mobile }}）
                </span>
              </div>
              <template #reference>
                <span :style="colorObj(item.suggestionHandleStatus, item.users == null)">
                  {{ i == 0 ? '' : '，' }}{{ item.flowHandleOfficeName }}
                </span>
              </template>
            </el-popover>
          </template>
        </xyl-global-table>
        <xyl-global-table-button label="" :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="show" name="批量交办" :beforeClose="handleBeforeClose">
      <SuggestBatchAdvanceAssignUnit :id="id" @callback="callback"></SuggestBatchAdvanceAssignUnit>
    </xyl-popup-window>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel
        name="预交办提案"
        :exportId="exportId"
        :params="exportParams"
        module="proposalExportExcel"
        :tableId="route.query.tableId"
        @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestAdvanceAssign' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
import SuggestBatchAdvanceAssignUnit from './component/SuggestBatchAdvanceAssignUnit.vue'

const route = useRoute()

const buttonList = ref([
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
])

const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  exportId,
  exportParams,
  exportShow,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  handleEditorCustom,
  handleQuery,
  handleGetParams,
  handleExportExcel,
  tableData,
  tableDataArray,
  tableQuery,
  tableRefReset
} = GlobalTable({ tableId: route.query.tableId, tableApi: 'suggestionList' })

const id = ref([])
const show = ref(false)
const labelId = ref('')
const labelList = ref([])

onActivated(() => {
  const suggestIds = JSON.parse(sessionStorage.getItem('suggestIds'))
  if (suggestIds) {
    tableQuery.value.ids = suggestIds
    suggestionCountSelector()
    setTimeout(() => {
      sessionStorage.removeItem('suggestIds')
      tableQuery.value.ids = []
    }, 1000)
  } else {
    suggestionCountSelector()
  }
})

const suggestionCountSelector = async () => {
  const { data } = await api.suggestionCountSelector({ countItemType: 'pre_assign' })
  labelId.value = data[0].itemCode
  labelList.value = data
  handleLabel()
}

const handleLabel = () => {
  tableQuery.value = { countItemCode: labelId.value || null }
  if (labelId.value === 'all') {
    buttonList.value = [
      { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
      { id: 'export', name: '导出Excel', type: 'primary', has: '' },
      { id: 'sign', name: '催办签收', type: 'primary', has: '' }
    ]
  } else if (labelId.value === 'no_receive') {
    buttonList.value = [
      { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
      { id: 'export', name: '导出Excel', type: 'primary', has: '' },
      { id: 'sign', name: '催办签收', type: 'primary', has: '' },
      { id: 'next', name: '批量交办', type: 'primary', has: 'next' }
    ]
  } else if (labelId.value === 'has_receive') {
    buttonList.value = [
      { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
      { id: 'export', name: '导出Excel', type: 'primary', has: '' },
      { id: 'next', name: '批量交办', type: 'primary', has: 'next' }
    ]
  }
  handleQuery()
}

const handleReset = () => {
  keyword.value = ''
  handleQuery()
}

const handleButton = (isType) => {
  switch (isType) {
    case 'next':
      if (tableDataArray.value.length) {
        id.value = tableDataArray.value.map((v) => v.id)
        show.value = true
      } else {
        ElMessage({ type: 'warning', message: '请至少选择一条数据' })
      }
      break
    case 'sign':
      handleSign()
      break
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleBeforeClose = (cb) => {
  tableRefReset()
  handleQuery()
  cb()
}

const handleSign = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作将会提醒选中的建议的办理单位尽快签收建议, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        suggestionPress('preAssignNoticeHandleOfficeConfirm')
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消提醒' })
      })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}

const suggestionPress = async (type) => {
  const { code } = await api.suggestionPress({ ids: tableDataArray.value.map((v) => v.id), pressMessageCode: type })
  if (code === 200) {
    ElMessage({ type: 'success', message: '提醒成功' })
    tableRefReset()
    handleQuery()
  }
}

const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}

const handleDetails = (item) => {
  qiankunMicro.setGlobalState({
    openRoute: {
      name: '建议详情',
      path: '/proposal/SuggestDetail',
      query: { id: item.id, type: labelId.value !== 'all' ? 'assign' : 'preAssign' }
    }
  })
}

const colorObj = (state, type) => {
  var color = { color: '#000' }
  if (state === 'has_answer') {
    color.color = '#4fcc72'
  } else if (state === 'handling') {
    color.color = '#fbd536'
  } else if (state === 'apply_adjust') {
    color.color = '#ca6063'
  }
  if (type) {
    color = { color: '#000' }
  }
  return color
}

const callback = () => {
  tableRefReset()
  suggestionCountSelector()
  show.value = false
  exportShow.value = false
}
</script>

<style lang="scss" scoped>
.SuggestAdvanceAssign {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - ((var(--zy-height) * 2) + (var(--zy-distance-four) * 4) + 42px));
  }
}

.SuggestUnitPopper {
  width: 500px !important;

  .SuggestUnitPopperName {
    font-size: var(--zy-name-font-size);
    line-height: var(--zy-line-height);
    padding-bottom: var(--zy-font-name-distance-five);
  }

  .SuggestUnitPopperText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
  }
}
</style>
