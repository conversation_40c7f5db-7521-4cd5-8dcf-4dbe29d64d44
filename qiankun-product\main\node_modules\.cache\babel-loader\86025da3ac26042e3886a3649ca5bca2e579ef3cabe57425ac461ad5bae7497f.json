{"ast": null, "code": "import api from '@/api';\nimport { computed } from 'vue';\nimport store from '@/store';\nimport { downloadFile } from '../../config/MicroGlobal';\nimport { globalFileLocation } from '../../config/location';\nvar __default__ = {\n  name: 'XylGlobalFile'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    fileData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    showPreview: {\n      type: Boolean,\n      default: true\n    },\n    showDownload: {\n      type: Boolean,\n      default: true\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var fileData = computed(function () {\n      return props.fileData;\n    });\n    var fileIcon = function fileIcon(fileType) {\n      var IconClass = {\n        docx: 'globalFileWord',\n        doc: 'globalFileWord',\n        wps: 'globalFileWPS',\n        xlsx: 'globalFileExcel',\n        xls: 'globalFileExcel',\n        pdf: 'globalFilePDF',\n        pptx: 'globalFilePPT',\n        ppt: 'globalFilePPT',\n        txt: 'globalFileTXT',\n        jpg: 'globalFilePicture',\n        png: 'globalFilePicture',\n        gif: 'globalFilePicture',\n        avi: 'globalFileVideo',\n        mp4: 'globalFileVideo',\n        zip: 'globalFileCompress',\n        rar: 'globalFileCompress'\n      };\n      return IconClass[fileType] || 'globalFileUnknown';\n    };\n    var handlePreview = function handlePreview(row) {\n      globalFileLocation({\n        name: process.env.VUE_APP_NAME,\n        fileId: row.id,\n        fileType: row.extName,\n        fileName: row.originalFileName,\n        fileSize: row.fileSize\n      });\n    };\n    var handleDownload = function handleDownload(row) {\n      if (window.__POWERED_BY_QIANKUN__) {\n        downloadFile({\n          fileId: row.id,\n          fileType: row.extName,\n          fileName: row.originalFileName,\n          fileSize: row.fileSize\n        });\n      } else {\n        if (window.__PUBLIC__) {\n          api.globalFileDownload(row.id, row.originalFileName);\n        } else {\n          store.commit('setDownloadFile', {\n            fileId: row.id,\n            fileType: row.extName,\n            fileName: row.originalFileName,\n            fileSize: row.fileSize\n          });\n        }\n      }\n    };\n    var __returned__ = {\n      props,\n      fileData,\n      fileIcon,\n      handlePreview,\n      handleDownload,\n      get api() {\n        return api;\n      },\n      computed,\n      get store() {\n        return store;\n      },\n      get downloadFile() {\n        return downloadFile;\n      },\n      get globalFileLocation() {\n        return globalFileLocation;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["api", "computed", "store", "downloadFile", "globalFileLocation", "__default__", "name", "props", "__props", "fileData", "fileIcon", "fileType", "IconClass", "docx", "doc", "wps", "xlsx", "xls", "pdf", "pptx", "ppt", "txt", "jpg", "png", "gif", "avi", "mp4", "zip", "rar", "handlePreview", "row", "process", "env", "VUE_APP_NAME", "fileId", "id", "extName", "fileName", "originalFileName", "fileSize", "handleDownload", "window", "__POWERED_BY_QIANKUN__", "__PUBLIC__", "globalFileDownload", "commit"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/xyl-global-file/xyl-global-file.vue"], "sourcesContent": ["<template>\r\n  <div class=\"xyl-global-file\">\r\n    <div class=\"globalFile\" v-for=\"item in fileData\" :key=\"item.id\">\r\n      <div class=\"globalFileIcon\" :class=\"fileIcon(item.extName)\"></div>\r\n      <div class=\"globalFileText ellipsis\">{{ item.originalFileName }}</div>\r\n      <div class=\"globalFileButton\">\r\n        <slot :row=\"item\"></slot>\r\n        <span @click=\"handlePreview(item)\" v-if=\"props.showPreview\"></span>\r\n        <span @click=\"handleDownload(item)\" v-if=\"props.showDownload\"></span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'XylGlobalFile' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { computed } from 'vue'\r\nimport store from '@/store'\r\nimport { downloadFile } from '../../config/MicroGlobal'\r\nimport { globalFileLocation } from '../../config/location'\r\nconst props = defineProps({\r\n  fileData: { type: Array, default: () => [] },\r\n  showPreview: { type: Boolean, default: true },\r\n  showDownload: { type: Boolean, default: true }\r\n})\r\nconst fileData = computed(() => props.fileData)\r\n\r\nconst fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nconst handlePreview = (row) => {\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nconst handleDownload = (row) => {\r\n  if (window.__POWERED_BY_QIANKUN__) {\r\n    downloadFile({ fileId: row.id, fileType: row.extName, fileName: row.originalFileName, fileSize: row.fileSize })\r\n  } else {\r\n    if (window.__PUBLIC__) {\r\n      api.globalFileDownload(row.id, row.originalFileName)\r\n    } else {\r\n      store.commit('setDownloadFile', {\r\n        fileId: row.id,\r\n        fileType: row.extName,\r\n        fileName: row.originalFileName,\r\n        fileSize: row.fileSize\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.xyl-global-file {\r\n  padding-bottom: var(--zy-distance-four);\r\n\r\n  .globalFile {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    padding-bottom: var(--zy-distance-five);\r\n\r\n    .globalFileIcon {\r\n      width: var(--zy-title-font-size);\r\n      height: var(--zy-title-font-size);\r\n      min-width: var(--zy-title-font-size);\r\n      vertical-align: middle;\r\n    }\r\n\r\n    .globalFileText {\r\n      max-width: 100%;\r\n      padding-left: 6px;\r\n      padding-right: 20px;\r\n      position: relative;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      vertical-align: middle;\r\n    }\r\n\r\n    .globalFileButton {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      span {\r\n        display: inline-block;\r\n        width: var(--zy-title-font-size);\r\n        height: var(--zy-title-font-size);\r\n        background: url('./img/preview.png') no-repeat;\r\n        background-size: 80% 80%;\r\n        background-position: center;\r\n        cursor: pointer;\r\n        vertical-align: middle;\r\n      }\r\n\r\n      span + span {\r\n        margin-left: var(--zy-distance-two);\r\n        background: url('./img/download.png') no-repeat;\r\n        background-size: 80% 80%;\r\n        background-position: center;\r\n      }\r\n    }\r\n\r\n    .globalFileUnknown {\r\n      background: url('./img/unknown.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFilePDF {\r\n      background: url('./img/PDF.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFileWord {\r\n      background: url('./img/Word.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFileExcel {\r\n      background: url('./img/Excel.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFilePicture {\r\n      background: url('./img/picture.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFileVideo {\r\n      background: url('./img/video.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFileTXT {\r\n      background: url('./img/TXT.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFileCompress {\r\n      background: url('./img/compress.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFileWPS {\r\n      background: url('./img/WPS.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n\r\n    .globalFilePPT {\r\n      background: url('./img/PPT.png') no-repeat;\r\n      background-size: 80% 80%;\r\n      background-position: center;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAiBA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,KAAK;AAC9B,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,kBAAkB,QAAQ,uBAAuB;AAP1D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;IAQxC,IAAMC,KAAK,GAAGC,OAIZ;IACF,IAAMC,QAAQ,GAAGR,QAAQ,CAAC;MAAA,OAAMM,KAAK,CAACE,QAAQ;IAAA,EAAC;IAE/C,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,QAAQ,EAAK;MAC7B,IAAMC,SAAS,GAAG;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,GAAG,EAAE,gBAAgB;QACrBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,iBAAiB;QACvBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,eAAe;QACrBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,oBAAoB;QACzBC,GAAG,EAAE;MACP,CAAC;MACD,OAAOhB,SAAS,CAACD,QAAQ,CAAC,IAAI,mBAAmB;IACnD,CAAC;IACD,IAAMkB,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAK;MAC7B1B,kBAAkB,CAAC;QACjBE,IAAI,EAAEyB,OAAO,CAACC,GAAG,CAACC,YAAY;QAC9BC,MAAM,EAAEJ,GAAG,CAACK,EAAE;QACdxB,QAAQ,EAAEmB,GAAG,CAACM,OAAO;QACrBC,QAAQ,EAAEP,GAAG,CAACQ,gBAAgB;QAC9BC,QAAQ,EAAET,GAAG,CAACS;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIV,GAAG,EAAK;MAC9B,IAAIW,MAAM,CAACC,sBAAsB,EAAE;QACjCvC,YAAY,CAAC;UAAE+B,MAAM,EAAEJ,GAAG,CAACK,EAAE;UAAExB,QAAQ,EAAEmB,GAAG,CAACM,OAAO;UAAEC,QAAQ,EAAEP,GAAG,CAACQ,gBAAgB;UAAEC,QAAQ,EAAET,GAAG,CAACS;QAAS,CAAC,CAAC;MACjH,CAAC,MAAM;QACL,IAAIE,MAAM,CAACE,UAAU,EAAE;UACrB3C,GAAG,CAAC4C,kBAAkB,CAACd,GAAG,CAACK,EAAE,EAAEL,GAAG,CAACQ,gBAAgB,CAAC;QACtD,CAAC,MAAM;UACLpC,KAAK,CAAC2C,MAAM,CAAC,iBAAiB,EAAE;YAC9BX,MAAM,EAAEJ,GAAG,CAACK,EAAE;YACdxB,QAAQ,EAAEmB,GAAG,CAACM,OAAO;YACrBC,QAAQ,EAAEP,GAAG,CAACQ,gBAAgB;YAC9BC,QAAQ,EAAET,GAAG,CAACS;UAChB,CAAC,CAAC;QACJ;MACF;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}