<template>
  <div class="GlobalAiChatFile" ref="tag">
    <div class="GlobalAiChatFileWrap">
      <div class="GlobalAiChatFileScroll" :style="scrollStyle" ref="scroll">
        <div class="GlobalAiChatFileItem" v-for="item in fileData" :key="item.id" @click="handlePreview(item)">
          <div class="GlobalAiChatFileItemClose" @click="handleClose(item)">
            <el-icon>
              <CircleCloseFilled />
            </el-icon>
          </div>
          <div class="globalFileIcon" :class="fileIcon(item?.extName)"></div>
          <div class="GlobalChatMessagesFileName ellipsis">{{ item?.originalFileName || '未知文件' }}</div>
          <div class="GlobalChatMessagesFileSize">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>
        </div>
        <div class="GlobalAiChatFileItem" v-for="item in fileList" :key="item.uid">
          <div class="globalFileIcon" :class="fileIcon(item?.fileType)"></div>
          <div class="GlobalChatMessagesFileName ellipsis">{{ item?.fileName || '未知文件' }}</div>
          <div class="GlobalChatMessagesFileSize">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>
          <el-progress :percentage="item.progress" :show-text="false" :stroke-width="2" />
        </div>
      </div>
      <div class="GlobalAiChatFilePrev" v-if="prevShow" @click="scrollClick('prev')">
        <el-icon>
          <DArrowLeft />
        </el-icon>
      </div>
      <div class="GlobalAiChatFileNext" v-if="nextShow" @click="scrollClick('next')">
        <el-icon>
          <DArrowRight />
        </el-icon>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'GlobalAiChatFile' }
</script>
<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { size2Str } from 'common/js/utils.js'
import { globalFileLocation } from 'common/config/location'
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
const props = defineProps({
  fileList: { type: Array, default: () => [] },
  fileData: { type: Array, default: () => [] },
  distance: { type: Number, default: 168 }
})
const emit = defineEmits(['close'])
const fileList = computed(() => props.fileList)
const fileData = computed(() => props.fileData)
const fileIcon = (fileType) => {
  const IconClass = {
    docx: 'globalFileWord',
    doc: 'globalFileWord',
    wps: 'globalFileWPS',
    xlsx: 'globalFileExcel',
    xls: 'globalFileExcel',
    pdf: 'globalFilePDF',
    pptx: 'globalFilePPT',
    ppt: 'globalFilePPT',
    txt: 'globalFileTXT',
    jpg: 'globalFilePicture',
    png: 'globalFilePicture',
    gif: 'globalFilePicture',
    avi: 'globalFileVideo',
    mp4: 'globalFileVideo',
    zip: 'globalFileCompress',
    rar: 'globalFileCompress'
  }
  return IconClass[fileType] || 'globalFileUnknown'
}
const scrollStyle = computed(() => ({ transform: `translateX(${scrollLeft.value}px)` }))
const tag = ref()
const scroll = ref()
const prevShow = ref(false)
const nextShow = ref(false)
const scrollLeft = ref(0)
const scrollClick = (type) => {
  const left = tag.value.offsetWidth - scroll.value.scrollWidth
  if (type === 'prev') {
    scrollLeft.value = scrollLeft.value + props.distance > 0 ? 0 : scrollLeft.value + props.distance
  } else if (type === 'next') {
    scrollLeft.value = scrollLeft.value - props.distance < left ? left : scrollLeft.value - props.distance
  }
  delay(() => {
    prevShow.value = scrollLeft.value !== 0
    nextShow.value = scrollLeft.value !== left
  }, 520)
}
const delay = (() => {
  let timer = 0
  return (callback, ms) => {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
const handleClose = (item) => {
  emit('close', item)
}
const handleResizeWindow = () => {
  nextTick(() => {
    if (tag.value.offsetWidth < scroll.value.offsetWidth) {
      nextShow.value = true
    }
  })
}
const handlePreview = (row) => {
  globalFileLocation({
    name: process.env.VUE_APP_NAME,
    fileId: row.id,
    fileType: row.extName,
    fileName: row.originalFileName,
    fileSize: row.fileSize
  })
}
onMounted(() => {
  nextTick(() => {
    erd.listenTo(tag.value, () => {
      handleResizeWindow()
    })
  })
  nextTick(() => {
    erd.listenTo(scroll.value, () => {
      handleResizeWindow()
    })
  })
})
onUnmounted(() => {
  erd.uninstall(tag.value)
  erd.uninstall(scroll.value)
})
</script>
<style lang="scss">
.GlobalAiChatFile {
  width: 100%;
  background-color: #fff;
  position: relative;
  border-radius: 8px;
  overflow: hidden;

  .GlobalAiChatFileWrap {
    width: 100%;
    overflow: hidden;

    .GlobalAiChatFilePrev,
    .GlobalAiChatFileNext {
      position: absolute;
      top: 0;
      height: 100%;
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      z-index: 9;
    }

    .GlobalAiChatFilePrev {
      left: 0;
    }

    .GlobalAiChatFileNext {
      right: 0;
    }

    .GlobalAiChatFileScroll {
      padding: 6px 12px;
      white-space: nowrap;
      position: relative;
      transition: transform 0.3s;
      float: left;
      display: block;
      z-index: 3;

      .GlobalAiChatFileItem {
        width: 180px;
        height: 52px;
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        background: #fff;
        position: relative;
        padding: 0 40px 0 12px;
        border-radius: var(--el-border-radius-base);
        border: 1px solid var(--zy-el-border-color-light);
        background: var(--zy-el-color-info-light-9);
        word-wrap: break-word;
        white-space: pre-wrap;
        cursor: pointer;

        & + .GlobalAiChatFileItem {
          margin-left: 12px;
        }

        &:hover {
          .GlobalAiChatFileItemClose {
            display: inline-block;
          }
        }

        .GlobalAiChatFileItemClose {
          width: 14px;
          height: 14px;
          font-size: 14px;
          display: none;
          position: absolute;
          top: 0;
          right: 0;
          transform: translate(50%, -50%);
        }

        .zy-el-progress {
          position: absolute;
          left: 50%;
          bottom: 0;
          width: 100%;
          transform: translateX(-50%);
        }

        .GlobalChatMessagesFileName {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          padding-bottom: 2px;
        }

        .GlobalChatMessagesFileSize {
          color: var(--zy-el-text-color-secondary);
          font-size: calc(var(--zy-text-font-size) - 2px);
        }

        .globalFileIcon {
          width: 28px;
          height: 28px;
          vertical-align: middle;
          position: absolute;
          top: 50%;
          right: 6px;
          transform: translateY(-50%);
        }

        .globalFileUnknown {
          background: url('./img/unknown.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFilePDF {
          background: url('./img/PDF.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFileWord {
          background: url('./img/Word.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFileExcel {
          background: url('./img/Excel.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFilePicture {
          background: url('./img/picture.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFileVideo {
          background: url('./img/video.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFileTXT {
          background: url('./img/TXT.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFileCompress {
          background: url('./img/compress.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFileWPS {
          background: url('./img/WPS.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }

        .globalFilePPT {
          background: url('./img/PPT.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;
        }
      }
    }
  }
}
</style>
