{"ast": null, "code": "var _process$env, _window3;\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar origin = window.location.origin;\nvar catalog = ((_process$env = process.env) === null || _process$env === void 0 ? void 0 : _process$env.VUE_APP_CATALOG) || '/';\nvar isShow = process.env.NODE_ENV === 'development';\nvar microAppObject = {\n  demo: 'http://localhost:200/',\n  system: 'http://localhost:2001/',\n  interaction: 'http://localhost:2002/',\n  information: 'http://localhost:2003/',\n  cloudDisk: 'http://localhost:2004/',\n  networkPolitics: 'http://localhost:2005/',\n  npcDeputy: 'http://localhost:2006/',\n  cppccMember: 'http://localhost:2007/',\n  activity: 'http://localhost:2008/',\n  onDuty: 'http://localhost:2009/',\n  performDuties: 'http://localhost:2010/',\n  learningTraining: 'http://localhost:2011/',\n  suggest: 'http://localhost:2012/',\n  proposal: 'http://localhost:2013/',\n  dataVisual: 'http://localhost:2022/',\n  partyBuilding: 'http://localhost:2033/',\n  negotiation: 'http://localhost:2220/',\n  interactionContact: 'http://localhost:2023/',\n  legislationGeneral: 'http://localhost:2024/',\n  legislation: 'http://localhost:2025/',\n  recordingReview: 'http://localhost:2026/',\n  dataSupportPlatform: 'http://localhost:2028/',\n  apporem: 'http://localhost:2029/',\n  largeScreen: 'http://localhost:2068/',\n  minSuggest: 'http://localhost:2030/',\n  publicOpinion: 'http://localhost:2031/',\n  networkSupervis: 'http://localhost:2032/',\n  chorography: 'http://localhost:2098/',\n  chroniclesMuseum: 'http://localhost:2099/',\n  contactStation: 'http://localhost:2036/',\n  meetting: 'http://localhost:2052/',\n  readingText: 'http://localhost:2053/',\n  zhtzWeb: 'http://localhost:3333/',\n  kanban: 'http://localhost:2034/',\n  infoManage: 'http://localhost:2036/',\n  exhibition: 'http://localhost:2035/',\n  oldSupervision: 'http://localhost:2222/',\n  oldSuggest: 'http://localhost:2227/',\n  oldAcademy: 'http://localhost:2228/',\n  supportSystem: 'http://localhost:2229/',\n  oldScreenData: 'http://localhost:2111/',\n  zhtDeputyWorkstation: 'http://localhost:2066/',\n  zhtIntelligenceMeetting: 'http://localhost:2067/',\n  partyBuildingResumptionCedit: 'http://localhost:2069/',\n  keyProjectSupervision: 'http://localhost:2110/',\n  bigScreenManage: 'http://localhost:3001/',\n  administrationManage: 'http://localhost:2037/',\n  unitedFront: 'http://localhost:2091/',\n  allCFIC: 'http://localhost:2055/',\n  conferenceSystem: 'http://localhost:2073/',\n  supervisionManagement: 'http://localhost:2072/',\n  businessInspectionPoint: 'http://localhost:2071/',\n  leaveModule: 'http://localhost:2074/',\n  PartyBuildingPerformanceCreditDepartment: 'http://localhost:2070/',\n  eedsGeneralModule: 'http://localhost:2112/',\n  opMgtSys: 'http://localhost:2088/',\n  electronicFiling: 'http://localhost:2089/',\n  supervisionWork: 'http://localhost:2095/'\n};\nvar microAppData = function microAppData() {\n  var _window;\n  /**\r\n   * 微应用配置\r\n   * name: 微应用名称 - 具有唯一性\r\n   * entry: 微应用入口 - 通过该地址加载微应用\r\n   * container: 微应用挂载节点 - 微应用加载完成后将挂载在该节点上\r\n   * activeRule: 微应用触发的路由规则 - 触发路由规则后将加载该微应用\r\n   */\n  var microApp = {};\n  var prefetchApp = [];\n  var timeStr = new Date().getHours();\n  if ((_window = window) !== null && _window !== void 0 && (_window = _window.globalConfig) !== null && _window !== void 0 && _window.VUE_APP_MICRO_APP.length) {\n    var _window2;\n    var VUE_APP_MICRO_APP = (_window2 = window) === null || _window2 === void 0 || (_window2 = _window2.globalConfig) === null || _window2 === void 0 ? void 0 : _window2.VUE_APP_MICRO_APP;\n    for (var key in microAppObject) {\n      if (Object.hasOwnProperty.call(microAppObject, key) && VUE_APP_MICRO_APP.includes(key)) {\n        microApp[key] = isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`;\n        prefetchApp.push({\n          name: key,\n          entry: isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`\n        });\n      }\n    }\n  } else {\n    for (var _key in microAppObject) {\n      if (Object.hasOwnProperty.call(microAppObject, _key)) {\n        microApp[_key] = isShow ? microAppObject[_key] : `${origin}${catalog}microApp/${_key}/?time=${timeStr}`;\n        prefetchApp.push({\n          name: _key,\n          entry: isShow ? microAppObject[_key] : `${origin}${catalog}microApp/${_key}/?time=${timeStr}`\n        });\n      }\n    }\n  }\n  return {\n    microApp,\n    prefetchApp\n  };\n};\nvar mainPath = isShow ? 'http://localhost:2000/' : origin + catalog;\nvar microAppPath = function microAppPath(key) {\n  return Object.hasOwnProperty.call(microAppObject, key) ? isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/` : '';\n};\nvar systemUrl = function systemUrl(url) {\n  if (!url) return '';\n  var Expression = /http(s)?:\\/\\/([\\w-]+\\.)+[\\w-]+(\\/[\\w- .\\/?%&=]*)?/;\n  var pathExp = new RegExp(Expression);\n  return pathExp.test(url) ? url : `${window.location.protocol}//${window.location.hostname}:${url}`;\n};\nvar config = _objectSpread({\n  // 后台接口配置\n  API_URL: systemUrl(((_window3 = window) === null || _window3 === void 0 || (_window3 = _window3.globalConfig) === null || _window3 === void 0 ? void 0 : _window3.API_URL) || process.env.VUE_APP_URL) || 'https://productpc.cszysoft.com:8080/lzt',\n  origin: origin,\n  catalog: catalog,\n  mainPath: mainPath,\n  microAppPath: microAppPath\n}, microAppData());\n// console.log(process.env)\nconsole.log(config);\nexport default config;", "map": {"version": 3, "names": ["origin", "window", "location", "catalog", "_process$env", "process", "env", "VUE_APP_CATALOG", "isShow", "NODE_ENV", "microAppObject", "demo", "system", "interaction", "information", "cloudDisk", "networkPolitics", "npcDeputy", "cppccMember", "activity", "onDuty", "perform<PERSON><PERSON><PERSON>", "learningTraining", "suggest", "proposal", "dataVisual", "partyBuilding", "negotiation", "interactionContact", "legislationGeneral", "legislation", "recordingReview", "dataSupportPlatform", "apporem", "largeScreen", "minSuggest", "publicOpinion", "networkSupervis", "chorography", "chroniclesMuseum", "contactStation", "meetting", "readingText", "zhtzWeb", "kanban", "infoManage", "exhibition", "oldSupervision", "oldSuggest", "oldAcademy", "supportSystem", "oldScreenData", "zhtDeputyWorkstation", "zhtIntelligenceMeetting", "partyBuildingResumptionCedit", "keyProjectSupervision", "bigScreenManage", "administrationManage", "unitedFront", "allCFIC", "conferenceSystem", "supervisionManagement", "businessInspectionPoint", "leaveModule", "PartyBuildingPerformanceCreditDepartment", "eedsGeneralModule", "opMgtSys", "electronicFiling", "supervisionWork", "microAppData", "_window", "microApp", "prefetchApp", "timeStr", "Date", "getHours", "globalConfig", "VUE_APP_MICRO_APP", "length", "_window2", "key", "Object", "hasOwnProperty", "call", "includes", "push", "name", "entry", "mainP<PERSON>", "microAppPath", "systemUrl", "url", "Expression", "pathExp", "RegExp", "test", "protocol", "hostname", "config", "_objectSpread", "API_URL", "_window3", "VUE_APP_URL", "console", "log"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/config/index.js"], "sourcesContent": ["const origin = window.location.origin\r\nconst catalog = process.env?.VUE_APP_CATALOG || '/'\r\nconst isShow = process.env.NODE_ENV === 'development'\r\nconst microAppObject = {\r\n  demo: 'http://localhost:200/',\r\n  system: 'http://localhost:2001/',\r\n  interaction: 'http://localhost:2002/',\r\n  information: 'http://localhost:2003/',\r\n  cloudDisk: 'http://localhost:2004/',\r\n  networkPolitics: 'http://localhost:2005/',\r\n  npcDeputy: 'http://localhost:2006/',\r\n  cppccMember: 'http://localhost:2007/',\r\n  activity: 'http://localhost:2008/',\r\n  onDuty: 'http://localhost:2009/',\r\n  performDuties: 'http://localhost:2010/',\r\n  learningTraining: 'http://localhost:2011/',\r\n  suggest: 'http://localhost:2012/',\r\n  proposal: 'http://localhost:2013/',\r\n  dataVisual: 'http://localhost:2022/',\r\n  partyBuilding: 'http://localhost:2033/',\r\n  negotiation: 'http://localhost:2220/',\r\n  interactionContact: 'http://localhost:2023/',\r\n  legislationGeneral: 'http://localhost:2024/',\r\n  legislation: 'http://localhost:2025/',\r\n  recordingReview: 'http://localhost:2026/',\r\n  dataSupportPlatform: 'http://localhost:2028/',\r\n  apporem: 'http://localhost:2029/',\r\n  largeScreen: 'http://localhost:2068/',\r\n  minSuggest: 'http://localhost:2030/',\r\n  publicOpinion: 'http://localhost:2031/',\r\n  networkSupervis: 'http://localhost:2032/',\r\n  chorography: 'http://localhost:2098/',\r\n  chroniclesMuseum: 'http://localhost:2099/',\r\n  contactStation: 'http://localhost:2036/',\r\n  meetting: 'http://localhost:2052/',\r\n  readingText: 'http://localhost:2053/',\r\n  zhtzWeb: 'http://localhost:3333/',\r\n  kanban: 'http://localhost:2034/',\r\n  infoManage: 'http://localhost:2036/',\r\n  exhibition: 'http://localhost:2035/',\r\n  oldSupervision: 'http://localhost:2222/',\r\n  oldSuggest: 'http://localhost:2227/',\r\n  oldAcademy: 'http://localhost:2228/',\r\n  supportSystem: 'http://localhost:2229/',\r\n  oldScreenData: 'http://localhost:2111/',\r\n  zhtDeputyWorkstation: 'http://localhost:2066/',\r\n  zhtIntelligenceMeetting: 'http://localhost:2067/',\r\n  partyBuildingResumptionCedit: 'http://localhost:2069/',\r\n  keyProjectSupervision: 'http://localhost:2110/',\r\n  bigScreenManage: 'http://localhost:3001/',\r\n  administrationManage: 'http://localhost:2037/',\r\n  unitedFront: 'http://localhost:2091/',\r\n  allCFIC: 'http://localhost:2055/',\r\n  conferenceSystem: 'http://localhost:2073/',\r\n  supervisionManagement: 'http://localhost:2072/',\r\n  businessInspectionPoint: 'http://localhost:2071/',\r\n  leaveModule: 'http://localhost:2074/',\r\n  PartyBuildingPerformanceCreditDepartment: 'http://localhost:2070/',\r\n  eedsGeneralModule: 'http://localhost:2112/',\r\n  opMgtSys: 'http://localhost:2088/',\r\n  electronicFiling: 'http://localhost:2089/',\r\n  supervisionWork: 'http://localhost:2095/'\r\n}\r\nconst microAppData = () => {\r\n  /**\r\n   * 微应用配置\r\n   * name: 微应用名称 - 具有唯一性\r\n   * entry: 微应用入口 - 通过该地址加载微应用\r\n   * container: 微应用挂载节点 - 微应用加载完成后将挂载在该节点上\r\n   * activeRule: 微应用触发的路由规则 - 触发路由规则后将加载该微应用\r\n   */\r\n  var microApp = {}\r\n  var prefetchApp = []\r\n  const timeStr = new Date().getHours()\r\n  if (window?.globalConfig?.VUE_APP_MICRO_APP.length) {\r\n    const VUE_APP_MICRO_APP = window?.globalConfig?.VUE_APP_MICRO_APP\r\n    for (const key in microAppObject) {\r\n      if (Object.hasOwnProperty.call(microAppObject, key) && VUE_APP_MICRO_APP.includes(key)) {\r\n        microApp[key] = isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`\r\n        prefetchApp.push({\r\n          name: key,\r\n          entry: isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`\r\n        })\r\n      }\r\n    }\r\n  } else {\r\n    for (const key in microAppObject) {\r\n      if (Object.hasOwnProperty.call(microAppObject, key)) {\r\n        microApp[key] = isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`\r\n        prefetchApp.push({\r\n          name: key,\r\n          entry: isShow ? microAppObject[key] : `${origin}${catalog}microApp/${key}/?time=${timeStr}`\r\n        })\r\n      }\r\n    }\r\n  }\r\n  return { microApp, prefetchApp }\r\n}\r\nconst mainPath = isShow ? 'http://localhost:2000/' : origin + catalog\r\nconst microAppPath = (key) =>\r\n  Object.hasOwnProperty.call(microAppObject, key)\r\n    ? isShow\r\n      ? microAppObject[key]\r\n      : `${origin}${catalog}microApp/${key}/`\r\n    : ''\r\nconst systemUrl = (url) => {\r\n  if (!url) return ''\r\n  const Expression = /http(s)?:\\/\\/([\\w-]+\\.)+[\\w-]+(\\/[\\w- .\\/?%&=]*)?/\r\n  const pathExp = new RegExp(Expression)\r\n  return pathExp.test(url) ? url : `${window.location.protocol}//${window.location.hostname}:${url}`\r\n}\r\nconst config = {\r\n  // 后台接口配置\r\n  API_URL:\r\n    systemUrl(window?.globalConfig?.API_URL || process.env.VUE_APP_URL) || 'https://productpc.cszysoft.com:8080/lzt',\r\n  origin: origin,\r\n  catalog: catalog,\r\n  mainPath: mainPath,\r\n  microAppPath: microAppPath,\r\n  // 生成子应用配置\r\n  ...microAppData()\r\n}\r\n// console.log(process.env)\r\nconsole.log(config)\r\nexport default config\r\n"], "mappings": ";;;;;;AAAA,IAAMA,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACF,MAAM;AACrC,IAAMG,OAAO,GAAG,EAAAC,YAAA,GAAAC,OAAO,CAACC,GAAG,cAAAF,YAAA,uBAAXA,YAAA,CAAaG,eAAe,KAAI,GAAG;AACnD,IAAMC,MAAM,GAAGH,OAAO,CAACC,GAAG,CAACG,QAAQ,KAAK,aAAa;AACrD,IAAMC,cAAc,GAAG;EACrBC,IAAI,EAAE,uBAAuB;EAC7BC,MAAM,EAAE,wBAAwB;EAChCC,WAAW,EAAE,wBAAwB;EACrCC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,wBAAwB;EACnCC,eAAe,EAAE,wBAAwB;EACzCC,SAAS,EAAE,wBAAwB;EACnCC,WAAW,EAAE,wBAAwB;EACrCC,QAAQ,EAAE,wBAAwB;EAClCC,MAAM,EAAE,wBAAwB;EAChCC,aAAa,EAAE,wBAAwB;EACvCC,gBAAgB,EAAE,wBAAwB;EAC1CC,OAAO,EAAE,wBAAwB;EACjCC,QAAQ,EAAE,wBAAwB;EAClCC,UAAU,EAAE,wBAAwB;EACpCC,aAAa,EAAE,wBAAwB;EACvCC,WAAW,EAAE,wBAAwB;EACrCC,kBAAkB,EAAE,wBAAwB;EAC5CC,kBAAkB,EAAE,wBAAwB;EAC5CC,WAAW,EAAE,wBAAwB;EACrCC,eAAe,EAAE,wBAAwB;EACzCC,mBAAmB,EAAE,wBAAwB;EAC7CC,OAAO,EAAE,wBAAwB;EACjCC,WAAW,EAAE,wBAAwB;EACrCC,UAAU,EAAE,wBAAwB;EACpCC,aAAa,EAAE,wBAAwB;EACvCC,eAAe,EAAE,wBAAwB;EACzCC,WAAW,EAAE,wBAAwB;EACrCC,gBAAgB,EAAE,wBAAwB;EAC1CC,cAAc,EAAE,wBAAwB;EACxCC,QAAQ,EAAE,wBAAwB;EAClCC,WAAW,EAAE,wBAAwB;EACrCC,OAAO,EAAE,wBAAwB;EACjCC,MAAM,EAAE,wBAAwB;EAChCC,UAAU,EAAE,wBAAwB;EACpCC,UAAU,EAAE,wBAAwB;EACpCC,cAAc,EAAE,wBAAwB;EACxCC,UAAU,EAAE,wBAAwB;EACpCC,UAAU,EAAE,wBAAwB;EACpCC,aAAa,EAAE,wBAAwB;EACvCC,aAAa,EAAE,wBAAwB;EACvCC,oBAAoB,EAAE,wBAAwB;EAC9CC,uBAAuB,EAAE,wBAAwB;EACjDC,4BAA4B,EAAE,wBAAwB;EACtDC,qBAAqB,EAAE,wBAAwB;EAC/CC,eAAe,EAAE,wBAAwB;EACzCC,oBAAoB,EAAE,wBAAwB;EAC9CC,WAAW,EAAE,wBAAwB;EACrCC,OAAO,EAAE,wBAAwB;EACjCC,gBAAgB,EAAE,wBAAwB;EAC1CC,qBAAqB,EAAE,wBAAwB;EAC/CC,uBAAuB,EAAE,wBAAwB;EACjDC,WAAW,EAAE,wBAAwB;EACrCC,wCAAwC,EAAE,wBAAwB;EAClEC,iBAAiB,EAAE,wBAAwB;EAC3CC,QAAQ,EAAE,wBAAwB;EAClCC,gBAAgB,EAAE,wBAAwB;EAC1CC,eAAe,EAAE;AACnB,CAAC;AACD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EAAA,IAAAC,OAAA;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAMC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;EACrC,KAAAL,OAAA,GAAIrE,MAAM,cAAAqE,OAAA,gBAAAA,OAAA,GAANA,OAAA,CAAQM,YAAY,cAAAN,OAAA,eAApBA,OAAA,CAAsBO,iBAAiB,CAACC,MAAM,EAAE;IAAA,IAAAC,QAAA;IAClD,IAAMF,iBAAiB,IAAAE,QAAA,GAAG9E,MAAM,cAAA8E,QAAA,gBAAAA,QAAA,GAANA,QAAA,CAAQH,YAAY,cAAAG,QAAA,uBAApBA,QAAA,CAAsBF,iBAAiB;IACjE,KAAK,IAAMG,GAAG,IAAItE,cAAc,EAAE;MAChC,IAAIuE,MAAM,CAACC,cAAc,CAACC,IAAI,CAACzE,cAAc,EAAEsE,GAAG,CAAC,IAAIH,iBAAiB,CAACO,QAAQ,CAACJ,GAAG,CAAC,EAAE;QACtFT,QAAQ,CAACS,GAAG,CAAC,GAAGxE,MAAM,GAAGE,cAAc,CAACsE,GAAG,CAAC,GAAG,GAAGhF,MAAM,GAAGG,OAAO,YAAY6E,GAAG,UAAUP,OAAO,EAAE;QACpGD,WAAW,CAACa,IAAI,CAAC;UACfC,IAAI,EAAEN,GAAG;UACTO,KAAK,EAAE/E,MAAM,GAAGE,cAAc,CAACsE,GAAG,CAAC,GAAG,GAAGhF,MAAM,GAAGG,OAAO,YAAY6E,GAAG,UAAUP,OAAO;QAC3F,CAAC,CAAC;MACJ;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAMO,IAAG,IAAItE,cAAc,EAAE;MAChC,IAAIuE,MAAM,CAACC,cAAc,CAACC,IAAI,CAACzE,cAAc,EAAEsE,IAAG,CAAC,EAAE;QACnDT,QAAQ,CAACS,IAAG,CAAC,GAAGxE,MAAM,GAAGE,cAAc,CAACsE,IAAG,CAAC,GAAG,GAAGhF,MAAM,GAAGG,OAAO,YAAY6E,IAAG,UAAUP,OAAO,EAAE;QACpGD,WAAW,CAACa,IAAI,CAAC;UACfC,IAAI,EAAEN,IAAG;UACTO,KAAK,EAAE/E,MAAM,GAAGE,cAAc,CAACsE,IAAG,CAAC,GAAG,GAAGhF,MAAM,GAAGG,OAAO,YAAY6E,IAAG,UAAUP,OAAO;QAC3F,CAAC,CAAC;MACJ;IACF;EACF;EACA,OAAO;IAAEF,QAAQ;IAAEC;EAAY,CAAC;AAClC,CAAC;AACD,IAAMgB,QAAQ,GAAGhF,MAAM,GAAG,wBAAwB,GAAGR,MAAM,GAAGG,OAAO;AACrE,IAAMsF,YAAY,GAAG,SAAfA,YAAYA,CAAIT,GAAG;EAAA,OACvBC,MAAM,CAACC,cAAc,CAACC,IAAI,CAACzE,cAAc,EAAEsE,GAAG,CAAC,GAC3CxE,MAAM,GACJE,cAAc,CAACsE,GAAG,CAAC,GACnB,GAAGhF,MAAM,GAAGG,OAAO,YAAY6E,GAAG,GAAG,GACvC,EAAE;AAAA;AACR,IAAMU,SAAS,GAAG,SAAZA,SAASA,CAAIC,GAAG,EAAK;EACzB,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;EACnB,IAAMC,UAAU,GAAG,mDAAmD;EACtE,IAAMC,OAAO,GAAG,IAAIC,MAAM,CAACF,UAAU,CAAC;EACtC,OAAOC,OAAO,CAACE,IAAI,CAACJ,GAAG,CAAC,GAAGA,GAAG,GAAG,GAAG1F,MAAM,CAACC,QAAQ,CAAC8F,QAAQ,KAAK/F,MAAM,CAACC,QAAQ,CAAC+F,QAAQ,IAAIN,GAAG,EAAE;AACpG,CAAC;AACD,IAAMO,MAAM,GAAAC,aAAA;EACV;EACAC,OAAO,EACLV,SAAS,CAAC,EAAAW,QAAA,GAAApG,MAAM,cAAAoG,QAAA,gBAAAA,QAAA,GAANA,QAAA,CAAQzB,YAAY,cAAAyB,QAAA,uBAApBA,QAAA,CAAsBD,OAAO,KAAI/F,OAAO,CAACC,GAAG,CAACgG,WAAW,CAAC,IAAI,yCAAyC;EAClHtG,MAAM,EAAEA,MAAM;EACdG,OAAO,EAAEA,OAAO;EAChBqF,QAAQ,EAAEA,QAAQ;EAClBC,YAAY,EAAEA;AAAY,GAEvBpB,YAAY,CAAC,CAAC,CAClB;AACD;AACAkC,OAAO,CAACC,GAAG,CAACN,MAAM,CAAC;AACnB,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}