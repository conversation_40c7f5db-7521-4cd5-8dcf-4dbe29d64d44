{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createBlock as _createBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestAdjustReview\"\n};\nvar _hoisted_2 = {\n  class: \"SuggestAdjustReviewBody\"\n};\nvar _hoisted_3 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_global_info_item = _resolveComponent(\"global-info-item\");\n  var _component_global_info_line = _resolveComponent(\"global-info-line\");\n  var _component_global_info = _resolveComponent(\"global-info\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_suggest_simple_select_unit = _resolveComponent(\"suggest-simple-select-unit\");\n  var _component_xyl_date_picker = _resolveComponent(\"xyl-date-picker\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"SuggestAdjustReviewNameBody\"\n  }, [_createElementVNode(\"div\", {\n    class: \"SuggestAdjustReviewName\"\n  }, [_createElementVNode(\"div\", null, \"申请调整办理单位审查\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.adjustList, function (item) {\n    return _openBlock(), _createBlock(_component_global_info, {\n      key: item.id\n    }, {\n      default: _withCtx(function () {\n        return [_createVNode(_component_global_info_line, null, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_global_info_item, {\n              label: \"申请单位\"\n            }, {\n              default: _withCtx(function () {\n                return [_createTextVNode(_toDisplayString(item.handleOfficeName), 1 /* TEXT */)];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n              label: \"申请时间\"\n            }, {\n              default: _withCtx(function () {\n                return [_createTextVNode(_toDisplayString($setup.format(item.createDate)), 1 /* TEXT */)];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n          label: \"申请调整理由\"\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"pre\", null, _toDisplayString(item.adjustReason), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n          label: \"希望办理单位\"\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"pre\", null, _toDisplayString(item.hopeHandleOffice), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), item.verifyStatus ? (_openBlock(), _createBlock(_component_global_info_item, {\n          key: 0,\n          label: \"是否同意调整申请\"\n        }, {\n          default: _withCtx(function () {\n            return [_createTextVNode(_toDisplayString(item.verifyStatus === 1 ? '同意申请' : '驳回'), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)) : _createCommentVNode(\"v-if\", true), item.verifyStatus ? (_openBlock(), _createBlock(_component_global_info_item, {\n          key: 1,\n          label: item.verifyStatus === 1 ? '同意调整意见' : '驳回理由'\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"pre\", null, _toDisplayString(item.noPassReason), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"])) : _createCommentVNode(\"v-if\", true)];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */);\n  }), 128 /* KEYED_FRAGMENT */))]), _createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"是否同意调整申请\",\n        prop: \"verifyStatus\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.verifyStatus,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.verifyStatus = $event;\n            }),\n            onChange: $setup.reviewResultChange\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[10] || (_cache[10] = [_createTextVNode(\"同意申请\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 2\n              }, {\n                default: _withCtx(function () {\n                  return _cache[11] || (_cache[11] = [_createTextVNode(\"驳回\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: $setup.form.verifyStatus === 1 ? '同意调整意见' : '驳回理由',\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.noPassReason,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.noPassReason = $event;\n            }),\n            placeholder: $setup.form.verifyStatus === 1 ? '请输入同意调整意见' : '请输入驳回理由',\n            type: \"textarea\",\n            rows: 5,\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"label\"]), $setup.form.verifyStatus === 1 ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 0\n      }, [_createVNode(_component_el_form_item, {\n        label: \"办理方式\",\n        prop: \"transactType\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.transactType,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.transactType = $event;\n            }),\n            placeholder: \"请选择办理方式\",\n            onChange: $setup.transactTypeChange,\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_option, {\n                label: \"主办/协办\",\n                value: \"main_assist\"\n              }), _createVNode(_component_el_option, {\n                label: \"分办\",\n                value: \"publish\"\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.form.transactType === 'main_assist' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"主办单位\",\n        prop: \"mainHandleOfficeId\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_suggest_simple_select_unit, {\n            modelValue: $setup.form.mainHandleOfficeId,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.mainHandleOfficeId = $event;\n            }),\n            filterId: $setup.form.handleOfficeIds,\n            max: 1\n          }, null, 8 /* PROPS */, [\"modelValue\", \"filterId\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.form.transactType === 'main_assist' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"协办单位\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_suggest_simple_select_unit, {\n            modelValue: $setup.form.handleOfficeIds,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.handleOfficeIds = $event;\n            }),\n            filterId: $setup.form.mainHandleOfficeId\n          }, null, 8 /* PROPS */, [\"modelValue\", \"filterId\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.form.transactType === 'publish' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 2,\n        label: \"分办单位\",\n        prop: \"handleOfficeIds\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_suggest_simple_select_unit, {\n            modelValue: $setup.form.handleOfficeIds,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.form.handleOfficeIds = $event;\n            })\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n        class: \"zy-el-form-item-br\"\n      }, null, -1 /* HOISTED */)), !$setup.isPreAssign ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 3\n      }, [_createVNode(_component_el_form_item, {\n        label: \"答复截止时间\",\n        prop: \"answerStopDate\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_date_picker, {\n            modelValue: $setup.form.answerStopDate,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n              return $setup.form.answerStopDate = $event;\n            }),\n            type: \"datetime\",\n            \"value-format\": \"x\",\n            placeholder: \"请选择答复截止时间\",\n            \"disabled-date\": $setup.disabledDate\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"调整截止时间\",\n        prop: \"adjustStopDate\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_date_picker, {\n            modelValue: $setup.form.adjustStopDate,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n              return $setup.form.adjustStopDate = $event;\n            }),\n            type: \"datetime\",\n            \"value-format\": \"x\",\n            placeholder: \"请选择调整截止时间\",\n            \"disabled-date\": $setup.disabledDate\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      })], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), $setup.isPreAssign ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 4,\n        label: \"签收截止时间\",\n        prop: \"confirmStopDate\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_date_picker, {\n            modelValue: $setup.form.confirmStopDate,\n            \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n              return $setup.form.confirmStopDate = $event;\n            }),\n            type: \"datetime\",\n            \"value-format\": \"x\",\n            placeholder: \"请选择签收截止时间\",\n            \"disabled-date\": $setup.disabledDate\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[9] || (_cache[9] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[13] || (_cache[13] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[14] || (_cache[14] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$setup", "adjustList", "item", "_createBlock", "_component_global_info", "key", "id", "default", "_withCtx", "_createVNode", "_component_global_info_line", "_component_global_info_item", "label", "_createTextVNode", "_toDisplayString", "handleOfficeName", "_", "format", "createDate", "adjustReason", "hopeHandleOffice", "verifyStatus", "_createCommentVNode", "noPassReason", "_component_el_form", "ref", "model", "form", "rules", "inline", "_component_el_form_item", "prop", "_component_el_radio_group", "modelValue", "_cache", "$event", "onChange", "reviewResultChange", "_component_el_radio", "_component_el_input", "placeholder", "type", "rows", "clearable", "_component_el_select", "transactType", "transactTypeChange", "_component_el_option", "value", "_component_suggest_simple_select_unit", "mainHandleOfficeId", "filterId", "handleOfficeIds", "max", "isPreAssign", "_component_xyl_date_picker", "answerStopDate", "disabledDate", "adjustStopDate", "confirmStopDate", "_hoisted_3", "_component_el_button", "onClick", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\component\\SuggestAdjustReview.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestAdjustReview\">\r\n    <div class=\"SuggestAdjustReviewNameBody\">\r\n      <div class=\"SuggestAdjustReviewName\">\r\n        <div>申请调整办理单位审查</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"SuggestAdjustReviewBody\">\r\n      <global-info v-for=\"item in adjustList\" :key=\"item.id\">\r\n        <global-info-line>\r\n          <global-info-item label=\"申请单位\">{{ item.handleOfficeName }}</global-info-item>\r\n          <global-info-item label=\"申请时间\">{{ format(item.createDate) }}</global-info-item>\r\n        </global-info-line>\r\n        <global-info-item label=\"申请调整理由\">\r\n          <pre>{{ item.adjustReason }}</pre>\r\n        </global-info-item>\r\n        <global-info-item label=\"希望办理单位\">\r\n          <pre>{{ item.hopeHandleOffice }}</pre>\r\n        </global-info-item>\r\n        <global-info-item v-if=\"item.verifyStatus\" label=\"是否同意调整申请\">\r\n          {{ item.verifyStatus === 1 ? '同意申请' : '驳回' }}\r\n        </global-info-item>\r\n        <global-info-item v-if=\"item.verifyStatus\" :label=\"item.verifyStatus === 1 ? '同意调整意见' : '驳回理由'\">\r\n          <pre>{{ item.noPassReason }}</pre>\r\n        </global-info-item>\r\n      </global-info>\r\n    </div>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"是否同意调整申请\" prop=\"verifyStatus\" class=\"globalFormTitle\">\r\n        <el-radio-group v-model=\"form.verifyStatus\" @change=\"reviewResultChange\">\r\n          <el-radio :label=\"1\">同意申请</el-radio>\r\n          <el-radio :label=\"2\">驳回</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item :label=\"form.verifyStatus === 1 ? '同意调整意见' : '驳回理由'\" class=\"globalFormTitle\">\r\n        <el-input\r\n          v-model=\"form.noPassReason\"\r\n          :placeholder=\"form.verifyStatus === 1 ? '请输入同意调整意见' : '请输入驳回理由'\"\r\n          type=\"textarea\"\r\n          :rows=\"5\"\r\n          clearable />\r\n      </el-form-item>\r\n      <template v-if=\"form.verifyStatus === 1\">\r\n        <el-form-item label=\"办理方式\" prop=\"transactType\">\r\n          <el-select v-model=\"form.transactType\" placeholder=\"请选择办理方式\" @change=\"transactTypeChange\" clearable>\r\n            <el-option label=\"主办/协办\" value=\"main_assist\" />\r\n            <el-option label=\"分办\" value=\"publish\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <template v-if=\"form.transactType === 'main_assist'\">\r\n          <el-form-item label=\"主办单位\" prop=\"mainHandleOfficeId\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit\r\n              v-model=\"form.mainHandleOfficeId\"\r\n              :filterId=\"form.handleOfficeIds\"\r\n              :max=\"1\"></suggest-simple-select-unit>\r\n          </el-form-item>\r\n        </template>\r\n        <template v-if=\"form.transactType === 'main_assist'\">\r\n          <el-form-item label=\"协办单位\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit\r\n              v-model=\"form.handleOfficeIds\"\r\n              :filterId=\"form.mainHandleOfficeId\"></suggest-simple-select-unit>\r\n          </el-form-item>\r\n        </template>\r\n        <template v-if=\"form.transactType === 'publish'\">\r\n          <el-form-item label=\"分办单位\" prop=\"handleOfficeIds\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"></suggest-simple-select-unit>\r\n          </el-form-item>\r\n        </template>\r\n        <div class=\"zy-el-form-item-br\"></div>\r\n        <template v-if=\"!isPreAssign\">\r\n          <el-form-item label=\"答复截止时间\" prop=\"answerStopDate\">\r\n            <xyl-date-picker\r\n              v-model=\"form.answerStopDate\"\r\n              type=\"datetime\"\r\n              value-format=\"x\"\r\n              placeholder=\"请选择答复截止时间\"\r\n              :disabled-date=\"disabledDate\"></xyl-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"调整截止时间\" prop=\"adjustStopDate\">\r\n            <xyl-date-picker\r\n              v-model=\"form.adjustStopDate\"\r\n              type=\"datetime\"\r\n              value-format=\"x\"\r\n              placeholder=\"请选择调整截止时间\"\r\n              :disabled-date=\"disabledDate\"></xyl-date-picker>\r\n          </el-form-item>\r\n        </template>\r\n        <template v-if=\"isPreAssign\">\r\n          <el-form-item label=\"签收截止时间\" prop=\"confirmStopDate\">\r\n            <xyl-date-picker\r\n              v-model=\"form.confirmStopDate\"\r\n              type=\"datetime\"\r\n              value-format=\"x\"\r\n              placeholder=\"请选择签收截止时间\"\r\n              :disabled-date=\"disabledDate\"></xyl-date-picker>\r\n          </el-form-item>\r\n        </template>\r\n      </template>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestAdjustReview' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated, watch } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  transactObj: { type: Object, default: () => ({}) }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  verifyStatus: 1,\r\n  noPassReason: '', // 交办意见\r\n  transactType: '', // 请选择办理方式\r\n  mainHandleOfficeId: [],\r\n  handleOfficeIds: [],\r\n  answerStopDate: '',\r\n  adjustStopDate: '',\r\n  confirmStopDate: ''\r\n})\r\nconst rules = reactive({\r\n  verifyStatus: [{ required: true, message: '请选择是否同意调整申请', trigger: ['blur', 'change'] }],\r\n  transactType: [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }],\r\n  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],\r\n  handleOfficeIds: [{ type: 'array', required: false, message: '请选择协办单位', trigger: ['blur', 'change'] }],\r\n  answerStopDate: [{ required: true, message: '请选择答复截止时间', trigger: ['blur', 'change'] }],\r\n  adjustStopDate: [{ required: true, message: '请选择调整截止时间', trigger: ['blur', 'change'] }],\r\n  confirmStopDate: [{ required: true, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]\r\n})\r\nconst disabledDate = (time) => time.getTime() < Date.now() + 3600 * 1000 * 24 * 3\r\nconst adjustList = ref([])\r\nconst isPreAssign = ref(false)\r\n\r\nonActivated(() => {\r\n  globalReadConfig()\r\n  handingPortionAdjustList()\r\n})\r\n\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: ['proposal_enable_pre_assign', 'SuggestSignTime'] })\r\n  if (data.proposal_enable_pre_assign) {\r\n    isPreAssign.value = Boolean(data?.proposal_enable_pre_assign)\r\n  }\r\n  if (data.SuggestSignTime) {\r\n    form.confirmStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestSignTime)\r\n  }\r\n}\r\n\r\nconst handingPortionAdjustList = async () => {\r\n  const res = await api.handingPortionAdjustList({ query: { suggestionId: props.id } })\r\n  var { data } = res\r\n  adjustList.value = data.filter((v) => !v.verifyStatus)\r\n}\r\n\r\nconst reviewResultChange = () => {\r\n  rules.transactType = [{ required: false, message: '请选择办理方式', trigger: ['blur', 'change'] }]\r\n  rules.answerStopDate = [{ required: false, message: '请选择答复截止时间', trigger: ['blur', 'change'] }]\r\n  rules.adjustStopDate = [{ required: false, message: '请选择调整截止时间', trigger: ['blur', 'change'] }]\r\n  rules.confirmStopDate = [{ required: false, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]\r\n  if (form.verifyStatus === 1) {\r\n    rules.transactType = [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }]\r\n    rules.answerStopDate = [{ required: true, message: '请选择答复截止时间', trigger: ['blur', 'change'] }]\r\n    rules.adjustStopDate = [{ required: true, message: '请选择调整截止时间', trigger: ['blur', 'change'] }]\r\n    rules.confirmStopDate = [{ required: true, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst transactTypeChange = () => {\r\n  if (form.transactType === 'main_assist') {\r\n    rules.mainHandleOfficeId = [\r\n      { type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }\r\n    ]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.transactType === 'publish') {\r\n    rules.mainHandleOfficeId = [\r\n      { type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }\r\n    ]\r\n    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.mainHandleOfficeId = [\r\n      { type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }\r\n    ]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      globalJson()\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\n\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/cppcc/handingPortionAdjust/verify', {\r\n    suggestionId: props.id,\r\n    verifyStatus: form.verifyStatus,\r\n    noPassReason: form.noPassReason,\r\n    handleOfficeType: form.verifyStatus === 1 ? form.transactType : null, // 办理方式\r\n    mainHandleOfficeId: form.verifyStatus === 1 ? form.mainHandleOfficeId.join('') : null, // 主办单位\r\n    handleOfficeIds: form.verifyStatus === 1 ? form.handleOfficeIds : null, // 协办或分办单位\r\n    answerStopDate: form.verifyStatus === 1 ? form.answerStopDate : null,\r\n    adjustStopDate: form.verifyStatus === 1 ? form.adjustStopDate : null,\r\n    confirmStopDate: form.verifyStatus === 1 ? form.confirmStopDate : null\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '交办成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\nwatch(\r\n  () => props.transactObj,\r\n  () => {\r\n    if (form.transactType === '') {\r\n      if (props.transactObj.transactType) {\r\n        form.transactType = props.transactObj.transactType\r\n        form.mainHandleOfficeId = props.transactObj.mainHandleOfficeId\r\n        form.handleOfficeIds = props.transactObj.handleOfficeIds\r\n        transactTypeChange()\r\n      }\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestAdjustReview {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SuggestAdjustReviewNameBody {\r\n    padding: 0 var(--zy-distance-one);\r\n    padding-top: var(--zy-distance-one);\r\n\r\n    .SuggestAdjustReviewName {\r\n      width: 100%;\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      position: relative;\r\n      text-align: center;\r\n\r\n      div {\r\n        display: inline-block;\r\n        background-color: #fff;\r\n        position: relative;\r\n        z-index: 2;\r\n        padding: 0 20px;\r\n      }\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 100%;\r\n        height: 1px;\r\n        background-color: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .SuggestAdjustReviewBody {\r\n    padding: var(--zy-distance-one);\r\n    padding-bottom: 0;\r\n\r\n    .global-info {\r\n      padding-bottom: 12px;\r\n\r\n      .global-info-item {\r\n        .global-info-label {\r\n          width: 160px;\r\n        }\r\n\r\n        .global-info-content {\r\n          width: calc(100% - 160px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest-simple-select-unit {\r\n    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;\r\n    border-radius: var(--zy-el-input-border-radius, var(--zy-el-border-radius-base));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAMzBA,KAAK,EAAC;AAAyB;;EA4F7BA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;uBAlGjCC,mBAAA,CAuGM,OAvGNC,UAuGM,G,4BAtGJC,mBAAA,CAIM;IAJDH,KAAK,EAAC;EAA6B,IACtCG,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAyB,IAClCG,mBAAA,CAAqB,aAAhB,YAAU,E,wBAGnBA,mBAAA,CAmBM,OAnBNC,UAmBM,I,kBAlBJH,mBAAA,CAiBcI,SAAA,QAzBpBC,WAAA,CAQkCC,MAAA,CAAAC,UAAU,EAR5C,UAQ0BC,IAAI;yBAAxBC,YAAA,CAiBcC,sBAAA;MAjB2BC,GAAG,EAAEH,IAAI,CAACI;;MARzDC,OAAA,EAAAC,QAAA,CASQ;QAAA,OAGmB,CAHnBC,YAAA,CAGmBC,2BAAA;UAZ3BH,OAAA,EAAAC,QAAA,CAUU;YAAA,OAA6E,CAA7EC,YAAA,CAA6EE,2BAAA;cAA3DC,KAAK,EAAC;YAAM;cAVxCL,OAAA,EAAAC,QAAA,CAUyC;gBAAA,OAA2B,CAVpEK,gBAAA,CAAAC,gBAAA,CAU4CZ,IAAI,CAACa,gBAAgB,iB;;cAVjEC,CAAA;0CAWUP,YAAA,CAA+EE,2BAAA;cAA7DC,KAAK,EAAC;YAAM;cAXxCL,OAAA,EAAAC,QAAA,CAWyC;gBAAA,OAA6B,CAXtEK,gBAAA,CAAAC,gBAAA,CAW4Cd,MAAA,CAAAiB,MAAM,CAACf,IAAI,CAACgB,UAAU,kB;;cAXlEF,CAAA;;;UAAAA,CAAA;sCAaQP,YAAA,CAEmBE,2BAAA;UAFDC,KAAK,EAAC;QAAQ;UAbxCL,OAAA,EAAAC,QAAA,CAcU;YAAA,OAAkC,CAAlCZ,mBAAA,CAAkC,aAAAkB,gBAAA,CAA1BZ,IAAI,CAACiB,YAAY,iB;;UAdnCH,CAAA;sCAgBQP,YAAA,CAEmBE,2BAAA;UAFDC,KAAK,EAAC;QAAQ;UAhBxCL,OAAA,EAAAC,QAAA,CAiBU;YAAA,OAAsC,CAAtCZ,mBAAA,CAAsC,aAAAkB,gBAAA,CAA9BZ,IAAI,CAACkB,gBAAgB,iB;;UAjBvCJ,CAAA;sCAmBgCd,IAAI,CAACmB,YAAY,I,cAAzClB,YAAA,CAEmBQ,2BAAA;UArB3BN,GAAA;UAmBmDO,KAAK,EAAC;;UAnBzDL,OAAA,EAAAC,QAAA,CAoBU;YAAA,OAA6C,CApBvDK,gBAAA,CAAAC,gBAAA,CAoBaZ,IAAI,CAACmB,YAAY,uC;;UApB9BL,CAAA;wCAAAM,mBAAA,gBAsBgCpB,IAAI,CAACmB,YAAY,I,cAAzClB,YAAA,CAEmBQ,2BAAA;UAxB3BN,GAAA;UAsBoDO,KAAK,EAAEV,IAAI,CAACmB,YAAY;;UAtB5Ed,OAAA,EAAAC,QAAA,CAuBU;YAAA,OAAkC,CAAlCZ,mBAAA,CAAkC,aAAAkB,gBAAA,CAA1BZ,IAAI,CAACqB,YAAY,iB;;UAvBnCP,CAAA;0DAAAM,mBAAA,e;;MAAAN,CAAA;;oCA2BIP,YAAA,CA4EUe,kBAAA;IA5EDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAE1B,MAAA,CAAA2B,IAAI;IAAGC,KAAK,EAAE5B,MAAA,CAAA4B,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACpC,KAAK,EAAC;;IA3B1Fc,OAAA,EAAAC,QAAA,CA4BM;MAAA,OAKe,CALfC,YAAA,CAKeqB,uBAAA;QALDlB,KAAK,EAAC,UAAU;QAACmB,IAAI,EAAC,cAAc;QAACtC,KAAK,EAAC;;QA5B/Dc,OAAA,EAAAC,QAAA,CA6BQ;UAAA,OAGiB,CAHjBC,YAAA,CAGiBuB,yBAAA;YAhCzBC,UAAA,EA6BiCjC,MAAA,CAAA2B,IAAI,CAACN,YAAY;YA7BlD,uBAAAa,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA6BiCnC,MAAA,CAAA2B,IAAI,CAACN,YAAY,GAAAc,MAAA;YAAA;YAAGC,QAAM,EAAEpC,MAAA,CAAAqC;;YA7B7D9B,OAAA,EAAAC,QAAA,CA8BU;cAAA,OAAoC,CAApCC,YAAA,CAAoC6B,mBAAA;gBAAzB1B,KAAK,EAAE;cAAC;gBA9B7BL,OAAA,EAAAC,QAAA,CA8B+B;kBAAA,OAAI0B,MAAA,SAAAA,MAAA,QA9BnCrB,gBAAA,CA8B+B,MAAI,E;;gBA9BnCG,CAAA;kBA+BUP,YAAA,CAAkC6B,mBAAA;gBAAvB1B,KAAK,EAAE;cAAC;gBA/B7BL,OAAA,EAAAC,QAAA,CA+B+B;kBAAA,OAAE0B,MAAA,SAAAA,MAAA,QA/BjCrB,gBAAA,CA+B+B,IAAE,E;;gBA/BjCG,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;UAkCMP,YAAA,CAOeqB,uBAAA;QAPAlB,KAAK,EAAEZ,MAAA,CAAA2B,IAAI,CAACN,YAAY;QAA4B5B,KAAK,EAAC;;QAlC/Ec,OAAA,EAAAC,QAAA,CAmCQ;UAAA,OAKc,CALdC,YAAA,CAKc8B,mBAAA;YAxCtBN,UAAA,EAoCmBjC,MAAA,CAAA2B,IAAI,CAACJ,YAAY;YApCpC,uBAAAW,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoCmBnC,MAAA,CAAA2B,IAAI,CAACJ,YAAY,GAAAY,MAAA;YAAA;YACzBK,WAAW,EAAExC,MAAA,CAAA2B,IAAI,CAACN,YAAY;YAC/BoB,IAAI,EAAC,UAAU;YACdC,IAAI,EAAE,CAAC;YACRC,SAAS,EAAT;;;QAxCV3B,CAAA;oCA0CsBhB,MAAA,CAAA2B,IAAI,CAACN,YAAY,U,cAAjC3B,mBAAA,CAwDWI,SAAA;QAlGjBO,GAAA;MAAA,IA2CQI,YAAA,CAKeqB,uBAAA;QALDlB,KAAK,EAAC,MAAM;QAACmB,IAAI,EAAC;;QA3CxCxB,OAAA,EAAAC,QAAA,CA4CU;UAAA,OAGY,CAHZC,YAAA,CAGYmC,oBAAA;YA/CtBX,UAAA,EA4C8BjC,MAAA,CAAA2B,IAAI,CAACkB,YAAY;YA5C/C,uBAAAX,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA4C8BnC,MAAA,CAAA2B,IAAI,CAACkB,YAAY,GAAAV,MAAA;YAAA;YAAEK,WAAW,EAAC,SAAS;YAAEJ,QAAM,EAAEpC,MAAA,CAAA8C,kBAAkB;YAAEH,SAAS,EAAT;;YA5CpGpC,OAAA,EAAAC,QAAA,CA6CY;cAAA,OAA+C,CAA/CC,YAAA,CAA+CsC,oBAAA;gBAApCnC,KAAK,EAAC,OAAO;gBAACoC,KAAK,EAAC;kBAC/BvC,YAAA,CAAwCsC,oBAAA;gBAA7BnC,KAAK,EAAC,IAAI;gBAACoC,KAAK,EAAC;;;YA9CxChC,CAAA;;;QAAAA,CAAA;UAiDwBhB,MAAA,CAAA2B,IAAI,CAACkB,YAAY,sB,cAC/B1C,YAAA,CAKe2B,uBAAA;QAvDzBzB,GAAA;QAkDwBO,KAAK,EAAC,MAAM;QAACmB,IAAI,EAAC,oBAAoB;QAACtC,KAAK,EAAC;;QAlDrEc,OAAA,EAAAC,QAAA,CAmDY;UAAA,OAGwC,CAHxCC,YAAA,CAGwCwC,qCAAA;YAtDpDhB,UAAA,EAoDuBjC,MAAA,CAAA2B,IAAI,CAACuB,kBAAkB;YApD9C,uBAAAhB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoDuBnC,MAAA,CAAA2B,IAAI,CAACuB,kBAAkB,GAAAf,MAAA;YAAA;YAC/BgB,QAAQ,EAAEnD,MAAA,CAAA2B,IAAI,CAACyB,eAAe;YAC9BC,GAAG,EAAE;;;QAtDpBrC,CAAA;YAAAM,mBAAA,gBAyDwBtB,MAAA,CAAA2B,IAAI,CAACkB,YAAY,sB,cAC/B1C,YAAA,CAIe2B,uBAAA;QA9DzBzB,GAAA;QA0DwBO,KAAK,EAAC,MAAM;QAACnB,KAAK,EAAC;;QA1D3Cc,OAAA,EAAAC,QAAA,CA2DY;UAAA,OAEmE,CAFnEC,YAAA,CAEmEwC,qCAAA;YA7D/EhB,UAAA,EA4DuBjC,MAAA,CAAA2B,IAAI,CAACyB,eAAe;YA5D3C,uBAAAlB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA4DuBnC,MAAA,CAAA2B,IAAI,CAACyB,eAAe,GAAAjB,MAAA;YAAA;YAC5BgB,QAAQ,EAAEnD,MAAA,CAAA2B,IAAI,CAACuB;;;QA7D9BlC,CAAA;YAAAM,mBAAA,gBAgEwBtB,MAAA,CAAA2B,IAAI,CAACkB,YAAY,kB,cAC/B1C,YAAA,CAEe2B,uBAAA;QAnEzBzB,GAAA;QAiEwBO,KAAK,EAAC,MAAM;QAACmB,IAAI,EAAC,iBAAiB;QAACtC,KAAK,EAAC;;QAjElEc,OAAA,EAAAC,QAAA,CAkEY;UAAA,OAAwF,CAAxFC,YAAA,CAAwFwC,qCAAA;YAlEpGhB,UAAA,EAkEiDjC,MAAA,CAAA2B,IAAI,CAACyB,eAAe;YAlErE,uBAAAlB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAkEiDnC,MAAA,CAAA2B,IAAI,CAACyB,eAAe,GAAAjB,MAAA;YAAA;;;QAlErEnB,CAAA;YAAAM,mBAAA,gB,4BAqEQ1B,mBAAA,CAAsC;QAAjCH,KAAK,EAAC;MAAoB,6B,CACdO,MAAA,CAAAsD,WAAW,I,cAA5B5D,mBAAA,CAiBWI,SAAA;QAvFnBO,GAAA;MAAA,IAuEUI,YAAA,CAOeqB,uBAAA;QAPDlB,KAAK,EAAC,QAAQ;QAACmB,IAAI,EAAC;;QAvE5CxB,OAAA,EAAAC,QAAA,CAwEY;UAAA,OAKkD,CALlDC,YAAA,CAKkD8C,0BAAA;YA7E9DtB,UAAA,EAyEuBjC,MAAA,CAAA2B,IAAI,CAAC6B,cAAc;YAzE1C,uBAAAtB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAyEuBnC,MAAA,CAAA2B,IAAI,CAAC6B,cAAc,GAAArB,MAAA;YAAA;YAC5BM,IAAI,EAAC,UAAU;YACf,cAAY,EAAC,GAAG;YAChBD,WAAW,EAAC,WAAW;YACtB,eAAa,EAAExC,MAAA,CAAAyD;;;QA7E9BzC,CAAA;UA+EUP,YAAA,CAOeqB,uBAAA;QAPDlB,KAAK,EAAC,QAAQ;QAACmB,IAAI,EAAC;;QA/E5CxB,OAAA,EAAAC,QAAA,CAgFY;UAAA,OAKkD,CALlDC,YAAA,CAKkD8C,0BAAA;YArF9DtB,UAAA,EAiFuBjC,MAAA,CAAA2B,IAAI,CAAC+B,cAAc;YAjF1C,uBAAAxB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAiFuBnC,MAAA,CAAA2B,IAAI,CAAC+B,cAAc,GAAAvB,MAAA;YAAA;YAC5BM,IAAI,EAAC,UAAU;YACf,cAAY,EAAC,GAAG;YAChBD,WAAW,EAAC,WAAW;YACtB,eAAa,EAAExC,MAAA,CAAAyD;;;QArF9BzC,CAAA;wCAAAM,mBAAA,gBAwFwBtB,MAAA,CAAAsD,WAAW,I,cACzBnD,YAAA,CAOe2B,uBAAA;QAhGzBzB,GAAA;QAyFwBO,KAAK,EAAC,QAAQ;QAACmB,IAAI,EAAC;;QAzF5CxB,OAAA,EAAAC,QAAA,CA0FY;UAAA,OAKkD,CALlDC,YAAA,CAKkD8C,0BAAA;YA/F9DtB,UAAA,EA2FuBjC,MAAA,CAAA2B,IAAI,CAACgC,eAAe;YA3F3C,uBAAAzB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA2FuBnC,MAAA,CAAA2B,IAAI,CAACgC,eAAe,GAAAxB,MAAA;YAAA;YAC7BM,IAAI,EAAC,UAAU;YACf,cAAY,EAAC,GAAG;YAChBD,WAAW,EAAC,WAAW;YACtB,eAAa,EAAExC,MAAA,CAAAyD;;;QA/F9BzC,CAAA;YAAAM,mBAAA,e,+BAAAA,mBAAA,gBAmGM1B,mBAAA,CAGM,OAHNgE,UAGM,GAFJnD,YAAA,CAAqEoD,oBAAA;QAA1DpB,IAAI,EAAC,SAAS;QAAEqB,OAAK,EAAA5B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEnC,MAAA,CAAA+D,UAAU,CAAC/D,MAAA,CAAAgE,OAAO;QAAA;;QApG5DzD,OAAA,EAAAC,QAAA,CAoG+D;UAAA,OAAE0B,MAAA,SAAAA,MAAA,QApGjErB,gBAAA,CAoG+D,IAAE,E;;QApGjEG,CAAA;UAqGQP,YAAA,CAA4CoD,oBAAA;QAAhCC,OAAK,EAAE9D,MAAA,CAAAiE;MAAS;QArGpC1D,OAAA,EAAAC,QAAA,CAqGsC;UAAA,OAAE0B,MAAA,SAAAA,MAAA,QArGxCrB,gBAAA,CAqGsC,IAAE,E;;QArGxCG,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}