{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SatisfactionSurvey\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"survey-content\"\n};\nvar _hoisted_3 = {\n  key: 0,\n  class: \"survey-question\"\n};\nvar _hoisted_4 = {\n  class: \"survey-text\"\n};\nvar _hoisted_5 = {\n  key: 1,\n  class: \"survey-answer\"\n};\nvar _hoisted_6 = {\n  class: \"survey-text\"\n};\nvar _hoisted_7 = {\n  class: \"survey-faces\"\n};\nvar _hoisted_8 = [\"onClick\"];\nvar _hoisted_9 = [\"innerHTML\"];\nvar _hoisted_10 = {\n  class: \"survey-face-label\"\n};\nvar _hoisted_11 = {\n  key: 1,\n  class: \"survey-reason-title\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  var _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"survey-header\"\n  }, [_createElementVNode(\"span\", null, \"您对本次回答是否满意？\")], -1 /* HOISTED */)), _createCommentVNode(\" 显示对话内容 \"), $props.data.question || $props.data.answer ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [$props.data.question ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"survey-label\"\n  }, \"问题：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, _toDisplayString($props.data.question), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $props.data.answer ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"survey-label\"\n  }, \"回答：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($props.data.answer), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.faces, function (item, idx) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: item.id,\n      class: _normalizeClass(['survey-face', {\n        active: $setup.satisfaction === item.id\n      }]),\n      onClick: function onClick($event) {\n        return $setup.selectSatisfaction(item.id);\n      }\n    }, [_createElementVNode(\"span\", {\n      innerHTML: item.icon\n    }, null, 8 /* PROPS */, _hoisted_9), _createElementVNode(\"div\", _hoisted_10, _toDisplayString(item.label), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_8);\n  }), 128 /* KEYED_FRAGMENT */))]), $setup.showReasons ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, \"请告诉我们具体原因（可多选，非必填）\")) : _createCommentVNode(\"v-if\", true), $setup.showReasons ? (_openBlock(), _createBlock(_component_el_checkbox_group, {\n    key: 2,\n    modelValue: $setup.reasons,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.reasons = $event;\n    }),\n    class: \"survey-reasons\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.reasonOptions, function (item) {\n        return _openBlock(), _createBlock(_component_el_checkbox, {\n          key: item.id,\n          label: item.id\n        }, {\n          default: _withCtx(function () {\n            return [_createTextVNode(_toDisplayString(item.label), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n    type: \"primary\",\n    class: \"survey-submit\",\n    disabled: !$setup.satisfaction,\n    onClick: $setup.handleSubmit\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"提交\")]);\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "$props", "data", "question", "answer", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_Fragment", "_renderList", "$setup", "faces", "item", "idx", "id", "_normalizeClass", "active", "satisfaction", "onClick", "$event", "selectSatisfaction", "innerHTML", "icon", "_hoisted_9", "_hoisted_10", "label", "_hoisted_8", "showReasons", "_hoisted_11", "_createBlock", "_component_el_checkbox_group", "modelValue", "reasons", "_cache", "default", "_withCtx", "reasonOptions", "_component_el_checkbox", "_createTextVNode", "_", "_createVNode", "_component_el_button", "type", "disabled", "handleSubmit"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalAiChat\\SatisfactionSurvey.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SatisfactionSurvey\">\r\n    <div class=\"survey-header\">\r\n      <span>您对本次回答是否满意？</span>\r\n    </div>\r\n    <!-- 显示对话内容 -->\r\n    <div v-if=\"data.question || data.answer\" class=\"survey-content\">\r\n      <div v-if=\"data.question\" class=\"survey-question\">\r\n        <div class=\"survey-label\">问题：</div>\r\n        <div class=\"survey-text\">{{ data.question }}</div>\r\n      </div>\r\n      <div v-if=\"data.answer\" class=\"survey-answer\">\r\n        <div class=\"survey-label\">回答：</div>\r\n        <div class=\"survey-text\">{{ data.answer }}</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"survey-faces\">\r\n      <div v-for=\"(item, idx) in faces\" :key=\"item.id\" :class=\"['survey-face', { active: satisfaction === item.id }]\"\r\n        @click=\"selectSatisfaction(item.id)\">\r\n        <span v-html=\"item.icon\"></span>\r\n        <div class=\"survey-face-label\">{{ item.label }}</div>\r\n      </div>\r\n    </div>\r\n    <div v-if=\"showReasons\" class=\"survey-reason-title\">请告诉我们具体原因（可多选，非必填）</div>\r\n    <el-checkbox-group v-if=\"showReasons\" v-model=\"reasons\" class=\"survey-reasons\">\r\n      <el-checkbox v-for=\"item in reasonOptions\" :key=\"item.id\" :label=\"item.id\">{{ item.label }}</el-checkbox>\r\n    </el-checkbox-group>\r\n    <el-button type=\"primary\" class=\"survey-submit\" :disabled=\"!satisfaction\" @click=\"handleSubmit\">提交</el-button>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SatisfactionSurvey' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nimport api from '@/api'\r\nconst props = defineProps({ data: { type: Object, default: () => ({}) } })\r\nconst emit = defineEmits(['callback'])\r\nconst satisfaction = ref('')\r\nconst reasons = ref([])\r\nconst faces = ref([])\r\nconst reasonOptions = ref([])\r\n// 计算是否显示不满意原因\r\nconst showReasons = computed(() => {\r\n  return satisfaction.value === '3' ||\r\n    satisfaction.value === '4' ||\r\n    satisfaction.value === '5'\r\n})\r\nonMounted(() => {\r\n  dictionaryData()\r\n})\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['satisfaction', 'reasons_for_dissatisfaction'] })\r\n  var { data } = res\r\n\r\n  // 为满意度选项添加图标\r\n  const iconMap = {\r\n    '非常满意': '😊',\r\n    '基本满意': '🙂',\r\n    '一般': '😐',\r\n    '不满意': '🙁',\r\n    '非常不满意': '😞'\r\n  }\r\n\r\n  faces.value = data.satisfaction.map(item => ({\r\n    ...item,\r\n    value: item.id,\r\n    label: item.name,\r\n    icon: iconMap[item.name] || '😐'\r\n  }))\r\n\r\n  reasonOptions.value = data.reasons_for_dissatisfaction\r\n}\r\nconst selectSatisfaction = val => {\r\n  satisfaction.value = val\r\n  // 如果选择满意或非常满意，清空原因选择\r\n  if (val === '1' || val === '2') {\r\n    reasons.value = []\r\n  }\r\n}\r\nconst handleSubmit = async () => {\r\n  const { code } = await api.globalJson('/aiSatisfactionSurvey/add', {\r\n    form: {\r\n      title: props.data.question,\r\n      satisfaction: satisfaction.value,\r\n      reasonsForDissatisfaction: reasons.value.join(','),\r\n      content: props.data.answer || ''\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SatisfactionSurvey {\r\n  width: 588px;\r\n  padding: 32px 32px 24px 32px;\r\n\r\n  .survey-header {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    margin-bottom: 24px;\r\n    position: relative;\r\n  }\r\n\r\n  .survey-content {\r\n    margin-bottom: 20px;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    border: 1px solid #e9ecef;\r\n\r\n    .survey-question,\r\n    .survey-answer {\r\n      margin-bottom: 12px;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .survey-label {\r\n      font-weight: bold;\r\n      color: #333;\r\n      margin-bottom: 4px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .survey-text {\r\n      color: #666;\r\n      line-height: 1.5;\r\n      font-size: 14px;\r\n      word-wrap: break-word;\r\n      white-space: pre-wrap;\r\n    }\r\n  }\r\n\r\n  .survey-faces {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    margin-bottom: 18px;\r\n\r\n    .survey-face {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      cursor: pointer;\r\n      font-size: 32px;\r\n      color: #bbb;\r\n      transition: color 0.2s;\r\n\r\n      .survey-face-label {\r\n        font-size: 14px;\r\n        margin-top: 6px;\r\n        color: #666;\r\n      }\r\n\r\n      &.active {\r\n        color: #409eff;\r\n\r\n        .survey-face-label {\r\n          color: #409eff;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .survey-reason-title {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin-bottom: 10px;\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .survey-reasons {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-bottom: 18px;\r\n\r\n    .el-checkbox {\r\n      margin: 6px 0;\r\n    }\r\n  }\r\n\r\n  .survey-submit {\r\n    width: 100%;\r\n    font-size: 16px;\r\n    margin-top: 8px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EADjCC,GAAA;EAM6CD,KAAK,EAAC;;;EANnDC,GAAA;EAOgCD,KAAK,EAAC;;;EAEzBA,KAAK,EAAC;AAAa;;EAThCC,GAAA;EAW8BD,KAAK,EAAC;;;EAEvBA,KAAK,EAAC;AAAa;;EAGvBA,KAAK,EAAC;AAAc;iBAhB7B;iBAAA;;EAoBaA,KAAK,EAAC;AAAmB;;EApBtCC,GAAA;EAuB4BD,KAAK,EAAC;;;;;;uBAtBhCE,mBAAA,CA2BM,OA3BNC,UA2BM,G,0BA1BJC,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAe,IACxBI,mBAAA,CAAwB,cAAlB,aAAW,E,sBAEnBC,mBAAA,YAAe,EACJC,MAAA,CAAAC,IAAI,CAACC,QAAQ,IAAIF,MAAA,CAAAC,IAAI,CAACE,MAAM,I,cAAvCP,mBAAA,CASM,OATNQ,UASM,GAROJ,MAAA,CAAAC,IAAI,CAACC,QAAQ,I,cAAxBN,mBAAA,CAGM,OAHNS,UAGM,G,0BAFJP,mBAAA,CAAmC;IAA9BJ,KAAK,EAAC;EAAc,GAAC,KAAG,sBAC7BI,mBAAA,CAAkD,OAAlDQ,UAAkD,EAAAC,gBAAA,CAAtBP,MAAA,CAAAC,IAAI,CAACC,QAAQ,iB,KATjDH,mBAAA,gBAWiBC,MAAA,CAAAC,IAAI,CAACE,MAAM,I,cAAtBP,mBAAA,CAGM,OAHNY,UAGM,G,0BAFJV,mBAAA,CAAmC;IAA9BJ,KAAK,EAAC;EAAc,GAAC,KAAG,sBAC7BI,mBAAA,CAAgD,OAAhDW,UAAgD,EAAAF,gBAAA,CAApBP,MAAA,CAAAC,IAAI,CAACE,MAAM,iB,KAb/CJ,mBAAA,e,KAAAA,mBAAA,gBAgBID,mBAAA,CAMM,OANNY,UAMM,I,kBALJd,mBAAA,CAIMe,SAAA,QArBZC,WAAA,CAiBiCC,MAAA,CAAAC,KAAK,EAjBtC,UAiBmBC,IAAI,EAAEC,GAAG;yBAAtBpB,mBAAA,CAIM;MAJ6BD,GAAG,EAAEoB,IAAI,CAACE,EAAE;MAAGvB,KAAK,EAjB7DwB,eAAA;QAAAC,MAAA,EAiByFN,MAAA,CAAAO,YAAY,KAAKL,IAAI,CAACE;MAAE;MACxGI,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAU,kBAAkB,CAACR,IAAI,CAACE,EAAE;MAAA;QAClCnB,mBAAA,CAAgC;MAA1B0B,SAAkB,EAAVT,IAAI,CAACU;4BAnB3BC,UAAA,GAoBQ5B,mBAAA,CAAqD,OAArD6B,WAAqD,EAAApB,gBAAA,CAAnBQ,IAAI,CAACa,KAAK,iB,yBApBpDC,UAAA;oCAuBehB,MAAA,CAAAiB,WAAW,I,cAAtBlC,mBAAA,CAA4E,OAA5EmC,WAA4E,EAAxB,oBAAkB,KAvB1EhC,mBAAA,gBAwB6Bc,MAAA,CAAAiB,WAAW,I,cAApCE,YAAA,CAEoBC,4BAAA;IA1BxBtC,GAAA;IAAAuC,UAAA,EAwBmDrB,MAAA,CAAAsB,OAAO;IAxB1D,uBAAAC,MAAA,QAAAA,MAAA,gBAAAd,MAAA;MAAA,OAwBmDT,MAAA,CAAAsB,OAAO,GAAAb,MAAA;IAAA;IAAE5B,KAAK,EAAC;;IAxBlE2C,OAAA,EAAAC,QAAA,CAyBmB;MAAA,OAA6B,E,kBAA1C1C,mBAAA,CAAyGe,SAAA,QAzB/GC,WAAA,CAyBkCC,MAAA,CAAA0B,aAAa,EAzB/C,UAyB0BxB,IAAI;6BAAxBiB,YAAA,CAAyGQ,sBAAA;UAA7D7C,GAAG,EAAEoB,IAAI,CAACE,EAAE;UAAGW,KAAK,EAAEb,IAAI,CAACE;;UAzB7EoB,OAAA,EAAAC,QAAA,CAyBiF;YAAA,OAAgB,CAzBjGG,gBAAA,CAAAlC,gBAAA,CAyBoFQ,IAAI,CAACa,KAAK,iB;;UAzB9Fc,CAAA;;;;IAAAA,CAAA;uCAAA3C,mBAAA,gBA2BI4C,YAAA,CAA8GC,oBAAA;IAAnGC,IAAI,EAAC,SAAS;IAACnD,KAAK,EAAC,eAAe;IAAEoD,QAAQ,GAAGjC,MAAA,CAAAO,YAAY;IAAGC,OAAK,EAAER,MAAA,CAAAkC;;IA3BtFV,OAAA,EAAAC,QAAA,CA2BoG;MAAA,OAAEF,MAAA,QAAAA,MAAA,OA3BtGK,gBAAA,CA2BoG,IAAE,E;;IA3BtGC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}