{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalAiChatFile\",\n  ref: \"tag\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalAiChatFileWrap\"\n};\nvar _hoisted_3 = [\"onClick\"];\nvar _hoisted_4 = [\"onClick\"];\nvar _hoisted_5 = {\n  class: \"GlobalChatMessagesFileName ellipsis\"\n};\nvar _hoisted_6 = {\n  class: \"GlobalChatMessagesFileSize\"\n};\nvar _hoisted_7 = {\n  class: \"GlobalChatMessagesFileName ellipsis\"\n};\nvar _hoisted_8 = {\n  class: \"GlobalChatMessagesFileSize\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_CircleCloseFilled = _resolveComponent(\"CircleCloseFilled\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_progress = _resolveComponent(\"el-progress\");\n  var _component_DArrowLeft = _resolveComponent(\"DArrowLeft\");\n  var _component_DArrowRight = _resolveComponent(\"DArrowRight\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", {\n    class: \"GlobalAiChatFileScroll\",\n    style: _normalizeStyle($setup.scrollStyle),\n    ref: \"scroll\"\n  }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.fileData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"GlobalAiChatFileItem\",\n      key: item.id,\n      onClick: function onClick($event) {\n        return $setup.handlePreview(item);\n      }\n    }, [_createElementVNode(\"div\", {\n      class: \"GlobalAiChatFileItemClose\",\n      onClick: function onClick($event) {\n        return $setup.handleClose(item);\n      }\n    }, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(function () {\n        return [_createVNode(_component_CircleCloseFilled)];\n      }),\n      _: 1 /* STABLE */\n    })], 8 /* PROPS */, _hoisted_4), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon(item === null || item === void 0 ? void 0 : item.extName)])\n    }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString((item === null || item === void 0 ? void 0 : item.originalFileName) || '未知文件'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(item !== null && item !== void 0 && item.fileSize ? $setup.size2Str(item === null || item === void 0 ? void 0 : item.fileSize) : '0KB'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_3);\n  }), 128 /* KEYED_FRAGMENT */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.fileList, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"GlobalAiChatFileItem\",\n      key: item.uid\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon(item === null || item === void 0 ? void 0 : item.fileType)])\n    }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_7, _toDisplayString((item === null || item === void 0 ? void 0 : item.fileName) || '未知文件'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, _toDisplayString(item !== null && item !== void 0 && item.fileSize ? $setup.size2Str(item === null || item === void 0 ? void 0 : item.fileSize) : '0KB'), 1 /* TEXT */), _createVNode(_component_el_progress, {\n      percentage: item.progress,\n      \"show-text\": false,\n      \"stroke-width\": 2\n    }, null, 8 /* PROPS */, [\"percentage\"])]);\n  }), 128 /* KEYED_FRAGMENT */))], 4 /* STYLE */), $setup.prevShow ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"GlobalAiChatFilePrev\",\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.scrollClick('prev');\n    })\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_DArrowLeft)];\n    }),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true), $setup.nextShow ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"GlobalAiChatFileNext\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.scrollClick('next');\n    })\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_DArrowRight)];\n    }),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true)])], 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "style", "_normalizeStyle", "$setup", "scrollStyle", "_Fragment", "_renderList", "fileData", "item", "key", "id", "onClick", "$event", "handlePreview", "handleClose", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_CircleCloseFilled", "_", "_hoisted_4", "_normalizeClass", "fileIcon", "extName", "_hoisted_5", "_toDisplayString", "originalFileName", "_hoisted_6", "fileSize", "size2Str", "_hoisted_3", "fileList", "uid", "fileType", "_hoisted_7", "fileName", "_hoisted_8", "_component_el_progress", "percentage", "progress", "prevShow", "_cache", "scrollClick", "_component_DArrowLeft", "_createCommentVNode", "nextShow", "_component_DArrowRight"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalAiChat\\GlobalAiChatFile.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChatFile\" ref=\"tag\">\r\n    <div class=\"GlobalAiChatFileWrap\">\r\n      <div class=\"GlobalAiChatFileScroll\" :style=\"scrollStyle\" ref=\"scroll\">\r\n        <div class=\"GlobalAiChatFileItem\" v-for=\"item in fileData\" :key=\"item.id\" @click=\"handlePreview(item)\">\r\n          <div class=\"GlobalAiChatFileItemClose\" @click=\"handleClose(item)\">\r\n            <el-icon>\r\n              <CircleCloseFilled />\r\n            </el-icon>\r\n          </div>\r\n          <div class=\"globalFileIcon\" :class=\"fileIcon(item?.extName)\"></div>\r\n          <div class=\"GlobalChatMessagesFileName ellipsis\">{{ item?.originalFileName || '未知文件' }}</div>\r\n          <div class=\"GlobalChatMessagesFileSize\">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>\r\n        </div>\r\n        <div class=\"GlobalAiChatFileItem\" v-for=\"item in fileList\" :key=\"item.uid\">\r\n          <div class=\"globalFileIcon\" :class=\"fileIcon(item?.fileType)\"></div>\r\n          <div class=\"GlobalChatMessagesFileName ellipsis\">{{ item?.fileName || '未知文件' }}</div>\r\n          <div class=\"GlobalChatMessagesFileSize\">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>\r\n          <el-progress :percentage=\"item.progress\" :show-text=\"false\" :stroke-width=\"2\" />\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalAiChatFilePrev\" v-if=\"prevShow\" @click=\"scrollClick('prev')\">\r\n        <el-icon>\r\n          <DArrowLeft />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"GlobalAiChatFileNext\" v-if=\"nextShow\" @click=\"scrollClick('next')\">\r\n        <el-icon>\r\n          <DArrowRight />\r\n        </el-icon>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChatFile' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'\r\nimport { size2Str } from 'common/js/utils.js'\r\nimport { globalFileLocation } from 'common/config/location'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({\r\n  fileList: { type: Array, default: () => [] },\r\n  fileData: { type: Array, default: () => [] },\r\n  distance: { type: Number, default: 168 }\r\n})\r\nconst emit = defineEmits(['close'])\r\nconst fileList = computed(() => props.fileList)\r\nconst fileData = computed(() => props.fileData)\r\nconst fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nconst scrollStyle = computed(() => ({ transform: `translateX(${scrollLeft.value}px)` }))\r\nconst tag = ref()\r\nconst scroll = ref()\r\nconst prevShow = ref(false)\r\nconst nextShow = ref(false)\r\nconst scrollLeft = ref(0)\r\nconst scrollClick = (type) => {\r\n  const left = tag.value.offsetWidth - scroll.value.scrollWidth\r\n  if (type === 'prev') {\r\n    scrollLeft.value = scrollLeft.value + props.distance > 0 ? 0 : scrollLeft.value + props.distance\r\n  } else if (type === 'next') {\r\n    scrollLeft.value = scrollLeft.value - props.distance < left ? left : scrollLeft.value - props.distance\r\n  }\r\n  delay(() => {\r\n    prevShow.value = scrollLeft.value !== 0\r\n    nextShow.value = scrollLeft.value !== left\r\n  }, 520)\r\n}\r\nconst delay = (() => {\r\n  let timer = 0\r\n  return (callback, ms) => {\r\n    clearTimeout(timer)\r\n    timer = setTimeout(callback, ms)\r\n  }\r\n})()\r\nconst handleClose = (item) => {\r\n  emit('close', item)\r\n}\r\nconst handleResizeWindow = () => {\r\n  nextTick(() => {\r\n    if (tag.value.offsetWidth < scroll.value.offsetWidth) {\r\n      nextShow.value = true\r\n    }\r\n  })\r\n}\r\nconst handlePreview = (row) => {\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    erd.listenTo(tag.value, () => {\r\n      handleResizeWindow()\r\n    })\r\n  })\r\n  nextTick(() => {\r\n    erd.listenTo(scroll.value, () => {\r\n      handleResizeWindow()\r\n    })\r\n  })\r\n})\r\nonUnmounted(() => {\r\n  erd.uninstall(tag.value)\r\n  erd.uninstall(scroll.value)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiChatFile {\r\n  width: 100%;\r\n  background-color: #fff;\r\n  position: relative;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n\r\n  .GlobalAiChatFileWrap {\r\n    width: 100%;\r\n    overflow: hidden;\r\n\r\n    .GlobalAiChatFilePrev,\r\n    .GlobalAiChatFileNext {\r\n      position: absolute;\r\n      top: 0;\r\n      height: 100%;\r\n      width: 22px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background-color: #fff;\r\n      cursor: pointer;\r\n      z-index: 9;\r\n    }\r\n\r\n    .GlobalAiChatFilePrev {\r\n      left: 0;\r\n    }\r\n\r\n    .GlobalAiChatFileNext {\r\n      right: 0;\r\n    }\r\n\r\n    .GlobalAiChatFileScroll {\r\n      padding: 6px 12px;\r\n      white-space: nowrap;\r\n      position: relative;\r\n      transition: transform 0.3s;\r\n      float: left;\r\n      display: block;\r\n      z-index: 3;\r\n\r\n      .GlobalAiChatFileItem {\r\n        width: 180px;\r\n        height: 52px;\r\n        display: inline-flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        background: #fff;\r\n        position: relative;\r\n        padding: 0 40px 0 12px;\r\n        border-radius: var(--el-border-radius-base);\r\n        border: 1px solid var(--zy-el-border-color-light);\r\n        background: var(--zy-el-color-info-light-9);\r\n        word-wrap: break-word;\r\n        white-space: pre-wrap;\r\n        cursor: pointer;\r\n\r\n        & + .GlobalAiChatFileItem {\r\n          margin-left: 12px;\r\n        }\r\n\r\n        &:hover {\r\n          .GlobalAiChatFileItemClose {\r\n            display: inline-block;\r\n          }\r\n        }\r\n\r\n        .GlobalAiChatFileItemClose {\r\n          width: 14px;\r\n          height: 14px;\r\n          font-size: 14px;\r\n          display: none;\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          transform: translate(50%, -50%);\r\n        }\r\n\r\n        .zy-el-progress {\r\n          position: absolute;\r\n          left: 50%;\r\n          bottom: 0;\r\n          width: 100%;\r\n          transform: translateX(-50%);\r\n        }\r\n\r\n        .GlobalChatMessagesFileName {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding-bottom: 2px;\r\n        }\r\n\r\n        .GlobalChatMessagesFileSize {\r\n          color: var(--zy-el-text-color-secondary);\r\n          font-size: calc(var(--zy-text-font-size) - 2px);\r\n        }\r\n\r\n        .globalFileIcon {\r\n          width: 28px;\r\n          height: 28px;\r\n          vertical-align: middle;\r\n          position: absolute;\r\n          top: 50%;\r\n          right: 6px;\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        .globalFileUnknown {\r\n          background: url('./img/unknown.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePDF {\r\n          background: url('./img/PDF.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWord {\r\n          background: url('./img/Word.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileExcel {\r\n          background: url('./img/Excel.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePicture {\r\n          background: url('./img/picture.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileVideo {\r\n          background: url('./img/video.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileTXT {\r\n          background: url('./img/TXT.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileCompress {\r\n          background: url('./img/compress.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWPS {\r\n          background: url('./img/WPS.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePPT {\r\n          background: url('./img/PPT.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,kBAAkB;EAACC,GAAG,EAAC;;;EAC3BD,KAAK,EAAC;AAAsB;iBAFrC;iBAAA;;EAWeA,KAAK,EAAC;AAAqC;;EAC3CA,KAAK,EAAC;AAA4B;;EAIlCA,KAAK,EAAC;AAAqC;;EAC3CA,KAAK,EAAC;AAA4B;;;;;;;uBAhB/CE,mBAAA,CA+BM,OA/BNC,UA+BM,GA9BJC,mBAAA,CA6BM,OA7BNC,UA6BM,GA5BJD,mBAAA,CAiBM;IAjBDJ,KAAK,EAAC,wBAAwB;IAAEM,KAAK,EAHhDC,eAAA,CAGkDC,MAAA,CAAAC,WAAW;IAAER,GAAG,EAAC;yBAC3DC,mBAAA,CASMQ,SAAA,QAbdC,WAAA,CAIyDH,MAAA,CAAAI,QAAQ,EAJjE,UAIiDC,IAAI;yBAA7CX,mBAAA,CASM;MATDF,KAAK,EAAC,sBAAsB;MAA2Bc,GAAG,EAAED,IAAI,CAACE,EAAE;MAAGC,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAU,aAAa,CAACL,IAAI;MAAA;QAClGT,mBAAA,CAIM;MAJDJ,KAAK,EAAC,2BAA2B;MAAEgB,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAW,WAAW,CAACN,IAAI;MAAA;QAC7DO,YAAA,CAEUC,kBAAA;MARtBC,OAAA,EAAAC,QAAA,CAOc;QAAA,OAAqB,CAArBH,YAAA,CAAqBI,4BAAA,E;;MAPnCC,CAAA;wBAAAC,UAAA,GAUUtB,mBAAA,CAAmE;MAA9DJ,KAAK,EAVpB2B,eAAA,EAUqB,gBAAgB,EAASnB,MAAA,CAAAoB,QAAQ,CAACf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,OAAO;6BAC1DzB,mBAAA,CAA6F,OAA7F0B,UAA6F,EAAAC,gBAAA,CAAzC,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,gBAAgB,6BAC1E5B,mBAAA,CAAqG,OAArG6B,UAAqG,EAAAF,gBAAA,CAA1DlB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqB,QAAQ,GAAG1B,MAAA,CAAA2B,QAAQ,CAACtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,QAAQ,0B,iBAZ7FE,UAAA;qDAcQlC,mBAAA,CAKMQ,SAAA,QAnBdC,WAAA,CAcyDH,MAAA,CAAA6B,QAAQ,EAdjE,UAciDxB,IAAI;yBAA7CX,mBAAA,CAKM;MALDF,KAAK,EAAC,sBAAsB;MAA2Bc,GAAG,EAAED,IAAI,CAACyB;QACpElC,mBAAA,CAAoE;MAA/DJ,KAAK,EAfpB2B,eAAA,EAeqB,gBAAgB,EAASnB,MAAA,CAAAoB,QAAQ,CAACf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,QAAQ;6BAC3DnC,mBAAA,CAAqF,OAArFoC,UAAqF,EAAAT,gBAAA,CAAjC,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,QAAQ,6BAClErC,mBAAA,CAAqG,OAArGsC,UAAqG,EAAAX,gBAAA,CAA1DlB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqB,QAAQ,GAAG1B,MAAA,CAAA2B,QAAQ,CAACtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,QAAQ,2BACnFd,YAAA,CAAgFuB,sBAAA;MAAlEC,UAAU,EAAE/B,IAAI,CAACgC,QAAQ;MAAG,WAAS,EAAE,KAAK;MAAG,cAAY,EAAE;;mDAGvCrC,MAAA,CAAAsC,QAAQ,I,cAAhD5C,mBAAA,CAIM;IAzBZY,GAAA;IAqBWd,KAAK,EAAC,sBAAsB;IAAkBgB,OAAK,EAAA+B,MAAA,QAAAA,MAAA,gBAAA9B,MAAA;MAAA,OAAET,MAAA,CAAAwC,WAAW;IAAA;MACnE5B,YAAA,CAEUC,kBAAA;IAxBlBC,OAAA,EAAAC,QAAA,CAuBU;MAAA,OAAc,CAAdH,YAAA,CAAc6B,qBAAA,E;;IAvBxBxB,CAAA;UAAAyB,mBAAA,gBA0B8C1C,MAAA,CAAA2C,QAAQ,I,cAAhDjD,mBAAA,CAIM;IA9BZY,GAAA;IA0BWd,KAAK,EAAC,sBAAsB;IAAkBgB,OAAK,EAAA+B,MAAA,QAAAA,MAAA,gBAAA9B,MAAA;MAAA,OAAET,MAAA,CAAAwC,WAAW;IAAA;MACnE5B,YAAA,CAEUC,kBAAA;IA7BlBC,OAAA,EAAAC,QAAA,CA4BU;MAAA,OAAe,CAAfH,YAAA,CAAegC,sBAAA,E;;IA5BzB3B,CAAA;UAAAyB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}