{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestNumberingSubmit\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"FormTermYearCurrent\"\n};\nvar _hoisted_3 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_date_picker = _resolveComponent(\"xyl-date-picker\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"届次\",\n        prop: \"termYearId\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.termYearId,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.termYearId = $event;\n            }),\n            placeholder: \"请选择届次\",\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.termYearData, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.termYearId,\n                  label: item.name,\n                  value: item.termYearId\n                }, {\n                  default: _withCtx(function () {\n                    return [_createTextVNode(_toDisplayString(item.name) + \" \", 1 /* TEXT */), item.isTermYearCurrent ? (_openBlock(), _createElementBlock(\"span\", _hoisted_2, \" (当前届次)\")) : _createCommentVNode(\"v-if\", true)];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n        class: \"zy-el-form-item-br\"\n      }, null, -1 /* HOISTED */)), _createVNode(_component_el_form_item, {\n        label: \"大会编号前缀\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.meetingNumberPrefix,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.meetingNumberPrefix = $event;\n            }),\n            placeholder: \"请输入大会编号前缀\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"大会起始编号\",\n        prop: \"meetingStartNumber\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.meetingStartNumber,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.meetingStartNumber = $event;\n            }),\n            maxlength: \"10\",\n            \"show-word-limit\": \"\",\n            onInput: _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.meetingStartNumber = $setup.validNum($setup.form.meetingStartNumber);\n            }),\n            placeholder: \"请输入大会起始编号\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"平时编号前缀\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.usualNumberPrefix,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.usualNumberPrefix = $event;\n            }),\n            placeholder: \"请输入平时编号前缀\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"平时起始编号\",\n        prop: \"usualStartNumber\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.usualStartNumber,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.form.usualStartNumber = $event;\n            }),\n            maxlength: \"10\",\n            \"show-word-limit\": \"\",\n            onInput: _cache[6] || (_cache[6] = function ($event) {\n              return $setup.form.usualStartNumber = $setup.validNum($setup.form.usualStartNumber);\n            }),\n            placeholder: \"请输入平时起始编号\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"大会时间\",\n        prop: \"conventionTime\",\n        class: \"globalFormTime\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_date_picker, {\n            modelValue: $setup.form.conventionTime,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n              return $setup.form.conventionTime = $event;\n            }),\n            \"value-format\": \"x\",\n            type: \"datetimerange\",\n            \"start-placeholder\": \"请选择大会开始时间\",\n            \"end-placeholder\": \"请选择大会结束时间\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[8] || (_cache[8] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[9] || (_cache[9] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[10] || (_cache[10] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_select", "modelValue", "termYearId", "_cache", "$event", "placeholder", "clearable", "_Fragment", "_renderList", "termYearData", "item", "_createBlock", "_component_el_option", "name", "value", "_createTextVNode", "_toDisplayString", "isTermYearCurrent", "_hoisted_2", "_createCommentVNode", "_", "_createElementVNode", "_component_el_input", "meetingNumberPrefix", "meetingStartNumber", "maxlength", "onInput", "validNum", "usualNumberPrefix", "usualStartNumber", "_component_xyl_date_picker", "conventionTime", "type", "_hoisted_3", "_component_el_button", "onClick", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\component\\SuggestNumberingSubmit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestNumberingSubmit\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"届次\" prop=\"termYearId\">\r\n        <el-select v-model=\"form.termYearId\" placeholder=\"请选择届次\" clearable>\r\n          <el-option v-for=\"item in termYearData\" :key=\"item.termYearId\" :label=\"item.name\" :value=\"item.termYearId\">\r\n            <template #default>\r\n              {{ item.name }}\r\n              <span v-if=\"item.isTermYearCurrent\" class=\"FormTermYearCurrent\"> (当前届次)</span>\r\n            </template>\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <div class=\"zy-el-form-item-br\"></div>\r\n      <el-form-item label=\"大会编号前缀\">\r\n        <el-input v-model=\"form.meetingNumberPrefix\" placeholder=\"请输入大会编号前缀\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"大会起始编号\" prop=\"meetingStartNumber\">\r\n        <el-input v-model=\"form.meetingStartNumber\" maxlength=\"10\" show-word-limit\r\n          @input=\"form.meetingStartNumber = validNum(form.meetingStartNumber)\" placeholder=\"请输入大会起始编号\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"平时编号前缀\">\r\n        <el-input v-model=\"form.usualNumberPrefix\" placeholder=\"请输入平时编号前缀\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"平时起始编号\" prop=\"usualStartNumber\">\r\n        <el-input v-model=\"form.usualStartNumber\" maxlength=\"10\" show-word-limit\r\n          @input=\"form.usualStartNumber = validNum(form.usualStartNumber)\" placeholder=\"请输入平时起始编号\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"大会时间\" prop=\"conventionTime\" class=\"globalFormTime\">\r\n        <xyl-date-picker v-model=\"form.conventionTime\" value-format=\"x\" type=\"datetimerange\"\r\n          start-placeholder=\"请选择大会开始时间\" end-placeholder=\"请选择大会结束时间\" />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestNumberingSubmit' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { validNum } from 'common/js/utils.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  termYearId: '',\r\n  meetingNumberPrefix: '',\r\n  meetingStartNumber: '',\r\n  usualNumberPrefix: '',\r\n  usualStartNumber: '',\r\n  conventionTime: ''\r\n})\r\nconst rules = reactive({\r\n  termYearId: [{ required: true, message: '请选择届次', trigger: ['blur', 'change'] }],\r\n  meetingStartNumber: [{ required: true, message: '请输入大会起始编号', trigger: ['blur', 'change'] }],\r\n  usualStartNumber: [{ required: false, message: '请输入平时起始编号', trigger: ['blur', 'change'] }],\r\n  conventionTime: [{ required: true, message: '请选择大会时间', trigger: ['blur', 'change'] }]\r\n})\r\nconst termYearData = ref([])\r\n\r\nonMounted(() => {\r\n  termYearTree()\r\n  if (props.id) { suggestionTermYearInfo() }\r\n})\r\n\r\nconst termYearTree = async () => {\r\n  const res = await api.termYearTree({ termYearType: 'cppcc_member' })\r\n  var { data } = res\r\n  var array = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    array = [...array, ...data[index].children]\r\n  }\r\n  termYearData.value = array\r\n  termYearCurrent()\r\n}\r\n// 获取当前届次\r\nconst termYearCurrent = async () => {\r\n  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })\r\n  if (termYearData.value) {\r\n    termYearData.value = termYearData.value.map(v => ({ ...v, isTermYearCurrent: v.termYearId === data.id }))\r\n  }\r\n}\r\nconst suggestionTermYearInfo = async () => {\r\n  const res = await api.suggestionTermYearInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.termYearId = data.termYearId\r\n  form.meetingNumberPrefix = data.meetingNumberPrefix\r\n  form.meetingStartNumber = data.meetingStartNumber\r\n  form.usualNumberPrefix = data.usualNumberPrefix\r\n  form.usualStartNumber = data.usualStartNumber\r\n  form.conventionTime = data.meetingStartDate && data.meetingEndDate ? [data.meetingStartDate, data.meetingEndDate] : ''\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/proposalTermYear/edit' : '/proposalTermYear/add', {\r\n    form: {\r\n      termYearId: form.termYearId,\r\n      meetingNumberPrefix: form.meetingNumberPrefix,\r\n      meetingStartNumber: form.meetingStartNumber,\r\n      usualNumberPrefix: form.usualNumberPrefix,\r\n      usualStartNumber: form.usualStartNumber,\r\n      meetingStartDate: form.conventionTime ? form.conventionTime[0] : '',\r\n      meetingEndDate: form.conventionTime ? form.conventionTime[1] : ''\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestNumberingSubmit {\r\n  width: 680px;\r\n}\r\n\r\n.FormTermYearCurrent {\r\n  color: var(--zy-el-color-primary);\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EADrCC,GAAA;EAQkDD,KAAK,EAAC;;;EAwB7CA,KAAK,EAAC;AAAkB;;;;;;;;;uBA/BjCE,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJC,YAAA,CAkCUC,kBAAA;IAlCDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACX,KAAK,EAAC;;IAF1FY,OAAA,EAAAC,QAAA,CAGM;MAAA,OASe,CATfT,YAAA,CASeU,uBAAA;QATDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC;;QAHpCJ,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAOY,CAPZT,YAAA,CAOYa,oBAAA;YAXpBC,UAAA,EAI4BV,MAAA,CAAAC,IAAI,CAACU,UAAU;YAJ3C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAI4Bb,MAAA,CAAAC,IAAI,CAACU,UAAU,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,OAAO;YAACC,SAAS,EAAT;;YAJjEX,OAAA,EAAAC,QAAA,CAKqB;cAAA,OAA4B,E,kBAAvCX,mBAAA,CAKYsB,SAAA,QAVtBC,WAAA,CAKoCjB,MAAA,CAAAkB,YAAY,EALhD,UAK4BC,IAAI;qCAAtBC,YAAA,CAKYC,oBAAA;kBAL6B5B,GAAG,EAAE0B,IAAI,CAACR,UAAU;kBAAGJ,KAAK,EAAEY,IAAI,CAACG,IAAI;kBAAGC,KAAK,EAAEJ,IAAI,CAACR;;kBAClFP,OAAO,EAAAC,QAAA,CAChB;oBAAA,OAAe,CAP7BmB,gBAAA,CAAAC,gBAAA,CAOiBN,IAAI,CAACG,IAAI,IAAG,GACf,iBAAYH,IAAI,CAACO,iBAAiB,I,cAAlChC,mBAAA,CAA8E,QAA9EiC,UAA8E,EAAd,SAAO,KARrFC,mBAAA,e;;kBAAAC,CAAA;;;;YAAAA,CAAA;;;QAAAA,CAAA;sCAaMC,mBAAA,CAAsC;QAAjCtC,KAAK,EAAC;MAAoB,6BAC/BI,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC;MAAQ;QAdlCH,OAAA,EAAAC,QAAA,CAeQ;UAAA,OAAiF,CAAjFT,YAAA,CAAiFmC,mBAAA;YAfzFrB,UAAA,EAe2BV,MAAA,CAAAC,IAAI,CAAC+B,mBAAmB;YAfnD,uBAAApB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAe2Bb,MAAA,CAAAC,IAAI,CAAC+B,mBAAmB,GAAAnB,MAAA;YAAA;YAAEC,WAAW,EAAC,WAAW;YAACC,SAAS,EAAT;;;QAf7Ec,CAAA;UAiBMjC,YAAA,CAGeU,uBAAA;QAHDC,KAAK,EAAC,QAAQ;QAACC,IAAI,EAAC;;QAjBxCJ,OAAA,EAAAC,QAAA,CAkBQ;UAAA,OAC2G,CAD3GT,YAAA,CAC2GmC,mBAAA;YAnBnHrB,UAAA,EAkB2BV,MAAA,CAAAC,IAAI,CAACgC,kBAAkB;YAlBlD,uBAAArB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAkB2Bb,MAAA,CAAAC,IAAI,CAACgC,kBAAkB,GAAApB,MAAA;YAAA;YAAEqB,SAAS,EAAC,IAAI;YAAC,iBAAe,EAAf,EAAe;YACvEC,OAAK,EAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEb,MAAA,CAAAC,IAAI,CAACgC,kBAAkB,GAAGjC,MAAA,CAAAoC,QAAQ,CAACpC,MAAA,CAAAC,IAAI,CAACgC,kBAAkB;YAAA;YAAGnB,WAAW,EAAC,WAAW;YAACC,SAAS,EAAT;;;QAnBvGc,CAAA;UAqBMjC,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC;MAAQ;QArBlCH,OAAA,EAAAC,QAAA,CAsBQ;UAAA,OAA+E,CAA/ET,YAAA,CAA+EmC,mBAAA;YAtBvFrB,UAAA,EAsB2BV,MAAA,CAAAC,IAAI,CAACoC,iBAAiB;YAtBjD,uBAAAzB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAsB2Bb,MAAA,CAAAC,IAAI,CAACoC,iBAAiB,GAAAxB,MAAA;YAAA;YAAEC,WAAW,EAAC,WAAW;YAACC,SAAS,EAAT;;;QAtB3Ec,CAAA;UAwBMjC,YAAA,CAGeU,uBAAA;QAHDC,KAAK,EAAC,QAAQ;QAACC,IAAI,EAAC;;QAxBxCJ,OAAA,EAAAC,QAAA,CAyBQ;UAAA,OACuG,CADvGT,YAAA,CACuGmC,mBAAA;YA1B/GrB,UAAA,EAyB2BV,MAAA,CAAAC,IAAI,CAACqC,gBAAgB;YAzBhD,uBAAA1B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAyB2Bb,MAAA,CAAAC,IAAI,CAACqC,gBAAgB,GAAAzB,MAAA;YAAA;YAAEqB,SAAS,EAAC,IAAI;YAAC,iBAAe,EAAf,EAAe;YACrEC,OAAK,EAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEb,MAAA,CAAAC,IAAI,CAACqC,gBAAgB,GAAGtC,MAAA,CAAAoC,QAAQ,CAACpC,MAAA,CAAAC,IAAI,CAACqC,gBAAgB;YAAA;YAAGxB,WAAW,EAAC,WAAW;YAACC,SAAS,EAAT;;;QA1BnGc,CAAA;UA4BMjC,YAAA,CAGeU,uBAAA;QAHDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,gBAAgB;QAAChB,KAAK,EAAC;;QA5B7DY,OAAA,EAAAC,QAAA,CA6BQ;UAAA,OAC8D,CAD9DT,YAAA,CAC8D2C,0BAAA;YA9BtE7B,UAAA,EA6BkCV,MAAA,CAAAC,IAAI,CAACuC,cAAc;YA7BrD,uBAAA5B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA6BkCb,MAAA,CAAAC,IAAI,CAACuC,cAAc,GAAA3B,MAAA;YAAA;YAAE,cAAY,EAAC,GAAG;YAAC4B,IAAI,EAAC,eAAe;YAClF,mBAAiB,EAAC,WAAW;YAAC,iBAAe,EAAC;;;QA9BxDZ,CAAA;UAgCMC,mBAAA,CAGM,OAHNY,UAGM,GAFJ9C,YAAA,CAAqE+C,oBAAA;QAA1DF,IAAI,EAAC,SAAS;QAAEG,OAAK,EAAAhC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAA6C,UAAU,CAAC7C,MAAA,CAAA8C,OAAO;QAAA;;QAjC5D1C,OAAA,EAAAC,QAAA,CAiC+D;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAjCjEY,gBAAA,CAiC+D,IAAE,E;;QAjCjEK,CAAA;UAkCQjC,YAAA,CAA4C+C,oBAAA;QAAhCC,OAAK,EAAE5C,MAAA,CAAA+C;MAAS;QAlCpC3C,OAAA,EAAAC,QAAA,CAkCsC;UAAA,OAAEO,MAAA,SAAAA,MAAA,QAlCxCY,gBAAA,CAkCsC,IAAE,E;;QAlCxCK,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}