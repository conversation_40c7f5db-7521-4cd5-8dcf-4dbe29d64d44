{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, vShow as _vShow, createElementVNode as _createElementVNode, withDirectives as _withDirectives, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, withModifiers as _withModifiers, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective } from \"vue\";\nvar _hoisted_1 = [\"element-loading-spinner\", \"lement-loading-text\"];\nvar _hoisted_2 = {\n  class: \"TextRecognitionHead\"\n};\nvar _hoisted_3 = {\n  class: \"TextRecognitionButton\"\n};\nvar _hoisted_4 = {\n  class: \"TextRecognitionButtonItem\"\n};\nvar _hoisted_5 = {\n  class: \"TextRecognitionButton\"\n};\nvar _hoisted_6 = {\n  class: \"TextRecognitionButtonItem\"\n};\nvar _hoisted_7 = {\n  class: \"TextRecognitionBody\"\n};\nvar _hoisted_8 = {\n  class: \"TextRecognitionBodyLeft\"\n};\nvar _hoisted_9 = {\n  class: \"TextRecognitionUploadBody\"\n};\nvar _hoisted_10 = {\n  class: \"TextRecognitionUpload\"\n};\nvar _hoisted_11 = {\n  class: \"zy-el-upload__tip\"\n};\nvar _hoisted_12 = {\n  class: \"TextRecognitionUploadProgressInfo\"\n};\nvar _hoisted_13 = {\n  class: \"TextRecognitionUploadProgress\"\n};\nvar _hoisted_14 = {\n  class: \"TextRecognitionUploadProgressBox\"\n};\nvar _hoisted_15 = {\n  class: \"TextRecognitionUploadProgressName ellipsis\"\n};\nvar _hoisted_16 = {\n  key: 0,\n  class: \"TextRecognitionWord\"\n};\nvar _hoisted_17 = {\n  class: \"TextRecognitionBodyRight\"\n};\nvar _hoisted_18 = {\n  class: \"TextRecognitionChatBody\"\n};\nvar _hoisted_19 = {\n  class: \"TextRecognitionChatBodyEditor\"\n};\nvar _hoisted_20 = {\n  class: \"TextRecognitionChatBodyEditorBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_upload_filled = _resolveComponent(\"upload-filled\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_upload = _resolveComponent(\"el-upload\");\n  var _component_el_progress = _resolveComponent(\"el-progress\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"TextRecognition\",\n    \"element-loading-spinner\": $setup.svg,\n    \"lement-loading-text\": $setup.loadingText,\n    \"element-loading-svg-view-box\": \"-10, -10, 50, 50\"\n  }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_withDirectives(_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"重新上传\")]);\n    }),\n    _: 1 /* STABLE */\n  })], 512 /* NEED_PATCH */), [[_vShow, $setup.file.id]]), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"TextRecognitionButtonItem\"\n  }, null, -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_5, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"TextRecognitionButtonItem\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n    type: \"primary\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[4] || (_cache[4] = [_createTextVNode(\"导出\")]);\n    }),\n    _: 1 /* STABLE */\n  })])])]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_upload, {\n    drag: \"\",\n    action: \"/\",\n    \"before-upload\": $setup.handleFile,\n    \"http-request\": $setup.fileUpload,\n    \"show-file-list\": false,\n    multiple: \"\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_icon, {\n        class: \"zy-el-icon--upload\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_upload_filled)];\n        }),\n        _: 1 /* STABLE */\n      }), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"zy-el-upload__text\"\n      }, [_createTextVNode(\" 将附件拖拽至此区域，或 \"), _createElementVNode(\"em\", null, \"点击上传\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, \"仅支持\" + _toDisplayString(['jpg', 'jpeg', 'png', 'pdf'].join('、')) + \"格式\", 1 /* TEXT */)];\n    }),\n    _: 1 /* STABLE */\n  }), _withDirectives(_createElementVNode(\"div\", {\n    class: \"TextRecognitionUploadProgressBody\",\n    onClick: _cache[0] || (_cache[0] = _withModifiers(function () {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon($setup.progressType)])\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.progressText), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"TextRecognitionUploadProgressText\"\n  }, \"正在解析\", -1 /* HOISTED */))])]), _createVNode(_component_el_progress, {\n    percentage: $setup.fileProgress,\n    \"show-text\": false,\n    \"stroke-width\": 12\n  }, null, 8 /* PROPS */, [\"percentage\"])])], 512 /* NEED_PATCH */), [[_vShow, $setup.isShowProgress]])])], 512 /* NEED_PATCH */), [[_vShow, !$setup.file.id]]), $setup.file.id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [['pdf', 'doc', 'docx', 'wps', 'ofd'].includes(_ctx.fileType) ? (_openBlock(), _createBlock($setup[\"PreviewPdf\"], {\n    key: 0,\n    id: $setup.file.id,\n    type: $setup.file.extName,\n    name: $setup.file.newFileName\n  }, null, 8 /* PROPS */, [\"id\", \"type\", \"name\"])) : _createCommentVNode(\"v-if\", true), ['png', 'jpg', 'jpeg', 'gif', 'tif'].includes($setup.file.extName) ? (_openBlock(), _createBlock($setup[\"PreviewPic\"], {\n    key: 1,\n    id: $setup.file.id,\n    type: $setup.file.extName,\n    name: $setup.file.newFileName\n  }, null, 8 /* PROPS */, [\"id\", \"type\", \"name\"])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createVNode($setup[\"GlobalAiChatScroll\"], {\n    ref: \"chatScrollRef\",\n    AiChatCode: \"img_extract\",\n    chatId: $setup.chatId,\n    fileData: $setup.fileData,\n    onHandlePromptWord: $setup.handlePromptWord,\n    onHandleGuideWord: $setup.handleGuideWord,\n    onHandleRetryMessage: $setup.handleRetryMessage,\n    onHandleStreamingCallback: $setup.handleStreamingCallback,\n    onHandleSendMessageCallback: $setup.handleSendMessageCallback\n  }, null, 8 /* PROPS */, [\"chatId\", \"fileData\"]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_withDirectives(_createVNode($setup[\"GlobalAiChatFile\"], {\n    fileList: $setup.fileList,\n    fileData: $setup.fileData,\n    onClose: $setup.handleClose\n  }, null, 8 /* PROPS */, [\"fileList\", \"fileData\"]), [[_vShow, $setup.fileList.length || $setup.fileData.length]]), _createVNode($setup[\"GlobalAiChatEditor\"], {\n    ref: \"editorRef\",\n    modelValue: $setup.sendContent,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.sendContent = $event;\n    }),\n    disabled: $setup.disabled,\n    onSend: $setup.handleSendMessage,\n    onStop: $setup.handleStopMessage,\n    onUploadCallback: $setup.handleFileUpload,\n    onFileCallback: $setup.handleFileCallback\n  }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])])])])])])], 8 /* PROPS */, _hoisted_1)), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "$setup", "svg", "loadingText", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_button", "type", "onClick", "handleReset", "default", "_withCtx", "_cache", "_createTextVNode", "_", "file", "id", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_component_el_upload", "drag", "action", "handleFile", "fileUpload", "multiple", "_component_el_icon", "_component_upload_filled", "_hoisted_11", "_toDisplayString", "join", "_withModifiers", "_hoisted_12", "_hoisted_13", "_normalizeClass", "fileIcon", "progressType", "_hoisted_14", "_hoisted_15", "progressText", "_component_el_progress", "percentage", "fileProgress", "isShowProgress", "_hoisted_16", "includes", "_ctx", "fileType", "_createBlock", "extName", "name", "newFileName", "_createCommentVNode", "_hoisted_17", "_hoisted_18", "ref", "AiChatCode", "chatId", "fileData", "onHandlePromptWord", "handlePromptWord", "onHandleGuideWord", "handleGuideWord", "onHandleRetryMessage", "handleRetryMessage", "onHandleStreamingCallback", "handleStreamingCallback", "onHandleSendMessageCallback", "handleSendMessageCallback", "_hoisted_19", "_hoisted_20", "fileList", "onClose", "handleClose", "length", "modelValue", "send<PERSON><PERSON><PERSON>", "$event", "disabled", "onSend", "handleSendMessage", "onStop", "handleStopMessage", "onUploadCallback", "handleFileUpload", "onFileCallback", "handleFileCallback", "_hoisted_1", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBoxFunction\\TextRecognition\\TextRecognition.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"TextRecognition\"\r\n    v-loading=\"loading\"\r\n    :element-loading-spinner=\"svg\"\r\n    :lement-loading-text=\"loadingText\"\r\n    element-loading-svg-view-box=\"-10, -10, 50, 50\">\r\n    <div class=\"TextRecognitionHead\">\r\n      <div class=\"TextRecognitionButton\">\r\n        <div class=\"TextRecognitionButtonItem\" v-show=\"file.id\">\r\n          <el-button type=\"primary\" @click=\"handleReset\">重新上传</el-button>\r\n        </div>\r\n        <div class=\"TextRecognitionButtonItem\"></div>\r\n      </div>\r\n      <div class=\"TextRecognitionButton\">\r\n        <div class=\"TextRecognitionButtonItem\"></div>\r\n        <div class=\"TextRecognitionButtonItem\">\r\n          <el-button type=\"primary\">导出</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"TextRecognitionBody\">\r\n      <div class=\"TextRecognitionBodyLeft\">\r\n        <div class=\"TextRecognitionUploadBody\" v-show=\"!file.id\">\r\n          <div class=\"TextRecognitionUpload\">\r\n            <el-upload\r\n              drag\r\n              action=\"/\"\r\n              :before-upload=\"handleFile\"\r\n              :http-request=\"fileUpload\"\r\n              :show-file-list=\"false\"\r\n              multiple>\r\n              <el-icon class=\"zy-el-icon--upload\">\r\n                <upload-filled />\r\n              </el-icon>\r\n              <div class=\"zy-el-upload__text\">\r\n                将附件拖拽至此区域，或\r\n                <em>点击上传</em>\r\n              </div>\r\n              <div class=\"zy-el-upload__tip\">仅支持{{ ['jpg', 'jpeg', 'png', 'pdf'].join('、') }}格式</div>\r\n            </el-upload>\r\n            <div class=\"TextRecognitionUploadProgressBody\" @click.stop v-show=\"isShowProgress\">\r\n              <div class=\"TextRecognitionUploadProgressInfo\">\r\n                <div class=\"TextRecognitionUploadProgress\">\r\n                  <div class=\"globalFileIcon\" :class=\"fileIcon(progressType)\"></div>\r\n                  <div class=\"TextRecognitionUploadProgressBox\">\r\n                    <div class=\"TextRecognitionUploadProgressName ellipsis\">{{ progressText }}</div>\r\n                    <div class=\"TextRecognitionUploadProgressText\">正在解析</div>\r\n                  </div>\r\n                </div>\r\n                <el-progress :percentage=\"fileProgress\" :show-text=\"false\" :stroke-width=\"12\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"TextRecognitionWord\" v-if=\"file.id\">\r\n          <template v-if=\"['pdf', 'doc', 'docx', 'wps', 'ofd'].includes(fileType)\">\r\n            <preview-pdf :id=\"file.id\" :type=\"file.extName\" :name=\"file.newFileName\"></preview-pdf>\r\n          </template>\r\n          <template v-if=\"['png', 'jpg', 'jpeg', 'gif', 'tif'].includes(file.extName)\">\r\n            <preview-pic :id=\"file.id\" :type=\"file.extName\" :name=\"file.newFileName\"></preview-pic>\r\n          </template>\r\n        </div>\r\n      </div>\r\n      <div class=\"TextRecognitionBodyRight\">\r\n        <div class=\"TextRecognitionChatBody\">\r\n          <GlobalAiChatScroll\r\n            ref=\"chatScrollRef\"\r\n            AiChatCode=\"img_extract\"\r\n            :chatId=\"chatId\"\r\n            :fileData=\"fileData\"\r\n            @handlePromptWord=\"handlePromptWord\"\r\n            @handleGuideWord=\"handleGuideWord\"\r\n            @handleRetryMessage=\"handleRetryMessage\"\r\n            @handleStreamingCallback=\"handleStreamingCallback\"\r\n            @handleSendMessageCallback=\"handleSendMessageCallback\"></GlobalAiChatScroll>\r\n          <div class=\"TextRecognitionChatBodyEditor\">\r\n            <div class=\"TextRecognitionChatBodyEditorBody\">\r\n              <GlobalAiChatFile\r\n                :fileList=\"fileList\"\r\n                :fileData=\"fileData\"\r\n                @close=\"handleClose\"\r\n                v-show=\"fileList.length || fileData.length\" />\r\n              <GlobalAiChatEditor\r\n                ref=\"editorRef\"\r\n                v-model=\"sendContent\"\r\n                :disabled=\"disabled\"\r\n                @send=\"handleSendMessage\"\r\n                @stop=\"handleStopMessage\"\r\n                @uploadCallback=\"handleFileUpload\"\r\n                @fileCallback=\"handleFileCallback\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'TextRecognition' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick, defineAsyncComponent } from 'vue'\r\nimport { guid, svg } from '../../AiToolBox/AiToolBox.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst GlobalAiChatScroll = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatScroll.vue'))\r\nconst GlobalAiChatFile = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatFile.vue'))\r\nconst GlobalAiChatEditor = defineAsyncComponent(() => import('../../GlobalAiChat/GlobalAiChatEditor.vue'))\r\nconst PreviewPdf = defineAsyncComponent(() => import('@/components/global-file-preview/components/preview-pdf.vue'))\r\nconst PreviewPic = defineAsyncComponent(() => import('@/components/global-file-preview/components/preview-pic.vue'))\r\n\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n\r\nconst file = ref({})\r\nconst fileProgress = ref(0)\r\nconst isShowProgress = ref(false)\r\nconst progressText = ref('审查意见的补充报告.docx')\r\nconst progressType = ref('docx')\r\n\r\nconst chatScrollRef = ref()\r\nconst editorRef = ref()\r\nconst chatId = ref('')\r\nconst fileData = ref([])\r\nconst fileList = ref([])\r\nconst sendContent = ref('')\r\nconst disabled = ref(false)\r\nconst sendMessageIndex = ref(0)\r\n\r\nconst fileIcon = (name) => {\r\n  const type = name.substring(name.lastIndexOf('.') + 1) || ''\r\n  const IconClass = {\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    jpeg: 'globalFilePicture',\r\n    pdf: 'globalFilePDF'\r\n  }\r\n  return IconClass[type] || 'globalFileUnknown'\r\n}\r\n\r\nonMounted(() => {\r\n  chatId.value = guid()\r\n})\r\n\r\nconst handleReset = () => {\r\n  chatId.value = guid()\r\n  file.value = {}\r\n  editorRef.value?.handleSetFile([])\r\n  sendMessageIndex.value = 0\r\n  nextTick(() => {\r\n    chatScrollRef.value?.handleNewChat()\r\n  })\r\n}\r\n\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!fileData.value.length && !sendMessageIndex.value)\r\n    return ElMessage({ type: 'warning', message: `请先上传相关资料!` })\r\n  const fileId = fileData.value.map((v) => v.id).join(',')\r\n  const params = { question: value, attachmentIds: fileId, tool: '1960237803606405122' }\r\n  chatScrollRef.value?.handleSendMessage(value, params)\r\n}\r\nconst handlePromptWord = (data) => {\r\n  handleSendMessage(data.promptWord)\r\n}\r\nconst handleGuideWord = (data) => {\r\n  chatScrollRef.value?.handleSendMessage(data.question, data)\r\n}\r\nconst handleRetryMessage = (data) => {\r\n  fileData.value = data.fileData\r\n  handleSendMessage(data.content)\r\n}\r\nconst handleStopMessage = () => {\r\n  chatScrollRef.value?.handleStopMessage()\r\n}\r\nconst handleStreamingCallback = (data) => {\r\n  disabled.value = data\r\n}\r\nconst handleSendMessageCallback = () => {\r\n  sendMessageIndex.value += 1\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n}\r\n\r\n/**\r\n * 限制上传附件的文件类型\r\n */\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  const isShow = ['jpg', 'jpeg', 'png', 'pdf'].includes(fileType)\r\n  if (!isShow) ElMessage({ type: 'warning', message: `仅支持${['jpg', 'jpeg', 'png', 'pdf'].join('、')}格式!` })\r\n  isShowProgress.value = true\r\n  fileProgress.value = 0\r\n  progressText.value = file.name\r\n  progressType.value = fileType\r\n  return isShow\r\n}\r\n\r\nconst onUploadProgress = (progressEvent) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    fileProgress.value = parseInt(progress)\r\n  }\r\n}\r\n/**\r\n * 上传附件请求方法\r\n */\r\nconst fileUpload = (file) => {\r\n  const param = new FormData()\r\n  param.append('file', file.file)\r\n  handleGlobalUpload(param)\r\n}\r\n\r\nconst handleGlobalUpload = async (params) => {\r\n  try {\r\n    const { data } = await api.globalUpload(params, onUploadProgress, guid())\r\n    file.value = data\r\n    editorRef.value?.handleSetFile([data])\r\n    loading.value = false\r\n    isShowProgress.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n    isShowProgress.value = false\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.TextRecognition {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .TextRecognitionHead {\r\n    width: 100%;\r\n    padding: var(--zy-distance-four) 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .TextRecognitionButton {\r\n      width: 796px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      & + .TextRecognitionButton {\r\n        width: calc(100% - 828px);\r\n      }\r\n      .TextRecognitionButtonItem {\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n  .TextRecognitionBody {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2)));\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: var(--zy-distance-four);\r\n    .TextRecognitionBodyLeft {\r\n      width: 812px;\r\n      height: 100%;\r\n      .TextRecognitionUploadBody {\r\n        width: 800px;\r\n        height: 100%;\r\n        background: #fff;\r\n        .globalFileIcon {\r\n          width: 32px;\r\n          height: 32px;\r\n          vertical-align: middle;\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 0;\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        .globalFileUnknown {\r\n          background: url('../../img/file_type/unknown.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePDF {\r\n          background: url('../../img/file_type/PDF.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePicture {\r\n          background: url('../../img/file_type/picture.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .TextRecognitionUpload {\r\n          width: 100%;\r\n          padding: var(--zy-distance-two);\r\n          position: relative;\r\n\r\n          .zy-el-upload {\r\n            --zy-el-upload-dragger-padding-horizontal: 20px;\r\n            --zy-el-upload-dragger-padding-vertical: 10px;\r\n            .zy-el-upload-dragger {\r\n              height: 220px;\r\n              display: flex;\r\n              align-items: center;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n            }\r\n            .zy-el-icon {\r\n              font-size: 99px;\r\n            }\r\n            .zy-el-upload__text {\r\n              line-height: var(--zy-line-height);\r\n            }\r\n\r\n            .zy-el-upload__tip {\r\n              padding: 0 var(--zy-distance-one);\r\n              line-height: var(--zy-line-height);\r\n            }\r\n          }\r\n\r\n          .TextRecognitionUploadProgressBody {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: var(--zy-distance-two) 0;\r\n            position: absolute;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            background: rgba(255, 255, 255, 0.8);\r\n\r\n            .TextRecognitionUploadProgressInfo {\r\n              width: 460px;\r\n              .TextRecognitionUploadProgress {\r\n                width: 100%;\r\n                padding: var(--zy-distance-five) 40px;\r\n                position: relative;\r\n                .TextRecognitionUploadProgressBox {\r\n                  width: 100%;\r\n                  .TextRecognitionUploadProgressName {\r\n                    font-size: var(--zy-name-font-size);\r\n                    line-height: var(--zy-line-height);\r\n                  }\r\n                  .TextRecognitionUploadProgressText {\r\n                    color: var(--zy-el-color-primary);\r\n                    font-size: var(--zy-text-font-size);\r\n                    line-height: var(--zy-line-height);\r\n                  }\r\n                }\r\n                .TextRecognitionUploadProgressClose {\r\n                  width: 40px;\r\n                  height: 40px;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  position: absolute;\r\n                  right: 0;\r\n                  bottom: calc(var(--zy-distance-five) / 2);\r\n                  cursor: pointer;\r\n                  .zy-el-icon {\r\n                    font-size: 26px;\r\n                    &:hover {\r\n                      color: var(--zy-el-color-danger);\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .TextRecognitionWord {\r\n        width: 100%;\r\n        height: 100%;\r\n        .vue-office-pdf-wrapper {\r\n          padding: 0 !important;\r\n          canvas {\r\n            left: 0 !important;\r\n            transform: none !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .TextRecognitionBodyRight {\r\n      width: calc(100% - 820px);\r\n      height: 100%;\r\n      .TextRecognitionChatBody {\r\n        width: 100%;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        background: #fff;\r\n        .GlobalAiChatBody {\r\n          padding-top: 12px;\r\n        }\r\n        .TextRecognitionChatBodyEditor {\r\n          width: 100%;\r\n          padding: 12px;\r\n          .GlobalAiChatFileItemClose {\r\n            display: none !important;\r\n          }\r\n          .TextRecognitionChatBodyEditorBody {\r\n            width: 100%;\r\n            background: #fff;\r\n            border-radius: 8px;\r\n            box-shadow: var(--zy-el-box-shadow);\r\n            border: 1px solid var(--zy-el-border-color-lighter);\r\n            .GlobalAiChatEditorUpload {\r\n              & > div {\r\n                display: none;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";iBAAA;;EAOSA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAA2B;;EAKnCA,KAAK,EAAC;AAAuB;;EAE3BA,KAAK,EAAC;AAA2B;;EAKrCA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAuB;;EAezBA,KAAK,EAAC;AAAmB;;EAGzBA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAA+B;;EAEnCA,KAAK,EAAC;AAAkC;;EACtCA,KAAK,EAAC;AAA4C;;EA9C3EC,GAAA;EAuDaD,KAAK,EAAC;;;EASRA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAyB;;EAW7BA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAmC;;;;;;;;wCA5ExDE,mBAAA,CA+FM;IA9FJF,KAAK,EAAC,iBAAiB;IAEtB,yBAAuB,EAAEG,MAAA,CAAAC,GAAG;IAC5B,qBAAmB,EAAED,MAAA,CAAAE,WAAW;IACjC,8BAA4B,EAAC;MAC7BC,mBAAA,CAaM,OAbNC,UAaM,GAZJD,mBAAA,CAKM,OALNE,UAKM,G,gBAJJF,mBAAA,CAEM,OAFNG,UAEM,GADJC,YAAA,CAA+DC,oBAAA;IAApDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEV,MAAA,CAAAW;;IAV5CC,OAAA,EAAAC,QAAA,CAUyD;MAAA,OAAIC,MAAA,QAAAA,MAAA,OAV7DC,gBAAA,CAUyD,MAAI,E;;IAV7DC,CAAA;wCASuDhB,MAAA,CAAAiB,IAAI,CAACC,EAAE,E,6BAGtDf,mBAAA,CAA6C;IAAxCN,KAAK,EAAC;EAA2B,4B,GAExCM,mBAAA,CAKM,OALNgB,UAKM,G,0BAJJhB,mBAAA,CAA6C;IAAxCN,KAAK,EAAC;EAA2B,6BACtCM,mBAAA,CAEM,OAFNiB,UAEM,GADJb,YAAA,CAAwCC,oBAAA;IAA7BC,IAAI,EAAC;EAAS;IAjBnCG,OAAA,EAAAC,QAAA,CAiBoC;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAjBtCC,gBAAA,CAiBoC,IAAE,E;;IAjBtCC,CAAA;YAqBIb,mBAAA,CA0EM,OA1ENkB,UA0EM,GAzEJlB,mBAAA,CAyCM,OAzCNmB,UAyCM,G,gBAxCJnB,mBAAA,CA+BM,OA/BNoB,UA+BM,GA9BJpB,mBAAA,CA6BM,OA7BNqB,WA6BM,GA5BJjB,YAAA,CAeYkB,oBAAA;IAdVC,IAAI,EAAJ,EAAI;IACJC,MAAM,EAAC,GAAG;IACT,eAAa,EAAE3B,MAAA,CAAA4B,UAAU;IACzB,cAAY,EAAE5B,MAAA,CAAA6B,UAAU;IACxB,gBAAc,EAAE,KAAK;IACtBC,QAAQ,EAAR;;IA/BdlB,OAAA,EAAAC,QAAA,CAgCc;MAAA,OAEU,CAFVN,YAAA,CAEUwB,kBAAA;QAFDlC,KAAK,EAAC;MAAoB;QAhCjDe,OAAA,EAAAC,QAAA,CAiCgB;UAAA,OAAiB,CAAjBN,YAAA,CAAiByB,wBAAA,E;;QAjCjChB,CAAA;oCAmCcb,mBAAA,CAGM;QAHDN,KAAK,EAAC;MAAoB,IAnC7CkB,gBAAA,CAmC8C,eAE9B,GAAAZ,mBAAA,CAAa,YAAT,MAAI,E,sBAEVA,mBAAA,CAAuF,OAAvF8B,WAAuF,EAAxD,KAAG,GAAAC,gBAAA,+BAAiCC,IAAI,SAAQ,IAAE,gB;;IAvC/FnB,CAAA;sBAyCYb,mBAAA,CAWM;IAXDN,KAAK,EAAC,mCAAmC;IAAEa,OAAK,EAAAI,MAAA,QAAAA,MAAA,MAzCjEsB,cAAA,CAyC2D,cAAW;MACxDjC,mBAAA,CASM,OATNkC,WASM,GARJlC,mBAAA,CAMM,OANNmC,WAMM,GALJnC,mBAAA,CAAkE;IAA7DN,KAAK,EA5C5B0C,eAAA,EA4C6B,gBAAgB,EAASvC,MAAA,CAAAwC,QAAQ,CAACxC,MAAA,CAAAyC,YAAY;2BACzDtC,mBAAA,CAGM,OAHNuC,WAGM,GAFJvC,mBAAA,CAAgF,OAAhFwC,WAAgF,EAAAT,gBAAA,CAArBlC,MAAA,CAAA4C,YAAY,kB,0BACvEzC,mBAAA,CAAyD;IAApDN,KAAK,EAAC;EAAmC,GAAC,MAAI,qB,KAGvDU,YAAA,CAAgFsC,sBAAA;IAAlEC,UAAU,EAAE9C,MAAA,CAAA+C,YAAY;IAAG,WAAS,EAAE,KAAK;IAAG,cAAY,EAAE;+EATX/C,MAAA,CAAAgD,cAAc,E,wCAlBrChD,MAAA,CAAAiB,IAAI,CAACC,EAAE,E,GAgChBlB,MAAA,CAAAiB,IAAI,CAACC,EAAE,I,cAA9CnB,mBAAA,CAOM,OAPNkD,WAOM,G,qCANiDC,QAAQ,CAACC,IAAA,CAAAC,QAAQ,K,cACpEC,YAAA,CAAuFrD,MAAA;IAzDnGF,GAAA;IAyD0BoB,EAAE,EAAElB,MAAA,CAAAiB,IAAI,CAACC,EAAE;IAAGT,IAAI,EAAET,MAAA,CAAAiB,IAAI,CAACqC,OAAO;IAAGC,IAAI,EAAEvD,MAAA,CAAAiB,IAAI,CAACuC;qDAzDxEC,mBAAA,gB,qCA2D+DP,QAAQ,CAAClD,MAAA,CAAAiB,IAAI,CAACqC,OAAO,K,cACxED,YAAA,CAAuFrD,MAAA;IA5DnGF,GAAA;IA4D0BoB,EAAE,EAAElB,MAAA,CAAAiB,IAAI,CAACC,EAAE;IAAGT,IAAI,EAAET,MAAA,CAAAiB,IAAI,CAACqC,OAAO;IAAGC,IAAI,EAAEvD,MAAA,CAAAiB,IAAI,CAACuC;qDA5DxEC,mBAAA,e,KAAAA,mBAAA,e,GAgEMtD,mBAAA,CA8BM,OA9BNuD,WA8BM,GA7BJvD,mBAAA,CA4BM,OA5BNwD,WA4BM,GA3BJpD,YAAA,CAS8EP,MAAA;IAR5E4D,GAAG,EAAC,eAAe;IACnBC,UAAU,EAAC,aAAa;IACvBC,MAAM,EAAE9D,MAAA,CAAA8D,MAAM;IACdC,QAAQ,EAAE/D,MAAA,CAAA+D,QAAQ;IAClBC,kBAAgB,EAAEhE,MAAA,CAAAiE,gBAAgB;IAClCC,iBAAe,EAAElE,MAAA,CAAAmE,eAAe;IAChCC,oBAAkB,EAAEpE,MAAA,CAAAqE,kBAAkB;IACtCC,yBAAuB,EAAEtE,MAAA,CAAAuE,uBAAuB;IAChDC,2BAAyB,EAAExE,MAAA,CAAAyE;mDAC9BtE,mBAAA,CAgBM,OAhBNuE,WAgBM,GAfJvE,mBAAA,CAcM,OAdNwE,WAcM,G,gBAbJpE,YAAA,CAIgDP,MAAA;IAH7C4E,QAAQ,EAAE5E,MAAA,CAAA4E,QAAQ;IAClBb,QAAQ,EAAE/D,MAAA,CAAA+D,QAAQ;IAClBc,OAAK,EAAE7E,MAAA,CAAA8E;+DACA9E,MAAA,CAAA4E,QAAQ,CAACG,MAAM,IAAI/E,MAAA,CAAA+D,QAAQ,CAACgB,MAAM,E,GAC5CxE,YAAA,CAOuCP,MAAA;IANrC4D,GAAG,EAAC,WAAW;IApF/BoB,UAAA,EAqFyBhF,MAAA,CAAAiF,WAAW;IArFpC,uBAAAnE,MAAA,QAAAA,MAAA,gBAAAoE,MAAA;MAAA,OAqFyBlF,MAAA,CAAAiF,WAAW,GAAAC,MAAA;IAAA;IACnBC,QAAQ,EAAEnF,MAAA,CAAAmF,QAAQ;IAClBC,MAAI,EAAEpF,MAAA,CAAAqF,iBAAiB;IACvBC,MAAI,EAAEtF,MAAA,CAAAuF,iBAAiB;IACvBC,gBAAc,EAAExF,MAAA,CAAAyF,gBAAgB;IAChCC,cAAY,EAAE1F,MAAA,CAAA2F;iFA1F/BC,UAAA,K,qBAGe5F,MAAA,CAAA6F,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}