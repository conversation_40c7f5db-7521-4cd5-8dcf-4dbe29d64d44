<template>
  <div class="SubmitReply" :style="'width:' + props.width">
    <div class="SuggestAssignDetailNameBody" v-if="props.name">
      <div class="SuggestAssignDetailName">
        <div>{{ props.name }}</div>
      </div>
    </div>
    <el-form ref="formRef" :model="form" inline :rules="rules" class="globalForm">
      <el-form-item label="提交人" label-width="100" v-if="!props.name" class="globalFormTitle">
        <span>{{ details.submitUserName }}</span>
        <span>{{ format(details.submitDate, 'YYYY-MM-DD') }}</span>
      </el-form-item>
      <el-form-item label="标题" v-if="!props.name" label-width="100" class="globalFormTitle">
        {{ details.title }}
      </el-form-item>
      <el-form-item label="历史回复" label-width="100" class="globalFormTitle"
        v-if="details.replyList && details.replyList.length > 0 && !replyId && !props.name">
        <div v-for="item in details.replyList" :key="item.id">
          <span class="bold">阶段反馈</span>
          <span>{{ item.opinion }}</span>
        </div>
      </el-form-item>
      <el-form-item label="回复选项" label-width="100" class="globalFormTitle" v-if="false">
        <el-radio-group v-model="form.handleType" :disabled="disabled">
          <el-radio :label="1" size="large">阶段反馈</el-radio>
          <el-radio :label="2" size="large">最终结果</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="模板下载" label-width="100" class="globalFormTitle">
        <div>协商成果办理情况反馈表</div>
      </el-form-item>
      <el-form-item label="回复内容" label-width="100" class="globalFormTitle" prop="opinion">
        <el-input v-model="form.opinion" type="textarea" maxlength="800" :rows="4" placeholder="请输入回复内容" clearable />
      </el-form-item>
      <el-form-item label="上传附件" label-width="100" class="globalFormTitle">
        <xyl-upload-file @fileUpload="fileUpload" :fileData="fileData"></xyl-upload-file>
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default { name: "SubmitReply" }
</script>
<script setup>
import api from '@/api'
import { reactive, onMounted, ref, computed } from 'vue'
import { format } from 'common/js/time.js'
import { ElMessage } from 'element-plus'
import { useStore } from "vuex";
const props = defineProps({
  id: { type: String, default: '' },
  userType: { type: String, default: '' },
  replyId: { type: String, default: '' },
  name: { type: String, default: '' },
  disabled: { type: Boolean, default: false },
  width: { type: String, default: '800px' }
})
const emit = defineEmits(['callback'])
const store = useStore()
const formRef = ref()
const user = computed(() => store.getters.getUserFn)
const form = reactive({
  opinion: '', // 意见
  handleType: 2, // 审核
  spareDict: ''
})
const details = ref({})
const fileData = ref([])
const rules = reactive({
  spareDict: [{ required: true, message: '请选择理由', trigger: ['blur', 'change'] }],
  opinion: [{ required: true, message: '请输入回复内容', trigger: ['blur', 'change'] }]
})
onMounted(() => { if (props.id) { microAdviceInfo() } })

const microAdviceInfo = async () => {
  const res = await api.microAdviceInfo({ detailId: props.id })
  var { data } = res
  details.value = data
  if (props.replyId) {
    let info = data.replyList.filter((item) => item.id === props.replyId)
    form.opinion = info[0].opinion
    form.handleType = info[0].handleType
    if (info[0].attachments) {
      fileData.value = info[0].attachments // 图片地址
    }
  }
}
const fileUpload = (file) => {
  fileData.value = file
}

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请输入必填项！' }) }
  })
}
const globalJson = async () => {
  if (props.replyId) {
    const { code } = await api.microFlowRecordEdit({
      form: {
        id: props.replyId,
        opinion: form.opinion,
        attachmentIds: fileData.value.map(v => v.id).join(','),
        handleType: form.handleType
      }
    })
    if (code === 200) {
      form.opinion = ''
      fileData.value = []
      form.handleType = 1
      ElMessage({ type: 'success', message: '回复成功' })
      emit('callback')
    }
  } else {
    // if(user.value.specialRoleKeys.includes('micro_manage')){
    //   nextNodeId = 'manageReply'
    // }
    // if(user.value.specialRoleKeys.includes('micro_group_role')){
    //   nextNodeId = 'groupReply'
    // }
    // if(user.value.specialRoleKeys.includes('cppcc_member')){
    //   nextNodeId = 'memberReply'
    // }
    let params = {
      microAdviceId: props.id,
      nextNodeId: props.userType,
      record: {
        handleType: form.handleType,
        opinion: form.opinion,
        attachmentIds: fileData.value.map(v => v.id).join(','),
        targetArea: user.value.areaId
      }
    }
    const { code } = await api.complete(params)
    if (code === 200) {
      form.opinion = ''
      fileData.value = []
      form.handleType = 1
      ElMessage({ type: 'success', message: '回复成功' })
      emit('callback')
    }
  }

}
const resetForm = () => { emit('callback') }
</script>

<style scoped lang="scss">
.SubmitReply {

  //width: 800px;
  .SuggestAssignDetailNameBody {
    padding: 0 var(--zy-distance-one);
    padding-top: var(--zy-distance-one);

    .SuggestAssignDetailName {
      width: 100%;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      position: relative;
      text-align: center;

      div {
        display: inline-block;
        background-color: #fff;
        position: relative;
        z-index: 2;
        padding: 0 20px;
      }

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 1px;
        background-color: var(--zy-el-color-primary);
      }
    }
  }

  .globalFormTitle {
    .bold {
      font-weight: bold;
    }

    span {
      margin-right: 10px;
    }
  }
}
</style>
