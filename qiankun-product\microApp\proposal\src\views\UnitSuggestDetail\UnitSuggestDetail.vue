<template>
  <div class="UnitSuggestDetail">
    <div class="SuggestDetailProcessInfo" v-if="transactUnitObj.delays?.length">
      <div class="SuggestLabelName">
        申请延期记录
        <div class="SuggestLabelNameButton">
          <el-button @click="isAnswerRecords = !isAnswerRecords" v-if="transactUnitObj.delays?.length > 1"
            type="primary">
            查看更多延期记录
          </el-button>
        </div>
      </div>
      <global-info>
        <global-info-line>
          <global-info-item label="申请单位">{{
            handlerOffice?.flowHandleOfficeName
          }}</global-info-item>
          <global-info-item label="申请时间">{{
            format(delaysInfo.createDate)
          }}</global-info-item>
        </global-info-line>
        <global-info-line>
          <global-info-item label="答复截止时间">{{
            format(delaysInfo.lastAnswerAdjustDate)
          }}</global-info-item>
          <global-info-item label="申请答复截止时间">{{
            format(delaysInfo.lastApplyAdjustDate)
          }}</global-info-item>
        </global-info-line>
        <global-info-item label="申请延期理由">
          <pre>{{ delaysInfo.delayReason }}</pre>
        </global-info-item>
        <global-info-item label="是否同意延期申请">
          {{ delaysInfo.verifyStatus ? (delaysInfo.verifyStatus === 1 ? '同意申请' : '驳回') : '待审查' }}
        </global-info-item>
        <global-info-item v-if="delaysInfo.verifyStatus === 2" label="驳回理由">
          <pre>{{ delaysInfo.noPassReason }}</pre>
        </global-info-item>
      </global-info>
    </div>
    <div class="SuggestDetailProcessInfo" v-if="transactUnitObj.adjusts?.length">
      <div class="SuggestLabelName">
        申请调整记录
        <div class="SuggestLabelNameButton">
          <el-button @click="isAdjustsRecords = !isAdjustsRecords" v-if="transactUnitObj.adjusts?.length > 1"
            type="primary">
            查看更多调整记录
          </el-button>
        </div>
      </div>
      <global-info>
        <global-info-line>
          <global-info-item label="申请单位">{{
            handlerOffice?.flowHandleOfficeName
          }}</global-info-item>
          <global-info-item label="申请时间">{{
            format(adjustsInfo.createDate)
          }}</global-info-item>
        </global-info-line>
        <global-info-item label="申请调整理由">
          <pre>{{ adjustsInfo.adjustReason }}</pre>
        </global-info-item>
        <global-info-item label="希望办理单位">
          <pre>{{ adjustsInfo.hopeHandleOffice }}</pre>
        </global-info-item>
        <global-info-item label="是否同意调整申请">
          {{ adjustsInfo.verifyStatus ? (adjustsInfo.verifyStatus === 1 ? '同意申请' : '驳回') : '待审查' }}
        </global-info-item>
        <global-info-item v-if="adjustsInfo.verifyStatus" :label="adjustsInfo.verifyStatus === 1 ? '同意调整意见' : '驳回理由'">
          <pre>{{ adjustsInfo.noPassReason }}</pre>
        </global-info-item>
      </global-info>
    </div>
    <div class="SuggestTransactDetailNameBody">
      <div class="SuggestTransactDetailName">
        <div>{{ props.type === 'unitPreAssign' ? '预交办提案签收' : '提案办理' }}</div>
      </div>
    </div>
    <template v-if="props.type === 'unitPreAssign'">
      <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
        <el-form-item label="是否签收" prop="sign" class="globalFormTitle">
          <el-radio-group v-model="form.sign" :disabled="!isTime">
            <el-radio key="1" label="1">签收</el-radio>
            <el-radio key="0" label="0">申请调整</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整理由" v-if="form.sign === '0'" prop="adjustReason" class="globalFormTitle">
          <el-input v-model="form.adjustReason" placeholder="请输入调整理由" type="textarea" :rows="5" :disabled="!isTime"
            clearable />
        </el-form-item>
        <el-form-item label="希望办理单位" v-if="form.sign === '0'" prop="hopeHandleOffice" class="globalFormTitle">
          <el-input v-model="form.hopeHandleOffice" placeholder="请输入希望办理单位" type="textarea" :rows="5"
            :disabled="!isTime" clearable />
        </el-form-item>
        <div class="globalFormButton">
          <el-button type="primary" @click="submitForm(formRef)" :disabled="!isTime">提交</el-button>
          <el-button @click="resetForm" :disabled="!isTime">取消</el-button>
        </div>
      </el-form>
    </template>
    <template v-else>
      <div class="SuggestTransactBody">
        <template v-if="suggestionOfficeShow">
          <global-info>
            <global-info-item label="办理单位">{{ handlerOffice?.flowHandleOfficeName }}</global-info-item>
            <global-info-item label="调整截止时间" class="transactDetail">
              <div class="transactDetailBody">
                <div class="transactDetailInfo">
                  {{ format(adjustTime) }}
                  （
                  <global-countdown :time="adjustTime" @callback="adjustCallback"></global-countdown>
                  ）
                </div>
                <div class="transactDetailButton" v-if="isAdjust && transactStatus !== 'trace'">
                  <el-button @click="isAdjustShow = !isAdjustShow"
                    :disabled="transactUnitObj.isReply || transactUnitObj.isAdjusts || transactStatus !== 'handling'"
                    type="primary">
                    申请调整办理单位
                  </el-button>
                </div>
              </div>
            </global-info-item>
            <global-info-item label="答复截止时间" class="transactDetail">
              <div class="transactDetailBody">
                <div class="transactDetailInfo">
                  {{ format(answerTime) }}
                  （
                  <global-countdown :time="answerTime" @callback="answerCallback"></global-countdown>
                  ）
                </div>
                <div class="transactDetailButton" v-if="isAnswer && transactStatus !== 'trace'">
                  <el-button @click="isAnswerShow = !isAnswerShow"
                    :disabled="transactUnitObj.isReply || transactUnitObj.isDelays || transactStatus !== 'handling'"
                    type="primary">
                    申请延期答复
                  </el-button>
                </div>
              </div>
            </global-info-item>
            <global-info-item label="办理情况（仅供标记）" class="transactDetail">
              <div class="transactDetailBody">
                <div class="transactDetailInfo">
                  <el-select v-model="handleCondition" :disabled="transactStatus === 'trace'" placeholder="请选择内部流程状态"
                    clearable>
                    <el-option v-for="item in suggestionHandleStatus" :key="item.key" :label="item.name"
                      :value="item.key" />
                  </el-select>
                  <el-input v-model="handleStatusContent" :disabled="transactStatus === 'trace'" placeholder="请输入内容"
                    type="textarea" :rows="5" clearable />
                </div>
                <div class="transactDetailButton" v-if="transactStatus !== 'trace'">
                  <el-button @click="handleConditionClick" type="primary">更新办理情况</el-button>
                </div>
              </div>
            </global-info-item>
            <global-info-item label="征询意见表模板下载" class="transactDetail">
              <div class="transactDetailBody">
                <div class="transactDetailInfo">
                  <el-link @click="consultationDownLoad(details, setExtResult)" type="primary">{{ details.serialNumber
                  }}征询意见表.docx</el-link>
                </div>
              </div>
            </global-info-item>
            <global-info-item label="沟通情况" class="transactDetail">
              <div class="transactDetailBody">
                <div class="transactDetailInfo">
                  <el-link @click="show = !show" type="primary">查看办理单位与委员沟通情况</el-link>
                </div>
                <div class="transactDetailButton" v-if="transactStatus !== 'trace'">
                  <el-button @click="isShow = !isShow" :disabled="isConclude" type="primary">添加沟通情况</el-button>
                </div>
              </div>
            </global-info-item>
            <global-info-item label="答复意见" class="transactDetail">
              <div class="transactDetailBody">
                <div class="transactDetailInfo">
                  <div v-for="item in transactUnitObj.answers" :key="item.id">
                    <el-link @click="handleReply(item)" type="primary">
                      查看{{ handlerOffice?.flowHandleOfficeName }}的答复信息
                      {{ format(item.answerDate) }}
                      <span v-if="item.submitAnswerType === 'history'">（历史答复）</span>
                      <span v-if="item.submitAnswerType === 'history_trace'">（历史跟踪办理答复）</span>
                      <span v-if="item.submitAnswerType === 'trace'">（跟踪办理答复）</span>
                      <span v-if="item.submitAnswerType === 'direct'">（最终答复）</span>
                      {{ item.suggestionAnswerType?.label }}
                    </el-link>
                  </div>
                </div>
                <div class="transactDetailButton">
                  <span v-if="transactStatus === 'handling' && !isConclude"
                    style="font-size: 12px; color: red">请先添加沟通情况后再填写答复文件</span>
                  <el-button @click="isReplyShow = !isReplyShow" v-if="transactStatus === 'handling' && !isConclude"
                    :disabled="disabledFunc()" type="primary">填写答复文件</el-button>
                  <el-button @click="isReplyShow = !isReplyShow" v-if="transactStatus === 'has_answer' && !isConclude"
                    :disabled="!transactUnitObj.communications?.length &&
                      handleOfficeType !== 'assist'
                      " type="primary">编辑答复文件</el-button>
                  <el-button @click="isReplyShow = !isReplyShow"
                    v-if="!isReply && transactStatus === 'apply_adjust' && !isConclude" :disabled="!transactUnitObj.communications?.length &&
                      handleOfficeType !== 'assist'
                      " type="primary">填写答复文件</el-button>
                  <el-button @click="isReplyShow = !isReplyShow"
                    v-if="isReply && transactStatus === 'apply_adjust' && !isConclude" :disabled="!transactUnitObj.communications?.length &&
                      handleOfficeType !== 'assist'
                      " type="primary">编辑答复文件</el-button>
                  <el-button @click="handleTrackTransact" v-if="
                    transactStatus === 'has_answer' ||
                    transactStatus === 'apply_trace' ||
                    (transactStatus === 'has_answer' && isConclude)
                  " :disabled="handleOfficeType === 'apply_trace'" type="primary">申请跟踪办理</el-button>
                  <el-button @click="isTrackTransactReply = !isTrackTransactReply" v-if="transactStatus === 'trace'"
                    type="primary">跟踪办理答复文件</el-button>
                </div>
              </div>
            </global-info-item>
            <global-info-item label="满意度测评">
              <div v-if="!satisfactions.length">
                <el-link @click="handleSatisfactions({ id: '' })" type="primary">查看满意度测评</el-link>
              </div>
              <div v-for="item in satisfactions" :key="item.id">
                <el-link @click="handleSatisfactions(item)" type="primary">{{ item.handleResultName
                }}{{ item.isHistoryTest ? "（历史测评）" : "（最终测评）" }}</el-link>
              </div>
            </global-info-item>
          </global-info>
        </template>
        <template v-else>
          <div class="noText">协办单位办理情况联合主办单位一同答复,无需单独答复</div>
        </template>
      </div>
    </template>
  </div>
  <xyl-popup-window v-model="show" :beforeClose="beforeClose" name="办理单位与委员沟通情况">
    <CommunicationSituation :id="props.id" :unitId="transactId" type></CommunicationSituation>
  </xyl-popup-window>
  <xyl-popup-window v-model="isShow" name="添加沟通情况">
    <CommunicationSituationSubmit :suggestId="props.id" :unitId="transactId" @callback="callback">
    </CommunicationSituationSubmit>
  </xyl-popup-window>
  <xyl-popup-window v-model="isAnswerShow" name="申请延期">
    <ApplyForAnswer :suggestId="props.id" :unitId="transactId" :time="answerTime" @callback="handleCallback">
    </ApplyForAnswer>
  </xyl-popup-window>
  <xyl-popup-window v-model="isAnswerRecords" name="申请延期记录">
    <UnitApplyForAnswerRecords :name="handlerOffice?.flowHandleOfficeName" :data="transactUnitObj.delays">
    </UnitApplyForAnswerRecords>
  </xyl-popup-window>
  <xyl-popup-window v-model="isAdjustShow" name="申请调整">
    <ApplyForAdjust :suggestId="props.id" :unitId="transactId" @callback="handleCallback"></ApplyForAdjust>
  </xyl-popup-window>
  <xyl-popup-window v-model="isAdjustsRecords" name="申请调整记录">
    <UnitApplyForAdjustRecords :name="handlerOffice?.flowHandleOfficeName" :data="transactUnitObj.adjusts">
    </UnitApplyForAdjustRecords>
  </xyl-popup-window>
  <xyl-popup-window v-model="isReplyShow" name="填写答复文件">
    <SubmitSuggestReply :suggestId="props.id" :unitId="transactId" :id="isReply ? transactId : ''"
      @callback="handleCallback"></SubmitSuggestReply>
  </xyl-popup-window>
  <xyl-popup-window v-model="isTrackTransactReply" name="填写跟踪办理答复文件">
    <SubmitSuggestTrackTransactReply :suggestId="props.id" :unitId="transactId" :traceId="tracesInfo.id"
      @callback="handleCallback"></SubmitSuggestTrackTransactReply>
  </xyl-popup-window>
  <xyl-popup-window v-model="replyDetailShow" name="答复文件详情">
    <SuggestReplyDetail :id="replyId"></SuggestReplyDetail>
  </xyl-popup-window>
  <xyl-popup-window v-model="isTrackTransactShow" name="申请跟踪办理">
    <ApplyForTrackTransact :unitId="transactId" :suggestId="props.id" @callback="handleCallback">
    </ApplyForTrackTransact>
  </xyl-popup-window>
  <xyl-popup-window v-model="ifShow" name="满意度测评">
    <SegreeSatisfactionDetail :id="satisfactionsId" :suggestId="props.id"></SegreeSatisfactionDetail>
  </xyl-popup-window>
</template>
<script>
export default { name: "UnitSuggestDetail" };
</script>
<script setup>
import api from '@/api'
import { ref, reactive, onActivated, computed, watch, onDeactivated, onBeforeUnmount } from 'vue'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { format } from 'common/js/time.js'
import { ElMessage } from 'element-plus'
import { exportWordHtmlObj } from "common/config/MicroGlobal"
import CommunicationSituation from '@/views/SuggestDetail/CommunicationSituation/CommunicationSituation.vue'
import CommunicationSituationSubmit from '@/views/SuggestDetail/CommunicationSituation/CommunicationSituationSubmit.vue'
import ApplyForAnswer from './component/ApplyForAnswer.vue'
import UnitApplyForAnswerRecords from './component/UnitApplyForAnswerRecords.vue'
import ApplyForAdjust from './component/ApplyForAdjust.vue'
import UnitApplyForAdjustRecords from './component/UnitApplyForAdjustRecords.vue'
import SubmitSuggestReply from './component/SubmitSuggestReply.vue'
import ApplyForTrackTransact from './component/ApplyForTrackTransact.vue'
import SubmitSuggestTrackTransactReply from './component/SubmitSuggestTrackTransactReply.vue'
import SuggestReplyDetail from '@/views/SuggestDetail/SuggestReplyDetail/SuggestReplyDetail.vue'
import SegreeSatisfactionDetail from '@/views/SuggestDetail/SegreeSatisfactionDetail/SegreeSatisfactionDetail.vue'
const props = defineProps({
  id: { type: String, default: '' },
  type: { type: String, default: '' },
  details: { type: Object, default: () => ({}) },
  transactUnitObj: { type: Object, default: () => ({}) },
  satisfactions: { type: Array, default: () => [] },
  allhandleOfficeInfos: { type: Array, default: () => [] },
  setExtResult: { type: Object, default: () => ({}) },
  suggestionOfficeShow: { type: Boolean, default: true }
})
const emit = defineEmits(['refresh', 'callback'])

const formRef = ref()
const form = reactive({
  sign: '1',
  adjustReason: '',
  hopeHandleOffice: ''
})
const rules = reactive({
  sign: [{ required: true, message: '请选择是否签收', trigger: ['blur', 'change'] }],
  adjustReason: [{ required: true, message: '请输入调整理由', trigger: ['blur', 'change'] }],
  hopeHandleOffice: [{ required: true, message: '请输入希望办理单位', trigger: ['blur', 'change'] }]
})

const transactUnitObj = computed(() => props.transactUnitObj)
const allhandleOfficeInfos = computed(() => props.allhandleOfficeInfos)
const delaysInfo = computed(() => props.transactUnitObj.delays[props.transactUnitObj.delays.length - 1])
const adjustsInfo = computed(() => props.transactUnitObj.adjusts[props.transactUnitObj.adjusts.length - 1])
const tracesInfo = computed(() => props.transactUnitObj.traces.filter((v) => !v.hasAnswer)[0] || {})
const satisfactions = computed(() => props.satisfactions)
const handlerOffice = computed(() => props.transactUnitObj.handlerOffice)
const adjustTime = computed(() => props.transactUnitObj?.officeAdjustStopDate)
const answerTime = computed(() => props.transactUnitObj?.officeAnswerStopDate)
const transactId = computed(() => props.transactUnitObj?.handlerOffice?.id)
const transactStatus = computed(() => props.transactUnitObj?.handlerOffice?.currentHandleStatus)
const handleOfficeType = computed(() => props.transactUnitObj?.handlerOffice?.handleOfficeType)
const isReply = computed(
  () => props.transactUnitObj.answers?.filter((v) => v.submitAnswerType === 'direct')?.length || 0
)
const isConclude = computed(() => props.type === 'unitConclude')

const show = ref(false);
const isShow = ref(false);
const isAdjust = ref(false);
const isAnswer = ref(false);
const isAnswerShow = ref(false);
const isAnswerRecords = ref(false);
const isAdjustShow = ref(false);
const isAdjustsRecords = ref(false);
const isReplyShow = ref(false);
const isTrackTransactReply = ref(false);
const isTrackTransactShow = ref(false);

const replyId = ref("");
const replyDetailShow = ref(false);

const ifShow = ref(false);
const satisfactionsId = ref(""); // 满意度测评ID

const handleCondition = ref('')
const handleStatusContent = ref('')
const suggestionHandleStatus = ref([])
const AreaId = ref('')
const isTime = ref(false)
onActivated(() => {
  dictionaryData()
  globalReadConfig()
  AreaId.value = sessionStorage.getItem('AreaId') || ''
})
const isAssignAnswer = ref(false)
const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({
    codes: ['proposal_check_assist_answer']
  })
  isAssignAnswer.value = data?.proposal_check_assist_answer === 'true'
}
const disabledFunc = () => {
  if (isAssignAnswer.value) {
    if (handleOfficeType.value == 'main') {
      return !(
        transactUnitObj.value.communications?.length &&
        allhandleOfficeInfos.value.filter(
          (v) => v.handlerOffice.currentHandleStatus === 'has_answer' && v.handlerOffice.handleOfficeType === 'assist'
        ).length === allhandleOfficeInfos.value.filter((v) => v.handlerOffice.handleOfficeType === 'assist').length
      )
    } else if (handleOfficeType.value === 'assist') {
      return false
    } else {
      return !transactUnitObj.value.communications?.length
    }
  } else {
    return !transactUnitObj.value.communications?.length && handleOfficeType.value !== 'assist'
  }
}
const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['suggestion_handle_status'] })
  var { data } = res
  suggestionHandleStatus.value = data.suggestion_handle_status
  qiankunMicro.setGlobalState({ AiChatCode: 'ai-proposal-handle-chat' })
  qiankunMicro.setGlobalState({ AiChatWindow: true })
}
const consultationDownLoad = (row, setExtResult) => {
  console.log('row===>', row)
  console.log('setExtResult===>', setExtResult)
  row.organizer = handlerOffice.value.flowHandleOfficeName || ""; // 主办单位
  row.officePhone = row.submitUserInfo.officePhone || ""; // 办公电话
  row.mobile = row.submitUserInfo.mobile || ""; // 手机
  row.callAddress = row.submitUserInfo.callAddress || ""; // 通讯地址
  const assistData = setExtResult.filter((item) => item.handleOfficeType === "assist");
  const assistNames = assistData.map((item) => item.handleOfficeName).join(",");
  row.CoOrganizer = assistNames; // 协办单位
  const mainData = setExtResult.filter((item) => item.handleOfficeType === "main");
  row.telephone = mainData[0] ? mainData[0].telephone : '';
  row.circlesType = row.termYear?.circlesType?.name
  row.boutType = row.termYear?.boutType?.name
  exportWordHtmlObj({
    code: "unitSuggestDetail",
    name: "征询意见表模板下载",
    key: "content",
    data: row,
  });
}
const beforeClose = (cb) => {
  emit("refresh")
  cb()
}
const callback = () => {
  emit('refresh')
  isShow.value = false
}
const adjustCallback = (type) => {
  isAdjust.value = type
}
const answerCallback = (type) => {
  isAnswer.value = type
}
const handleCallback = (type) => {
  isAnswerShow.value = false;
  isAdjustShow.value = false;
  isReplyShow.value = false;
  isTrackTransactShow.value = false;
  if (type) {
    emit("callback");
  }
}
const handleConditionClick = () => {
  globalJson()
}
const globalJson = async () => {
  const { code } = await api.globalJson("/proposal/handle/status", {
    handlingPortionId: transactId.value,
    suggestionHandleStatus: handleCondition.value,
    handleStatusContent: handleStatusContent.value,
  });
  if (code === 200) {
    ElMessage({ type: "success", message: "更新成功" });
    emit("refresh");
  }
};
const handleReply = (item) => {
  replyId.value = item.id;
  replyDetailShow.value = true;
};
const handleTrackTransact = () => {
  isTrackTransactShow.value = true;
};
const handleSatisfactions = (item) => {
  satisfactionsId.value = item.id
  ifShow.value = true
}
onDeactivated(() => {
  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })
  qiankunMicro.setGlobalState({ AiChatContent: '' })
  qiankunMicro.setGlobalState({ AiChatWindow: false })
})
onBeforeUnmount(() => {
  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })
  qiankunMicro.setGlobalState({ AiChatContent: '' })
  qiankunMicro.setGlobalState({ AiChatWindow: false })
})
const checkTime = (time) => {
  // 获取传入时间、现在时间的时间戳
  const date = new Date(time).getTime()
  const nowDate = new Date().getTime()
  // 判断现在的时间是否大于传入时间
  return nowDate < date
}
watch(
  () => [props.transactUnitObj, props.details],
  () => {
    isTime.value = checkTime(props.transactUnitObj?.officeConfirmStopDate)
    qiankunMicro.setGlobalState({ AiChatContent: props.details?.content || '' })
    handleCondition.value = props.transactUnitObj.handlerOffice?.suggestionHandleStatus?.value
    handleStatusContent.value = props.transactUnitObj.handlerOffice?.handleStatusContent
  },
  { immediate: true }
)

const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (form.sign === '1') {
        handlingPortionConfirm()
      } else {
        handingPortionAdjust()
      }
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}

const handlingPortionConfirm = async () => {
  const { code } = await api.handlingPortionConfirm({
    form: { id: transactId.value }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '签收成功' })
    emit('callback')
  }
}

const handingPortionAdjust = async () => {
  const { code } = await api.globalJson('/cppcc/handingPortionAdjust/add', {
    form: {
      handlingPortionId: transactId.value,
      suggestionId: props.id,
      adjustReason: form.adjustReason,
      hopeHandleOffice: form.hopeHandleOffice
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '申请成功' })
    emit('callback')
  }
}

const resetForm = () => {
  emit('callback')
}
</script>
<style lang="scss">
.UnitSuggestDetail {
  width: 100%;
  height: 100%;

  .SuggestDetailProcessInfo {
    padding-top: 0 !important;
  }

  .SuggestTransactDetailNameBody {
    padding: 0 var(--zy-distance-one);
    padding-top: var(--zy-distance-one);

    .SuggestTransactDetailName {
      width: 100%;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      position: relative;
      text-align: center;

      div {
        display: inline-block;
        background-color: #fff;
        position: relative;
        z-index: 2;
        padding: 0 20px;
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 1px;
        background-color: var(--zy-el-color-primary);
      }
    }
  }

  .SuggestTransactBody {
    padding: var(--zy-distance-one);

    .global-info {
      padding-bottom: 12px;

      .global-info-item {
        .global-info-label {
          width: 160px;
        }

        .global-info-content {
          width: calc(100% - 160px);
        }
      }

      .transactDetail {
        .global-info-content {
          width: calc(100% - 160px);
          padding: 0;

          &>span {
            width: 100%;
            height: 100%;
          }

          .transactDetailBody {
            width: 100%;
            height: 100%;
            display: flex;

            .transactDetailInfo {
              width: calc(100% - 180px);
              padding: var(--zy-distance-five) var(--zy-distance-four);
              display: flex;
              align-items: center;
              flex-wrap: wrap;

              .zy-el-select {
                margin-bottom: var(--zy-distance-five);
              }
            }

            .transactDetailButton {
              width: 180px;
              border-left: 1px solid var(--zy-el-border-color-lighter);
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              padding: var(--zy-distance-five) var(--zy-distance-four);

              .zy-el-button {
                --zy-el-button-size: var(--zy-height-secondary);
                border-radius: var(--el-border-radius-small);
                margin: 0;
              }

              .zy-el-button+.zy-el-button {
                margin-top: var(--zy-distance-five);
              }
            }
          }
        }
      }
    }

    .noText {
      text-align: center;
      font-size: var(--zy-name-font-size);
      font-weight: bold;
      border: 1px solid #3657c0;
      border-radius: 5px;
      padding: 120px 0;
    }
  }
}
</style>
