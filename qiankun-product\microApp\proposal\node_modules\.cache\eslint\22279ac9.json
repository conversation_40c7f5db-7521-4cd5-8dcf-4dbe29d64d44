[{"D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\api\\index.js": "1", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\index.js": "2", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\router\\index.js": "3", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\SuperEdit.vue": "4", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSummaryReport\\UnitSummaryReport.vue": "5", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestConclude\\UnitSuggestConclude.vue": "6", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\batchExport\\batchExport.vue": "7", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\SuggestClueMine.vue": "8", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueAdd\\SuggestClueAdd.vue": "9", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\SuggestClueControl.vue": "10", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestTrackTransact\\UnitSuggestTrackTransact.vue": "11", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\SuggestClueRegister.vue": "12", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestDocument\\SuggestDocument.vue": "13", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitAllSuggest\\UnitAllSuggest.vue": "14", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestReply\\UnitSuggestReply.vue": "15", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestAdvanceAssign\\UnitSuggestAdvanceAssign.vue": "16", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestTransact\\UnitSuggestTransact.vue": "17", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReply\\SuggestReply.vue": "18", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConclude\\SuggestConclude.vue": "19", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\SuggestTrackTransact.vue": "20", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForPostpone\\SuggestApplyForPostpone.vue": "21", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPreApplyForAdjust\\SuggestPreApplyForAdjust.vue": "22", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTransact\\SuggestTransact.vue": "23", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\SuggestApplyForAdjust.vue": "24", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\SuggestAssign.vue": "25", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\DoNotReceiveSuggest\\DoNotReceiveSuggest.vue": "26", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAdvanceAssign\\SuggestAdvanceAssign.vue": "27", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\SuggestReview.vue": "28", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SuggestedClassification.vue": "29", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SuggestedSubdivide.vue": "30", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\SuggestStatistics.vue": "31", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\SuggestControls.vue": "32", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\SubmitProposalClue.vue": "33", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\AllSuggest.vue": "34", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestDetail.vue": "35", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\PersonalAllSuggest\\PersonalAllSuggest.vue": "36", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\MyLedSuggest.vue": "37", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyJointSuggest\\MyJointSuggest.vue": "38", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SuggestDraftBox\\SuggestDraftBox.vue": "39", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\SuggestNumbering.vue": "40", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\CollectiveProposalUnit.vue": "41", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\SubmitSuggest.vue": "42", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\HistoricalProposal\\HistoricalProposal.vue": "43", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\ManualMergeProposal.vue": "44", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\assets\\js\\suggestExportWord.js": "45", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SelectHandlingUnit.vue": "46", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandWaySuperEdit.vue": "47", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\components\\SuggestClueDetails.vue": "48", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\HistoricalProposal\\HistoricalProposalNew.vue": "49", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggestPrint\\suggestPrint.vue": "50", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\component\\SuggestBasicInfo.vue": "51", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\HistoricalProposal\\HistoricalProposalDetails.vue": "52", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestAssignDetail.vue": "53", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\components\\SuggestClueDetails.vue": "54", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\component\\SuggestTrackTransactDetail.vue": "55", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\UnitSuggestDetail.vue": "56", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\ApplyForAdjustResult\\ApplyForAdjustResult.vue": "57", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\component\\SuggestAdjustReview.vue": "58", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestReplyDetail\\SuggestReplyDetail.vue": "59", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SegreeSatisfactionDetail\\SegreeSatisfactionDetail.vue": "60", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueAdd\\components\\SuggestClueDetails.vue": "61", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\ScoreProportion.vue": "62", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueSubmit.vue": "63", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\component\\SubmitSegreeSatisfaction.vue": "64", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SimilarityQuery.vue": "65", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueDetails.vue": "66", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\component\\SuggestNumberingSubmit.vue": "67", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\component\\SubmitCollectiveProposalUnit.vue": "68", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\global-dynamic-title\\global-dynamic-title.vue": "69", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\component\\SuggestReviewDetail.vue": "70", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandUnitSuperNew.vue": "71", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestReply.vue": "72", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendType\\SuggestRecommendType.vue": "73", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendUnit\\SuggestRecommendUnit.vue": "74", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\global-dynamic-title\\global-dynamic-input.vue": "75"}, {"size": 16010, "mtime": 1758505872218, "results": "76", "hashOfConfig": "77"}, {"size": 525, "mtime": 1758505703728, "results": "78", "hashOfConfig": "77"}, {"size": 13628, "mtime": 1758505703743, "results": "79", "hashOfConfig": "77"}, {"size": 26538, "mtime": 1758505704046, "results": "80", "hashOfConfig": "77"}, {"size": 7979, "mtime": 1758505704096, "results": "81", "hashOfConfig": "77"}, {"size": 5188, "mtime": 1758505704062, "results": "82", "hashOfConfig": "77"}, {"size": 7441, "mtime": 1758505704101, "results": "83", "hashOfConfig": "77"}, {"size": 4993, "mtime": 1758505703902, "results": "84", "hashOfConfig": "77"}, {"size": 11022, "mtime": 1758505703877, "results": "85", "hashOfConfig": "77"}, {"size": 7728, "mtime": 1758505703887, "results": "86", "hashOfConfig": "77"}, {"size": 4725, "mtime": 1758505704084, "results": "87", "hashOfConfig": "77"}, {"size": 4066, "mtime": 1758505703911, "results": "88", "hashOfConfig": "77"}, {"size": 3340, "mtime": 1758505703921, "results": "89", "hashOfConfig": "77"}, {"size": 4852, "mtime": 1758505704052, "results": "90", "hashOfConfig": "77"}, {"size": 5175, "mtime": 1758505704080, "results": "91", "hashOfConfig": "77"}, {"size": 10818, "mtime": 1758505704056, "results": "92", "hashOfConfig": "77"}, {"size": 12483, "mtime": 1758505704091, "results": "93", "hashOfConfig": "77"}, {"size": 9800, "mtime": 1758505703982, "results": "94", "hashOfConfig": "77"}, {"size": 5981, "mtime": 1758505703926, "results": "95", "hashOfConfig": "77"}, {"size": 5578, "mtime": 1758505704006, "results": "96", "hashOfConfig": "77"}, {"size": 5670, "mtime": 1758505703857, "results": "97", "hashOfConfig": "77"}, {"size": 4362, "mtime": 1758505703977, "results": "98", "hashOfConfig": "77"}, {"size": 15340, "mtime": 1758505704017, "results": "99", "hashOfConfig": "77"}, {"size": 5196, "mtime": 1758505703844, "results": "100", "hashOfConfig": "77"}, {"size": 7975, "mtime": 1758505703864, "results": "101", "hashOfConfig": "77"}, {"size": 4182, "mtime": 1758505703795, "results": "102", "hashOfConfig": "77"}, {"size": 11829, "mtime": 1758505703838, "results": "103", "hashOfConfig": "77"}, {"size": 7600, "mtime": 1758505703988, "results": "104", "hashOfConfig": "77"}, {"size": 16145, "mtime": 1758505704029, "results": "105", "hashOfConfig": "77"}, {"size": 15417, "mtime": 1758505704033, "results": "106", "hashOfConfig": "77"}, {"size": 16790, "mtime": 1758505704001, "results": "107", "hashOfConfig": "77"}, {"size": 10485, "mtime": 1758505703952, "results": "108", "hashOfConfig": "77"}, {"size": 5759, "mtime": 1758505703828, "results": "109", "hashOfConfig": "77"}, {"size": 19363, "mtime": 1758505703750, "results": "110", "hashOfConfig": "77"}, {"size": 65697, "mtime": 1758505703965, "results": "111", "hashOfConfig": "77"}, {"size": 5616, "mtime": 1758505703823, "results": "112", "hashOfConfig": "77"}, {"size": 6716, "mtime": 1758505703765, "results": "113", "hashOfConfig": "77"}, {"size": 6103, "mtime": 1758505703759, "results": "114", "hashOfConfig": "77"}, {"size": 5803, "mtime": 1758505703790, "results": "115", "hashOfConfig": "77"}, {"size": 5063, "mtime": 1758505703942, "results": "116", "hashOfConfig": "77"}, {"size": 5660, "mtime": 1758505703931, "results": "117", "hashOfConfig": "77"}, {"size": 49134, "mtime": 1758505703783, "results": "118", "hashOfConfig": "77"}, {"size": 8004, "mtime": 1758505872009, "results": "119", "hashOfConfig": "77"}, {"size": 4694, "mtime": 1758505703817, "results": "120", "hashOfConfig": "77"}, {"size": 8355, "mtime": 1758505703679, "results": "121", "hashOfConfig": "77"}, {"size": 5137, "mtime": 1758505704024, "results": "122", "hashOfConfig": "77"}, {"size": 6353, "mtime": 1758505704041, "results": "123", "hashOfConfig": "77"}, {"size": 3170, "mtime": 1758505703906, "results": "124", "hashOfConfig": "77"}, {"size": 10703, "mtime": 1758505909894, "results": "125", "hashOfConfig": "77"}, {"size": 20944, "mtime": 1758505703737, "results": "126", "hashOfConfig": "77"}, {"size": 10376, "mtime": 1758505703973, "results": "127", "hashOfConfig": "77"}, {"size": 3552, "mtime": 1758505885563, "results": "128", "hashOfConfig": "77"}, {"size": 21910, "mtime": 1758505703871, "results": "129", "hashOfConfig": "77"}, {"size": 3172, "mtime": 1758505703915, "results": "130", "hashOfConfig": "77"}, {"size": 5315, "mtime": 1758505704011, "results": "131", "hashOfConfig": "77"}, {"size": 28269, "mtime": 1758505704068, "results": "132", "hashOfConfig": "77"}, {"size": 3346, "mtime": 1758505703957, "results": "133", "hashOfConfig": "77"}, {"size": 12462, "mtime": 1758505703849, "results": "134", "hashOfConfig": "77"}, {"size": 2717, "mtime": 1758505703969, "results": "135", "hashOfConfig": "77"}, {"size": 3876, "mtime": 1758505703960, "results": "136", "hashOfConfig": "77"}, {"size": 3489, "mtime": 1758505703881, "results": "137", "hashOfConfig": "77"}, {"size": 2316, "mtime": 1758505703775, "results": "138", "hashOfConfig": "77"}, {"size": 5212, "mtime": 1758505703897, "results": "139", "hashOfConfig": "77"}, {"size": 8163, "mtime": 1758505703771, "results": "140", "hashOfConfig": "77"}, {"size": 4888, "mtime": 1758505703693, "results": "141", "hashOfConfig": "77"}, {"size": 3196, "mtime": 1758505703893, "results": "142", "hashOfConfig": "77"}, {"size": 5643, "mtime": 1758505703946, "results": "143", "hashOfConfig": "77"}, {"size": 4102, "mtime": 1758505703937, "results": "144", "hashOfConfig": "77"}, {"size": 5482, "mtime": 1758505703717, "results": "145", "hashOfConfig": "77"}, {"size": 17771, "mtime": 1758505703995, "results": "146", "hashOfConfig": "77"}, {"size": 9773, "mtime": 1758505704037, "results": "147", "hashOfConfig": "77"}, {"size": 8096, "mtime": 1758505704074, "results": "148", "hashOfConfig": "77"}, {"size": 3761, "mtime": 1758505703699, "results": "149", "hashOfConfig": "77"}, {"size": 2613, "mtime": 1758505703706, "results": "150", "hashOfConfig": "77"}, {"size": 838, "mtime": 1758505703712, "results": "151", "hashOfConfig": "77"}, {"filePath": "152", "messages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1nwkhy", {"filePath": "154", "messages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\api\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\router\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\SuperEdit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSummaryReport\\UnitSummaryReport.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestConclude\\UnitSuggestConclude.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\batchExport\\batchExport.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\SuggestClueMine.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueAdd\\SuggestClueAdd.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\SuggestClueControl.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestTrackTransact\\UnitSuggestTrackTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\SuggestClueRegister.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestDocument\\SuggestDocument.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitAllSuggest\\UnitAllSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestReply\\UnitSuggestReply.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestAdvanceAssign\\UnitSuggestAdvanceAssign.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestTransact\\UnitSuggestTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReply\\SuggestReply.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConclude\\SuggestConclude.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\SuggestTrackTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForPostpone\\SuggestApplyForPostpone.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPreApplyForAdjust\\SuggestPreApplyForAdjust.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTransact\\SuggestTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\SuggestApplyForAdjust.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\SuggestAssign.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\DoNotReceiveSuggest\\DoNotReceiveSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAdvanceAssign\\SuggestAdvanceAssign.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\SuggestReview.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SuggestedClassification.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SuggestedSubdivide.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\SuggestStatistics.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\SuggestControls.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\SubmitProposalClue.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\AllSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\PersonalAllSuggest\\PersonalAllSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\MyLedSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyJointSuggest\\MyJointSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SuggestDraftBox\\SuggestDraftBox.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\SuggestNumbering.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\CollectiveProposalUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\SubmitSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\HistoricalProposal\\HistoricalProposal.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\ManualMergeProposal.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\assets\\js\\suggestExportWord.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SelectHandlingUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandWaySuperEdit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\components\\SuggestClueDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\HistoricalProposal\\HistoricalProposalNew.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggestPrint\\suggestPrint.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\component\\SuggestBasicInfo.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\HistoricalProposal\\HistoricalProposalDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestAssignDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\components\\SuggestClueDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\component\\SuggestTrackTransactDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\UnitSuggestDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\ApplyForAdjustResult\\ApplyForAdjustResult.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\component\\SuggestAdjustReview.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestReplyDetail\\SuggestReplyDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SegreeSatisfactionDetail\\SegreeSatisfactionDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueAdd\\components\\SuggestClueDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\ScoreProportion.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\component\\SubmitSegreeSatisfaction.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SimilarityQuery.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\component\\SuggestNumberingSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\component\\SubmitCollectiveProposalUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\global-dynamic-title\\global-dynamic-title.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\component\\SuggestReviewDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandUnitSuperNew.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestReply.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendType\\SuggestRecommendType.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendUnit\\SuggestRecommendUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\global-dynamic-title\\global-dynamic-input.vue", []]