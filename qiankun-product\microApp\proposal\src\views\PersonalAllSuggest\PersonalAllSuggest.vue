<template>
  <div class="PersonalAllSuggest">
    <xyl-search-button
      @queryClick="handleQuery"
      @resetClick="handleReset"
      @handleButton="handleButton"
      :buttonList="buttonList"
      :data="tableHead"
      ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table
        ref="tableRef"
        row-key="id"
        :data="tableData"
        @select="handleTableSelect"
        @select-all="handleTableSelect"
        @sort-change="handleSortChange"
        :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick">
          <template #title="scope">
            <el-link @click="handleDetails(scope.row)" type="primary" class="AllSuggestIsMajorSuggestionLink">
              <span v-if="scope.row.isMajorSuggestion" class="SuggestMajorIcon"></span>
              <span v-if="scope.row.isOpen" class="SuggestOpenIcon"></span>
              {{ scope.row.title }}
            </el-link>
          </template>
          <template #mainHandleOffices="scope">
            {{ scope.row.mainHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}
          </template>
          <template #assistHandleOffices="scope">
            {{ scope.row.assistHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}
          </template>
          <template #publishHandleOffices="scope">
            {{ scope.row.publishHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}
          </template>
        </xyl-global-table>
        <xyl-global-table-button :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
        :total="totals"
        background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel
        name="所有提案"
        :exportId="exportId"
        :params="exportParams"
        module="proposalExportExcel"
        tableId="id_prop_proposal_member_view"
        @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'PersonalAllSuggest' }
</script>
<script setup>
import { onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord } from '@/assets/js/suggestExportWord'
import { ElMessage } from 'element-plus'
const buttonList = [
  // { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' }
]
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel
} = GlobalTable({ tableId: 'id_prop_proposal_member_view', tableApi: 'suggestionList' })

onActivated(() => {
  handleQuery()
})
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  if (item.ownOrJoin) {
    qiankunMicro.setGlobalState({
      openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } }
    })
  } else {
    if (item.suggestOpenType?.value === 'open_all') {
      qiankunMicro.setGlobalState({
        openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id, logo: 'Personal' } }
      })
    } else {
      ElMessage({ type: 'warning', message: '当前提案仅公开标题，无法查看详情' })
    }
  }
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
}
</script>
<style lang="scss">
.PersonalAllSuggest {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }

  .AllSuggestIsMajorSuggestionLink {
    .zy-el-link__inner {
      .SuggestOpenIcon {
        width: 40px;
        height: 19px;
        display: inline-block;
        background: url('@/assets/img/suggest_open_icon.png') no-repeat;
        background-size: 100% 100%;
        margin-right: 6px;
      }
      .SuggestMajorIcon {
        width: 40px;
        height: 19px;
        display: inline-block;
        background: url('@/assets/img/suggest_major_icon.png') no-repeat;
        background-size: 100% 100%;
        margin-right: 6px;
      }
    }
  }
}
</style>
