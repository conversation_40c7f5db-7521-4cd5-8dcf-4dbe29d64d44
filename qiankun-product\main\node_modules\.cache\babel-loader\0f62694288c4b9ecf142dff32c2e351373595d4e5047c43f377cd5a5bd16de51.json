{"ast": null, "code": "var HomeLayout = function HomeLayout() {\n  return import('customize/HomeLayout/HomeLayout.vue');\n}; // 新默认打开页面\nvar LoginView = function LoginView() {\n  return import('customize/LoginView/LoginViewCopy.vue');\n};\n// const LoginView = () => import('customize/LoginView/LoginView.vue')\nvar LayoutContainer = function LayoutContainer() {\n  return import('customize/LayoutContainer/LayoutContainer.vue');\n};\nvar UnifyLogin = function UnifyLogin() {\n  return import('customize/UnifyLogin/UnifyLogin.vue');\n};\nvar LoginViewOne = function LoginViewOne() {\n  return import('customize/LoginViewOne/LoginViewOne.vue');\n};\nvar LoginViewRegion = function LoginViewRegion() {\n  return import('customize/LoginViewRegion/LoginViewRegion.vue');\n};\nvar PostMam = function PostMam() {\n  return import('customize/PostMam/PostMam.vue');\n};\nvar WorkBench = function WorkBench() {\n  return import('customize/WorkBench/WorkBench.vue');\n};\nvar WorkBenchCopy = function WorkBenchCopy() {\n  return import('customize/WorkBench/WorkBenchCopy.vue');\n};\nvar OtherRouter = function OtherRouter() {\n  return import('customize/OtherRouter/OtherRouter.vue');\n};\nvar NotFoundPage = function NotFoundPage() {\n  return import('customize/NotFoundPage/NotFoundPage.vue');\n};\nvar NoneAccessAuthority = function NoneAccessAuthority() {\n  return import('customize/NoneAccessAuthority/NoneAccessAuthority.vue');\n};\nvar GlobalHome = function GlobalHome() {\n  return import('customize/GlobalHome/GlobalHome.vue');\n};\nvar HotSpot = function HotSpot() {\n  return import('customize/HotSpot/HotSpot.vue');\n};\nvar BackgroundCheck = function BackgroundCheck() {\n  return import('customize/BackgroundCheck/BackgroundCheck.vue');\n};\nvar BackgroundCheckInfo = function BackgroundCheckInfo() {\n  return import('customize/BackgroundCheck/BackgroundCheckInfo.vue');\n};\nvar BackgroundCheckDetails = function BackgroundCheckDetails() {\n  return import('customize/BackgroundCheck/BackgroundCheckDetails.vue');\n};\nvar PublicSentimentInfo = function PublicSentimentInfo() {\n  return import('customize/PublicSentimentInfo/PublicSentimentInfo.vue');\n};\nvar AnswerManage = function AnswerManage() {\n  return import('customize/QuestionsAndAnswers/AnswerManage.vue');\n};\nvar QuestionsAndAnswers = function QuestionsAndAnswers() {\n  return import('customize/QuestionsAndAnswers/QuestionsAndAnswers.vue');\n};\n// const homePage = () => import('customize/homePage/homePage.vue')\nvar homePage = function homePage() {\n  return import('customize/homePage/homePageNew.vue');\n};\n\n// const GlobalChat = () => import('customize/GlobalChat/GlobalChat.vue')\nvar GlobalLayoutChat = function GlobalLayoutChat() {\n  return import('customize/GlobalChat/GlobalLayoutChat.vue');\n};\nvar DevelopContent = function DevelopContent() {\n  return import('customize/Intelligentize/DevelopContent/DevelopContent.vue');\n};\nvar VersionComparison = function VersionComparison() {\n  return import('customize/Intelligentize/VersionComparison/VersionComparison.vue');\n};\nvar DataRecommendation = function DataRecommendation() {\n  return import('customize/Intelligentize/DataRecommendation/DataRecommendation.vue');\n};\nvar DataRecommendationOpen = function DataRecommendationOpen() {\n  return import('customize/Intelligentize/DataRecommendation/DataRecommendationOpen.vue');\n};\nvar AssistedWriting = function AssistedWriting() {\n  return import('customize/AssistedWriting/AssistedWriting.vue');\n};\nvar AssistedWritingRD = function AssistedWritingRD() {\n  return import('customize/AssistedWriting/AssistedWritingRD.vue');\n};\nvar AiReportGenera = function AiReportGenera() {\n  return import('customize/AiReportGenera/AiReportGenera.vue');\n};\nvar AiReportGeneraList = function AiReportGeneraList() {\n  return import('customize/AiReportGenera/AiReportGeneraList.vue');\n};\nvar AiReportGeneraView = function AiReportGeneraView() {\n  return import('customize/AiReportGenera/AiReportGeneraView.vue');\n};\nvar HotspotPush = function HotspotPush() {\n  return import('customize/HotspotPush/HotspotPush.vue');\n};\nvar WarningDetail = function WarningDetail() {\n  return import('customize/HotspotPush/WarningDetail.vue');\n};\nvar VersionComparisonAi = function VersionComparisonAi() {\n  return import('customize/Intelligentize/VersionComparison/VersionComparisonAi.vue');\n};\nvar GlobalAiToolBox = function GlobalAiToolBox() {\n  return import('customize/AiToolBox/GlobalAiToolBox.vue');\n};\nvar AiToolBox = function AiToolBox() {\n  return import('customize/AiToolBox/AiToolBox.vue');\n};\nvar AiUseStatistics = function AiUseStatistics() {\n  return import('customize/AiUseStatistics/AiUseStatistics.vue');\n};\nvar AiHotWordList = function AiHotWordList() {\n  return import('customize/AiUseStatistics/AiHotWord/AiHotWordList.vue');\n};\n// 议题符合性审查\nvar complianceReviewTopics = function complianceReviewTopics() {\n  return import('customize/complianceReviewTopics/complianceReviewTopics.vue');\n};\nvar page = [{\n  path: '/PostMam',\n  name: 'PostMam',\n  component: PostMam,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/WorkBench',\n  name: 'WorkBench',\n  component: WorkBench,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/OtherRouter',\n  name: 'OtherRouter',\n  component: OtherRouter,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/NotFoundPage',\n  name: 'NotFoundPage',\n  component: NotFoundPage,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/NoneAccessAuthority',\n  name: 'NoneAccessAuthority',\n  component: NoneAccessAuthority,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/GlobalHome',\n  name: 'GlobalHome',\n  component: GlobalHome,\n  meta: {\n    moduleName: 'main'\n  }\n},\n// { path: '/GlobalChat', name: 'GlobalChat', component: GlobalChat, meta: { moduleName: 'main' } },\n{\n  path: '/HotSpot',\n  name: 'HotSpot',\n  component: HotSpot,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/BackgroundCheck',\n  name: 'BackgroundCheck',\n  component: BackgroundCheck,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/BackgroundCheckInfo',\n  name: 'BackgroundCheckInfo',\n  component: BackgroundCheckInfo,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/BackgroundCheckDetails',\n  name: 'BackgroundCheckDetails',\n  component: BackgroundCheckDetails,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/PublicSentimentInfo',\n  name: 'PublicSentimentInfo',\n  component: PublicSentimentInfo,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AnswerManage',\n  name: 'AnswerManage',\n  component: AnswerManage,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/QuestionsAndAnswers',\n  name: 'QuestionsAndAnswers',\n  component: QuestionsAndAnswers,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/homePage',\n  name: 'homePage',\n  component: homePage,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/WorkBenchCopy',\n  name: 'WorkBenchCopy',\n  component: WorkBenchCopy,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/DevelopContent',\n  name: 'DevelopContent',\n  component: DevelopContent,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/VersionComparison',\n  name: 'VersionComparison',\n  component: VersionComparison,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/DataRecommendation',\n  name: 'DataRecommendation',\n  component: DataRecommendation,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/complianceReviewTopics',\n  name: 'complianceReviewTopics',\n  component: complianceReviewTopics,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AssistedWriting',\n  name: 'AssistedWriting',\n  component: AssistedWriting,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AssistedWritingRD',\n  name: 'AssistedWritingRD',\n  component: AssistedWritingRD,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AiReportGenera',\n  name: 'AiReportGenera',\n  component: AiReportGenera,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AiReportGeneraList',\n  name: 'AiReportGeneraList',\n  component: AiReportGeneraList,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AiReportGeneraView',\n  name: 'AiReportGeneraView',\n  component: AiReportGeneraView,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/HotspotPush',\n  name: 'HotspotPush',\n  component: HotspotPush,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/WarningDetail',\n  name: 'WarningDetail',\n  component: WarningDetail,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AiToolBox',\n  name: 'AiToolBox',\n  component: AiToolBox,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AiUseStatistics',\n  name: 'AiUseStatistics',\n  component: AiUseStatistics,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/AiHotWordList',\n  name: 'AiHotWordList',\n  component: AiHotWordList,\n  meta: {\n    moduleName: 'main'\n  }\n}];\nvar customizeRouter = [{\n  path: '/HomeLayout',\n  name: 'HomeLayout',\n  component: HomeLayout,\n  meta: {\n    moduleName: 'verify'\n  }\n}, {\n  path: '/LoginView',\n  name: 'LoginView',\n  component: LoginView\n}, {\n  path: '/:pathMatch(.*)*',\n  name: 'LayoutContainer',\n  component: LayoutContainer,\n  children: page\n}, {\n  path: '/GlobalLayoutChat',\n  name: 'GlobalLayoutChat',\n  component: GlobalLayoutChat,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/UnifyLogin',\n  name: 'UnifyLogin',\n  component: UnifyLogin,\n  meta: {\n    moduleName: 'verify'\n  }\n}, {\n  path: '/LoginViewOne',\n  name: 'LoginViewOne',\n  component: LoginViewOne,\n  meta: {\n    moduleName: 'verify'\n  }\n}, {\n  path: '/LoginViewRegion',\n  name: 'LoginViewRegion',\n  component: LoginViewRegion,\n  meta: {\n    moduleName: 'verify'\n  }\n}, {\n  path: '/GlobalAiToolBox',\n  name: 'GlobalAiToolBox',\n  component: GlobalAiToolBox,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/DataRecommendationOpen',\n  name: 'DataRecommendationOpen',\n  component: DataRecommendationOpen\n}, {\n  path: '/DevelopContentPublic',\n  name: 'DevelopContentPublic',\n  component: DevelopContent,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/VersionComparisonPublic',\n  name: 'VersionComparisonPublic',\n  component: VersionComparison,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/DataRecommendationPublic',\n  name: 'DataRecommendationPublic',\n  component: DataRecommendation,\n  meta: {\n    moduleName: 'main'\n  }\n}, {\n  path: '/VersionComparisonAi',\n  name: 'VersionComparisonAi',\n  component: VersionComparisonAi,\n  meta: {\n    moduleName: 'main'\n  }\n}];\nexport default customizeRouter;", "map": {"version": 3, "names": ["HomeLayout", "<PERSON><PERSON><PERSON>ie<PERSON>", "LayoutContainer", "UnifyLogin", "LoginViewOne", "LoginViewRegion", "PostMam", "WorkBench", "WorkBenchCopy", "OtherRouter", "NotFoundPage", "NoneAccessAuthority", "GlobalHome", "HotSpot", "<PERSON><PERSON><PERSON><PERSON>", "BackgroundCheckInfo", "BackgroundCheckDetails", "PublicSentimentInfo", "AnswerManage", "QuestionsAndAnswers", "homePage", "GlobalLayoutChat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VersionComparison", "DataRecommendation", "DataRecommendationOpen", "AssistedWriting", "AssistedWritingRD", "AiReportGenera", "AiReportGeneraList", "AiReportGeneraView", "HotspotPush", "WarningDetail", "VersionComparisonAi", "GlobalAiToolBox", "AiToolBox", "AiUseStatistics", "AiHotWordList", "complianceReviewTopics", "page", "path", "name", "component", "meta", "moduleName", "customizeRouter", "children"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/config/router.js"], "sourcesContent": ["const HomeLayout = () => import('customize/HomeLayout/HomeLayout.vue') // 新默认打开页面\r\nconst LoginView = () => import('customize/LoginView/LoginViewCopy.vue')\r\n// const LoginView = () => import('customize/LoginView/LoginView.vue')\r\nconst LayoutContainer = () => import('customize/LayoutContainer/LayoutContainer.vue')\r\nconst UnifyLogin = () => import('customize/UnifyLogin/UnifyLogin.vue')\r\nconst LoginViewOne = () => import('customize/LoginViewOne/LoginViewOne.vue')\r\nconst LoginViewRegion = () => import('customize/LoginViewRegion/LoginViewRegion.vue')\r\nconst PostMam = () => import('customize/PostMam/PostMam.vue')\r\nconst WorkBench = () => import('customize/WorkBench/WorkBench.vue')\r\nconst WorkBenchCopy = () => import('customize/WorkBench/WorkBenchCopy.vue')\r\nconst OtherRouter = () => import('customize/OtherRouter/OtherRouter.vue')\r\nconst NotFoundPage = () => import('customize/NotFoundPage/NotFoundPage.vue')\r\nconst NoneAccessAuthority = () => import('customize/NoneAccessAuthority/NoneAccessAuthority.vue')\r\nconst GlobalHome = () => import('customize/GlobalHome/GlobalHome.vue')\r\nconst HotSpot = () => import('customize/HotSpot/HotSpot.vue')\r\nconst BackgroundCheck = () => import('customize/BackgroundCheck/BackgroundCheck.vue')\r\nconst BackgroundCheckInfo = () => import('customize/BackgroundCheck/BackgroundCheckInfo.vue')\r\nconst BackgroundCheckDetails = () => import('customize/BackgroundCheck/BackgroundCheckDetails.vue')\r\nconst PublicSentimentInfo = () => import('customize/PublicSentimentInfo/PublicSentimentInfo.vue')\r\nconst AnswerManage = () => import('customize/QuestionsAndAnswers/AnswerManage.vue')\r\nconst QuestionsAndAnswers = () => import('customize/QuestionsAndAnswers/QuestionsAndAnswers.vue')\r\n// const homePage = () => import('customize/homePage/homePage.vue')\r\nconst homePage = () => import('customize/homePage/homePageNew.vue')\r\n\r\n// const GlobalChat = () => import('customize/GlobalChat/GlobalChat.vue')\r\nconst GlobalLayoutChat = () => import('customize/GlobalChat/GlobalLayoutChat.vue')\r\nconst DevelopContent = () => import('customize/Intelligentize/DevelopContent/DevelopContent.vue')\r\nconst VersionComparison = () => import('customize/Intelligentize/VersionComparison/VersionComparison.vue')\r\nconst DataRecommendation = () => import('customize/Intelligentize/DataRecommendation/DataRecommendation.vue')\r\nconst DataRecommendationOpen = () => import('customize/Intelligentize/DataRecommendation/DataRecommendationOpen.vue')\r\nconst AssistedWriting = () => import('customize/AssistedWriting/AssistedWriting.vue')\r\nconst AssistedWritingRD = () => import('customize/AssistedWriting/AssistedWritingRD.vue')\r\nconst AiReportGenera = () => import('customize/AiReportGenera/AiReportGenera.vue')\r\nconst AiReportGeneraList = () => import('customize/AiReportGenera/AiReportGeneraList.vue')\r\nconst AiReportGeneraView = () => import('customize/AiReportGenera/AiReportGeneraView.vue')\r\nconst HotspotPush = () => import('customize/HotspotPush/HotspotPush.vue')\r\nconst WarningDetail = () => import('customize/HotspotPush/WarningDetail.vue')\r\nconst VersionComparisonAi = () => import('customize/Intelligentize/VersionComparison/VersionComparisonAi.vue')\r\nconst GlobalAiToolBox = () => import('customize/AiToolBox/GlobalAiToolBox.vue')\r\nconst AiToolBox = () => import('customize/AiToolBox/AiToolBox.vue')\r\nconst AiUseStatistics = () => import('customize/AiUseStatistics/AiUseStatistics.vue')\r\nconst AiHotWordList = () => import('customize/AiUseStatistics/AiHotWord/AiHotWordList.vue')\r\n// 议题符合性审查\r\nconst complianceReviewTopics = () => import('customize/complianceReviewTopics/complianceReviewTopics.vue')\r\nconst page = [\r\n  { path: '/PostMam', name: 'PostMam', component: PostMam, meta: { moduleName: 'main' } },\r\n  { path: '/WorkBench', name: 'WorkBench', component: WorkBench, meta: { moduleName: 'main' } },\r\n  { path: '/OtherRouter', name: 'OtherRouter', component: OtherRouter, meta: { moduleName: 'main' } },\r\n  { path: '/NotFoundPage', name: 'NotFoundPage', component: NotFoundPage, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/NoneAccessAuthority',\r\n    name: 'NoneAccessAuthority',\r\n    component: NoneAccessAuthority,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/GlobalHome', name: 'GlobalHome', component: GlobalHome, meta: { moduleName: 'main' } },\r\n  // { path: '/GlobalChat', name: 'GlobalChat', component: GlobalChat, meta: { moduleName: 'main' } },\r\n  { path: '/HotSpot', name: 'HotSpot', component: HotSpot, meta: { moduleName: 'main' } },\r\n  { path: '/BackgroundCheck', name: 'BackgroundCheck', component: BackgroundCheck, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/BackgroundCheckInfo',\r\n    name: 'BackgroundCheckInfo',\r\n    component: BackgroundCheckInfo,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/BackgroundCheckDetails',\r\n    name: 'BackgroundCheckDetails',\r\n    component: BackgroundCheckDetails,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/PublicSentimentInfo',\r\n    name: 'PublicSentimentInfo',\r\n    component: PublicSentimentInfo,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/AnswerManage', name: 'AnswerManage', component: AnswerManage, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/QuestionsAndAnswers',\r\n    name: 'QuestionsAndAnswers',\r\n    component: QuestionsAndAnswers,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/homePage', name: 'homePage', component: homePage, meta: { moduleName: 'main' } },\r\n  { path: '/WorkBenchCopy', name: 'WorkBenchCopy', component: WorkBenchCopy, meta: { moduleName: 'main' } },\r\n  { path: '/DevelopContent', name: 'DevelopContent', component: DevelopContent, meta: { moduleName: 'main' } },\r\n  { path: '/VersionComparison', name: 'VersionComparison', component: VersionComparison, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/DataRecommendation',\r\n    name: 'DataRecommendation',\r\n    component: DataRecommendation,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/complianceReviewTopics',\r\n    name: 'complianceReviewTopics',\r\n    component: complianceReviewTopics,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/AssistedWriting', name: 'AssistedWriting', component: AssistedWriting, meta: { moduleName: 'main' } },\r\n  { path: '/AssistedWritingRD', name: 'AssistedWritingRD', component: AssistedWritingRD, meta: { moduleName: 'main' } },\r\n  { path: '/AiReportGenera', name: 'AiReportGenera', component: AiReportGenera, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/AiReportGeneraList',\r\n    name: 'AiReportGeneraList',\r\n    component: AiReportGeneraList,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/AiReportGeneraView',\r\n    name: 'AiReportGeneraView',\r\n    component: AiReportGeneraView,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  { path: '/HotspotPush', name: 'HotspotPush', component: HotspotPush, meta: { moduleName: 'main' } },\r\n  { path: '/WarningDetail', name: 'WarningDetail', component: WarningDetail, meta: { moduleName: 'main' } },\r\n  {\r\n    path: '/AiToolBox',\r\n    name: 'AiToolBox',\r\n    component: AiToolBox,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/AiUseStatistics',\r\n    name: 'AiUseStatistics',\r\n    component: AiUseStatistics,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/AiHotWordList',\r\n    name: 'AiHotWordList',\r\n    component: AiHotWordList,\r\n    meta: { moduleName: 'main' }\r\n  }\r\n]\r\nconst customizeRouter = [\r\n  { path: '/HomeLayout', name: 'HomeLayout', component: HomeLayout, meta: { moduleName: 'verify' } },\r\n  { path: '/LoginView', name: 'LoginView', component: LoginView },\r\n  { path: '/:pathMatch(.*)*', name: 'LayoutContainer', component: LayoutContainer, children: page },\r\n  { path: '/GlobalLayoutChat', name: 'GlobalLayoutChat', component: GlobalLayoutChat, meta: { moduleName: 'main' } },\r\n  { path: '/UnifyLogin', name: 'UnifyLogin', component: UnifyLogin, meta: { moduleName: 'verify' } },\r\n  { path: '/LoginViewOne', name: 'LoginViewOne', component: LoginViewOne, meta: { moduleName: 'verify' } },\r\n  { path: '/LoginViewRegion', name: 'LoginViewRegion', component: LoginViewRegion, meta: { moduleName: 'verify' } },\r\n  { path: '/GlobalAiToolBox', name: 'GlobalAiToolBox', component: GlobalAiToolBox, meta: { moduleName: 'main' } },\r\n  { path: '/DataRecommendationOpen', name: 'DataRecommendationOpen', component: DataRecommendationOpen },\r\n  {\r\n    path: '/DevelopContentPublic',\r\n    name: 'DevelopContentPublic',\r\n    component: DevelopContent,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/VersionComparisonPublic',\r\n    name: 'VersionComparisonPublic',\r\n    component: VersionComparison,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/DataRecommendationPublic',\r\n    name: 'DataRecommendationPublic',\r\n    component: DataRecommendation,\r\n    meta: { moduleName: 'main' }\r\n  },\r\n  {\r\n    path: '/VersionComparisonAi',\r\n    name: 'VersionComparisonAi',\r\n    component: VersionComparisonAi,\r\n    meta: { moduleName: 'main' }\r\n  }\r\n]\r\nexport default customizeRouter\r\n"], "mappings": "AAAA,IAAMA,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS,MAAM,CAAC,qCAAqC,CAAC;AAAA,GAAC;AACvE,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OAAS,MAAM,CAAC,uCAAuC,CAAC;AAAA;AACvE;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,+CAA+C,CAAC;AAAA;AACrF,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS,MAAM,CAAC,qCAAqC,CAAC;AAAA;AACtE,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAC5E,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,+CAA+C,CAAC;AAAA;AACrF,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA;EAAA,OAAS,MAAM,CAAC,+BAA+B,CAAC;AAAA;AAC7D,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OAAS,MAAM,CAAC,mCAAmC,CAAC;AAAA;AACnE,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,uCAAuC,CAAC;AAAA;AAC3E,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAAS,MAAM,CAAC,uCAAuC,CAAC;AAAA;AACzE,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAC5E,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,uDAAuD,CAAC;AAAA;AACjG,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS,MAAM,CAAC,qCAAqC,CAAC;AAAA;AACtE,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA;EAAA,OAAS,MAAM,CAAC,+BAA+B,CAAC;AAAA;AAC7D,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,+CAA+C,CAAC;AAAA;AACrF,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,mDAAmD,CAAC;AAAA;AAC7F,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA;EAAA,OAAS,MAAM,CAAC,sDAAsD,CAAC;AAAA;AACnG,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,uDAAuD,CAAC;AAAA;AACjG,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAAS,MAAM,CAAC,gDAAgD,CAAC;AAAA;AACnF,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,uDAAuD,CAAC;AAAA;AACjG;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA;EAAA,OAAS,MAAM,CAAC,oCAAoC,CAAC;AAAA;;AAEnE;AACA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OAAS,MAAM,CAAC,2CAA2C,CAAC;AAAA;AAClF,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAAS,MAAM,CAAC,4DAA4D,CAAC;AAAA;AACjG,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;EAAA,OAAS,MAAM,CAAC,kEAAkE,CAAC;AAAA;AAC1G,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,oEAAoE,CAAC;AAAA;AAC7G,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA;EAAA,OAAS,MAAM,CAAC,wEAAwE,CAAC;AAAA;AACrH,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,+CAA+C,CAAC;AAAA;AACrF,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA;AACzF,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAAS,MAAM,CAAC,6CAA6C,CAAC;AAAA;AAClF,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA;AAC1F,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA;AAC1F,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA;EAAA,OAAS,MAAM,CAAC,uCAAuC,CAAC;AAAA;AACzE,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAC7E,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,oEAAoE,CAAC;AAAA;AAC9G,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,yCAAyC,CAAC;AAAA;AAC/E,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OAAS,MAAM,CAAC,mCAAmC,CAAC;AAAA;AACnE,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,+CAA+C,CAAC;AAAA;AACrF,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,uDAAuD,CAAC;AAAA;AAC3F;AACA,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA;EAAA,OAAS,MAAM,CAAC,6DAA6D,CAAC;AAAA;AAC1G,IAAMC,IAAI,GAAG,CACX;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEpC,OAAO;EAAEqC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACvF;EAAEJ,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEnC,SAAS;EAAEoC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC7F;EAAEJ,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEjC,WAAW;EAAEkC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACnG;EAAEJ,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAEhC,YAAY;EAAEiC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACtG;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAE/B,mBAAmB;EAC9BgC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAE9B,UAAU;EAAE+B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC;AAChG;AACA;EAAEJ,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAE7B,OAAO;EAAE8B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACvF;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAE5B,eAAe;EAAE6B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAE3B,mBAAmB;EAC9B4B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,yBAAyB;EAC/BC,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,EAAE1B,sBAAsB;EACjC2B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEzB,mBAAmB;EAC9B0B,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAExB,YAAY;EAAEyB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACtG;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEvB,mBAAmB;EAC9BwB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEtB,QAAQ;EAAEuB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC1F;EAAEJ,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAElC,aAAa;EAAEmC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACzG;EAAEJ,IAAI,EAAE,iBAAiB;EAAEC,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEpB,cAAc;EAAEqB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC5G;EAAEJ,IAAI,EAAE,oBAAoB;EAAEC,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAEnB,iBAAiB;EAAEoB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACrH;EACEJ,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAElB,kBAAkB;EAC7BmB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,yBAAyB;EAC/BC,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,EAAEJ,sBAAsB;EACjCK,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEhB,eAAe;EAAEiB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EAAEJ,IAAI,EAAE,oBAAoB;EAAEC,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAEf,iBAAiB;EAAEgB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACrH;EAAEJ,IAAI,EAAE,iBAAiB;EAAEC,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEd,cAAc;EAAEe,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC5G;EACEJ,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEb,kBAAkB;EAC7Bc,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEZ,kBAAkB;EAC7Ba,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EAAEJ,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEX,WAAW;EAAEY,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACnG;EAAEJ,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEV,aAAa;EAAEW,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EACzG;EACEJ,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEP,SAAS;EACpBQ,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEN,eAAe;EAC1BO,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEL,aAAa;EACxBM,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,CACF;AACD,IAAMC,eAAe,GAAG,CACtB;EAAEL,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAE1C,UAAU;EAAE2C,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAS;AAAE,CAAC,EAClG;EAAEJ,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEzC;AAAU,CAAC,EAC/D;EAAEuC,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAExC,eAAe;EAAE4C,QAAQ,EAAEP;AAAK,CAAC,EACjG;EAAEC,IAAI,EAAE,mBAAmB;EAAEC,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAErB,gBAAgB;EAAEsB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAClH;EAAEJ,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEvC,UAAU;EAAEwC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAS;AAAE,CAAC,EAClG;EAAEJ,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAEtC,YAAY;EAAEuC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAS;AAAE,CAAC,EACxG;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAErC,eAAe;EAAEsC,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAS;AAAE,CAAC,EACjH;EAAEJ,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAER,eAAe;EAAES,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAAE,CAAC,EAC/G;EAAEJ,IAAI,EAAE,yBAAyB;EAAEC,IAAI,EAAE,wBAAwB;EAAEC,SAAS,EAAEjB;AAAuB,CAAC,EACtG;EACEe,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAEpB,cAAc;EACzBqB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAEnB,iBAAiB;EAC5BoB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAElB,kBAAkB;EAC7BmB,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,EACD;EACEJ,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAET,mBAAmB;EAC9BU,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAO;AAC7B,CAAC,CACF;AACD,eAAeC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}