{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestPreApplyForAdjust\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table = _resolveComponent(\"xyl-global-table\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_export_excel = _resolveComponent(\"xyl-export-excel\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList,\n    data: $setup.tableHead,\n    ref: \"queryRef\"\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\", \"data\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect,\n    onSortChange: $setup.handleSortChange,\n    \"header-cell-class-name\": $setup.handleHeaderClass\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_xyl_global_table, {\n        tableHead: $setup.tableHead,\n        onTableClick: $setup.handleTableClick,\n        noTooltip: ['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\n      }, {\n        mainHandleOffices: _withCtx(function (scope) {\n          var _scope$row$mainHandle;\n          return [_createTextVNode(_toDisplayString((_scope$row$mainHandle = scope.row.mainHandleOffices) === null || _scope$row$mainHandle === void 0 ? void 0 : _scope$row$mainHandle.map(function (v) {\n            return v.flowHandleOfficeName;\n          }).join('、')), 1 /* TEXT */)];\n        }),\n        assistHandleOffices: _withCtx(function (scope) {\n          var _scope$row$assistHand;\n          return [_createTextVNode(_toDisplayString((_scope$row$assistHand = scope.row.assistHandleOffices) === null || _scope$row$assistHand === void 0 ? void 0 : _scope$row$assistHand.map(function (v) {\n            return v.flowHandleOfficeName;\n          }).join('、')), 1 /* TEXT */)];\n        }),\n        publishHandleOffices: _withCtx(function (scope) {\n          var _scope$row$publishHan;\n          return [_createTextVNode(_toDisplayString((_scope$row$publishHan = scope.row.publishHandleOffices) === null || _scope$row$publishHan === void 0 ? void 0 : _scope$row$publishHan.map(function (v) {\n            return v.flowHandleOfficeName;\n          }).join('、')), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"tableHead\"]), _createVNode(_component_xyl_global_table_button, {\n        editCustomTableHead: $setup.handleEditorCustom\n      }, null, 8 /* PROPS */, [\"editCustomTableHead\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\", \"onSortChange\", \"header-cell-class-name\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.exportShow,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.exportShow = $event;\n    }),\n    name: \"导出Excel\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_xyl_export_excel, {\n        name: \"预交办提案\",\n        exportId: $setup.exportId,\n        params: $setup.exportParams,\n        module: \"proposalExportExcel\",\n        tableId: \"id_prop_proposal_preAssign_adjust\",\n        onExcelCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"exportId\", \"params\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "data", "tableHead", "ref", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "onSortChange", "handleSortChange", "handleHeaderClass", "default", "_component_el_table_column", "type", "width", "fixed", "_component_xyl_global_table", "onTableClick", "handleTableClick", "noTooltip", "mainHandleOffices", "scope", "_scope$row$mainHandle", "_createTextVNode", "_toDisplayString", "row", "map", "v", "flowHandleOfficeName", "join", "assistHandleOffices", "_scope$row$assistHand", "publishHandleOffices", "_scope$row$publishHan", "_component_xyl_global_table_button", "editCustomTableHead", "handleEditorCustom", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "exportShow", "name", "_component_xyl_export_excel", "exportId", "params", "exportParams", "module", "tableId", "onExcelCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPreApplyForAdjust\\SuggestPreApplyForAdjust.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestPreApplyForAdjust\">\r\n    <xyl-search-button\r\n      @queryClick=\"handleQuery\"\r\n      @resetClick=\"handleReset\"\r\n      @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\"\r\n      :data=\"tableHead\"\r\n      ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table\r\n        ref=\"tableRef\"\r\n        row-key=\"id\"\r\n        :data=\"tableData\"\r\n        @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\"\r\n        @sort-change=\"handleSortChange\"\r\n        :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table\r\n          :tableHead=\"tableHead\"\r\n          @tableClick=\"handleTableClick\"\r\n          :noTooltip=\"['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']\">\r\n          <template #mainHandleOffices=\"scope\">\r\n            {{ scope.row.mainHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}\r\n          </template>\r\n          <template #assistHandleOffices=\"scope\">\r\n            {{ scope.row.assistHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}\r\n          </template>\r\n          <template #publishHandleOffices=\"scope\">\r\n            {{ scope.row.publishHandleOffices?.map((v) => v.flowHandleOfficeName).join('、') }}\r\n          </template>\r\n        </xyl-global-table>\r\n        <xyl-global-table-button :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination\r\n        v-model:currentPage=\"pageNo\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleQuery\"\r\n        @current-change=\"handleQuery\"\r\n        :total=\"totals\"\r\n        background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\" name=\"导出Excel\">\r\n      <xyl-export-excel\r\n        name=\"预交办提案\"\r\n        :exportId=\"exportId\"\r\n        :params=\"exportParams\"\r\n        module=\"proposalExportExcel\"\r\n        tableId=\"id_prop_proposal_preAssign_adjust\"\r\n        @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestPreApplyForAdjust' }\r\n</script>\r\n<script setup>\r\nimport { onActivated } from 'vue'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nconst buttonList = [\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n]\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel\r\n} = GlobalTable({ tableId: 'id_prop_proposal_preAssign_adjust', tableApi: 'suggestionList' })\r\n\r\nonActivated(() => {\r\n  handleQuery()\r\n})\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams())\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '建议详情', path: '/proposal/SuggestDetail', query: { id: item.id, type: 'adjust' } }\r\n  })\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  exportShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestPreApplyForAdjust {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;EAY9BA,KAAK,EAAC;AAAa;;EA2BnBA,KAAK,EAAC;AAAkB;;;;;;;;;;;uBAvC/BC,mBAAA,CA2DM,OA3DNC,UA2DM,GA1DJC,YAAA,CAUoBC,4BAAA;IATjBC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IACvBC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IACvBC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC1BC,UAAU,EAAEN,MAAA,CAAAM,UAAU;IACtBC,IAAI,EAAEP,MAAA,CAAAQ,SAAS;IAChBC,GAAG,EAAC;;IACOC,MAAM,EAAAC,QAAA,CACf;MAAA,OAAwF,CAAxFd,YAAA,CAAwFe,mBAAA;QAVhGC,UAAA,EAU2Bb,MAAA,CAAAc,OAAO;QAVlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAU2BhB,MAAA,CAAAc,OAAO,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAV/DC,SAAA,CAUuEnB,MAAA,CAAAC,WAAW;QAAEmB,SAAS,EAAT;;;IAVpFC,CAAA;+CAaIC,mBAAA,CA0BM,OA1BNC,UA0BM,GAzBJ1B,YAAA,CAwBW2B,mBAAA;IAvBTf,GAAG,EAAC,UAAU;IACd,SAAO,EAAC,IAAI;IACXF,IAAI,EAAEP,MAAA,CAAAyB,SAAS;IACfC,QAAM,EAAE1B,MAAA,CAAA2B,iBAAiB;IACzBC,WAAU,EAAE5B,MAAA,CAAA2B,iBAAiB;IAC7BE,YAAW,EAAE7B,MAAA,CAAA8B,gBAAgB;IAC7B,wBAAsB,EAAE9B,MAAA,CAAA+B;;IArBjCC,OAAA,EAAArB,QAAA,CAsBQ;MAAA,OAAuE,CAAvEd,YAAA,CAAuEoC,0BAAA;QAAtDC,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/DvC,YAAA,CAamBwC,2BAAA;QAZhB7B,SAAS,EAAER,MAAA,CAAAQ,SAAS;QACpB8B,YAAU,EAAEtC,MAAA,CAAAuC,gBAAgB;QAC5BC,SAAS,EAAE;;QACDC,iBAAiB,EAAA9B,QAAA,CAC1B,UAA+E+B,KAD9C;UAAA,IAAAC,qBAAA;UAAA,QA3B7CC,gBAAA,CAAAC,gBAAA,EAAAF,qBAAA,GA4BeD,KAAK,CAACI,GAAG,CAACL,iBAAiB,cAAAE,qBAAA,uBAA3BA,qBAAA,CAA6BI,GAAG,WAAEC,CAAC;YAAA,OAAKA,CAAC,CAACC,oBAAoB;UAAA,GAAEC,IAAI,sB;;QAE9DC,mBAAmB,EAAAxC,QAAA,CAC5B,UAAiF+B,KAD9C;UAAA,IAAAU,qBAAA;UAAA,QA9B/CR,gBAAA,CAAAC,gBAAA,EAAAO,qBAAA,GA+BeV,KAAK,CAACI,GAAG,CAACK,mBAAmB,cAAAC,qBAAA,uBAA7BA,qBAAA,CAA+BL,GAAG,WAAEC,CAAC;YAAA,OAAKA,CAAC,CAACC,oBAAoB;UAAA,GAAEC,IAAI,sB;;QAEhEG,oBAAoB,EAAA1C,QAAA,CAC7B,UAAkF+B,KAD9C;UAAA,IAAAY,qBAAA;UAAA,QAjChDV,gBAAA,CAAAC,gBAAA,EAAAS,qBAAA,GAkCeZ,KAAK,CAACI,GAAG,CAACO,oBAAoB,cAAAC,qBAAA,uBAA9BA,qBAAA,CAAgCP,GAAG,WAAEC,CAAC;YAAA,OAAKA,CAAC,CAACC,oBAAoB;UAAA,GAAEC,IAAI,sB;;QAlCtF7B,CAAA;wCAqCQxB,YAAA,CAA6F0D,kCAAA;QAAnEC,mBAAmB,EAAExD,MAAA,CAAAyD;MAAkB,iD;;IArCzEpC,CAAA;sGAwCIC,mBAAA,CAUM,OAVNoC,UAUM,GATJ7D,YAAA,CAQe8D,wBAAA;IAPLC,WAAW,EAAE5D,MAAA,CAAA6D,MAAM;IA1CnC,wBAAA9C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA0C6BhB,MAAA,CAAA6D,MAAM,GAAA7C,MAAA;IAAA;IACnB,WAAS,EAAEhB,MAAA,CAAA8D,QAAQ;IA3CnC,qBAAA/C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2C2BhB,MAAA,CAAA8D,QAAQ,GAAA9C,MAAA;IAAA;IAC1B,YAAU,EAAEhB,MAAA,CAAA+D,SAAS;IACtBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAEjE,MAAA,CAAAC,WAAW;IACxBiE,eAAc,EAAElE,MAAA,CAAAC,WAAW;IAC3BkE,KAAK,EAAEnE,MAAA,CAAAoE,MAAM;IACdC,UAAU,EAAV;qHAEJxE,YAAA,CAQmByE,2BAAA;IA3DvBzD,UAAA,EAmD+Bb,MAAA,CAAAuE,UAAU;IAnDzC,uBAAAxD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAmD+BhB,MAAA,CAAAuE,UAAU,GAAAvD,MAAA;IAAA;IAAEwD,IAAI,EAAC;;IAnDhDxC,OAAA,EAAArB,QAAA,CAoDM;MAAA,OAM+C,CAN/Cd,YAAA,CAM+C4E,2BAAA;QAL7CD,IAAI,EAAC,OAAO;QACXE,QAAQ,EAAE1E,MAAA,CAAA0E,QAAQ;QAClBC,MAAM,EAAE3E,MAAA,CAAA4E,YAAY;QACrBC,MAAM,EAAC,qBAAqB;QAC5BC,OAAO,EAAC,mCAAmC;QAC1CC,eAAa,EAAE/E,MAAA,CAAAgF;;;IA1DxB3D,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}