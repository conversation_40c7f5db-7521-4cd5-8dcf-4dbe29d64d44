<template>
  <el-scrollbar class="GlobalChatViewWindow">
    <div class="GlobalChatViewWindowBox">
      <div class="GlobalChatViewWindowBody" v-if="chatInfo?.type === 1">
        <div class="GlobalChatViewWindowUser">
          <el-image :src="imgUrl(chatInfo.chatObjectInfo.img)" fit="cover" draggable="false" />
          <div class="GlobalChatViewWindowUserName ellipsis">{{ chatInfo.chatObjectInfo.name }}</div>
        </div>
        <div class="GlobalChatViewWindowUser" @click="handleCreateGroup">
          <div class="GlobalChatViewWindowUserControls">
            <el-icon>
              <Plus />
            </el-icon>
          </div>
        </div>
      </div>
      <div class="GlobalChatViewWindowBody" v-if="chatInfo?.type === 3">
        <div class="GlobalChatViewWindowUserInput">
          <el-input v-model="keyword" :prefix-icon="Search" placeholder="搜索群成员" clearable />
        </div>
        <div class="GlobalChatViewWindowUser" v-for="item in groupUser" :key="item.accountId">
          <div class="GlobalChatViewWindowUserLogo" v-if="item.isOwner">群主</div>
          <el-image :src="imgUrl(item.photo || item.headImg)" fit="cover" draggable="false" />
          <div class="GlobalChatViewWindowUserName ellipsis">{{ item.userName }}</div>
        </div>
        <div class="GlobalChatViewWindowUser" @click="handleGroupAddUser" v-if="!keyword">
          <div class="GlobalChatViewWindowUserControls">
            <el-icon>
              <Plus />
            </el-icon>
          </div>
        </div>
        <div class="GlobalChatViewWindowUser" @click="handleGroupDelUser" v-if="isOwner && !keyword">
          <div class="GlobalChatViewWindowUserControls">
            <el-icon>
              <Minus />
            </el-icon>
          </div>
        </div>
        <div class="GlobalChatViewWindowUserButton" @click="isAllUser = !isAllUser"
          v-if="isUserLength && !keyword && !isAllUser">展开更多
          <el-icon>
            <ArrowDown />
          </el-icon>
        </div>
        <div class="GlobalChatViewWindowUserButton" @click="isAllUser = !isAllUser"
          v-if="isUserLength && !keyword && isAllUser">收起
          <el-icon>
            <ArrowUp />
          </el-icon>
        </div>
      </div>
      <div class="GlobalChatViewWindowControls" v-if="chatInfo?.type === 3">
        <div class="GlobalChatViewWindowControlsItem" @click="handleGroupName">
          <div>群组名称</div>
          <div class="GlobalChatViewWindowGroupName ellipsis">
            {{ chatInfo.chatObjectInfo.name }}
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
        <div class="GlobalChatViewWindowControlsItem" @click="handleGroupQr">
          <div>群组二维码</div>
          <div class="GlobalChatViewWindowGroupName ellipsis">
            <div v-html="qrCodeIcon"></div>
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
        <div class="GlobalChatViewWindowControlsItem" @click="handleGroupAnnouncement">
          <div>群公告</div>
          <div class="GlobalChatViewWindowGroupName ellipsis">
            {{ groupInfo.callBoard }}
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
        <div class="GlobalChatViewWindowControlsItem" @click="handleGroupTransfer" v-if="isOwner">
          <div>转让群主</div>
          <div class="GlobalChatViewWindowGroupName ellipsis">
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
      <div class="GlobalChatViewWindowControls">
        <div class="GlobalChatViewWindowControlsItem">
          <div>消息免打扰</div>
          <el-switch v-model="notification" :active-value="1" :inactive-value="2" size="small" @change="handleChange" />
        </div>
        <div class="GlobalChatViewWindowControlsItem">
          <div>置顶聊天</div>
          <el-switch v-model="isTop" size="small" @change="handleIsTopChange" />
        </div>
        <div class="GlobalChatViewWindowControlsItem">
          <div>是否禁言</div>
          <el-switch v-model="isSpeak" size="small" />
        </div>
      </div>
      <div class="GlobalChatViewWindowControls">
        <div class="GlobalChatViewWindowClearAway" @click="handleClearAway">清除聊天记录</div>
      </div>
      <div class="GlobalChatViewWindowControls" v-if="chatInfo?.type === 3">
        <div class="GlobalChatViewWindowClearAway" @click="handleQuitGroup">退出群组</div>
      </div>
      <div class="GlobalChatViewWindowControls" v-if="isOwner && chatInfo?.type === 3">
        <div class="GlobalChatViewWindowClearAway" @click="handleDissolveGroup">解散群组</div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script>
export default { name: 'GlobalChatViewWindow' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import * as RongIMLib from '@rongcloud/imlib-next'
import { user, appOnlyHeader } from 'common/js/system_var.js'
import { qrCodeIcon } from '../../js/icon.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
const store = useStore()
const props = defineProps({ chatInfo: { type: Object, default: () => ({}) }, groupUser: { type: Array, default: () => ([]) } })
const emit = defineEmits(['refresh', 'callback'])
const rongCloudUrl = computed(() => store.getters.getRongCloudUrl)
const isPrivatization = computed(() => store.getters.getIsPrivatization)
const chatInfo = computed(() => props.chatInfo)
const keyword = ref('')
const isOwner = computed(() => {
  let show = false
  for (let index = 0; index < props.groupUser.length; index++) {
    const item = props.groupUser[index]
    if (item.isOwner && user.value?.accountId === item.accountId) show = true
  }
  return show
})
const isUserLength = computed(() => props.groupUser.length > (isOwner.value ? 14 : 15))
const isAllUser = ref(false)
const groupUser = computed(() => {
  if (props.groupUser.length > (isOwner.value ? 14 : 15) && !isAllUser.value && !keyword.value) {
    return props.groupUser.slice(0, (isOwner.value ? 14 : 15))
  } else {
    return props.groupUser.filter(v => v.userName?.toLowerCase()?.includes(keyword.value?.toLowerCase()))
  }
})
const groupInfo = ref({})
const isTop = ref(true)
const notification = ref(2)

const isSpeak = ref(false)
// 图片地址拼接组合
const imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')
onMounted(() => {
  isTop.value = chatInfo.value.isTop
  getNotificationStatus(chatInfo.value.type, chatInfo.value.id)
  if (chatInfo.value.type === 3) chatGroupInfo(chatInfo.value.id)
})
const chatGroupInfo = async (id) => {
  const { data } = await api.chatGroupInfo({ detailId: id.slice(appOnlyHeader.value.length) })
  groupInfo.value = data
}
const getNotificationStatus = async (conversationType, targetId) => {
  const { code, data } = await RongIMLib.getConversationNotificationStatus({ conversationType, targetId })
  if (!code) notification.value = data
}
const handleChange = async () => {
  const { code } = await RongIMLib.setConversationNotificationStatus({ conversationType: chatInfo.value.type, targetId: chatInfo.value.id }, notification.value)
  if (!code) handleRefresh()
}
const handleIsTopChange = async () => {
  const { code } = await RongIMLib.setConversationToTop({ conversationType: chatInfo.value.type, targetId: chatInfo.value.id }, isTop.value)
  if (!code) handleRefresh()
}
const handleClearAway = () => {
  ElMessageBox.confirm('此操作将清除聊天记录, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => { clearHistoryMessages() }).catch(() => { ElMessage({ type: 'info', message: '已取消清除' }) })
}
const clearHistoryMessages = async () => {
  const { code } = await RongIMLib.clearHistoryMessages({ conversationType: chatInfo.value.type, targetId: chatInfo.value.id }, chatInfo.value.sentTime)
  if (!code) handleRefresh()
}
const handleCreateGroup = () => {
  emit('callback', 'create', chatInfo.value)
}
const handleGroupAddUser = () => {
  emit('callback', 'add', chatInfo.value)
}
const handleGroupDelUser = () => {
  emit('callback', 'del', chatInfo.value)
}
const handleGroupName = () => {
  if (!isOwner.value) return
  emit('callback', 'name', chatInfo.value)
}
const handleGroupQr = () => {
  emit('callback', 'qr', chatInfo.value)
}
const handleGroupAnnouncement = () => {
  emit('callback', 'announcement', chatInfo.value, isOwner.value)
}
const handleGroupTransfer = () => {
  emit('callback', 'transfer', chatInfo.value)
}

const handleQuitGroup = () => {
  if (isOwner.value) return ElMessage({ type: 'warning', message: '退出群组前请先转让群主！' })
  ElMessageBox.confirm('此操作将退出当前群组, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => { chatGroupEdit() }).catch(() => { ElMessage({ type: 'info', message: '已取消退出' }) })
}
const chatGroupEdit = async () => {
  if (isOwner.value) return ElMessage({ type: 'warning', message: '退出群组前请先转让群主！' })
  const memberUserIds = groupInfo.value.memberUserIds.filter(v => (user.value?.accountId !== v))
  const { code } = await api.chatGroupEdit({
    form: { id: chatInfo.value?.chatObjectInfo?.id, groupName: groupInfo.value.groupName },
    ownerUserId: groupInfo.value.ownerUserId, memberUserIds: memberUserIds
  })
  if (code === 200) handleRongCloudGroup([user.value?.accountId])
}
const handleDissolveGroup = () => {
  ElMessageBox.confirm('此操作将解散当前群组, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => { chatGroupDel() }).catch(() => { ElMessage({ type: 'info', message: '已取消解散' }) })
}
const chatGroupDel = async () => {
  const { code } = await api.chatGroupDel({ ids: [chatInfo.value?.chatObjectInfo?.id] })
  if (code === 200) handleClearAway()
}
const handleRongCloudGroup = async (userIds) => {
  const { code } = await api.rongCloud(rongCloudUrl.value, {
    type: 'quitGroup',
    userIds: userIds.join(','),
    groupId: chatInfo.value.id,
    groupName: groupInfo.value.groupName,
    environment: 1
  }, isPrivatization.value)
  if (code === 200) {
    const sendMessageData = { name: `${user.value?.userName} 已退出群聊`, data: `${user.value?.userName}|OUI|${user.value?.accountId}|| 已退出群聊` }
    emit('callback', 'quit', sendMessageData)
  }
}
const handleRefresh = () => { emit('refresh') }
</script>
<style lang="scss">
.GlobalChatViewWindow {
  width: 320px;
  height: 100%;

  .GlobalChatViewWindowBox {
    width: 320px;
    padding: 20px;
  }

  .GlobalChatViewWindowBody {
    display: flex;
    flex-wrap: wrap;

    .GlobalChatViewWindowUserInput {
      width: 100%;
      padding-bottom: 20px;

      .zy-el-input {
        width: 100%;
        height: var(--zy-height-routine);
      }
    }

    .GlobalChatViewWindowUserButton {
      width: 100%;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 0 12px 0;
      cursor: pointer;

      .zy-el-icon {
        margin-left: 2px;
        margin-bottom: 1px;
      }
    }
  }

  .GlobalChatViewWindowUser {
    width: 25%;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-bottom: 10px;
    position: relative;

    .GlobalChatViewWindowUserControls {
      width: 42px;
      height: 42px;
      border-radius: 50%;
      border: 2px solid var(--zy-el-text-color-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .zy-el-icon {
        font-size: 18px;
      }
    }

    .GlobalChatViewWindowUserLogo {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translate(12px, -50%);
      padding: 0px 4px;
      background: var(--zy-el-color-primary);
      border-radius: 4px;
      font-size: 12px;
      color: #fff;
      z-index: 2;
    }

    .zy-el-image {
      width: 42px;
      height: 42px;
      border-radius: 50%;
      overflow: hidden;
    }

    .GlobalChatViewWindowUserName {
      width: 100%;
      font-size: 12px;
      text-align: center;
      padding-top: 6px;
    }
  }

  .GlobalChatViewWindowControls {
    width: 100%;
    padding-top: 6px;
    background: var(--zy-el-color-info-light-9);

    .GlobalChatViewWindowControlsItem {
      width: 100%;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      line-height: 1.6;
      background: #fff;
      position: relative;

      &+.GlobalChatViewWindowControlsItem {
        &::after {
          content: "";
          width: 100%;
          height: 1px;
          position: absolute;
          top: 0;
          left: 0;
          transform: translateY(-50%);
          background: var(--zy-el-border-color);
        }
      }

      .GlobalChatViewWindowGroupName {
        width: 68%;
        min-height: 16px;
        text-align: right;
        padding-right: 16px;
        position: relative;
        cursor: pointer;

        .zy-el-icon {
          position: absolute;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
          font-size: 14px;
        }

        &>div {
          width: 100%;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          path {
            fill: var(--zy-el-text-color-primary);
          }
        }
      }
    }

    .GlobalChatViewWindowClearAway {
      width: 100%;
      font-size: 12px;
      line-height: 38px;
      color: red;
      cursor: pointer;
      text-align: center;
      background: #fff;
    }
  }
}
</style>
