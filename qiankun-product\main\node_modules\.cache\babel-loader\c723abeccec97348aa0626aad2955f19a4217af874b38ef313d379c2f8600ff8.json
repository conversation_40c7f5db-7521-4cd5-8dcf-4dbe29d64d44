{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = [\"lement-loading-text\"];\nvar _hoisted_2 = {\n  class: \"TextComparisonHead\"\n};\nvar _hoisted_3 = {\n  class: \"TextComparisonButton\"\n};\nvar _hoisted_4 = {\n  class: \"TextComparisonButtonItem\"\n};\nvar _hoisted_5 = {\n  class: \"TextComparisonButton\"\n};\nvar _hoisted_6 = {\n  class: \"TextComparisonButtonItem\"\n};\nvar _hoisted_7 = {\n  class: \"TextComparisonBody\"\n};\nvar _hoisted_8 = {\n  class: \"TextComparisonBodyLeft\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"TextComparison\",\n    \"element-loading-spinner\": $setup.svg,\n    \"lement-loading-text\": $setup.loadingText,\n    \"element-loading-svg-view-box\": \"-10, -10, 50, 50\"\n  }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleImport\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"文档导入\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"TextComparisonButtonItem\"\n  }, null, -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_5, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"TextComparisonButtonItem\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleExportWord\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"导出\")]);\n    }),\n    _: 1 /* STABLE */\n  })])])]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_TinyMceEditor, {\n    ref: \"wordRef\",\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.content = $event;\n    }),\n    setting: $setup.setting,\n    content_style: $setup.content_style\n  }, null, 8 /* PROPS */, [\"modelValue\", \"setting\", \"content_style\"])]), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"TextComparisonBodyRight\"\n  }, null, -1 /* HOISTED */))])], 8 /* PROPS */, _hoisted_1)), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "$setup", "svg", "loadingText", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_button", "type", "onClick", "handleImport", "default", "_withCtx", "_cache", "_createTextVNode", "_", "_hoisted_5", "_hoisted_6", "handleExportWord", "_hoisted_7", "_hoisted_8", "_component_TinyMceEditor", "ref", "modelValue", "content", "$event", "setting", "content_style", "_hoisted_1", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBoxFunction\\TextComparison\\TextComparison.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"TextComparison\"\r\n    v-loading=\"loading\"\r\n    :element-loading-spinner=\"svg\"\r\n    :lement-loading-text=\"loadingText\"\r\n    element-loading-svg-view-box=\"-10, -10, 50, 50\">\r\n    <div class=\"TextComparisonHead\">\r\n      <div class=\"TextComparisonButton\">\r\n        <div class=\"TextComparisonButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleImport\">文档导入</el-button>\r\n        </div>\r\n        <div class=\"TextComparisonButtonItem\"></div>\r\n      </div>\r\n      <div class=\"TextComparisonButton\">\r\n        <div class=\"TextComparisonButtonItem\"></div>\r\n        <div class=\"TextComparisonButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleExportWord\">导出</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"TextComparisonBody\">\r\n      <div class=\"TextComparisonBodyLeft\">\r\n        <TinyMceEditor ref=\"wordRef\" v-model=\"content\" :setting=\"setting\" :content_style=\"content_style\" />\r\n      </div>\r\n      <div class=\"TextComparisonBodyRight\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'TextComparison' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { setting, content_style, trigerUpload } from '../../AiToolBox/AiToolBox.js'\r\nimport { ElMessage } from 'element-plus'\r\n\r\nconst store = useStore()\r\n\r\nconst svg =\r\n  '<path class=\"path\" d=\"M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15\" style=\"stroke-width: 4px; fill: rgba(0, 0, 0, 0)\"/>'\r\n\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst wordRef = ref()\r\nconst content = ref('')\r\n\r\nconst handleImport = () => {\r\n  trigerUpload().then((file) => {\r\n    const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n    const isShow = ['doc', 'docx', 'wps'].includes(fileType)\r\n    if (!isShow) return ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'wps'].join('、')}格式!` })\r\n    loading.value = true\r\n    fileWordUpload(file)\r\n  })\r\n}\r\nconst fileWordUpload = async (file) => {\r\n  try {\r\n    const param = new FormData()\r\n    param.append('file', file)\r\n    const { data } = await api.fileword2html(param)\r\n    content.value = data\r\n      .replace(/<\\/?html[^>]*>/g, '')\r\n      .replace(/<head\\b[^<]*(?:(?!<\\/head>)<[^<]*)*<\\/head>/gi, '')\r\n      .replace(/<\\/?body[^>]*>/g, '')\r\n      .replace(/<\\/?div[^>]*>/g, '')\r\n    loading.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nconst handleExportWord = () => {\r\n  store.commit('setExportWordHtmlObj', {\r\n    code: 'exportWord',\r\n    name: '智能纠错 --- 文档导出',\r\n    key: 'content',\r\n    data: { content: content.value }\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.TextComparison {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .TextComparisonHead {\r\n    width: 100%;\r\n    padding: var(--zy-distance-two) 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .TextComparisonButton {\r\n      width: 796px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      & + .TextComparisonButton {\r\n        width: calc(100% - 840px);\r\n      }\r\n      .TextComparisonButtonItem {\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n  .TextComparisonBody {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-two) * 2)));\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: var(--zy-distance-two);\r\n    .TextComparisonBodyLeft {\r\n      width: 820px;\r\n      height: 100%;\r\n      .TinyMceEditor {\r\n        height: 100%;\r\n        .tox-tinymce {\r\n          border: none;\r\n        }\r\n        .tox-editor-header {\r\n          width: 796px;\r\n          border: 1px solid #ccc;\r\n          border-bottom: none;\r\n          margin: auto;\r\n          margin-right: 30px;\r\n        }\r\n      }\r\n    }\r\n    .TextComparisonBodyRight {\r\n      width: calc(100% - 840px);\r\n      height: 100%;\r\n      .globalTable {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";iBAAA;;EAOSA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAA0B;;EAKlCA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAA0B;;EAKpCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAwB;;;;;wCArBvCC,mBAAA,CA0BM;IAzBJD,KAAK,EAAC,gBAAgB;IAErB,yBAAuB,EAAEE,MAAA,CAAAC,GAAG;IAC5B,qBAAmB,EAAED,MAAA,CAAAE,WAAW;IACjC,8BAA4B,EAAC;MAC7BC,mBAAA,CAaM,OAbNC,UAaM,GAZJD,mBAAA,CAKM,OALNE,UAKM,GAJJF,mBAAA,CAEM,OAFNG,UAEM,GADJC,YAAA,CAAgEC,oBAAA;IAArDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEV,MAAA,CAAAW;;IAV5CC,OAAA,EAAAC,QAAA,CAU0D;MAAA,OAAIC,MAAA,QAAAA,MAAA,OAV9DC,gBAAA,CAU0D,MAAI,E;;IAV9DC,CAAA;kCAYQb,mBAAA,CAA4C;IAAvCL,KAAK,EAAC;EAA0B,4B,GAEvCK,mBAAA,CAKM,OALNc,UAKM,G,0BAJJd,mBAAA,CAA4C;IAAvCL,KAAK,EAAC;EAA0B,6BACrCK,mBAAA,CAEM,OAFNe,UAEM,GADJX,YAAA,CAAkEC,oBAAA;IAAvDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEV,MAAA,CAAAmB;;IAjB5CP,OAAA,EAAAC,QAAA,CAiB8D;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAjBhEC,gBAAA,CAiB8D,IAAE,E;;IAjBhEC,CAAA;YAqBIb,mBAAA,CAKM,OALNiB,UAKM,GAJJjB,mBAAA,CAEM,OAFNkB,UAEM,GADJd,YAAA,CAAmGe,wBAAA;IAApFC,GAAG,EAAC,SAAS;IAvBpCC,UAAA,EAuB8CxB,MAAA,CAAAyB,OAAO;IAvBrD,uBAAAX,MAAA,QAAAA,MAAA,gBAAAY,MAAA;MAAA,OAuB8C1B,MAAA,CAAAyB,OAAO,GAAAC,MAAA;IAAA;IAAGC,OAAO,EAAE3B,MAAA,CAAA2B,OAAO;IAAGC,aAAa,EAAE5B,MAAA,CAAA4B;mGAEpFzB,mBAAA,CAA2C;IAAtCL,KAAK,EAAC;EAAyB,4B,mBAzB1C+B,UAAA,K,qBAGe7B,MAAA,CAAA8B,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}