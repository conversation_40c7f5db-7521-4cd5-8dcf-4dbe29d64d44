{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createBlock as _createBlock, createTextVNode as _createTextVNode, vShow as _vShow, withDirectives as _withDirectives, resolveDynamicComponent as _resolveDynamicComponent, KeepAlive as _KeepAlive, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, Transition as _Transition } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LayoutViewOneLogo\"\n};\nvar _hoisted_2 = {\n  class: \"LayoutViewOneName\"\n};\nvar _hoisted_3 = {\n  class: \"LayoutViewOneHeaderMenu\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  class: \"LayoutViewOneHeaderMenuItem\"\n};\nvar _hoisted_5 = {\n  class: \"LayoutViewOneHeaderMenuItem\"\n};\nvar _hoisted_6 = {\n  class: \"LayoutViewOneInfo\",\n  ref: \"LayoutViewInfo\"\n};\nvar _hoisted_7 = {\n  class: \"LayoutViewOneUser\"\n};\nvar _hoisted_8 = {\n  class: \"forbidSelect\"\n};\nvar _hoisted_9 = [\"innerHTML\"];\nvar _hoisted_10 = {\n  key: 0,\n  class: \"LayoutViewOneBreadcrumb\"\n};\nvar _hoisted_11 = {\n  class: \"LayoutViewOneBody\"\n};\nvar _hoisted_12 = {\n  key: 0,\n  class: \"ConstraintEditPassWord\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  var _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  var _component_el_tabs = _resolveComponent(\"el-tabs\");\n  var _component_xyl_region = _resolveComponent(\"xyl-region\");\n  var _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  var _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  var _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  var _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  var _component_el_header = _resolveComponent(\"el-header\");\n  var _component_xyl_menu = _resolveComponent(\"xyl-menu\");\n  var _component_el_aside = _resolveComponent(\"el-aside\");\n  var _component_xyl_tab_item = _resolveComponent(\"xyl-tab-item\");\n  var _component_xyl_tab = _resolveComponent(\"xyl-tab\");\n  var _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  var _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  var _component_router_view = _resolveComponent(\"router-view\");\n  var _component_el_main = _resolveComponent(\"el-main\");\n  var _component_el_container = _resolveComponent(\"el-container\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createVNode(_component_el_container, {\n    class: \"LayoutViewOne\",\n    style: _normalizeStyle(`background: url('${$setup.layoutBg}') no-repeat;background-size: 100% 100%;`)\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_header, {\n        class: \"LayoutViewOneHeader\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", {\n            class: \"LayoutViewOneBox\",\n            ref: \"LayoutViewBox\",\n            onClick: _cache[0] || (_cache[0] = function () {\n              return $setup.WorkBenchReturn && $setup.WorkBenchReturn.apply($setup, arguments);\n            })\n          }, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_image, {\n            src: $setup.systemLogo,\n            fit: \"cover\"\n          }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_2, _toDisplayString($setup.systemNameAreaPrefix === 'true' ? $setup.regionName : '') + _toDisplayString($setup.systemName), 1 /* TEXT */)], 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_tabs, {\n            modelValue: $setup.tabMenu,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.tabMenu = $event;\n            }),\n            onTabChange: $setup.handleClick\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tabMenuData, function (item) {\n                return _openBlock(), _createBlock(_component_el_tab_pane, {\n                  key: item.id,\n                  name: item.id,\n                  label: item.name\n                }, {\n                  label: _withCtx(function () {\n                    return [item.routePath !== '/WorkBench' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _toDisplayString(item.name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.routePath === '/WorkBench' ? (_openBlock(), _createBlock(_component_el_popover, {\n                      key: 1,\n                      trigger: \"hover\",\n                      \"popper-class\": \"LayoutViewOneWorkBenchPopover\",\n                      transition: \"zy-el-zoom-in-top\"\n                    }, {\n                      reference: _withCtx(function () {\n                        return [_createElementVNode(\"div\", _hoisted_5, _toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      default: _withCtx(function () {\n                        return [_createVNode($setup[\"LayoutViewOneWorkBench\"], {\n                          id: item.id,\n                          data: item.children\n                        }, null, 8 /* PROPS */, [\"id\", \"data\"])];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1024 /* DYNAMIC_SLOTS */)) : _createCommentVNode(\"v-if\", true)];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"name\", \"label\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"onTabChange\"])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_xyl_region, {\n            modelValue: $setup.regionId,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.regionId = $event;\n            }),\n            data: $setup.area,\n            onSelect: $setup.regionSelect,\n            props: {\n              label: 'name',\n              children: 'children'\n            }\n          }, null, 8 /* PROPS */, [\"modelValue\", \"data\", \"onSelect\"]), _createVNode(_component_el_tooltip, {\n            placement: \"top\",\n            effect: \"light\",\n            offset: 6,\n            disabled: !$setup.role.length\n          }, {\n            content: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.role, function (item, index) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  class: \"LayoutViewOneRoleItem\",\n                  key: index\n                }, _toDisplayString(item), 1 /* TEXT */);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            default: _withCtx(function () {\n              return [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_image, {\n                src: $setup.user.image,\n                fit: \"cover\"\n              }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", _hoisted_8, _toDisplayString($setup.user.userName), 1 /* TEXT */)])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"disabled\"]), _createVNode($setup[\"LayoutPersonalDoList\"], null, {\n            default: _withCtx(function () {\n              return _cache[10] || (_cache[10] = [_createTextVNode(\"待办\")]);\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode($setup[\"LayoutBoxMessage\"], null, {\n            default: _withCtx(function () {\n              return _cache[11] || (_cache[11] = [_createTextVNode(\"消息\")]);\n            }),\n            _: 1 /* STABLE */\n          }), _createElementVNode(\"div\", {\n            class: \"LayoutViewOneRefresh\",\n            innerHTML: $setup.refreshIcon,\n            onClick: _cache[3] || (_cache[3] = function ($event) {\n              return $setup.handleCommand('refresh');\n            }),\n            title: \"重新加载平台\"\n          }, null, 8 /* PROPS */, _hoisted_9), _createVNode(_component_el_dropdown, {\n            onCommand: $setup.handleCommand\n          }, {\n            dropdown: _withCtx(function () {\n              return [_createVNode(_component_el_dropdown_menu, null, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_el_dropdown_item, {\n                    command: \"task\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[12] || (_cache[12] = [_createTextVNode(\"系统任务管理器\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createCommentVNode(\" <el-dropdown-item command=\\\"refresh\\\">重新加载平台</el-dropdown-item> \"), _createCommentVNode(\" <el-dropdown-item command=\\\"locale\\\">简繁切换</el-dropdown-item>\\r\\n              <el-dropdown-item command=\\\"help\\\">帮助文档</el-dropdown-item> \"), _createVNode(_component_el_dropdown_item, {\n                    command: \"edit_password\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[13] || (_cache[13] = [_createTextVNode(\"修改密码\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_el_dropdown_item, {\n                    command: \"exit\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[14] || (_cache[14] = [_createTextVNode(\"安全退出\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  })];\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            default: _withCtx(function () {\n              return [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n                class: \"LayoutOperation\"\n              }, null, -1 /* HOISTED */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onCommand\"])], 512 /* NEED_PATCH */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_container, {\n        class: \"LayoutViewOneContainer\"\n      }, {\n        default: _withCtx(function () {\n          return [_withDirectives(_createVNode(_component_el_aside, {\n            class: \"LayoutViewOneAside\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_xyl_menu, {\n                modelValue: $setup.menuId,\n                \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n                  return $setup.menuId = $event;\n                }),\n                menuData: $setup.menuData,\n                onSelect: $setup.menuClick\n              }, null, 8 /* PROPS */, [\"modelValue\", \"menuData\", \"onSelect\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.isView]]), _createVNode(_component_el_main, {\n            class: _normalizeClass([\"LayoutViewOneMain\", {\n              LayoutViewOneMainView: !$setup.isView,\n              LayoutViewOneMainBreadcrumb: !$setup.isView && $setup.tabData.length > 1\n            }])\n          }, {\n            default: _withCtx(function () {\n              return [_withDirectives(_createVNode(_component_xyl_tab, {\n                modelValue: $setup.menuId,\n                \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n                  return $setup.menuId = $event;\n                }),\n                onTabClick: $setup.tabClick,\n                onRefresh: $setup.handleRefresh,\n                onClose: $setup.handleClose,\n                onCloseOther: $setup.handleCloseOther\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tabData, function (item) {\n                    return _openBlock(), _createBlock(_component_xyl_tab_item, {\n                      key: item.id,\n                      value: item.id\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\", \"onTabClick\", \"onRefresh\", \"onClose\", \"onCloseOther\"]), [[_vShow, $setup.isView]]), !$setup.isView && $setup.tabData.length > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_el_breadcrumb, {\n                \"separator-icon\": $setup.ArrowRight\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tabData, function (item, index) {\n                    return _openBlock(), _createBlock(_component_el_breadcrumb_item, {\n                      key: `key-${item.id}`,\n                      onClick: function onClick($event) {\n                        return $setup.handleBreadcrumb(item, index);\n                      }\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"separator-icon\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_router_view, null, {\n                default: _withCtx(function (_ref) {\n                  var Component = _ref.Component;\n                  return [(_openBlock(), _createBlock(_KeepAlive, {\n                    include: $setup.keepAliveRoute\n                  }, [$setup.isMain && $setup.isRefresh ? (_openBlock(), _createBlock(_resolveDynamicComponent(Component), {\n                    key: _ctx.$route.fullPath\n                  })) : _createCommentVNode(\"v-if\", true)], 1032 /* PROPS, DYNAMIC_SLOTS */, [\"include\"]))];\n                }),\n                _: 1 /* STABLE */\n              }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.MicroApp, function (item) {\n                return _withDirectives((_openBlock(), _createBlock($setup[\"SubAppViewport\"], {\n                  key: item,\n                  name: item\n                }, null, 8 /* PROPS */, [\"name\"])), [[_vShow, !$setup.isMain && $setup.isMicroApp === item]]);\n              }), 128 /* KEYED_FRAGMENT */))])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"class\"]), $setup.whetherAiChat ? (_openBlock(), _createBlock(_component_el_aside, {\n            key: 0,\n            class: \"LayoutViewOneFloatingWindow\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_Transition, {\n                name: \"width-animation\"\n              }, {\n                default: _withCtx(function () {\n                  return [$setup.AiChatViewType ? _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n                    key: 0,\n                    class: \"LayoutViewOneFloatingWindowBody\",\n                    style: _normalizeStyle({\n                      '--ai-chat-target-width': $setup.AiChatTargetWidth\n                    })\n                  }, [_createVNode($setup[\"GlobalAiChat\"], {\n                    modelValue: $setup.AiChatWindowShow,\n                    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n                      return $setup.AiChatWindowShow = $event;\n                    })\n                  }, null, 8 /* PROPS */, [\"modelValue\"])], 4 /* STYLE */)), [[_vShow, $setup.AiChatWindowShow]]) : _createCommentVNode(\"v-if\", true)];\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.helpShow,\n        \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n          return $setup.helpShow = $event;\n        }),\n        name: \"帮助文档\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"HelpDocument\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.editPassWordShow,\n        \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n          return $setup.editPassWordShow = $event;\n        }),\n        name: \"修改密码\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"EditPassWord\"], {\n            type: $setup.verifyEditPassWord,\n            onCallback: $setup.editPassWordCallback\n          }, null, 8 /* PROPS */, [\"type\", \"onCallback\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), $setup.verifyEditPassWordShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode($setup[\"EditPassWord\"], {\n        type: $setup.verifyEditPassWord,\n        onCallback: $setup.editPassWordCallback\n      }, null, 8 /* PROPS */, [\"type\", \"onCallback\"])])) : _createCommentVNode(\"v-if\", true), $setup.isRegionSelectShow ? (_openBlock(), _createBlock($setup[\"GlobalRegionSelect\"], {\n        key: 1,\n        onCallback: $setup.regionSelect\n      }, null, 8 /* PROPS */, [\"onCallback\"])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"style\"]), _createVNode($setup[\"qusetionAnswering\"]), $setup.rongCloudToken ? (_openBlock(), _createBlock($setup[\"GlobalChatFloating\"], {\n    key: 0\n  })) : _createCommentVNode(\"v-if\", true), $setup.whetherAiChat ? (_openBlock(), _createBlock($setup[\"GlobalFloatingWindow\"], {\n    key: 1,\n    modelValue: $setup.AiChatWindowShow,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n      return $setup.AiChatWindowShow = $event;\n    }),\n    disabled: $setup.AiChatViewType\n  }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])) : _createCommentVNode(\"v-if\", true), $setup.whetherAiChat ? (_openBlock(), _createBlock($setup[\"GlobalAiControls\"], {\n    key: 2\n  })) : _createCommentVNode(\"v-if\", true), $setup.isMain && $setup.loginHintShow ? (_openBlock(), _createBlock($setup[\"suggestPop\"], {\n    key: 3\n  })) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "key", "ref", "_createElementBlock", "_Fragment", "_createVNode", "_component_el_container", "style", "_normalizeStyle", "$setup", "layoutBg", "default", "_withCtx", "_component_el_header", "_createElementVNode", "onClick", "_cache", "WorkBenchReturn", "apply", "arguments", "_hoisted_1", "_component_el_image", "src", "systemLogo", "fit", "_hoisted_2", "_toDisplayString", "systemNameAreaPrefix", "regionName", "systemName", "_hoisted_3", "_component_el_tabs", "modelValue", "tabMenu", "$event", "onTabChange", "handleClick", "_renderList", "tabMenuData", "item", "_createBlock", "_component_el_tab_pane", "id", "name", "label", "routePath", "_hoisted_4", "_createCommentVNode", "_component_el_popover", "trigger", "transition", "reference", "_hoisted_5", "data", "children", "_", "_hoisted_6", "_component_xyl_region", "regionId", "area", "onSelect", "regionSelect", "props", "_component_el_tooltip", "placement", "effect", "offset", "disabled", "role", "length", "content", "index", "_hoisted_7", "user", "image", "_hoisted_8", "userName", "_createTextVNode", "innerHTML", "refreshIcon", "handleCommand", "title", "_hoisted_9", "_component_el_dropdown", "onCommand", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_component_el_aside", "_component_xyl_menu", "menuId", "menuData", "menuClick", "<PERSON><PERSON><PERSON><PERSON>", "_component_el_main", "_normalizeClass", "LayoutViewOneMainView", "LayoutViewOneMainBreadcrumb", "tabData", "_component_xyl_tab", "onTabClick", "tabClick", "onRefresh", "handleRefresh", "onClose", "handleClose", "onCloseOther", "handleCloseOther", "_component_xyl_tab_item", "value", "_hoisted_10", "_component_el_breadcrumb", "ArrowRight", "_component_el_breadcrumb_item", "handleBreadcrumb", "_hoisted_11", "_component_router_view", "_ref", "Component", "_KeepAlive", "include", "keepAliveRoute", "is<PERSON><PERSON>", "isRefresh", "_resolveDynamicComponent", "_ctx", "$route", "fullPath", "MicroApp", "isMicroApp", "whetherAiChat", "_Transition", "AiChatViewType", "AiChatTargetWidth", "AiChatWindowShow", "_component_xyl_popup_window", "helpShow", "editPassWordShow", "type", "verifyEditPassWord", "onCallback", "editPassWordCallback", "verifyEditPassWordShow", "_hoisted_12", "isRegionSelectShow", "rongCloudToken", "loginHintShow"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutViewOne\\LayoutViewOne.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"LayoutViewOne\" :style=\"`background: url('${layoutBg}') no-repeat;background-size: 100% 100%;`\">\r\n    <el-header class=\"LayoutViewOneHeader\">\r\n      <div class=\"LayoutViewOneBox\" ref=\"LayoutViewBox\" @click=\"WorkBenchReturn\">\r\n        <div class=\"LayoutViewOneLogo\">\r\n          <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n        </div>\r\n        <div class=\"LayoutViewOneName\">{{ systemNameAreaPrefix === 'true' ? regionName : '' }}{{ systemName }}</div>\r\n      </div>\r\n      <div class=\"LayoutViewOneHeaderMenu\">\r\n        <el-tabs v-model=\"tabMenu\" @tab-change=\"handleClick\">\r\n          <el-tab-pane v-for=\"item in tabMenuData\" :key=\"item.id\" :name=\"item.id\" :label=\"item.name\">\r\n            <template #label>\r\n              <div class=\"LayoutViewOneHeaderMenuItem\" v-if=\"item.routePath !== '/WorkBench'\">{{ item.name }}</div>\r\n              <el-popover trigger=\"hover\" popper-class=\"LayoutViewOneWorkBenchPopover\" transition=\"zy-el-zoom-in-top\"\r\n                v-if=\"item.routePath === '/WorkBench'\">\r\n                <template #reference>\r\n                  <div class=\"LayoutViewOneHeaderMenuItem\">{{ item.name }}</div>\r\n                </template>\r\n                <LayoutViewOneWorkBench :id=\"item.id\" :data=\"item.children\"></LayoutViewOneWorkBench>\r\n              </el-popover>\r\n            </template>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"LayoutViewOneInfo\" ref=\"LayoutViewInfo\">\r\n        <xyl-region v-model=\"regionId\" :data=\"area\" @select=\"regionSelect\"\r\n          :props=\"{ label: 'name', children: 'children' }\"></xyl-region>\r\n        <el-tooltip placement=\"top\" effect=\"light\" :offset=\"6\" :disabled=\"!role.length\">\r\n          <template #content>\r\n            <div class=\"LayoutViewOneRoleItem\" v-for=\"(item, index) in role\" :key=\"index\">{{ item }}</div>\r\n          </template>\r\n          <div class=\"LayoutViewOneUser\">\r\n            <el-image :src=\"user.image\" fit=\"cover\" />\r\n            <span class=\"forbidSelect\">{{ user.userName }}</span>\r\n          </div>\r\n        </el-tooltip>\r\n        <LayoutPersonalDoList>待办</LayoutPersonalDoList>\r\n        <LayoutBoxMessage>消息</LayoutBoxMessage>\r\n        <div class=\"LayoutViewOneRefresh\" v-html=\"refreshIcon\" @click=\"handleCommand('refresh')\" title=\"重新加载平台\"></div>\r\n        <el-dropdown @command=\"handleCommand\">\r\n          <div class=\"LayoutOperation\"></div>\r\n          <template #dropdown>\r\n            <el-dropdown-menu>\r\n              <el-dropdown-item command=\"task\">系统任务管理器</el-dropdown-item>\r\n              <!-- <el-dropdown-item command=\"refresh\">重新加载平台</el-dropdown-item> -->\r\n              <!-- <el-dropdown-item command=\"locale\">简繁切换</el-dropdown-item>\r\n              <el-dropdown-item command=\"help\">帮助文档</el-dropdown-item> -->\r\n              <el-dropdown-item command=\"edit_password\">修改密码</el-dropdown-item>\r\n              <el-dropdown-item command=\"exit\">安全退出</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </template>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"LayoutViewOneContainer\">\r\n      <el-aside class=\"LayoutViewOneAside\" v-show=\"isView\">\r\n        <xyl-menu v-model=\"menuId\" :menuData=\"menuData\" @select=\"menuClick\"></xyl-menu>\r\n      </el-aside>\r\n      <el-main class=\"LayoutViewOneMain\"\r\n        :class=\"{ LayoutViewOneMainView: !isView, LayoutViewOneMainBreadcrumb: !isView && tabData.length > 1 }\">\r\n        <xyl-tab v-model=\"menuId\" @tab-click=\"tabClick\" @refresh=\"handleRefresh\" @close=\"handleClose\"\r\n          @closeOther=\"handleCloseOther\" v-show=\"isView\">\r\n          <xyl-tab-item v-for=\"item in tabData\" :key=\"item.id\" :value=\"item.id\">{{ item.name }}</xyl-tab-item>\r\n        </xyl-tab>\r\n        <div class=\"LayoutViewOneBreadcrumb\" v-if=\"!isView && tabData.length > 1\">\r\n          <el-breadcrumb :separator-icon=\"ArrowRight\">\r\n            <el-breadcrumb-item v-for=\"(item, index) in tabData\" :key=\"`key-${item.id}`\"\r\n              @click=\"handleBreadcrumb(item, index)\">\r\n              {{ item.name }}\r\n            </el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"LayoutViewOneBody\">\r\n          <router-view v-slot=\"{ Component }\">\r\n            <keep-alive :include=\"keepAliveRoute\">\r\n              <component v-if=\"isMain && isRefresh\" :key=\"$route.fullPath\" :is=\"Component\"></component>\r\n            </keep-alive>\r\n          </router-view>\r\n          <SubAppViewport v-for=\"item in MicroApp\" :key=\"item\" v-show=\"!isMain && isMicroApp === item\" :name=\"item\">\r\n          </SubAppViewport>\r\n        </div>\r\n      </el-main>\r\n      <el-aside class=\"LayoutViewOneFloatingWindow\" v-if=\"whetherAiChat\">\r\n        <transition name=\"width-animation\">\r\n          <div class=\"LayoutViewOneFloatingWindowBody\" :style=\"{ '--ai-chat-target-width': AiChatTargetWidth }\"\r\n            v-if=\"AiChatViewType\" v-show=\"AiChatWindowShow\">\r\n            <GlobalAiChat v-model=\"AiChatWindowShow\"></GlobalAiChat>\r\n          </div>\r\n        </transition>\r\n      </el-aside>\r\n    </el-container>\r\n    <xyl-popup-window v-model=\"helpShow\" name=\"帮助文档\">\r\n      <HelpDocument></HelpDocument>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"editPassWordShow\" name=\"修改密码\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </xyl-popup-window>\r\n    <div class=\"ConstraintEditPassWord\" v-if=\"verifyEditPassWordShow\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </div>\r\n    <GlobalRegionSelect v-if=\"isRegionSelectShow\" @callback=\"regionSelect\"></GlobalRegionSelect>\r\n  </el-container>\r\n  <qusetionAnswering></qusetionAnswering>\r\n  <GlobalChatFloating v-if=\"rongCloudToken\"></GlobalChatFloating>\r\n  <GlobalFloatingWindow v-model=\"AiChatWindowShow\" :disabled=\"AiChatViewType\" v-if=\"whetherAiChat\" />\r\n  <GlobalAiControls v-if=\"whetherAiChat\" />\r\n  <suggestPop v-if=\"isMain && loginHintShow\" />\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutViewOne' }\r\n</script>\r\n<script setup>\r\nimport { defineAsyncComponent } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport {\r\n  qiankun,\r\n  LayoutView,\r\n  ChatMethod,\r\n  AiChatMethod,\r\n  refreshIcon,\r\n  loginHintMethod\r\n} from '../LayoutView/LayoutView.js'\r\nimport { systemLogo, systemName, layoutBg, whetherAiChat, systemNameAreaPrefix } from 'common/js/system_var.js'\r\nimport { ArrowRight } from '@element-plus/icons-vue'\r\nconst HelpDocument = defineAsyncComponent(() => import('../LayoutContainer/components/HelpDocument'))\r\nconst EditPassWord = defineAsyncComponent(() => import('../LayoutContainer/components/EditPassWord'))\r\nconst LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))\r\nconst LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))\r\nconst GlobalRegionSelect = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalRegionSelect'))\r\nconst GlobalChatFloating = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalChatFloating'))\r\nconst GlobalFloatingWindow = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalFloatingWindow'))\r\nconst GlobalAiControls = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalAiControls'))\r\nconst GlobalAiChat = defineAsyncComponent(() => import('../GlobalAiChat/GlobalAiChat'))\r\nconst LayoutViewOneWorkBench = defineAsyncComponent(() => import('./component/LayoutViewOneWorkBench.vue'))\r\nconst qusetionAnswering = defineAsyncComponent(() => import('./component/question-answering.vue'))\r\nconst suggestPop = defineAsyncComponent(() => import('../LayoutView/component/suggestPop'))\r\nconst SubAppViewport = {\r\n  name: 'SubAppViewport',\r\n  props: ['name'],\r\n  template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\r\n}\r\nconst { isMain } = qiankun(useRoute())\r\nconst {\r\n  user,\r\n  area,\r\n  role,\r\n  LayoutViewBox,\r\n  LayoutViewInfo,\r\n  helpShow,\r\n  handleCommand,\r\n  editPassWordShow,\r\n  verifyEditPassWord,\r\n  verifyEditPassWordShow,\r\n  editPassWordCallback,\r\n  regionId,\r\n  regionName,\r\n  regionSelect,\r\n  isRegionSelectShow,\r\n  isView,\r\n  tabMenu,\r\n  tabMenuData,\r\n  handleClick,\r\n  menuId,\r\n  menuData,\r\n  menuClick,\r\n  handleBreadcrumb,\r\n  WorkBenchReturn,\r\n  isRefresh,\r\n  keepAliveRoute,\r\n  tabData,\r\n  tabClick,\r\n  handleRefresh,\r\n  handleClose,\r\n  handleCloseOther,\r\n  isMicroApp,\r\n  MicroApp\r\n} = LayoutView(useRoute(), useRouter())\r\nconst router = useRouter()\r\nconst { rongCloudToken } = ChatMethod()\r\nconst { loginHintShow } = loginHintMethod()\r\nconst { AiChatTargetWidth, AiChatViewType, AiChatWindowShow } = AiChatMethod()\r\n\r\nconst handleAiToolBox = () => {\r\n  router.push('/GlobalAiToolBox')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutViewOne {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .LayoutViewOneHeader {\r\n    width: 100%;\r\n    height: 68px;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .LayoutViewOneBox {\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 40px 0 20px;\r\n      cursor: pointer;\r\n\r\n      .LayoutViewOneLogo {\r\n        width: 52px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .LayoutViewOneName {\r\n        font-size: var(--zy-system-font-size);\r\n        line-height: var(--zy-line-height);\r\n        font-weight: bold;\r\n        color: var(--zy-el-color-primary);\r\n        padding-left: 12px;\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneHeaderMenu {\r\n      flex: 1;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      overflow: hidden;\r\n\r\n      .zy-el-tabs {\r\n        width: 100%;\r\n\r\n        .zy-el-tabs__header {\r\n          margin: 0;\r\n\r\n          .zy-el-tabs__nav-next,\r\n          .zy-el-tabs__nav-prev {\r\n            width: 26px;\r\n            font-size: calc(var(--zy-navigation-font-size) + 4px);\r\n            line-height: 48px;\r\n\r\n            .zy-el-icon {\r\n              color: var(--zy-el-text-color-primary);\r\n            }\r\n          }\r\n\r\n          .zy-el-tabs__nav-wrap {\r\n            padding: 0 calc(var(--zy-navigation-font-size) + 8px);\r\n\r\n            &::after {\r\n              background-color: transparent;\r\n            }\r\n\r\n            .zy-el-tabs__item {\r\n              height: 43px;\r\n              line-height: 43px;\r\n              font-weight: normal;\r\n              font-size: var(--zy-navigation-font-size);\r\n            }\r\n\r\n            .is-active {\r\n              font-weight: bold;\r\n            }\r\n\r\n            .zy-el-tabs__active-bar {\r\n              height: 3px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .zy-el-tabs__content {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n\r\n    .isLayoutViewHeaderMenu {\r\n      .zy-el-tabs {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneInfo {\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 20px;\r\n\r\n      .LayoutViewOneAiToolBox {\r\n        height: 36px;\r\n        display: flex;\r\n        align-items: center;\r\n        color: #3657c0;\r\n        background: linear-gradient(180deg, #ffffff 0%, #a7fff8 76%, #7cd7f5 100%);\r\n        border-radius: 18px;\r\n        font-size: var(--zy-navigation-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-left: calc(var(--zy-navigation-font-size) + 22px);\r\n        padding-right: 16px;\r\n        position: relative;\r\n        cursor: pointer;\r\n        margin-right: 10px;\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 16px;\r\n          transform: translateY(-50%);\r\n          width: var(--zy-navigation-font-size);\r\n          height: var(--zy-navigation-font-size);\r\n          background: url('../img/ai_tool_box_icon.png') no-repeat;\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n\r\n      .xyl-region {\r\n        padding-left: 12px;\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .xyl-region-view {\r\n          height: var(--zy-height-routine);\r\n\r\n          .xyl-region-img {\r\n            width: 20px;\r\n            height: 20px;\r\n          }\r\n\r\n          .xyl-region-name {\r\n            font-size: var(--zy-text-font-size);\r\n          }\r\n\r\n          .xyl-region-icon {\r\n            width: calc(var(--zy-text-font-size) + 6px);\r\n            font-size: var(--zy-text-font-size);\r\n          }\r\n        }\r\n      }\r\n\r\n      .LayoutViewOneUser {\r\n        display: flex;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        height: var(--zy-height-routine);\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 10px;\r\n\r\n        .zy-el-image {\r\n          width: calc(var(--zy-height-routine) - 2px);\r\n          height: calc(var(--zy-height-routine) - 2px);\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        span {\r\n          color: #fff;\r\n          margin-left: 6px;\r\n          padding-right: 12px;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n      }\r\n\r\n      .zy-el-badge {\r\n        height: var(--zy-height-routine);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 10px;\r\n\r\n        &+.zy-el-badge {\r\n          margin-left: 20px;\r\n        }\r\n\r\n        .LayoutPersonalDoList,\r\n        .LayoutBoxMessage {\r\n          width: auto;\r\n          height: auto;\r\n          padding: 0 12px 0 36px;\r\n          background-size: 20px 20px;\r\n          background-position: 12px center;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          color: #fff;\r\n        }\r\n      }\r\n\r\n      .zy-el-dropdown {\r\n        width: var(--zy-height-routine);\r\n        height: var(--zy-height-routine);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 10px;\r\n      }\r\n\r\n      .LayoutViewOneRefresh {\r\n        width: var(--zy-height-routine);\r\n        height: var(--zy-height-routine);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 20px;\r\n        cursor: pointer;\r\n\r\n        svg {\r\n          width: 24px;\r\n          height: 24px;\r\n        }\r\n      }\r\n\r\n      .LayoutPersonalDoList {\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_personal_do_list.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutBoxMessage {\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_box_message.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutOperation {\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_operation.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .LayoutViewOneContainer {\r\n    width: 100%;\r\n    height: calc(100% - 68px);\r\n\r\n    .LayoutViewOneFloatingWindow {\r\n      width: auto;\r\n      height: 100%;\r\n\r\n      .LayoutViewOneFloatingWindowBody {\r\n        width: var(--ai-chat-target-width);\r\n        height: 100%;\r\n        background: #fff;\r\n        box-sizing: border-box;\r\n        transform-origin: left center;\r\n        border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      /* 进入动画 */\r\n      .width-animation-enter-active {\r\n        animation: widen 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 离开动画 */\r\n      .width-animation-leave-active {\r\n        animation: narrow 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 定义进入动画 */\r\n      @keyframes widen {\r\n        from {\r\n          width: 0;\r\n        }\r\n\r\n        to {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n      }\r\n\r\n      /* 定义离开动画 */\r\n      @keyframes narrow {\r\n        from {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n\r\n        to {\r\n          width: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneAside {\r\n      width: auto;\r\n    }\r\n\r\n    .LayoutViewOneMain {\r\n      height: 100%;\r\n      padding: 0 var(--zy-distance-three) 0 0;\r\n\r\n      .LayoutViewOneBreadcrumb {\r\n        width: 100%;\r\n        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px);\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: #fff;\r\n        padding: 0 var(--zy-distance-two);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .zy-el-breadcrumb {\r\n          font-size: var(--zy-name-font-size);\r\n\r\n          .zy-el-breadcrumb__inner {\r\n            cursor: pointer;\r\n            font-weight: bold;\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n\r\n          .zy-el-breadcrumb__item {\r\n            &:last-child {\r\n              .zy-el-breadcrumb__inner {\r\n                cursor: text;\r\n                font-weight: normal;\r\n                color: var(--zy-el-text-color-regular);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .LayoutViewOneBody {\r\n        width: 100%;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n        background: #fff;\r\n\r\n        .subApp-viewport {\r\n          width: 100%;\r\n          height: 100%;\r\n\r\n          >div {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneMainView {\r\n      width: 100%;\r\n      padding: 0;\r\n\r\n      .LayoutViewOneBody {\r\n        height: 100%;\r\n        background: transparent;\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneMainBreadcrumb {\r\n      width: 100%;\r\n\r\n      .LayoutViewOneBody {\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n      }\r\n    }\r\n  }\r\n\r\n  .ConstraintEditPassWord {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 999;\r\n    background-color: #fff;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .EditPassWord {\r\n      box-shadow: 0px 2px 40px rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.LayoutViewOneRoleItem {\r\n  font-size: var(--zy-text-font-size);\r\n  line-height: var(--zy-line-height);\r\n}\r\n\r\n.LayoutViewOneWorkBenchPopover {\r\n  width: 680px !important;\r\n  padding: 0 !important;\r\n\r\n  .LayoutViewOneWorkBench {\r\n    width: 100%;\r\n    max-height: 480px;\r\n\r\n    .zy-el-scrollbar__wrap {\r\n      max-height: 480px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAIaA,KAAK,EAAC;AAAmB;;EAGzBA,KAAK,EAAC;AAAmB;;EAE3BA,KAAK,EAAC;AAAyB;;EAT1CC,GAAA;EAamBD,KAAK,EAAC;;;EAIFA,KAAK,EAAC;AAA6B;;EAQ/CA,KAAK,EAAC,mBAAmB;EAACE,GAAG,EAAC;;;EAO1BF,KAAK,EAAC;AAAmB;;EAEtBA,KAAK,EAAC;AAAc;iBAlCtC;;EAAAC,GAAA;EAiEaD,KAAK,EAAC;;;EAQNA,KAAK,EAAC;AAAmB;;EAzEtCC,GAAA;EAkGSD,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;;;uBAlGfG,mBAAA,CAAAC,SAAA,SACEC,YAAA,CAqGeC,uBAAA;IArGDN,KAAK,EAAC,eAAe;IAAEO,KAAK,EAD5CC,eAAA,qBACkEC,MAAA,CAAAC,QAAQ;;IAD1EC,OAAA,EAAAC,QAAA,CAEI;MAAA,OAoDY,CApDZP,YAAA,CAoDYQ,oBAAA;QApDDb,KAAK,EAAC;MAAqB;QAF1CW,OAAA,EAAAC,QAAA,CAGM;UAAA,OAKM,CALNE,mBAAA,CAKM;YALDd,KAAK,EAAC,kBAAkB;YAACE,GAAG,EAAC,eAAe;YAAEa,OAAK,EAAAC,MAAA,QAAAA,MAAA;cAAA,OAAEP,MAAA,CAAAQ,eAAA,IAAAR,MAAA,CAAAQ,eAAA,CAAAC,KAAA,CAAAT,MAAA,EAAAU,SAAA,CAAe;YAAA;cACvEL,mBAAA,CAEM,OAFNM,UAEM,GADJf,YAAA,CAA0CgB,mBAAA;YAA/BC,GAAG,EAAEb,MAAA,CAAAc,UAAU;YAAEC,GAAG,EAAC;8CAElCV,mBAAA,CAA4G,OAA5GW,UAA4G,EAAAC,gBAAA,CAA1EjB,MAAA,CAAAkB,oBAAoB,cAAclB,MAAA,CAAAmB,UAAU,SAAAF,gBAAA,CAAWjB,MAAA,CAAAoB,UAAU,iB,yBAErGf,mBAAA,CAeM,OAfNgB,UAeM,GAdJzB,YAAA,CAaU0B,kBAAA;YAvBlBC,UAAA,EAU0BvB,MAAA,CAAAwB,OAAO;YAVjC,uBAAAjB,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;cAAA,OAU0BzB,MAAA,CAAAwB,OAAO,GAAAC,MAAA;YAAA;YAAGC,WAAU,EAAE1B,MAAA,CAAA2B;;YAVhDzB,OAAA,EAAAC,QAAA,CAWuB;cAAA,OAA2B,E,kBAAxCT,mBAAA,CAWcC,SAAA,QAtBxBiC,WAAA,CAWsC5B,MAAA,CAAA6B,WAAW,EAXjD,UAW8BC,IAAI;qCAAxBC,YAAA,CAWcC,sBAAA;kBAX4BxC,GAAG,EAAEsC,IAAI,CAACG,EAAE;kBAAGC,IAAI,EAAEJ,IAAI,CAACG,EAAE;kBAAGE,KAAK,EAAEL,IAAI,CAACI;;kBACxEC,KAAK,EAAAhC,QAAA,CAKd;oBAAA,OAGR,CAPuD2B,IAAI,CAACM,SAAS,qB,cAA7D1C,mBAAA,CAAqG,OAArG2C,UAAqG,EAAApB,gBAAA,CAAlBa,IAAI,CAACI,IAAI,oBAb1GI,mBAAA,gBAesBR,IAAI,CAACM,SAAS,qB,cADtBL,YAAA,CAMaQ,qBAAA;sBApB3B/C,GAAA;sBAc0BgD,OAAO,EAAC,OAAO;sBAAC,cAAY,EAAC,+BAA+B;sBAACC,UAAU,EAAC;;sBAEvEC,SAAS,EAAAvC,QAAA,CAClB;wBAAA,OAA8D,CAA9DE,mBAAA,CAA8D,OAA9DsC,UAA8D,EAAA1B,gBAAA,CAAlBa,IAAI,CAACI,IAAI,iB;;sBAjBvEhC,OAAA,EAAAC,QAAA,CAmBgB;wBAAA,OAAqF,CAArFP,YAAA,CAAqFI,MAAA;0BAA5DiC,EAAE,EAAEH,IAAI,CAACG,EAAE;0BAAGW,IAAI,EAAEd,IAAI,CAACe;;;sBAnBlEC,CAAA;oDAAAR,mBAAA,e;;kBAAAQ,CAAA;;;;YAAAA,CAAA;8DAyBMzC,mBAAA,CA4BM,OA5BN0C,UA4BM,GA3BJnD,YAAA,CACgEoD,qBAAA;YA3BxEzB,UAAA,EA0B6BvB,MAAA,CAAAiD,QAAQ;YA1BrC,uBAAA1C,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;cAAA,OA0B6BzB,MAAA,CAAAiD,QAAQ,GAAAxB,MAAA;YAAA;YAAGmB,IAAI,EAAE5C,MAAA,CAAAkD,IAAI;YAAGC,QAAM,EAAEnD,MAAA,CAAAoD,YAAY;YAC9DC,KAAK,EAAE;cAAAlB,KAAA;cAAAU,QAAA;YAAA;uEACVjD,YAAA,CAQa0D,qBAAA;YARDC,SAAS,EAAC,KAAK;YAACC,MAAM,EAAC,OAAO;YAAEC,MAAM,EAAE,CAAC;YAAGC,QAAQ,GAAG1D,MAAA,CAAA2D,IAAI,CAACC;;YAC3DC,OAAO,EAAA1D,QAAA,CACmB;cAAA,OAA6B,E,kBAAhET,mBAAA,CAA8FC,SAAA,QA9B1GiC,WAAA,CA8BuE5B,MAAA,CAAA2D,IAAI,EA9B3E,UA8BuD7B,IAAI,EAAEgC,KAAK;qCAAtDpE,mBAAA,CAA8F;kBAAzFH,KAAK,EAAC,uBAAuB;kBAAgCC,GAAG,EAAEsE;oCAAUhC,IAAI;;;YA9BjG5B,OAAA,EAAAC,QAAA,CAgCU;cAAA,OAGM,CAHNE,mBAAA,CAGM,OAHN0D,UAGM,GAFJnE,YAAA,CAA0CgB,mBAAA;gBAA/BC,GAAG,EAAEb,MAAA,CAAAgE,IAAI,CAACC,KAAK;gBAAElD,GAAG,EAAC;gDAChCV,mBAAA,CAAqD,QAArD6D,UAAqD,EAAAjD,gBAAA,CAAvBjB,MAAA,CAAAgE,IAAI,CAACG,QAAQ,iB;;YAlCvDrB,CAAA;2CAqCQlD,YAAA,CAA+CI,MAAA;YArCvDE,OAAA,EAAAC,QAAA,CAqC8B;cAAA,OAAEI,MAAA,SAAAA,MAAA,QArChC6D,gBAAA,CAqC8B,IAAE,E;;YArChCtB,CAAA;cAsCQlD,YAAA,CAAuCI,MAAA;YAtC/CE,OAAA,EAAAC,QAAA,CAsC0B;cAAA,OAAEI,MAAA,SAAAA,MAAA,QAtC5B6D,gBAAA,CAsC0B,IAAE,E;;YAtC5BtB,CAAA;cAuCQzC,mBAAA,CAA8G;YAAzGd,KAAK,EAAC,sBAAsB;YAAC8E,SAAoB,EAAZrE,MAAA,CAAAsE,WAAW;YAAGhE,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;cAAA,OAAEzB,MAAA,CAAAuE,aAAa;YAAA;YAAaC,KAAK,EAAC;kCAvCvGC,UAAA,GAwCQ7E,YAAA,CAYc8E,sBAAA;YAZAC,SAAO,EAAE3E,MAAA,CAAAuE;UAAa;YAEvBK,QAAQ,EAAAzE,QAAA,CACjB;cAAA,OAOmB,CAPnBP,YAAA,CAOmBiF,2BAAA;gBAlD/B3E,OAAA,EAAAC,QAAA,CA4Cc;kBAAA,OAA2D,CAA3DP,YAAA,CAA2DkF,2BAAA;oBAAzCC,OAAO,EAAC;kBAAM;oBA5C9C7E,OAAA,EAAAC,QAAA,CA4C+C;sBAAA,OAAOI,MAAA,SAAAA,MAAA,QA5CtD6D,gBAAA,CA4C+C,SAAO,E;;oBA5CtDtB,CAAA;sBA6CcR,mBAAA,qEAAsE,EACtEA,mBAAA,8IAC4D,EAC5D1C,YAAA,CAAiEkF,2BAAA;oBAA/CC,OAAO,EAAC;kBAAe;oBAhDvD7E,OAAA,EAAAC,QAAA,CAgDwD;sBAAA,OAAII,MAAA,SAAAA,MAAA,QAhD5D6D,gBAAA,CAgDwD,MAAI,E;;oBAhD5DtB,CAAA;sBAiDclD,YAAA,CAAwDkF,2BAAA;oBAAtCC,OAAO,EAAC;kBAAM;oBAjD9C7E,OAAA,EAAAC,QAAA,CAiD+C;sBAAA,OAAII,MAAA,SAAAA,MAAA,QAjDnD6D,gBAAA,CAiD+C,MAAI,E;;oBAjDnDtB,CAAA;;;gBAAAA,CAAA;;;YAAA5C,OAAA,EAAAC,QAAA,CAyCU;cAAA,OAAmC,C,4BAAnCE,mBAAA,CAAmC;gBAA9Bd,KAAK,EAAC;cAAiB,4B;;YAzCtCuD,CAAA;;;QAAAA,CAAA;UAuDIlD,YAAA,CAoCeC,uBAAA;QApCDN,KAAK,EAAC;MAAwB;QAvDhDW,OAAA,EAAAC,QAAA,CAwDM;UAAA,OAEW,C,gBAFXP,YAAA,CAEWoF,mBAAA;YAFDzF,KAAK,EAAC;UAAoB;YAxD1CW,OAAA,EAAAC,QAAA,CAyDQ;cAAA,OAA+E,CAA/EP,YAAA,CAA+EqF,mBAAA;gBAzDvF1D,UAAA,EAyD2BvB,MAAA,CAAAkF,MAAM;gBAzDjC,uBAAA3E,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;kBAAA,OAyD2BzB,MAAA,CAAAkF,MAAM,GAAAzD,MAAA;gBAAA;gBAAG0D,QAAQ,EAAEnF,MAAA,CAAAmF,QAAQ;gBAAGhC,QAAM,EAAEnD,MAAA,CAAAoF;;;YAzDjEtC,CAAA;8CAwDmD9C,MAAA,CAAAqF,MAAM,E,GAGnDzF,YAAA,CAuBU0F,kBAAA;YAvBD/F,KAAK,EA3DpBgG,eAAA,EA2DqB,mBAAmB;cAAAC,qBAAA,GACExF,MAAA,CAAAqF,MAAM;cAAAI,2BAAA,GAAgCzF,MAAA,CAAAqF,MAAM,IAAIrF,MAAA,CAAA0F,OAAO,CAAC9B,MAAM;YAAA;;YA5DxG1D,OAAA,EAAAC,QAAA,CA6DQ;cAAA,OAGU,C,gBAHVP,YAAA,CAGU+F,kBAAA;gBAhElBpE,UAAA,EA6D0BvB,MAAA,CAAAkF,MAAM;gBA7DhC,uBAAA3E,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;kBAAA,OA6D0BzB,MAAA,CAAAkF,MAAM,GAAAzD,MAAA;gBAAA;gBAAGmE,UAAS,EAAE5F,MAAA,CAAA6F,QAAQ;gBAAGC,SAAO,EAAE9F,MAAA,CAAA+F,aAAa;gBAAGC,OAAK,EAAEhG,MAAA,CAAAiG,WAAW;gBACzFC,YAAU,EAAElG,MAAA,CAAAmG;;gBA9DvBjG,OAAA,EAAAC,QAAA,CA+DwB;kBAAA,OAAuB,E,kBAArCT,mBAAA,CAAoGC,SAAA,QA/D9GiC,WAAA,CA+DuC5B,MAAA,CAAA0F,OAAO,EA/D9C,UA+D+B5D,IAAI;yCAAzBC,YAAA,CAAoGqE,uBAAA;sBAA7D5G,GAAG,EAAEsC,IAAI,CAACG,EAAE;sBAAGoE,KAAK,EAAEvE,IAAI,CAACG;;sBA/D5E/B,OAAA,EAAAC,QAAA,CA+DgF;wBAAA,OAAe,CA/D/FiE,gBAAA,CAAAnD,gBAAA,CA+DmFa,IAAI,CAACI,IAAI,iB;;sBA/D5FY,CAAA;;;;gBAAAA,CAAA;iHA8DiD9C,MAAA,CAAAqF,MAAM,E,IAGHrF,MAAA,CAAAqF,MAAM,IAAIrF,MAAA,CAAA0F,OAAO,CAAC9B,MAAM,Q,cAApElE,mBAAA,CAOM,OAPN4G,WAOM,GANJ1G,YAAA,CAKgB2G,wBAAA;gBALA,gBAAc,EAAEvG,MAAA,CAAAwG;cAAU;gBAlEpDtG,OAAA,EAAAC,QAAA,CAmEgC;kBAAA,OAAgC,E,kBAApDT,mBAAA,CAGqBC,SAAA,QAtEjCiC,WAAA,CAmEwD5B,MAAA,CAAA0F,OAAO,EAnE/D,UAmEwC5D,IAAI,EAAEgC,KAAK;yCAAvC/B,YAAA,CAGqB0E,6BAAA;sBAHiCjH,GAAG,SAASsC,IAAI,CAACG,EAAE;sBACtE3B,OAAK,WAALA,OAAKA,CAAAmB,MAAA;wBAAA,OAAEzB,MAAA,CAAA0G,gBAAgB,CAAC5E,IAAI,EAAEgC,KAAK;sBAAA;;sBApElD5D,OAAA,EAAAC,QAAA,CAqEc;wBAAA,OAAe,CArE7BiE,gBAAA,CAAAnD,gBAAA,CAqEiBa,IAAI,CAACI,IAAI,iB;;sBArE1BY,CAAA;;;;gBAAAA,CAAA;yDAAAR,mBAAA,gBAyEQjC,mBAAA,CAQM,OARNsG,WAQM,GAPJ/G,YAAA,CAIcgH,sBAAA;gBA9ExB1G,OAAA,EAAAC,QAAA,CA2EY,UAAA0G,IAAA;kBAAA,IADqBC,SAAS,GAAAD,IAAA,CAATC,SAAS;kBAAA,S,cAC9B/E,YAAA,CAEagF,UAAA;oBAFAC,OAAO,EAAEhH,MAAA,CAAAiH;kBAAc,IACjBjH,MAAA,CAAAkH,MAAM,IAAIlH,MAAA,CAAAmH,SAAS,I,cAApCpF,YAAA,CAAyFqF,wBA5EvG,CA4EgFN,SAAS;oBAApCtH,GAAG,EAAE6H,IAAA,CAAAC,MAAM,CAACC;wBA5EjEjF,mBAAA,e;;gBAAAQ,CAAA;qCA+EUpD,mBAAA,CACiBC,SAAA,QAhF3BiC,WAAA,CA+EyC5B,MAAA,CAAAwH,QAAQ,EA/EjD,UA+EiC1F,IAAI;sDAA3BC,YAAA,CACiB/B,MAAA;kBADyBR,GAAG,EAAEsC,IAAI;kBAA2CI,IAAI,EAAEJ;+DAAtC9B,MAAA,CAAAkH,MAAM,IAAIlH,MAAA,CAAAyH,UAAU,KAAK3F,IAAI,E;;;YA/ErGgB,CAAA;wCAmF0D9C,MAAA,CAAA0H,aAAa,I,cAAjE3F,YAAA,CAOWiD,mBAAA;YA1FjBxF,GAAA;YAmFgBD,KAAK,EAAC;;YAnFtBW,OAAA,EAAAC,QAAA,CAoFQ;cAAA,OAKa,CALbP,YAAA,CAKa+H,WAAA;gBALDzF,IAAI,EAAC;cAAiB;gBApF1ChC,OAAA,EAAAC,QAAA,CAiKY;kBAAA,OAeC,CA1FKH,MAAA,CAAA4H,cAAc,G,+BADtBlI,mBAAA,CAGM;oBAxFhBF,GAAA;oBAqFeD,KAAK,EAAC,iCAAiC;oBAAEO,KAAK,EArF7DC,eAAA;sBAAA,0BAqF2FC,MAAA,CAAA6H;oBAAiB;sBAEhGjI,YAAA,CAAwDI,MAAA;oBAvFpEuB,UAAA,EAuFmCvB,MAAA,CAAA8H,gBAAgB;oBAvFnD,uBAAAvH,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;sBAAA,OAuFmCzB,MAAA,CAAA8H,gBAAgB,GAAArG,MAAA;oBAAA;uFADTzB,MAAA,CAAA8H,gBAAgB,E,IAtF1DxF,mBAAA,e;;gBAAAQ,CAAA;;;YAAAA,CAAA;gBAAAR,mBAAA,e;;QAAAQ,CAAA;UA4FIlD,YAAA,CAEmBmI,2BAAA;QA9FvBxG,UAAA,EA4F+BvB,MAAA,CAAAgI,QAAQ;QA5FvC,uBAAAzH,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;UAAA,OA4F+BzB,MAAA,CAAAgI,QAAQ,GAAAvG,MAAA;QAAA;QAAES,IAAI,EAAC;;QA5F9ChC,OAAA,EAAAC,QAAA,CA6FM;UAAA,OAA6B,CAA7BP,YAAA,CAA6BI,MAAA,kB;;QA7FnC8C,CAAA;yCA+FIlD,YAAA,CAEmBmI,2BAAA;QAjGvBxG,UAAA,EA+F+BvB,MAAA,CAAAiI,gBAAgB;QA/F/C,uBAAA1H,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;UAAA,OA+F+BzB,MAAA,CAAAiI,gBAAgB,GAAAxG,MAAA;QAAA;QAAES,IAAI,EAAC;;QA/FtDhC,OAAA,EAAAC,QAAA,CAgGM;UAAA,OAAyF,CAAzFP,YAAA,CAAyFI,MAAA;YAA1EkI,IAAI,EAAElI,MAAA,CAAAmI,kBAAkB;YAAGC,UAAQ,EAAEpI,MAAA,CAAAqI;;;QAhG1DvF,CAAA;yCAkG8C9C,MAAA,CAAAsI,sBAAsB,I,cAAhE5I,mBAAA,CAEM,OAFN6I,WAEM,GADJ3I,YAAA,CAAyFI,MAAA;QAA1EkI,IAAI,EAAElI,MAAA,CAAAmI,kBAAkB;QAAGC,UAAQ,EAAEpI,MAAA,CAAAqI;2DAnG1D/F,mBAAA,gBAqG8BtC,MAAA,CAAAwI,kBAAkB,I,cAA5CzG,YAAA,CAA4F/B,MAAA;QArGhGR,GAAA;QAqGmD4I,UAAQ,EAAEpI,MAAA,CAAAoD;iDArG7Dd,mBAAA,e;;IAAAQ,CAAA;gCAuGElD,YAAA,CAAuCI,MAAA,wBACbA,MAAA,CAAAyI,cAAc,I,cAAxC1G,YAAA,CAA+D/B,MAAA;IAxGjER,GAAA;EAAA,MAAA8C,mBAAA,gBAyGoFtC,MAAA,CAAA0H,aAAa,I,cAA/F3F,YAAA,CAAmG/B,MAAA;IAzGrGR,GAAA;IAAA+B,UAAA,EAyGiCvB,MAAA,CAAA8H,gBAAgB;IAzGjD,uBAAAvH,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;MAAA,OAyGiCzB,MAAA,CAAA8H,gBAAgB,GAAArG,MAAA;IAAA;IAAGiC,QAAQ,EAAE1D,MAAA,CAAA4H;yDAzG9DtF,mBAAA,gBA0G0BtC,MAAA,CAAA0H,aAAa,I,cAArC3F,YAAA,CAAyC/B,MAAA;IA1G3CR,GAAA;EAAA,MAAA8C,mBAAA,gBA2GoBtC,MAAA,CAAAkH,MAAM,IAAIlH,MAAA,CAAA0I,aAAa,I,cAAzC3G,YAAA,CAA6C/B,MAAA;IA3G/CR,GAAA;EAAA,MAAA8C,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}