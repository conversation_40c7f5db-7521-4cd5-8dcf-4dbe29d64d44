{"ast": null, "code": "import { onActivated } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { suggestExportWord } from '@/assets/js/suggestExportWord';\nvar __default__ = {\n  name: 'DoNotReceiveSuggest'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var buttonList = [{\n      id: 'exportWord',\n      name: '导出Word',\n      type: 'primary',\n      has: ''\n    }, {\n      id: 'export',\n      name: '导出Excel',\n      type: 'primary',\n      has: ''\n    }];\n    var _GlobalTable = GlobalTable({\n        tableId: route.query.tableId,\n        tableApi: 'suggestionList'\n      }),\n      keyword = _GlobalTable.keyword,\n      queryRef = _GlobalTable.queryRef,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableHead = _GlobalTable.tableHead,\n      tableData = _GlobalTable.tableData,\n      exportId = _GlobalTable.exportId,\n      exportParams = _GlobalTable.exportParams,\n      exportShow = _GlobalTable.exportShow,\n      handleQuery = _GlobalTable.handleQuery,\n      handleSortChange = _GlobalTable.handleSortChange,\n      handleHeaderClass = _GlobalTable.handleHeaderClass,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      tableRefReset = _GlobalTable.tableRefReset,\n      handleGetParams = _GlobalTable.handleGetParams,\n      handleEditorCustom = _GlobalTable.handleEditorCustom,\n      handleExportExcel = _GlobalTable.handleExportExcel;\n    onActivated(function () {\n      handleQuery();\n    });\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'exportWord':\n          suggestExportWord(handleGetParams());\n          break;\n        case 'export':\n          handleExportExcel();\n          break;\n        default:\n          break;\n      }\n    };\n    var handleTableClick = function handleTableClick(key, row) {\n      switch (key) {\n        case 'details':\n          handleDetails(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleDetails = function handleDetails(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '提案详情',\n          path: '/proposal/SuggestDetail',\n          query: {\n            id: item.id\n          }\n        }\n      });\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      exportShow.value = false;\n    };\n    var __returned__ = {\n      route,\n      buttonList,\n      keyword,\n      queryRef,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableHead,\n      tableData,\n      exportId,\n      exportParams,\n      exportShow,\n      handleQuery,\n      handleSortChange,\n      handleHeaderClass,\n      handleTableSelect,\n      tableRefReset,\n      handleGetParams,\n      handleEditorCustom,\n      handleExportExcel,\n      handleReset,\n      handleButton,\n      handleTableClick,\n      handleDetails,\n      callback,\n      onActivated,\n      get useRoute() {\n        return useRoute;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get suggestExportWord() {\n        return suggestExportWord;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["onActivated", "useRoute", "GlobalTable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestExportWord", "__default__", "name", "route", "buttonList", "id", "type", "has", "_GlobalTable", "tableId", "query", "tableApi", "keyword", "queryRef", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableHead", "tableData", "exportId", "exportParams", "exportShow", "handleQuery", "handleSortChange", "handleHeaderClass", "handleTableSelect", "tableRefReset", "handleGetParams", "handleEditorCustom", "handleExportExcel", "handleReset", "value", "handleButton", "isType", "handleTableClick", "key", "row", "handleDetails", "item", "setGlobalState", "openRoute", "path", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/DoNotReceiveSuggest/DoNotReceiveSuggest.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DoNotReceiveSuggest\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\"\r\n                       :data=\"tableHead\"\r\n                       ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\"\r\n                @sort-change=\"handleSortChange\"\r\n                :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\"\r\n                          @tableClick=\"handleTableClick\"></xyl-global-table>\r\n        <xyl-global-table-button :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"exportShow\"\r\n                      name=\"导出Excel\">\r\n      <xyl-export-excel :name=\"route.query.moduleName\"\r\n                        :exportId=\"exportId\"\r\n                        :params=\"exportParams\"\r\n                        module=\"proposalExportExcel\"\r\n                        :tableId=\"route.query.tableId\"\r\n                        @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DoNotReceiveSuggest' }\r\n</script>\r\n<script setup>\r\nimport { onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { suggestExportWord } from '@/assets/js/suggestExportWord'\r\nconst route = useRoute()\r\nconst buttonList = [\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出Excel', type: 'primary', has: '' }\r\n]\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  exportId,\r\n  exportParams,\r\n  exportShow,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  handleGetParams,\r\n  handleEditorCustom,\r\n  handleExportExcel\r\n} = GlobalTable({ tableId: route.query.tableId, tableApi: 'suggestionList' })\r\n\r\nonActivated(() => { handleQuery() })\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'exportWord':\r\n      suggestExportWord(handleGetParams())\r\n      break\r\n    case 'export':\r\n      handleExportExcel()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      handleDetails(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id } } })\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  exportShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.DoNotReceiveSuggest {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAyDA,SAASA,WAAW,QAAQ,KAAK;AACjC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,+BAA+B;AAPjE,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAsB,CAAC;;;;;IAQ9C,IAAMC,KAAK,GAAGN,QAAQ,CAAC,CAAC;IACxB,IAAMO,UAAU,GAAG,CACjB;MAAEC,EAAE,EAAE,YAAY;MAAEH,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,EAC9D;MAAEF,EAAE,EAAE,QAAQ;MAAEH,IAAI,EAAE,SAAS;MAAEI,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAG,CAAC,CAC5D;IACD,IAAAC,YAAA,GAqBIV,WAAW,CAAC;QAAEW,OAAO,EAAEN,KAAK,CAACO,KAAK,CAACD,OAAO;QAAEE,QAAQ,EAAE;MAAiB,CAAC,CAAC;MApB3EC,OAAO,GAAAJ,YAAA,CAAPI,OAAO;MACPC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MACRC,QAAQ,GAAAN,YAAA,CAARM,QAAQ;MACRC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,MAAM,GAAAR,YAAA,CAANQ,MAAM;MACNC,QAAQ,GAAAT,YAAA,CAARS,QAAQ;MACRC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,SAAS,GAAAX,YAAA,CAATW,SAAS;MACTC,SAAS,GAAAZ,YAAA,CAATY,SAAS;MACTC,QAAQ,GAAAb,YAAA,CAARa,QAAQ;MACRC,YAAY,GAAAd,YAAA,CAAZc,YAAY;MACZC,UAAU,GAAAf,YAAA,CAAVe,UAAU;MACVC,WAAW,GAAAhB,YAAA,CAAXgB,WAAW;MACXC,gBAAgB,GAAAjB,YAAA,CAAhBiB,gBAAgB;MAChBC,iBAAiB,GAAAlB,YAAA,CAAjBkB,iBAAiB;MACjBC,iBAAiB,GAAAnB,YAAA,CAAjBmB,iBAAiB;MACjBC,aAAa,GAAApB,YAAA,CAAboB,aAAa;MACbC,eAAe,GAAArB,YAAA,CAAfqB,eAAe;MACfC,kBAAkB,GAAAtB,YAAA,CAAlBsB,kBAAkB;MAClBC,iBAAiB,GAAAvB,YAAA,CAAjBuB,iBAAiB;IAGnBnC,WAAW,CAAC,YAAM;MAAE4B,WAAW,CAAC,CAAC;IAAC,CAAC,CAAC;IACpC,IAAMQ,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBpB,OAAO,CAACqB,KAAK,GAAG,EAAE;MAClBT,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMU,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,YAAY;UACfnC,iBAAiB,CAAC6B,eAAe,CAAC,CAAC,CAAC;UACpC;QACF,KAAK,QAAQ;UACXE,iBAAiB,CAAC,CAAC;UACnB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMK,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,GAAG,EAAEC,GAAG,EAAK;MACrC,QAAQD,GAAG;QACT,KAAK,SAAS;UACZE,aAAa,CAACD,GAAG,CAAC;UAClB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9BzC,YAAY,CAAC0C,cAAc,CAAC;QAAEC,SAAS,EAAE;UAAExC,IAAI,EAAE,MAAM;UAAEyC,IAAI,EAAE,yBAAyB;UAAEjC,KAAK,EAAE;YAAEL,EAAE,EAAEmC,IAAI,CAACnC;UAAG;QAAE;MAAE,CAAC,CAAC;IACvH,CAAC;IACD,IAAMuC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBhB,aAAa,CAAC,CAAC;MACfJ,WAAW,CAAC,CAAC;MACbD,UAAU,CAACU,KAAK,GAAG,KAAK;IAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}