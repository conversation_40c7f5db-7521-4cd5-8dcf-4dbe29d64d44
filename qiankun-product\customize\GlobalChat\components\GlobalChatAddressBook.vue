<template>
  <div class="GlobalChatAddressBook" :class="{ GlobalChatMacAddressBook: isMac }">
    <div class="GlobalChatAddressBookList forbidSelect">
      <div class="GlobalChatAddressBookInput">
        <el-autocomplete v-model="keyword" :prefix-icon="Search" :fetch-suggestions="querySearch" placeholder="搜索"
          popper-class="GlobalChatAddressBookAutocomplete" clearable @select="handleClick">
          <template #default="{ item }">
            <div class="GlobalChatAddressBookItem forbidSelect">
              <el-image :src="imgUrl(item.user.photo || item.user.headImg)" fit="cover" draggable="false" />
              <div class="GlobalChatAddressBookName ellipsis">{{ item.user.userName }}</div>
            </div>
          </template>
        </el-autocomplete>
      </div>
      <el-scrollbar class="GlobalChatAddressBookScrollbar">
        <el-tree ref="treeRef" lazy :load="loadNode" node-key="id" :props="{ isLeaf: 'isLeaf' }">
          <template #default="{ data }">
            <div class="GlobalChatAddressBookLabel" v-if="data.type !== 'user'">{{ data.label }}</div>
            <div :class="['GlobalChatAddressBookItem', { 'is-active': data.id === userId }]" @click="handleClick(data)"
              v-if="data.type === 'user'">
              <el-image :src="imgUrl(data.user.photo || data.user.headImg)" fit="cover" draggable="false" />
              <div class="GlobalChatAddressBookName ellipsis">{{ data.user.userName }}</div>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
    <div class="GlobalChatAddressBookBody" v-if="userId">
      <div class="GlobalChatAddressBookInfo">
        <el-image :src="imgUrl(userInfo.photo || userInfo.headImg)" fit="cover" draggable="false" />
        <div class="GlobalChatAddressBookInfoBody">
          <div class="GlobalChatAddressBookName ellipsis" @click="openUser(userInfo)">{{ userInfo.userName }}
            <span v-html="maleIcon" v-if="userInfo?.sex?.value === '1'"></span>
            <span v-html="femaleIcon" v-if="userInfo?.sex?.value === '2'"></span>
            <div class="GlobalChatAddressBookIcon">
              <el-icon @click="handleOftenList(1)" v-if="!oftenList?.includes(userId)">
                <Star />
              </el-icon>
              <el-icon class="is-active" @click="handleOftenList(0)" v-if="oftenList?.includes(userId)">
                <StarFilled />
              </el-icon>
            </div>
          </div>
          <div class="GlobalChatAddressBookText ellipsis">
            <span v-html="mobileIcon"></span>
            {{ userInfo.mobile }}
          </div>
          <div class="GlobalChatAddressBookText ellipsis">{{ userInfo.position }}</div>
        </div>
        <el-button type="primary" @click="handleSendMessage">发送消息</el-button>
      </div>
      <el-scrollbar class="GlobalChatAddressBookUserScroll">
        <div class="GlobalChatAddressBookUserBody">
          <div class="GlobalChatAddressBookUserText ellipsis">民族：{{ userInfo?.nation?.label }}</div>
          <div class="GlobalChatAddressBookUserText ellipsis">
            出生年月：{{ format(userInfo.birthday, 'YYYY-MM-DD') }}
          </div>
          <div class="GlobalChatAddressBookUserText ellipsis">籍贯：{{ userInfo.nativePlace }}</div>
          <div class="GlobalChatAddressBookUserText ellipsis"
            v-if="userInfo.roleIds?.includes('168****************') || userInfo.roleIds?.includes('1743150705994133506') || userInfo.roleIds?.includes('1643857093725327362')">
            办公室电话：{{ userInfo.officePhone }}</div>
          <div class="GlobalChatAddressBookUserText ellipsis" v-if="userInfo.roleIds?.includes('1640259895343255554')">
            地址：{{
              userInfo.callAddress }}</div>
          <div class="GlobalChatAddressBookUserText ellipsis" v-if="userInfo.roleIds?.includes('1640259895343255554')">
            个人二维码：</div>
          <div style="margin: 10px 0;" v-if="userInfo.roleIds?.includes('1640259895343255554')">
            <qrcode-vue :value="shareUrl" render-as="svg" level="L" :size="90" />
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="GlobalChatAddressBookDrag" v-if="!userId"></div>
  </div>
</template>
<script>
export default { name: 'GlobalChatAddressBook' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import * as RongIMLib from '@rongcloud/imlib-next'
import { format } from 'common/js/time.js'
import { appOnlyHeader } from 'common/js/system_var.js'
import { mobileIcon, maleIcon, femaleIcon } from '../js/icon.js'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import config from 'common/config'
import QrcodeVue from 'qrcode.vue'
import { useStore } from 'vuex'
const store = useStore()
const emit = defineEmits(['send'])
const isMac = window.electron?.isMac
const treeRef = ref()
const keyword = ref('')
const labelAll = ref([])
const userId = ref('')
const userInfo = ref({})
const oftenList = ref([])
const imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))
const shareUrl = ref('')
const querySearch = async (queryString, cb) => {
  const results = queryString ? await handleQuery(queryString) : []
  cb(results)
}
const handleQuery = async () => {
  let newUserDataAll = []
  for (let index = 0; index < labelAll.value.length; index++) {
    const item = labelAll.value[index]
    const newUserData = await handleUserData(item.id)
    newUserDataAll = [...newUserDataAll, ...newUserData]
  }
  return [...new Map(newUserDataAll.map((item) => [item.id, item])).values()]
}
const handleUserData = async (id) => {
  try {
    const { data } = await api.SelectPersonBookUser({
      isOpen: 1,
      keyword: keyword.value,
      labelCode: id,
      nodeId: '',
      relationBookId: '',
      tabCode: 'relationBooksTemp'
    })
    const newUserData = []
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      if (item.userId)
        newUserData.push({
          id: item.userId,
          label: item.userName,
          children: [],
          type: 'user',
          user: item,
          isLeaf: true
        })
    }
    return newUserData
  } catch (error) {
    return []
  }
}
const handleClick = (item) => {
  userId.value = item.id
  getUserInfo(item)
}
const getUserInfo = async () => {
  const { data } = await api.userInfo({ detailId: userId.value })
  userInfo.value = data
  globalReadConfig()
}

// 获取个人二维码
const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: ['appShareAddress'] })
  getUserQrCode(data.appShareAddress)
}
const getUserQrCode = async (appShareAddress) => {
  var params = {
    n: 'mo_npcinfo_details',
    u: '../mo_npcinfo_details/mo_npcinfo_details.stml',
    p: {
      id: userId.value
    }
  }
  longShortLink(appShareAddress + 'pages/index/?' + JSON.stringify(params).replace(/\{/g, "%7B").replace(/\}/g, "%7D").replace(/\u0022/g, "%22"))
}
const longShortLink = async (url) => {
  const { data } = await api.longShortLink(encodeURIComponent(url))
  shareUrl.value = `${config.API_URL}/viewing/${data}`
}
// 跳转个人信息
const openUser = (item) => {
  if (item.roleIds) {
    if (item.roleIds.includes('1640259895343255554')) {
      store.commit('setOpenRoute', { name: '委员信息详情', path: '/cppccMember/CppccMemberDetails', query: { id: item.id } })
      return
    }
    if (item.roleIds.includes('168****************') || item.roleIds.includes('1743150705994133506') || item.roleIds.includes('1643857093725327362')) {
      store.commit('setOpenRoute', { name: '用户信息详情', path: '/system/SubmitUser', query: { id: item.id, utype: 1 } })
    }
  }
}

const handleMessages = async (conversationType, targetId) => {
  const res = await RongIMLib.getConversation({ conversationType, targetId })
  return res
}
const handleSendMessage = async () => {
  const targetId = appOnlyHeader.value + userInfo.value.accountId
  const { code, data } = await handleMessages(1, targetId)
  if (!code) {
    let newSendMessage = {
      isTemporary: true,
      isTop: data.isTop,
      isNotInform: data.notificationStatus,
      id: data.targetId,
      targetId: data.targetId,
      type: data.conversationType,
      chatObjectInfo: {
        uid: data.targetId,
        id: userInfo.value.accountId,
        name: userInfo.value.userName,
        img: userInfo.value.photo || userInfo.value.headImg,
        userInfo: {
          userId: userInfo.value.id,
          userName: userInfo.value.userName,
          photo: userInfo.value.photo,
          headImg: userInfo.value.headImg
        }
      },
      sentTime: data.latestMessage?.sentTime || Date.parse(new Date()),
      messageType: data.latestMessage?.messageType || 'RC:TxtMsg',
      content: data.latestMessage?.content || { content: '' },
      count: data.unreadMessageCount
    }
    emit('send', newSendMessage)
  }
}
const SelectPersonTab = async (resolve) => {
  const { data } = await api.SelectPersonTab({ tabCodes: ['relationBooksTemp'] })
  const newLabelData = []
  for (let index = 0; index < data[0]?.chooseLabels.length; index++) {
    const item = data[0]?.chooseLabels[index]
    newLabelData.push({ id: item.labelCode, label: item.name, children: [], type: 'label', isLeaf: false })
  }
  labelAll.value = newLabelData
  resolve(newLabelData)
}

const handleTreeData = (id, data) => {
  const newLabelData = []
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item.code !== id) {
      const children = handleTreeData(id, item.children)
      newLabelData.push({ id: item.code, label: item.name, children: children, type: 'tree', isLeaf: false })
    }
  }
  return newLabelData
}
const SelectPersonGroup = async (id) => {
  const { data } = await api.SelectPersonGroup({ labelCode: id, tabCode: 'relationBooksTemp' })
  return handleTreeData(id, data)
}
const SelectPersonBookUser = async (parentId, id) => {
  const { data } = await api.SelectPersonBookUser({
    isOpen: 1,
    keyword: '',
    labelCode: parentId,
    nodeId: id,
    relationBookId: id,
    tabCode: 'relationBooksTemp'
  })
  const newUserData = []
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item.userId)
      newUserData.push({ id: item.userId, label: item.userName, children: [], type: 'user', user: item, isLeaf: true })
  }
  return newUserData
}
const loadNode = async (node, resolve) => {
  if (node.level === 0) {
    SelectPersonTab(resolve)
  } else {
    if (node.data?.children?.length) {
      const newTreeData = node.data?.children
      const newUserData = await SelectPersonBookUser(node.parent.key, node.key)
      const newData = [...newTreeData, ...newUserData]
      resolve(newData)
    } else {
      if (node.parent.level) {
        const newUserData = await SelectPersonBookUser(node.parent.key, node.key)
        resolve(newUserData)
      } else {
        const newTreeData = await SelectPersonGroup(node.key)
        const newUserData = await SelectPersonBookUser(node.key, node.key)
        resolve([...newTreeData, ...newUserData])
      }
    }
  }
}
const relationBookMemberOftenList = async (id) => {
  const { data } = await api.relationBookMemberOftenList()
  oftenList.value = data
}
const handleOftenList = (type) => {
  relationBookMemberSetOften(type)
}
const relationBookMemberSetOften = async (type) => {
  const { code } = await api.relationBookMemberSetOften({ isOften: type, userIds: [userId.value] })
  if (code === 200) {
    relationBookMemberOftenList()
    SelectPersonTab((data) => {
      treeRef.value?.store.setData(data)
    })
    ElMessage({ message: `${type ? '收藏为' : '移除'}常用联系人成功！`, type: 'success' })
  }
}
const handleRefresh = async () => {
  SelectPersonTab((data) => {
    treeRef.value?.store.setData(data)
  })
}
onMounted(() => {
  relationBookMemberOftenList()
})
defineExpose({ refresh: handleRefresh })
</script>
<style lang="scss">
.GlobalChatAddressBook {
  width: 100%;
  height: 100%;
  display: flex;

  &.GlobalChatMacAddressBook {
    .GlobalChatAddressBookList {
      .GlobalChatAddressBookInput {
        height: 56px;
      }

      .GlobalChatAddressBookScrollbar {
        height: calc(100% - 56px);
      }
    }

    .GlobalChatAddressBookDrag {
      height: 56px;
    }
  }

  .GlobalChatAddressBookList {
    width: 280px;
    height: 100%;
    border-right: 1px solid var(--zy-el-border-color-lighter);

    .GlobalChatAddressBookInput {
      width: 100%;
      height: 66px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 20px 0 20px;
      -webkit-app-region: drag;

      .zy-el-autocomplete {
        width: 240px;
        height: var(--zy-height-routine);
        -webkit-app-region: no-drag;

        .zy-el-input {
          width: 240px;
          height: var(--zy-height-routine);
        }
      }
    }

    .GlobalChatAddressBookScrollbar {
      width: 100%;
      height: calc(100% - 66px);

      .zy-el-tree {
        padding: 0 20px 20px 20px;

        .zy-el-tree-node {
          .zy-el-tree-node__content {
            height: auto;
            padding: 10px 0;
            position: relative;
            background: transparent;
          }
        }
      }

      .GlobalChatAddressBookLabel {
        &::after {
          content: '';
          width: 100%;
          border-bottom: 1px solid var(--zy-el-border-color-lighter);
          position: absolute;
          right: 0;
          bottom: 0;
        }
      }

      .GlobalChatAddressBookItem {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        &.is-active {
          color: var(--zy-el-color-primary);
        }

        .zy-el-image {
          width: 38px;
          height: 38px;
          border-radius: 50%;
          overflow: hidden;
        }

        .GlobalChatAddressBookName {
          width: calc(100% - 54px);
          font-size: 14px;

          &::after {
            content: '';
            width: calc(100% - 54px);
            border-bottom: 1px solid var(--zy-el-border-color-lighter);
            position: absolute;
            right: 0;
            bottom: -10px;
          }
        }
      }
    }
  }

  .GlobalChatAddressBookDrag {
    width: calc(100% - 280px);
    height: 66px;
    position: relative;
    -webkit-app-region: drag;

    &::before {
      content: '';
      width: 96px;
      height: 28px;
      position: absolute;
      top: 0;
      right: 0;
      background: transparent;
      -webkit-app-region: no-drag;
    }
  }

  .GlobalChatAddressBookBody {
    width: calc(100% - 280px);
    height: 100%;

    .GlobalChatAddressBookInfo {
      width: 100%;
      height: 180px;
      display: flex;
      align-items: center;
      padding: 0 40px;
      position: relative;
      border-bottom: 1px solid var(--zy-el-border-color-lighter);
      -webkit-app-region: drag;

      &::before {
        content: '';
        width: 96px;
        height: 28px;
        position: absolute;
        top: 0;
        right: 0;
        background: transparent;
        -webkit-app-region: no-drag;
      }

      .zy-el-image {
        width: 78px;
        height: 78px;
        border-radius: 8%;
        overflow: hidden;
        -webkit-app-region: no-drag;
      }

      .GlobalChatAddressBookInfoBody {
        padding-left: 16px;
        -webkit-app-region: no-drag;

        .GlobalChatAddressBookName {
          width: 100%;
          display: flex;
          align-items: center;
          font-size: 18px;
          line-height: 26px;
          font-weight: bold;
          padding-bottom: 6px;

          span {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 12px;
          }

          .GlobalChatAddressBookIcon {
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-left: 20px;

            .zy-el-icon {
              cursor: pointer;
              font-size: 18px;
              color: var(--zy-el-text-color-secondary);
            }

            .is-active {
              font-size: 20px;
              color: var(--zy-el-color-warning);
            }
          }
        }

        .GlobalChatAddressBookText {
          width: 100%;
          height: 24px;
          font-size: 14px;
          line-height: 24px;
          padding-top: 2px;
          display: flex;
          align-items: center;

          span {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 6px;
          }
        }
      }

      .zy-el-button {
        position: absolute;
        right: var(--zy-distance-two);
        bottom: 12px;
        height: var(--zy-height-secondary);
        -webkit-app-region: no-drag;
      }
    }

    .GlobalChatAddressBookUserScroll {
      width: 100%;
      height: calc(100% - 180px);

      .GlobalChatAddressBookUserBody {
        padding: 20px 40px;

        .GlobalChatAddressBookUserText {
          width: 100%;
          height: 24px;
          font-size: 14px;
          line-height: 24px;
          padding-top: 2px;
        }
      }
    }
  }
}

.GlobalChatAddressBookAutocomplete {
  .GlobalChatAddressBookItem {
    width: 218px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    position: relative;

    &.is-active {
      color: var(--zy-el-color-primary);
    }

    .zy-el-image {
      width: 38px;
      height: 38px;
      border-radius: 50%;
      overflow: hidden;
    }

    .GlobalChatAddressBookName {
      width: calc(100% - 54px);
      font-size: 14px;
      line-height: normal;

      &::after {
        content: '';
        width: calc(100% - 54px);
        border-bottom: 1px solid var(--zy-el-border-color-lighter);
        position: absolute;
        right: 0;
        bottom: -10px;
      }
    }
  }
}
</style>
