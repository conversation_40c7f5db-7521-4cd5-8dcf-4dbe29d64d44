/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    GlobalDynamicInput: typeof import('./src/components/global-dynamic-title/global-dynamic-input.vue')['default']
    GlobalDynamicTitle: typeof import('./src/components/global-dynamic-title/global-dynamic-title.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SimilarityDetails: typeof import('./src/components/SimilarityQuery/SimilarityDetails.vue')['default']
    SimilarityQuery: typeof import('./src/components/SimilarityQuery/SimilarityQuery.vue')['default']
    SuggestPrint: typeof import('./src/components/suggestPrint/suggestPrint.vue')['default']
    SuggestRecommendType: typeof import('./src/components/SuggestRecommendType/SuggestRecommendType.vue')['default']
    SuggestRecommendUnit: typeof import('./src/components/SuggestRecommendUnit/SuggestRecommendUnit.vue')['default']
    SuggestRecommendUser: typeof import('./src/components/SuggestRecommendUser/SuggestRecommendUser.vue')['default']
    SuggestSelectUnit: typeof import('./src/components/suggest-select-unit/suggest-select-unit.vue')['default']
    SuggestSimpleSelectUnit: typeof import('./src/components/suggest-simple-select-unit/suggest-simple-select-unit.vue')['default']
  }
}
