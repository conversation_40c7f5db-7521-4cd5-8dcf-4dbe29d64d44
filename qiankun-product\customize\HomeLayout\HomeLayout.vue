<template>
  <div class="HomeLayout">
    <div class="home-header">
      <img class="home-logo" src="../img/home_toptext_bg.png" alt="logo" />
      <div class="home-header-right">
        <div class="search-box">
          <input type="text" placeholder="请输入搜索内容" />
          <button class="search-btn" @click="search">
            <img class="search-icon" src="../img/search_icon.png" alt="搜索" />
          </button>
        </div>
        <button class="login-btn" @click="openLogin">
          <img src="../img/login_btn_bg.png" alt="登录首页" />
          <span style="color: #003399;">登录首页</span>
        </button>
        <button class="mine-btn" @click="openLogin">
          <img src="../img/mine_btn_bg.png" alt="我的" />
          <span>
            <img class="mine-icon" src="../img/mine_icon.png" alt="我的" />
            我的
          </span>
        </button>
      </div>
    </div>
    <div class="home-content">
      <div class="menu-list">
        <div class="menu-item menu-bg1 u-top" @click="openLogin">
          <img class="menu-icon" src="../img/menu_icon1.png" alt="我的工作" style="margin-top: -20px;margin-left: 35px;" />
          <div class="menu-title" style="margin-left: 35px;">我的工作</div>
        </div>
        <div class="menu-item menu-bg2 u-bottom" @click="openLogin" style="margin-left: -65px;height: 400px;">
          <img class="menu-icon" src="../img/menu_icon2.png" alt="我的待办" style="margin-top: -65px;" />
          <div class="menu-title">我的待办</div>
        </div>
        <div class="menu-item menu-bg3 u-bottom" @click="openLogin" style="margin-left: -90px;height: 400px;">
          <img class="menu-icon" src="../img/menu_icon3.png" alt="综合应用" style="margin-top: -65px;" />
          <div class="menu-title">综合应用</div>
        </div>
        <div class="menu-item menu-bg4 u-top" @click="openLogin" style="margin-left: -70px;">
          <img class="menu-icon" src="../img/menu_icon4.png" alt="其他应用" style="margin-top: -20px;margin-right: 50px;" />
          <div class="menu-title" style="margin-right: 50px;">其他应用</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'HomeLayout' }
</script>
<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
const search = () => {
  console.log('搜索')
}
const openLogin = () => {
  router.push({ name: 'LoginView' })
}
</script>
<style lang="scss">
.HomeLayout {
  width: 100%;
  height: 100%;
  background: url("../img/home_layout_bg.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;

  .home-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 40px 0 40px;
    box-sizing: border-box;
    position: relative;
    z-index: 2;
    flex-shrink: 0;

    .home-logo {
      height: 74px;
    }

    .home-header-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-box {
        display: flex;
        align-items: center;
        border-radius: 20px;
        padding: 0 0 0 8px;
        height: 36px;
        border: 1px solid #FFFFFF;
        width: 350px;

        input {
          border: none;
          outline: none;
          height: 100%;
          padding: 0 8px;
          border-radius: 20px 0 0 20px;
          background: rgb(0, 0, 0, 0);
          width: calc(100% - 55px);
          color: #fff;

          &::placeholder {
            color: #fff;
            opacity: 1;
          }
        }

        .search-btn {
          background: url("../img/search_btn_bg.png") no-repeat center/cover;
          border: none;
          width: 55px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .search-icon {
            width: 18px;
            height: 18px;
          }
        }
      }

      .login-btn,
      .mine-btn {
        background: none;
        border: none;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0;
        cursor: pointer;

        img {
          height: 39px;
        }

        span {
          position: absolute;
          left: 0;
          width: 100%;
          text-align: center;
          color: #fff;
          font-size: 14px;
          line-height: 39px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .mine-btn .mine-icon {
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
    }
  }

  .home-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 35vh;
    position: relative;
    z-index: 1;

    .menu-list {
      width: calc(100% - 240px); // 两边各120px
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      position: relative;
    }

    .menu-item {
      flex: 1 1 0;
      max-width: 475px;
      min-width: 220px;
      aspect-ratio: 475/450; // 保持比例
      border-radius: 24px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      // box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.15);
      cursor: pointer;
      transition: transform 0.2s;
      margin: 0;

      &:hover {
        // transform: translateY(-8px) scale(1.03);
        // box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.18);
      }

      .menu-icon {
        width: 82px;
        height: 82px;
      }

      .menu-title {
        color: #fff;
        font-size: 28px;
        font-weight: bold;
        margin-top: 5px;
      }
    }

    // U形布局
    .u-top {
      margin-top: 0;
    }

    .u-bottom {
      margin-top: 50px; // 第二、第三个往下
    }

    .menu-bg1 {
      background-image: url('../img/menu_bg1.png');
    }

    .menu-bg2 {
      background-image: url('../img/menu_bg2.png');
    }

    .menu-bg3 {
      background-image: url('../img/menu_bg3.png');
    }

    .menu-bg4 {
      background-image: url('../img/menu_bg4.png');
    }
  }
}
</style>
