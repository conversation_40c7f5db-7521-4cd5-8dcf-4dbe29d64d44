<template>
  <div class="SuggestNumberingSubmit">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="届次" prop="termYearId">
        <el-select v-model="form.termYearId" placeholder="请选择届次" clearable>
          <el-option v-for="item in termYearData" :key="item.termYearId" :label="item.name" :value="item.termYearId">
            <template #default>
              {{ item.name }}
              <span v-if="item.isTermYearCurrent" class="FormTermYearCurrent"> (当前届次)</span>
            </template>
          </el-option>
        </el-select>
      </el-form-item>
      <div class="zy-el-form-item-br"></div>
      <el-form-item label="大会编号前缀">
        <el-input v-model="form.meetingNumberPrefix" placeholder="请输入大会编号前缀" clearable />
      </el-form-item>
      <el-form-item label="大会起始编号" prop="meetingStartNumber">
        <el-input v-model="form.meetingStartNumber" maxlength="10" show-word-limit
          @input="form.meetingStartNumber = validNum(form.meetingStartNumber)" placeholder="请输入大会起始编号" clearable />
      </el-form-item>
      <el-form-item label="平时编号前缀">
        <el-input v-model="form.usualNumberPrefix" placeholder="请输入平时编号前缀" clearable />
      </el-form-item>
      <el-form-item label="平时起始编号" prop="usualStartNumber">
        <el-input v-model="form.usualStartNumber" maxlength="10" show-word-limit
          @input="form.usualStartNumber = validNum(form.usualStartNumber)" placeholder="请输入平时起始编号" clearable />
      </el-form-item>
      <el-form-item label="大会时间" prop="conventionTime" class="globalFormTime">
        <xyl-date-picker v-model="form.conventionTime" value-format="x" type="datetimerange"
          start-placeholder="请选择大会开始时间" end-placeholder="请选择大会结束时间" />
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SuggestNumberingSubmit' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { validNum } from 'common/js/utils.js'
import { ElMessage } from 'element-plus'
const props = defineProps({ id: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  termYearId: '',
  meetingNumberPrefix: '',
  meetingStartNumber: '',
  usualNumberPrefix: '',
  usualStartNumber: '',
  conventionTime: ''
})
const rules = reactive({
  termYearId: [{ required: true, message: '请选择届次', trigger: ['blur', 'change'] }],
  meetingStartNumber: [{ required: true, message: '请输入大会起始编号', trigger: ['blur', 'change'] }],
  usualStartNumber: [{ required: false, message: '请输入平时起始编号', trigger: ['blur', 'change'] }],
  conventionTime: [{ required: true, message: '请选择大会时间', trigger: ['blur', 'change'] }]
})
const termYearData = ref([])

onMounted(() => {
  termYearTree()
  if (props.id) { suggestionTermYearInfo() }
})

const termYearTree = async () => {
  const res = await api.termYearTree({ termYearType: 'cppcc_member' })
  var { data } = res
  var array = []
  for (let index = 0; index < data.length; index++) {
    array = [...array, ...data[index].children]
  }
  termYearData.value = array
  termYearCurrent()
}
// 获取当前届次
const termYearCurrent = async () => {
  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })
  if (termYearData.value) {
    termYearData.value = termYearData.value.map(v => ({ ...v, isTermYearCurrent: v.termYearId === data.id }))
  }
}
const suggestionTermYearInfo = async () => {
  const res = await api.suggestionTermYearInfo({ detailId: props.id })
  var { data } = res
  form.termYearId = data.termYearId
  form.meetingNumberPrefix = data.meetingNumberPrefix
  form.meetingStartNumber = data.meetingStartNumber
  form.usualNumberPrefix = data.usualNumberPrefix
  form.usualStartNumber = data.usualStartNumber
  form.conventionTime = data.meetingStartDate && data.meetingEndDate ? [data.meetingStartDate, data.meetingEndDate] : ''
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(props.id ? '/proposalTermYear/edit' : '/proposalTermYear/add', {
    form: {
      termYearId: form.termYearId,
      meetingNumberPrefix: form.meetingNumberPrefix,
      meetingStartNumber: form.meetingStartNumber,
      usualNumberPrefix: form.usualNumberPrefix,
      usualStartNumber: form.usualStartNumber,
      meetingStartDate: form.conventionTime ? form.conventionTime[0] : '',
      meetingEndDate: form.conventionTime ? form.conventionTime[1] : ''
    }
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}
const resetForm = () => { emit('callback') }
</script>
<style lang="scss">
.SuggestNumberingSubmit {
  width: 680px;
}

.FormTermYearCurrent {
  color: var(--zy-el-color-primary);
}
</style>
