{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON>s as _withKeys, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"OutcomeManagement\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_date_picker = _resolveComponent(\"xyl-date-picker\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table = _resolveComponent(\"xyl-global-table\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_export_excel = _resolveComponent(\"xyl-export-excel\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _component_SubmitMinSuggestManage = _resolveComponent(\"SubmitMinSuggestManage\");\n  var _component_SubmitExamine = _resolveComponent(\"SubmitExamine\");\n  var _component_SubmitHandle = _resolveComponent(\"SubmitHandle\");\n  var _component_SubmitReplyManage = _resolveComponent(\"SubmitReplyManage\");\n  var _component_SubmitReply = _resolveComponent(\"SubmitReply\");\n  var _component_SubmitJoinManage = _resolveComponent(\"SubmitJoinManage\");\n  var _component_SubmitApplyChange = _resolveComponent(\"SubmitApplyChange\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList,\n    data: $setup.tableHead,\n    ref: \"queryRef\"\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"]), _createVNode(_component_xyl_date_picker, {\n        modelValue: $setup.year,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.year = $event;\n        }),\n        placeholder: \"请选择年份\",\n        onChange: $setup.queryChange,\n        \"value-format\": \"YYYY\",\n        type: \"year\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\", \"data\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect,\n    onSortChange: $setup.handleSortChange,\n    \"header-cell-class-name\": $setup.handleHeaderClass\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_xyl_global_table, {\n        tableHead: $setup.tableHead,\n        onTableClick: $setup.handleTableClick\n      }, null, 8 /* PROPS */, [\"tableHead\"]), _createVNode(_component_xyl_global_table_button, {\n        data: $setup.tableButtonList,\n        max: 13,\n        elWhetherShow: $setup.handleElWhetherShow,\n        onButtonClick: $setup.handleCommand,\n        editCustomTableHead: $setup.handleEditorCustom\n      }, null, 8 /* PROPS */, [\"editCustomTableHead\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\", \"onSortChange\", \"header-cell-class-name\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.showExportExcel,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.showExportExcel = $event;\n    }),\n    name: \"导出Excel\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_xyl_export_excel, {\n        tableId: \"id_micro_advice\",\n        module: \"microAdviceManageExcel\",\n        name: \"微建议管理\",\n        exportId: $setup.exportId,\n        params: $setup.exportExcelParams,\n        onExcelCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"exportId\", \"params\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: $setup.id ? '编辑微建议' : '新增微建议'\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_SubmitMinSuggestManage, {\n        id: $setup.id,\n        onCallback: $setup.submitCallback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.examineShow,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n      return $setup.examineShow = $event;\n    }),\n    name: $setup.nextNodeId ? '不予受理' : '审核'\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_SubmitExamine, {\n        id: $setup.id,\n        width: \"800px\",\n        nextNodeId: $setup.nextNodeId,\n        onCallback: $setup.examineCallback\n      }, null, 8 /* PROPS */, [\"id\", \"nextNodeId\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.handleShow,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n      return $setup.handleShow = $event;\n    }),\n    name: \"交办\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_SubmitHandle, {\n        id: $setup.id,\n        onCallback: $setup.examineCallback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.replyManageShow,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n      return $setup.replyManageShow = $event;\n    }),\n    name: \"回复管理\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_SubmitReplyManage, {\n        id: $setup.id,\n        onCallback: $setup.examineCallback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.replyShow,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n      return $setup.replyShow = $event;\n    }),\n    name: \"回复\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_SubmitReply, {\n        id: $setup.id,\n        userType: \"manageReply\",\n        onCallback: $setup.examineCallback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.joinManageShow,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = function ($event) {\n      return $setup.joinManageShow = $event;\n    }),\n    name: \"协办管理\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_SubmitJoinManage, {\n        id: $setup.id,\n        onCallback: $setup.examineCallback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.applyShow,\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = function ($event) {\n      return $setup.applyShow = $event;\n    }),\n    name: $setup.applyName\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_SubmitApplyChange, {\n        id: $setup.id,\n        type: $setup.applyType,\n        disabled: $setup.disabled,\n        onCallback: $setup.examineCallback\n      }, null, 8 /* PROPS */, [\"id\", \"type\", \"disabled\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "data", "tableHead", "ref", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_component_xyl_date_picker", "year", "onChange", "query<PERSON>hange", "type", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "onSortChange", "handleSortChange", "handleHeaderClass", "default", "_component_el_table_column", "width", "fixed", "_component_xyl_global_table", "onTableClick", "handleTableClick", "_component_xyl_global_table_button", "tableButtonList", "max", "elWhetherShow", "handleElWhetherShow", "onButtonClick", "handleCommand", "editCustomTableHead", "handleEditorCustom", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "showExportExcel", "name", "_component_xyl_export_excel", "tableId", "module", "exportId", "params", "exportExcelParams", "onExcelCallback", "callback", "show", "id", "_component_SubmitMinSuggestManage", "onCallback", "submitCallback", "examineShow", "nextNodeId", "_component_SubmitExamine", "examine<PERSON><PERSON><PERSON>", "handleShow", "_component_SubmitHandle", "replyManageShow", "_component_SubmitReplyManage", "replyShow", "_component_SubmitReply", "userType", "joinManageShow", "_component_SubmitJoinManage", "applyShow", "applyName", "_component_SubmitApplyChange", "applyType", "disabled"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\negotiation\\src\\views\\OutcomeManagement\\OutcomeManagement.vue"], "sourcesContent": ["<template>\r\n  <div class=\"OutcomeManagement\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\" :data=\"tableHead\" ref=\"queryRef\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n        <xyl-date-picker v-model=\"year\" placeholder=\"请选择年份\" @change=\"queryChange\" value-format=\"YYYY\" type=\"year\" />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\" @sort-change=\"handleSortChange\" :header-cell-class-name=\"handleHeaderClass\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\"></xyl-global-table>\r\n        <xyl-global-table-button :data=\"tableButtonList\" :max=\"13\" :elWhetherShow=\"handleElWhetherShow\"\r\n          @buttonClick=\"handleCommand\" :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"showExportExcel\" name=\"导出Excel\">\r\n      <xyl-export-excel tableId=\"id_micro_advice\" module=\"microAdviceManageExcel\" name=\"微建议管理\" :exportId=\"exportId\"\r\n        :params=\"exportExcelParams\" @excelCallback=\"callback\"></xyl-export-excel>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"show\" :name=\"id ? '编辑微建议' : '新增微建议'\">\r\n      <SubmitMinSuggestManage :id=\"id\" @callback=\"submitCallback\"></SubmitMinSuggestManage>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"examineShow\" :name=\"nextNodeId ? '不予受理' : '审核'\">\r\n      <SubmitExamine :id=\"id\" width=\"800px\" :nextNodeId=\"nextNodeId\" @callback=\"examineCallback\"></SubmitExamine>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"handleShow\" name=\"交办\">\r\n      <SubmitHandle :id=\"id\" @callback=\"examineCallback\"></SubmitHandle>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"replyManageShow\" name=\"回复管理\">\r\n      <SubmitReplyManage :id=\"id\" @callback=\"examineCallback\"></SubmitReplyManage>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"replyShow\" name=\"回复\">\r\n      <SubmitReply :id=\"id\" userType=\"manageReply\" @callback=\"examineCallback\"></SubmitReply>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"joinManageShow\" name=\"协办管理\">\r\n      <SubmitJoinManage :id=\"id\" @callback=\"examineCallback\"></SubmitJoinManage>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"applyShow\" :name=\"applyName\">\r\n      <SubmitApplyChange :id=\"id\" :type=\"applyType\" :disabled=\"disabled\" @callback=\"examineCallback\">\r\n      </SubmitApplyChange>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'OutcomeManagement' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated } from 'vue'\r\n// import { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\n// import { exportWordHtmlList } from 'common/config/MicroGlobal'\r\n// import { publicOpinionExportWord } from '@/assets/js/publicOpinionExportWord'\r\n// import SubmitMinSuggestManage from './component/SubmitMinSuggestManage';\r\n// import SubmitHandle from \"./component/SubmitHandle\";\r\n// import SubmitExamine from \"./component/SubmitExamine\";\r\n// import SubmitReply from \"./component/SubmitReply\";\r\n// import SubmitApplyChange from './component/SubmitApplyChange';\r\n// import SubmitReplyManage from \"./component/SubmitReplyManage\";\r\n// import SubmitJoinManage from \"./component/SubmitJoinManage\";\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { qiankunMicro } from \"common/config/MicroGlobal\";\r\nimport { useRoute } from 'vue-router'\r\nconst route = useRoute()\r\nconst tableButtonList = [{ id: 'showEdit', name: '编辑', width: 30, has: '', whetherShow: true },\r\n{ id: 'showAudit', name: '审核', width: 30, has: '', whetherShow: true },\r\n{ id: 'showReply', name: '回复', width: 30, has: '', whetherShow: true },\r\n{ id: 'showHandle', name: '交办', width: 30, has: '', whetherShow: true },\r\n{ id: 'showApplyChange', name: '申请调整', width: 40, has: '', whetherShow: true },\r\n{ id: 'showBack', name: '退回', width: 30, has: '', whetherShow: true },\r\n{ id: 'showReject', name: '不予处理', width: 40, has: '', whetherShow: true },\r\n{ id: 'showChangeAudit', name: '申诉审核', width: 40, has: '', whetherShow: true },\r\n{ id: 'showReplyManage', name: '回复管理', width: 40, has: '', whetherShow: true },\r\n{ id: 'showJoinManage', name: '协办', width: 30, has: '', whetherShow: true },\r\n{ id: 'showEvaluationDetail', name: '查看评价', width: 40, has: '', whetherShow: true },\r\n{ id: 'showCancelColar', name: '取消领办', width: 40, has: '', whetherShow: true }\r\n]\r\nconst buttonList = [\r\n  // { id: 'new', name: '新增', type: 'primary', has: '' },\r\n  { id: 'export', name: '导出excel', type: 'primary', has: 'export' },\r\n  { id: 'exportWord', name: '导出word', type: 'primary', has: 'export' },\r\n  { id: 'del', name: '删除', type: '', has: 'del' },\r\n  { id: 'batchToAudit', name: '一键还原待审核', type: '', has: 'batch_audit' },\r\n  { id: 'batchToReply', name: '一键还原未回复', type: '', has: 'batch_reply' },\r\n]\r\nconst showExportExcel = ref(false)\r\nconst id = ref('')\r\nconst exportId = ref([])\r\nconst show = ref(false)\r\nconst examineShow = ref(false)\r\nconst replyManageShow = ref(false)\r\nconst handleShow = ref(false)\r\nconst replyShow = ref(false)\r\nconst applyShow = ref(false)\r\nconst joinManageShow = ref(false)\r\nconst hasReply = ref('')\r\nconst applyName = ref('')\r\nconst applyType = ref('')\r\nconst nextNodeId = ref('')\r\nconst disabled = ref(false)\r\nconst hasEvaluation = ref('')\r\nconst year = ref('')\r\nconst exportExcelParams = ref({})\r\n\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  queryRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  exportShow,\r\n  tableDataArray,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleEditorCustom,\r\n  // handleGetParams,\r\n  tableHead,\r\n  tableQuery,\r\n} = GlobalTable(\r\n  {\r\n    tableId: 'id_micro_negotiate',\r\n    tableApi: 'microAdviceList',\r\n    delApi: 'microAdviceDels',\r\n    tableDataObj: {\r\n      orderBys: [\r\n        {\r\n          columnId: 'id_micro_advice_create_date',\r\n          isDesc: 1\r\n        }\r\n      ],\r\n      query: {\r\n        curry: '2'\r\n      }\r\n    }\r\n  })\r\nconst ids = ref([])\r\n\r\nonActivated(() => {\r\n  if (route.query.id) {\r\n    ids.value = JSON.parse(route.query.id)\r\n    tableQuery.value = { ids: ids.value }\r\n  }\r\n  handleQuery()\r\n})\r\n\r\nconst handleElWhetherShow = (row, isType) => {\r\n  if (isType === 'showEdit') {\r\n    return row.showEdit === 1\r\n  } else if (isType === 'showAudit') {\r\n    return row.showAudit === 1\r\n  } else if (isType === 'showReply') {\r\n    return row.showReply === 1\r\n  } else if (isType === 'showHandle') {\r\n    return row.showHandle === 1\r\n  } else if (isType === 'showApplyChange') {\r\n    return row.showApplyChange === 1\r\n  } else if (isType === 'showBack') {\r\n    return row.showBack === 1\r\n  } else if (isType === 'showReject') {\r\n    return row.showReject === 1\r\n  } else if (isType === 'showChangeAudit') {\r\n    return row.showChangeAudit === 1\r\n  } else if (isType === 'showReplyManage') {\r\n    return row.showReplyManage === 1\r\n  } else if (isType === 'showJoinManage') {\r\n    return row.showJoinManage === 1\r\n  } else if (isType === 'showEvaluationDetail') {\r\n    return row.showEvaluationDetail === 1\r\n  } else if (isType === 'showCancelColar') {\r\n    return row.showCancelColar === 1\r\n  }\r\n}\r\n\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'showEdit':\r\n      handleEdit(row)\r\n      break\r\n    case 'showAudit':\r\n      handleExamine(row)\r\n      break\r\n    case 'showReply':\r\n      handleReply(row)\r\n      break\r\n    case 'showHandle':\r\n      onHandle(row)\r\n      break\r\n    case 'showApplyChange':\r\n      handleApply(row)\r\n      break\r\n    case 'showBack':\r\n      handleBack(row)\r\n      break\r\n    case 'showReject':\r\n      handleReject(row)\r\n      break\r\n    case 'showChangeAudit':\r\n      handleChangeAudit(row)\r\n      break\r\n    case 'showReplyManage':\r\n      handleReplyManage(row)\r\n      break\r\n    case 'showJoinManage':\r\n      handleJoinManage(row)\r\n      break\r\n    case 'showEvaluationDetail':\r\n      handleEvaluationDetail(row)\r\n      break\r\n    case 'showCancelColar':\r\n      handleCancelColar(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n\r\nconst handleButton = (id) => {\r\n  switch (id) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'del':\r\n      handleDel()\r\n      break\r\n    case 'export':\r\n      exportExcel()\r\n      break\r\n    case 'batchToAudit':\r\n      batchToAudit()\r\n      break\r\n    case 'exportWord':\r\n      // handleExportWord()\r\n      // publicOpinionExportWord(handleGetParams(), 'microAdviceList')\r\n      break\r\n    case 'batchToReply':\r\n      batchToReply()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n\r\nconst handleDel = () => {\r\n  if (tableDataArray.value.length) {\r\n    ElMessageBox.confirm(`此操作将删除当前选中的数据, 是否继续?`, '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(() => { globalDel() }).catch(() => { ElMessage({ type: 'info', message: `已取消删除` }) })\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n  }\r\n}\r\nconst globalDel = async () => {\r\n  const { code, data } = await api.microAdviceDels({ ids: tableDataArray.value.map(v => v.id) })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', dangerouslyUseHTMLString: true, message: data })\r\n    tableDataArray.value = []\r\n    tableRef.value.clearSelection()\r\n    handleQuery()\r\n  }\r\n}\r\n\r\nconst exportExcel = () => {\r\n  exportId.value = tableDataArray.value.map(item => item.id)\r\n  exportExcelParams.value = {\r\n    where: queryRef.value.getWheres(),\r\n    ids: route.query.ids ? JSON.parse(route.query.ids) : []\r\n  }\r\n  showExportExcel.value = true\r\n}\r\n\r\n\r\nconst batchToReply = () => {\r\n  if (tableDataArray.value.length) {\r\n    ElMessageBox.confirm(`此操作将一键还原未回复当前选中的数据, 是否继续?`, '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(async () => {\r\n      const { code, data } = await api.batchToUnAnswer({ ids: tableDataArray.value.map(v => v.id) })\r\n      if (code === 200) {\r\n        ElMessage({ type: 'success', dangerouslyUseHTMLString: true, message: data })\r\n        handleQuery()\r\n      }\r\n      // userBatch({ batchType: type, userIds: tableDataArray.value.map(v => v.id) }, `${text}成功`)\r\n    }).catch(() => { ElMessage({ type: 'info', message: `已取消一键还原待未回复` }) })\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n  }\r\n}\r\n\r\nconst batchToAudit = () => {\r\n  if (tableDataArray.value.length) {\r\n    ElMessageBox.confirm(`此操作将一键还原待审核当前选中的数据, 是否继续?`, '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(async () => {\r\n      const { code, data } = await api.batchToAudit({ ids: tableDataArray.value.map(v => v.id) })\r\n      if (code === 200) {\r\n        ElMessage({ type: 'success', dangerouslyUseHTMLString: true, message: data })\r\n        handleQuery()\r\n      }\r\n      // userBatch({ batchType: type, userIds: tableDataArray.value.map(v => v.id) }, `${text}成功`)\r\n    }).catch(() => { ElMessage({ type: 'info', message: `已取消一键还原待审核` }) })\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n  }\r\n}\r\n\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  hasReply.value = ''\r\n  hasEvaluation.value = ''\r\n  year.value = ''\r\n  tableQuery.value = { year: year.value || null }\r\n  handleQuery()\r\n}\r\n\r\nconst queryChange = () => {\r\n  tableQuery.value = { year: year.value || null }\r\n}\r\n\r\nconst handleNew = () => {\r\n  // id.value = ''\r\n  // show.value = true\r\n  qiankunMicro.setGlobalState({ openRoute: { name: `微建议新增`, path: '/negotiation/OutcomeReportingNew' } })\r\n}\r\n\r\nconst handleApply = (item) => {\r\n  applyName.value = '申请调整'\r\n  applyType.value = 'applyChange'\r\n  id.value = item.id\r\n  applyShow.value = true\r\n}\r\nconst handleBack = (item) => {\r\n  applyName.value = '退回'\r\n  applyType.value = 'backUnder'\r\n  id.value = item.id\r\n  applyShow.value = true\r\n}\r\n\r\nconst handleChangeAudit = (item) => {\r\n  applyName.value = '申诉审核'\r\n  applyType.value = 'changeAudit'\r\n  id.value = item.id\r\n  applyShow.value = true\r\n}\r\nconst handleEvaluationDetail = (item) => {\r\n  applyName.value = '查看评价'\r\n  applyType.value = 'memberEvaluation'\r\n  disabled.value = true\r\n  id.value = item.id\r\n  applyShow.value = true\r\n}\r\n\r\nconst handleJoinManage = (item) => {\r\n  id.value = item.id\r\n  joinManageShow.value = true\r\n}\r\n\r\nconst handleExamine = (item) => { // 审核\r\n  // id.value = item.id\r\n  // examineShow.value = true\r\n  qiankunMicro.setGlobalState({ openRoute: { name: `微建议详情`, path: '/negotiation/OutcomeManageDetails', query: { id: item.id } } })\r\n}\r\n\r\nconst handleReject = (item) => { // 不予受理\r\n  id.value = item.id\r\n  nextNodeId.value = 'passAudit'\r\n  examineShow.value = true\r\n}\r\n\r\nconst handleReply = (item) => { // 回复\r\n  // id.value = item.id\r\n  // replyShow.value = true\r\n  qiankunMicro.setGlobalState({ openRoute: { name: `成果详情`, path: '/negotiation/OutcomeManageDetails', query: { id: item.id, userType: 'manageReply' } } })\r\n}\r\n\r\nconst handleReplyManage = (item) => { // 回复管理\r\n  id.value = item.id\r\n  replyManageShow.value = true\r\n}\r\n\r\nconst onHandle = (item) => { // 交办\r\n  // id.value = item.id\r\n  // handleShow.value = true\r\n  qiankunMicro.setGlobalState({ openRoute: { name: `成果详情`, path: '/negotiation/OutcomeManageDetails', query: { id: item.id } } })\r\n}\r\nconst handleEdit = (item) => {\r\n  // id.value = item.id\r\n  // show.value = true\r\n  qiankunMicro.setGlobalState({ openRoute: { name: `编辑成果`, path: '/negotiation/OutcomeReportingNew', query: { id: item.id } } })\r\n}\r\n\r\nconst submitCallback = () => { // 新增编辑\r\n  show.value = false\r\n  handleQuery()\r\n}\r\n\r\nconst callback = () => {\r\n  showExportExcel.value = false\r\n  exportShow.value = false\r\n  handleQuery()\r\n}\r\n\r\nconst examineCallback = () => { // 审核回调\r\n  examineShow.value = false\r\n  handleShow.value = false\r\n  replyManageShow.value = false\r\n  replyShow.value = false\r\n  joinManageShow.value = false\r\n  applyShow.value = false\r\n  handleQuery()\r\n}\r\n\r\nconst handleCancelColar = (item) => {\r\n  ElMessageBox.confirm(`是否取消领办?`, '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    const { code } = await api.complete({ microAdviceId: item.id, nextNodeId: 'cancelColar' })\r\n    if (code === 200) {\r\n      ElMessage({ type: 'success', message: `取消领办成功` })\r\n      handleQuery()\r\n    }\r\n  }).catch(() => { ElMessage({ type: 'info', message: `已取消` }) })\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      qiankunMicro.setGlobalState({ openRoute: { name: `成果详情`, path: '/negotiation/OutcomeManageDetails', query: { id: row.id, userType: row.showReply ? 'manageReply' : '' } } })\r\n      break\r\n    case 'comment':\r\n      qiankunMicro.setGlobalState({ openRoute: { name: `评论`, path: '/minSuggest/MinSuggestComment', query: { id: row.id, type: 'min_suggest' } } })\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.OutcomeManagement {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 10px 20px;\r\n\r\n  .xyl-search {\r\n    width: 460px;\r\n\r\n    .zy-el-date-editor {\r\n      //width: 120px;\r\n      margin-left: 20px;\r\n    }\r\n\r\n    .zy-el-select,\r\n    .zy-el-date-editor {\r\n      margin-left: 20px;\r\n    }\r\n  }\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - 116px);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAQvBA,KAAK,EAAC;AAAa;;EASnBA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;;;;;uBAjB/BC,mBAAA,CAgDM,OAhDNC,UAgDM,GA/CJC,YAAA,CAMoBC,4BAAA;IANAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IAAGC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IAAGC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC/FC,UAAU,EAAEN,MAAA,CAAAM,UAAU;IAAGC,IAAI,EAAEP,MAAA,CAAAQ,SAAS;IAAEC,GAAG,EAAC;;IACpCC,MAAM,EAAAC,QAAA,CACf;MAAA,OAAwF,CAAxFd,YAAA,CAAwFe,mBAAA;QALhGC,UAAA,EAK2Bb,MAAA,CAAAc,OAAO;QALlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAK2BhB,MAAA,CAAAc,OAAO,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAL/DC,SAAA,CAKuEnB,MAAA,CAAAC,WAAW;QAAEmB,SAAS,EAAT;0DAC5EvB,YAAA,CAA4GwB,0BAAA;QANpHR,UAAA,EAMkCb,MAAA,CAAAsB,IAAI;QANtC,uBAAAP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAMkChB,MAAA,CAAAsB,IAAI,GAAAN,MAAA;QAAA;QAAEC,WAAW,EAAC,OAAO;QAAEM,QAAM,EAAEvB,MAAA,CAAAwB,WAAW;QAAE,cAAY,EAAC,MAAM;QAACC,IAAI,EAAC;;;IAN3GC,CAAA;+CASIC,mBAAA,CAQM,OARNC,UAQM,GAPJ/B,YAAA,CAMWgC,mBAAA;IANDpB,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAAEF,IAAI,EAAEP,MAAA,CAAA8B,SAAS;IAAGC,QAAM,EAAE/B,MAAA,CAAAgC,iBAAiB;IAC/EC,WAAU,EAAEjC,MAAA,CAAAgC,iBAAiB;IAAGE,YAAW,EAAElC,MAAA,CAAAmC,gBAAgB;IAAG,wBAAsB,EAAEnC,MAAA,CAAAoC;;IAXjGC,OAAA,EAAA1B,QAAA,CAYQ;MAAA,OAAuE,CAAvEd,YAAA,CAAuEyC,0BAAA;QAAtDb,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACc,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/D3C,YAAA,CAA2F4C,2BAAA;QAAxEjC,SAAS,EAAER,MAAA,CAAAQ,SAAS;QAAGkC,YAAU,EAAE1C,MAAA,CAAA2C;8CACtD9C,YAAA,CACmG+C,kCAAA;QADzErC,IAAI,EAAEP,MAAA,CAAA6C,eAAe;QAAGC,GAAG,EAAE,EAAE;QAAGC,aAAa,EAAE/C,MAAA,CAAAgD,mBAAmB;QAC3FC,aAAW,EAAEjD,MAAA,CAAAkD,aAAa;QAAGC,mBAAmB,EAAEnD,MAAA,CAAAoD;;;IAf7D1B,CAAA;sGAkBIC,mBAAA,CAIM,OAJN0B,UAIM,GAHJxD,YAAA,CAE+ByD,wBAAA;IAFRC,WAAW,EAAEvD,MAAA,CAAAwD,MAAM;IAnBhD,wBAAAzC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAmB0ChB,MAAA,CAAAwD,MAAM,GAAAxC,MAAA;IAAA;IAAU,WAAS,EAAEhB,MAAA,CAAAyD,QAAQ;IAnB7E,qBAAA1C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAmBqEhB,MAAA,CAAAyD,QAAQ,GAAAzC,MAAA;IAAA;IAAG,YAAU,EAAEhB,MAAA,CAAA0D,SAAS;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAE5D,MAAA,CAAAC,WAAW;IAAG4D,eAAc,EAAE7D,MAAA,CAAAC,WAAW;IACvG6D,KAAK,EAAE9D,MAAA,CAAA+D,MAAM;IAAEC,UAAU,EAAV;qHAEpBnE,YAAA,CAGmBoE,2BAAA;IA1BvBpD,UAAA,EAuB+Bb,MAAA,CAAAkE,eAAe;IAvB9C,uBAAAnD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAuB+BhB,MAAA,CAAAkE,eAAe,GAAAlD,MAAA;IAAA;IAAEmD,IAAI,EAAC;;IAvBrD9B,OAAA,EAAA1B,QAAA,CAwBM;MAAA,OAC2E,CAD3Ed,YAAA,CAC2EuE,2BAAA;QADzDC,OAAO,EAAC,iBAAiB;QAACC,MAAM,EAAC,wBAAwB;QAACH,IAAI,EAAC,OAAO;QAAEI,QAAQ,EAAEvE,MAAA,CAAAuE,QAAQ;QACzGC,MAAM,EAAExE,MAAA,CAAAyE,iBAAiB;QAAGC,eAAa,EAAE1E,MAAA,CAAA2E;;;IAzBpDjD,CAAA;qCA2BI7B,YAAA,CAEmBoE,2BAAA;IA7BvBpD,UAAA,EA2B+Bb,MAAA,CAAA4E,IAAI;IA3BnC,uBAAA7D,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2B+BhB,MAAA,CAAA4E,IAAI,GAAA5D,MAAA;IAAA;IAAGmD,IAAI,EAAEnE,MAAA,CAAA6E,EAAE;;IA3B9CxC,OAAA,EAAA1B,QAAA,CA4BM;MAAA,OAAqF,CAArFd,YAAA,CAAqFiF,iCAAA;QAA5DD,EAAE,EAAE7E,MAAA,CAAA6E,EAAE;QAAGE,UAAQ,EAAE/E,MAAA,CAAAgF;;;IA5BlDtD,CAAA;6CA8BI7B,YAAA,CAEmBoE,2BAAA;IAhCvBpD,UAAA,EA8B+Bb,MAAA,CAAAiF,WAAW;IA9B1C,uBAAAlE,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA8B+BhB,MAAA,CAAAiF,WAAW,GAAAjE,MAAA;IAAA;IAAGmD,IAAI,EAAEnE,MAAA,CAAAkF,UAAU;;IA9B7D7C,OAAA,EAAA1B,QAAA,CA+BM;MAAA,OAA2G,CAA3Gd,YAAA,CAA2GsF,wBAAA;QAA3FN,EAAE,EAAE7E,MAAA,CAAA6E,EAAE;QAAEtC,KAAK,EAAC,OAAO;QAAE2C,UAAU,EAAElF,MAAA,CAAAkF,UAAU;QAAGH,UAAQ,EAAE/E,MAAA,CAAAoF;;;IA/BhF1D,CAAA;6CAiCI7B,YAAA,CAEmBoE,2BAAA;IAnCvBpD,UAAA,EAiC+Bb,MAAA,CAAAqF,UAAU;IAjCzC,uBAAAtE,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAiC+BhB,MAAA,CAAAqF,UAAU,GAAArE,MAAA;IAAA;IAAEmD,IAAI,EAAC;;IAjChD9B,OAAA,EAAA1B,QAAA,CAkCM;MAAA,OAAkE,CAAlEd,YAAA,CAAkEyF,uBAAA;QAAnDT,EAAE,EAAE7E,MAAA,CAAA6E,EAAE;QAAGE,UAAQ,EAAE/E,MAAA,CAAAoF;;;IAlCxC1D,CAAA;qCAoCI7B,YAAA,CAEmBoE,2BAAA;IAtCvBpD,UAAA,EAoC+Bb,MAAA,CAAAuF,eAAe;IApC9C,uBAAAxE,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoC+BhB,MAAA,CAAAuF,eAAe,GAAAvE,MAAA;IAAA;IAAEmD,IAAI,EAAC;;IApCrD9B,OAAA,EAAA1B,QAAA,CAqCM;MAAA,OAA4E,CAA5Ed,YAAA,CAA4E2F,4BAAA;QAAxDX,EAAE,EAAE7E,MAAA,CAAA6E,EAAE;QAAGE,UAAQ,EAAE/E,MAAA,CAAAoF;;;IArC7C1D,CAAA;qCAuCI7B,YAAA,CAEmBoE,2BAAA;IAzCvBpD,UAAA,EAuC+Bb,MAAA,CAAAyF,SAAS;IAvCxC,uBAAA1E,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAuC+BhB,MAAA,CAAAyF,SAAS,GAAAzE,MAAA;IAAA;IAAEmD,IAAI,EAAC;;IAvC/C9B,OAAA,EAAA1B,QAAA,CAwCM;MAAA,OAAuF,CAAvFd,YAAA,CAAuF6F,sBAAA;QAAzEb,EAAE,EAAE7E,MAAA,CAAA6E,EAAE;QAAEc,QAAQ,EAAC,aAAa;QAAEZ,UAAQ,EAAE/E,MAAA,CAAAoF;;;IAxC9D1D,CAAA;qCA0CI7B,YAAA,CAEmBoE,2BAAA;IA5CvBpD,UAAA,EA0C+Bb,MAAA,CAAA4F,cAAc;IA1C7C,uBAAA7E,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OA0C+BhB,MAAA,CAAA4F,cAAc,GAAA5E,MAAA;IAAA;IAAEmD,IAAI,EAAC;;IA1CpD9B,OAAA,EAAA1B,QAAA,CA2CM;MAAA,OAA0E,CAA1Ed,YAAA,CAA0EgG,2BAAA;QAAvDhB,EAAE,EAAE7E,MAAA,CAAA6E,EAAE;QAAGE,UAAQ,EAAE/E,MAAA,CAAAoF;;;IA3C5C1D,CAAA;qCA6CI7B,YAAA,CAGmBoE,2BAAA;IAhDvBpD,UAAA,EA6C+Bb,MAAA,CAAA8F,SAAS;IA7CxC,uBAAA/E,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OA6C+BhB,MAAA,CAAA8F,SAAS,GAAA9E,MAAA;IAAA;IAAGmD,IAAI,EAAEnE,MAAA,CAAA+F;;IA7CjD1D,OAAA,EAAA1B,QAAA,CA8CM;MAAA,OACoB,CADpBd,YAAA,CACoBmG,4BAAA;QADAnB,EAAE,EAAE7E,MAAA,CAAA6E,EAAE;QAAGpD,IAAI,EAAEzB,MAAA,CAAAiG,SAAS;QAAGC,QAAQ,EAAElG,MAAA,CAAAkG,QAAQ;QAAGnB,UAAQ,EAAE/E,MAAA,CAAAoF;;;IA9CpF1D,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}