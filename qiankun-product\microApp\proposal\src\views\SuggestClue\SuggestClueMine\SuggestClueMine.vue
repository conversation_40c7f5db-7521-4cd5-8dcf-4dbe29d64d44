<template>
  <div class="SuggestClueMine">
    <xyl-search-button @queryClick="handleQuery"
                       @resetClick="handleReset"
                       @handleButton="handleButton"
                       :buttonList="buttonList">
      <template #search>
        <el-input v-model="keyword"
                  placeholder="请输入关键词"
                  @keyup.enter="handleQuery"
                  clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef"
                row-key="id"
                :data="tableData"
                @select="handleTableSelect"
                @select-all="handleTableSelect">
        <el-table-column type="selection"
                         reserve-selection
                         width="60"
                         fixed />
        <el-table-column label="标题"
                         min-width="280"
                         show-overflow-tooltip>
          <template #default="scope">
            <el-link @click="handleDetail(scope.row)"
                     type="primary">{{ scope.row.title }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="线索类别"
                         min-width="120"
                         prop="proposalClueType.label" />
        <el-table-column label="是否选登"
                         min-width="120">
          <template #default="scope">
            <el-icon
                     :class="[!scope.row.ifPublish ? 'globalTableClock' : scope.row.ifPublish === 1 ? 'globalTableCheck' : 'globalTableClose']">
              <CircleCheck v-if="scope.row.ifPublish === 1" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column label="提供者"
                         min-width="120"
                         prop="createByName" />
        <el-table-column label="提交时间"
                         width="190">
          <template #default="scope">{{ format(scope.row.createDate) }}</template>
        </el-table-column>
        <el-table-column label="采纳次数"
                         width="120"
                         prop="useTimes" />
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo"
                     v-model:page-size="pageSize"
                     :page-sizes="pageSizes"
                     layout="total, sizes, prev, pager, next, jumper"
                     @size-change="handleQuery"
                     @current-change="handleQuery"
                     :total="totals"
                     background />
    </div>
    <xyl-popup-window v-model="detailsShow"
                      name="提案线索详情">
      <SuggestClueDetails :id="id"></SuggestClueDetails>
    </xyl-popup-window>
    <xyl-popup-window v-model="submitShow"
                      :name="id ? '新建线索' : '编辑线索'">
      <SuggestClueSubmit :id="id"
                         @callback="callback"></SuggestClueSubmit>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestClueMine' }
</script>
<script setup>
import { ref, onActivated } from 'vue'
import { format } from 'common/js/time.js'
import { GlobalTable } from 'common/js/GlobalTable.js'
import SuggestClueDetails from './components/SuggestClueDetails'
import SuggestClueSubmit from './components/SuggestClueSubmit'
import { clueExportWord } from '@/assets/js/suggestExportWord'
const detailsShow = ref(false)
const id = ref('')
const submitShow = ref(false)

const {
  keyword,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableData,
  handleQuery,
  handleTableSelect,
  handleGetParams,
} = GlobalTable({ tableApi: 'proposalClueList', tableDataObj: { selectMy: true } })
// ifPublish: '1'
onActivated(() => {
  handleQuery()
})

const buttonList = [
  { id: 'new', name: '新增', type: 'primary', has: 'new' },
  { id: 'exportWord', name: '导出Word', type: 'primary', has: 'export' },
]

const handleDetail = (item) => {
  id.value = item.id
  console.log(id.value, 'id.value')
  detailsShow.value = true
}

const handleButton = (isType) => {
  switch (isType) {
    case 'new':
      handleNew()
      break
    case 'exportWord':
      clueExportWord(handleGetParams())
      break
    default:
      break
  }
}

const handleReset = () => {
  keyword.value = ''
  handleQuery()
}

const handleNew = () => {
  id.value = ""
  submitShow.value = true
}

const callback = () => {
  detailsShow.value = false
  submitShow.value = false
  handleQuery()
}

</script>
<style lang="scss">
.SuggestClueMine {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));
  }
}
</style>
