{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport SuggestClueDetails from './components/SuggestClueDetails';\nimport SuggestClueSubmit from './components/SuggestClueSubmit';\nimport { clueExportWord } from '@/assets/js/suggestExportWord';\nvar __default__ = {\n  name: 'SuggestClueMine'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var detailsShow = ref(false);\n    var id = ref('');\n    var submitShow = ref(false);\n    var _GlobalTable = GlobalTable({\n        tableApi: 'proposalClueList',\n        tableDataObj: {\n          selectMy: true\n        }\n      }),\n      keyword = _GlobalTable.keyword,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      handleGetParams = _GlobalTable.handleGetParams;\n    // ifPublish: '1'\n    onActivated(function () {\n      handleQuery();\n    });\n    var buttonList = [{\n      id: 'new',\n      name: '新增',\n      type: 'primary',\n      has: 'new'\n    }, {\n      id: 'exportWord',\n      name: '导出Word',\n      type: 'primary',\n      has: 'export'\n    }];\n    var handleDetail = function handleDetail(item) {\n      id.value = item.id;\n      console.log(id.value, 'id.value');\n      detailsShow.value = true;\n    };\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'new':\n          handleNew();\n          break;\n        case 'exportWord':\n          clueExportWord(handleGetParams());\n          break;\n        default:\n          break;\n      }\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleNew = function handleNew() {\n      id.value = \"\";\n      submitShow.value = true;\n    };\n    var callback = function callback() {\n      detailsShow.value = false;\n      submitShow.value = false;\n      handleQuery();\n    };\n    var __returned__ = {\n      detailsShow,\n      id,\n      submitShow,\n      keyword,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      handleTableSelect,\n      handleGetParams,\n      buttonList,\n      handleDetail,\n      handleButton,\n      handleReset,\n      handleNew,\n      callback,\n      ref,\n      onActivated,\n      get format() {\n        return format;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get SuggestClueDetails() {\n        return SuggestClueDetails;\n      },\n      get SuggestClueSubmit() {\n        return SuggestClueSubmit;\n      },\n      get clueExportWord() {\n        return clueExportWord;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "format", "GlobalTable", "SuggestClueDetails", "SuggestClueSubmit", "clueExportWord", "__default__", "name", "detailsShow", "id", "submitShow", "_GlobalTable", "tableApi", "tableDataObj", "selectMy", "keyword", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "handleTableSelect", "handleGetParams", "buttonList", "type", "has", "handleDetail", "item", "value", "console", "log", "handleButton", "isType", "handleNew", "handleReset", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestClue/SuggestClueMine/SuggestClueMine.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestClueMine\">\r\n    <xyl-search-button @queryClick=\"handleQuery\"\r\n                       @resetClick=\"handleReset\"\r\n                       @handleButton=\"handleButton\"\r\n                       :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\"\r\n                  placeholder=\"请输入关键词\"\r\n                  @keyup.enter=\"handleQuery\"\r\n                  clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\"\r\n                row-key=\"id\"\r\n                :data=\"tableData\"\r\n                @select=\"handleTableSelect\"\r\n                @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\"\r\n                         reserve-selection\r\n                         width=\"60\"\r\n                         fixed />\r\n        <el-table-column label=\"标题\"\r\n                         min-width=\"280\"\r\n                         show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            <el-link @click=\"handleDetail(scope.row)\"\r\n                     type=\"primary\">{{ scope.row.title }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"线索类别\"\r\n                         min-width=\"120\"\r\n                         prop=\"proposalClueType.label\" />\r\n        <el-table-column label=\"是否选登\"\r\n                         min-width=\"120\">\r\n          <template #default=\"scope\">\r\n            <el-icon\r\n                     :class=\"[!scope.row.ifPublish ? 'globalTableClock' : scope.row.ifPublish === 1 ? 'globalTableCheck' : 'globalTableClose']\">\r\n              <CircleCheck v-if=\"scope.row.ifPublish === 1\" />\r\n            </el-icon>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"提供者\"\r\n                         min-width=\"120\"\r\n                         prop=\"createByName\" />\r\n        <el-table-column label=\"提交时间\"\r\n                         width=\"190\">\r\n          <template #default=\"scope\">{{ format(scope.row.createDate) }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"采纳次数\"\r\n                         width=\"120\"\r\n                         prop=\"useTimes\" />\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\"\r\n                     v-model:page-size=\"pageSize\"\r\n                     :page-sizes=\"pageSizes\"\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     @size-change=\"handleQuery\"\r\n                     @current-change=\"handleQuery\"\r\n                     :total=\"totals\"\r\n                     background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"detailsShow\"\r\n                      name=\"提案线索详情\">\r\n      <SuggestClueDetails :id=\"id\"></SuggestClueDetails>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"submitShow\"\r\n                      :name=\"id ? '新建线索' : '编辑线索'\">\r\n      <SuggestClueSubmit :id=\"id\"\r\n                         @callback=\"callback\"></SuggestClueSubmit>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestClueMine' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport SuggestClueDetails from './components/SuggestClueDetails'\r\nimport SuggestClueSubmit from './components/SuggestClueSubmit'\r\nimport { clueExportWord } from '@/assets/js/suggestExportWord'\r\nconst detailsShow = ref(false)\r\nconst id = ref('')\r\nconst submitShow = ref(false)\r\n\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleGetParams,\r\n} = GlobalTable({ tableApi: 'proposalClueList', tableDataObj: { selectMy: true } })\r\n// ifPublish: '1'\r\nonActivated(() => {\r\n  handleQuery()\r\n})\r\n\r\nconst buttonList = [\r\n  { id: 'new', name: '新增', type: 'primary', has: 'new' },\r\n  { id: 'exportWord', name: '导出Word', type: 'primary', has: 'export' },\r\n]\r\n\r\nconst handleDetail = (item) => {\r\n  id.value = item.id\r\n  console.log(id.value, 'id.value')\r\n  detailsShow.value = true\r\n}\r\n\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'exportWord':\r\n      clueExportWord(handleGetParams())\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\n\r\nconst handleNew = () => {\r\n  id.value = \"\"\r\n  submitShow.value = true\r\n}\r\n\r\nconst callback = () => {\r\n  detailsShow.value = false\r\n  submitShow.value = false\r\n  handleQuery()\r\n}\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestClueMine {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAgFA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,SAASC,cAAc,QAAQ,+BAA+B;AAR9D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAkB,CAAC;;;;;IAS1C,IAAMC,WAAW,GAAGT,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAMU,EAAE,GAAGV,GAAG,CAAC,EAAE,CAAC;IAClB,IAAMW,UAAU,GAAGX,GAAG,CAAC,KAAK,CAAC;IAE7B,IAAAY,YAAA,GAWIT,WAAW,CAAC;QAAEU,QAAQ,EAAE,kBAAkB;QAAEC,YAAY,EAAE;UAAEC,QAAQ,EAAE;QAAK;MAAE,CAAC,CAAC;MAVjFC,OAAO,GAAAJ,YAAA,CAAPI,OAAO;MACPC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MACRC,MAAM,GAAAN,YAAA,CAANM,MAAM;MACNC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,QAAQ,GAAAR,YAAA,CAARQ,QAAQ;MACRC,SAAS,GAAAT,YAAA,CAATS,SAAS;MACTC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,WAAW,GAAAX,YAAA,CAAXW,WAAW;MACXC,iBAAiB,GAAAZ,YAAA,CAAjBY,iBAAiB;MACjBC,eAAe,GAAAb,YAAA,CAAfa,eAAe;IAEjB;IACAxB,WAAW,CAAC,YAAM;MAChBsB,WAAW,CAAC,CAAC;IACf,CAAC,CAAC;IAEF,IAAMG,UAAU,GAAG,CACjB;MAAEhB,EAAE,EAAE,KAAK;MAAEF,IAAI,EAAE,IAAI;MAAEmB,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAM,CAAC,EACtD;MAAElB,EAAE,EAAE,YAAY;MAAEF,IAAI,EAAE,QAAQ;MAAEmB,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAS,CAAC,CACrE;IAED,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,IAAI,EAAK;MAC7BpB,EAAE,CAACqB,KAAK,GAAGD,IAAI,CAACpB,EAAE;MAClBsB,OAAO,CAACC,GAAG,CAACvB,EAAE,CAACqB,KAAK,EAAE,UAAU,CAAC;MACjCtB,WAAW,CAACsB,KAAK,GAAG,IAAI;IAC1B,CAAC;IAED,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,KAAK;UACRC,SAAS,CAAC,CAAC;UACX;QACF,KAAK,YAAY;UACf9B,cAAc,CAACmB,eAAe,CAAC,CAAC,CAAC;UACjC;QACF;UACE;MACJ;IACF,CAAC;IAED,IAAMY,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBrB,OAAO,CAACe,KAAK,GAAG,EAAE;MAClBR,WAAW,CAAC,CAAC;IACf,CAAC;IAED,IAAMa,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB1B,EAAE,CAACqB,KAAK,GAAG,EAAE;MACbpB,UAAU,CAACoB,KAAK,GAAG,IAAI;IACzB,CAAC;IAED,IAAMO,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB7B,WAAW,CAACsB,KAAK,GAAG,KAAK;MACzBpB,UAAU,CAACoB,KAAK,GAAG,KAAK;MACxBR,WAAW,CAAC,CAAC;IACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}