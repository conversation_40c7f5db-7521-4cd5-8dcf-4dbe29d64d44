{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { format } from 'common/js/time.js';\nimport { ref, onActivated } from 'vue';\nimport { saveAs } from 'file-saver';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport SubmitUnitSummaryReport from './component/SubmitUnitSummaryReport';\nimport UnitSummaryReportDetails from './component/UnitSummaryReportDetails';\nvar __default__ = {\n  name: 'UnitSummaryReport'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [{\n      id: 'refresh',\n      name: '刷新',\n      type: 'primary',\n      has: 'refresh'\n    }, {\n      id: 'rush',\n      name: '催办',\n      type: 'primary',\n      has: 'rush'\n    }, {\n      id: 'exportFile',\n      name: '导出附件',\n      type: 'primary',\n      has: 'export_file'\n    }];\n    var tableButtonList = [{\n      id: 'edit',\n      name: '上传',\n      width: 100,\n      has: 'edit'\n    }];\n    var id = ref('');\n    var show = ref(false);\n    var detailsShow = ref(false);\n    var hasUpload = ref('');\n    var termYearId = ref('');\n    var termYearData = ref([]);\n    var _GlobalTable = GlobalTable({\n        tableApi: 'handleOfficeReportList'\n      }),\n      keyword = _GlobalTable.keyword,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery,\n      tableDataArray = _GlobalTable.tableDataArray,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      tableRefReset = _GlobalTable.tableRefReset,\n      tableQuery = _GlobalTable.tableQuery;\n    onActivated(function () {\n      handleQuery();\n      termYearCurrent();\n      termYearSelect();\n    });\n    // 获取当前届次\n    var termYearCurrent = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$termYearCu, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.termYearCurrent({\n                termYearType: 'cppcc_member'\n              });\n            case 2:\n              _yield$api$termYearCu = _context.sent;\n              data = _yield$api$termYearCu.data;\n              if (!termYearId.value) {\n                termYearId.value = data.id;\n              }\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function termYearCurrent() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var termYearSelect = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _yield$api$termYearSe, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.termYearSelect({\n                termYearType: 'cppcc_member'\n              });\n            case 2:\n              _yield$api$termYearSe = _context2.sent;\n              data = _yield$api$termYearSe.data;\n              termYearData.value = data;\n            case 5:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function termYearSelect() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'refresh':\n          handleOfficeReportFlush();\n          break;\n        case 'rush':\n          ElMessageBox.confirm('此操作将发送短信提醒当前选中的办理单位上传总结报告, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(function () {\n            handleOfficeReportPress();\n          }).catch(function () {\n            ElMessage({\n              type: 'info',\n              message: '已取消操作'\n            });\n          });\n          break;\n        case 'exportFile':\n          handleBatch();\n          break;\n        default:\n          break;\n      }\n    };\n    var handleCommand = function handleCommand(row, isType) {\n      switch (isType) {\n        case 'edit':\n          handleEdit(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleDetails = function handleDetails(item) {\n      id.value = item.id;\n      detailsShow.value = true;\n    };\n    var queryChange = function queryChange() {\n      tableQuery.value = {\n        query: {\n          termYearId: termYearId.value || null,\n          hasUpload: hasUpload.value || null\n        }\n      };\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      hasUpload.value = '';\n      termYearId.value = '';\n      termYearCurrent();\n      tableQuery.value = {\n        query: {\n          termYearId: termYearId.value || null,\n          hasUpload: hasUpload.value || null\n        }\n      };\n      handleQuery();\n    };\n    var handleEdit = function handleEdit(item) {\n      id.value = item.id;\n      show.value = true;\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      show.value = false;\n    };\n    var handleBatch = function handleBatch() {\n      if (tableDataArray.value.length) {\n        ElMessageBox.confirm('此操作将当前选中数据的附件导出zip, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(function () {\n          handleOfficeReportZip();\n        }).catch(function () {\n          ElMessage({\n            type: 'info',\n            message: '已取消导出'\n          });\n        });\n      } else {\n        ElMessageBox.confirm('当前没有选择数据，是否根据列表筛选条件导出所有数据?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(function () {\n          handleOfficeReportZip();\n        }).catch(function () {\n          ElMessage({\n            type: 'info',\n            message: '已取消导出'\n          });\n        });\n      }\n    };\n    var handleOfficeReportFlush = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _yield$api$handleOffi, code;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.handleOfficeReportFlush();\n            case 2:\n              _yield$api$handleOffi = _context3.sent;\n              code = _yield$api$handleOffi.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '刷新成功'\n                });\n                tableRefReset();\n                handleQuery();\n              }\n            case 5:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function handleOfficeReportFlush() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var handleOfficeReportPress = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _yield$api$handleOffi2, code;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.handleOfficeReportPress({\n                ids: tableDataArray.value.map(function (v) {\n                  return v.id;\n                })\n              });\n            case 2:\n              _yield$api$handleOffi2 = _context4.sent;\n              code = _yield$api$handleOffi2.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '催办成功'\n                });\n                tableRefReset();\n                handleQuery();\n              }\n            case 5:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function handleOfficeReportPress() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handleOfficeReportZip = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.next = 2;\n              return api.handleOfficeReportZip({\n                ids: tableDataArray.value.map(function (v) {\n                  return v.id;\n                }),\n                keyword: keyword.value,\n                query: {\n                  termYearId: termYearId.value || null,\n                  hasUpload: hasUpload.value || null\n                }\n              });\n            case 2:\n              res = _context5.sent;\n              saveAs(res, '总结报告附件.zip');\n              tableRefReset();\n              handleQuery();\n            case 6:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function handleOfficeReportZip() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      buttonList,\n      tableButtonList,\n      id,\n      show,\n      detailsShow,\n      hasUpload,\n      termYearId,\n      termYearData,\n      keyword,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      tableDataArray,\n      handleTableSelect,\n      tableRefReset,\n      tableQuery,\n      termYearCurrent,\n      termYearSelect,\n      handleButton,\n      handleCommand,\n      handleDetails,\n      queryChange,\n      handleReset,\n      handleEdit,\n      callback,\n      handleBatch,\n      handleOfficeReportFlush,\n      handleOfficeReportPress,\n      handleOfficeReportZip,\n      get api() {\n        return api;\n      },\n      get format() {\n        return format;\n      },\n      ref,\n      onActivated,\n      get saveAs() {\n        return saveAs;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get SubmitUnitSummaryReport() {\n        return SubmitUnitSummaryReport;\n      },\n      get UnitSummaryReportDetails() {\n        return UnitSummaryReportDetails;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "format", "ref", "onActivated", "saveAs", "GlobalTable", "ElMessage", "ElMessageBox", "SubmitUnitSummaryReport", "UnitSummaryReportDetails", "__default__", "buttonList", "id", "has", "tableButtonList", "width", "show", "detailsShow", "hasUpload", "termYearId", "termYearData", "_GlobalTable", "tableApi", "keyword", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "tableDataArray", "handleTableSelect", "tableRefReset", "tableQuery", "termYearCurrent", "termYearSelect", "_ref2", "_callee", "_yield$api$termYearCu", "data", "_callee$", "_context", "termYearType", "_ref3", "_callee2", "_yield$api$termYearSe", "_callee2$", "_context2", "handleButton", "isType", "handleOfficeReportFlush", "confirm", "confirmButtonText", "cancelButtonText", "handleOfficeReportPress", "message", "handleBatch", "handleCommand", "row", "handleEdit", "handleDetails", "item", "query<PERSON>hange", "query", "handleReset", "callback", "handleOfficeReportZip", "_ref4", "_callee3", "_yield$api$handleOffi", "code", "_callee3$", "_context3", "_ref5", "_callee4", "_yield$api$handleOffi2", "_callee4$", "_context4", "ids", "map", "_ref6", "_callee5", "res", "_callee5$", "_context5"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/UnitSummaryReport/UnitSummaryReport.vue"], "sourcesContent": ["<template>\r\n  <div class=\"UnitSummaryReport\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\" searchPopover>\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n      <template #searchPopover>\r\n        <el-select v-model=\"termYearId\" placeholder=\"请选择届次\" clearable>\r\n          <el-option v-for=\"item in termYearData\" :key=\"item.key\" :label=\"item.name\" :value=\"item.key\" />\r\n        </el-select>\r\n        <el-select v-model=\"hasUpload\" @change=\"queryChange\" placeholder=\"请选择是否上传\" clearable>\r\n          <el-option value=\"1\" label=\"已上传\" />\r\n          <el-option value=\"0\" label=\"未上传\" />\r\n        </el-select>\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <el-table-column label=\"办理单位\" min-width=\"220\" prop=\"handleOfficeName\">\r\n          <template #default=\"scope\">\r\n            <el-link type=\"primary\" @click=\"handleDetails(scope.row)\">{{ scope.row.handleOfficeName }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"届次\" min-width=\"120\" prop=\"termYearName\" />\r\n        <el-table-column label=\"是否上传\" width=\"120\" class-name=\"globalTableIcon\">\r\n          <template #default=\"scope\">\r\n            <el-icon :class=\"[scope.row.hasUpload ? 'globalTableCheck' : 'globalTableClose']\">\r\n              <CircleCheck v-if=\"scope.row.hasUpload\" />\r\n              <CircleClose v-if=\"!scope.row.hasUpload\" />\r\n            </el-icon>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"经办人\" min-width=\"160\" prop=\"uploadUserName\" />\r\n        <el-table-column label=\"联系电话\" min-width=\"160\" prop=\"contactMobile\" />\r\n        <el-table-column label=\"创建时间\" width=\"180\">\r\n          <template #default=\"scope\">{{ format(scope.row.createDate) }}</template>\r\n        </el-table-column>\r\n        <xyl-global-table-button :data=\"tableButtonList\" @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"上传\">\r\n      <SubmitUnitSummaryReport :id=\"id\" @callback=\"callback\"></SubmitUnitSummaryReport>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"detailsShow\" name=\"详情\">\r\n      <UnitSummaryReportDetails :id=\"id\"></UnitSummaryReportDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'UnitSummaryReport' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { format } from 'common/js/time.js'\r\nimport { ref, onActivated } from 'vue'\r\nimport { saveAs } from 'file-saver'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport SubmitUnitSummaryReport from './component/SubmitUnitSummaryReport'\r\nimport UnitSummaryReportDetails from './component/UnitSummaryReportDetails'\r\nconst buttonList = [\r\n  { id: 'refresh', name: '刷新', type: 'primary', has: 'refresh' },\r\n  { id: 'rush', name: '催办', type: 'primary', has: 'rush' },\r\n  { id: 'exportFile', name: '导出附件', type: 'primary', has: 'export_file' }\r\n]\r\nconst tableButtonList = [{ id: 'edit', name: '上传', width: 100, has: 'edit' }]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst detailsShow = ref(false)\r\nconst hasUpload = ref('')\r\nconst termYearId = ref('')\r\nconst termYearData = ref([])\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  tableDataArray,\r\n  handleTableSelect,\r\n  tableRefReset,\r\n  tableQuery\r\n} = GlobalTable({ tableApi: 'handleOfficeReportList' })\r\n\r\nonActivated(() => {\r\n  handleQuery()\r\n  termYearCurrent()\r\n  termYearSelect()\r\n})\r\n// 获取当前届次\r\nconst termYearCurrent = async () => {\r\n  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })\r\n  if (!termYearId.value) { termYearId.value = data.id }\r\n}\r\nconst termYearSelect = async () => {\r\n  const { data } = await api.termYearSelect({ termYearType: 'cppcc_member' })\r\n  termYearData.value = data\r\n}\r\n\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'refresh':\r\n      handleOfficeReportFlush()\r\n      break\r\n    case 'rush':\r\n      ElMessageBox.confirm('此操作将发送短信提醒当前选中的办理单位上传总结报告, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        handleOfficeReportPress()\r\n      }).catch(() => { ElMessage({ type: 'info', message: '已取消操作' }) })\r\n      break\r\n    case 'exportFile':\r\n      handleBatch()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleDetails = (item) => {\r\n  id.value = item.id\r\n  detailsShow.value = true\r\n}\r\nconst queryChange = () => {\r\n  tableQuery.value = { query: { termYearId: termYearId.value || null, hasUpload: hasUpload.value || null } }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  hasUpload.value = ''\r\n  termYearId.value = ''\r\n  termYearCurrent()\r\n  tableQuery.value = { query: { termYearId: termYearId.value || null, hasUpload: hasUpload.value || null } }\r\n  handleQuery()\r\n}\r\nconst handleEdit = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n}\r\nconst handleBatch = () => {\r\n  if (tableDataArray.value.length) {\r\n    ElMessageBox.confirm('此操作将当前选中数据的附件导出zip, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(() => { handleOfficeReportZip() }).catch(() => { ElMessage({ type: 'info', message: '已取消导出' }) })\r\n  } else {\r\n    ElMessageBox.confirm('当前没有选择数据，是否根据列表筛选条件导出所有数据?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(() => { handleOfficeReportZip() }).catch(() => { ElMessage({ type: 'info', message: '已取消导出' }) })\r\n  }\r\n}\r\nconst handleOfficeReportFlush = async () => {\r\n  const { code } = await api.handleOfficeReportFlush()\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '刷新成功' })\r\n    tableRefReset()\r\n    handleQuery()\r\n  }\r\n}\r\nconst handleOfficeReportPress = async () => {\r\n  const { code } = await api.handleOfficeReportPress({ ids: tableDataArray.value.map(v => v.id) })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '催办成功' })\r\n    tableRefReset()\r\n    handleQuery()\r\n  }\r\n}\r\nconst handleOfficeReportZip = async () => {\r\n  const res = await api.handleOfficeReportZip({\r\n    ids: tableDataArray.value.map(v => v.id),\r\n    keyword: keyword.value, query: { termYearId: termYearId.value || null, hasUpload: hasUpload.value || null }\r\n  })\r\n  saveAs(res, '总结报告附件.zip')\r\n  tableRefReset()\r\n  handleQuery()\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.UnitSummaryReport {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA6DA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,OAAOC,uBAAuB,MAAM,qCAAqC;AACzE,OAAOC,wBAAwB,MAAM,sCAAsC;AAV3E,IAAAC,WAAA,GAAe;EAAErC,IAAI,EAAE;AAAoB,CAAC;;;;;IAW5C,IAAMsC,UAAU,GAAG,CACjB;MAAEC,EAAE,EAAE,SAAS;MAAEvC,IAAI,EAAE,IAAI;MAAEtD,IAAI,EAAE,SAAS;MAAE8F,GAAG,EAAE;IAAU,CAAC,EAC9D;MAAED,EAAE,EAAE,MAAM;MAAEvC,IAAI,EAAE,IAAI;MAAEtD,IAAI,EAAE,SAAS;MAAE8F,GAAG,EAAE;IAAO,CAAC,EACxD;MAAED,EAAE,EAAE,YAAY;MAAEvC,IAAI,EAAE,MAAM;MAAEtD,IAAI,EAAE,SAAS;MAAE8F,GAAG,EAAE;IAAc,CAAC,CACxE;IACD,IAAMC,eAAe,GAAG,CAAC;MAAEF,EAAE,EAAE,MAAM;MAAEvC,IAAI,EAAE,IAAI;MAAE0C,KAAK,EAAE,GAAG;MAAEF,GAAG,EAAE;IAAO,CAAC,CAAC;IAC7E,IAAMD,EAAE,GAAGV,GAAG,CAAC,EAAE,CAAC;IAClB,IAAMc,IAAI,GAAGd,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMe,WAAW,GAAGf,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAMgB,SAAS,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMiB,UAAU,GAAGjB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMkB,YAAY,GAAGlB,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAAmB,YAAA,GAaIhB,WAAW,CAAC;QAAEiB,QAAQ,EAAE;MAAyB,CAAC,CAAC;MAZrDC,OAAO,GAAAF,YAAA,CAAPE,OAAO;MACPC,QAAQ,GAAAH,YAAA,CAARG,QAAQ;MACRC,MAAM,GAAAJ,YAAA,CAANI,MAAM;MACNC,MAAM,GAAAL,YAAA,CAANK,MAAM;MACNC,QAAQ,GAAAN,YAAA,CAARM,QAAQ;MACRC,SAAS,GAAAP,YAAA,CAATO,SAAS;MACTC,SAAS,GAAAR,YAAA,CAATQ,SAAS;MACTC,WAAW,GAAAT,YAAA,CAAXS,WAAW;MACXC,cAAc,GAAAV,YAAA,CAAdU,cAAc;MACdC,iBAAiB,GAAAX,YAAA,CAAjBW,iBAAiB;MACjBC,aAAa,GAAAZ,YAAA,CAAbY,aAAa;MACbC,UAAU,GAAAb,YAAA,CAAVa,UAAU;IAGZ/B,WAAW,CAAC,YAAM;MAChB2B,WAAW,CAAC,CAAC;MACbK,eAAe,CAAC,CAAC;MACjBC,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IACF;IACA,IAAMD,eAAe;MAAA,IAAAE,KAAA,GAAA1C,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgE,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAAtJ,mBAAA,GAAAuB,IAAA,UAAAgI,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA3D,IAAA,GAAA2D,QAAA,CAAAtF,IAAA;YAAA;cAAAsF,QAAA,CAAAtF,IAAA;cAAA,OACC4C,GAAG,CAACmC,eAAe,CAAC;gBAAEQ,YAAY,EAAE;cAAe,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAG,QAAA,CAAA7F,IAAA;cAApE2F,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZ,IAAI,CAACrB,UAAU,CAACvH,KAAK,EAAE;gBAAEuH,UAAU,CAACvH,KAAK,GAAG4I,IAAI,CAAC5B,EAAE;cAAC;YAAC;YAAA;cAAA,OAAA8B,QAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAoD,OAAA;MAAA,CACtD;MAAA,gBAHKH,eAAeA,CAAA;QAAA,OAAAE,KAAA,CAAAxC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGpB;IACD,IAAMwC,cAAc;MAAA,IAAAQ,KAAA,GAAAjD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAuE,SAAA;QAAA,IAAAC,qBAAA,EAAAN,IAAA;QAAA,OAAAtJ,mBAAA,GAAAuB,IAAA,UAAAsI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjE,IAAA,GAAAiE,SAAA,CAAA5F,IAAA;YAAA;cAAA4F,SAAA,CAAA5F,IAAA;cAAA,OACE4C,GAAG,CAACoC,cAAc,CAAC;gBAAEO,YAAY,EAAE;cAAe,CAAC,CAAC;YAAA;cAAAG,qBAAA,GAAAE,SAAA,CAAAnG,IAAA;cAAnE2F,IAAI,GAAAM,qBAAA,CAAJN,IAAI;cACZpB,YAAY,CAACxH,KAAK,GAAG4I,IAAI;YAAA;YAAA;cAAA,OAAAQ,SAAA,CAAA9D,IAAA;UAAA;QAAA,GAAA2D,QAAA;MAAA,CAC1B;MAAA,gBAHKT,cAAcA,CAAA;QAAA,OAAAQ,KAAA,CAAA/C,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGnB;IAED,IAAMqD,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZC,uBAAuB,CAAC,CAAC;UACzB;QACF,KAAK,MAAM;UACT5C,YAAY,CAAC6C,OAAO,CAAC,kCAAkC,EAAE,IAAI,EAAE;YAC7DC,iBAAiB,EAAE,IAAI;YACvBC,gBAAgB,EAAE,IAAI;YACtBvI,IAAI,EAAE;UACR,CAAC,CAAC,CAACuB,IAAI,CAAC,YAAM;YACZiH,uBAAuB,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAChE,KAAK,CAAC,YAAM;YAAEe,SAAS,CAAC;cAAEvF,IAAI,EAAE,MAAM;cAAEyI,OAAO,EAAE;YAAQ,CAAC,CAAC;UAAC,CAAC,CAAC;UACjE;QACF,KAAK,YAAY;UACfC,WAAW,CAAC,CAAC;UACb;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAET,MAAM,EAAK;MACrC,QAAQA,MAAM;QACZ,KAAK,MAAM;UACTU,UAAU,CAACD,GAAG,CAAC;UACf;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9BlD,EAAE,CAAChH,KAAK,GAAGkK,IAAI,CAAClD,EAAE;MAClBK,WAAW,CAACrH,KAAK,GAAG,IAAI;IAC1B,CAAC;IACD,IAAMmK,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB7B,UAAU,CAACtI,KAAK,GAAG;QAAEoK,KAAK,EAAE;UAAE7C,UAAU,EAAEA,UAAU,CAACvH,KAAK,IAAI,IAAI;UAAEsH,SAAS,EAAEA,SAAS,CAACtH,KAAK,IAAI;QAAK;MAAE,CAAC;IAC5G,CAAC;IACD,IAAMqK,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB1C,OAAO,CAAC3H,KAAK,GAAG,EAAE;MAClBsH,SAAS,CAACtH,KAAK,GAAG,EAAE;MACpBuH,UAAU,CAACvH,KAAK,GAAG,EAAE;MACrBuI,eAAe,CAAC,CAAC;MACjBD,UAAU,CAACtI,KAAK,GAAG;QAAEoK,KAAK,EAAE;UAAE7C,UAAU,EAAEA,UAAU,CAACvH,KAAK,IAAI,IAAI;UAAEsH,SAAS,EAAEA,SAAS,CAACtH,KAAK,IAAI;QAAK;MAAE,CAAC;MAC1GkI,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAM8B,UAAU,GAAG,SAAbA,UAAUA,CAAIE,IAAI,EAAK;MAC3BlD,EAAE,CAAChH,KAAK,GAAGkK,IAAI,CAAClD,EAAE;MAClBI,IAAI,CAACpH,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMsK,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBjC,aAAa,CAAC,CAAC;MACfH,WAAW,CAAC,CAAC;MACbd,IAAI,CAACpH,KAAK,GAAG,KAAK;IACpB,CAAC;IACD,IAAM6J,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAI1B,cAAc,CAACnI,KAAK,CAACqE,MAAM,EAAE;QAC/BsC,YAAY,CAAC6C,OAAO,CAAC,2BAA2B,EAAE,IAAI,EAAE;UACtDC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBvI,IAAI,EAAE;QACR,CAAC,CAAC,CAACuB,IAAI,CAAC,YAAM;UAAE6H,qBAAqB,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC5E,KAAK,CAAC,YAAM;UAAEe,SAAS,CAAC;YAAEvF,IAAI,EAAE,MAAM;YAAEyI,OAAO,EAAE;UAAQ,CAAC,CAAC;QAAC,CAAC,CAAC;MAC3G,CAAC,MAAM;QACLjD,YAAY,CAAC6C,OAAO,CAAC,4BAA4B,EAAE,IAAI,EAAE;UACvDC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBvI,IAAI,EAAE;QACR,CAAC,CAAC,CAACuB,IAAI,CAAC,YAAM;UAAE6H,qBAAqB,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC5E,KAAK,CAAC,YAAM;UAAEe,SAAS,CAAC;YAAEvF,IAAI,EAAE,MAAM;YAAEyI,OAAO,EAAE;UAAQ,CAAC,CAAC;QAAC,CAAC,CAAC;MAC3G;IACF,CAAC;IACD,IAAML,uBAAuB;MAAA,IAAAiB,KAAA,GAAAzE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+F,SAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAArL,mBAAA,GAAAuB,IAAA,UAAA+J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1F,IAAA,GAAA0F,SAAA,CAAArH,IAAA;YAAA;cAAAqH,SAAA,CAAArH,IAAA;cAAA,OACP4C,GAAG,CAACmD,uBAAuB,CAAC,CAAC;YAAA;cAAAmB,qBAAA,GAAAG,SAAA,CAAA5H,IAAA;cAA5C0H,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBjE,SAAS,CAAC;kBAAEvF,IAAI,EAAE,SAAS;kBAAEyI,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/CvB,aAAa,CAAC,CAAC;gBACfH,WAAW,CAAC,CAAC;cACf;YAAC;YAAA;cAAA,OAAA2C,SAAA,CAAAvF,IAAA;UAAA;QAAA,GAAAmF,QAAA;MAAA,CACF;MAAA,gBAPKlB,uBAAuBA,CAAA;QAAA,OAAAiB,KAAA,CAAAvE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAO5B;IACD,IAAM2D,uBAAuB;MAAA,IAAAmB,KAAA,GAAA/E,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqG,SAAA;QAAA,IAAAC,sBAAA,EAAAL,IAAA;QAAA,OAAArL,mBAAA,GAAAuB,IAAA,UAAAoK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/F,IAAA,GAAA+F,SAAA,CAAA1H,IAAA;YAAA;cAAA0H,SAAA,CAAA1H,IAAA;cAAA,OACP4C,GAAG,CAACuD,uBAAuB,CAAC;gBAAEwB,GAAG,EAAEhD,cAAc,CAACnI,KAAK,CAACoL,GAAG,CAAC,UAAApJ,CAAC;kBAAA,OAAIA,CAAC,CAACgF,EAAE;gBAAA;cAAE,CAAC,CAAC;YAAA;cAAAgE,sBAAA,GAAAE,SAAA,CAAAjI,IAAA;cAAxF0H,IAAI,GAAAK,sBAAA,CAAJL,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBjE,SAAS,CAAC;kBAAEvF,IAAI,EAAE,SAAS;kBAAEyI,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/CvB,aAAa,CAAC,CAAC;gBACfH,WAAW,CAAC,CAAC;cACf;YAAC;YAAA;cAAA,OAAAgD,SAAA,CAAA5F,IAAA;UAAA;QAAA,GAAAyF,QAAA;MAAA,CACF;MAAA,gBAPKpB,uBAAuBA,CAAA;QAAA,OAAAmB,KAAA,CAAA7E,KAAA,OAAAD,SAAA;MAAA;IAAA,GAO5B;IACD,IAAMuE,qBAAqB;MAAA,IAAAc,KAAA,GAAAtF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4G,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAjM,mBAAA,GAAAuB,IAAA,UAAA2K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtG,IAAA,GAAAsG,SAAA,CAAAjI,IAAA;YAAA;cAAAiI,SAAA,CAAAjI,IAAA;cAAA,OACV4C,GAAG,CAACmE,qBAAqB,CAAC;gBAC1CY,GAAG,EAAEhD,cAAc,CAACnI,KAAK,CAACoL,GAAG,CAAC,UAAApJ,CAAC;kBAAA,OAAIA,CAAC,CAACgF,EAAE;gBAAA,EAAC;gBACxCW,OAAO,EAAEA,OAAO,CAAC3H,KAAK;gBAAEoK,KAAK,EAAE;kBAAE7C,UAAU,EAAEA,UAAU,CAACvH,KAAK,IAAI,IAAI;kBAAEsH,SAAS,EAAEA,SAAS,CAACtH,KAAK,IAAI;gBAAK;cAC5G,CAAC,CAAC;YAAA;cAHIuL,GAAG,GAAAE,SAAA,CAAAxI,IAAA;cAITuD,MAAM,CAAC+E,GAAG,EAAE,YAAY,CAAC;cACzBlD,aAAa,CAAC,CAAC;cACfH,WAAW,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAuD,SAAA,CAAAnG,IAAA;UAAA;QAAA,GAAAgG,QAAA;MAAA,CACd;MAAA,gBARKf,qBAAqBA,CAAA;QAAA,OAAAc,KAAA,CAAApF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQ1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}