{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted, computed, inject } from 'vue';\nimport { openConfig } from 'common/js/system_var.js';\nvar __default__ = {\n  name: 'suggestPop'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    isVisible: {\n      type: Boolean,\n      default: false\n    },\n    routePth: {\n      type: String,\n      default: ''\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var systemPlatform = computed(function () {\n      var _openConfig$value;\n      return ((_openConfig$value = openConfig.value) === null || _openConfig$value === void 0 ? void 0 : _openConfig$value.systemPlatform) || '';\n    });\n    var props = __props;\n    var openPage = inject('openPage');\n    var getgreetings = function getgreetings() {\n      var hour = new Date().getHours();\n      if (hour < 12) {\n        return '早上好';\n      } else if (hour < 18) {\n        return '下午好';\n      } else {\n        return '晚上好';\n      }\n    };\n    // npc_contact_committee  npc_member  delegation_manager  suggestion_office_user\n\n    var show = ref(false);\n    var delayedShow = ref(false);\n    var closePop = function closePop() {\n      if (delayedShow.value) {\n        delayedShow.value = !delayedShow.value;\n      } else {\n        setTimeout(function () {\n          delayedShow.value = !delayedShow.value;\n        }, 300);\n      }\n      show.value = !show.value;\n    };\n    var user = ref({});\n    var canChooseRoles = ref(systemPlatform.value == 'CPPCC' ? ['proposal_committee', 'suggestion_office_user', 'cppcc_member'] : ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member']);\n    var role = ref('');\n    var rulesoptions = ref([]);\n    var roles = ref([]);\n    var suggestionEnablePreAssign = ref(false); // 是否开启预交办\n    var getLoginHintConfig = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$globalRead, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.globalReadOpenConfig({\n                codes: ['suggestion_enable_pre_assign', 'proposal_enable_pre_assign']\n              });\n            case 2:\n              _yield$api$globalRead = _context.sent;\n              data = _yield$api$globalRead.data;\n              if (data.suggestion_enable_pre_assign && systemPlatform.value != 'CPPCC') {\n                suggestionEnablePreAssign.value = data.suggestion_enable_pre_assign == 'true';\n              }\n              if (data.proposal_enable_pre_assign && systemPlatform.value == 'CPPCC') {\n                suggestionEnablePreAssign.value = data.proposal_enable_pre_assign == 'true';\n              }\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function getLoginHintConfig() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    onMounted(function () {\n      getLoginHintConfig();\n      user.value = JSON.parse(sessionStorage.getItem('user'));\n      roles.value = user.value.specialRoleKeys.filter(function (item) {\n        return canChooseRoles.value.includes(item);\n      });\n      if (roles.value.includes('npc_contact_committee')) rulesoptions.value.push({\n        value: 'npc_contact_committee',\n        label: '联工委',\n        param: 'remind_admin'\n      });\n      if (roles.value.includes('proposal_committee')) rulesoptions.value.push({\n        value: 'proposal_committee',\n        label: '提案委',\n        param: 'remind_admin'\n      });\n      if (roles.value.includes('suggestion_office_user')) rulesoptions.value.push({\n        value: 'suggestion_office_user',\n        label: '办理单位',\n        param: 'remind_office'\n      });\n      if (roles.value.includes('delegation_manager')) rulesoptions.value.push({\n        value: 'delegation_manager',\n        label: '代表团管理员',\n        param: 'remind_delegation'\n      });\n      if (roles.value.includes('npc_member')) rulesoptions.value.push({\n        value: 'npc_member',\n        label: '人大代表',\n        param: 'remind_npc_member'\n      });\n      if (roles.value.includes('cppcc_member')) rulesoptions.value.push({\n        value: 'cppcc_member',\n        label: '政协委员',\n        param: 'remind_member'\n      });\n      role.value = rulesoptions.value[0].value;\n      getCompositeData();\n      setTimeout(function () {\n        show.value = true;\n        setTimeout(function () {\n          delayedShow.value = true;\n        }, 100);\n      }, 300);\n    });\n    var tableData = ref({});\n    var loading = ref(true);\n    var getCompositeData = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var url, res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              url = systemPlatform.value === 'CPPCC' ? '/proposalStatistics/composite' : '/suggestionStatistics/composite';\n              _context2.next = 3;\n              return api.globalJson(url, {\n                countView: rulesoptions.value.filter(function (v) {\n                  return v.value === role.value;\n                })[0].param\n              });\n            case 3:\n              res = _context2.sent;\n              tableData.value = res.data.tableData.length ? res.data.tableData[0] : {};\n              loading.value = false;\n            case 6:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function getCompositeData() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var RefreshRightclick = function RefreshRightclick() {\n      loading.value = true;\n      getCompositeData();\n    };\n    var goSuggestList = function goSuggestList(type, key) {\n      var _tableData$value$key, _tableData$value$key2, _tableData$value$key3;\n      var count = ((_tableData$value$key = tableData.value[key]) === null || _tableData$value$key === void 0 ? void 0 : _tableData$value$key.amount) || 0;\n      if (count === 0) {\n        ElMessage({\n          type: 'info',\n          message: `暂无${key === 'draftsList' ? '草稿' : '相关'}数据`\n        });\n        return;\n      }\n      var suggestIds = ((_tableData$value$key2 = tableData.value[key]) === null || _tableData$value$key2 === void 0 ? void 0 : _tableData$value$key2.suggestionIds) || ((_tableData$value$key3 = tableData.value[key]) === null || _tableData$value$key3 === void 0 ? void 0 : _tableData$value$key3.proposalIds) || [];\n      console.log(suggestIds);\n      if (suggestIds.length) {\n        sessionStorage.setItem('suggestIds', JSON.stringify(suggestIds));\n      } else {\n        sessionStorage.removeItem('suggestIds');\n      }\n      var url = systemPlatform.value == 'CPPCC' ? 'proposal' : 'suggest';\n      // if (type) return\n      openPage({\n        key: 'routePath',\n        value: `/${url}/` + type\n      });\n    };\n    var __returned__ = {\n      systemPlatform,\n      props,\n      openPage,\n      getgreetings,\n      show,\n      delayedShow,\n      closePop,\n      user,\n      canChooseRoles,\n      role,\n      rulesoptions,\n      roles,\n      suggestionEnablePreAssign,\n      getLoginHintConfig,\n      tableData,\n      loading,\n      getCompositeData,\n      RefreshRightclick,\n      goSuggestList,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      computed,\n      inject,\n      get openConfig() {\n        return openConfig;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "computed", "inject", "openConfig", "__default__", "systemPlatform", "_openConfig$value", "props", "__props", "openPage", "getgreetings", "hour", "Date", "getHours", "show", "delayedShow", "closePop", "setTimeout", "user", "canChooseRoles", "role", "rulesoptions", "roles", "suggestionEnablePreAssign", "getLoginHintConfig", "_ref2", "_callee", "_yield$api$globalRead", "data", "_callee$", "_context", "globalReadOpenConfig", "codes", "suggestion_enable_pre_assign", "proposal_enable_pre_assign", "JSON", "parse", "sessionStorage", "getItem", "special<PERSON><PERSON><PERSON><PERSON>s", "filter", "item", "includes", "label", "param", "getCompositeData", "tableData", "loading", "_ref3", "_callee2", "url", "res", "_callee2$", "_context2", "globalJson", "<PERSON><PERSON><PERSON><PERSON>", "RefreshRightclick", "goSuggestList", "key", "_tableData$value$key", "_tableData$value$key2", "_tableData$value$key3", "count", "amount", "ElMessage", "message", "suggestIds", "suggestionIds", "proposalIds", "console", "log", "setItem", "stringify", "removeItem"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutView/component/suggestPop.vue"], "sourcesContent": ["<template>\r\n  <div class=\"suggestPop\" :class=\"{ show: isVisible || show }\" v-loading=\"loading\">\r\n    <div class=\"suggestPopHead\" :class=\"{ showHead: show }\">\r\n      <el-icon v-if=\"show\">\r\n        <RefreshRight @click=\"RefreshRightclick\" />\r\n      </el-icon>\r\n      <el-icon v-if=\"show\" @click=\"closePop\">\r\n        <Close />\r\n      </el-icon>\r\n      <el-icon @click=\"closePop\" v-else>\r\n        <More />\r\n      </el-icon>\r\n    </div>\r\n    <div class=\"content\" v-if=\"delayedShow\">\r\n      <div class=\"suggestPopContentHeader\">{{ user.userName }}，{{ getgreetings() }}</div>\r\n      <div class=\"suggestPopContentChooseRole\" v-if=\"roles.length > 1\">\r\n        <div>选择您的身份以查看更多待办</div>\r\n        <el-select v-model=\"role\" size=\"small\" style=\"width: 120px\" @change=\"getCompositeData\">\r\n          <el-option v-for=\"item in rulesoptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n        </el-select>\r\n      </div>\r\n      <div class=\"suggestPopContentBody\">\r\n        <div>{{ tableData.termYear }}</div>\r\n        <template v-if=\"role == 'npc_contact_committee'\">\r\n          <div>\r\n            <span @click=\"goSuggestList('AllSuggest', 'meetList')\">{{ tableData.meetList?.amount || 0 }}</span>\r\n            件大会建议，\r\n            <span @click=\"goSuggestList('AllSuggest', 'usualList')\">{{ tableData.usualList?.amount || 0 }}</span>\r\n            件闭会建议，\r\n            <span\r\n              @click=\"goSuggestList('SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议', 'importantList')\">\r\n              {{ tableData.importantList?.amount || 0 }}\r\n            </span>\r\n            件重点督办建议\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span\r\n              @click=\"goSuggestList('SuggestReview?tableId=id_sgsn_suggestion_prepareVerify&moduleName=待审查建议', 'auditList')\">\r\n              {{ tableData.auditList?.amount || 0 }}\r\n            </span>\r\n            件待审查，\r\n            <template v-if=\"suggestionEnablePreAssign\">\r\n              <span @click=\"\r\n                goSuggestList('SuggestAdvanceAssign?tableId=id_sgsn_suggestion_preAssignSuggestion&moduleName=预交办', 'preAssignList')\r\n                \">\r\n                {{ tableData.preAssignList?.amount || 0 }}\r\n              </span>\r\n              件预交办，\r\n            </template>\r\n            <span @click=\"\r\n              goSuggestList('SuggestAssign?tableId=id_sgsn_suggestion_prepareSubmitHandle&moduleName=人大交办中', 'prepareSubmitHandleList')\r\n              \">\r\n              {{ tableData.prepareSubmitHandleList?.amount || 0 }}\r\n            </span>\r\n            件人大交办中\r\n          </div>\r\n          <div class=\"hasColorBox\">\r\n            <span @click=\"goSuggestList('SuggestTransact', 'handleList')\">{{ tableData.handleList?.amount || 0 }}</span>\r\n            件办理中，其中\r\n            <span class=\"red\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.redAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"yellow\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"green\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.greenAnswerDate?.amount || 0 }}</span>\r\n            件\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span @click=\"goSuggestList('SuggestApplyForAdjust', 'adjustList')\">{{ tableData.adjustList?.amount || 0\r\n              }}</span>\r\n            件调整申请待审核，\r\n            <span @click=\"goSuggestList('SuggestApplyForPostpone', 'delayList')\">{{ tableData.delayList?.amount || 0\r\n              }}</span>\r\n            件延期申请待审核\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestReply', 'answerList')\">{{ tableData.answerList?.amount || 0 }}</span>\r\n            件已答复\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('AllSuggest', 'satisfactionList')\">{{ tableData.satisfactionList?.amount || 0\r\n              }}</span>\r\n            件已答复待代表满意度测评\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestConclude', 'finishList')\">{{ tableData.finishList?.amount || 0 }}</span>\r\n            件已办结\r\n          </div>\r\n        </template>\r\n        <template v-if=\"role === 'proposal_committee'\">\r\n          <div>\r\n            <span @click=\"goSuggestList('AllSuggest', 'meetList')\">{{ tableData.meetList?.amount || 0 }}</span>\r\n            件大会提案，\r\n            <span @click=\"goSuggestList('AllSuggest', 'usualList')\">{{ tableData.usualList?.amount || 0 }}</span>\r\n            件闭会提案，\r\n            <span\r\n              @click=\"goSuggestList('SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案', 'importantList')\">\r\n              {{ tableData.importantList?.amount || 0 }}\r\n            </span>\r\n            件重点督办提案\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span\r\n              @click=\"goSuggestList('SuggestReview?tableId=id_prop_proposal_prepareVerify&moduleName=待审查提案', 'auditList')\">\r\n              {{ tableData.auditList?.amount || 0 }}\r\n            </span>\r\n            件待审查，\r\n            <template v-if=\"suggestionEnablePreAssign\">\r\n              <span @click=\"\r\n                goSuggestList('SuggestAdvanceAssign?tableId=id_prop_proposal_preAssignPropoasl&moduleName=预交办', 'preAssignList')\r\n                \">\r\n                {{ tableData.preAssignList?.amount || 0 }}\r\n              </span>\r\n              件预交办，\r\n            </template>\r\n            <span @click=\"\r\n              goSuggestList('SuggestAssign?tableId=id_prop_proposal_prepareSubmitHandle&moduleName=政协交办中', 'prepareSubmitHandleList')\r\n              \">\r\n              {{ tableData.prepareSubmitHandleList?.amount || 0 }}\r\n            </span>\r\n            件政协交办中\r\n          </div>\r\n          <div class=\"hasColorBox\">\r\n            <span @click=\"goSuggestList('SuggestTransact', 'handleList')\">{{ tableData.handleList?.amount || 0 }}</span>\r\n            件办理中，其中\r\n            <span class=\"red\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.redAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"yellow\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"green\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.greenAnswerDate?.amount || 0 }}</span>\r\n            件\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span @click=\"goSuggestList('SuggestApplyForAdjust', 'adjustList')\">{{ tableData.adjustList?.amount || 0\r\n              }}</span>\r\n            件调整申请待审核，\r\n            <span @click=\"goSuggestList('SuggestApplyForPostpone', 'delayList')\">{{ tableData.delayList?.amount || 0\r\n              }}</span>\r\n            件延期申请待审核\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestReply', 'answerList')\">{{ tableData.answerList?.amount || 0 }}</span>\r\n            件已答复\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestSatisfaction', 'satisfactionList')\">{{\r\n              tableData.satisfactionList?.amount || 0\r\n              }}</span>\r\n            件已答复待委员满意度测评\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestConclude', 'finishList')\">{{ tableData.finishList?.amount || 0 }}</span>\r\n            件已办结\r\n          </div>\r\n        </template>\r\n        <template v-if=\"role == 'suggestion_office_user'\">\r\n          <div>\r\n            共\r\n            <span @click=\"goSuggestList('UnitSuggestTransact', 'handleList')\">{{ tableData.handleList?.amount || 0\r\n              }}</span>\r\n            件办理中，\r\n            <span @click=\"\r\n              goSuggestList(\r\n                systemPlatform == 'CPPCC'\r\n                  ? 'SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案'\r\n                  : 'SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议',\r\n                'importantList'\r\n              )\r\n              \">\r\n              {{ tableData.importantList?.amount || 0 }}\r\n            </span>\r\n            件重点督办{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}\r\n          </div>\r\n          <div class=\"hasColorBox\">\r\n            其中\r\n            <span class=\"red\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.redAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"yellow\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"green\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.greenAnswerDate?.amount || 0 }}</span>\r\n            件\r\n          </div>\r\n          <div>\r\n            <span class=\"nocolorSpan\">{{ tableData.adjustList?.amount || 0 }}</span>\r\n            件调整申请待审核，\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span class=\"nocolorSpan\">{{ tableData.delayList?.amount || 0 }}</span>\r\n            件申请延期待审核，\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('UnitSuggestReply', 'answerList')\">{{ tableData.answerList?.amount || 0\r\n              }}</span>\r\n            件已答复\r\n          </div>\r\n          <!-- <div>\r\n            <span @click=\"goSuggestList('satisfactionList')\">{{ tableData.satisfactionList?.amount || 0 }}</span>\r\n            件已答复待代表满意度测评\r\n          </div> -->\r\n          <div>\r\n            <span @click=\"goSuggestList('UnitSuggestConclude', 'finishList')\">{{ tableData.finishList?.amount || 0\r\n              }}</span>\r\n            件已办结\r\n          </div>\r\n        </template>\r\n        <template v-if=\"role == 'delegation_manager'\">\r\n          <div>\r\n            本代表团\r\n            <span @click=\"goSuggestList('AllSuggest', 'memberList')\">{{ tableData.memberList?.amount || 0 }}</span>\r\n            件代表建议，\r\n            <span @click=\"goSuggestList('AllSuggest', 'teamList')\">{{ tableData.teamList?.amount || 0 }}</span>\r\n            件全团建议\r\n          </div>\r\n          <div>\r\n            <span @click=\"\r\n              goSuggestList(\r\n                'SuggestReview?tableId=id_sgsn_suggestion_base_delegation_verify&moduleName=代表团审查&nextNode=prepareVerify',\r\n                'delegationAuditList'\r\n              )\r\n              \">\r\n              {{ tableData.delegationAuditList?.amount || 0 }}\r\n            </span>\r\n            件待代表团审查建议\r\n          </div>\r\n        </template>\r\n        <template v-if=\"role == 'npc_member' || role == 'cppcc_member'\">\r\n          <div>\r\n            您已提交\r\n            <span @click=\"goSuggestList('MyLedSuggest', 'normalList')\">\r\n              {{ tableData.normalList?.amount || 0 }}\r\n            </span>\r\n            件{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}，\r\n            <span class=\"nocolorSpan\">{{ tableData.importantList?.amount || 0 }}</span>\r\n            件形成重点督办{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('MyJointSuggest', 'needJoinList')\">\r\n              {{ tableData.needJoinList?.amount || 0 }}\r\n            </span>\r\n            件需要确认是否{{ systemPlatform == 'CPPCC' ? '联名' : '附议' }}，\r\n            <span @click=\"goSuggestList('MyLedSuggest', 'backList')\">\r\n              {{ tableData.backList?.amount || 0 }}\r\n            </span>\r\n            件被退回，\r\n            <span @click=\"\r\n              goSuggestList(\r\n                systemPlatform == 'CPPCC'\r\n                  ? 'SuggestDraftBox?nextNode=prepareVerify'\r\n                  : 'SuggestDraftBox?nextNode=prepareVerify',\r\n                'draftsList'\r\n              )\r\n              \">\r\n              {{ tableData.draftsList?.amount || 0 }}\r\n            </span>\r\n            件在草稿箱\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('MyLedSuggest', 'satisfactionList')\">\r\n              {{ tableData.satisfactionList?.amount || 0 }}\r\n            </span>\r\n            件待满意度测评\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'suggestPop'\r\n}\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, computed, inject } from 'vue'\r\nimport { openConfig } from 'common/js/system_var.js'\r\nconst systemPlatform = computed(() => openConfig.value?.systemPlatform || '')\r\nconst props = defineProps({\r\n  isVisible: { type: Boolean, default: false },\r\n  routePth: { type: String, default: '' }\r\n})\r\n\r\nconst openPage = inject('openPage')\r\nconst getgreetings = () => {\r\n  const hour = new Date().getHours()\r\n  if (hour < 12) {\r\n    return '早上好'\r\n  } else if (hour < 18) {\r\n    return '下午好'\r\n  } else {\r\n    return '晚上好'\r\n  }\r\n}\r\n// npc_contact_committee  npc_member  delegation_manager  suggestion_office_user\r\n\r\nconst show = ref(false)\r\nconst delayedShow = ref(false)\r\nconst closePop = () => {\r\n  if (delayedShow.value) {\r\n    delayedShow.value = !delayedShow.value\r\n  } else {\r\n    setTimeout(() => {\r\n      delayedShow.value = !delayedShow.value\r\n    }, 300)\r\n  }\r\n  show.value = !show.value\r\n}\r\n\r\nconst user = ref({})\r\nconst canChooseRoles = ref(\r\n  systemPlatform.value == 'CPPCC'\r\n    ? ['proposal_committee', 'suggestion_office_user', 'cppcc_member']\r\n    : ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member']\r\n)\r\n\r\nconst role = ref('')\r\nconst rulesoptions = ref([])\r\nconst roles = ref([])\r\nconst suggestionEnablePreAssign = ref(false) // 是否开启预交办\r\nconst getLoginHintConfig = async () => {\r\n  const { data } = await api.globalReadOpenConfig({\r\n    codes: ['suggestion_enable_pre_assign', 'proposal_enable_pre_assign']\r\n  })\r\n  if (data.suggestion_enable_pre_assign && systemPlatform.value != 'CPPCC') {\r\n    suggestionEnablePreAssign.value = data.suggestion_enable_pre_assign == 'true'\r\n  }\r\n  if (data.proposal_enable_pre_assign && systemPlatform.value == 'CPPCC') {\r\n    suggestionEnablePreAssign.value = data.proposal_enable_pre_assign == 'true'\r\n  }\r\n}\r\nonMounted(() => {\r\n  getLoginHintConfig()\r\n  user.value = JSON.parse(sessionStorage.getItem('user'))\r\n  roles.value = user.value.specialRoleKeys.filter((item) => canChooseRoles.value.includes(item))\r\n  if (roles.value.includes('npc_contact_committee'))\r\n    rulesoptions.value.push({ value: 'npc_contact_committee', label: '联工委', param: 'remind_admin' })\r\n  if (roles.value.includes('proposal_committee'))\r\n    rulesoptions.value.push({ value: 'proposal_committee', label: '提案委', param: 'remind_admin' })\r\n  if (roles.value.includes('suggestion_office_user'))\r\n    rulesoptions.value.push({ value: 'suggestion_office_user', label: '办理单位', param: 'remind_office' })\r\n  if (roles.value.includes('delegation_manager'))\r\n    rulesoptions.value.push({ value: 'delegation_manager', label: '代表团管理员', param: 'remind_delegation' })\r\n  if (roles.value.includes('npc_member'))\r\n    rulesoptions.value.push({ value: 'npc_member', label: '人大代表', param: 'remind_npc_member' })\r\n  if (roles.value.includes('cppcc_member'))\r\n    rulesoptions.value.push({ value: 'cppcc_member', label: '政协委员', param: 'remind_member' })\r\n  role.value = rulesoptions.value[0].value\r\n  getCompositeData()\r\n  setTimeout(() => {\r\n    show.value = true\r\n    setTimeout(() => {\r\n      delayedShow.value = true\r\n    }, 100)\r\n  }, 300)\r\n})\r\nconst tableData = ref({})\r\nconst loading = ref(true)\r\nconst getCompositeData = async () => {\r\n  const url = systemPlatform.value === 'CPPCC' ? '/proposalStatistics/composite' : '/suggestionStatistics/composite'\r\n  const res = await api.globalJson(url, {\r\n    countView: rulesoptions.value.filter((v) => v.value === role.value)[0].param\r\n  })\r\n  tableData.value = res.data.tableData.length ? res.data.tableData[0] : {}\r\n  loading.value = false\r\n}\r\nconst RefreshRightclick = () => {\r\n  loading.value = true\r\n  getCompositeData()\r\n}\r\nconst goSuggestList = (type, key) => {\r\n  const count = tableData.value[key]?.amount || 0;\r\n  if (count === 0) {\r\n    ElMessage({\r\n      type: 'info',\r\n      message: `暂无${key === 'draftsList' ? '草稿' : '相关'}数据`\r\n    });\r\n    return;\r\n  }\r\n  const suggestIds = tableData.value[key]?.suggestionIds || tableData.value[key]?.proposalIds || []\r\n  console.log(suggestIds)\r\n\r\n  if (suggestIds.length) {\r\n    sessionStorage.setItem('suggestIds', JSON.stringify(suggestIds))\r\n  } else {\r\n    sessionStorage.removeItem('suggestIds')\r\n  }\r\n  const url = systemPlatform.value == 'CPPCC' ? 'proposal' : 'suggest'\r\n  // if (type) return\r\n  openPage({ key: 'routePath', value: `/${url}/` + type })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.suggestPop {\r\n  position: absolute;\r\n  right: 16px;\r\n  bottom: 0;\r\n  width: 50px;\r\n  background: #fff;\r\n  z-index: 999;\r\n  transform: translateY(18);\r\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);\r\n  border-radius: 8px 8px 0 0;\r\n  height: 18px;\r\n\r\n  &.show {\r\n    height: auto;\r\n    width: 500px;\r\n    height: 400px;\r\n    transform: translateY(0);\r\n  }\r\n\r\n  .suggestPopHead {\r\n    height: 18px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    padding: 0 16px;\r\n    background: var(--zy-el-color-primary);\r\n    color: #fff;\r\n    font-size: 20px;\r\n\r\n    .zy-el-icon {\r\n      margin-left: 10px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    &.showHead {\r\n      height: 36px;\r\n    }\r\n  }\r\n\r\n  .content {\r\n    height: calc(100% - 36px);\r\n    padding: 16px;\r\n    overflow-y: auto;\r\n\r\n    .suggestPopContentHeader {\r\n      border-bottom: 1px solid #e5e5e5;\r\n      padding-bottom: 16px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .suggestPopContentChooseRole {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n      font-size: 14px;\r\n      margin-bottom: 20px;\r\n\r\n      .zy-el-select {\r\n        margin-left: 16px;\r\n      }\r\n    }\r\n\r\n    .suggestPopContentBody {\r\n      padding-left: 10px;\r\n      line-height: 26px;\r\n\r\n      span {\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n      }\r\n\r\n      .nocolorSpan {\r\n        color: var(--zy-el-text-color-primary);\r\n      }\r\n\r\n      .hasColorBox {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .red {\r\n          width: 20px;\r\n          height: 20px;\r\n          background: #f56c6c;\r\n          border-radius: 50%;\r\n          display: inline-block;\r\n        }\r\n\r\n        .yellow {\r\n          width: 20px;\r\n          height: 20px;\r\n          background: rgb(246, 185, 47);\r\n          border-radius: 50%;\r\n          display: inline-block;\r\n        }\r\n\r\n        .green {\r\n          background: rgb(51, 203, 116);\r\n          width: 20px;\r\n          height: 20px;\r\n          border-radius: 50%;\r\n          display: inline-block;\r\n        }\r\n      }\r\n\r\n      .mb20 {\r\n        margin-bottom: 14px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA2RA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,KAAK;AACtD,SAASC,UAAU,QAAQ,yBAAyB;AAPpD,IAAAC,WAAA,GAAe;EACbjC,IAAI,EAAE;AACR,CAAC;;;;;;;;;;;;;;;IAMD,IAAMkC,cAAc,GAAGJ,QAAQ,CAAC;MAAA,IAAAK,iBAAA;MAAA,OAAM,EAAAA,iBAAA,GAAAH,UAAU,CAACzG,KAAK,cAAA4G,iBAAA,uBAAhBA,iBAAA,CAAkBD,cAAc,KAAI,EAAE;IAAA,EAAC;IAC7E,IAAME,KAAK,GAAGC,OAGZ;IAEF,IAAMC,QAAQ,GAAGP,MAAM,CAAC,UAAU,CAAC;IACnC,IAAMQ,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAClC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACb,OAAO,KAAK;MACd,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QACpB,OAAO,KAAK;MACd,CAAC,MAAM;QACL,OAAO,KAAK;MACd;IACF,CAAC;IACD;;IAEA,IAAMG,IAAI,GAAGf,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMgB,WAAW,GAAGhB,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAMiB,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IAAID,WAAW,CAACrH,KAAK,EAAE;QACrBqH,WAAW,CAACrH,KAAK,GAAG,CAACqH,WAAW,CAACrH,KAAK;MACxC,CAAC,MAAM;QACLuH,UAAU,CAAC,YAAM;UACfF,WAAW,CAACrH,KAAK,GAAG,CAACqH,WAAW,CAACrH,KAAK;QACxC,CAAC,EAAE,GAAG,CAAC;MACT;MACAoH,IAAI,CAACpH,KAAK,GAAG,CAACoH,IAAI,CAACpH,KAAK;IAC1B,CAAC;IAED,IAAMwH,IAAI,GAAGnB,GAAG,CAAC,CAAC,CAAC,CAAC;IACpB,IAAMoB,cAAc,GAAGpB,GAAG,CACxBM,cAAc,CAAC3G,KAAK,IAAI,OAAO,GAC3B,CAAC,oBAAoB,EAAE,wBAAwB,EAAE,cAAc,CAAC,GAChE,CAAC,uBAAuB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,YAAY,CAC5F,CAAC;IAED,IAAM0H,IAAI,GAAGrB,GAAG,CAAC,EAAE,CAAC;IACpB,IAAMsB,YAAY,GAAGtB,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMuB,KAAK,GAAGvB,GAAG,CAAC,EAAE,CAAC;IACrB,IAAMwB,yBAAyB,GAAGxB,GAAG,CAAC,KAAK,CAAC,EAAC;IAC7C,IAAMyB,kBAAkB;MAAA,IAAAC,KAAA,GAAAhC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsD,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAA5I,mBAAA,GAAAuB,IAAA,UAAAsH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAjD,IAAA,GAAAiD,QAAA,CAAA5E,IAAA;YAAA;cAAA4E,QAAA,CAAA5E,IAAA;cAAA,OACF4C,GAAG,CAACiC,oBAAoB,CAAC;gBAC9CC,KAAK,EAAE,CAAC,8BAA8B,EAAE,4BAA4B;cACtE,CAAC,CAAC;YAAA;cAAAL,qBAAA,GAAAG,QAAA,CAAAnF,IAAA;cAFMiF,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAGZ,IAAIA,IAAI,CAACK,4BAA4B,IAAI5B,cAAc,CAAC3G,KAAK,IAAI,OAAO,EAAE;gBACxE6H,yBAAyB,CAAC7H,KAAK,GAAGkI,IAAI,CAACK,4BAA4B,IAAI,MAAM;cAC/E;cACA,IAAIL,IAAI,CAACM,0BAA0B,IAAI7B,cAAc,CAAC3G,KAAK,IAAI,OAAO,EAAE;gBACtE6H,yBAAyB,CAAC7H,KAAK,GAAGkI,IAAI,CAACM,0BAA0B,IAAI,MAAM;cAC7E;YAAC;YAAA;cAAA,OAAAJ,QAAA,CAAA9C,IAAA;UAAA;QAAA,GAAA0C,OAAA;MAAA,CACF;MAAA,gBAVKF,kBAAkBA,CAAA;QAAA,OAAAC,KAAA,CAAA9B,KAAA,OAAAD,SAAA;MAAA;IAAA,GAUvB;IACDM,SAAS,CAAC,YAAM;MACdwB,kBAAkB,CAAC,CAAC;MACpBN,IAAI,CAACxH,KAAK,GAAGyI,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvDhB,KAAK,CAAC5H,KAAK,GAAGwH,IAAI,CAACxH,KAAK,CAAC6I,eAAe,CAACC,MAAM,CAAC,UAACC,IAAI;QAAA,OAAKtB,cAAc,CAACzH,KAAK,CAACgJ,QAAQ,CAACD,IAAI,CAAC;MAAA,EAAC;MAC9F,IAAInB,KAAK,CAAC5H,KAAK,CAACgJ,QAAQ,CAAC,uBAAuB,CAAC,EAC/CrB,YAAY,CAAC3H,KAAK,CAACgE,IAAI,CAAC;QAAEhE,KAAK,EAAE,uBAAuB;QAAEiJ,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAe,CAAC,CAAC;MAClG,IAAItB,KAAK,CAAC5H,KAAK,CAACgJ,QAAQ,CAAC,oBAAoB,CAAC,EAC5CrB,YAAY,CAAC3H,KAAK,CAACgE,IAAI,CAAC;QAAEhE,KAAK,EAAE,oBAAoB;QAAEiJ,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAe,CAAC,CAAC;MAC/F,IAAItB,KAAK,CAAC5H,KAAK,CAACgJ,QAAQ,CAAC,wBAAwB,CAAC,EAChDrB,YAAY,CAAC3H,KAAK,CAACgE,IAAI,CAAC;QAAEhE,KAAK,EAAE,wBAAwB;QAAEiJ,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAgB,CAAC,CAAC;MACrG,IAAItB,KAAK,CAAC5H,KAAK,CAACgJ,QAAQ,CAAC,oBAAoB,CAAC,EAC5CrB,YAAY,CAAC3H,KAAK,CAACgE,IAAI,CAAC;QAAEhE,KAAK,EAAE,oBAAoB;QAAEiJ,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAoB,CAAC,CAAC;MACvG,IAAItB,KAAK,CAAC5H,KAAK,CAACgJ,QAAQ,CAAC,YAAY,CAAC,EACpCrB,YAAY,CAAC3H,KAAK,CAACgE,IAAI,CAAC;QAAEhE,KAAK,EAAE,YAAY;QAAEiJ,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAoB,CAAC,CAAC;MAC7F,IAAItB,KAAK,CAAC5H,KAAK,CAACgJ,QAAQ,CAAC,cAAc,CAAC,EACtCrB,YAAY,CAAC3H,KAAK,CAACgE,IAAI,CAAC;QAAEhE,KAAK,EAAE,cAAc;QAAEiJ,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAgB,CAAC,CAAC;MAC3FxB,IAAI,CAAC1H,KAAK,GAAG2H,YAAY,CAAC3H,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK;MACxCmJ,gBAAgB,CAAC,CAAC;MAClB5B,UAAU,CAAC,YAAM;QACfH,IAAI,CAACpH,KAAK,GAAG,IAAI;QACjBuH,UAAU,CAAC,YAAM;UACfF,WAAW,CAACrH,KAAK,GAAG,IAAI;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IACF,IAAMoJ,SAAS,GAAG/C,GAAG,CAAC,CAAC,CAAC,CAAC;IACzB,IAAMgD,OAAO,GAAGhD,GAAG,CAAC,IAAI,CAAC;IACzB,IAAM8C,gBAAgB;MAAA,IAAAG,KAAA,GAAAvD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6E,SAAA;QAAA,IAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAnK,mBAAA,GAAAuB,IAAA,UAAA6I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,IAAA,GAAAwE,SAAA,CAAAnG,IAAA;YAAA;cACjBgG,GAAG,GAAG7C,cAAc,CAAC3G,KAAK,KAAK,OAAO,GAAG,+BAA+B,GAAG,iCAAiC;cAAA2J,SAAA,CAAAnG,IAAA;cAAA,OAChG4C,GAAG,CAACwD,UAAU,CAACJ,GAAG,EAAE;gBACpCK,SAAS,EAAElC,YAAY,CAAC3H,KAAK,CAAC8I,MAAM,CAAC,UAAC9G,CAAC;kBAAA,OAAKA,CAAC,CAAChC,KAAK,KAAK0H,IAAI,CAAC1H,KAAK;gBAAA,EAAC,CAAC,CAAC,CAAC,CAACkJ;cACzE,CAAC,CAAC;YAAA;cAFIO,GAAG,GAAAE,SAAA,CAAA1G,IAAA;cAGTmG,SAAS,CAACpJ,KAAK,GAAGyJ,GAAG,CAACvB,IAAI,CAACkB,SAAS,CAAC/E,MAAM,GAAGoF,GAAG,CAACvB,IAAI,CAACkB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cACxEC,OAAO,CAACrJ,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAA2J,SAAA,CAAArE,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA,CACtB;MAAA,gBAPKJ,gBAAgBA,CAAA;QAAA,OAAAG,KAAA,CAAArD,KAAA,OAAAD,SAAA;MAAA;IAAA,GAOrB;IACD,IAAM8D,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9BT,OAAO,CAACrJ,KAAK,GAAG,IAAI;MACpBmJ,gBAAgB,CAAC,CAAC;IACpB,CAAC;IACD,IAAMY,aAAa,GAAG,SAAhBA,aAAaA,CAAI5I,IAAI,EAAE6I,GAAG,EAAK;MAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACnC,IAAMC,KAAK,GAAG,EAAAH,oBAAA,GAAAb,SAAS,CAACpJ,KAAK,CAACgK,GAAG,CAAC,cAAAC,oBAAA,uBAApBA,oBAAA,CAAsBI,MAAM,KAAI,CAAC;MAC/C,IAAID,KAAK,KAAK,CAAC,EAAE;QACfE,SAAS,CAAC;UACRnJ,IAAI,EAAE,MAAM;UACZoJ,OAAO,EAAE,KAAKP,GAAG,KAAK,YAAY,GAAG,IAAI,GAAG,IAAI;QAClD,CAAC,CAAC;QACF;MACF;MACA,IAAMQ,UAAU,GAAG,EAAAN,qBAAA,GAAAd,SAAS,CAACpJ,KAAK,CAACgK,GAAG,CAAC,cAAAE,qBAAA,uBAApBA,qBAAA,CAAsBO,aAAa,OAAAN,qBAAA,GAAIf,SAAS,CAACpJ,KAAK,CAACgK,GAAG,CAAC,cAAAG,qBAAA,uBAApBA,qBAAA,CAAsBO,WAAW,KAAI,EAAE;MACjGC,OAAO,CAACC,GAAG,CAACJ,UAAU,CAAC;MAEvB,IAAIA,UAAU,CAACnG,MAAM,EAAE;QACrBsE,cAAc,CAACkC,OAAO,CAAC,YAAY,EAAEpC,IAAI,CAACqC,SAAS,CAACN,UAAU,CAAC,CAAC;MAClE,CAAC,MAAM;QACL7B,cAAc,CAACoC,UAAU,CAAC,YAAY,CAAC;MACzC;MACA,IAAMvB,GAAG,GAAG7C,cAAc,CAAC3G,KAAK,IAAI,OAAO,GAAG,UAAU,GAAG,SAAS;MACpE;MACA+G,QAAQ,CAAC;QAAEiD,GAAG,EAAE,WAAW;QAAEhK,KAAK,EAAE,IAAIwJ,GAAG,GAAG,GAAGrI;MAAK,CAAC,CAAC;IAC1D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}