<template>
  <div class="LoginViewCopy" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="login-header">
      <div class="login-logo-name">
        <el-image class="login-logo" :src="systemLogo" fit="contain" />
        <div class="login-name" v-html="systemName"></div>
      </div>
      <div class="login-header-right">
        <div class="search-box">
          <input type="text" placeholder="请输入搜索内容" />
          <button class="search-btn" @click="search">
            <img class="search-icon" src="../img/search_icon.png" alt="搜索" />
          </button>
        </div>
        <button class="mine-btn" @click="openLogin">
          <img src="../img/mine_btn_bg.png" alt="我的" />
          <span>
            <img class="mine-icon" src="../img/mine_icon.png" alt="我的" />
            我的
          </span>
        </button>
      </div>
    </div>
    <div class="LoginViewBox">
      <div class="LoginViewName">欢迎登录</div>
      <el-form ref="LoginForm" :model="form" :rules="rules" class="LoginViewForm">
        <el-form-item prop="account">
          <div class="input-bg">
            <el-input v-model="form.account" placeholder="账号/手机号" @blur="handleBlur" clearable />
          </div>
        </el-form-item>
        <el-form-item prop="password">
          <div class="input-bg">
            <el-input type="password" v-model="form.password" placeholder="密码" show-password clearable />
          </div>
        </el-form-item>
        <el-form-item class="smsValidation" v-if="loginVerifyShow && whetherVerifyCode" prop="verifyCode">
          <el-input v-model="form.verifyCode" placeholder="短信验证码" clearable> </el-input>
          <el-button type="primary" @click="handleGetVerifyCode" :disabled="countDownText != '获取验证码'">
            {{ countDownText }}</el-button>
        </el-form-item>
        <div class="LoginViewSlideVerify" v-if="loginVerifyShow && !whetherVerifyCode">
          <xyl-slide-verify ref="slideVerify" @again="onAgain" @success="onSuccess" :disabled="disabled" />
        </div>
        <div class="LoginViewFormOperation">
          <el-checkbox v-model="checked">记住用户名和密码</el-checkbox>
          <div class="LoginViewFormOperationText" @click="show = !show">忘记密码？</div>
        </div>
        <el-button type="primary" @click="submitForm(LoginForm)" class="LoginViewFormButton login-btn-bg"
          :loading="loading" :disabled="loginDisabled">{{ loading ? '登录中' : '登录' }}</el-button>
      </el-form>
      <div class="LoginViewOperation" v-if="appDownloadUrl">
        <div class="LoginViewOperationBox">
          <el-popover placement="top" width="auto" @show="refresh" @hide="hideQrcode">
            <div class="LoginViewQrCodeBox">
              <div class="LoginViewQrCodeNameBody">
                <div class="LoginViewQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="LoginViewQrCodeName">APP扫码登录</div>
              </div>
              <div class="LoginViewQrCodeRefreshBody">
                <qrcode-vue :value="loginQrcode" :size="120" />
                <div class="LoginViewQrCodeRefresh" v-show="loginQrcodeShow">
                  <el-button type="primary" @click="refresh">刷新</el-button>
                </div>
              </div>
              <div class="LoginViewQrCodeText">请使用{{ systemName }}APP扫码登录</div>
            </div>
            <template #reference>
              <div class="LoginViewQrCode"></div>
            </template>
          </el-popover>
          <div class="LoginViewOperationText">APP扫码登录</div>
        </div>
        <div class="LoginViewOperationBox">
          <el-popover placement="top" width="auto">
            <div class="LoginViewQrCodeBox">
              <div class="LoginViewQrCodeNameBody">
                <div class="LoginViewQrCodeLogo">
                  <el-image :src="systemLogo" fit="cover" />
                </div>
                <div class="LoginViewQrCodeName">手机APP下载</div>
              </div>
              <qrcode-vue :value="appDownloadUrl" :size="120" />
              <div class="LoginViewQrCodeText">使用其他软件扫码下载{{ systemName }}APP</div>
            </div>
            <template #reference>
              <div class="LoginViewApp"></div>
            </template>
          </el-popover>
          <div class="LoginViewOperationText">手机APP下载</div>
        </div>
      </div>
      <div class="LoginViewSystemTips" v-if="systemLoginContact">{{ systemLoginContact }}</div>
    </div>
    <xyl-popup-window v-model="show" name="重置密码">
      <ResetPassword @callback="show = !show"></ResetPassword>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'LoginViewCopy' }
</script>
<script setup>
import { ref, onMounted } from 'vue'
import QrcodeVue from 'qrcode.vue'
import { systemLogo, systemName, appDownloadUrl, systemLoginContact, user } from 'common/js/system_var.js'
import { LoginView } from './LoginView.js'
import config from 'common/config'
import ResetPassword from './component/ResetPassword.vue'
const backgroundImage = ref(`${config.API_URL}/pageImg/open/homePage?areaId=${user.value.areaId}`)
const show = ref(false)
const { loginVerifyShow, whetherVerifyCode, loginDisabled, loading, checked, imgList, LoginForm, form, rules, countDownText, slideVerify, disabled, loginQrcode, loginQrcodeShow, handleBlur, handleGetVerifyCode, onAgain, onSuccess, globalData, submitForm, loginInfo, refresh, hideQrcode } = LoginView()


onMounted(() => {
  loginInfo()
  globalData()
})
</script>
<style lang="scss">
.LoginViewCopy {
  width: 100%;
  height: 100%;
  background: no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .login-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 40px 0 40px;
    box-sizing: border-box;
    position: relative;
    z-index: 2;
    flex-shrink: 0;

    .login-logo-name {
      display: flex;
      align-items: center;

      .login-logo {
        height: 75px;
        width: 75px;
      }

      .login-name {
        font-size: 37px;
        color: #fff;
        font-weight: bold;
        margin-left: 15px;
        letter-spacing: 5px;
      }
    }


    .login-header-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-box {
        display: flex;
        align-items: center;
        border-radius: 20px;
        padding: 0 0 0 8px;
        height: 36px;
        border: 1px solid #FFFFFF;
        // width: 350px;

        input {
          border: none;
          outline: none;
          height: 100%;
          padding: 0 8px;
          border-radius: 20px 0 0 20px;
          background: rgb(0, 0, 0, 0);
          width: calc(100% - 55px);
          color: #fff;

          &::placeholder {
            color: #fff;
            opacity: 1;
          }
        }

        .search-btn {
          background: url("../img/search_btn_bg.png") no-repeat center/cover;
          border: none;
          width: 55px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .search-icon {
            width: 18px;
            height: 18px;
          }
        }
      }

      .mine-btn {
        background: none;
        border: none;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0;
        cursor: pointer;

        img {
          height: 39px;
        }

        span {
          position: absolute;
          left: 0;
          width: 100%;
          text-align: center;
          color: #fff;
          font-size: 14px;
          line-height: 39px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .mine-btn .mine-icon {
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
    }
  }

  .LoginViewBox {
    position: absolute;
    left: 100px; // 距离左侧 100px
    top: 55%;
    transform: translateY(-50%);
    width: 460px;
    height: 640px;
    background: url("../img/login_form_bg.png") no-repeat center/cover;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center; // 水平居中
    justify-content: flex-start;
    padding: 0 40px;

    .LoginViewName {
      width: 100%;
      text-align: center;
      font-size: 28px;
      font-weight: bold;
      color: #fff;
      margin-top: 82px; // 距离顶部
      margin-bottom: 26px; // 与表单间距
      margin-left: 45px;
      letter-spacing: 2px;
      line-height: 1.2;
    }

    .LoginViewForm {
      width: 360px;
      margin: 0px 20px 0 58px;
      padding-bottom: 20px;

      input:-webkit-autofill {
        transition: background-color 5000s ease-in-out 0s;
      }

      .input-bg {
        width: 100%;
        height: 44px;
        background: url("../img/input_bg.png") no-repeat center/cover; // 你的输入框背景图
        display: flex;
        align-items: center;
        padding: 0 16px;
        box-sizing: border-box;

        .zy-el-input {
          background: transparent !important;

          .zy-el-input__wrapper {
            background: transparent !important;
            box-shadow: none !important;
            border: none !important;
          }

          .zy-el-input__inner {
            background: transparent !important;
            color: #fff;
            border: none;
            font-size: 16px;

            &::placeholder {
              color: #fff;
              opacity: 0.7;
            }
          }
        }
      }

      .zy-el-form-item {
        margin-bottom: 20px;
      }

      .LoginViewFormButton.login-btn-bg {
        background: url("../img/login_btn.png") no-repeat center/cover !important; // 按钮背景图
        border: none !important;
        color: #fff !important;
        font-size: 18px;
        font-weight: bold;
        width: 100%;
        height: 44px;
        box-shadow: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: filter 0.2s;

        // 鼠标悬浮时可加亮
        &:hover,
        &:focus {
          filter: brightness(1.08);
        }

        // 禁用时灰度
        &.is-disabled,
        &[disabled] {
          filter: grayscale(0.6);
          opacity: 0.7;
          cursor: not-allowed;
        }
      }

      .is-loading {
        border-radius: 60px;
      }

      .smsValidation {
        .zy-el-form-item__content {
          display: flex;
          justify-content: space-between;
        }

        .zy-el-input {
          width: 56%;
        }
      }

      .LoginViewSlideVerify {
        margin-bottom: var(--zy-distance-five);
        width: 100%;

        .xyl-slide-verify-slider {
          width: 100% !important;
          border: 1px solid #7ea4ff !important;

          .xyl-slide-verify-slider-mask .xyl-slide-verify-slider-mask-item {
            background: #8ecbff;
          }

          .container-success .xyl-slide-verify-slider-mask .xyl-slide-verify-slider-mask-item {
            background-color: var(--zy-el-color-success);
          }

          .xyl-slide-verify-slider-text {
            color: #cee4ff !important;
          }
        }
      }

      .zy-el-checkbox,
      .zy-el-checkbox__label {
        color: #fff !important;
      }

      .LoginViewFormOperation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--zy-distance-three);

        .zy-el-checkbox {
          height: var(--zy-height-secondary);
        }

        .LoginViewFormOperationText {
          cursor: pointer;
          color: #51DCFF;
          font-size: var(--zy-text-font-size);
        }
      }
    }

    .LoginViewOperation {
      width: 100%;
      padding-bottom: var(--zy-distance-two);
      display: flex;
      justify-content: space-between;
      margin: 0px 45px 0 80px;

      .LoginViewOperationBox {
        margin: 0 var(--zy-distance-two);
        cursor: pointer;

        .LoginViewQrCode {
          width: 50px;
          height: 50px;
          background: url("../img/login_qr_code.png");
          background-size: 100% 100%;
          margin: auto;
        }

        .LoginViewApp {
          width: 50px;
          height: 50px;
          background: url("../img/login_app.png") no-repeat;
          background-size: auto 100%;
          background-position: center;
          margin: auto;
        }

        .LoginViewOperationText {
          font-size: var(--zy-text-font-size);
          line-height: var(--zy-line-height);
          padding: var(--el-border-radius-small) 0;
          text-align: center;
          color: #fff;
        }
      }
    }

    .LoginViewForm+.LoginViewSystemTips {
      padding-top: var(--zy-distance-one);
    }

    .LoginViewSystemTips {
      color: #fff;
      font-size: var(--zy-text-font-size);
      text-align: center;
      margin-left: 32px;
    }
  }
}

.LoginViewQrCodeBox {
  width: 320px;
  background-color: #fff;

  canvas {
    display: block;
    margin: auto;
  }

  .LoginViewQrCodeNameBody {
    padding: var(--zy-distance-three);
    display: flex;
    align-items: center;
    justify-content: center;

    .LoginViewQrCodeLogo {
      width: 26px;
      margin-right: 6px;

      .zy-el-image {
        width: 100%;
        display: block;
      }
    }

    .LoginViewQrCodeName {
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
    }
  }

  .LoginViewQrCodeRefreshBody {
    position: relative;

    .LoginViewQrCodeRefresh {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 120px;
      background-color: rgba(000, 000, 000, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;

      .zy-el-button {
        --zy-el-button-size: var(--zy-height-secondary);
      }
    }
  }

  .LoginViewQrCodeText {
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-three);
    color: var(--zy-el-color-primary);
  }
}
</style>
