{"ast": null, "code": "import { resolveComponent as _resolveComponent, normalizeStyle as _normalizeStyle, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"global-dynamic-input\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_input, {\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.content = $event;\n    }),\n    disabled: $setup.props.disabled,\n    style: _normalizeStyle({\n      width: $setup.inputWid\n    })\n  }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\", \"style\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_input", "modelValue", "$setup", "content", "_cache", "$event", "disabled", "props", "style", "_normalizeStyle", "width", "inputWid"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\global-dynamic-title\\global-dynamic-input.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-dynamic-input\">\r\n    <el-input v-model=\"content\"\r\n              :disabled=\"props.disabled\"\r\n              :style=\"{ width: inputWid }\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalDynamicInput' }\r\n</script>\r\n<script setup>\r\nimport { computed } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: [String, Number],\r\n  disabled: { type: Boolean, default: false }\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\n\r\nconst content = computed({\r\n  get () { return props.modelValue },\r\n  set (value) { emit('update:modelValue', value) }\r\n})\r\n\r\nconst inputWid = computed(() => {\r\n  if (!content.value) {\r\n    return '120px'\r\n  } else {\r\n    return props.disabled ? (String(content.value).length * 6 + 60) + 'px' : (String(content.value).length * 13 + 70) + 'px'\r\n  }\r\n})\r\n</script>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;;uBAAjCC,mBAAA,CAIM,OAJNC,UAIM,GAHJC,YAAA,CAEyCC,mBAAA;IAJ7CC,UAAA,EAEuBC,MAAA,CAAAC,OAAO;IAF9B,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAEuBH,MAAA,CAAAC,OAAO,GAAAE,MAAA;IAAA;IACfC,QAAQ,EAAEJ,MAAA,CAAAK,KAAK,CAACD,QAAQ;IACxBE,KAAK,EAJpBC,eAAA;MAAAC,KAAA,EAI+BR,MAAA,CAAAS;IAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}